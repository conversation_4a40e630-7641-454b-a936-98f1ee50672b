# 流域四预系统前端

## 项目概述

这是一个基于 Vue 3 + Vite 开发的流域四预系统前端项目，主要用于水文监测和防洪预警。系统集成了地图展示、数据可视化、实时监控等多个功能模块。

## 技术栈

- **核心框架**: Vue 3.5.13
- **构建工具**: Vite 4.5.0
- **UI框架**: Element Plus 2.2.21
- **地图引擎**: @narutogis/map3d-dc-sdk 1.0.2
- **状态管理**: Pinia 2.0.22
- **路由管理**: Vue Router 4.1.4
- **数据可视化**: ECharts 5.4.0
- **工具库**:
  - Moment.js (时间处理)
  - Turf.js (地理数据处理)
  - Axios (HTTP 请求)

## 项目依赖

### 核心依赖 (dependencies)

| 依赖包 | 版本 | 说明 |
|--------|------|------|
| vue | 3.5.13 | Vue.js 核心框架 |
| vue-router | 4.1.4 | Vue 路由管理 |
| pinia | 2.0.22 | Vue 状态管理库 |
| element-plus | 2.2.21 | Vue 3 UI 组件库 |
| @element-plus/icons-vue | 2.0.10 | Element Plus 图标库 |
| vite | 4.5.0 | 现代化构建工具 |
| echarts | 5.4.0 | 数据可视化图表库 |
| @narutogis/map3d-dc-sdk | 1.0.2 | 3D 地图引擎 SDK |
| @vue-flow/core | 1.45.0 | Vue 流程图核心库 |
| @vue-flow/background | 1.3.2 | Vue 流程图背景组件 |
| @vue-flow/controls | 1.1.2 | Vue 流程图控制组件 |
| @turf/turf | 7.1.0 | 地理空间分析库 |
| @vueuse/core | 9.5.0 | Vue 组合式 API 工具集 |
| @iconify/vue | 5.0.0 | 图标组件库 |
| @metastar/star-api | 2.0.180 | 星图 API 接口库 |
| axios | 0.27.2 | HTTP 请求库 |
| moment | 2.29.4 | 日期时间处理库 |
| mitt | 3.0.1 | 事件总线库 |
| clipboard | 2.0.11 | 剪贴板操作库 |
| file-saver | 2.0.5 | 文件保存库 |
| fuse.js | 6.6.2 | 模糊搜索库 |
| html2canvas | 1.4.1 | HTML 转 Canvas 库 |
| js-cookie | 3.0.1 | Cookie 操作库 |
| jsencrypt | 3.3.1 | RSA 加密库 |
| nprogress | 0.2.0 | 页面加载进度条 |
| qs | 6.11.0 | 查询字符串解析库 |
| svg-pan-zoom | 3.6.1 | SVG 缩放平移库 |
| uuid | 11.1.0 | UUID 生成库 |
| vue-cropper | 1.0.3 | Vue 图片裁剪组件 |
| larksr_websdk | 3.3.112 | 云渲染 WebSDK |
| scene-communication-plugin | 1.0.2 | 场景通信插件 |
| markdown-it | 14.1.0 | Markdown 解析器 |
| highlight.js | 11.11.1 | 代码高亮库 |
| github-markdown-css | 5.8.1 | GitHub 风格 Markdown 样式 |

### 开发依赖 (devDependencies)

| 依赖包 | 版本 | 说明 |
|--------|------|------|
| @vitejs/plugin-vue | 4.4.0 | Vite Vue 插件 |
| @vue/compiler-sfc | 3.5.13 | Vue 单文件组件编译器 |
| @narutogis/vite-plugin-dc | 1.0.0 | DC SDK Vite 插件 |
| oxlint | 1.0.0 | 快速 JavaScript/TypeScript 代码检查工具 |
| sass | 1.69.4 | CSS 预处理器 |
| unplugin-auto-import | 0.16.6 | 自动导入插件 |
| vite-plugin-compression | 0.5.1 | Vite 压缩插件 |
| vite-plugin-svg-icons | 2.0.1 | SVG 图标插件 |
| vite-plugin-vue-setup-extend | 0.4.0 | Vue setup 语法扩展插件 |

## 项目结构

```
src/
├── api/                    # API 接口定义
├── assets/                 # 静态资源文件
├── components/             # 公共组件
├── directive/              # 自定义指令
├── layout/                 # 布局组件
├── plugins/                # 插件配置
├── router/                 # 路由配置
├── store/                  # 状态管理
├── utils/                  # 工具函数
├── views/                  # 页面组件
├── App.vue                 # 根组件
├── main.js                 # 入口文件
├── permission.js           # 权限控制
└── settings.js             # 全局配置
```


## 核心功能模块

### 1. 地图展示模块

- 多图层管理
- 实时数据展示
- 地图交互控制
- 双屏对比分析

### 2. 数据监测模块

- 雨量监测
- 河道水情
- 水库水情
- 气象数据

### 3. 防洪预警模块

- 实时预警
- 风险评估
- 预案管理
- 调度方案

### 4. 避险转移模块

- 风险点管理
- 安置点管理
- 转移路线规划
- 人员统计

## 事件通信机制

项目使用全局事件总线（EventBus）进行组件间通信，主要包括：

### 地图控制事件
- 模块切换
- 图层控制
- 视角调整

### 数据更新事件
- 雨量数据更新
- 河流数据更新
- 水库数据更新

### 面板控制事件
- 面板显示/隐藏
- 数据展示控制
- 界面交互反馈

## 开发指南

### 环境要求

- Node.js >= 14.0.0
- npm >= 6.14.0

### 项目启动

```bash
# 安装依赖
npm install

# 开发环境启动
npm run dev

# 生产环境构建
npm run build:prod
```

### 开发规范

1. **组件开发规范**
   - 使用 Vue 3 组合式 API
   - 遵循单一职责原则
   - 保持组件粒度合适

2. **代码风格**
   - 使用 OxLint 进行代码检查
   - 遵循项目既定的命名规范
   - 保持代码注释完整

3. **Git 提交规范**
   - feat: 新功能
   - fix: 修复问题
   - docs: 文档修改
   - style: 代码格式修改
   - refactor: 代码重构
   - test: 测试用例
   - chore: 其他修改

## 部署说明

### 构建部署

```bash
# 构建生产环境版本
npm run build:prod

# 构建预发布环境版本
npm run build:stage
```

### 环境配置

- 开发环境：.env.development
- 生产环境：.env.production
- 预发布环境：.env.staging

## 注意事项

1. 地图相关操作需要注意性能优化
2. 大量实时数据更新时需要控制更新频率
3. 注意内存泄漏的防范
4. 保持用户界面的响应性
5. 确保数据安全性

## 维护支持

- 版本：3.8.4
- 许可证：MIT
- 文档更新时间：2024-03-14
