import vue from "@vitejs/plugin-vue";
import createAutoImport from "./auto-import";
import createSvgIcon from "./svg-icon";
import createCompression from "./compression";
import DC from "@narutogis/vite-plugin-dc";
import { visualizer } from "rollup-plugin-visualizer";

export default function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [
    vue({
      script: {
        defineModel: true,
      },
    }),
    DC(),
  ];
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSvgIcon(isBuild));
  isBuild && vitePlugins.push(...createCompression(viteEnv));
  isBuild && vitePlugins.push(visualizer({ filename: "statistics.html" }));
  return vitePlugins;
}
