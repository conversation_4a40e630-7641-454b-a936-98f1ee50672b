<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm1" :inline="true" class="form-container">
      <el-form-item label="区域类型">
        <el-select v-model="queryForm.regionType" placeholder="请选择" @change="typeChange">
          <el-option label="区域" :value="1" />
          <el-option label="流域" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="行政区" v-if="queryForm.regionType == 1">
        <el-tree-select v-model="queryForm.adcd" :data="adcdList" clearable
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="id" placeholder="请选择行政区"
          check-strictly
          :render-after-expand="false"
          />
      </el-form-item>
      <el-form-item label="选择流域" v-else>
        <el-tree-select v-model="queryForm.adcd" :data="transformDataForTreeSelect(waterAreaList)" check-strictly
          :render-after-expand="false" placeholder="请选择流域" />
      </el-form-item>
      <el-form-item label="选择时间">
        <el-date-picker v-model="queryForm.date" :shortcuts="shortcuts" :clearable="false"
          :default-value="defaultValue" type="datetimerange" :disabledDate="disabledFutureDates" range-separator="-"
          start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm"
          :disabled-minutes="() => {
            // 返回从1到59的数组，表示禁用的分钟
            return Array.from({ length: 59 }, (_, i) => i + 1);
          }"
          :disabled-seconds="() => {
            // 返回从1到59的数组，表示禁用的秒
            return Array.from({ length: 59 }, (_, i) => i + 1);
          }"
          />
      </el-form-item>
      <el-form-item label="雨量标准">
        <el-select v-model="queryForm.rainStandard" placeholder="请选择" clearable>
          <el-option label="0mm" :value="0"></el-option>
          <el-option label="10mm" :value="10"></el-option>
          <el-option label="25mm" :value="25"></el-option>
          <el-option label="50mm" :value="50"></el-option>
          <el-option label="100mm" :value="100"></el-option>
          <el-option label="200mm" :value="200"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search">查询</el-button>
        <el-button @click="reset" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <el-table v-loading="loading" :data="tableList" stripe row-key="adcd" :expand-row-keys="expandedRows" :tree-props="{hasChildren: 'hasChildren', children: 'children'}">
        <!-- <el-table-column label="序号" width="60" type="index"></el-table-column> -->
        <el-table-column label="区域" prop="region" class-name="region-table-column">
          <template #default="scope">
            <div class="region-cell">
              <span @click="handleExpandChange(scope.row)" v-if="scope.row.hasChildren" class="expand-icon-wrapper">
                <el-icon size="12" class="expand-icon" :class="{ 'is-loading': loadingRows.includes(scope.row.adcd) }">
                  <Loading v-if="loadingRows.includes(scope.row.adcd)" />
                  <ArrowRight v-else />
                </el-icon>
              </span>
              <span>{{ scope.row.region }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="累计面雨量(mm)" prop="totalRainfall" align="center">
          <template #default="scope">
            {{ scope.row.totalRainfall.toFixed(1) }}
          </template>
        </el-table-column>
        <el-table-column label="重现期(年)" prop="recurrencePeriod" align="center">
        </el-table-column>
        <el-table-column label="最大雨强（mm/h)" prop="maxRainfallIntensity" align="center">
        </el-table-column>
        <el-table-column label="单站最大降雨量(mm)" prop="maxRainfall" align="center">
        </el-table-column>
        <el-table-column label="最大降雨站点" prop="maxRainfallStationName" align="center">
        </el-table-column>
        <el-table-column label="降雨站点数量（座）" prop="stationCount" align="center">
        </el-table-column>
        <!-- <el-table-column label="操作" prop="stationAddr">
          <template #default="scope">
            <el-link type="primary" @click="editOne(scope.row)">编辑</el-link>
            <el-link type="primary" style="margin-left: 20px;" @click="deleteOne(scope.row.id)">删除</el-link>
          </template>
    </el-table-column> -->
      </el-table>
      <pagination :total="total" v-model:page="queryForm.pageNum"
        v-model:limit="queryForm.pageSize" @pagination="getList" />
    </div>
    <el-dialog v-model="addShow" :title="status == 'add' ? '新增' : '修改'" style="width: 36%;">
      <el-form ref="addFormRef" :model="addForm" label-width="auto" :rules="addRule">
        <template class="flexbox">
          <el-form-item label="区域" prop="adcd">
            <el-tree-select v-model="addForm.adcd" :data="adcdList" clearable style="width: 220px;"
              :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="id" placeholder="请选择行政区"
              check-strictly />
          </el-form-item>
          <el-form-item label="时段长" prop="longDuration">
            <el-select v-model="addForm.longDuration" placeholder="请选择" style="width: 220px;">
              <el-option v-for="item in hours" :key="item" :label="`${item}小时`" :value="item"></el-option>
            </el-select>
          </el-form-item>
        </template>
        <h3 style="color: #606266;font-weight: bold;">重现期降雨量:</h3>
        <template class="flexbox">
          <el-form-item label="2年" prop="zone">
            <el-input v-model="addForm.returnPeriod2" style="width: 220px;"
              oninput="value=value.replace(/[^0-9.]/g,0)"></el-input>
          </el-form-item>
          <el-form-item label="5年" prop="zone">
            <el-input v-model="addForm.returnPeriod5" style="width: 220px;"
              oninput="value=value.replace(/[^0-9.]/g,0)"></el-input>
          </el-form-item>
        </template>
        <template class="flexbox">
          <el-form-item label="10年" prop="zone">
            <el-input v-model="addForm.returnPeriod10" style="width: 220px;"
              oninput="value=value.replace(/[^0-9.]/g,0)"></el-input>
          </el-form-item>
          <el-form-item label="20年" prop="zone">
            <el-input v-model="addForm.returnPeriod20" style="width: 220px;"
              oninput="value=value.replace(/[^0-9.]/g,0)"></el-input>
          </el-form-item>
        </template>
        <template class="flexbox">
          <el-form-item label="30年" prop="zone">
            <el-input v-model="addForm.returnPeriod30" style="width: 220px;"
              oninput="value=value.replace(/[^0-9.]/g,0)"></el-input>
          </el-form-item>
          <el-form-item label="50年" prop="zone">
            <el-input v-model="addForm.returnPeriod50" style="width: 220px;"
              oninput="value=value.replace(/[^0-9.]/g,0)"></el-input>
          </el-form-item>
        </template>
        <template class="flexbox">
          <el-form-item label="100年" prop="zone">
            <el-input v-model="addForm.returnPeriod100" style="width: 220px;"
              oninput="value=value.replace(/[^0-9.]/g,0)"></el-input>
          </el-form-item>
          <el-form-item label="200年" prop="zone">
            <el-input v-model="addForm.returnPeriod200" style="width: 220px;"
              oninput="value=value.replace(/[^0-9.]/g,0)"></el-input>
          </el-form-item>
        </template>
        <template class="flexbox">
          <el-form-item label="500年" prop="zone">
            <el-input v-model="addForm.returnPeriod500" style="width: 220px;"
              oninput="value=value.replace(/[^0-9.]/g,0)"></el-input>
          </el-form-item>
          <el-form-item label="1000年" prop="zone">
            <el-input v-model="addForm.returnPeriod1000" style="width: 220px;"
              oninput="value=value.replace(/[^0-9.]/g,0)"></el-input>
          </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="submitForm" v-if="status == 'add'">确认</el-button>
          <el-button @click="updateForm" v-else>修改</el-button>
          <el-button type="primary" @click="addShow = false"> 取消 </el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance, computed, nextTick } from "vue";
import { getAdcdTree, selectStlyList } from "@/api/watershed/ads";
import { areaRainFallList, townRainFallList} from "@/api/watershed/query/index"
import moment from "moment";
export default defineComponent({
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      waterAreaList: [],
      shortcuts: [
        {
          text: '今日8时起',
          value: () => {
            //所有时间都只保留小时，分秒都为0
            const end = moment().format('YYYY-MM-DD HH:00:00').valueOf()
            const start = moment().format('YYYY-MM-DD 08:00:00').valueOf()
            return [start, end]
          },
        },
        {
          text: '昨日8时起',
          value: () => {
            const end = moment().format('YYYY-MM-DD HH:00:00').valueOf()
            const start = moment().subtract(1, 'days').format('YYYY-MM-DD 08:00:00').valueOf()
            return [start, end]
          },
        },
        {
          text: '最近24小时',
          value: () => {
            const end = moment().format('YYYY-MM-DD HH:00:00').valueOf()
            const start = moment().subtract(24, 'hours').format('YYYY-MM-DD HH:00:00').valueOf()
            return [start, end]
          },
        },
        {
          text: '最近48小时',
          value: () => {
            const end = moment().format('YYYY-MM-DD HH:00:00').valueOf()
            const start = moment().subtract(48, 'hours').format('YYYY-MM-DD HH:00:00').valueOf()
            return [start, end]
          },
        },
      ],
      addShow: false,
      queryForm: {
        pageNum: 1,
        regionType: 1,
        pageSize: 20,
        // adcd: '130634000000', //默认查曲阳县
        adcd: '', //默认查曲阳县
        date: [moment().subtract(1, 'days').format('YYYY-MM-DD 08:00:00').valueOf(), moment().format('YYYY-MM-DD HH:00:00').valueOf()],
        // rainStandard: 50,
        rainStandard: 0,
      },
      status: 'add',
      addRule: {
        adcd: [
          { required: true, message: '请选择区域', trigger: 'blur' },
        ],
        longDuration: [
          { required: true, message: '请输入时段长', trigger: 'blur' },
        ],
      },
      total: 0,
      tableList: [],
      loading: false,
      adcdList: [],
      dialogList: [],
      detailShow: false,
      dataShow: true,
      title: '',
      time: '',
      stcd: '',
      expandedRows: [],
      loadingRows: [], // 存储正在加载中的行ID
      addForm: {
        adcd: '',
        longDuration: '',
        returnPeriod2: '',
        returnPeriod5: '',
        returnPeriod10: '',
        returnPeriod20: '',
        returnPeriod30: '',
        returnPeriod50: '',
        returnPeriod100: '',
        returnPeriod200: '',
        returnPeriod500: '',
        returnPeriod1000: '',
      }
    })
    onMounted(() => {
      getAdcdList()
      getAllWater()
      getList()
    })
    const editOne = (item) => {
      state.status = 'edit'
      state.addForm = item
      state.addShow = true
    }
    const getAdcdList = () => {
      getAdcdTree({
        adcd: ''
      }).then((res) => {
        state.adcdList = res.data[0].children
      })
    }
    const typeChange = (e) => {

      state.queryForm.adcd = ''
    }
    const addData = () => {

      state.addShow = true
      proxy.$nextTick(() => {
        proxy.$refs.addFormRef.clearValidate()
      })
    }
    const transformDataForTreeSelect = (data) => {
      // 递归地转换数据以匹配 el-tree-select 的需求
      return data.map(item => ({
        label: item.data.name, // 使用 'name' 属性作为标签
        value: item.data.basinId, // 使用 'basinId' 属性作为值
        children: item.children ? transformDataForTreeSelect(item.children) : [], // 递归转换子节点
      }));
    }

    const getList = () => {
      state.loading = true
      // 清空展开状态
      state.expandedRows = []
      let startTime = moment(state.queryForm.date[0]).format('YYYY-MM-DD HH:mm:ss')
      let endTime = moment(state.queryForm.date[1]).format('YYYY-MM-DD HH:mm:ss')

      areaRainFallList({ startTime, endTime, ...state.queryForm }).then((res) => {
        state.tableList = (res.rows || []).map(item => {
          // 只有区县级别才可能有子节点
          const hasChildren = item.adcd && item.adcd.endsWith('000000');
          return {
            ...item,
            hasChildren: hasChildren,
          }
        })

        state.total = res.total || 0
        state.loading = false
      })
    }

    // 处理表格行展开事件
    const handleExpandChange = (row) => {
      // 如果当前行已经在加载中，不重复请求
      if (state.loadingRows.includes(row.adcd)) {
        return;
      }

      // 添加到加载状态
      state.loadingRows.push(row.adcd);
      row.children = [];
      getVillageData(row);
    }

    const getVillageData = async (row) => {
      state.expandedRows = [];
      const adcd = row.adcd;
      let startTime = moment(state.queryForm.date[0]).format('YYYY-MM-DD HH:mm:ss');
      let endTime = moment(state.queryForm.date[1]).format('YYYY-MM-DD HH:mm:ss');

      townRainFallList({
        startTime,
        endTime,
        ...state.queryForm,
        adcd
      }).then((res) => {
        row.hasChildren = false;
        // 防止没有子项时显示空白展开行
        if (res.rows && res.rows.length > 0) {
          row.children = res.rows.map(item => {
            return {
              ...item,
              level: 2,
              hasChildren: false // 子节点不可再展开
            }
          }) || [];

          nextTick(() => {
            if (!state.expandedRows.includes(adcd)) {
              state.expandedRows.push(adcd);
            }
          });
        } else {
          // 如果没有子项，将hasChildren设为false
          row.hasChildren = false;
        }

        // 移除加载状态
        const index = state.loadingRows.indexOf(adcd);
        if (index !== -1) {
          state.loadingRows.splice(index, 1);
        }
      }).catch(() => {
        // 出错时也移除加载状态
        const index = state.loadingRows.indexOf(adcd);
        if (index !== -1) {
          state.loadingRows.splice(index, 1);
        }
      });
    }
    const getAllWater = async () => {
      let res = await selectStlyList({ pageNum: 1, pageSize: 999 })
      state.waterAreaList = res.data || []
    }

    const reset = () => {
      state.queryForm = {
        pageNum: 1,
        pageSize: 20,
        regionType: 1,
        adcd: '',
        date: [moment().subtract(1, 'days').format('YYYY-MM-DD 08:00:00').valueOf(), moment().valueOf()],
        basinId: '',
        rainStandard: 50,
      }
      // 清空展开状态
      state.expandedRows = []
      getList()
    }

    return {
      ...toRefs(state),
      getList,
      reset,
      addData,
      editOne,
      transformDataForTreeSelect,
      typeChange,
      handleExpandChange
    };
  },
});
</script>

<style scoped lang="scss">
.flexbox {
  display: flex;
  justify-content: space-between;
}

.region-cell {
  display: inline-flex;
  align-items: center;

  .expand-icon-wrapper {
    margin-right: 8px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
  }

  .expand-icon {
    transition: all 0.3s;
    transform-origin: center;

    &.is-loading {
      animation: rotating 1s linear infinite;
      color: #409EFF;
    }

    &:hover {
      color: #409EFF;
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

:deep(.el-table__row) {
  .region-table-column {
    .el-table__placeholder {
      display: none;
    }
    .el-icon {
      margin-right: 8px;
    }
  }

  &.el-table__row--level-1 {
    .el-table__placeholder {
      display: inline-block;
    }
  }
}
</style>