<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm1" inline class="form-container">
      <el-form-item label="水库名称">
        <el-input v-model="queryForm.stnm" placeholder="水库名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="行政区">
        <el-tree-select v-model="queryForm.adcd" :data="adcdList" clearable
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="id" placeholder="请选择行政区"
          check-strictly />
      </el-form-item>
      <el-form-item label="工程规模">
        <el-select v-model="queryForm.projectScale" clearable placeholder="工程规模">
          <el-option label="大(1)型" value="1"></el-option>
          <el-option label="大(2)型" value="2"></el-option>
          <el-option label="中型" value="3"></el-option>
          <el-option label="小(1)型" value="4"></el-option>
          <el-option label="小(2)型" value="5"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="时间">
        <el-date-picker v-model="queryForm.date" type="datetime" placeholder="选择日期时间" format="YYYY-MM-DD HH:mm"
          :disabledDate="disabledFutureDates">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="超警状态">
        <el-select v-model="queryForm.overLimitType" clearable
          placeholder="超限幅度" @change="overLimitTypeChange">
          <el-option label="超汛限" value="1"></el-option>
          <el-option label="超设计" value="2"></el-option>
          <el-option label="超校核" value="3"></el-option>
          <el-option label="超历史" value="4"></el-option>
        </el-select>
        <!-- <el-select v-model="queryForm.overLimitAmplitude" :disabled="amplitudeDisabled" style="width: 170px" clearable
          placeholder="">
          <el-option label="0-10公分" value="1"></el-option>
          <el-option label="10公分-20公分" value="2"></el-option>
          <el-option label="20公分-1米" value="3"></el-option>
          <el-option label="1米及以上" value="4"></el-option>
        </el-select> -->
      </el-form-item>

      <el-form-item label="水库编码">
        <el-input v-model="queryForm.stcd" placeholder="水库编码"></el-input>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search">查询</el-button>
        <el-button @click="reset" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- <div>
      <span
        style="display: inline-block;width: 70px;text-align: right;padding-right: 12px;font-weight: 700;font-size: 14px;color: #606266;">时间</span>

    </div> -->
    <!-- <div class="export" style="margin-top: 20px;">
      <el-button type="primary" @click="handleExport">导出</el-button>
    </div> -->
    <div class="content content-table">
      <el-table v-loading="loading" :data="tableList" stripe>
        <el-table-column label="序号" width="60" type="index"></el-table-column>
        <!-- <el-table-column align="center" label="注册登记号" prop="registrationSerialNumber"></el-table-column> -->
        <el-table-column align="center" label="区县" prop="adnm"></el-table-column>
        <!-- <el-table-column align="center" label="县区" prop="area"></el-table-column> -->
        <el-table-column align="center" label="水库名称" prop="stnm">
          <template #default="scope">
            <span v-if="scope.row.overFloodLimitWaterLevel > 0"
              style="color: #F56C6C;text-decoration:underline;cursor: pointer;" @click="handleDetail(scope.row)">{{
                scope.row.stnm }}</span>
            <span v-else style="color: #1890FF;text-decoration:underline;cursor: pointer;"
              @click="handleDetail(scope.row)">{{
                scope.row.stnm }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="水库编码" prop="stcd" />
        <el-table-column align="center" label="时间" prop="nowDate" />
        <el-table-column align="center" label="工程规模" prop="projectScale">
          <template #default="scope">
            <span v-if="scope.row.projectScale == 1">大(1)型</span>
            <span v-if="scope.row.projectScale == 2">大(2)型</span>
            <span v-if="scope.row.projectScale == 3">中型</span>
            <span v-if="scope.row.projectScale == 4">小(1)型</span>
            <span v-if="scope.row.projectScale == 5">小(2)型</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="24h雨量(mm)" prop="rainValue"></el-table-column>
        <el-table-column align="center" label="水位(m)" prop="waterLevel">
          <template #default="scope">
            <span v-if="scope.row.overFloodLimitWaterLevel > 0" style="color: #F56C6C;">{{
              scope.row.waterLevel }}</span>
            <span v-else style="color: #1890FF;">{{
              scope.row.waterLevel }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="水势" prop="wptn"></el-table-column>
        <el-table-column align="center" label="入库流量(m³/s)" prop="inboundFlow"></el-table-column>
        <el-table-column align="center" label="出库流量(m³/s)" prop="outboundFlow" width="90px"></el-table-column>
        <el-table-column align="center" label="蓄水量(万m³)" prop="waterStorage"></el-table-column>
        <el-table-column align="center" label="汛限水位(m)" prop="floodLimitWaterLevel"></el-table-column>
        <el-table-column align="center" label="超汛限(m)" prop="overFloodLimitWaterLevel">
          <template #default="scope">
            <span v-if="scope.row.overFloodLimitWaterLevel > 0" style="color: #F56C6C;">{{
              scope.row.overFloodLimitWaterLevel }}</span>
            <span v-else style="color: #1890FF;">{{ scope.row.overFloodLimitWaterLevel }} </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="设计水位(m)" prop="designWaterLevel"></el-table-column>
        <el-table-column align="center" label="校核水位(m)" prop="checkWaterLevel"></el-table-column>
        <el-table-column align="center" label="历史最高水位(m)" prop="historyWaterLevel"></el-table-column>
      </el-table>
      <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
        @pagination="getList" />
    </div>
    <el-dialog :title="title" v-model="detailShow" width="700px" @close="handleClose">
      <div>
        <el-tabs v-model="activeLable" type="card" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane label="水库水情" :name="1"></el-tab-pane>
          <el-tab-pane label="水库库容曲线" :name="2"></el-tab-pane>
        </el-tabs>
        <div class="form" style="margin-bottom: 20px;">
          <el-date-picker v-model="time" type="datetimerange" range-separator="至" start-placeholder="开始时间"
            v-if="activeLable == 1" format="YYYY-MM-DD HH:mm" date-format="YYYY/MM/DD ddd" time-format="hh:mm"
            end-placeholder="结束时间" :clearable="false" style="width: 320px;margin-right: 10px;" />
          <el-button type="primary" @click="getChartsListInfo" v-if="activeLable == 1" icon="Search">查询</el-button>
          <el-button type="primary" plain @click="dialogReset" v-if="activeLable == 1" icon="Refresh">重置</el-button>
          <el-button type="primary" plain @click="dataShow = !dataShow">{{ dataShow ? '数据' : '图' }}</el-button>
          <!-- <el-button type="primary" plain>导出</el-button> -->
        </div>
        <div v-show="dataShow" class="rainfallChart" style="width: 660px;height: 300px;background-color: #f8f8f9;"
          id="rainfallChart3"></div>
        <div v-show="!dataShow">
          <el-table :data="dialogList" height="300px" border v-if="activeLable == 1">
            <el-table-column prop="time" label="时间"></el-table-column>
            <!-- <el-table-column prop="rainValue" label="降雨量(mm)"></el-table-column> -->
            <el-table-column prop="waterLevel" label="库水位(m)"></el-table-column>
            <el-table-column prop="inboundFlow" label="入库流量(m³/s)"></el-table-column>
            <el-table-column prop="outboundFlow" label="出库流量(m³/s)"></el-table-column>
          </el-table>
          <el-table :data="dialogList" height="300px" border v-else>
            <el-table-column prop="mstm" label="时间" v-if="activeLable == 1">
              <template #default="scope">
                {{ formatTime(scope.row.mstm) }}
              </template>
            </el-table-column>
            <el-table-column type="index" label="序号" width="80"></el-table-column>
            <!-- <el-table-column prop="rainValue" label="降雨量(mm)"></el-table-column> -->
            <el-table-column prop="rz" label="水位(m)"></el-table-column>
            <el-table-column prop="w" label="库容(百万m³)"></el-table-column>

          </el-table>
        </div>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance, nextTick } from "vue";
import { reservoirStationList, reservoirStationInfo, reservoirStationLine } from "@/api/watershed/query/index"
import { getAdcdTree, } from "@/api/watershed/ads";
import { ElMessage } from "element-plus";
import * as echarts from "echarts";
import moment from "moment";
export default defineComponent({
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      queryForm: {
        adcd: '',
        projectScale: '',
        stnm: "",
        stcd: "",
        overLimitType: '',
        pageNum: 1,
        pageSize: 20,
        overLimitAmplitude: "",
        date: '',
      },
      activeLable: 1,
      total: 0,
      tableList: [],
      adcdOption: [],
      loading: false,
      adcdList: [],
      exportData: {},
      dialogList: [],
      detailShow: false,
      dataShow: true,
      title: '',
      time: [moment().subtract(1, "day"), moment()],
      stcd: '',
      option1: {
        grid: {
          left: '60px',
          right: '60px',
          top: '50px',

          bottom: '70px',
          // containLabel: true
        },  //
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        legend: {
          width: "100%",
          top: '10px',
          data: [
            '出库流量',
            '入库流量',
            '实测水位'
          ],
          textStyle: {
            fontSize: 12,
            fontFamily: 'PingFang SC'
          }
        },
        xAxis: [

          {
            type: 'category',
            data: [],
            boundaryGap: true,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              // interval: "0",
            },
            axisLine: {
              lineStyle: {
              },
              onZero: true
            },
            position: 'bottom'   //x坐标轴的位置
          },
        ],
        axisPointer: {
          link: [
            {
              xAxisIndex: 'all'  //两个图表联动且tooltip合一
            }
          ]
        },
        dataZoom: [
          {
            type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0,//数据窗口范围的起始百分比0-100
            end: 100,//数据窗口范围的结束百分比0-100
            xAxisIndex: [0, 1],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: 15 //距离底部的距离
          },
          {
            type: 'inside',
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        yAxis: [

          {
            alignTicks: true,
            type: 'value',
            name: '流量m³/s',
            nameLocation: 'end', //坐标轴名称显示位置
            scale: true,
            nameTextStyle: {
              // color: "#fff"
            },
            axisLabel: {
              // color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                // color: "#fff"
              },
            },
            splitLine: {
              lineStyle: {
                // color: "#005b99"
              }
            }
          },
          {
            alignTicks: true,
            type: 'value',
            name: '水位(m)',
            nameLocation: 'end', //坐标轴名称显示位置
            // scale: true,
            nameTextStyle: {
              // color: "#fff"
            },
            axisLabel: {
              // color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                // color: "#fff"
              },
            },
            splitLine: {
              show: false,
            }
          }
        ],
        series: [

          {
            name: '出库流量',
            type: 'line',
            symbol: "none",
            color: "#FFCE2C",
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: []
          },
          {
            name: '入库流量',
            symbol: "none",
            type: 'line',
            color: "#29F8F8",
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: []
          },
          {
            name: '实测水位',
            symbol: "none",
            type: 'line',
            color: "#FF8F1F",
            data: [],
            xAxisIndex: 0,
            yAxisIndex: 1,

          },

        ]

      },
      option: {
        grid: {

          right: '100px',

        },

        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        xAxis: {
          type: 'category',
          name: '库容(百万m³)',

          position: 'bottom',
          data: []
        },
        yAxis: [
          {
            name: '水位(m)',
            type: 'value',
            axisLine: {
              show: true,
              lineStyle: {
                // color: "#fff"
              },
            },

            splitLine: {
              show: true,
            }
          },

        ],
        series: [
          {
            data: [],
            name: '水位',
            type: 'line'
          },

        ]
      },
      amplitudeDisabled: true
    })
    const overLimitTypeChange = (v) => {
      if (v) {
        state.amplitudeDisabled = false
      } else {
        state.amplitudeDisabled = true
        state.queryForm.overLimitAmplitude = ''
      }
    }
    onMounted(() => {
      state.queryForm.date = Number(moment().format('H')) >= 8 ? moment().startOf('day').add(8, 'hours') : moment().subtract(1, 'day').startOf('day').add(8, 'hours')
      getList()
      getAdcdList()
    })
    const getAdcdList = () => {
      getAdcdTree({
        adcd: ''
      }).then((res) => {
        state.adcdList = res.data[0].children
      })
    }
    const getList = () => {
      state.loading = true

      reservoirStationList({
        pageNum: state.queryForm.pageNum,
        pageSize: state.queryForm.pageSize,
        time: moment(state.queryForm.date).format("YYYY-MM-DD HH:mm:ss"),
        adcd: state.queryForm.adcd,
        projectScale: state.queryForm.projectScale,
        stnm: state.queryForm.stnm,
        stcd: state.queryForm.stcd,
        overLimitType: state.queryForm.overLimitType,
        overLimitAmplitude: state.queryForm.overLimitAmplitude,
      }).then(res => {
        state.loading = false
        state.tableList = res.data.records || []
        state.total = res.data.total || 0

      })
    }
    const formatTime = (time) => {
      return moment(time).format("YYYY-MM-DD HH:mm:ss")
    }
    const reset = () => {
      state.queryForm = {
        pageNum: 1,
        pageSize: 20,
        date: '',
        adcd: '',
        projectScale: '',
        stnm: '',
        stcd: '',
        overLimitType: '',
        overLimitAmplitude: '',
      }
      state.queryForm.date = Number(moment().format('H')) >= 8 ? moment().startOf('day').add(8, 'hours') : moment().subtract(1, 'day').startOf('day').add(8, 'hours')
      getList()
    }
    const handleExport = () => {
      proxy.download("/hydro/measuring/station/reservoirStationExport", state.exportData, `水库水情水情数据列表${new Date().getTime()}.xlsx`);
    }
    const handleDetail = (row) => {
      console.log(row)
      state.title = '水库 - ' + row.stnm
      state.detailShow = true
      state.stcd = row.stcd
      state.time = [moment().subtract(1, "day"), moment()]
      nextTick(() => {
        getChartsList()
      })
    }
    const getChartsListInfo = () => {
      if (state.activeLable == 1) {
        getChartsList()
      } else {
        getChartsList2()
      }
    }
    const handleClick = (tab) => {
      state.activeLable = tab.paneName
      if (state.activeLable == 1) {
        getChartsList()
      } else {
        getChartsList2()
      }
    }
    const getChartsList2 = () => {
      reservoirStationLine({
        startTime: moment(state.time[0]).format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(state.time[1]).format('YYYY-MM-DD HH:mm:ss'),
        stcd: state.stcd
      }).then((res) => {
        state.option.xAxis.data = []
        state.option.series[0].data = []
        // state.option.series[1].data = []
        if (res.data.length > 0) {
          res.data.forEach(el => {
            state.option.xAxis.data.push(el.w)
            state.option.series[0].data.push(el.rz)
            // state.option.series[1].data.push(el.w)
          })
          state.dialogList = res.data
          echarts
            .init(document.getElementById('rainfallChart3'))
            .dispose()
          let myEchart = echarts.init(
            document.getElementById('rainfallChart3')
          )
          myEchart.setOption(state.option)
        } else {
          ElMessage({ message: '当前时间暂无数据', type: 'warning' })
          echarts
            .init(document.getElementById('rainfallChart3'))
            .dispose()
        }
      })
    }
    const getChartsList = () => {
      reservoirStationInfo({
        startTime: moment(state.time[0]).format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(state.time[1]).format('YYYY-MM-DD HH:mm:ss'),
        stcd: state.stcd
      }).then(res => {
        state.option1.xAxis[0].data = []
        state.option1.series[0].data = []
        state.option1.series[1].data = []
        state.option1.series[2].data = []
        state.dialogList = []
        if (res.data.length > 0) {
          res.data.forEach(el => {
            el.time = moment(el.time).format("MM-DD HH:mm")
            state.option1.xAxis[0].data.push(moment(el.time).format("MM-DD HH:mm"))
            state.option1.series[0].data.push(el.outboundFlow)
            state.option1.series[1].data.push(el.inboundFlow)
            state.option1.series[2].data.push(el.waterLevel)
          })
          state.option1.xAxis[0].data.sort((a, b) => {
            return moment(a).valueOf() - moment(b).valueOf()
          })
          state.dialogList = res.data
          echarts
            .init(document.getElementById('rainfallChart3'))
            .dispose()
          let myEchart = echarts.init(
            document.getElementById('rainfallChart3')
          )
          myEchart.setOption(state.option1)
        } else {
          ElMessage({ message: '当前时间暂无数据', type: 'warning' })
          echarts
            .init(document.getElementById('rainfallChart3'))
            .dispose()
        }
      })
    }
    const dialogReset = () => {
      state.time = [moment().subtract(1, "day"), moment()];
      if (state.activeLable == 1) {
        getChartsList()
      } else {
        getChartsList2()
      }
    }
    const disabledFutureDates = (time) => {
      // 比较是否晚于当前时间
      return time.getTime() > moment().valueOf();
    }
    const handleClose = () => {
      state.detailShow = false;
      state.stcd = "";
      state.time = [moment().subtract(1, "day"), moment()];
      state.option1.xAxis.data = [];
      state.option1.series[0].data = [];
      state.option1.series[1].data = [];
      state.option1.series[2].data = []
    };
    return {
      ...toRefs(state),
      getList,
      reset,
      handleExport,
      handleDetail,
      getChartsList,
      overLimitTypeChange,
      disabledFutureDates,
      dialogReset,
      handleClick,
      getChartsListInfo,
      formatTime,
      handleClose
    };
  },
});
</script>