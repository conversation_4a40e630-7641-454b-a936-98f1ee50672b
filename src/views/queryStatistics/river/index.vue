<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm1" :inline="true" class="form-container">
        <el-form-item label="测站编码">
          <el-input v-model="queryForm.stcd" placeholder="请输入测站编码" clearable></el-input>
        </el-form-item>

        <el-form-item label="行政区">
          <el-tree-select v-model="queryForm.adcd" :data="adcdList" clearable
            :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="id" placeholder="请选择行政区"
            check-strictly />
        </el-form-item>
        <el-form-item label="流域">

          <el-tree-select v-model="queryForm.lyCode" :data="transformDataForTreeSelect(waterAreaList)" check-strictly
            :render-after-expand="false" placeholder="请选择流域" clearable />
        </el-form-item>
        <el-form-item label="时间" >
          <el-date-picker v-model="queryForm.time" type="datetime" :clearable="false" value-format="YYYY-MM-DD HH:mm" style="width: 200px" />
        </el-form-item>
        <el-form-item label="预警类型">
          <el-select v-model="queryForm.warnType" clearable placeholder="预警类型">
            <el-option label="超警戒" :value="1"></el-option>
            <el-option label="超保证" :value="2"></el-option>
            <el-option label="超历史" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="测站名称">
          <el-input v-model="queryForm.stnm" placeholder="请输入测站名称" clearable></el-input>
        </el-form-item>
        <el-form-item class="form-item-button">
          <el-button type="primary" @click="getList" icon="Search">查询</el-button>
          <el-button @click="reset" type="primary" plain icon="Refresh">重置</el-button>
        </el-form-item>
    </el-form>

    <div class="content content-table">
      <el-table v-loading="loading" :data="tableList" stripe>
        <el-table-column label="序号" width="60" type="index"></el-table-column>
        <el-table-column align="center" label="流域" prop="basinName"></el-table-column>
        <el-table-column align="center" label="河流" prop="riverName"></el-table-column>
        <el-table-column align="center" label="站名" prop="riverStationName" width="200">

          <template #default="scope">
            <span v-if="scope.row.overWarnWaterLevel > 0"
              style="color: #F56C6C;text-decoration:underline;cursor: pointer;" @click="handleDetail(scope.row)">{{
                scope.row.riverStationName }}</span>
            <span v-else style="color: #1890FF;text-decoration:underline;cursor: pointer;"
              @click="handleDetail(scope.row)">{{
                scope.row.riverStationName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="编码" prop="riverStationCode" />
        <el-table-column align="center" label="政区" prop="adminRegionName"></el-table-column>
        <el-table-column align="center" label="时间" prop="time" width="170"></el-table-column>
        <el-table-column align="center" label="水深/水位(m)" prop="waterLevel">
          <template #default="scope">
            <span v-if="scope.row.overWarnWaterLevel > 0" style="color: #F56C6C;">{{
              scope.row.waterLevel }}</span>
            <span v-else style="color: #1890FF;">{{
              scope.row.waterLevel }} </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="水势" prop="wptn">
          <template #default="scope">
            <span v-if="scope.row.wptn === '涨'" style="color: #F56C6C;">
              <el-icon><CaretTop /></el-icon>
              涨
            </span>
            <span v-else-if="scope.row.wptn === '落'" style="color: #67C23A;">
              <el-icon><CaretBottom /></el-icon>
              落
            </span>
            <span v-else>{{ scope.row.wptn }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="流量(m³/s)" prop="waterFlow"></el-table-column>
        <el-table-column align="center" label="警戒水位(m)" prop="warnWaterLevel"></el-table-column>
        <el-table-column align="center" label="超警戒水位(m)" prop="overWarnWaterLevel">
          <template #default="scope">
            <span v-if="scope.row.overWarnWaterLevel > 0" style="color: #F56C6C;">{{
              scope.row.overWarnWaterLevel }}</span>
            <span v-else> </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="保证水位(m)" prop="ensureWaterLevel"></el-table-column>
        <el-table-column align="center" label="历史最高水位(m)" prop="highestWaterLevel"></el-table-column>
      </el-table>
      <pagination :total="total" v-model:page="queryForm.pageNum"
        v-model:limit="queryForm.pageSize" @pagination="getList" />
    </div>
    <el-dialog :title="title" v-model="detailShow" width="700px" @close="handleClose">
      <div>
        <el-tabs v-model="activeLable" type="card" @tab-click="handleClick">
          <el-tab-pane label="河道水情" :name="1"></el-tab-pane>
          <el-tab-pane label="水位流量关系" :name="2"></el-tab-pane>
          <el-tab-pane label="河道大断面" :name="3"></el-tab-pane>
        </el-tabs>
        <div class="form" style="margin-bottom: 20px;">
          <el-date-picker v-model="time" type="datetimerange" range-separator="至" start-placeholder="开始时间"
            v-if="activeLable == 1" format="YYYY-MM-DD HH:mm" date-format="YYYY/MM/DD ddd" time-format="hh:mm"
            end-placeholder="结束时间" :clearable="false" style="width: 320px;margin-right: 10px;" />
          <el-button type="primary" @click="getChartsList" v-if="activeLable == 1" icon="Search">查询</el-button>
          <el-button type="primary" plain @click="resetCharts()" v-if="activeLable == 1" icon="Refresh">重置</el-button>
          <el-button type="primary" plain @click="dataShow = !dataShow">{{ dataShow ? '数据' : '图' }}</el-button>
          <!-- <el-button type="primary" plain>导出</el-button> -->
        </div>
        <div v-show="dataShow" class="monitorCharts1" style="width: 660px;height: 300px;" id="monitorCharts1"></div>
        <div v-show="!dataShow">
          <el-table :data="dialogList" height="300px" border>
            <el-table-column type="index" label="序号" width="80" align="center" v-if="activeLable != 1"></el-table-column>
            <el-table-column prop="time" label="时间" width="200" align="center" v-if="activeLable == 1"></el-table-column>
            <el-table-column prop="waterLevel" label="水位(m)" align="center" v-if="activeLable == 1"></el-table-column>
            <el-table-column prop="z" label="水位(m)" align="center" v-if="activeLable == 2"></el-table-column>
            <el-table-column prop="di" v-if="activeLable == 3" label="起点距(m)" align="center"></el-table-column>
            <el-table-column prop="zb" v-if="activeLable == 3" label="河底高程(m)" align="center"></el-table-column>
            <el-table-column v-if="activeLable != 3 && activeLable == 1" prop="waterFlow"
              label="流量(m³/s)" align="center"></el-table-column>
            <el-table-column v-if="activeLable != 3 && activeLable == 2" prop="q" label="流量(m³/s)" align="center"></el-table-column>
          </el-table>
        </div>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance } from "vue";
import { selectStlyList, getAdcdTree, getRvFloInfo } from "@/api/watershed/ads";

import { riverStationList, riverWaterLevel, riverSectionDetail } from "@/api/watershed/query/index"
import { ElMessage } from "element-plus";
import * as echarts from "echarts";
import moment from "moment";
export default defineComponent({
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      queryForm: {
        adcd: '',
        stcd: "",
        stnm: "",
        pageNum: 1,
        pageSize: 20,
        time: moment().format('YYYY-MM-DD HH:mm:ss')
      },
      total: 0,
      tableList: [],
      adcdOption: [],
      lycodeOptions: [],
      loading: false,
      adcdList: [],
      waterAreaList: [],
      activeLable: '',
      dialogList: [],
      detailShow: false,
      dataShow: true,
      title: '',
      time: [moment().subtract(7, 'days'), moment()],
      stcd: '',
      stationType: 1,
      option: {
        grid: {
          left: '60px',  //距左边距 留够name的宽度
          right: '60px',
          bottom: '60px',
          top: '50px',
          // containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        legend: {
          data: [
            '水位',
            '流量',
          ],
          // selected: {
          //   '保证水位': false,
          //   '警戒水位': false,
          //   '历史最高水位': false,
          // },
          textStyle: {
            // color: '#fff',
            fontSize: 12,
            fontFamily: 'PingFang SC'
          }
        },
        xAxis: {
          type: 'category',
          data: [],
          boundaryGap: false,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            // interval: "0",
            // color: "#fff"
          },
          axisLine: {
            lineStyle: {
              // color: "#fff"
            }
          },
          axisTick: {
            show: false
          },
        },
        dataZoom: [
          {
            type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0,//数据窗口范围的起始百分比0-100
            end: 100,//数据窗口范围的结束百分比0-100
            xAxisIndex: [0],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: 0 //距离底部的距离
          },
          {
            type: 'inside',
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '水位(m)',
            boundaryGap: ['0', '12.5%'],
            scale: true,
            nameTextStyle: {
              // color: "#fff"
            },
            axisLabel: {
              // color: "#fff"
            },
            splitLine: {
              lineStyle: {
                // color: "#005b99"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                // color: "#fff"
              },
            },
          },
          {
            type: 'value',
            name: '流量m³/s',
            boundaryGap: ['0', '30.5%'],
            // scale: true,
            nameTextStyle: {
              // color: "#fff"
            },
            axisLabel: {
              // color: "#fff"
            },
            splitLine: {
              show: false,
            },
            axisLine: {
              show: true,
            },
          }
        ],
        series: [
          {
            name: '水位',
            type: 'line',
            symbol: "none",
            color: "#059DFE",
            data: []
          },



          {
            name: '流量',
            type: 'line',
            symbol: "none",
            yAxisIndex: 1,
            // stack: '大型',
            color: "#1AD2A4",
            data: []
          },
        ]

      },
      option2: {
        grid: {
          left: '60px',  //距左边距 留够name的宽度
          right: '60px',
          bottom: '60px',
          top: '50px',
          // containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        xAxis: {
          type: 'category',
          name: '水位(m)',
          data: [],
          boundaryGap: false,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            // interval: "0",
            // color: "#fff"
          },
          axisLine: {
            lineStyle: {
              // color: "#fff"
            }
          },
          axisTick: {
            show: false
          },
        },
        dataZoom: [
          {
            type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0,//数据窗口范围的起始百分比0-100
            end: 100,//数据窗口范围的结束百分比0-100
            xAxisIndex: [0],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: 0 //距离底部的距离
          },
          {
            type: 'inside',
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '流量(m³/s)',
            scale: true,
            nameTextStyle: {
              // color: "#fff"
            },
            axisLabel: {
              // color: "#fff"
            },
            splitLine: {
              lineStyle: {
                // color: "#005b99"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                // color: "#fff"
              },
            },
          },

        ],
        series: [
          {
            name: '流量',
            type: 'line',
            symbol: "none",
            color: "#059DFE",
            areaStyle: {},
            data: []
          },


        ]

      },
      option3: {
        grid: {
          left: '50px',  //距左边距 留够name的宽度
          right: '70px',
          bottom: '60px',
          top: '50px',
          // containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        xAxis: {
          type: 'category',
          name: '起点距(m)',
          data: [],
          boundaryGap: false,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            // interval: "0",
            // color: "#fff"
          },
          axisLine: {
            onZero: false,
            lineStyle: {
              // color: "#fff"
            }
          },
          // axisTick: {
          //   show: true
          // },
        },
        dataZoom: [
          {
            type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0,//数据窗口范围的起始百分比0-100
            end: 100,//数据窗口范围的结束百分比0-100
            xAxisIndex: [0],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: 0 //距离底部的距离
          },
          {
            type: 'inside',
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        yAxis: [
          {
            min: 0,
            type: 'value',
            name: '河底高程(m)',
            scale: true,
            nameTextStyle: {
              // color: "#fff"
            },
            axisLabel: {
              onZero: true,
              // color: "#fff"
            },
            // axisTick: {
            //   show: true
            // },
            splitLine: {
              lineStyle: {
                // color: "#005b99"
              }
            },
            axisLine: {
              show: true,
              onZero: false,
              lineStyle: {
                // color: "#fff"
              },
            },
          },

        ],
        series: [
          {
            name: '河底高程',
            type: 'line',
            symbol: "none",
            color: "E1C08B",
            areaStyle: {
              color: '#E1C08B',
              shadowBlur: 10,
              opacity: 1
            },
            data: []
          },


        ]

      },
      exportData: {}
    })
    onMounted(() => {
      getAdcdList()
      getAllWater()
      getList()
    })
    const getAdcdList = () => {
      getAdcdTree({
        adcd: ''
      }).then((res) => {
        state.adcdList = res.data[0].children
      })

    }
    const getList = () => {
      state.loading = true
      // state.time = state.queryForm.date
      riverStationList({
        startTime: state.queryForm.time,
        stcd: state.queryForm.stcd,
        pageSize: state.queryForm.pageSize,
        pageNum: state.queryForm.pageNum,
        lyCode: state.queryForm.lyCode,
        stnm: state.queryForm.stnm,
        adcd: state.queryForm.adcd,
        warnType: state.queryForm.warnType,
      }).then(res => {
        state.tableList = res.data
        state.total = res.total
        state.loading = false
      })
    }
    const handleClick = (tab) => {
      state.activeLable = tab.paneName
      if (state.activeLable == 1) {
        getChartsList()
      } else if (state.activeLable == 2) {
        getChartsList2()
      } else {
        getChartsList3()
      }
    }
    const reset = () => {

      state.queryForm = {
        pageNum: 1,
        pageSize: 20,
        time: moment().format('YYYY-MM-DD HH:mm:ss'),
        stcd: '',
        lyCode: '',
        stnm: '',
        adcd: '',
        warnType: '',
      }
      getList()
    }
    const resetCharts = () => {
      state.time = [moment().subtract(1, 'days'), moment()]
      if (state.activeLable == 1) {
        getChartsList()
      } else if (state.activeLable == 2) {
        getChartsList2()
      } else {
        getChartsList3()
      }
    }
    const handleExport = () => {
      proxy.download("/sl323/measuring/station/riverWaterLevelExcel", state.exportData, `河道水情数据列表${new Date().getTime()}.xlsx`);
    }
    const handleDetail = (row) => {
      state.title = row.riverStationName
      state.detailShow = true
      state.stcd = row.riverStationCode
      state.stationType = row.stationType
      state.activeLable = 1
      nextTick(() => {
        getChartsList()
      })
    }
    const handleClose = () => {
      state.dataShow = true
      state.detailShow = false
      // 清空数据
      state.dialogList = []
      state.option.xAxis.data = []
      state.option.series[0].data = []
      state.option.series[1].data = []
      state.time = [moment().subtract(7, 'days'), moment()]
      // 销毁 echarts
      const chartEle = document.getElementById('monitorCharts1')
      if(chartEle) {
        echarts
          .init(chartEle)
          .dispose()
      }
    }
    const getChartsList = () => {
      riverWaterLevel({
        startTime: moment(state.time[0]).format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(state.time[1]).format('YYYY-MM-DD HH:mm:ss'),
        stcd: state.stcd
      }).then(res => {
        if (res.data.length == 0) {
          echarts
            .init(document.getElementById('monitorCharts1'))
            .dispose()
          state.dialogList = []
          ElMessage({ message: '当前时间暂无数据', type: 'warning' })
          return
        }


        state.option.xAxis.data = []
        res.data.forEach(el => {
          // state.option.xAxis.data.push(el.time)
          state.option.xAxis.data.push(moment(el.time).format('MM-DD HH:mm'))
          state.option.series[0].data.push(el.waterLevel)
          state.option.series[1].data.push(el.waterFlow)  //流量
          // state.option.series[1].data.push(el.ensureWaterLevel)
          // state.option.series[2].data.push(el.warnWaterLevel)
          // state.option.series[3].data.push(el.highestWaterLevel)

        })
        console.log(state.option)
        state.dialogList = res.data
        state.dialogList.sort((a, b) => {
          return moment(b.time).valueOf() - moment(a.time).valueOf()
        })
        echarts
          .init(document.getElementById('monitorCharts1'))
          .dispose()
        let myEchart = echarts.init(
          document.getElementById('monitorCharts1')
        )
        myEchart.setOption(state.option)
      })
    }
    const getChartsList2 = () => {
      getRvFloInfo(state.stcd).then(res => {
        if (res.data.length == 0) {
          echarts
            .init(document.getElementById('monitorCharts1'))
            .dispose()
          state.dialogList = []
          ElMessage({ message: '当前时间暂无数据', type: 'warning' })
          return
        }
        state.option2.xAxis.data = []
        let riverData = res.data.riverFloodWaterFlowDtos || []
        riverData.forEach(el => {
          // state.option.xAxis.data.push(el.time)
          // state.option.xAxis.data.push(moment(el.time).format('MM-DD HH:mm'))
          state.option2.xAxis.data.push(el.z)

          state.option2.series[0].data.push(el.q)

        })
        state.dialogList = riverData
        echarts
          .init(document.getElementById('monitorCharts1'))
          .dispose()
        let myEchart = echarts.init(
          document.getElementById('monitorCharts1')
        )
        myEchart.setOption(state.option2)
      })
    }
    const getChartsList3 = async () => {
      let res = await riverSectionDetail(state.stcd)
      let editTable = []
      if (res.data.length > 0) {

        editTable = res.data.map(item => {
          return {
            di: item.di,
            zb: item.zb
          }
        })
      }
      state.option3.xAxis.data = []
      state.option3.series[0].data = []
      let max = editTable ? Math.max(...editTable.map(item => item.zb)) : 0
      state.option3.yAxis[0].max = max * 1.2
      state.option3.xAxis.data = editTable.map(item => item.di)
      state.option3.series[0].data = editTable.map(item => item.zb)
      // state.option.xAxis.data.unshift(0)
      // state.option.series[0].data.unshift(0)
      state.dialogList = res.data

      echarts
        .init(document.getElementById('monitorCharts1'))
        .dispose()
      let myEchart = echarts.init(
        document.getElementById('monitorCharts1')
      )
      myEchart.setOption(state.option3)

    }
    const disabledFutureDates = (time) => {
      // 比较是否晚于当前时间
      return time.getTime() > moment().valueOf();
    }
    const getAllWater = async () => {
      let res = await selectStlyList({ pageNum: 1, pageSize: 999 })
      state.waterAreaList = res.data || []
    }

    const transformDataForTreeSelect = (data) => {
      // 递归地转换数据以匹配 el-tree-select 的需求
      return data.map(item => ({
        label: item.data.name, // 使用 'name' 属性作为标签
        value: item.data.basinId, // 使用 'basinId' 属性作为值
        children: item.children ? transformDataForTreeSelect(item.children) : [], // 递归转换子节点
      }));
    }

    return {
      ...toRefs(state),
      getList,
      reset,
      handleExport,
      handleDetail,
      getChartsList,
      disabledFutureDates,
      transformDataForTreeSelect,
      handleClick,
      resetCharts,
      handleClose
    };
  },
});
</script>
