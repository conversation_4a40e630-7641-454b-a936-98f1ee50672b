<template>
  <div class="real-time-video-container" id="full-box">
    <div class="left-video-list">
      <div class="list-title">
        <div class="title-query">
          <el-input
            v-model="stName"
            clearable
            placeholder="请输入监控点名称"
            suffix-icon="Search"
            @input="filterNode"
          ></el-input>
        </div>
      </div>
      <div class="list-box">
        <el-tree
          ref="dcTree"
          class="data-source-s-tree"
          v-loading="treeLoading"
          :data="treeData"
          node-key="id"
          highlight-current
          :props="defaultProps"
          @node-click="getProjectData"
          :filter-node-method="filterNodeMethod"
        >
          <template v-slot="{ node, data }">
            <div class="custom-tree-node">
              <p class="c-t-label">
                <el-icon color="orange" v-if="!data.indexCode">
                  <FolderOpened />
                </el-icon>
                <!-- :color="data.online === 1 ? 'green' : 'red'" -->
                <el-icon v-if="data.indexCode">
                  <VideoCamera />
                </el-icon>
                <span class="c-t-name"
                  >{{ node.label }}
                  <span v-if="data.child"
                    >({{ getFilteredChildCount(data) }})</span
                  ></span
                >
              </p>
            </div>
          </template>
        </el-tree>
      </div>
      <div class="video-control">
        <div class="v-c-title">云台控制</div>
        <div class="control-box">
          <div class="control-box-top">
            <div class="minus">
              <el-icon @click="ptzcontrol('ZOOM_OUT')" class="c-btn">
                <Minus />
              </el-icon>
            </div>
            <div class="direction-box">
              <p
                class="c-d-btn d-btn-center"
                @click="ptzcontrol('GOTO_PRESET')"
              >
                <svg-icon
                  icon-class="monitor"
                  :size="20"
                  color="white"
                ></svg-icon>
              </p>
              <p class="c-d-btn" @click="ptzcontrol('UP')">
                <el-icon class="cd-btn-t">
                  <ArrowUp />
                </el-icon>
              </p>
              <p class="c-d-btn" @click="ptzcontrol('RIGHT')">
                <el-icon class="cd-btn-r">
                  <ArrowUp />
                </el-icon>
              </p>
              <p class="c-d-btn" @click="ptzcontrol('LEFT')">
                <el-icon class="cd-btn-l">
                  <ArrowUp />
                </el-icon>
              </p>
              <p class="c-d-btn" @click="ptzcontrol('DOWN')">
                <el-icon class="cd-btn-b">
                  <ArrowUp />
                </el-icon>
              </p>
            </div>
            <div class="plus">
              <el-icon class="c-btn" @click="ptzcontrol('ZOOM_IN')">
                <Plus />
              </el-icon>
            </div>
          </div>
          <div class="control-box-bottom">
            <el-slider v-model="speedValue" :max="100" :show-tooltip="false">
            </el-slider>
            <p class="c-value">{{ speedValue }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="right-video-show">
      <div class="top-btn-control">
        <div>
          <span class="t-b-label">分屏：</span>
          <div class="t-b-svg">
            <svg-icon
              icon-class="singlesSquare"
              :size="20"
              color="#989898"
              :class="{ 'current-size': currentSize === 1 }"
              @click="changeShowSize(1)"
            ></svg-icon>
            <svg-icon
              icon-class="gridfour"
              :size="20"
              color="#989898"
              :class="{ 'current-size': currentSize === 4 }"
              @click="changeShowSize(4)"
            ></svg-icon>
            <svg-icon
              icon-class="grid"
              :size="20"
              color="#989898"
              :class="{ 'current-size': currentSize === 9 }"
              @click="changeShowSize(9)"
            ></svg-icon>
            <svg-icon
              icon-class="grid16"
              :size="20"
              color="#989898"
              :class="{ 'current-size': currentSize === 16 }"
              @click="changeShowSize(16)"
            ></svg-icon>
          </div>
        </div>
        <p class="full-screen" @click="changeFullScreen">
          <svg-icon
            icon-class="fullscreen"
            :size="20"
            color="#989898"
          ></svg-icon>
        </p>
      </div>
      <div class="video-container">
        <!-- 预创建16个固定的视频容器 -->
        <div
          v-for="index in 16"
          :key="`video-container-${index - 1}`"
          class="video-box"
          :class="{
            'item-1-size': currentSize === 1,
            'item-4-size': currentSize === 4,
            'item-9-size': currentSize === 9,
            'item-16-size': currentSize === 16,
            'current-select': currentSelect === index - 1,
            'video-hidden': index - 1 >= currentSize,
          }"
          :style="{ display: index - 1 >= currentSize ? 'none' : 'block' }"
          @click="selcetVideoBox(index - 1)"
        >
          <el-icon
            class="iconClose"
            :class="{ 'show-close': videoContainers[index - 1]?.videoUrl }"
            @click="closeVideo(index - 1)"
          >
            <Close />
          </el-icon>
          <p v-if="!videoContainers[index - 1]?.videoUrl" class="video-msg">
            {{ videoContainers[index - 1]?.msg || "无信号" }}
          </p>
          <hikvision-video-player
            v-if="videoContainers[index - 1]?.videoUrl"
            :videoInfo="videoContainers[index - 1]"
            :ref="(el) => (videoRefs[index - 1] = el)"
            :showControls="true"
            :autoplay="true"
            :muted="true"
            @player-closed="closeVideo(index - 1)"
            class="video-player"
          ></hikvision-video-player>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
  onDeactivated,
  watch,
  nextTick,
} from "vue";
import {
  getOnlineVideoList,
  getOnlineVideoPushUrl,
  controlMonitorPoint,
} from "@/api/video/online";
import HikvisionVideoPlayer from "./components/HikvisionVideoPlayer.vue";
import { ElMessage } from "element-plus";

// 响应式状态
const treeData = ref([]);
const videoContainers = ref([]); // 改名为 videoContainers，预创建16个容器
const currentSize = ref(1);
const currentSelect = ref(0);
const defaultProps = reactive({
  label: "name",
  children: "child",
  isLeaf: "leaf",
});
const treeLoading = ref(false);
const stName = ref("");
const ptzcontrolParams = reactive({
  cameraIndexCode: "",
  command: "",
  speed: 40,
  presetIndex: 0,
});
const speedValue = ref(40);
const idarr = ref([]);
const dcTree = ref(null);
const videoRefs = ref([]);

const getHnskVideoTreeList = async () => {
  try {
    treeLoading.value = true;
    treeData.value = [];
    idarr.value = [];
    const res = await getOnlineVideoList();

    if (res.code == "0") {
      if (res.data && res.data.list && res.data.list.length > 0) {
        // 保存原始数据，并过滤只保留拉萨市的视频
        const originalList = res.data.list.filter(
          (item) => item.externalCode && item.externalCode.includes("5401")
        );

        idarr.value = originalList.map((item) => item.indexCode);

        // 构建树形结构
        const treeMap = {};

        // 处理每个视频数据
        originalList.forEach((item) => {
          // 使用regionPathName字段分割路径
          const pathParts = item.regionPathName
            ? item.regionPathName.split("/")
            : ["未知位置"];

          // 从根节点开始构建
          let currentLevel = treeMap;
          let currentPath = "";

          // 遍历路径的每一级，构建树
          pathParts.forEach((part, index) => {
            currentPath = currentPath ? `${currentPath}/${part}` : part;

            // 如果当前层级不存在这个节点，创建它
            if (!currentLevel[part]) {
              currentLevel[part] = {
                id: currentPath,
                name: part,
                child: {},
                leaf: false,
              };
            }

            // 如果是最后一级路径，则添加视频节点
            if (index === pathParts.length - 1) {
              if (!currentLevel[part].videos) {
                currentLevel[part].videos = [];
              }

              // 添加视频节点
              currentLevel[part].videos.push({
                ...item,
                id: item.indexCode,
                name: item.name,
                indexCode: item.indexCode,
                leaf: true,
              });
            }

            // 移动到下一层
            currentLevel = currentLevel[part].child;
          });
        });

        // 将树形结构转换为数组形式
        const convertToArray = (treeObj) => {
          return Object.values(treeObj).map((node) => {
            // 转换子节点
            const children =
              Object.keys(node.child).length > 0
                ? convertToArray(node.child)
                : [];

            // 如果有视频，添加到子节点
            if (node.videos && node.videos.length > 0) {
              children.push(...node.videos);
            }

            // 返回格式化的节点
            return {
              ...node,
              child: children.length > 0 ? children : null,
            };
          });
        };

        // 转换为最终的树形结构
        treeData.value = convertToArray(treeMap);
      }
    } else {
      ElMessage.error(res.msg || "获取视频列表失败");
    }
    treeLoading.value = false;

    // 初始化16个固定的视频容器
    videoContainers.value = [];
    for (let i = 0; i < 16; i++) {
      videoContainers.value.push({
        id: i,
        deviceId: "",
        msg: "无信号",
        videoUrl: "",
      });
    }
  } catch (error) {
    console.log(error);
    treeLoading.value = false;
    ElMessage.error("获取视频列表失败：" + error.message);
  }
};

// 点击左侧水库进行播放
const getProjectData = async (data) => {
  if (data.indexCode) {
    // 销毁当前播放器
    videoRefs.value[currentSelect.value]?.destroyPlayer();
    // 先更新状态为加载中
    videoContainers.value[currentSelect.value] = {
      videoUrl: "",
      id: data.indexCode,
      msg: "加载中...",
    };

    try {
      const res = await getOnlineVideoPushUrl({
        cameraIndexCode: data.indexCode,
        protocol: "ws", // ws, rtsp, hls, rtmp
        streamType: 0,
        transmode: 1,
      });

      if (res.code == 200) {
        // 检查当前选中窗口是否还是请求时的窗口
        if (videoContainers.value[currentSelect.value]?.id === data.indexCode) {
          videoContainers.value[currentSelect.value] = {
            videoUrl: res.data,
            id: data.indexCode,
            name: data.name || "",
            msg: "",
            useHikvision: true,
          };
        }
      } else {
        if (videoContainers.value[currentSelect.value]?.id === data.indexCode) {
          videoContainers.value[currentSelect.value] = {
            videoUrl: "",
            id: data.indexCode,
            msg: res.msg || "获取视频失败",
          };
        }
        ElMessage.warning(res.msg || "获取视频失败");
      }
    } catch (error) {
      console.error("获取视频URL失败:", error);
      if (videoContainers.value[currentSelect.value]?.id === data.indexCode) {
        videoContainers.value[currentSelect.value] = {
          videoUrl: "",
          id: data.indexCode,
          msg: "视频加载失败，服务器可能不可用",
        };
      }
      ElMessage.error("获取视频URL失败，请检查网络连接");
    }
  }
};

// 调整所有播放器的窗口大小
const resizeAllPlayers = () => {
  console.log("开始调整所有播放器窗口大小...");

  // 遍历所有有视频的容器
  for (let i = 0; i < currentSize.value && i < 16; i++) {
    if (videoContainers.value[i]?.videoUrl && videoRefs.value[i]) {
      // 延迟调用以确保DOM已更新
      setTimeout(() => {
        if (
          videoRefs.value[i] &&
          typeof videoRefs.value[i].autoResize === "function"
        ) {
          videoRefs.value[i].autoResize();
        }
      }, 100);
    }
  }
};

// 分屏显示数量切换 - 优化版本，不重新创建组件
const changeShowSize = (size) => {
  console.log(`切换分屏模式: ${currentSize.value} -> ${size}`);

  // 保存当前选中的视频数据
  const currentData = videoContainers.value[currentSelect.value]
    ? { ...videoContainers.value[currentSelect.value] }
    : null;

  // 更新分屏大小
  const oldSize = currentSize.value;
  currentSize.value = size;

  // 如果是从大分屏切换到小分屏，需要处理超出范围的视频
  if (size < oldSize) {
    // 关闭超出新分屏范围的视频播放器
    for (let i = size; i < oldSize && i < 16; i++) {
      if (videoContainers.value[i]?.videoUrl) {
        // 先销毁播放器
        if (
          videoRefs.value[i] &&
          typeof videoRefs.value[i].destroyPlayer === "function"
        ) {
          videoRefs.value[i].destroyPlayer();
        }
        // 重置视频数据，但保持容器存在
        videoContainers.value[i] = {
          id: i,
          deviceId: "",
          msg: "无信号",
          videoUrl: "",
        };
      }
    }
  }

  // 重置当前选中为第一个窗口
  currentSelect.value = 0;

  // 如果有当前播放的视频，将其移动到第一个位置
  if (currentData?.videoUrl) {
    videoContainers.value[0] = {
      ...currentData,
      id: currentData.id, // 保持原有的设备ID
    };
  }

  // 等待DOM更新后调整所有播放器的窗口大小
  nextTick(() => {
    resizeAllPlayers();
  });

  console.log(`分屏切换完成，当前显示 ${size} 个窗口`);
};

// 选中播放框
const selcetVideoBox = (index) => {
  if (index >= currentSize.value) {
    index = 0;
  }
  currentSelect.value = index;
};

// 关闭选中播放
const closeVideo = (index) => {
  if (videoContainers.value[index] && videoContainers.value[index].videoUrl) {
    // 关闭前先销毁播放器
    if (
      videoRefs.value[index] &&
      typeof videoRefs.value[index].destroyPlayer === "function"
    ) {
      videoRefs.value[index].destroyPlayer();
    }

    // 重置视频数据
    videoContainers.value[index] = {
      videoUrl: "",
      id: index,
      deviceId: "",
      msg: "无信号",
    };
  }
};

// 控制台操作
const ptzcontrol = async (command) => {
  if (!videoContainers.value[currentSelect.value].videoUrl) {
    ElMessage.warning("请选择播放成功的窗口，进行控制");
    return;
  }

  try {
    ptzcontrolParams.cameraIndexCode =
      videoContainers.value[currentSelect.value].id;
    ptzcontrolParams.command = command;
    ptzcontrolParams.speed = speedValue.value;

    // 对于预置点命令，设置默认预置点编号
    if (command === "GOTO_PRESET") {
      ptzcontrolParams.presetIndex = 1; // 默认使用预置点1，可根据需要进行修改
      ptzcontrolParams.action = 1; // 默认停止所有操作
    }

    const res = await controlMonitorPoint({
      ...ptzcontrolParams,
      action: 0,
    });
    if (res.code === 200) {
      await controlMonitorPoint({
        ...ptzcontrolParams,
        action: 1,
      });
    }
  } catch (error) {
    console.error("控制指令执行失败:", error);
    ElMessage.error("控制指令执行失败，请检查网络连接");
  }
};

const changeFullScreen = () => {
  const fullarea = document.getElementById("full-box");
  fullarea.requestFullscreen();
};

// 监控点名称过滤方法
const filterNodeMethod = (value, data) => {
  if (!value) return true;
  // 如果当前节点是目录（没有indexCode），则不直接过滤，让其子节点决定是否显示
  if (!data.indexCode) {
    return true;
  }
  // 对监控点名称进行模糊匹配
  return data.name.toLowerCase().includes(value.toLowerCase());
};

// 当输入框内容变化时触发过滤
const filterNode = () => {
  dcTree.value?.filter(stName.value);
};

// 获取符合过滤条件的子节点数量
const getFilteredChildCount = (data) => {
  // 如果没有搜索条件，返回原始长度
  if (!stName.value) {
    return data.child ? data.child.length : 0;
  }

  // 如果有搜索条件，计算符合条件的子节点数量
  if (!data.child) return 0;

  // 递归计算符合条件的子节点数量
  let count = 0;

  for (const child of data.child) {
    if (child.indexCode) {
      // 如果是监控点，判断是否符合过滤条件
      if (child.name.toLowerCase().includes(stName.value.toLowerCase())) {
        count++;
      }
    } else {
      // 如果是目录，递归计算其下符合条件的节点数量
      count += getFilteredChildCount(child);
    }
  }

  return count;
};

// 生命周期钩子
onMounted(() => {
  getHnskVideoTreeList();
});

onDeactivated(() => {
  videoContainers.value.forEach((item, index) => {
    if (item.videoUrl != "") {
      closeVideo(index);
    }
  });
});

onBeforeUnmount(() => {
  videoContainers.value.forEach((item, index) => {
    if (item.videoUrl != "") {
      closeVideo(index);
    }
  });
});
</script>

<style lang="scss" scoped>
.real-time-video-container {
  // height: 100%;
  height: calc(100vh - 100px);
  width: 100%;
  display: flex;
  border: 1px solid #e9e9e9;
  background-color: #fff;
  justify-content: space-between;

  .left-video-list {
    width: 22%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .list-title {
      height: 50px;
      padding: 10px;
      border-right: 1px solid #e9e9e9;
      border-bottom: 1px solid #e9e9e9;

      .title-query {
        width: 100%;
        height: 30px;
      }

      .title-check {
        width: 100%;
        height: 35px;
        margin-top: 5px;
        display: flex;
        justify-content: flex-end;

        :deep(.el-checkbox__inner) {
          border-color: #4f96be;
        }
      }
    }

    .list-box {
      flex: 1;
      background: #edf3f9;
      height: calc(100% - 310px);
      border-left: 1px solid #e9e9e9;

      :deep(.data-source-s-tree) {
        height: 100%;
        overflow: auto;
        color: #989898;
        background-color: #f4f4f4;

        .c-t-label {
          display: flex;
          align-items: center;

          .c-t-name {
            margin-left: 5px;
          }
        }

        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
          width: 4px;
          height: 4px; /* 确保横向滚动条高度 */
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          // 滚动条颜色 灰色
          background: #ccc;
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: #ccc;
        }

        .el-tree-node {
          min-width: 100%;
          width: max-content;
        }

        .el-tree-node__content:hover {
          background-color: #3e8ef7;
        }

        .el-tree-node.is-current > .el-tree-node__content {
          background-color: #3e8ef7;
        }

        .el-tree-node__content {
          &:hover {
            color: #fff;
          }
        }

        .el-tree-node.is-current > .el-tree-node__content {
          color: #fff;
        }
      }
    }

    // ------------ 控制板 -------------
    .video-control {
      height: 220px;
      width: calc(100% - 2px);
      // position: absolute;
      bottom: 1px;
      left: 1px;
      z-index: 10;
      margin-left: 0;
      border: 1px solid #e9e9e9;
      transition: margin-left 1s ease;
      background-color: #fff;

      .v-c-title {
        height: 30px;
        color: #262626;
        line-height: 30px;
        padding-left: 15px;
        border-top: 1px solid #3d4245;
        border-bottom: 1px solid #3d4245;
        border-color: #e9e9e9;
      }

      .control-box {
        // min-width: 310px;
        width: 100%;
        height: 200px;
        color: #fff;
        padding: 0 10px;
        position: relative;
        background: #fff;

        .c-btn {
          width: 2.5vw;
          height: 2.5vw;
          border-radius: 50%;
          background: #3e8ef7;
          font-weight: bold;
          position: relative;

          &::before {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }

        .control-box-top {
          display: flex;
          height: calc(100% - 60px);
          align-items: center;
          justify-content: space-evenly;
          padding: 20px 0;
          position: relative;

          .direction-box {
            width: 103px;
            height: 103px;
            display: flex;
            flex-wrap: wrap;
            position: relative;
            transform: rotate(45deg);
            border-radius: 50%;
            overflow: hidden;
            border: 1px solid #fff;

            .c-d-btn {
              width: 50%;
              height: 50%;
              margin: 0 0;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #3e8ef7;
              border: 1px solid #fff;
              cursor: pointer;

              i {
                font-size: 20px;
                font-weight: bold;
              }

              &:hover {
                background: rgb(142, 187, 187);
              }
            }

            .cd-btn-t {
              transform: rotate(-45deg);
            }

            .cd-btn-r {
              transform: rotate(45deg);
            }

            .cd-btn-l {
              transform: rotate(-135deg);
            }

            .cd-btn-b {
              transform: rotate(135deg);
            }

            .d-btn-center {
              top: 25%;
              left: 24%;
              z-index: 15;
              width: 50px;
              height: 50px;
              // border: none;
              border-radius: 50%;
              position: absolute;
              transform: rotate(-45deg);
              border: 2px solid #fff;

              .icon-jingyin {
                text-align: center;
                padding-left: 10px;
                font-size: 20px;
              }
            }
          }

          .minus,
          .plus {
            .c-btn {
              border: 2px solid #fff;
              line-height: 30px;
              background: #3e8ef7;
              cursor: pointer;

              &:hover {
                background: rgb(142, 187, 187);
              }
            }
          }
        }

        .control-box-bottom {
          position: relative;
          width: 100%;
          height: 50px;
          display: flex;
          justify-content: space-evenly;

          .el-slider {
            width: 70%;

            :deep(.el-slider__runway) {
              background-color: #c1c1c1;
            }
          }

          .c-value {
            width: 5%;
            color: #000;
          }
        }

        .panel-control {
          position: absolute;
          right: -20px;
          top: 40%;
          width: 20px;
          height: 50px;
          cursor: pointer;
          color: #737373;
          font-weight: 300;
          line-height: 43px;
          border-radius: 0 5px 5px 0;
          border: 1px solid #e9e9e9;
          background: #fff;
          display: flex;
          align-items: center;
        }
      }
    }

    .hidden-panel {
      margin-left: -100%;
    }
  }

  // 右侧
  .right-video-show {
    width: 78%;
    height: 100%;

    .top-btn-control {
      height: 40px;
      color: #989898;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #edf3f9;

      div:first-child {
        display: flex;
        align-items: center;

        .t-b-svg {
          width: 100px;
          height: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          cursor: pointer;
        }
      }

      .t-b-label {
        margin-left: 10px;
      }

      .current-size {
        color: #3e8ef7 !important;
      }

      .full-screen {
        height: 30px;
        width: 30px;
        cursor: pointer;
        margin: 7px 10px;
        text-align: center;
        display: flex;
        align-items: center;
      }
    }

    .video-container {
      width: 100%;
      height: calc(100% - 40px);
      display: flex;
      flex-wrap: wrap;

      .video-box {
        width: 100%;
        height: 100%;
        background: #000;
        border: 1px solid #fff;
        position: relative;

        .video-player {
          width: 100%;
          height: 100%;
        }

        .iconClose {
          position: absolute;
          cursor: pointer;
          top: 8px;
          right: 8px;
          z-index: 999;
          padding: 6px;
          color: #fff;
          visibility: hidden;
          opacity: 0;
          border-radius: 50%;
          background: rgba(0, 0, 0, 0.6);
          backdrop-filter: blur(4px);
          transition: all 0.3s ease;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28px;
          height: 28px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

          &:hover {
            background: rgba(255, 59, 48, 0.8);
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(255, 59, 48, 0.3);
          }

          &:active {
            transform: scale(0.95);
          }
        }

        .video-msg {
          color: #3e8ef7;
          position: absolute;
          top: 50%;
          left: 0;
          width: 100%;
          text-align: center;
          transform: translateY(-50%);
        }

        &:hover > .show-close {
          visibility: visible;
          opacity: 1;
        }

        .show-flow {
          position: absolute;
          bottom: 30px;
          left: 20px;
        }

        .show-flow-item {
          display: block;
          color: #ffffff;
        }
      }

      .current-select {
        position: relative;
      }

      .current-select::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 4px solid #3e8ef7;
        pointer-events: none;
        z-index: 1;
      }

      .item-1-size {
        width: 100%;
        height: 100%;
      }

      .item-4-size {
        width: 50%;
        height: 50%;
      }

      .item-6-size {
        width: 33%;
        height: 50%;
      }

      .item-9-size {
        width: 33.3%;
        height: 33.3%;
      }

      .item-16-size {
        width: 25%;
        height: 25%;
      }

      .video-hidden {
        display: none !important;
      }
    }
  }
}

// 图标
.iconfont {
  font-size: 30px;
  margin-right: 10px;
}

.iconfont-sp {
  width: 25px;
  height: 25px;
  margin-right: 0;
}

.iconfont-sp-s {
  width: 19px;
  height: 19px;
}
</style>
