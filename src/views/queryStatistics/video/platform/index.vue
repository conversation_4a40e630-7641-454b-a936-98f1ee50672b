<template>
    <div class="app-container">
        <el-form :model="queryForm" ref="queryForm1" :inline="true" class="form-container" v-show="showSearch">
            <el-form-item label="视频平台名称">
                <el-input v-model="queryForm.name" placeholder="视频平台名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="国标编码">
                <el-input v-model="queryForm.gbCode" placeholder="国标编码" clearable></el-input>
            </el-form-item>

            <div class="form-item-button">
                <el-button type="primary" @click="getList" icon="Search">查询</el-button>
                <el-button @click="reset" type="primary" plain icon="Refresh">重置</el-button>
            </div>
        </el-form>
        <div style="margin: 10px 0; padding: 0 5px ">
            <el-row :gutter="10">
                <el-col :span="1.5">
                    <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
                </el-col>

                <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
        </div>
        <div class="content content-table">
            <el-table v-loading="loading" :data="tableList" stripe>
                <el-table-column label="序号" width="60" type="index"></el-table-column>
                <el-table-column label="视频平台名称" align="center" prop="name"></el-table-column>
                <el-table-column label="国标编码" align="center" prop="gbCode"></el-table-column>
                <el-table-column label="域" align="center" prop="domain"></el-table-column>
                <el-table-column label="IP" align="center" prop="ip"></el-table-column>
                <el-table-column label="端口" align="center" prop="port"></el-table-column>
                <el-table-column label="用户名" align="center" prop="userName"></el-table-column>
                <el-table-column label="密码" align="center" prop="password"></el-table-column>
                <el-table-column label="状态" align="center" prop="status">
                    <template #default="scope">
                        <div class="status-container">
                            <span class="status-dot" :class="{ 'online': scope.row.status === 1, 'offline': scope.row.status === 2 }"></span>
                            <span>{{ scope.row.status === 1 ? '在线' : '离线' }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" prop="" width="220">
                    <template #default="scope">
                        <el-button type="primary" text @click="handleDetail(scope.row)" icon="View"
                            style="margin: 0px; padding: 0 5px 0 5px">查看</el-button>
                        <el-button type="primary" text @click="handleEdit(scope.row)" icon="Edit"
                            style="margin: 0px; padding: 0 5px 0 5px">编辑</el-button>
                        <el-button type="danger" text @click="handleDelete(scope.row)" icon="Delete"
                            style="margin: 0px; padding: 0 5px 0 5px">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
                @pagination="getList" />
        </div>
        <el-dialog :title="title" v-model="detailShow" width="700px" @close="dataShow = true">
            <div>
                <el-form :model="form" label-width="100px" ref="formRef" :rules="rules">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="视频平台名称" prop="name">
                                <el-input v-model="form.name" placeholder="视频平台名称" clearable
                                    :readonly="isReadonlyMode" maxlength="25"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="国标编码" prop="gbCode">
                                <el-input v-model="form.gbCode" placeholder="国标编码" clearable
                                    :readonly="isReadonlyMode" maxlength="18"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="域" prop="domain">
                                <el-input v-model="form.domain" placeholder="域" clearable
                                    :readonly="isReadonlyMode" maxlength="18"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="IP" prop="ip">
                                <el-input v-model="form.ip" placeholder="IP" clearable
                                    :readonly="isReadonlyMode" maxlength="20"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="端口" prop="port">
                                <el-input v-model="form.port" placeholder="端口" clearable
                                    :readonly="isReadonlyMode" maxlength="8"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="用户名" prop="userName">
                                <el-input v-model="form.userName" placeholder="用户名" clearable
                                    :readonly="isReadonlyMode" maxlength="20"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="密码" prop="password">
                                <el-input v-model="form.password" placeholder="密码" clearable
                                    :readonly="isReadonlyMode" maxlength="20"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="form.remark" placeholder="备注" type="textarea" clearable
                            :readonly="isReadonlyMode" maxlength="100"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <el-button @click="submitForm" type="primary">提交</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
.status-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.online {
    background-color: #52c41a;
    box-shadow: 0 0 5px #52c41a;
    animation: pulse 1.5s infinite;
}

.offline {
    background-color: #f5222d;
    box-shadow: 0 0 5px #f5222d;
}

@keyframes pulse {
    0% {
        transform: scale(0.9);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(0.9);
        opacity: 0.7;
    }
}
</style>

<script>
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance, nextTick, ref } from "vue";
import { getVideoPlatformList, addVideoPlatform, updateVideoPlatform, deleteVideoPlatform } from "@/api/warning";
import { ElMessage } from "element-plus";

export default defineComponent({
    setup() {
        const { proxy } = getCurrentInstance();
        const showSearch = ref(true);
        const state = reactive({
            queryForm: {
                gbCode: '',
                name: '',
                pageNum: 1,
                pageSize: 20,
            },
            total: 0,
            tableList: [],
            loading: false,
            detailShow: false,
            title: '',
            isReadonlyMode: false,
            form: {
                id: null,
                gbCode: '',
                name: '',
                domain: '',
                ip: '',
                port: '',
                userName: '',
                password: '',
                remark: '',
                tenantId: null,
            },
            rules: {
                name: [
                    { required: true, message: "视频平台名称不能为空", trigger: "blur" },
                    { max: 25, message: "长度不能超过 25 个字符", trigger: "blur" }
                ],
                gbCode: [
                    { required: true, message: "国标编码不能为空", trigger: "blur" },
                    { min: 18, max: 18, message: "长度必须为 18 位字符", trigger: "blur" }
                ],
                domain: [
                    { required: true, message: "域不能为空", trigger: "blur" },
                    { min: 18, max: 18, message: "长度必须为 18 位字符", trigger: "blur" }
                ],
                ip: [
                    { required: true, message: "IP不能为空", trigger: "blur" },
                    { min: 20, max: 20, message: "长度必须为 20 位字符", trigger: "blur" }
                ],
                port: [
                    { required: true, message: "端口不能为空", trigger: "blur" },
                    {
                        validator: (rule, value, callback) => {
                            const portStr = String(value); //必须将number类型转换为string类型，否则无法校验
                            if (!portStr) {
                                return callback(new Error("端口不能为空"));
                            }
                            if (portStr.length !== 8) {
                                return callback(new Error("长度必须为 8 位字符"));
                            }
                            callback();
                        },
                        trigger: "blur"
                    }
                ],
                userName: [
                    { required: true, message: "用户名不能为空", trigger: "blur" },
                    { max: 20, message: "长度不能超过 20 个字符", trigger: "blur" }
                ],
                password: [
                    { required: true, message: "密码不能为空", trigger: "blur" },
                    { max: 20, message: "长度不能超过 20 个字符", trigger: "blur" }
                ],
                remark: [
                    { max: 100, message: "长度不能超过 100 个字符", trigger: "blur" }
                ]
            }
        });

        // 查询列表
        const getList = () => {
            state.loading = true;
            getVideoPlatformList({
                gbCode: state.queryForm.gbCode,
                name: state.queryForm.name,
                pageNum: state.queryForm.pageNum,
                pageSize: state.queryForm.pageSize
            }).then(res => {
                state.loading = false;
                if (res.code === 200) {
                    state.tableList = res.rows || [];
                    state.total = res.total || 0;
                } else {
                    ElMessage.error(res.msg || '获取数据失败');
                }
            }).catch(() => {
                state.loading = false;
                ElMessage.error('网络异常，获取数据失败');
            });
        };

        onMounted(() => {
            getList();
        });
        const reset = () => {
            state.queryForm = {
                pageNum: 1,
                pageSize: 20,
                gbCode: '',
                name: '',
            }
            getList()
        }
        const handleDetail = (row) => {
            state.title = row.name;
            state.isReadonlyMode = true;
            state.detailShow = true;
            Object.assign(state.form, row);
            if (proxy.$refs.formRef) {
                nextTick(() => {
                    proxy.$refs.formRef.clearValidate();
                });
            }
        };
        const handleEdit = (row) => {
            state.isReadonlyMode = false;
            state.title = '编辑 - ' + row.name;
            state.detailShow = true;
            Object.assign(state.form, row);
            if (proxy.$refs.formRef) {
                nextTick(() => {
                    proxy.$refs.formRef.clearValidate();
                });
            }
        };
        const handleAdd = () => {
            state.isReadonlyMode = false;
            state.title = '新增';
            state.detailShow = true;
            state.form = {
                id: null,
                gbCode: '',
                name: '',
                domain: '',
                ip: '',
                port: '',
                userName: '',
                password: '',
                remark: '',
                tenantId: null,
            };
            if (proxy.$refs.formRef) {
                nextTick(() => {
                    proxy.$refs.formRef.clearValidate();
                });
            }
        };
        const handleDelete = (row) => {
            proxy.$modal.confirm('是否确认删除名称为\"' + row.name + '\"的数据项？').then(function() {
                return deleteVideoPlatform(row.id);
            }).then(() => {
                getList();
                proxy.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        };
        const submitForm = () => {
            proxy.$refs["formRef"].validate(valid => {
                console.log(state.form)
                debugger
                if (valid) {
                    if (state.form.id != null) {
                        updateVideoPlatform(state.form).then(res => {
                            if (res.code === 200) {
                                proxy.$modal.msgSuccess("修改成功");
                                state.detailShow = false;
                                getList();
                            } else {
                                proxy.$modal.msgError(res.msg || "修改失败");
                            }
                        }).catch(() => {
                            proxy.$modal.msgError("网络异常，修改失败");
                        });
                    } else {
                        addVideoPlatform(state.form).then(res => {
                            if (res.code === 200) {
                                proxy.$modal.msgSuccess("新增成功");
                                state.detailShow = false;
                                getList();
                            } else {
                                proxy.$modal.msgError(res.msg || "新增失败");
                            }
                        }).catch(() => {
                            proxy.$modal.msgError("网络异常，新增失败");
                        });
                    }
                } else {
                    proxy.$modal.msgError("表单校验失败，请检查输入项！");
                    return false;
                }
            });
        };
        return {
            ...toRefs(state),
            getList,
            reset,
            handleDetail,
            handleEdit,
            showSearch,
            handleAdd,
            handleDelete,
            submitForm
        };
    },
});
</script>