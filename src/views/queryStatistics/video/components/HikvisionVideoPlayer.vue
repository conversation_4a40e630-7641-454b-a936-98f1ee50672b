<template>
  <div
    class="hikvision-video-player"
    ref="videoPlayerContainer"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @mousemove="handleMouseMove"
  >
    <!-- 播放器容器 -->
    <div :id="playerId" class="player-container" ref="playerContainer"></div>

    <!-- 加载中效果 -->
    <div v-if="isLoading" class="video-loading-overlay">
      <div class="loading-container">
        <!-- 主要加载动画 -->
        <div class="loading-spinner-container">
          <div class="spinner-ring"></div>
          <div class="spinner-ring-inner"></div>
          <div class="spinner-pulse"></div>
        </div>
        <!-- 加载文字 -->
        <div class="loading-text-container">
          <div class="loading-text">正在开始实时预览，请稍后</div>
          <div class="loading-dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="errorState" class="video-error-overlay">
      <div class="error-content">
        <i class="el-icon-warning"></i>
        <div class="error-message">播放失败：{{ errorMessage }}</div>
        <div class="error-actions">
          <el-button type="info" size="small" @click="showErrorDetails">
            详情
          </el-button>
          <el-button type="default" size="small" @click="closePlayer">
            关闭窗口
          </el-button>
        </div>
      </div>
    </div>

    <!-- 错误详情弹窗 -->
    <el-dialog
      v-model="errorDetailsVisible"
      title="错误详情"
      width="500px"
      :modal="true"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      append-to-body
    >
      <div class="error-details-content">
        <div class="error-info-section">
          <h4>错误信息</h4>
          <div class="error-info-item" v-if="errorDetails.code">
            <span class="label">错误代码：</span>
            <span class="value error-code">{{ errorDetails.code }}</span>
          </div>
          <div class="error-info-item" v-if="errorDetails.timestamp">
            <span class="label">发生时间：</span>
            <span class="value">{{
              formatTimestamp(errorDetails.timestamp)
            }}</span>
          </div>
        </div>

        <div class="error-info-section" v-if="videoInfo">
          <h4>视频信息</h4>
          <div class="error-info-item" v-if="videoInfo.name">
            <span class="label">摄像头名称：</span>
            <span class="value">{{ videoInfo.name }}</span>
          </div>
          <div class="error-info-item" v-if="videoInfo.id">
            <span class="label">设备ID：</span>
            <span class="value">{{ videoInfo.id }}</span>
          </div>
          <div class="error-info-item" v-if="videoInfo.videoUrl">
            <span class="label">视频地址：</span>
            <span class="value url-text">{{ videoInfo.videoUrl }}</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="copyErrorInfo">复制错误信息</el-button>
          <el-button type="primary" @click="errorDetailsVisible = false"
            >关 闭</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 控制栏 -->
    <div
      v-if="showControls && !errorState"
      class="video-controls"
      :class="{ 'controls-visible': isHovering }"
    >
      <div class="controls-center">
        <span class="video-title">{{
          videoInfo.name || "海康威视视频流"
        }}</span>
      </div>

      <div class="controls-right">
        <el-button
          circle
          size="small"
          @click="toggleMute"
          :title="isMuted ? '取消静音' : '静音'"
          class="control-button mute-button"
        >
          <img
            :src="isMuted ? voiceMuteIcon : voiceIcon"
            :alt="isMuted ? '静音' : '有声'"
            class="control-icon"
          />
        </el-button>
        <el-button
          icon="Camera"
          circle
          size="small"
          @click="captureScreenshot"
          title="截图"
          class="control-button"
        ></el-button>
        <el-button
          icon="FullScreen"
          circle
          size="small"
          @click="toggleFullscreen"
          title="全屏"
          class="control-button"
        ></el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { HikvisionPlayer } from "@/utils/hikvisionPlayer.js";
import voiceIcon from "@/assets/icon/voice.svg";
import voiceMuteIcon from "@/assets/icon/voice-mute.svg";

defineOptions({
  name: "HikvisionVideoPlayer",
});

const emit = defineEmits(["player-closed"]);

const props = defineProps({
  videoInfo: {
    type: Object,
    default: () => ({}),
  },
  showControls: {
    type: Boolean,
    default: true,
  },
  autoplay: {
    type: Boolean,
    default: true,
  },
  muted: {
    type: Boolean,
    default: true, // 默认静音
  },
});

// 响应式数据
const videoPlayerContainer = ref(null);
const playerContainer = ref(null);
const playerId = computed(
  () => `hikvision-player-${props.videoInfo.id || "default"}`
);
const hikvisionPlayer = ref(null);
const errorState = ref(false);
const errorMessage = ref("视频加载失败，请稍后重试");
const errorDetails = ref({
  code: null,
  timestamp: null,
  originalError: null,
});
const errorDetailsVisible = ref(false);
const isLoading = ref(false);
const isPlaying = ref(false);
const isMuted = ref(props.muted);
const autoRetryCount = ref(0);
const MAX_AUTO_RETRIES = 3;
const isHovering = ref(false);
const isRetrying = ref(false); // 防止重复重试
const isDestroyed = ref(false); // 组件是否已销毁
const initializationId = ref(0); // 初始化ID，用于防止过期的初始化
let hideControlsTimer = null;
let retryTimer = null;

// 监听视频URL变化
watch(
  () => props.videoInfo.videoUrl,
  (newUrl, oldUrl) => {
    // 只有URL真正变化时才重新初始化
    if (newUrl !== oldUrl) {
      nextTick(() => {
        if (newUrl) {
          console.log("视频URL变化，重新初始化播放器:", oldUrl, "->", newUrl);
          autoRetryCount.value = 0; // 新视频重置重试计数
          isRetrying.value = false; // 重置重试状态

          // 清理重试定时器
          if (retryTimer) {
            clearTimeout(retryTimer);
            retryTimer = null;
          }

          destroyPlayer();
          errorState.value = false;
          errorMessage.value = "视频加载失败，请稍后重试";

          // 延迟初始化，确保销毁完成
          setTimeout(() => {
            if (!isDestroyed.value && props.videoInfo.videoUrl === newUrl) {
              initPlayer();
            }
          }, 200);
        }
      });
    }
  },
  { immediate: false }
);

// 初始化播放器
const initPlayer = async () => {
  if (!props.videoInfo.videoUrl) {
    showError("视频地址无效");
    return;
  }

  // 检查组件是否已销毁
  if (isDestroyed.value) {
    console.log("组件已销毁，停止初始化");
    return;
  }

  // 防止重复初始化
  if (isRetrying.value && hikvisionPlayer.value) {
    console.log("播放器正在重试中，跳过重复初始化");
    return;
  }

  // 生成新的初始化ID
  const currentInitId = ++initializationId.value;
  console.log(
    `开始初始化播放器 (ID: ${currentInitId}):`,
    props.videoInfo.videoUrl
  );

  try {
    // 设置加载状态
    isLoading.value = true;
    errorState.value = false;

    // 创建海康威视播放器实例
    hikvisionPlayer.value = new HikvisionPlayer({
      containerId: playerId.value,
      videoUrl: props.videoInfo.videoUrl,
      config: {
        autoplay: props.autoplay,
        muted: props.muted,
        controls: false, // 使用自定义控制栏
        width: "100%",
        height: "100%",
      },
      onError: (message, errorCode) => {
        // 检查初始化ID是否还有效
        if (currentInitId !== initializationId.value || isDestroyed.value) {
          console.log(`忽略过期的错误回调 (ID: ${currentInitId}):`, message);
          return;
        }
        // 延迟处理错误，避免在初始化过程中立即触发重试
        setTimeout(() => {
          if (currentInitId === initializationId.value && !isDestroyed.value) {
            handlePlayerError(message, { code: errorCode });
          }
        }, 100);
      },
      onSuccess: async () => {
        // 检查初始化ID是否还有效
        if (currentInitId !== initializationId.value || isDestroyed.value) {
          console.log(`忽略过期的成功回调 (ID: ${currentInitId})`);
          return;
        }
        console.log(`海康威视播放器播放成功 (ID: ${currentInitId})`);
        isLoading.value = false;
        isPlaying.value = true;
        errorState.value = false;
        autoRetryCount.value = 0; // 成功后重置重试计数

        // 确保静音状态正确
        if (isMuted.value && hikvisionPlayer.value) {
          try {
            await hikvisionPlayer.value.closeSound();
            console.log("已确保播放器处于静音状态");
          } catch (error) {
            console.warn("设置静音状态失败:", error);
          }
        }
      },
      onLoading: (loading) => {
        // 检查初始化ID是否还有效
        if (currentInitId !== initializationId.value || isDestroyed.value) {
          return;
        }
        isLoading.value = loading;
      },
    });

    // 再次检查是否还有效
    if (currentInitId !== initializationId.value || isDestroyed.value) {
      console.log(`初始化已过期，停止执行 (ID: ${currentInitId})`);
      return;
    }

    // 初始化播放器
    await hikvisionPlayer.value.init();
  } catch (error) {
    // 检查初始化ID是否还有效
    if (currentInitId !== initializationId.value || isDestroyed.value) {
      console.log(
        `忽略过期的初始化错误 (ID: ${currentInitId}):`,
        error.message
      );
      return;
    }
    console.error(`海康威视播放器初始化失败 (ID: ${currentInitId}):`, error);
    // 延迟处理错误，避免立即触发重试
    setTimeout(() => {
      if (currentInitId === initializationId.value && !isDestroyed.value) {
        handlePlayerError(error.message || "播放器初始化失败", {
          originalError: error,
        });
      }
    }, 100);
  }
};

// 处理播放器错误
const handlePlayerError = (message, details = {}) => {
  // 如果组件已销毁，不处理错误
  if (isDestroyed.value) {
    console.log("组件已销毁，忽略错误:", message);
    return;
  }

  // 如果正在重试中，避免重复处理
  if (isRetrying.value) {
    console.log("正在重试中，忽略新的错误:", message);
    return;
  }

  // 如果组件已经卸载，不处理错误
  if (!playerContainer.value) {
    console.log("组件已卸载，忽略错误:", message);
    return;
  }

  // 直接显示错误，不自动重试
  showError(message, details);
};

// 显示错误信息
const showError = (message, details = {}) => {
  errorState.value = true;
  isLoading.value = false;
  isPlaying.value = false;
  isRetrying.value = false;
  errorMessage.value = message || "视频加载失败，请稍后重试";

  // 更新错误详情
  errorDetails.value = {
    code: details.code || null,
    timestamp: Date.now(),
    originalError: details.originalError || message,
  };

  console.error(errorMessage.value, details);
};

// 关闭播放器
const closePlayer = () => {
  console.log("用户手动关闭播放器");

  // 清理所有状态和定时器
  destroyPlayer();

  // 重置错误状态
  errorState.value = false;
  errorMessage.value = "视频加载失败，请稍后重试";

  // 通知父组件播放器已关闭
  emit("player-closed");
};

// 鼠标悬停控制
const handleMouseEnter = () => {
  isHovering.value = true;
  if (hideControlsTimer) {
    clearTimeout(hideControlsTimer);
    hideControlsTimer = null;
  }
};

const handleMouseLeave = () => {
  isHovering.value = false;
};

const handleMouseMove = () => {
  if (!isHovering.value) {
    isHovering.value = true;
  }

  // 重置隐藏计时器
  if (hideControlsTimer) {
    clearTimeout(hideControlsTimer);
  }

  hideControlsTimer = setTimeout(() => {
    isHovering.value = false;
  }, 3000); // 3秒后隐藏控制栏
};

// 切换静音
const toggleMute = async () => {
  if (!hikvisionPlayer.value) return;

  isMuted.value = !isMuted.value;

  if (isMuted.value) {
    await hikvisionPlayer.value.closeSound();
  } else {
    await hikvisionPlayer.value.openSound();
  }
};

// 截图功能
const captureScreenshot = async () => {
  if (!hikvisionPlayer.value) return;

  try {
    await hikvisionPlayer.value.capture();
    ElMessage.success("截图成功");
  } catch (error) {
    console.error("截图失败:", error);
    ElMessage.error("截图失败，请稍后重试");
  }
};

// 切换全屏
const toggleFullscreen = async () => {
  if (!hikvisionPlayer.value) return;

  await hikvisionPlayer.value.fullscreen();
};

// 显示错误详情
const showErrorDetails = () => {
  errorDetailsVisible.value = true;
};

// 格式化时间戳
const formatTimestamp = (timestamp) => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

// 复制错误信息
const copyErrorInfo = async () => {
  try {
    const errorInfo = {
      错误描述: errorMessage.value,
      错误代码: errorDetails.value.code,
      发生时间: formatTimestamp(errorDetails.value.timestamp),
      摄像头名称: props.videoInfo.name,
      设备ID: props.videoInfo.id,
      视频地址: props.videoInfo.videoUrl,
    };

    const errorText = Object.entries(errorInfo)
      .filter(([key, value]) => value)
      .map(([key, value]) => `${key}: ${value}`)
      .join("\n");

    await navigator.clipboard.writeText(errorText);

    // 使用 Element Plus 的消息提示
    ElMessage.success("错误信息已复制到剪贴板");
  } catch (err) {
    console.error("复制失败:", err);
    ElMessage.error("复制失败，请手动选择文本复制");
  }
};

// 销毁播放器
const destroyPlayer = () => {
  console.log("销毁播放器");

  // 标记组件为已销毁状态
  isDestroyed.value = true;

  // 增加初始化ID，使所有进行中的初始化失效
  initializationId.value++;

  // 清理重试定时器
  if (retryTimer) {
    clearTimeout(retryTimer);
    retryTimer = null;
  }

  // 停止重试状态
  isRetrying.value = false;

  if (hikvisionPlayer.value) {
    try {
      hikvisionPlayer.value.destroy();
    } catch (e) {
      console.warn("海康威视播放器销毁时出现错误:", e);
    }
    hikvisionPlayer.value = null;
  }

  // 重置所有状态
  isPlaying.value = false;
  isLoading.value = false;
  errorState.value = false;

  // 重置销毁状态，允许重新初始化
  setTimeout(() => {
    isDestroyed.value = false;
  }, 100);
};

// 调整播放器窗口大小
const resizePlayer = async (width, height) => {
  if (hikvisionPlayer.value && hikvisionPlayer.value.resize) {
    try {
      console.log(`调整播放器窗口大小: ${width}x${height}`);
      await hikvisionPlayer.value.resize(width, height);
    } catch (error) {
      console.error("调整播放器窗口大小失败:", error);
    }
  }
};

// 自动调整播放器大小以适应容器
const autoResize = () => {
  if (playerContainer.value) {
    const rect = playerContainer.value.getBoundingClientRect();
    if (rect.width > 0 && rect.height > 0) {
      resizePlayer(rect.width, rect.height);
    }
  }
};

// 暴露方法给父组件
defineExpose({
  destroyPlayer,
  showError,
  toggleFullscreen,
  resizePlayer,
  autoResize,
});

onMounted(() => {
  if (props.videoInfo.videoUrl) {
    initPlayer();
  }

  // 监听容器大小变化
  if (playerContainer.value && window.ResizeObserver) {
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        if (width > 0 && height > 0) {
          // 延迟调用以避免频繁调整
          setTimeout(() => {
            resizePlayer(width, height);
          }, 100);
        }
      }
    });

    resizeObserver.observe(playerContainer.value);

    // 在组件卸载时清理观察器
    onUnmounted(() => {
      resizeObserver.disconnect();
    });
  }
});

onUnmounted(() => {
  destroyPlayer();
  // 清理所有定时器
  if (hideControlsTimer) {
    clearTimeout(hideControlsTimer);
    hideControlsTimer = null;
  }
  if (retryTimer) {
    clearTimeout(retryTimer);
    retryTimer = null;
  }
});
</script>

<style lang="scss" scoped>
.hikvision-video-player {
  width: 100%;
  height: 100%;
  position: relative;
  background: #000;
  overflow: hidden;
}

.player-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 加载效果样式 */
.video-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(5, 11, 20, 0.8);
  backdrop-filter: blur(3px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  animation: fadeIn 0.3s ease;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24px;
}

.loading-spinner-container {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid rgba(64, 158, 255, 0.1);
  border-top: 3px solid rgba(64, 158, 255, 0.8);
  border-radius: 50%;
  animation: spin 1.5s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
  box-shadow: 0 0 20px rgba(64, 158, 255, 0.2);
}

.spinner-ring-inner {
  position: absolute;
  width: 70%;
  height: 70%;
  border: 3px solid transparent;
  border-right: 3px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1.2s cubic-bezier(0.65, 0.05, 0.36, 1) infinite reverse;
}

.spinner-pulse {
  position: absolute;
  width: 40%;
  height: 40%;
  background-color: rgba(64, 158, 255, 0.2);
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.loading-text {
  color: white;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

.loading-dots {
  display: flex;
  gap: 6px;
  justify-content: center;
  margin-top: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  background-color: #fff;
  border-radius: 50%;
  opacity: 0.6;
}

.dot:nth-child(1) {
  animation: dotPulse 1.5s ease-in-out infinite;
}

.dot:nth-child(2) {
  animation: dotPulse 1.5s ease-in-out 0.5s infinite;
}

.dot:nth-child(3) {
  animation: dotPulse 1.5s ease-in-out 1s infinite;
}

/* 错误提示样式 */
.video-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.error-content {
  text-align: center;
  padding: 20px;
  color: white;
  border-radius: 4px;
  max-width: 80%;
}

.error-content i {
  font-size: 48px;
  color: #ff4d4f;
  margin-bottom: 10px;
}

.error-message {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 15px;
}

/* 控制栏样式 */
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 10px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 5;
  opacity: 0;
  visibility: hidden;
  transform: translateY(100%);
  transition: all 0.3s ease;
}

.video-controls.controls-visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.controls-left,
.controls-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.controls-center {
  flex: 1;
  text-align: center;
}

.video-title {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.control-button {
  background: rgba(0, 0, 0, 0.5) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;

  &:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
  }
}

.control-icon {
  width: 16px;
  height: 16px;
  filter: invert(1); /* 将图标变为白色 */
}

/* 动画效果 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes dotPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

/* 错误详情弹窗样式 */
.error-details-content {
  max-height: 500px;
  overflow-y: auto;
}

.error-info-section {
  margin-bottom: 20px;

  h4 {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 8px;
  }
}

.error-info-item {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;

  .label {
    min-width: 100px;
    color: #606266;
    font-weight: 500;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .value {
    color: #303133;
    word-break: break-all;
    flex: 1;

    &.error-code {
      font-family: "Courier New", monospace;
      background-color: #f5f7fa;
      padding: 2px 6px;
      border-radius: 3px;
      color: #e6a23c;
      font-weight: 600;
    }

    &.url-text {
      font-family: "Courier New", monospace;
      font-size: 12px;
      background-color: #f5f7fa;
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid #e4e7ed;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-controls {
    padding: 8px 10px;
  }

  .controls-left,
  .controls-right {
    gap: 6px;
  }

  .video-title {
    font-size: 12px;
  }

  .error-info-item {
    flex-direction: column;

    .label {
      min-width: auto;
      margin-bottom: 4px;
    }
  }

  .error-details-content {
    max-height: 400px;
  }
}
</style>
