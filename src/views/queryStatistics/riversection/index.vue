<template>
  <div class="app-container">
    <el-form
      :model="queryForm"
      ref="queryForm1"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="测站名称">
        <el-input v-model="queryForm.stnm" placeholder="测站名称"></el-input>
      </el-form-item>
      <el-form-item label="行政区">
        <el-tree-select
          v-model="queryForm.adcd"
          :data="adcdList"
          clearable
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
          value-key="id"
          placeholder="请选择行政区"
          check-strictly
        />
      </el-form-item>
      <el-form-item label="选择流域">
        <el-tree-select
          v-model="queryForm.basinId"
          :data="transformDataForTreeSelect(waterAreaList)"
          clearable
          check-strictly
          :render-after-expand="false"
          placeholder="请选择流域"
        />
      </el-form-item>
      <el-form-item label="选择河流">
        <el-tree-select
          v-model="queryForm.riverId"
          :data="transformDataForTreeSelects(riverList)"
          clearable
          check-strictly
          :render-after-expand="false"
          placeholder="请选择河流"
        />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search"
          >查询</el-button
        >
        <el-button @click="reset" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="content content-table">
      <div class="table-header">
        <!-- <el-button type="primary" @click="getTemplate">下载模板</el-button> -->
        <el-button @click="handleImport" type="info" plain icon="Upload"
          >导入</el-button
        >
      </div>
      <el-table v-loading="loading" :data="tableList" stripe="" height="100%">
        <el-table-column label="序号" width="60" type="index"></el-table-column>
        <el-table-column label="测站编码" prop="stcd"></el-table-column>
        <el-table-column label="测站名称" prop="stnm"> </el-table-column>
        <el-table-column label="流域" prop="basinName"></el-table-column>
        <el-table-column label="河流" prop="riverName"></el-table-column>
        <el-table-column label="区县" prop="adnm"></el-table-column>
        <el-table-column label="最近施测时间" prop="mstm"></el-table-column>
        <el-table-column label="操作" prop="stationAddr" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              icon="Edit"
              size="mini"
              link
              @click="editRow(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryForm.pageNum"
        v-model:limit="queryForm.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- <div class="export"
        style="display: flex; justify-content: space-between; align-items: center; padding-left: 10px; border-left: 4px solid #1890FF;height: 20px;">
        <span style="font-size: 18px;">综述</span>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </div> -->


    <el-dialog
      :title="title"
      v-model="detailShow"
      width="70vw"
      destroy-on-close
      :close-on-click-modal="false"
      class="section-dialog"
      @close="dataShow = true"
    >
      <div class="section-form-container">
        <div class="section-title-bar">
          <h3 class="section-subtitle">基础信息</h3>
        </div>
        <el-form
          ref="editFormRef"
          :model="editForm"
          label-width="90px"
          :rules="editRules"
          class="base-info-form"
        >
          <div class="form-row">
            <el-form-item label="测站名称" prop="stnm">
              <el-input v-model="editForm.stnm" disabled />
            </el-form-item>
            <el-form-item label="测站编码" prop="stcd">
              <el-input v-model="editForm.stcd" disabled />
            </el-form-item>
            <el-form-item label="施测时间" prop="mstm">
              <el-date-picker
                v-model="editForm.mstm"
                type="datetime"
                placeholder="请选择施测时间"
              />
            </el-form-item>
            <el-form-item label="起测岸别" prop="bgbk">
              <el-select v-model="editForm.bgbk" placeholder="请选择">
                <el-option label="左岸" value="L" />
                <el-option label="右岸" value="R" />
              </el-select>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <div class="section-form-container">
        <div class="section-title-bar">
          <h3 class="section-subtitle">断面数据</h3>
          <div class="section-actions">
            <el-tooltip content="添加一行" placement="top">
              <el-button
                type="primary"
                circle
                size="small"
                @click="addTabelRow"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="删除末行" placement="top">
              <el-button
                type="danger"
                circle
                size="small"
                @click="removeTableRow"
              >
                <el-icon><Minus /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>

        <div class="section-data-content">
          <div class="data-table-container">
            <el-table
              :data="editTable"
              border
              stripe
              size="small"
              max-height="330px"
              highlight-current-row
              class="section-data-table"
            >
              <el-table-column
                type="index"
                label="垂线号"
                width="80"
                align="center"
              />
              <el-table-column label="起点距(m)" prop="di">
                <template #default="scope">
                  <el-input-number
                    v-model="editTable[scope.$index].di"
                    placeholder="请输入起点距"
                    :precision="3"
                    controls-position="right"
                    size="small"
                  />
                </template>
              </el-table-column>
              <el-table-column label="河底高程(m)" prop="zb">
                <template #default="scope">
                  <el-input-number
                    v-model="editTable[scope.$index].zb"
                    placeholder="请输入河底高程"
                    controls-position="right"
                    size="small"
                    :precision="3"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center">
                <template #default="scope">
                  <el-button
                    type="danger"
                    size="small"
                    circle
                    @click="removeIndexRow(scope.$index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="chart-container">
            <div id="charts"></div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button type="primary" @click="submitSection">保 存</el-button>
        <el-button @click="detailShow = false" plain>取 消</el-button>
      </template>
    </el-dialog>
    <el-dialog title="断面数据导入" v-model="open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="headers"
        :action="url"
        :disabled="isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="getTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <el-button type="primary" @click="submitFileForm">保 存</el-button>
        <el-button @click="open = false">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  toRefs,
  shallowRef,
  onMounted,
  onUnmounted,
  getCurrentInstance,
} from "vue";
import { getAdcdTree, selectStlyList, selectRvList } from "@/api/watershed/ads";
import {
  riverSectionList,
  riverSectionSave,
  riverSectionDetail,
} from "@/api/watershed/query/index";
import * as echarts from "echarts";
import moment from "moment";
import { getToken } from "@/utils/auth";
export default defineComponent({
  setup() {
    const state = reactive({
      waterAreaList: [],
      open: false,
      isUploading: false,

      // 设置上传的请求头部
      headers: { Authorization: "Bearer " + getToken() },
      // 上传的地址
      url: import.meta.env.VITE_APP_BASE_API + "/hydro//strvsectb/import",
      queryForm: {
        pageNum: 1,
        pageSize: 20,
        date: [
          moment().subtract(1, "days").format("YYYY-MM-DD 08:00:00").valueOf(),
          moment().valueOf(),
        ],
        stnm: "",
        adcd: "",
        riverId: "",
        basinId: "",
      },
      option: {
        grid: {
          left: "50px", //距左边距 留够name的宽度
          right: "70px",
          bottom: "60px",
          top: "50px",
          // containLabel: true
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: { color: "#555" },
            label: {
              backgroundColor: "#555",
            },
          },
          backgroundColor: "rgba(255, 255, 255, 0.9)",
          borderColor: "#ccc",
          borderWidth: 1,
          textStyle: {
            color: "#333",
          },
          formatter: function (params) {
            const point = params[0];
            return `<div style="padding: 3px 6px;">
                            <div style="margin-bottom: 4px;font-weight: bold;color: #666;">断面数据</div>
                            <div style="display: flex; justify-content: space-between;">
                                <span>起点距:</span>
                                <span style="font-weight: bold;margin-left: 10px;">${point.name} m</span>
                            </div>
                            <div style="display: flex; justify-content: space-between;">
                                <span>河底高程:</span>
                                <span style="font-weight: bold;margin-left: 10px;">${point.value} m</span>
                            </div>
                        </div>`;
          },
        },
        xAxis: {
          type: "category",
          name: "起点距(m)",
          data: [],
          boundaryGap: false,
          axisPointer: {
            type: "shadow",
          },
          axisLabel: {
            // interval: "0",
            color: "#666",
            fontSize: 11,
          },
          axisLine: {
            lineStyle: {
              color: "#ccc",
            },
          },
          axisTick: {
            show: false,
          },
          nameTextStyle: {
            fontWeight: "bold",
            color: "#333",
          },
        },
        dataZoom: [
          {
            type: "slider", //有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true, //是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0, //数据窗口范围的起始百分比0-100
            end: 100, //数据窗口范围的结束百分比0-100
            xAxisIndex: [0], // 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: 10, //距离底部的距离
            height: 20,
            borderColor: "#ccc",
            fillerColor: "rgba(64, 158, 255, 0.15)",
            handleStyle: {
              color: "#409eff",
            },
            textStyle: {
              color: "#666",
            },
          },
          {
            type: "inside",
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true,
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "河底高程(m)",
            min: 0,
            scale: true,
            nameTextStyle: {
              fontWeight: "bold",
              color: "#333",
              padding: [0, 30, 0, 0],
            },
            axisLabel: {
              color: "#666",
              fontSize: 11,
              formatter: function (value) {
                return value.toFixed(1);
              },
            },
            splitLine: {
              lineStyle: {
                color: "#eee",
                type: "dashed",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
              },
            },
          },
        ],
        series: [
          {
            name: "河底高程",
            type: "line",
            symbol: "circle",
            symbolSize: 8,
            color: "#D2B48C",
            lineStyle: {
              width: 3,
            },
            emphasis: {
              itemStyle: {
                color: "#D2B48C",
                borderColor: "#fff",
                borderWidth: 2,
                shadowColor: "rgba(0, 0, 0, 0.3)",
                shadowBlur: 10,
              },
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(210, 180, 140, 0.7)", // 渐变起始颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(210, 180, 140, 0.1)", // 渐变结束颜色
                  },
                ],
              },
              shadowBlur: 10,
              shadowColor: "rgba(210, 180, 140, 0.5)",
              opacity: 0.8,
            },
            data: [],
          },
        ],
      },
      total: 0,
      riverList: [],
      tableList: [],
      editTable: [],
      loading: false,
      adcdList: [],
      dialogList: [],
      detailShow: false,
      dataShow: true,
      title: "",
      time: "",
      stcd: "",
      exportData: {},
      editForm: {
        stcd: "",
        stnm: "",
        mstm: "",
        bgbk: "",
      },
      editRules: {
        mstm: [
          { required: true, message: "请选择施测时间", trigger: "change" },
        ],
        bgbk: [
          { required: true, message: "请选择起测岸别", trigger: "change" },
        ],
      },
    });
    const { proxy } = getCurrentInstance();

    const handleImport = () => {
      state.open = true;
      proxy.$nextTick(() => {
        proxy.$refs.uploadRef.clearFiles();
      });
    };
    const editRow = async (row) => {
      state.title = "编辑河道大断面 - " + row.stnm;
      state.editForm = [];
      state.editTable = [];
      state.editForm.stcd = row.stcd;
      state.editForm.stnm = row.stnm;
      let res = await riverSectionDetail(row.stcd);
      if (res.data.length > 0) {
        state.editForm.mstm = moment(res.data[0].mstm).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        state.editForm.bgbk = res.data[0].bgbk;

        state.editTable = res.data.map((item) => {
          return {
            di: item.di,
            zb: item.zb,
          };
        });
      }
      state.detailShow = true;

      proxy.$nextTick(() => {
        initCharts();
      });
    };
    const getAdcdList = () => {
      getAdcdTree({
        adcd: "",
      }).then((res) => {
        state.adcdList = res.data[0].children;
      });
    };
    const myEchart = shallowRef();
    const initCharts = () => {
      state.option.xAxis.data = [];
      state.option.series[0].data = [];
      let max = state.editTable
        ? Math.max(...state.editTable.map((item) => item.zb))
        : 0;
      state.option.yAxis[0].max = parseFloat((max * 1.2).toFixed(1));
      state.option.xAxis.data = state.editTable.map((item) => item.di);
      state.option.series[0].data = state.editTable.map((item) => item.zb);
      echarts.init(document.getElementById("charts")).dispose();
      myEchart.value = echarts.init(document.getElementById("charts"));
      myEchart.value.setOption(state.option);

      // 添加窗口大小变化时重绘图表的功能
      window.addEventListener("resize", handleResize);
    };

    // 图表重绘函数
    const handleResize = () => {
      myEchart.value && myEchart.value.resize();
    };

    const getTemplate = () => {
      proxy.download(
        "file/download",
        {
          fileName: "65808457-4e5b-4b5a-b964-c34dc2e03b85_河道大断面模板.xlsx",
          bucketName: "excel-template",
        },
        `河道大断面数据模板.xlsx`
      );
    };
    const getList = () => {
      state.loading = true;
      state.time = state.queryForm.date;
      riverSectionList({
        pageNum: state.queryForm.pageNum,
        pageSize: state.queryForm.pageSize,
        adcd: state.queryForm.adcd,
        basicId: state.queryForm.basinId,
        riverId: state.queryForm.riverId,
        stnm: state.queryForm.stnm,
      }).then((res) => {
        state.tableList = res.data || [];
        state.total = res.total || 0;
        state.loading = false;
      });
    };

    const submitSection = () => {
      if (state.editTable.length === 0)
        return proxy.$modal.msgError("请至少保留一组数据！");
      proxy.$refs.editFormRef.validate((valid) => {
        if (valid) {
          riverSectionSave(
            state.editTable.map((item, index) => {
              return {
                vtno: index + 1,
                stcd: state.editForm.stcd,
                di: item.di,
                zb: item.zb,
                mstm: moment(state.editForm.mstm).format("YYYY-MM-DD HH:mm:ss"),
                bgbk: state.editForm.bgbk,
              };
            })
          ).then((res) => {
            if (res.code == 200) {
              proxy.$modal.msgSuccess("保存成功！");
              state.detailShow = false;
              getList();
            } else {
              proxy.$modal.msgError(res.msg);
            }
          });
        }
      });
    };
    const addTabelRow = () => {
      if (state.editTable.length === 0) {
        state.editTable.push({
          di: "",
          zb: "",
        });
        return false;
      }
      if (
        state.editTable[state.editTable.length - 1].di == "" ||
        state.editTable[state.editTable.length - 1].zb == ""
      ) {
        proxy.$modal.msgError("起点距和高程关系不完整！");
        return false;
      }
      if (state.editTable.length == 1) {
      } else if (
        state.editTable[state.editTable.length - 1] &&
        state.editTable[state.editTable.length - 2] &&
        Number(state.editTable[state.editTable.length - 1].di) <
          Number(state.editTable[state.editTable.length - 2].di)
      ) {
        proxy.$modal.msgError("起点距非递增！");
        return false;
      }

      state.editTable.push({
        di: "",
        zb: "",
      });
      initCharts();
    };
    const removeTableRow = () => {
      if (state.editTable.length > 1) {
        state.editTable.pop();
        initCharts();
      } else {
        proxy.$modal.msgError("至少保留一条数据！");
      }
    };
    const removeIndexRow = (index) => {
      if (state.editTable.length >= 2) {
        state.editTable.splice(index, 1);
        initCharts();
      } else {
        proxy.$modal.msgError("至少保留一条数据！");
      }
    };
    const handleClick = (tab) => {
      state.activeLable = tab.paneName;
      getChartsList();
    };
    const handleFileUploadProgress = (event, file, fileList) => {
      state.isUploading = true;
    };
    const handleFileSuccess = (response, file, fileList) => {
      state.open = false;
      state.isUploading = false;
      proxy.$refs["uploadRef"].handleRemove(file);
      proxy.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      getList();
    };
    const getAllWater = async () => {
      let res = await selectStlyList({ pageNum: 1, pageSize: 999 });
      state.waterAreaList = res.data || [];
    };
    const getAllRiver = async () => {
      let res = await selectRvList({ pageNum: 1, pageSize: 999 });
      state.riverList = res.data || [];
    };
    const transformDataForTreeSelect = (data) => {
      // 递归地转换数据以匹配 el-tree-select 的需求
      return data.map((item) => ({
        label: item.data.name, // 使用 'name' 属性作为标签
        value: item.data.basinId, // 使用 'basinId' 属性作为值
        children: item.children
          ? transformDataForTreeSelect(item.children)
          : [], // 递归转换子节点
      }));
    };
    const transformDataForTreeSelects = (data) => {
      // 递归地转换数据以匹配 el-tree-select 的需求
      return data.map((item) => ({
        label: item.data.rvName, // 使用 'name' 属性作为标签
        value: item.data.id, // 使用 'basinId' 属性作为值
        children: item.children
          ? transformDataForTreeSelects(item.children)
          : [], // 递归转换子节点
      }));
    };
    const reset = () => {
      state.queryForm = {
        pageNum: 1,
        pageSize: 20,
        date: [
          moment().subtract(1, "days").format("YYYY-MM-DD 08:00:00").valueOf(),
          moment().valueOf(),
        ],
        stcd: "",
        adcd: "",
        riverId: "",
        basinId: "",
      };
      getList();
    };
    const handleExport = () => {
      proxy.download(
        "/sl323/measuring/station/rainStationExport",
        state.exportData,
        `雨情数据列表${new Date().getTime()}.xlsx`
      );
    };

    function submitFileForm() {
      proxy.$refs["uploadRef"].submit();
    }

    const disabledFutureDates = (time) => {
      // 比较是否晚于当前时间
      return time.getTime() > moment().valueOf();
    };

    onMounted(() => {
      getAdcdList();
      getAllWater();
      getAllRiver();
      getList();
    });
    onUnmounted(() => {
      // 移除窗口resize事件监听器，防止内存泄漏
      window.removeEventListener("resize", handleResize);
      // 销毁图表实例
      if (myEchart.value) {
        myEchart.value.dispose();
        myEchart.value = null;
      }
    });

    return {
      ...toRefs(state),
      getList,
      reset,
      handleExport,
      disabledFutureDates,
      transformDataForTreeSelect,
      handleClick,
      transformDataForTreeSelects,
      editRow,
      addTabelRow,
      removeTableRow,
      removeIndexRow,
      getTemplate,
      submitSection,
      handleImport,
      handleFileSuccess,
      handleFileUploadProgress,
      submitFileForm,
    };
  },
});
</script>

<style scoped lang="scss">
.dobth {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}

:deep(.section-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    margin-right: 0;
  }

  .el-dialog__body {
    padding: 20px 24px;
    max-height: 75vh;
    overflow-y: auto;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 14px 20px;
  }
}

.section-form-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  padding: 16px 0 8px;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: #409eff;
  }
}

.base-info-form {
  padding: 0 16px;

  .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    :deep(.el-form-item) {
      margin-bottom: 16px;
      flex: 1;
      min-width: 280px;

      .el-form-item__label {
        font-weight: 500;
      }

      .el-input__wrapper,
      .el-select__wrapper {
        transition: all 0.3s;
      }

      .el-input.is-disabled .el-input__wrapper {
        background-color: #f8f9fa;
      }
    }
  }
}

.section-actions {
  display: flex;
  gap: 8px;
}

.section-data-content {
  display: flex;
  padding: 0 16px;
  gap: 24px;

  @media screen and (max-width: 1200px) {
    flex-direction: column;
  }
}

.data-table-container {
  flex: 1;

  .section-data-table {
    width: 100%;
  }
}

.chart-container {
  flex: 1.2;

  #charts {
    height: 350px;
  }
}
</style>
