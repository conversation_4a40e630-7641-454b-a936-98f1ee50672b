<template>
  <div class="app-container">
    <el-form
      :model="queryForm"
      ref="queryForm1"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="测站名称">
        <el-input
          v-model="queryForm.stnm"
          placeholder="测站名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="测站编码">
        <el-input
          v-model="queryForm.stcd"
          placeholder="测站编码"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="行政区">
        <el-tree-select
          v-model="queryForm.adcd"
          :data="adcdList"
          clearable
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
          value-key="id"
          placeholder="请选择行政区"
          check-strictly
        />
      </el-form-item>
      <el-form-item label="选择流域">
        <el-tree-select
          v-model="queryForm.basinId"
          :data="transformDataForTreeSelect(waterAreaList)"
          check-strictly
          :render-after-expand="false"
          placeholder="请选择流域"
          clearable
        />
      </el-form-item>
      <el-form-item label="选择时间">
        <el-date-picker
          v-model="queryForm.date"
          :shortcuts="shortcuts"
          :clearable="false"
          :default-value="defaultValue"
          type="datetimerange"
          :disabledDate="disabledFutureDates"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm"
          :disabled-minutes="
            () => {
              // 返回从1到59的数组，表示禁用的分钟
              return Array.from({ length: 59 }, (_, i) => i + 1);
            }
          "
          :disabled-seconds="
            () => {
              // 返回从1到59的数组，表示禁用的秒
              return Array.from({ length: 59 }, (_, i) => i + 1);
            }
          "
        />
      </el-form-item>
      <el-form-item label="雨量标准">
        <el-select v-model="queryForm.rainStandard" placeholder="请选择">
          <el-option label="0mm" :value="0"></el-option>
          <el-option label="10mm" :value="10"></el-option>
          <el-option label="25mm" :value="25"></el-option>
          <el-option label="50mm" :value="50"></el-option>
          <el-option label="100mm" :value="100"></el-option>
          <el-option label="200mm" :value="200"></el-option>
        </el-select>
      </el-form-item>
      <div class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search"
          >查询</el-button
        >
        <el-button @click="reset" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </div>
    </el-form>
    <!-- <div class="export"
      style="display: flex; justify-content: space-between; align-items: center; padding-left: 10px; border-left: 4px solid #1890FF;height: 20px;">
      <span style="font-size: 18px;">综述</span>
      <el-button type="primary" @click="handleExport">导出</el-button>
    </div> -->
    <div class="content content-table">
      <div class="summarize">
        <div class="list">
          <div
            class="item-card"
            v-for="(item, index) in amountList"
            :key="index"
            :class="{ 'total-count': index === 0 }"
          >
            <div class="item-value">
              <span>{{ item.value }}</span>
              <span class="unit">个</span>
            </div>
            <div class="item-name">{{ item.name }}</div>
          </div>
        </div>
        <div class="summary-box">
          <div class="summary-content">
            <i class="el-icon-info-filled"></i>
            <span
              >区域面雨量为 <b>{{ avgRainfall }}</b
              >mm，</span
            >
            <span
              >最大降雨量站点为 <b>{{ topRainStName }}</b>
              <b>{{ topRainValue }}</b
              >mm，</span
            >
            <span
              >其次 <b>{{ twoRainStName }}</b> <b>{{ twoRainValue }}</b
              >mm，</span
            >
            <span
              >再次 <b>{{ threeRainStName }}</b> <b>{{ threeRainValue }}</b
              >mm</span
            >
          </div>
        </div>
      </div>
      <el-table v-loading="loading" :data="tableList" stripe align="center">
        <el-table-column label="序号" width="60" type="index"></el-table-column>
        <el-table-column label="测站编码" prop="stcd"></el-table-column>
        <el-table-column label="测站名称" prop="stnm">
          <template #default="scope">
            <span
              style="
                color: #1890ff;
                text-decoration: underline;
                cursor: pointer;
              "
              @click="handleDetail(scope.row)"
              >{{ scope.row.stnm }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="流域" prop="basinName"></el-table-column>
        <!-- <el-table-column label="地市" prop="city"></el-table-column> -->
        <el-table-column label="县区" prop="county"></el-table-column>
        <el-table-column
          label="累计雨量(mm)"
          prop="totalRainValue"
        ></el-table-column>
        <el-table-column label="站址" prop="stationAddr"></el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryForm.pageNum"
        v-model:limit="queryForm.pageSize"
        @pagination="getList"
      />
    </div>
    <el-dialog
      :title="title"
      v-model="detailShow"
      width="700px"
      @close="handleClose"
    >
      <div>
        <el-tabs
          v-model="activeLable"
          type="card"
          class="demo-tabs"
          @tab-click="handleClick"
        >
          <el-tab-pane label="时段降雨" :name="0"></el-tab-pane>
          <el-tab-pane label="日降雨" :name="1"></el-tab-pane>
        </el-tabs>
        <div class="form" style="margin-bottom: 20px">
          <el-date-picker
            v-model="time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            format="YYYY-MM-DD HH:mm"
            date-format="YYYY/MM/DD ddd"
            time-format="hh:mm"
            end-placeholder="结束时间"
            :clearable="false"
            style="width: 320px; margin-right: 10px"
          />
          <el-button type="primary" @click="getChartsList" icon="Search"
            >查询</el-button
          >
          <el-button
            type="primary"
            plain
            @click="
              time = queryForm.date;
              getChartsList();
            "
            icon="Refresh"
            >重置</el-button
          >
          <el-button type="primary" plain @click="dataShow = !dataShow">{{
            dataShow ? "数据" : "图"
          }}</el-button>
          <!-- <el-button type="primary" plain>导出</el-button> -->
        </div>
        <div
          v-show="dataShow"
          class="rainfallChart"
          style="width: 660px; height: 300px"
          id="rainfallChart"
        ></div>
        <div v-show="!dataShow">
          <el-table :data="dialogList" height="300px" border>
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="tm"
              label="时间"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="rainValue"
              label="降雨量(mm)"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="totalRainValue"
              label="累计降雨量(mm)"
              align="center"
            ></el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  getCurrentInstance,
  nextTick,
} from "vue";
import { getAdcdTree, selectStlyList } from "@/api/watershed/ads";
import {
  measuringStationList,
  measuringStationInfo,
} from "@/api/watershed/query/index";
import * as echarts from "echarts";
import moment from "moment";
export default defineComponent({
  setup() {
    const state = reactive({
      waterAreaList: [],
      shortcuts: [
        {
          text: "今日8时起",
          value: () => {
            //所有时间都只保留小时，分秒都为0
            const end = moment().format("YYYY-MM-DD HH:00:00").valueOf();
            const start = moment().format("YYYY-MM-DD 08:00:00").valueOf();
            return [start, end];
          },
        },
        {
          text: "昨日8时起",
          value: () => {
            const end = moment().format("YYYY-MM-DD HH:00:00").valueOf();
            const start = moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD 08:00:00")
              .valueOf();
            return [start, end];
          },
        },
        {
          text: "最近24小时",
          value: () => {
            const end = moment().format("YYYY-MM-DD HH:00:00").valueOf();
            const start = moment()
              .subtract(24, "hours")
              .format("YYYY-MM-DD HH:00:00")
              .valueOf();
            return [start, end];
          },
        },
        {
          text: "最近48小时",
          value: () => {
            const end = moment().format("YYYY-MM-DD HH:00:00").valueOf();
            const start = moment()
              .subtract(48, "hours")
              .format("YYYY-MM-DD HH:00:00")
              .valueOf();
            return [start, end];
          },
        },
      ],
      queryForm: {
        pageNum: 1,
        pageSize: 20,
        stcd: "",
        date: [
          moment().subtract(1, "days").format("YYYY-MM-DD 08:00:00").valueOf(),
          moment().format("YYYY-MM-DD HH:00:00").valueOf(),
        ],
        adcd: "",
        stnm: "",
        basinId: "",
        rainStandard: 50,
      },
      total: 0,
      tableList: [],
      amountList: [
        { name: "雨量站点总数", value: 0, id: 1 },
        { name: "10.0mm以下的雨量站", value: 0, id: 2 },
        { name: "10~25mm的雨量站", value: 0, id: 3 },
        { name: "25~50mm的雨量站", value: 0, id: 4 },
        { name: "50~100mm的雨量站", value: 0, id: 5 },
        { name: "100-250mm的雨量站", value: 0, id: 6 },
        { name: "250mm以上的雨量站", value: 0, id: 7 },
      ],
      topRainStName: "",
      twoRainStName: "",
      threeRainStName: "",
      activeLable: 0,
      topRainValue: "",
      twoRainValue: "",
      threeRainValue: "",
      avgRainfall: "",
      loading: false,
      adcdList: [],
      dialogList: [],
      option: {
        grid: {
          left: "60px", //距左边距 留够name的宽度
          right: "60px",
          bottom: "50px",
          top: "30px",
          // containLabel: true
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: { color: "#555" },
            label: {
              backgroundColor: "#555",
            },
          },
        },
        xAxis: {
          type: "category",
          data: [],
          boundaryGap: true,
          axisPointer: {
            type: "shadow",
          },
          axisLabel: {
            // interval: "0",
            overflow: "truncate",
            // width: '100'
          },
          axisLine: {
            lineStyle: {},
          },
          axisTick: {
            show: false,
          },
        },

        dataZoom: [
          {
            type: "slider", //有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true, //是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0, //数据窗口范围的起始百分比0-100
            end: 100, //数据窗口范围的结束百分比0-100
            xAxisIndex: [0], // 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: 0, //距离底部的距离
          },
          {
            type: "inside",
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true,
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "降雨量(mm)",
            // scale: true,
            nameTextStyle: {},
            axisLabel: {},
            splitLine: {
              lineStyle: {
                // color: "#005b99"
              },
            },
            axisLine: {
              show: true,
              lineStyle: {},
            },
          },
          {
            type: "value",
            name: "累计降雨量(mm)",
            scale: true,
            splitLine: {
              show: false,
            },
            axisLine: {
              show: true,
            },
          },
        ],
        series: [
          {
            name: "降雨量",
            type: "bar",
            // smooth: true,
            symbol: "none",
            color: "#059DFE",
            data: [],
            // barWidth: 20
          },
          {
            name: "累计降雨量",
            type: "line",
            // smooth: true,
            symbol: "none",
            yAxisIndex: 1,
            color: "#fac858",
            data: [],
          },
        ],
      },
      detailShow: false,
      dataShow: true,
      title: "",
      time: "",
      stcd: "",
      exportData: {},
    });
    const { proxy } = getCurrentInstance();
    onMounted(() => {
      getAdcdList();
      getAllWater();
      getList();
    });
    const getAdcdList = () => {
      getAdcdTree({
        adcd: "",
      }).then((res) => {
        state.adcdList = res.data[0].children;
      });
    };
    const getList = () => {
      state.loading = true;
      state.time = state.queryForm.date;

      measuringStationList({
        startTime: moment(state.queryForm.date[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        endTime: moment(state.queryForm.date[1]).format("YYYY-MM-DD HH:mm:ss"),
        pageNum: state.queryForm.pageNum,
        pageSize: state.queryForm.pageSize,
        adcd: state.queryForm.adcd,
        stnm: state.queryForm.stnm,
        stcd: state.queryForm.stcd,
        rainStandard: state.queryForm.rainStandard,
        basinId: state.queryForm.basinId,
        rainLevel: [].join(","),
      }).then((res) => {
        state.amountList[0].value = res.data.totalCount;
        state.amountList[1].value = res.data.zeroToTenCount;
        state.amountList[2].value = res.data.tenToTwentyFiveCount;
        state.amountList[3].value = res.data.twentyFiveToFiftyCount;
        state.amountList[4].value = res.data.fiftyToOneHundredCount;
        state.amountList[5].value = res.data.hundredToTwoHundredFiftyCount;
        state.amountList[6].value = res.data.moreThanTwoHundredFiftyCount;
        state.loading = false;
        state.topRainStName = res.data.topRainStName;
        state.topRainValue = res.data.topRainValue;
        state.twoRainStName = res.data.twoRainStName;
        state.twoRainValue = res.data.twoRainValue;
        state.threeRainStName = res.data.threeRainStName;
        state.threeRainValue = res.data.threeRainValue;
        state.avgRainfall = res.data.avgRainfall;
        if (res.data.ipage) {
          state.tableList = res.data.ipage.records;
          state.total = res.data.ipage.total;
        } else {
          state.tableList = [];
          state.total = 0;
        }
      });
    };
    const handleClick = (tab) => {
      state.activeLable = tab.paneName;
      if (state.activeLable === 1) {
        // 当日降雨量标签被选中时，自动设置时间范围为最近30天
        state.time = [
          moment().subtract(30, "days").format("YYYY-MM-DD 08:00:00").valueOf(),
          moment().valueOf(),
        ];
      } else {
        state.time = [
          moment().subtract(1, "days").format("YYYY-MM-DD 08:00:00").valueOf(),
          moment().valueOf(),
        ];
      }
      getChartsList();
    };
    const getAllWater = async () => {
      let res = await selectStlyList({ pageNum: 1, pageSize: 999 });
      state.waterAreaList = res.data || [];
    };
    const transformDataForTreeSelect = (data) => {
      // 递归地转换数据以匹配 el-tree-select 的需求
      return data.map((item) => ({
        label: item.data.name, // 使用 'name' 属性作为标签
        value: item.data.basinId, // 使用 'basinId' 属性作为值
        children: item.children
          ? transformDataForTreeSelect(item.children)
          : [], // 递归转换子节点
      }));
    };
    const reset = () => {
      state.queryForm = {
        pageNum: 1,
        pageSize: 20,
        date: [
          moment().subtract(1, "days").format("YYYY-MM-DD 08:00:00").valueOf(),
          moment().valueOf(),
        ],
        adcd: "",
        stnm: "",
        stcd: "",
        basinId: "",
        rainStandard: 50,
      };
      getList();
    };
    const handleExport = () => {
      proxy.download(
        "/sl323/measuring/station/rainStationExport",
        state.exportData,
        `雨情数据列表${new Date().getTime()}.xlsx`
      );
    };
    const handleDetail = (row) => {
      state.title = "测站 - " + row.stnm;
      state.detailShow = true;
      state.stcd = row.stcd;
      nextTick(() => {
        getChartsList();
      });
    };

    const getChartsList = () => {
      measuringStationInfo({
        startTime: moment(state.time[0]).format("YYYY-MM-DD HH:mm:ss"),
        endTime: moment(state.time[1]).format("YYYY-MM-DD HH:mm:ss"),
        stcd: state.stcd,
        rainType: state.activeLable,
      }).then((res) => {
        state.option.xAxis.data = [];
        state.option.series[0].data = [];
        state.option.series[1].data = [];
        res.data.forEach((el) => {
          if (state.activeLable === 0) {
            state.option.xAxis.data.push(moment(el.tm).format("MM-DD HH:mm"));
            el.tm = moment(el.tm).format("MM-DD HH:mm");
          } else {
            state.option.xAxis.data.push(moment(el.tm).format("MM-DD "));
            el.tm = moment(el.tm).format("MM-DD ");
          }
          state.option.series[0].data.push(el.rainValue);
          state.option.series[1].data.push(el.totalRainValue);
        });
        state.dialogList = res.data;
        // 表格按时间倒序
        state.dialogList.sort(
          (a, b) => moment(b.tm).valueOf() - moment(a.tm).valueOf()
        );
        echarts.init(document.getElementById("rainfallChart")).dispose();
        let myEchart = echarts.init(document.getElementById("rainfallChart"));
        myEchart.setOption(state.option);
      });
    };
    const disabledFutureDates = (time) => {
      // 比较是否晚于当前时间
      return time.getTime() > moment().valueOf();
    };

    const handleClose = () => {
      state.detailShow = false;
      state.stcd = "";
      state.time = [
        moment().subtract(1, "days").format("YYYY-MM-DD 08:00:00").valueOf(),
        moment().valueOf(),
      ];
      state.option.xAxis.data = [];
      state.option.series[0].data = [];
      state.option.series[1].data = [];
    };
    return {
      ...toRefs(state),
      getList,
      reset,
      handleExport,
      handleDetail,
      getChartsList,
      disabledFutureDates,
      transformDataForTreeSelect,
      handleClick,
      handleClose,
    };
  },
});
</script>

<style scoped lang="scss">
.summarize {
  margin: 4px 0;
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 10px 0 rgba(79, 89, 104, 0.1);

  .list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 20px;

    .item-card {
      width: calc(100% / 7 - 10px);
      min-width: 120px;
      height: 90px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: #f8f9fb;
      border-radius: 6px;
      padding: 12px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-1px);
      }

      &.total-count {
        background: #f6f9fe;
        border: 1px solid #d9e8ff;
        position: relative;

        &::after {
          content: none;
        }

        .item-value {
          font-size: 26px;
          color: #1890ff;
          font-weight: 600;
        }

        .item-name {
          font-weight: 500;
          color: #333;
        }
      }

      .item-value {
        font-size: 24px;
        font-weight: 600;
        color: #1890ff;
        margin-bottom: 8px;

        .unit {
          font-size: 14px;
          color: #666;
          font-weight: normal;
          margin-left: 2px;
        }
      }

      .item-name {
        font-size: 14px;
        color: #606266;
        text-align: center;
      }
    }
  }

  .summary-box {
    width: 100%;
    padding: 10px 20px;
    background-color: #fff9f0;
    border-left: 2px solid #ff8503;
    border-radius: 4px;

    .summary-content {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      color: #606266;
      line-height: 24px;

      i {
        color: #ff8503;
        font-size: 16px;
        margin-right: 8px;
      }

      b {
        color: #ff8503;
        font-weight: 600;
        margin: 0 2px;
      }

      span {
        margin-right: 6px;
      }
    }
  }
}
</style>
