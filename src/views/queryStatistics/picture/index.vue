<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm1" label-width="60px" :inline="true" class="form-container">
      <el-form-item label="行政区">
        <el-tree-select v-model="queryForm.adcd" :data="adcdList" clearable
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="id" placeholder="请选择行政区"
          check-strictly />
      </el-form-item>

      <el-form-item label="水库名称" label-width="100px">
        <el-input v-model="queryForm.reservoirName" placeholder="测站名称"></el-input>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker v-model="queryForm.date" type="datetimerange" style="width: 310px" :clearable="false"
          :disabledDate="disabledFutureDates" range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm" />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search">查询</el-button>
        <el-button @click="reset" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="data-table-img" v-if="tableList.length > 0" v-loading="loading">
      <div class="data-image" v-for="(item, index) in tableList" :key="index">
        <div class="data-image-title">
          <div class="title1">{{ item.reservoirName }}<span class="adcd">[{{ item.address }}]</span></div>
          <div>{{ item.createTime }}</div>
        </div>
        <div class="data-image-content">
          <div class="data-ic-right">
            <div class="data-ic-img">
              <img :src="imgList[index]" class="data-img" />
            </div>
            <!-- <div class="data-ic-stlc">{{ item.address }}</div> -->
            <div class="data-ic-btn" @click="powerStationClick(item)">
              <div>详情</div>
              <div>
                <img src="@/assets/icon/detail.png" class="xq-img" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="data-table-img" v-if="tableList.length == 0" v-loading="loading">
      <div class="nodata"></div>
    </div>

    <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
      @pagination="getList" />
    <el-dialog :title="title" v-model="showView" width="70%" top="5vh" @close="close">
      <div style="display: flex;">

        <el-form :inline="true" label-width="85px" @keyup.enter="handleQuery()">
          <el-form-item label="测点：">
            <el-select v-model="mpcd" placeholder="请选择测点" @change="dmchange">
              <el-option v-for="(item, index) in dmarr" :key="index" :label="item.mpcd" :value="item.mpcd" />
            </el-select>
          </el-form-item>
          <el-form-item label="查询时间：" class="form-time">
            <el-date-picker v-model="time" type="datetimerange" :clearable="false" range-separator="-"
              start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm" />
          </el-form-item>
        </el-form>
        <!-- <el-date-picker v-model="time" type="datetimerange" :clearable="false" range-separator="-"
          start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm" /> -->
        <el-button type="primary" style="margin-left: 20px;" @click="handleQuery">查询</el-button>

      </div>
      <div class="realRain-table">
        <div class="table-img" v-loading="tableLoading">
          <div v-for="(item, index) in tableData" :key="index" class="singel-img">
            <div class="data-image" :class="{ selImg: selflag === index }" @click="selectImg(item, index)">
              <img :src="item.imgurl" class="data-img" />
            </div>
            <div class="data-time">{{ item.time }}</div>
          </div>
        </div>
        <div class="table-carousel">
          <div v-if="flag" class="table-carousel-img">
            <img class="carousel-image" :src="url1" />
            <div class="carousel-left">
              <img src="@/assets/images/left.png" class="carousel-left-but" @click="gotoUp()" />
            </div>
            <div class="carousel-right">
              <img src="@/assets/images/right.png" class="carousel-right-but" @click="gotoDown()" />
            </div>
          </div>
          <div v-else class="empt-disflow"></div>
        </div>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance } from "vue";
// import { reservoirMonitorPage } from "@/api/watershed/query/index"
import { getAdcdTree, } from "@/api/watershed/ads";
import Nopic from "@/assets/images/no-picture.png";
import moment from "moment";
export default defineComponent({
  setup() {
    const state = reactive({
      queryForm: {
        adcd: '',
        pageNum: 1,
        pageSize: 10,
        date: [moment().subtract(1, "day"), moment()]
      },
      total: 0,
      adcdOption: [],
      adcdList: [],
      tableList: [],
      time: [moment().subtract(1, "day"), moment()],
      tableLoading: false,
      tableData: [],
      showView: false,
      title: '',
      selflag: 0,
      flag: false,
      url1: "",
      reservoirCode: '',
      loading: false,
      imgList: [],
      mpcd: '',
      dmarr: [],
      idx: 0,
    })
    const { proxy } = getCurrentInstance();
    onMounted(() => {
      getList()
      getAdcdList()
    })
    const getAdcdList = () => {
      getAdcdTree({
        adcd: ''
      }).then((res) => {
        state.adcdList = res.data[0].children
      })
    }
    const getList = () => {
      state.loading = true
      // reservoirMonitorPage({
      //   pageNum: state.queryForm.pageNum,
      //   pageSize: state.queryForm.pageSize,
      //   reservoirName: state.queryForm.reservoirName,
      //   adminRegionCode: state.queryForm.adcd,
      //   startTime: moment(state.queryForm.date[0]).format("YYYY-MM-DD HH:mm:ss"),
      //   endTime: moment(state.queryForm.date[1]).format("YYYY-MM-DD HH:mm:ss")
      // }).then(res => {
      //   state.tableList = res.data.records
      //   state.total = res.data.total
      //   state.imgList = []
      //   state.tableList.forEach((el) => {
      //     let a
      //     if (el.stationMpcdDTOS && el.stationMpcdDTOS.length > 0) {
      //       if (el.stationMpcdDTOS[0].stationImageDTOS.length > 0) {
      //         a = el.stationMpcdDTOS[0].stationImageDTOS[0].imgurl
      //       } else {
      //         a = Nopic
      //       }
      //     } else {
      //       a = Nopic
      //     }
      //     state.imgList.push(a)
      //   })
      // }).finally(() => {
      //   state.loading = false
      // })
    }
    const reset = () => {
      state.queryForm = {
        adcd: '',
        pageNum: 1,
        pageSize: 20,
        date: [moment().subtract(1, "day"), moment()]
      }
      getList()
    }
    const close = () => {
      state.tableData = []
      state.showView = false
      state.title = ''
      state.selflag = 0
      state.flag = false
      state.url1 = ""
      state.reservoirCode = ''
    }

    const powerStationClick = (item) => {
      state.mpcd = ''
      if (item.stationMpcdDTOS && item.stationMpcdDTOS.length > 0) {
        if (item.stationMpcdDTOS[0].stationImageDTOS.length > 0) {
          state.dmarr = item.stationMpcdDTOS
          state.mpcd = item.stationMpcdDTOS[0].mpcd
          state.tableData = item.stationMpcdDTOS[0].stationImageDTOS;
          state.url1 = item.stationMpcdDTOS[0].stationImageDTOS[0].imgurl
          state.flag = true;
        }
      }
      state.time = state.queryForm.date
      state.reservoirCode = item.reservoirCode
      state.showView = true
      state.title = item.reservoirName

    }
    const handleQuery = () => {
      // reservoirMonitorPage({
      //   pageNum: 1,
      //   pageSize: state.queryForm.pageSize,
      //   reservoirName: state.title,
      //   startTime: moment(state.time[0]).format("YYYY-MM-DD HH:mm:ss"),
      //   endTime: moment(state.time[1]).format("YYYY-MM-DD HH:mm:ss")
      // }).then(res => {
      //   if (res.data.records[0].stationMpcdDTOS && res.data.records[0].stationMpcdDTOS.length > 0) {
      //     if (res.data.records[0].stationMpcdDTOS[state.idx].stationImageDTOS.length > 0) {
      //       state.tableData = res.data.records[0].stationMpcdDTOS[state.idx].stationImageDTOS;
      //       state.url1 = res.data.records[0].stationMpcdDTOS[state.idx].stationImageDTOS[0].imgurl
      //       state.flag = true;
      //       state.selflag = 0;
      //     }
      //   }
      // })
    }
    const dmchange = (v) => {
      let i = state.dmarr.findIndex(el => {
        return el.mpcd == v
      })
      state.idx = i ? i : 0
    }

    const selectImg = (item, index) => {
      state.url1 = "";
      state.url1 = item.imgurl;
      state.selflag = index;
    }
    //左箭头点击
    const gotoUp = () => {
      let len = state.tableData.findIndex((str) => {
        return str.imgurl == state.url1;
      });
      // console.log(len);
      // console.log(state.selflag);
      if (len == 0) {
        proxy.$modal.msgWarning('这已经是第一张图了！')
      } else {
        state.selflag = len - 1;
        state.url1 = state.tableData[len - 1].imgurl;
      }
    }
    //右箭头点击
    const gotoDown = () => {
      let len = state.tableData.findIndex((str) => {
        return str.imgurl == state.url1;
      });
      // console.log(len);
      if (len == state.tableData.length - 1) {
        proxy.$modal.msgWarning('这已经是最后一张图了！')
      } else {
        state.selflag = len + 1;
        state.url1 = state.tableData[len + 1].imgurl;
      }
    }
    const disabledFutureDates = (time) => {
      // 比较是否晚于当前时间
      return time.getTime() > moment().valueOf();
    }
    return {
      ...toRefs(state),
      getList,
      reset,
      close,
      powerStationClick,
      selectImg,
      gotoUp,
      gotoDown,
      handleQuery,
      disabledFutureDates,
      dmchange
    };
  },
});
</script>

<style scoped lang="scss">
.data-table-img {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
  height: calc(100vh - 240px);

  .data-image {
    width: calc(25% - 10px);
    height: 46%;
    background: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    margin-bottom: 20px;
    margin-right: 10px;
    padding: 10px;

    .data-image-title {
      height: 30px;
      line-height: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title1 {
        font-size: 15px;
        font-family: "PingFang SC-Medium";
        font-weight: 500;
        color: #303133;

        .adcd {
          font-size: 13px;
          color: #606266;
        }
      }

      >div:last-child {
        font-size: 14px;
        font-family: "PingFang SC-Regular";
        font-weight: 400;
        color: #606266;
      }
    }

    .data-image-content {
      height: calc(100% - 30px);
      display: flex;
      padding: 10px 10px 0 10px;

      .data-ic-left {
        width: 120px;
        height: 100%;
        overflow-y: auto;

        .selectCss {
          background: rgba(178, 216, 255, 0.1);
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #409EFF;
        }

        >div {
          cursor: pointer;
          height: 25px;
          margin-bottom: 5px;
          border: 1px solid rgba(64, 158, 255, 0.2);
          border-radius: 4px 4px 4px 4px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .data-ic-right {
        width: 100%;
        height: 100%;
        position: relative;

        .data-ic-stlc {
          width: auto;
          position: absolute;
          right: 5px;
          bottom: 35px;
          font-size: 14px;
          font-family: "PingFang SC-Medium";
          font-weight: 500;
          color: #FFFFFF;
        }

        .data-ic-img {
          width: 100%;
          height: calc(100% - 30px);
          border-radius: 4px 4px 4px 4px;

          .data-img {
            width: 100%;
            height: 100%;
          }
        }

        .data-ic-btn {
          width: 100%;
          height: 22px;
          margin-top: 5px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          cursor: pointer;

          >div:first-child {
            height: 22px;
            font-size: 16px;
            font-family: "PingFang SC-Medium";
            font-weight: 500;
            color: #2480DE;
          }

          .xq-img {
            width: 15px;
            height: 15px;
            margin-left: 3px;
            margin-top: 3px;
          }
        }
      }

    }
  }

  .nodata {
    width: 100%;
    height: 100%;
    background: url('@/assets/images/noDate.jpg') no-repeat;
    background-position: center center;
  }
}

.realRain-table {
  margin-top: 20px;
  height: 600px;
  width: 100%;
  display: flex;

  .table-img {
    width: 300px;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    .singel-img {
      width: 100%;
      height: 154px;
      margin-bottom: 10px;
      position: relative;

      .data-time {
        position: absolute;
        width: auto;
        right: 10px;
        bottom: 10px;
        font-size: 14px;
        font-family: "PingFang SC-Medium";
        font-weight: 500;
        color: #FFFFFF;
      }

      .selImg {
        padding: 0 !important;
      }

      .data-image {
        width: 100%;
        height: 144px;
        cursor: pointer;
        padding: 5px;

        .data-img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .table-carousel {
    margin-left: 20px;
    width: calc(100% - 320px);
    height: 100%;
    border: 1px solid black;

    .table-carousel-img {
      width: 100%;
      height: 100%;
      position: relative;

      .show-flow {
        position: absolute;
        bottom: 20px;
        left: 20px;

        .show-flow-item {
          display: block;
          color: #ffffff;
          font-size: 20px;
        }
      }

      :deep(.el-carousel__container) {
        height: 95%;
      }

      .carousel-image {
        width: 100%;
        height: 100%;
      }

      .carousel-right {
        cursor: pointer;
        position: absolute;
        width: 30px;
        height: 30px;
        right: 10px;
        top: 50%;

        .carousel-right-but {
          width: 100%;
          height: 100%;
        }
      }

      .carousel-left {
        cursor: pointer;
        position: absolute;
        width: 30px;
        height: 30px;
        left: 10px;
        top: 50%;

        .carousel-left-but {
          width: 100%;
          height: 100%;
        }
      }
    }

    .empt-disflow {
      display: block;
      width: 300px;
      height: 270px;
      margin-top: 200px;
      margin-left: 600px;
      color: #909399;
      box-sizing: border-box;
      zoom: 1;
      background: url("@/assets/images/noDate.jpg") no-repeat;
      background-size: cover;
    }
  }
}
</style>
