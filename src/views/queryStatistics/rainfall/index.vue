<template>
  <div class="app-container">
    <el-form
      :model="queryForm"
      ref="queryForm1"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="行政区">
        <el-tree-select
          v-model="queryForm.adcd"
          :data="adcdList"
          clearable
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
          value-key="id"
          placeholder="请选择行政区"
          check-strictly
        />
      </el-form-item>
      <el-form-item label="时段长">
        <el-select
          v-model="queryForm.longDuration"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in hours"
            :key="item"
            :label="`${item}小时`"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search"
          >查询</el-button
        >
        <el-button @click="reset" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="addData">新增</el-button>
      </div>
      <el-table v-loading="loading" :data="tableList" stripe>
        <el-table-column label="序号" width="60" type="index"></el-table-column>
        <el-table-column label="区县" prop="adnm"></el-table-column>
        <el-table-column label="时段长(时)" prop="longDuration">
        </el-table-column>
        <el-table-column label="重现期(年)" prop="basinName" align="center">
          <el-table-column
            :label="item + ''"
            v-for="item in years"
            :prop="`returnPeriod${item}`"
          >
          </el-table-column>
        </el-table-column>

        <el-table-column
          label="操作"
          prop="stationAddr"
          width="160"
          align="center"
        >
          <template #default="scope">
            <el-button
              type="primary"
              icon="Edit"
              size="mini"
              link
              @click="editOne(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              icon="Delete"
              size="mini"
              link
              @click="deleteOne(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryForm.pageNum"
        v-model:limit="queryForm.pageSize"
        @pagination="getList"
      />
    </div>
    <el-dialog
      v-model="addShow"
      :title="title"
      width="650px"
      :close-on-click-modal="false"
      class="rainfall-dialog"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        label-width="80px"
        :rules="addRule"
        class="rainfall-form"
      >
        <div class="form-section">
          <h3 class="section-title">基础信息</h3>
          <div class="form-row">
            <el-form-item label="区域" prop="adcd">
              <el-tree-select
                v-model="addForm.adcd"
                :data="adcdList"
                clearable
                style="width: 100%"
                :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
                value-key="id"
                placeholder="请选择行政区"
                check-strictly
              />
            </el-form-item>
            <el-form-item label="时段长" prop="longDuration">
              <el-select
                v-model="addForm.longDuration"
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="item in hours"
                  :key="item"
                  :label="`${item}小时`"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <div class="form-section">
          <h3 class="section-title">重现期降雨量</h3>
          <div class="form-grid">
            <el-form-item label="2年" prop="returnPeriod2">
              <el-input
                v-model="addForm.returnPeriod2"
                placeholder="请输入数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
            <el-form-item label="5年" prop="returnPeriod5">
              <el-input
                v-model="addForm.returnPeriod5"
                placeholder="请输入数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
            <el-form-item label="10年" prop="returnPeriod10">
              <el-input
                v-model="addForm.returnPeriod10"
                placeholder="请输入数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
            <el-form-item label="20年" prop="returnPeriod20">
              <el-input
                v-model="addForm.returnPeriod20"
                placeholder="请输入数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
            <el-form-item label="30年" prop="returnPeriod30">
              <el-input
                v-model="addForm.returnPeriod30"
                placeholder="请输入数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
            <el-form-item label="50年" prop="returnPeriod50">
              <el-input
                v-model="addForm.returnPeriod50"
                placeholder="请输入数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
            <el-form-item label="100年" prop="returnPeriod100">
              <el-input
                v-model="addForm.returnPeriod100"
                placeholder="请输入数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
            <el-form-item label="200年" prop="returnPeriod200">
              <el-input
                v-model="addForm.returnPeriod200"
                placeholder="请输入数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
            <el-form-item label="500年" prop="returnPeriod500">
              <el-input
                v-model="addForm.returnPeriod500"
                placeholder="请输入数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
            <el-form-item label="1000年" prop="returnPeriod1000">
              <el-input
                v-model="addForm.returnPeriod1000"
                placeholder="请输入数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
      <template #footer>
        <el-button @click="submitForm" v-if="status == 'add'" type="primary"
          >保 存</el-button
        >
        <el-button @click="updateForm" v-else type="primary">保 存</el-button>
        <el-button @click="addShow = false" plain>取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  getCurrentInstance,
} from "vue";
import { getAdcdTree } from "@/api/watershed/ads";
import {
  rainFallList,
  addRainFallData,
  deleteRainFallData,
  updateRainFallData,
} from "@/api/watershed/query/index";

export default defineComponent({
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      waterAreaList: [],
      hours: [1, 3, 6, 12, 24, 48, 72, 120, 168],
      years: [2, 5, 10, 20, 30, 50, 100, 200, 500, 1000],
      addShow: false,
      queryForm: {
        pageNum: 1,
        pageSize: 20,
        adcd: "",
        longDuration: "",
      },
      status: "add",
      addRule: {
        adcd: [{ required: true, message: "请选择区域", trigger: "blur" }],
        longDuration: [
          { required: true, message: "请输入时段长", trigger: "blur" },
        ],
        returnPeriod2: [
          { required: true, message: "请输入降雨量数值", trigger: "blur" },
        ],
        returnPeriod5: [
          { required: true, message: "请输入降雨量数值", trigger: "blur" },
          //数值要比returnPeriod2大
          {
            validator: (rule, value, callback) => {
              if (parseInt(value) <= parseInt(state.addForm.returnPeriod2)) {
                callback(new Error("重现期降雨量数值递增!!"));
              } else {
                callback();
              }
            },
          },
        ],
        returnPeriod10: [
          { required: true, message: "请输入降雨量数值", trigger: "blur" },
          //数值要比returnPeriod5大
          {
            validator: (rule, value, callback) => {
              if (parseInt(value) <= parseInt(state.addForm.returnPeriod5)) {
                callback(new Error("重现期降雨量数值递增!!"));
              } else {
                callback();
              }
            },
          },
        ],
        returnPeriod20: [
          { required: true, message: "请输入降雨量数值", trigger: "blur" },
          //数值要比returnPeriod10大
          {
            validator: (rule, value, callback) => {
              if (parseInt(value) <= parseInt(state.addForm.returnPeriod10)) {
                callback(new Error("重现期降雨量数值递增!!"));
              } else {
                callback();
              }
            },
          },
        ],
        returnPeriod30: [
          { required: true, message: "请输入降雨量数值", trigger: "blur" },
          //数值要比returnPeriod20大
          {
            validator: (rule, value, callback) => {
              if (parseInt(value) <= parseInt(state.addForm.returnPeriod20)) {
                callback(new Error("重现期降雨量数值递增!!"));
              } else {
                callback();
              }
            },
          },
        ],
        returnPeriod50: [
          { required: true, message: "请输入降雨量数值", trigger: "blur" },
          //数值要比returnPeriod30大
          {
            validator: (rule, value, callback) => {
              if (parseInt(value) <= parseInt(state.addForm.returnPeriod30)) {
                callback(new Error("重现期降雨量数值递增!!"));
              } else {
                callback();
              }
            },
          },
        ],
        returnPeriod100: [
          { required: true, message: "请输入降雨量数值", trigger: "blur" },
          //数值要比returnPeriod50大
          {
            validator: (rule, value, callback) => {
              if (parseInt(value) <= parseInt(state.addForm.returnPeriod50)) {
                callback(new Error("重现期降雨量数值递增!!"));
              } else {
                callback();
              }
            },
          },
        ],
        returnPeriod200: [
          { required: true, message: "请输入降雨量数值", trigger: "blur" },
          //数值要比returnPeriod100大
          {
            validator: (rule, value, callback) => {
              if (parseInt(value) <= parseInt(state.addForm.returnPeriod100)) {
                callback(new Error("重现期降雨量数值递增!!"));
              } else {
                callback();
              }
            },
          },
        ],
        returnPeriod500: [
          { required: true, message: "请输入降雨量数值", trigger: "blur" },
          //数值要比returnPeriod200大
          {
            validator: (rule, value, callback) => {
              if (parseInt(value) <= parseInt(state.addForm.returnPeriod200)) {
                callback(new Error("重现期降雨量数值递增!!"));
              } else {
                callback();
              }
            },
          },
        ],
        returnPeriod1000: [
          { required: true, message: "请输入降雨量数值", trigger: "blur" },
          //数值要比returnPeriod500大
          {
            validator: (rule, value, callback) => {
              if (parseInt(value) <= parseInt(state.addForm.returnPeriod500)) {
                callback(new Error("重现期降雨量数值递增!!"));
              } else {
                callback();
              }
            },
          },
        ],
      },
      total: 0,
      tableList: [],
      loading: false,
      adcdList: [],
      dialogList: [],
      detailShow: false,
      dataShow: true,
      title: "",
      time: "",
      stcd: "",
      addForm: {
        adcd: "",
        longDuration: "",
        returnPeriod2: "",
        returnPeriod5: "",
        returnPeriod10: "",
        returnPeriod20: "",
        returnPeriod30: "",
        returnPeriod50: "",
        returnPeriod100: "",
        returnPeriod200: "",
        returnPeriod500: "",
        returnPeriod1000: "",
      },
    });
    onMounted(() => {
      getAdcdList();
      getList();
    });
    const editOne = (item) => {
      state.title = "编辑降雨频率 - " + item.adnm;
      state.status = "edit";
      state.addForm = Object.assign({}, item);
      state.addShow = true;
    };
    const getAdcdList = () => {
      getAdcdTree({
        adcd: "",
      }).then((res) => {
        state.adcdList = res.data[0].children;
      });
    };
    const addData = () => {
      state.title = "新增降雨频率";
      state.status = "add";
      state.addShow = true;
      proxy.$nextTick(() => {
        proxy.$refs.addFormRef.resetFields();
      });
    };
    const submitForm = () => {
      proxy.$refs.addFormRef.validate((valid) => {
        if (valid) {
          addRainFallData(state.addForm).then((res) => {
            if (res.code == 200) {
              proxy.$modal.msgSuccess("添加成功");
              getList();
            } else {
              proxy.$modal.msgError(res.msg);
            }
            state.addShow = false;
          });
        }
      });
    };
    const updateForm = () => {
      proxy.$refs.addFormRef.validate((valid) => {
        if (valid) {
          updateRainFallData(state.addForm).then((res) => {
            if (res.code == 200) {
              proxy.$modal.msgSuccess("修改成功");
              getList();
            } else {
              proxy.$modal.msgError(res.msg);
            }
            state.addShow = false;
          });
        }
      });
    };
    const deleteOne = (row) => {
      proxy
        .$confirm("是否删除该数据", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          deleteRainFallData({
            id: row.id,
          }).then((res) => {
            if (res.code == 200) {
              proxy.$modal.msgSuccess("删除成功");
            } else {
              proxy.$modal.msgError(res.msg);
            }
            getList();
          });
        });
    };
    const getList = () => {
      state.loading = true;
      rainFallList(state.queryForm).then((res) => {
        state.tableList = res.data || [];
        state.total = res.total || 0;
        state.loading = false;
      });
    };

    const reset = () => {
      state.queryForm = {
        pageNum: 1,
        pageSize: 20,
        adcd: "",
        longDuration: "",
      };
      getList();
    };

    return {
      ...toRefs(state),
      getList,
      reset,
      addData,
      submitForm,
      deleteOne,
      editOne,
      updateForm,
    };
  },
});
</script>

<style scoped lang="scss">
.flexbox {
  display: flex;
  justify-content: space-between;
}

.rainfall-dialog {
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    margin-right: 0;
  }

  :deep(.el-dialog__body) {
    padding: 24px 30px;
  }

  :deep(.el-dialog__footer) {
    border-top: 1px solid #f0f0f0;
    padding: 16px 20px;
  }

  :deep(.el-dialog__headerbtn) {
    top: 16px;
    right: 20px;
  }
}

.rainfall-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 10px;

  /* 美化滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }

  :deep(.el-form-item) {
    margin-bottom: 20px;
  }
}

.form-section {
  margin-bottom: 24px;
  background-color: #ffffff;
  border-radius: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: -1px;
    width: 50px;
    height: 2px;
    background-color: #409eff;
  }
}

.form-row {
  display: flex;
  gap: 20px;

  :deep(.el-form-item) {
    flex: 1;
    margin-right: 0;
  }
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px 20px;

  :deep(.el-form-item) {
    margin: 0;
  }

  :deep(.el-input) {
    transition: all 0.3s;

    &:hover .el-input__wrapper {
      box-shadow: 0 0 0 1px #c0c4cc inset;
    }

    .el-input__wrapper {
      border-radius: 4px;
    }
  }
}
</style>
