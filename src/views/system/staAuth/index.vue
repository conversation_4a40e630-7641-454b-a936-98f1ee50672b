<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" class="form-container">
      <el-form-item label="测站名称/代码" prop="searchValue">
        <el-input v-model="queryParams.searchValue" placeholder="请输入测站名称/代码" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="测站类型" prop="sttp">
        <el-select v-model="queryParams.sttp" placeholder="请选择测站类型" clearable style="width: 200px">
          <el-option v-for="item in sttpSelects" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属租户" prop="tenantId">
        <el-select v-model="queryParams.tenantId" placeholder="请选择租户" clearable style="width: 200px">
          <el-option v-for="item in tenantOptions" :key="item.id" :label="item.tenantName" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="行政区划" prop="adcd">
        <el-tree-select v-model="queryParams.adcd" :data="adcdOptions" clearable
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="adcd" placeholder="选择所属县区"
          check-strictly />
      </el-form-item>

      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <el-table v-loading="loading" :data="lyList" stripe>
        <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
        <el-table-column prop="stcd" label="测站编码" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="stnm" label="测站名称" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="adnm" label="区县" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="sttp" label="测站类型" :show-overflow-tooltip="true">
          <template #default="scope">
            <span>{{ getengScal(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="tenantName" label="所属租户" :show-overflow-tooltip="true"></el-table-column>

        <el-table-column label="操作" align="center" width="210" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">权限分配</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog v-model="open" width="400px" append-to-body title="权限分配" @close="cancel">
      <el-form ref="rsvRef" :model="form" :rules="rules" label-width="100px">
        <el-col :span="24">
          <el-form-item label="管理租户" prop="tenantId">
            <el-select v-model="form.tenantId" placeholder="请选择管理租户" @change="changeTenant">
              <el-option v-for="item in tenantOptions" :key="item.id" :label="item.tenantName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="允许访问" prop="viewTenantIdList">
            <el-select v-model="form.viewTenantIdList" placeholder="请选择允许访问租户" multiple>
              <el-option v-for="item in tenantOptions" :key="item.id" :label="item.tenantName" :value="item.id"
                :disabled="item.disabled">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance } from "vue";
import { selectAdcdList, selectAuthList, getStaTenInfo, saveAuthList } from "@/api/watershed/ads";
import { listTenant } from "@/api/system/tenant";
export default defineComponent({
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      queryParams: {
        resName: '',
        registerCode: '',
        engScal: '',
        asNext: 1, // 1 关联查询 ， 0 不关联就查自己的
        pageNum: 1,
        pageSize: 1000,
      },
      adcdOptions: [],
      loading: false,
      lyList: [],
      open: false,
      stcd: '',
      form: {
        viewTenantIdList: []
      },
      rules: {
        tenantName: [{ required: true, message: "管理不能为空", trigger: "blur" }],
      },
      sttpSelects: [
        { label: '雨量站', value: 'PP' },
        { label: '河道水文站', value: 'ZQ' },
        { label: '河道水位站', value: 'ZZ' },
        { label: '水库水文站', value: 'RR' },
      ],
      tenantOptions: [],
    })
    /** 查询区县下拉树结构 */
    const getTreeselect = () => {

      selectAdcdList({}).then((res) => {
        state.adcdOptions = res.data
      })
    }
    const getengScal = (data) => {
      for (let i = 0; i < state.sttpSelects.length; i++) {
        if (state.sttpSelects[i].value == data.sttp) {
          return state.sttpSelects[i].label
        }
      }
    }
    const handleQuery = () => {
      state.loading = true
      selectAuthList(state.queryParams).then(res => {
        state.lyList = res.data.records
        state.loading = false
      }).finally(() => {
        state.loading = false
      })
    }
    const resetQuery = () => {
      proxy.resetForm("queryRef");
      handleQuery();
    }
    const handleUpdate = (row) => {
      state.stcd = row.stcd
      getStaTenInfo(row.stcd).then(res => {
        state.form.tenantId = res.data.tenantId
        if (res.data.stationTenantDtos && res.data.stationTenantDtos.length > 0) {
          let arr = []
          res.data.stationTenantDtos.forEach(e => {
            arr.push(e.tenantId)
          })
          state.form.viewTenantIdList = arr
        } else {
          state.form.viewTenantIdList = []
        }
        changeTenant(state.form.tenantId)
        state.open = true
      })
    }
    const changeTenant = (v) => {
      state.tenantOptions.forEach(e => {

        e.disabled = e.id == v;
      })
    }
    const cancel = () => {
      state.open = false
      state.form = {
        viewTenantIdList: []
      }
    }
    const submitForm = () => {
      proxy.$refs["rsvRef"].validate(valid => {
        if (valid) {
          saveAuthList({
            stcd: state.stcd,
            ...state.form
          }).then(res => {
            proxy.$modal.msgSuccess("修改成功");
            handleQuery()
            cancel()
          })
        }
      })

    }
    onMounted(() => {
      getTreeselect()
      handleQuery()
      listTenant({
        pageNum: 1,
        pageSize: 1000,
      }).then(res => {
        state.tenantOptions = res.data
      })
    })
    return {
      ...toRefs(state),
      getengScal,
      handleQuery,
      handleUpdate,
      resetQuery,
      changeTenant,
      cancel,
      submitForm
    };
  },
});
</script>
<style scoped lang="scss"></style>