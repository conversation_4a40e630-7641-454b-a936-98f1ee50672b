<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="form-container">
         <el-form-item label="部门名称" prop="deptName">
            <el-input v-model="queryParams.deptName" placeholder="请输入部门名称" clearable
               @keyup.enter="handleQuery" />
         </el-form-item>

         <el-form-item class="form-item-btn">
            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <div class="content content-table">
         <div class="table-header">
            <el-row :gutter="10">
               <el-col :span="1.5">
                  <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
               </el-col>
               <el-col :span="1.5">
                  <el-button type="info" plain icon="Sort" @click="toggleExpandAll">展开/折叠</el-button>
               </el-col>
               <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
         </div>
         <el-table v-if="refreshTable" v-loading="loading" :data="deptList" row-key="deptId"
            :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" stripe>
            <el-table-column prop="deptName" label="部门名称" width="260"></el-table-column>
            <el-table-column prop="orderNum" label="排序" width="200"></el-table-column>

            <el-table-column label="创建时间" align="center" prop="createTime" width="200">
               <template #default="scope">
                  <span>{{ parseTime(scope.row.createTime) }}</span>
               </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
               <template #default="scope">
                  <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                     v-if="scope.row.isDefault == 0">修改</el-button>
                  <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)">新增</el-button>
                  <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                     v-if="scope.row.isDefault == 0">删除</el-button>
               </template>
            </el-table-column>
         </el-table>
      </div>
      <!-- 添加或修改部门对话框 -->
      <el-dialog :title="title" v-model="open" width="600px" append-to-body>
         <el-form ref="deptRef" :model="form" :rules="rules" label-width="100px">
            <el-row>
               <el-col :span="12" v-if="form.parentId !== 0">
                  <el-form-item label="上级部门" prop="parentId">
                     <el-tree-select v-model="form.parentId" :data="deptOptions"
                        :props="{ value: 'deptId', label: 'deptName', children: 'children' }" value-key="deptId"
                        placeholder="选择上级部门" check-strictly style="width: 100%;" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="部门名称" prop="deptName">
                     <el-input v-model="form.deptName" placeholder="请输入部门名称" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="显示排序" prop="orderNum">
                     <el-input-number v-model="form.orderNum" controls-position="right" :min="0" style="width: 100%;" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="所属行政区" prop="adcd">
                     <el-tree-select v-model="form.adcd" :data="adcdOptions" clearable
                        :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="id"
                        placeholder="请选择行政区" check-strictly />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="负责人" prop="leader">
                     <el-input v-model="form.leader" placeholder="请输入负责人" maxlength="20" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="联系电话" prop="phone">
                     <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">保 存</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup>
import { listDept, getDept, delDept, addDept, updateDept } from "@/api/system/dept";
import { getAdcdTree } from "@/api/watershed/ads";

defineOptions({
  name: 'DeptIndex'
})

const { proxy } = getCurrentInstance();

const deptList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const deptOptions = ref([]);
const isExpandAll = ref(true);
const refreshTable = ref(true);
const adcdOptions = ref([]);

const data = reactive({
   form: {},
   queryParams: {
      deptName: undefined,
      status: undefined
   },
   rules: {
      // parentId: [{ required: true, message: "上级部门不能为空", trigger: "blur" }],
      deptName: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
      orderNum: [{ required: true, message: "显示排序不能为空", trigger: "blur" }],
      phone: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }]
   },
});

const { queryParams, form, rules } = toRefs(data);
/** 查询流域下拉树结构 */
function getTreeselect() {
   adcdOptions.value = [];
   getAdcdTree().then(res => {
      adcdOptions.value = res.data[0].children

   })
   listDept({}).then(response => {
      deptOptions.value = proxy.handleTree(response.data, "deptId");

   });
}
/** 查询部门列表 */
function getList() {
   loading.value = true;
   listDept(queryParams.value).then(response => {
      deptList.value = proxy.handleTree(response.data, "deptId");
      loading.value = false;
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 表单重置 */
function reset() {
   form.value = {
      deptId: undefined,
      parentId: undefined,
      deptName: undefined,
      orderNum: 0,
      leader: undefined,
      phone: undefined,
      email: undefined,
      status: "0"
   };
   proxy.resetForm("deptRef");
}
/** 搜索按钮操作 */
function handleQuery() {
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
   reset();
   // listDept().then(response => {
   //    deptOptions.value = proxy.handleTree(response.data, "deptId");
   // });
   if (row != undefined) {
      form.value.parentId = row.deptId;
   }
   open.value = true;
   title.value = "添加部门";
}
/** 展开/折叠操作 */
function toggleExpandAll() {
   refreshTable.value = false;
   isExpandAll.value = !isExpandAll.value;
   nextTick(() => {
      refreshTable.value = true;
   });
}
/** 修改按钮操作 */
function handleUpdate(row) {
   reset();
   // listDeptExcludeChild(row.deptId).then(response => {
   //    deptOptions.value = proxy.handleTree(response.data, "deptId");
   // });
   getDept(row.deptId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改部门";
   });
}
/** 提交按钮 */
function submitForm() {
   proxy.$refs["deptRef"].validate(valid => {
      if (valid) {
         if (form.value.deptId != undefined) {
            updateDept(form.value).then(response => {
               proxy.$modal.msgSuccess("修改成功");
               open.value = false;
               getList();
            });
         } else {
            addDept(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getList();
            });
         }
      }
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   proxy.$modal.confirm('是否确认删除名称为"' + row.deptName + '"的数据项?').then(function () {
      return delDept(row.deptId);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}

getList();
getTreeselect();
</script>
