<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" class="form-container">
      <el-form-item label="套餐名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入套餐名" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="套餐状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择租户套餐状态" clearable>
          <el-option v-for="dict in sys_tenant_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
             >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
             >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
            >导出</el-button>
          </el-col> -->
          <right-toolbar :showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="tenantpackageList" @selection-change="handleSelectionChange" stripe>
        <el-table-column fixed label="序号" min-width="20%" align="center">
          <template v-slot="scope">

            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + (scope.$index + 1) }} </span>
          </template>
        </el-table-column>
        <el-table-column label="套餐编号" align="center" prop="id" v-if="false" />
        <el-table-column label="套餐名" align="center" prop="name" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template v-slot="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="租户套餐状态" align="center" prop="status">
          <template v-slot="scope">
            <dict-tag :options="sys_tenant_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template v-slot="scope">
            <el-button type="primary" link @click="handleUpdate(scope.row)" icon="Edit">修改</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)" icon="Delete">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" v-model="open" width="550px" append-to-body>
      <div class="form-dialog">
        <el-form ref="form" :model="diaForm" :rules="rules" label-width="80px">
        <el-form-item label="套餐名" prop="name">
          <el-input v-model="diaForm.name" placeholder="请输入套餐名" />
        </el-form-item>
        <el-form-item label="菜单权限">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event)">展开/折叠</el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event)">全选/全不选</el-checkbox>
          <el-tree class="tree-border" :data="menuOptions" show-checkbox ref="menu" node-key="id"
            :check-strictly="menuCheckStrictly" empty-text="加载中，请稍后" :props="defaultProps"></el-tree>
        </el-form-item>

        <el-form-item label="套餐状态">
          <el-radio-group v-model="diaForm.status">
            <el-radio v-for="dict in sys_tenant_status" :key="dict.value" :label="parseInt(dict.value)">{{
              dict.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, reactive, getCurrentInstance, toRefs } from "vue";
import { listTenantpackage, getTenantpackage, delTenantpackage, addTenantpackage, updateTenantpackage, listSimpleMenus, listSimpleAllMenus } from "@/api/system/tenantpackage";
import { parseTime } from "@/utils/ruoyi";
export default defineComponent({
  setup() {
    // name: "Tenantpackage",
    // dicts: ['sys_tenant_status'],
    const { proxy } = getCurrentInstance();
    const { sys_tenant_status } = proxy.useDict("sys_tenant_status");
    const state = reactive({
      defaultProps: {
        label: "name",
        children: "children"
      },
      menuOptions: [], // 菜单列表
      //展开参数
      menuExpand: false,
      menuNodeAll: false,
      menuCheckStrictly: true,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 租户套餐表格数据
      tenantpackageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        status: null
      },
      // 表单参数
      diaForm: {
        id: null,
        name: null,
        menuIds: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        status: 0
      },
      // 表单校验
      rules: {
        name: [{ required: true, message: "套餐名不能为空", trigger: "blur" }],
        status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
        menuIds: [{ required: true, message: "关联的菜单编号不能为空", trigger: "blur" }],
      },
      allMenu: [],


    })
    return {
      ...toRefs(state),
      sys_tenant_status
    }
  },
  created() {
    this.getList();
    this.getMenus();
  },
  methods: {
    /** 查询租户套餐列表 */
    getList() {
      this.loading = true;
      listTenantpackage(this.queryParams).then(response => {
        this.tenantpackageList = response.data;
        this.total = response.total;
      }).finally(() => {
        this.loading = false;
      })
    },
    /** 获得菜单 */
    getMenus() {
      listSimpleMenus().then(response => {
        // 处理 menuOptions 参数
        this.menuOptions = [];
        // 只需要配置
        this.menuOptions.push(...this.handleTree(response.data, "id"));
      });
      listSimpleAllMenus().then(res => {
        this.allMenu = res.data
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置

    reset() {
      if (this.$refs.menu !== undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.menuExpand = false;
      this.menuNodeAll = false;
      this.menuCheckStrictly = true;
      this.diaForm = {
        id: null,
        name: null,
        menuIds: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        status: 0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加租户套餐";
      this.menuCheckStrictly = false;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      this.open = true;
      this.title = "修改租户套餐";
      // 获得菜单列表
      getTenantpackage(id).then(response => {
        this.diaForm = response.data;
        // 设置菜单项
        // 设置为严格，避免设置父节点自动选中子节点，解决半选中问题
        this.menuCheckStrictly = true
        // 设置选中
        this.$refs.menu.setCheckedKeys(response.data.menuIds.split(','));
        // 设置为非严格，继续使用半选中
        this.menuCheckStrictly = false
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // console.log(this.diaForm.menuIds.split(','));
          let str = ''
          if (this.diaForm.menuIds) {
            this.diaForm.menuIds.split(',').forEach(e => {
              this.allMenu.forEach(el => {
                if (e == el.parentId && el.type == 3) {
                  str += ',' + el.id
                }
              })
            });
          }
          // this.diaForm.menuIds += str
          console.log(str);
          if (this.diaForm.id != null) {
            console.log([...this.$refs.menu.getCheckedKeys(), ...this.$refs.menu.getHalfCheckedKeys()].toString() + str);
            updateTenantpackage({
              ...this.diaForm,
              menuIds: [...this.$refs.menu.getCheckedKeys(), ...this.$refs.menu.getHalfCheckedKeys()].toString() + str
            }).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTenantpackage({
              // ...this.diaForm,
              name: this.diaForm.name,
              status: this.diaForm.status,
              menuIds: [...this.$refs.menu.getCheckedKeys(), ...this.$refs.menu.getHalfCheckedKeys()].toString() + str
            }).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除当前租户套餐数据项？').then(function () {
        return delTenantpackage(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/tenant-package/export', {
        ...this.queryParams
      }, `租户套餐_${parseTime(new Date().getTime(), '{y}-{m}-{d}')}.xlsx`)
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      let treeList = this.menuOptions;
      for (let i = 0; i < treeList.length; i++) {
        this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value) {
      this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value) {
      this.diaForm.menuCheckStrictly = value;
    }
  }
})
</script>
<style lang="scss" scoped>
.form-dialog {
  height: calc(100vh - 400px);
  min-height: 400px;
  overflow-y: auto;

  // 美化滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 4px;
  }
}
</style>