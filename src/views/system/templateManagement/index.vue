<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="文件模板编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入文件模板编号"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="文件模板名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入文件模板名称"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="文件模板文件名" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入文件模板文件名"
          clearable
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="Refresh" type="primary" plain @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd"
              >新增</el-button
            >
            <!-- <el-button type="success" icon="DocumentAdd" @click="importDialogVisible = true" style="margin-left: 8px;">导入</el-button> -->
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </div>
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="templateList"
        row-key="id"
        stripe
      >
        <el-table-column
          type="index"
          label="序号"
          width="65"
          align="center"
          :index="indexMethod"
        ></el-table-column>
        <el-table-column
          prop="code"
          label="文件模板编号"
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="文件模板名称"
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="filePath"
          label="文件模板文件路径"
          width="550"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="tenantId"
          label="租户编号"
          align="center"
        ></el-table-column>
        

        <el-table-column
          label="操作"
          align="center"
          width="280"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <!-- <el-button
              link
              type="primary"
              icon="View"
              @click="handleView(scope.row)"
              >查看</el-button
            > -->
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 新增弹窗：新增预报任务 -->
    <el-dialog
      :title="dialogTitle"
      v-model="addDialogVisible"
      width="500px"
      append-to-body
      @close="resetAddForm"
    >
      <el-form
        :model="addForm"
        :rules="addRules"
        ref="addFormRef"
        label-width="140px"
      >
        <!-- 文件模板上传组件 -->
        <el-form-item label="文件模板上传" prop="file">
          <el-upload
            class="upload-demo"
            :action="uploadFileUrl"
            :limit="1"
            :show-file-list="true"
            :file-list="uploadFileList"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            :data="uploadParams.data"
            :headers="uploadParams.headers"
            :auto-upload="true"
            :disabled="false"
            :on-preview="handlePreview"
          >
            <el-button type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="文件模板编号" prop="code" required>
          <el-input v-model="addForm.code" placeholder="请输入文件模板编号" />
        </el-form-item>
        <el-form-item label="文件模板名称" prop="name" required>
          <el-input v-model="addForm.name" placeholder="请先上传文件模板" readonly />
        </el-form-item>
        <el-form-item label="文件模板文件名" prop="fileName" required>
          <el-input v-model="addForm.fileName" placeholder="请输入文件模板文件名" />
        </el-form-item>
        <el-form-item label="文件模板文件路径" prop="filePath" required>
          <el-input v-model="addForm.filePath" placeholder="请先上传文件模板" readonly />
        </el-form-item>
        <!-- <el-form-item label="租户编号" prop="tenantId" required>
          <el-input v-model="addForm.tenantId" placeholder="请输入租户编号" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
  
<script setup>
import { ref, reactive, computed, toRefs, getCurrentInstance, onMounted } from "vue";
import { getFileTemplateList, addFileTemplate, modifyFileTemplate, deleteFileTemplate } from "@/api/watershed/forecast/index.js";
import { getToken } from "@/utils/auth";

const { proxy } = getCurrentInstance();
const uploadFileUrl = import.meta.env.VITE_APP_BASE_API + "/file/upload";
const downloadFileUrl = import.meta.env.VITE_APP_BASE_API + "/file/download";
// 列表数据与分页
const templateList = ref([]);
const total = ref(0);
const loading = ref(false);
const showSearch = ref(true);
const refreshTable = ref(true);
const uploadParams = reactive({
  data: {
    bucketName: 'watershed'
  },
  headers: {
    Authorization: 'Bearer ' + getToken()
  }
});
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  code: '',
  name: '',
  fileName: ''
});

// 弹窗表单
const addDialogVisible = ref(false);
const addFormRef = ref();
const addForm = reactive({
  code: '',
  name: '',
  fileName: '',
  filePath: '',
  tenantId: ''
});
const dialogMode = ref('add'); // 'add' | 'edit'
const dialogTitle = computed(() => dialogMode.value === 'edit' ? '编辑文件模板' : '新增文件模板');

// 校验规则
const addRules = {
  code: [{ required: true, message: "请输入文件模板编号", trigger: "blur" }],
  name: [{ required: true, message: "请输入文件模板名称", trigger: "blur" }],
  fileName: [{ required: true, message: "请输入文件模板文件名", trigger: "blur" }],
  filePath: [{ required: true, message: "请输入文件模板文件路径", trigger: "blur" }],
  tenantId: [{ required: true, message: "请输入租户编号", trigger: "blur" }],
};

// 查询列表
function getList() {
  loading.value = true;
  getFileTemplateList({
    code: queryParams.code,
    name: queryParams.name,
    fileName: queryParams.fileName,
    pageNum: queryParams.pageNum,
    pageSize: queryParams.pageSize
  }).then(res => {
    templateList.value = res.rows || [];
    total.value = res.total || 0;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

// 查询按钮操作
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}
// 重置按钮操作
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
// 新增按钮操作
function handleAdd() {
  dialogMode.value = 'add';
  Object.assign(addForm, { code: '', name: '', fileName: '', filePath: '', tenantId: '' });
  uploadFileList.value = [];
  addDialogVisible.value = true;
}
// 编辑按钮操作
function handleUpdate(row) {
  dialogMode.value = 'edit';
  Object.assign(addForm, row);
  // 初始化文件列表为当前文件
  if (row.filePath && row.name) {
    uploadFileList.value = [{
      name: row.name,
      url: downloadFileUrl
    }];
  } else {
    uploadFileList.value = [];
  }
  addDialogVisible.value = true;
}
// 删除按钮操作
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除编号为"' + row.code + '"的文件模板?')
    .then(() => deleteFileTemplate(row.code))
    .then(() => {
      proxy.$modal.msgSuccess("删除成功");
      getList();
    })
    .catch(() => {});
}
// 弹窗保存
function handleSave() {
  addFormRef.value.validate(valid => {
    if (!valid) return;
    const api = dialogMode.value === 'add' ? addFileTemplate : modifyFileTemplate;
    api({
      code: addForm.code,
      name: addForm.name,
      fileName: addForm.fileName,
      filePath: addForm.filePath,
      tenantId: addForm.tenantId
    }).then(() => {
      proxy.$modal.msgSuccess(dialogMode.value === 'add' ? "新增成功" : "编辑成功");
      addDialogVisible.value = false;
      resetAddForm();
      getList();
    });
  });
}
// 分页
function handlePagination(page, pageSize) {
  queryParams.pageNum = page;
  queryParams.pageSize = pageSize;
  getList();
}



// 上传前校验（可选）
function beforeUpload(file) {
  // 可根据需要添加文件类型/大小校验
  return true;
}

const uploadFileList = ref([]);

// 上传成功回调
function handleUploadSuccess(response, file, fileList) {
  // 1. 判断状态码
  if (response && response.code === 200 && response.msg) {
    addForm.filePath = response.msg;
    // 2. 提取最后一个下划线后的内容作为name
    const idx = response.msg.lastIndexOf('_');
    if (idx !== -1 && idx < response.msg.length - 1) {
      addForm.name = response.msg.substring(idx + 1);
    } else {
      addForm.name = response.msg;
    }
    // 自动校验通过
    if (addFormRef.value) {
      addFormRef.value.validateField('name');
      addFormRef.value.validateField('filePath');
    }
    // 上传成功提示
    proxy.$modal.msgSuccess('上传成功');
    // 更新上传文件列表，仅保留最新一个
    uploadFileList.value = [{
      name: addForm.name,
      url: '', // 如有下载地址可补充
    }];
  } else {
    proxy.$modal.msgError('上传返回数据异常');
  }
}

// 移除文件回调
function handleRemove(file, fileList) {
  addForm.name = '';
  addForm.filePath = '';
  uploadFileList.value = [];
}

function resetAddForm() {
  // 重置表单字段
  Object.assign(addForm, {
    code: '',
    name: '',
    fileName: '',
    filePath: '',
    tenantId: ''
  });
  // 清空上传文件列表
  uploadFileList.value = [];
  // 重置表单校验
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
}

// 新增handlePreview方法，支持点击文件名下载
function handlePreview(file) {
  const baseUrl = file.url;
  const fileName = file.name || addForm.name || 'downloaded_file';
  const headers = uploadParams && uploadParams.headers ? uploadParams.headers : {};
  // 拼接params
  const params = new URLSearchParams({
    bucketName: 'watershed',
    fileName: addForm.filePath
  }).toString();
  const urlWithParams = baseUrl + (baseUrl.includes('?') ? '&' : '?') + params;

  fetch(urlWithParams, {
    method: 'POST',
    headers: {
      ...headers
    }
  })
    .then(response => {
      if (!response.ok) throw new Error('文件下载失败');
      return response.blob();
    })
    .then(blob => {
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(link.href);
    })
    .catch(() => {
      proxy.$modal.msgError('文件下载失败');
    });
}

onMounted(() => {
  getList();
});
</script>
  
<style scoped>
.rainfall-flex-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 420px;
  min-height: 320px;
  margin-top: 10px;
}
.rainfall-table-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 6px 0 0 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 16px 8px 16px 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.rainfall-divider {
  width: 2px;
  background: #e5e6eb;
  margin: 0 8px;
  border-radius: 2px;
  height: 100%;
  align-self: stretch;
}
.rainfall-chart-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 0 6px 6px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 16px 16px 16px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.rainfall-echart {
  width: 100%;
  height: 100%;
  min-height: 320px;
}
.apple-glass-tag {
  display: inline-flex;
  align-items: center;
  min-width: 110px;
  padding: 0 22px;
  height: 38px;
  line-height: 36px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 999px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  color: #fff;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.1);
  background: linear-gradient(90deg, #4ade80 0%, #22d3ee 100%);
  user-select: none;
  cursor: default;
  position: relative;
  overflow: hidden;
}
.apple-glass-tag--done {
  background: linear-gradient(90deg, #4ade80 0%, #22d3ee 100%);
  color: #fff;
}
.apple-glass-tag--doing {
  background: linear-gradient(90deg, #60a5fa 0%, #2563eb 100%);
  color: #fff;
}
.apple-glass-tag--pending {
  background: linear-gradient(90deg, #fbbf24 0%, #f59e42 100%);
  color: #fff;
}
.tag-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}
.tag-text {
  display: inline-block;
  vertical-align: middle;
}
.done-animate svg {
  animation: done-bounce 1.8s infinite cubic-bezier(0.4, 2, 0.2, 1);
}
@keyframes done-bounce {
  0%,
  100% {
    transform: scale(1);
  }
  10% {
    transform: scale(1.18);
  }
  20% {
    transform: scale(0.92);
  }
  30% {
    transform: scale(1);
  }
}
.doing-pulse {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 20px;
}
.doing-pulse .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #60a5fa;
  opacity: 0.5;
  animation: dot-pulse 1.5s infinite cubic-bezier(0.4, 0, 0.2, 1);
}
.doing-pulse .dot:nth-child(2) {
  animation-delay: 0.3s;
}
.doing-pulse .dot:nth-child(3) {
  animation-delay: 0.6s;
}
@keyframes dot-pulse {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.3);
  }
}
.pending-animate svg {
  animation: pending-breath 2.2s ease-in-out infinite;
}
@keyframes pending-breath {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}
.tag-icon svg {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}
</style>
  