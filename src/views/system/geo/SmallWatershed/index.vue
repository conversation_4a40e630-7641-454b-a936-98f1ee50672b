<script setup>
import { onMounted, reactive, computed, ref } from "vue";
import MinMap from "@/components/Map/plugins/drawTool";
import { getToken } from "@/utils/auth";
import { deepClone } from "@/utils";
import {
  getSubBasinList,
  getSubBasinInfo,
  saveSubBasin,
  deleteSubBasin,
  getSubBasinWeight,
  saveSubBasinWeight,
} from "@/api/watershed";
import { selectStlyList, selectStaList } from "@/api/watershed/ads";

defineOptions({
  name: "SmallWatershed",
});

const { proxy } = getCurrentInstance();
const uploadRef = ref("");

const watershedList = ref([]);
const openVisible = ref(false);
const openDetail = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const title = ref("");
const lyOptions = ref([]); // 流域树
const refreshTable = ref(true);
const total = ref(0);
const uploadUrl = import.meta.env.VITE_APP_BASE_API + "/hydro/convert/shp-kml/";
const token = getToken();
const detailForm = ref({});

const data = reactive({
  total: 0,
  form: {
    geoType: "people",
  },
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    name: "",
    pcode: "",
  },
  rules: {
    code: [
      { required: true, message: "小流域代码不能为空", trigger: "blur" },
      // {
      //   pattern: /^\d{8}$/,
      //   message: "小流域代码必须为8位数字",
      //   trigger: ["blur", "change"],
      // },
    ],
    name: [
      { required: true, message: "小流域名称不能为空", trigger: "blur" },
      { max: 15, message: "小流域名称不超过15个字符", trigger: "blur" },
    ],
    pcode: [{ required: true, message: "所属流域不能为空", trigger: "blur" }],
    area: [
      {
        pattern: /^\d+(\.\d{1})?$/,
        message: "请输入正确的数字格式，保留1位小数",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const addFlag = ref(true);

const { queryParams, form, rules } = toRefs(data);

/** 查询小流域列表 */
const getList = async () => {
  try {
    loading.value = true;
    const res = await getSubBasinList(queryParams.value);
    if (res.code == 200) {
      watershedList.value = res.rows;
      total.value = res.total;
    }
  } catch (error) {
    console.error("获取小流域列表失败:", error);
  } finally {
    loading.value = false;
  }
};

/** 初始化流域下拉树 */
const getLyTree = () => {
  lyOptions.value = [];
  selectStlyList({}).then((res) => {
    lyOptions.value = formatTreeList(res.data);
  });
};

const formatTreeList = (data) => {
  // 检查是否是数组
  if (!Array.isArray(data)) {
    return null;
  }
  // 对数组中的每个元素进行处理
  return data.map((item) => {
    // 复制 data 属性并移除 children
    const newData = { ...item.data };
    delete newData.children;
    // 递归处理子元素
    if (item.children && item.children.length > 0) {
      newData.children = formatTreeList(item.children);
    }
    return newData;
  });
};

/** 取消按钮 */
const cancel = () => {
  openVisible.value = false;
  uploadRef.value?.clearFiles();
  uploadRef.value?.handleRemove();
  reset();
};

/** 表单重置 */
const reset = (flag) => {
  form.value = {
    geoType: "people",
  };
  proxy.resetForm("watershedRef");
};

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryRef");
  handleQuery();
};

/** 新增按钮操作 */
const handleAdd = () => {
  addFlag.value = true;
  reset(true);
  openVisible.value = true;
  title.value = "添加小流域";
};

/** 查看详情 */
const handleDetail = async (row) => {
  const res = await getSubBasinInfo(row.code);
  if (res.code == 200) {
    detailForm.value = res.data;
    openDetail.value = true;
  }
};

/** 提交表单 */
const submitForm = () => {
  proxy.$refs["watershedRef"].validate((valid) => {
    if (valid) {
      if (
        form.value.geom == null ||
        form.value.geom == "" ||
        JSON.stringify(form.value.geom) == {}
      ) {
        form.value.geom = null;
      }

      saveSubBasin({
        ...form.value,
        pcode: form.value.pcode.toString(),
      }).then((res) => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess(addFlag.value ? "新增成功" : "修改成功");
          openVisible.value = false;
          getList();
        }
      });
    }
  });
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  addFlag.value = false;
  reset(false);
  const res = await getSubBasinInfo(row.code);
  if (res.code == 200) {
    form.value = res.data;
    openVisible.value = true;
    title.value = "修改小流域";
  }
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.name + '"的小流域?')
    .then(async () => {
      const res = await deleteSubBasin(row.code);
      if (res.code == 200) {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      }
    });
};

/** 更新空间数据边界 */
const updateBound = (geojson) => {
  form.value.geom = geojson;
};

/** 处理文件变更 */
const handleChange = (file, fileList) => {
  if (fileList.length > 1) {
    fileList[0] = fileList[1];
    fileList.splice(1, 1);
  }
};

/** 处理上传成功 */
const handleSuccess = (response) => {
  if (response.code == 200) {
    form.value.geom = response.data;
  } else {
    uploadRef.value?.clearFiles();
    proxy.$modal.msgError(response.msg);
  }
};

// 降雨权重相关
const watershedName = ref("");
const rainfallWeightOpen = ref(false);
const rainfallWeightForm = ref({
  code: "",
  name: "",
  weightPOS: [],
});
const totalWeight = computed(() => {
  return rainfallWeightForm.value?.weightPOS
    ?.reduce((sum, station) => {
      return sum + (Number(station.weight) || 0);
    }, 0)
    ?.toFixed(1);
});

const stationOptions = ref([]); // 雨量站选项

const stationLoading = ref(false);
// 存储初始表单数据，用于重置操作
const originalRainfallWeightForm = ref({});

/** 获取测站列表 */
const getStationList = async () => {
  try {
    stationLoading.value = true;
    const res = await selectStaList({
      pageNum: 1,
      pageSize: 1000,
      stationTpyeList: ["PP", "ZQ", "RR"],
    });
    stationOptions.value = res.data.records;
    stationLoading.value = false;
  } catch (error) {
    console.error("获取测站列表失败:", error);
    stationLoading.value = false;
  }
};

/** 打开降雨权重设置 */
const handleRainfallWeight = async (row) => {
  getSubBasinWeight(row.code).then((res) => {
    if (res.code == 200) {
      rainfallWeightForm.value = {
        code: row.code,
        weightPOS: res.data.weightPOS,
      };
      originalRainfallWeightForm.value = deepClone(rainfallWeightForm.value);
      watershedName.value = `${row.name}(${row.code})`;
      rainfallWeightOpen.value = true;
    }
  });

  // 获取测站列表
  getStationList();
};

/** 添加雨量站行 */
const handleAddStation = (row) => {
  rainfallWeightForm.value.weightPOS.push({
    code: row.code,
    stcd: "",
    weight: null,
  });
};

/** 删除雨量站行 */
const handleDeleteStation = (index) => {
  rainfallWeightForm.value.weightPOS.splice(index, 1);
};

/** 取消降雨权重设置 */
const cancelRainfallWeight = () => {
  rainfallWeightOpen.value = false;
  rainfallWeightForm.value = {};
};

/** 提交降雨权重设置 */
const submitRainfallWeight = async () => {
  // 所有测站都要有权重
  const allStations = rainfallWeightForm.value.weightPOS.every(
    (station) =>
      station.stcd && station.weight !== null && station.weight !== undefined
  );
  if (!allStations) {
    proxy.$modal.msgWarning("所有测站都要有权重");
    return;
  }

  // 获取有效的测站（已填写stationId的测站）
  const validStations = rainfallWeightForm.value.weightPOS.filter(
    (station) =>
      station.stcd && station.weight !== null && station.weight !== undefined
  );

  // 计算权重总和
  const totalWeight = validStations.reduce(
    (sum, station) => sum + (Number(station.weight) || 0),
    0
  );

  if (Math.abs(totalWeight - 1) > 0.001) {
    proxy.$modal.msgWarning("权重总和必须为1");
    return;
  }

  await saveSubBasinWeight(rainfallWeightForm.value);

  proxy.$modal.msgSuccess("设置降雨权重成功");
  rainfallWeightOpen.value = false;
  getList();
};

/** 重置降雨权重设置 */
const resetRainfallWeight = () => {
  proxy.$modal
    .confirm("确定要重置所有数据吗?")
    .then(() => {
      // 将表单恢复到初始状态
      rainfallWeightForm.value = deepClone(originalRainfallWeightForm.value);
      proxy.$modal.msgSuccess("重置成功");
    })
    .catch(() => {});
};

/** 计算表格的合计行 */
const getSummaries = () => {
  return ["", "总计", totalWeight.value];
};

// 初始化数据
onMounted(() => {
  getLyTree();
  getList();
});
</script>

<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="小流域名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入小流域名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属流域" prop="pcode">
        <el-tree-select
          v-model="queryParams.pcode"
          :data="lyOptions"
          clearable
          :props="{
            value: 'code',
            label: 'name',
            children: 'children',
          }"
          value-key="code"
          placeholder="选择所属流域"
          check-strictly
        />
      </el-form-item>

      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="Refresh" type="primary" plain @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd"
              >新增</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </div>
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="watershedList"
        row-key="code"
        stripe
      >
        <el-table-column type="index" label="序号" width="65" align="center" />
        <el-table-column
          prop="name"
          label="小流域名称"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column prop="code" label="小流域代码" align="center" />
        <el-table-column
          prop="pname"
          label="所属流域"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          prop="area"
          label="流域面积(km²)"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          label="操作"
          align="center"
          width="320"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Setting"
              @click="handleRainfallWeight(scope.row)"
              >设置降雨权重</el-button
            >
            <el-button
              link
              type="primary"
              icon="View"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改小流域对话框 -->
    <el-dialog
      :title="title"
      v-model="openVisible"
      width="720px"
      append-to-body
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="watershedRef"
        :model="form"
        :rules="rules"
        label-width="140px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="小流域代码" prop="code">
              <el-input
                :disabled="!addFlag"
                v-model="form.code"
                placeholder="请输入小流域代码"
                maxlength="8"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="小流域名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入小流域名称"
                maxlength="15"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择所属流域" prop="pcode">
              <el-tree-select
                class="w-full"
                :disabled="!addFlag"
                v-model="form.pcode"
                :data="lyOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children',
                }"
                value-key="code"
                placeholder="选择所属流域"
                check-strictly
                :render-after-expand="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流域面积(km²)" prop="area">
              <el-input-number
                v-model="form.area"
                placeholder="请输入流域面积"
                :min="0"
                :max="1000000"
                :precision="1"
                :step="0.1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="空间数据" prop="geoType">
              <el-radio-group v-model="form.geoType">
                <el-radio label="zip"
                  >矢量数据(shp-zip,kml,geojson文件)</el-radio
                >
                <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                <el-radio label="people">人工绘制</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <div class="upload-container" v-show="form.geoType === 'zip'">
            <el-upload
              class="upload-demo w-full"
              ref="uploadRef"
              drag
              name="multipartFile"
              :on-change="handleChange"
              :action="uploadUrl"
              :data="{}"
              :headers="{ Authorization: token }"
              :limit="2"
              :on-success="handleSuccess"
            >
              <div class="el-upload__text">拖拽上传 或 <em>点击上传</em></div>
            </el-upload>
          </div>
          <div class="min-map-container">
            <min-map
              @updateBound="updateBound"
              :geom="form.geom"
              :show-tool="form.geoType === 'people'"
              style="width: 100%; height: 100%"
            ></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看小流域详情对话框 -->
    <el-dialog
      title="小流域详情"
      v-model="openDetail"
      width="720px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="小流域名称">{{
          detailForm.name
        }}</el-descriptions-item>
        <el-descriptions-item label="小流域代码">{{
          detailForm.code
        }}</el-descriptions-item>
        <el-descriptions-item label="所属流域">{{
          detailForm.pname
        }}</el-descriptions-item>
        <el-descriptions-item label="流域面积(km²)">{{
          detailForm.area
        }}</el-descriptions-item>
      </el-descriptions>
      <div
        style="width: 100%; height: 400px; background: #555; margin-top: 20px"
      >
        <min-map
          :geom="detailForm.geom"
          :showTool="false"
          style="width: 100%; height: 100%"
        ></min-map>
      </div>
    </el-dialog>

    <!-- 降雨权重设置对话框 -->
    <el-dialog
      title="设置降雨权重"
      v-model="rainfallWeightOpen"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      @close="cancelRainfallWeight"
    >
      <div style="margin-bottom: 20px; font-size: 16px; font-weight: bold">
        {{ watershedName }}
      </div>

      <el-table
        :data="rainfallWeightForm.weightPOS"
        border
        class="w-full"
        :show-summary="true"
        :summary-method="getSummaries"
        v-loading="stationLoading"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column label="测站">
          <template #default="{ row }">
            <el-select
              v-model="row.stcd"
              placeholder="请选择测站"
              clearable
              class="w-full"
              :disabled="!!row.stcd"
            >
              <el-option
                v-for="item in stationOptions"
                :key="item.stcd"
                :label="item.stnm"
                :value="item.stcd"
                :disabled="
                  rainfallWeightForm.weightPOS.some(
                    (station) => station.stcd === item.stcd
                  )
                "
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="权重" prop="weight" width="200">
          <template #default="{ row }">
            <el-input-number
              :id="'weightInput' + row.stcd"
              v-model="row.weight"
              :min="0"
              :max="1"
              :precision="1"
              :step="0.1"
              class="w-full"
              placeholder="请输入权重"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ $index, row }">
            <el-button
              link
              type="primary"
              icon="Plus"
              @click="handleAddStation(row)"
              v-if="$index === rainfallWeightForm.weightPOS.length - 1"
            >
              添加
            </el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDeleteStation($index)"
              v-if="rainfallWeightForm.weightPOS.length > 1"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px">
        注：权重范围0-1，保留1位小数，所有测站权重之和必须为1
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRainfallWeight"
            >确 定</el-button
          >
          <el-button type="warning" @click="resetRainfallWeight"
            >重 置</el-button
          >
          <el-button @click="cancelRainfallWeight">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.upload-container {
  width: 100%;
  height: 130px;
}
.min-map-container {
  width: 100%;
  height: 350px;
  background: #555;
}
</style>
