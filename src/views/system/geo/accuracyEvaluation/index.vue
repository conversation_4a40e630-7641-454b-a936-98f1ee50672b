<template>
    <div class="app-container">
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        class="form-container"
      >
        <el-form-item label="预报方案" prop="forecastSchemeId">
          <el-select
            v-model="queryParams.forecastSchemeId"
            placeholder="请选择预报方案"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="item in forecastSchemeOptions"
              :key="item.id"
              :label="item.schemeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>


        <el-form-item class="form-item-btn">
          <el-button type="primary" icon="Search" @click="handleQuery"
            >查询</el-button
          >
          <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <div class="content content-table">
        <!-- 评价信息提示 -->
        <div class="evaluation-info">
          <el-icon class="info-icon"><InfoFilled /></el-icon>
          <span>参与评价的历史洪水<b>10场</b>，合格率为<b>90%</b></span>
        </div>

        <div class="table-header">
          <el-row :gutter="10">
            <!-- <el-col :span="1.5">
              <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
              <el-button type="success" icon="DocumentAdd" @click="importDialogVisible = true" style="margin-left: 8px;">导入</el-button>
            </el-col> -->
            <right-toolbar
              v-model:showSearch="showSearch"
              @queryTable="getList"
            ></right-toolbar>
          </el-row>
        </div>
        <el-table
          v-if="refreshTable"
          v-loading="loading"
          :data="accuracyList"
          row-key="id"
          stripe
        >
          <el-table-column
            type="index"
            label="序号"
            width="65"
            align="center"
            :index="indexMethod"
          ></el-table-column>
          <el-table-column
            prop="schemeName"
            label="预报方案"
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="floodHistoryCode"
            label="历史洪水编号"
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="basinName"
            label="所属流域"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="flowRelativeError"
            label="洪量相对误差(%)"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="peakRelativeError"
            label="洪峰流量相对误差(%)"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="peakTimeLag"
            label="峰现时延(小时)"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="certaintyCoefficient"
            label="确定性系数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="result"
            label="评价结果"
            align="center"
          >
            <template #default="scope">
              <span
                class="apple-glass-tag"
                :class="{
                  'apple-glass-tag--excellent': scope.row.result === 1,
                  'apple-glass-tag--bad': scope.row.result === 0
                }"
              >
                {{ scope.row.result === 1 ? '合格' : '不合格' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="time"
            label="评价时间"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="120"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="View"
                @click="handleView(scope.row)"
              >查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>

      <!-- 新增弹窗：新增预报任务 -->

      <!-- 新增：查看详情弹窗 -->
      <el-dialog
        :title="viewDialogTitle"
        v-model="viewDialogVisible"
        width="800px"
        append-to-body
      >
        <el-descriptions :column="2" border>
          <el-descriptions-item label="历史洪水编号">{{ viewDetail.floodEventCode }}</el-descriptions-item>
          <el-descriptions-item label="方案名称">{{ viewDetail.forecastSchemeName }}</el-descriptions-item>
          <el-descriptions-item label="测站">{{ viewDetail.stationName }}</el-descriptions-item>
          <el-descriptions-item label="时间范围">{{ viewDetail.timeRange }}</el-descriptions-item>
          <el-descriptions-item label="确定性系数">{{ viewDetail.nse }}</el-descriptions-item>
          <el-descriptions-item label="评价结果">{{ viewDetail.evaluationResult }}</el-descriptions-item>
          <el-descriptions-item label="降雨量">{{ viewDetail.rainfall }}</el-descriptions-item>
          <el-descriptions-item label="实测洪量">{{ viewDetail.measuredVolume }}</el-descriptions-item>
          <el-descriptions-item label="计算洪量">{{ viewDetail.calculatedVolume }}</el-descriptions-item>
          <el-descriptions-item label="洪量相对误差">{{ viewDetail.volumeRelError }}%</el-descriptions-item>
          <el-descriptions-item label="实测洪峰流量">{{ viewDetail.measuredPeakFlow }}</el-descriptions-item>
          <el-descriptions-item label="计算洪峰流量">{{ viewDetail.calculatedPeakFlow }}</el-descriptions-item>
          <el-descriptions-item label="洪峰流量相对误差">{{ viewDetail.peakFlowRelError }}%</el-descriptions-item>
          <el-descriptions-item label="实测峰现时间">{{ viewDetail.measuredPeakTime }}</el-descriptions-item>
          <el-descriptions-item label="计算峰现时间">{{ viewDetail.calculatedPeakTime }}</el-descriptions-item>
          <el-descriptions-item label="峰现时延">{{ viewDetail.peakTimeDeviation }}小时</el-descriptions-item>
        </el-descriptions>
        <div style="margin: 20px 0 0 0;">
          <el-radio-group v-model="detailViewType" size="small" style="margin-bottom: 12px;">
            <el-radio-button label="chart">
              <el-icon style="vertical-align: middle; margin-right: 4px;"><Picture /></el-icon>图
            </el-radio-button>
            <el-radio-button label="table">
              <el-icon style="vertical-align: middle; margin-right: 4px;"><Tickets /></el-icon>表格
            </el-radio-button>
          </el-radio-group>
          <div v-if="detailViewType === 'chart'">
            <div ref="detailChartRef" style="width: 100%; height: 320px;"></div>
          </div>
          <el-table
            v-if="detailViewType === 'table'"
            :data="viewDetailTableData"
            style="width: 100%; margin-top: 12px;"
            height="320"
            border
          >
            <el-table-column type="index" label="序号" width="60" align="center"/>
            <el-table-column prop="time" label="时间" align="center"/>
            <el-table-column prop="rainfall" label="降雨量" align="center"/>
            <el-table-column prop="measuredFlow" label="实测流量" align="center"/>
            <el-table-column prop="calculatedFlow" label="计算流量" align="center"/>
            <el-table-column prop="relError" label="相对误差(%)" align="center"/>
          </el-table>
        </div>
        <template #footer>
          <!-- 仅保留footer插槽，不显示关闭按钮 -->
        </template>
      </el-dialog>
    </div>
  </template>

  <script setup>
  import { onMounted, onUnmounted, nextTick, reactive, ref, watch, computed} from "vue";
  import {
    selectStaList,
    selectStlyList,
  } from "@/api/watershed/ads";
  import * as echarts from 'echarts';
  import { getAccuracyEvaluationList } from '@/api/watershed/forecast/index.js';
  import { getSubBasinList } from '@/api/watershed/index.js';
  import { downloadByMain } from '@/api/warning/index.js';
  import { getForecastSchemeList } from '@/api/scheduling/index'
  import moment from 'moment'
  import { ElMessage } from 'element-plus';
  import { ElTableV2 } from 'element-plus';
  import { Picture, Tickets, InfoFilled } from '@element-plus/icons-vue'
  import { toRefs } from 'vue';

  defineOptions({
    name: "TownshipIndex",
  });

  const { proxy } = getCurrentInstance();
  const accuracyList = ref([])
  const total = ref(0)
  const loading = ref(false);
  const showSearch = ref(true);
  const refreshTable = ref(true);
  const uploadUrl = import.meta.env.VITE_APP_BASE_API + "/hydro/convert/shp-kml/";
  import { getToken } from "@/utils/auth";
  const token = getToken();

  // 分页处理
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
    forecastSchemeId: undefined,
    status: 1,
  });

  const { queryParams: queryParamsForApi, statusOptions } = toRefs(queryParams);

  const subBasinOptions = ref([]);
  const stationOptions = ref([]);

  function indexMethod(index) {
    return (queryParams.pageNum - 1) * queryParams.pageSize + index + 1;
  }

  // 递归处理流域树结构，将data字段提升到节点本身
  function transformBasinTree(tree) {
    return (tree || []).map(node => {
      const { data, children } = node;
      return {
        ...data,
        children: transformBasinTree(children)
      };
    });
  }

  async function initOptions() {
    // 流域
    const basinRes = await selectStlyList({});
    subBasinOptions.value = transformBasinTree(basinRes.data || []);
    // 雨量站
    const stationRes = await selectStaList({ asNext: 1, pageNum: 1, pageSize: 999999, sttp: 'PP' });
    stationOptions.value = (stationRes.data?.records || []).map(item => ({ code: item.stcd, name: `${item.stnm}(${item.stcd})` }));
  }

  onMounted(async () => {
    await loadForecastSchemeOptions();
    getList();
  });

  function handleDelete(row) {
    proxy.$modal.confirm('是否确认删除编号为"' + row.code + '"的历史洪水?')
      .then(() => deleteTypicalRainfall(row.id))
      .then(() => {
        proxy.$modal.msgSuccess('删除成功');
        getList();
      })
      .catch(() => {});
  }


  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.pageNum = 1;
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    queryParams.pageNum = 1;
    getList();
  }
  // 获取精度评价列表
  async function getList() {
    loading.value = true;
    try {
      const params = {
        pageNum: queryParams.pageNum,
        pageSize: queryParams.pageSize,
      };
      if (queryParams.forecastSchemeId) {
        params.schemeId = queryParams.forecastSchemeId;
      }
      const res = await getAccuracyEvaluationList(params);
      if (res && Array.isArray(res.rows)) {
        accuracyList.value = res.rows;
        total.value = res.total || 0;
      } else {
        accuracyList.value = [];
        total.value = 0;
      }
    } catch (e) {
      accuracyList.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  }
  const rainfallWeightForm = ref({
    adcd: "",
    adnm: "",
    stations: [
      {
        stationId: "",
        stationName: "",
        weight: null,
      },
    ],
  });

  // 存储初始表单数据，用于重置操作
  const originalRainfallWeightForm = ref({
    adcd: "",
    adnm: "",
    stations: [
      {
        stationId: "",
        stationName: "",
        weight: null,
      },
    ],
  });

  const rainfallChartRef = ref(null);
  let rainfallChartInstance = null;




  const data = reactive({
    forecastSchemeOptions: [],
  });
  const { forecastSchemeOptions } = toRefs(data);

  async function loadForecastSchemeOptions() {
    try {
      const res = await getForecastSchemeList();
      if (res && Array.isArray(res.rows)) {
        forecastSchemeOptions.value = res.rows;
        // 自动选择最近的方案
        if (res.rows.length > 0) {
          // 找到updateTime距离现在最近的项
          const now = moment();
          let minDiff = Infinity;
          let closestItem = null;
          res.rows.forEach(item => {
            // 兼容各种时间格式
            const itemTime = moment(item.updateTime);
            if (itemTime.isValid()) {
              const diff = Math.abs(itemTime.diff(now));
              if (diff < minDiff) {
                minDiff = diff;
                closestItem = item;
              }
            }
          });
          if (closestItem) {
            queryParams.forecastSchemeId = closestItem.id;
            // 自动请求接口刷新数据
            getList();
          }
        }
      }
    } catch (e) {
      proxy.$message.error('获取预报方案失败');
    }
  }

  // 查看详情弹窗相关
  const viewDialogVisible = ref(false)
  // 恢复viewDetail的mock初始化数据
  const viewDetail = reactive({
    floodEventCode: '202106洪水',
    forecastSchemeName: '方案A',
    stationName: '测站X',
    timeRange: '2024-06-01 10:00 ~ 2024-06-03 10:00',
    nse: 0.92,
    evaluationResult: '优秀',
    rainfall: 120.5,
    measuredVolume: 3000,
    calculatedVolume: 3100,
    volumeRelError: 3.3,
    measuredPeakFlow: 500,
    calculatedPeakFlow: 520,
    peakFlowRelError: 4.0,
    measuredPeakTime: '2024-06-02 08:00',
    calculatedPeakTime: '2024-06-02 08:30',
    peakTimeDeviation: 0.5
  })
  // 新增currentRow变量
  const currentRow = ref(null)
  // 修改viewDialogTitle为currentRow.schemeName+currentRow.floodHistoryCode，无分隔符，若currentRow为空则回退为默认标题
  const viewDialogTitle = computed(() => {
    if (currentRow.value && (currentRow.value.schemeName || currentRow.value.floodHistoryCode)) {
      return (currentRow.value.schemeName || '') + (currentRow.value.floodHistoryCode || '')
    }
    return '详情'
  })
  // mock 详情表格/图数据
  const viewDetailTableData = ref([
    { time: '2024-06-01 10:00', rainfall: 10, measuredFlow: 100, calculatedFlow: 110, relError: 10 },
    { time: '2024-06-01 11:00', rainfall: 15, measuredFlow: 120, calculatedFlow: 125, relError: 4.2 },
    { time: '2024-06-01 12:00', rainfall: 8, measuredFlow: 130, calculatedFlow: 128, relError: 1.5 },
    { time: '2024-06-01 13:00', rainfall: 0, measuredFlow: 140, calculatedFlow: 138, relError: 1.4 },
    { time: '2024-06-01 14:00', rainfall: 5, measuredFlow: 150, calculatedFlow: 155, relError: 3.3 }
  ])
  const detailViewType = ref('chart') // chart or table
  const detailChartRef = ref(null)
  let detailChartInstance = null

  function renderDetailChart() {
    if (!detailChartRef.value) return
    if (!detailChartInstance) {
      detailChartInstance = echarts.init(detailChartRef.value)
    }
    const xData = viewDetailTableData.value.map(item => item.time)
    const rainfallData = viewDetailTableData.value.map(item => item.rainfall)
    const measuredFlowData = viewDetailTableData.value.map(item => item.measuredFlow)
    const calculatedFlowData = viewDetailTableData.value.map(item => item.calculatedFlow)
    detailChartInstance.setOption({
      tooltip: { trigger: 'axis' },
      legend: { data: ['降雨量', '实测流量', '计算流量'] },
      grid: { left: 50, right: 50, top: 30, bottom: 40 },
      xAxis: [
        { type: 'category', data: xData, axisLabel: { rotate: 0, fontSize: 12, margin: 16, interval: 'auto' }, name: '时间' }
      ],
      yAxis: [
        { type: 'value', name: '降雨量(mm)', min: 0 },
        { type: 'value', name: '流量(m³/s)', min: 0, position: 'right' }
      ],
      series: [
        { name: '降雨量', type: 'bar', data: rainfallData, yAxisIndex: 0, itemStyle: { color: '#409EFF' } },
        { name: '实测流量', type: 'line', data: measuredFlowData, yAxisIndex: 1, smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { width: 2 }, itemStyle: { color: '#67C23A' } },
        { name: '计算流量', type: 'line', data: calculatedFlowData, yAxisIndex: 1, smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { width: 2 }, itemStyle: { color: '#E6A23C' } }
      ]
    })
    detailChartInstance.resize()
  }

  watch(detailViewType, (val) => {
    if (val === 'chart') {
      nextTick(() => {
        if (detailChartInstance) {
          detailChartInstance.dispose()
          detailChartInstance = null
        }
        renderDetailChart()
      })
    }
  })
  watch(viewDialogVisible, (val) => {
    if (val && detailViewType.value === 'chart') {
      nextTick(() => {
        renderDetailChart()
      })
    }
  })
  onMounted(() => {
    if (detailViewType.value === 'chart') {
      nextTick(() => {
        renderDetailChart()
      })
    }
    window.addEventListener('resize', handleDetailChartResize)
  })
  onUnmounted(() => {
    if (detailChartInstance) {
      detailChartInstance.dispose()
      detailChartInstance = null
    }
    window.removeEventListener('resize', handleDetailChartResize)
  })
  function handleDetailChartResize() {
    if (detailChartInstance) detailChartInstance.resize()
  }

  // 查看按钮逻辑
  // handleView(row)只赋值currentRow，不再修改viewDetail
  function handleView(row) {
    currentRow.value = row
    viewDialogVisible.value = true
  }

  function tagBounce(e) {
    const el = e.currentTarget;
    el.classList.remove('bounce');
    void el.offsetWidth;
    el.classList.add('bounce');
  }

  const treeData = ref([])
  const floodHistoryOptions = ref([])

  // 递归适配流域树结构
  function transformBasinTreeNode(node) {
    return {
      ...node.data,
      children: (node.children || []).map(transformBasinTreeNode)
    }
  }

  const reEvaluateLoading = ref(false)
  function handleReEvaluate(row) {
    if (reEvaluateLoading.value) return
    reEvaluateLoading.value = true
    reEvaluateFloodHistory(row.code)
      .then(() => {
        ElMessage.success('重新评价成功')
        getList()
      })
      .catch(err => {
        ElMessage.error('重新评价失败：' + (err?.msg || '未知错误'))
      })
      .finally(() => {
        reEvaluateLoading.value = false
      })
  }
  </script>

  <style lang="scss" scoped>
  .evaluation-info {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff9f0;
  border-left: 2px solid #ff8503;
  border-radius: 4px;
}

.evaluation-info .info-icon {
  color: #ff8503;
  font-size: 18px;
  margin-right: 8px;
}

.evaluation-info span {
  color: #333;
  font-size: 14px;
  font-weight: 500;

  b {
    color: #ff8503;
    font-weight: 600;
    margin: 0 2px;
  }
}

.rainfall-flex-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 420px;
  min-height: 320px;
  margin-top: 10px;
  }
  .rainfall-table-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 6px 0 0 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 16px 8px 16px 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  }
  .rainfall-divider {
  width: 2px;
  background: #e5e6eb;
  margin: 0 8px;
  border-radius: 2px;
  height: 100%;
  align-self: stretch;
  }
  .rainfall-chart-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 0 6px 6px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 16px 16px 16px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  }
  .rainfall-echart {
  width: 100%;
  height: 100%;
  min-height: 320px;
  }
  .apple-glass-tag {
    display: inline-block;
    min-width: 64px;
    padding: 0 22px;
    height: 32px;
    line-height: 30px;
    font-size: 15px;
    font-weight: 600;
    border-radius: 999px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
    color: #fff;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10);
    user-select: none;
    cursor: default;
    position: relative;
    overflow: hidden;
    transition: box-shadow 0.3s;
    animation: apple-breath 2.4s infinite cubic-bezier(.4,0,.2,1);
  }
  .apple-glass-tag--excellent {
    background: linear-gradient(90deg, #4ADE80 0%, #22D3EE 100%);
    box-shadow: 0 0 16px 0 #4ADE8044;
  }
  .apple-glass-tag--good {
    background: linear-gradient(90deg, #60A5FA 0%, #2563EB 100%);
    box-shadow: 0 0 16px 0 #60A5FA44;
  }
  .apple-glass-tag--normal {
    background: linear-gradient(90deg, #FBBF24 0%, #F59E42 100%);
    box-shadow: 0 0 16px 0 #FBBF2444;
  }
  .apple-glass-tag--bad {
    background: linear-gradient(90deg, #F87171 0%, #F43F5E 100%);
    box-shadow: 0 0 16px 0 #F8717144;
  }
  @keyframes apple-breath {
    0%, 100% { box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10); opacity: 1; }
    50% { box-shadow: 0 8px 32px 0 rgba(0,0,0,0.18); opacity: 0.92; }
  }
  </style>
