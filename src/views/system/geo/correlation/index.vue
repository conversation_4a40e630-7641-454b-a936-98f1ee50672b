<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="form-container">
      <el-form-item label="水库名称" prop="resName">
        <el-input v-model="queryParams.resName" placeholder="请输入水库名称" clearable
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="注册登记号" prop="registerCode">
        <el-input v-model="queryParams.registerCode" placeholder="请输入注册登记号" clearable
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="行政区" prop="adcd">
        <el-tree-select v-model="queryParams.adcd" :data="adcdOptions"
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="adcd" placeholder="选择所属区县"
          check-strictly />
      </el-form-item>
      <el-form-item label="工程规模" prop="engScal">
        <el-select v-model="queryParams.engScal" placeholder="请选择工程规模" clearable>
          <el-option v-for="item in engScalOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>

      </el-form-item>

      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd">新增河流</el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="info" plain icon="Upload" @click="handleImport">导入</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </div>
      <el-table v-if="refreshTable" v-loading="loading" :data="lyList" row-key="lycode" stripe>
        <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
        <el-table-column prop="resName" label="水库名称" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="registerCode" label="注册登记号" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="countryName" label="区县" align="center"></el-table-column>
        <el-table-column prop="townName" label="乡镇" :show-overflow-tooltip="true" align="center"></el-table-column>
        <el-table-column prop="engScal" label="工程规模" :show-overflow-tooltip="true">
          <template #default="scope">
            <span>{{ parseKjlx(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="stationRelVos" label="关联测站" :show-overflow-tooltip="true" align="center">
          <template #default="scope">
            <span>{{ showStnm(scope.row.stationRelVos) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="stationRelVos" label="测站编码" :show-overflow-tooltip="true" align="center">
          <template #default="scope">
            <span>{{ showStcd(scope.row.stationRelVos) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="stationRelVos" label="监测项目" :show-overflow-tooltip="true" align="center">
          <template #default="scope">
            <span>{{ showrvBasArea(scope.row.stationRelVos) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="210" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <!-- <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
        @pagination="getList" />
    </div>
    <!-- 添加或修改流域对话框 -->
    <el-dialog v-model="open" width="900px" append-to-body :close-on-click-modal="false">
      <template #header>
        <el-row>
          <el-col :span="12">
            <span style="font-size: 18px;">水库名称:{{ titleName.resName }}</span>
          </el-col>
          <el-col :span="12">
            <span style="font-size: 18px;">注册登记号:{{ titleName.registerCode }}</span>
          </el-col>
        </el-row>
      </template>
      <el-button style="margin-bottom: 10px;" plain icon="Plus" type="primary" @click="addRelation">新增</el-button>

      <el-form ref="menuRef" :model="form" label-width="100px">
        <el-table :data="relationList" border>
          <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
          <el-table-column prop="stcd" label="测站">
            <template #default="scope">
              <el-select v-model="scope.row.stcd" filterable>
                <el-option v-for="item in staOptions" :key="item.stcd" :label="item.stnm" :value="item.stcd" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="monitorItems" label="监测项目">
            <template #default="scope">
              <el-select v-model="scope.row.monitorItems">
                <el-option v-for="item in monitorItemsOptions" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="dataTime" label="最近数据采集时间" :show-overflow-tooltip="true">
            <template #default="scope">
              {{ scope.row.dataTime || '无数据' }}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button link type="primary" @click="handleTest(scope.row)">数据测试</el-button>
              <el-button link type="danger" @click="handleDel(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="curTitle" v-model="open2" width="920px" height="900px" append-to-body>
      <el-form ref="menuRef" label-width="130px" height="900px">
        <el-row>
          <div style="width: 100%;background: #555;height: 600px;">
            <min-map :geom="curGeom" :showTool="false" style="width: 100%;height:100%"></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open2 = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
        :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
        :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">保 存</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  relationStcd,
  delRvInfo,
  getAdcdTree,
  getCorInfo, selectCorList,
  selectStaList,
  testCorInfo
} from "@/api/watershed/ads";
import MinMap from "@/components/Map/plugins/drawTool";
import { onUnmounted } from "vue";
import { getToken } from '@/utils/auth'

defineOptions({
  name: 'CorrelationIndex'
})

const total = ref(0)
const { proxy } = getCurrentInstance();

const lyList = ref([]);
const open = ref(false);
const open2 = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const curGeom = ref("");
const curTitle = ref("");
const adcdOptions = ref([]); // 行政区树 默认获取一下
const refreshTable = ref(true);
const relationList = ref([]);
const staOptions = ref([]);
const uploadRef = ref('')
/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,

  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/system/reservoir/reservoirStationExcel"
});
const engScalOptions = [
  { label: '大(1)型', value: '1' },
  { label: '大(2)型', value: '2' },
  { label: '中型', value: '3' },
  { label: '小(1)型', value: '4' },
  { label: '小(2)型', value: '5' },
  { label: '其他', value: '6' },
]
const monitorItemsOptions = [
  { label: '库区降雨', value: 1 },
  { label: '上游降雨', value: 2 },
  { label: '库上水位', value: 3 },
  { label: '库下水位', value: 4 },
  { label: '入库流量', value: 5 },
  { label: '出库流量', value: 6 },
]

const data = reactive({
  form: {
    geoType: 'people'
  },
  queryParams: {
    pageNum: 1,
    pageSize: 1000,
    asNext: 1,
    resName: '',
    adcd: '',
    engScal: '',
  },
  rules: {
    rvName: [{ required: true, message: "流域名称不能为空", trigger: "blur" }],
    lycode: [{ required: true, message: "流域代码不能为空", trigger: "blur" }],
    orderNum: [{ required: true, message: "流域顺序不能为空", trigger: "blur" }],
    path: [{ required: true, message: "路由地址不能为空", trigger: "blur" }]
  },
  titleName: {
    resName: '',
    registerCode: '',
    resCode: ''
  }
});

const { queryParams, form, rules, titleName } = toRefs(data);

/** 查询流域列表 */
async function getList() {
  loading.value = true;

  selectCorList(queryParams.value).then(res => {
    lyList.value = res.data.records
    total.value = res.data.total
    loading.value = false;
  });
}
getTreeselect()
/** 查询区县下拉树结构 */
function getTreeselect() {
  adcdOptions.value = [];
  getAdcdTree().then(res => {
    adcdOptions.value = res.data[0].children
  })

  selectStaList({
    pageNum: 1,
    pageSize: 1000,
  }).then(res => {
    staOptions.value = res.data.records
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  relationList.value = []
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function showStnm(arr) {
  if (arr && arr.length > 0) {
    let str = ''
    arr.forEach(e => {
      str += e.stnm + ','
    })
    return str.slice(0, str.length - 1);
  } else {
    return ''
  }
}
function showStcd(arr) {
  if (arr && arr.length > 0) {
    let str = ''
    arr.forEach(e => {
      str += e.stcd + ','
    })
    return str.slice(0, str.length - 1);
  } else {
    return ''
  }
}
function showrvBasArea(arr) {
  if (arr && arr.length > 0) {
    let str = ''
    arr.forEach(e => {
      str += getMon(e.monitorItems) + ','
    })
    return str.slice(0, str.length - 1);
  } else {
    return ''
  }
}
function getMon(v) {
  for (let i = 0; i < monitorItemsOptions.length; i++) {
    if (monitorItemsOptions[i].value == v) {
      return monitorItemsOptions[i].label
    }
  }
}
function addRelation() {
  if (relationList.value.length > 0) {
    if (relationList.value[relationList.value.length - 1].stcd && relationList.value[relationList.value.length - 1].monitorItems) {
      relationList.value.push({
        stcd: null,
        monitorItems: null,
        dataTime: '',
      })
    } else {
      proxy.$modal.msgError("测站或监测项目不可为空！");

    }
  } else {
    relationList.value.push({
      stcd: null,
      monitorItems: null,
      dataTime: '',
    })
  }
}
function handleTest(row) {
  if (!row.stcd && !row.monitorItems) {
    proxy.$modal.msgError("测站或监测项目不可为空！");
    return
  }
  testCorInfo({
    stcd: row.stcd,
    monitorItems: row.monitorItems,
  }).then(res => {
    if (res.msg) {
      // row.dataTime = "2024-05-02 08:00:00"
      row.dataTime = res.msg
    }
  })
}
function handleDel(index) {
  relationList.value.splice(index, 1)
}


/** 修改按钮操作 */
async function handleUpdate(row) {
  titleName.value.resName = row.resName
  titleName.value.registerCode = row.registerCode
  titleName.value.resCode = row.resCode
  reset();

  let res = await getCorInfo(row.registerCode)
  open.value = true;
  if (res.data.stationRelVos && res.data.stationRelVos.length > 0) {
    res.data.stationRelVos.forEach(el => {
      relationList.value.push(el)
    })
  }
}

function parseKjlx(data) {
  for (let i = 0; i < engScalOptions.length; i++) {
    if (engScalOptions[i].value == data.engScal) {
      return engScalOptions[i].label
    }
  }
}


/** 提交按钮 */
function submitForm() {
  // proxy.$refs["menuRef"].validate(valid => {
  //   if (valid) {
  //     // form.value.geom = JSON.stringify(geom)
  //     if (form.value.addFlag) {
  //       addRvInfo(form.value).then(response => {
  //         proxy.$modal.msgSuccess("新增成功");
  //         open.value = false;
  //         getList();
  //       });
  //     } else {
  //       updateRvInfo(form.value).then(response => {
  //         proxy.$modal.msgSuccess("修改成功");
  //         open.value = false;
  //         getList();
  //       });
  //     }
  //   }
  // });


  const bol = relationList.value.some((item, index, array) => array.slice(index + 1).find(other => item.monitorItems == other.monitorItems && item.stcd == other.stcd));
  if (bol) {
    proxy.$modal.msgError("测站和监测项目不可重复添加");
  } else if (!relationList.value[relationList.value.length - 1].stcd || !relationList.value[relationList.value.length - 1].monitorItems) {
    proxy.$modal.msgError("测站或监测项目不可为空！");
  } else {
    const arr = [];
    relationList.value.forEach(el => {
      arr.push({
        stcd: el.stcd,
        monitorItems: el.monitorItems,
        registerCode: titleName.value.registerCode,
      })
    })
    relationStcd(arr).then(res => {
      if (res.code == 200) {
        open.value = false;
        getList();
      }
    })

  }
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.rvName + '"的数据项?').then(function () {
    return delRvInfo({ id: row.id, lyCode: row.lyCode });
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}
function updateBound(geojson) {
  // 提交geojson数据到后台
  console.log(geojson)
  form.value.geom = geojson
}

function handleSuccess(
  response
) {
  if (response.code == 200) {
    form.value.geom = response.data
  } else {
    uploadRef.value?.clearFiles()
    proxy.$modal.msgSuccess(response.msg);
  }
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "水库关联测站导入";
  upload.open = true;
};
/** 下载模板操作 */
function importTemplate() {
  proxy.download("file/download", {
    fileName: '2b4abe96-e21b-4bf9-882b-9a1a78dd5473_水库与测站关联关系导入.xlsx',
    bucketName: 'excel-template'
  }, `水库关联测站导入模板.xlsx`);
};
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
};
/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
};

onMounted(() => {
  window.EventBus.$on('updateBound/update', updateBound)

})
onUnmounted(() => {

})

getList();
</script>
