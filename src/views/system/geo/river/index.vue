<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="河流名称" prop="rvName">
        <el-input
          v-model="queryParams.rvName"
          placeholder="请输入河流名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd"
              >新增</el-button
            >
          </el-col>

          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="lyList" stripe>
        <!-- <el-table-column type="index" label="序号" width="65" align="center"></el-table-column> -->
        <el-table-column
          type="index"
          label="序号"
          width="65"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="basinName"
          label="所属流域"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="rvName"
          label="河流名称"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="rvLen"
          label="河流长度(km)"
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="centuryFloodLevel"
          label="百年一遇洪水水位(m)"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="centuryFloodDischarge"
          label="百年一遇洪水流量(m³/s)"
          align="center"
        ></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="210"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="showMap(scope.row)"
              >查看</el-button
            >
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <Pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 添加或修改流域对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="720px"
      append-to-body
      @close="cancel"
    >
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="180px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属流域" prop="basinId">
              <el-tree-select
                :disabled="!form.addFlag"
                v-model="form.basinId"
                :data="lyOptions"
                :props="{
                  value: 'basinId',
                  label: 'name',
                  children: 'children',
                }"
                value-key="basinId"
                placeholder="选择流域"
                check-strictly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级河流" prop="parentId">
              <!-- <el-select v-model="form.parentId" placeholder="上级河流" clearable style="width: 200px">
                <el-option v-for="dict in crOverTypeSelects" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select> -->
              <el-tree-select
                v-model="form.parentId"
                :data="lyList"
                :props="{
                  value: 'id',
                  label: 'rvName',
                  children: 'children',
                }"
                value-key="rvCode"
                :render-after-expand="false"
                placeholder="选择上级河流"
                check-strictly
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河流名称" prop="rvName">
              <el-input v-model="form.rvName" placeholder="请输入流域名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河流代码" prop="rvCode">
              <el-input
                v-model="form.rvCode"
                placeholder="请输入河流代码"

              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="百年一遇洪水流量(m³/s)"
              prop="centuryFloodDischarge"
            >
              <el-input
                v-model="form.centuryFloodDischarge"
                placeholder="请输入百年一遇洪水流量"
                :readonly="!form.addFlag"
                :formatter="(value) => `${value}`.replace(/[^\d.]/g, '')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="百年一遇洪水水位(m)" prop="centuryFloodLevel">
              <el-input
                v-model="form.centuryFloodLevel"
                placeholder="请输入百年一遇洪水水位"
                :readonly="!form.addFlag"
                :formatter="(value) => `${value}`.replace(/[^\d.]/g, '')"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="河流类型" prop="rvType">
              <el-select
                v-model="form.rvType"
                placeholder="河流类型"
                clearable
                style="width: 200px"
              >
                <el-option label="自然流域" value="1" />
                <el-option label="区间流域" value="2" />
                <el-option label="平原水网区" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河流级别" prop="rvGrad">
              <el-select
                v-model="form.rvGrad"
                placeholder="河流级别"
                clearable
                style="width: 200px"
              >
                <el-option label="干流" value="0" />
                <el-option label="一级支流" value="1" />
                <el-option label="二级支流" value="2" />
                <el-option label="三级支流" value="3" />
                <el-option label="四级支流" value="4" />
                <el-option label="五级支流" value="5" />
                <el-option label="六级支流" value="6" />
                <el-option label="七级支流" value="7" />
                <el-option label="八级支流" value="8" />
                <el-option label="未定级别" value="99" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河源所在位置" prop="rvSourLoc">
              <el-input
                v-model="form.rvSourLoc"
                placeholder="请输入河源所在位置"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河口所在位置" prop="rvMouLoc">
              <el-input
                v-model="form.rvMouLoc"
                placeholder="请输入河口所在位置"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="跨界类型" prop="crOverType">
              <el-select
                v-model="form.crOverType"
                placeholder="跨界类型"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="dict in crOverTypeSelects"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流经行政区" prop="flowArea1">
              <el-tree-select
                v-model="form.flowArea1"
                :data="adcdOptions"
                clearable
                multiple
                :render-after-expand="false"
                :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
                value-key="adcd"
                placeholder="请选择行政区"
                check-strictly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河流长度(km)" prop="rvLen">
              <el-input v-model="form.rvLen" placeholder="请输入河流长度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河流流经面积(km²)" prop="rvBasArea">
              <el-input
                v-model="form.rvBasArea"
                placeholder="请输入河流流经面积"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="平均比降(%)" prop="averSlop">
              <el-input v-model="form.averSlop" placeholder="请输入平均比降" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岸别" prop="bank">
              <el-select
                v-model="form.bank"
                placeholder="岸别"
                clearable
                style="width: 200px"
              >
                <el-option label="不分" value="0" />
                <el-option label="左" value="1" />
                <el-option label="右" value="2" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="空间数据" prop="geoType">
              <el-radio-group v-model="form.geoType">
                <el-radio label="zip"
                  >矢量数据(shp-zip,kml,geojson文件)</el-radio
                >
                <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                <el-radio label="people">人工绘制</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <div
            style="width: 100%; height: 130px"
            v-show="form.geoType === 'zip'"
          >
            <el-upload
              class="upload-demo"
              style="width: 100%"
              ref="uploadRef"
              drag
              name="multipartFile"
              :on-change="handleChange"
              :action="uploadUrl"
              :data="{}"
              :headers="{ Authorization: token }"
              :limit="2"
              :on-success="handleSuccess"
            >
              <div class="el-upload__text">拖拽上传 或 <em>点击上传</em></div>
            </el-upload>
          </div>
          <div style="width: 100%; height: 350px; background: #555">
            <min-map
              @updateBound="updateBound"
              :geoType="geoType"
              :geom="form.geom"
              :show-tool="form.geoType === 'people'"
              style="width: 100%; height: 100%"
            ></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      :title="curTitle"
      v-model="open2"
      width="920px"
      height="900px"
      append-to-body
    >
      <el-form ref="menuRef" label-width="130px" height="900px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="河流名称:">
              <span>{{ detailData.rvName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河流代码:">
              <span>{{ detailData.rvCode }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="百年一遇洪水流量:">
              <span>{{ detailData.centuryFloodDischarge }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="百年一遇洪水水位:">
              <span>{{ detailData.centuryFloodLevel }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属流域:">
              <span>{{ detailData.basinName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级河流:">
              <!-- <span>{{ detailData.rvName }}</span> -->
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="河源所在位置:">
              <span>{{ detailData.rvSourLoc }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河口所在位置:">
              <span>{{ detailData.rvMouLoc }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="跨界类型:">
              <span>{{ parseKjlx(detailData) }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流经行政区划:">
              <span>{{ parseLyAndAdcds(detailData) }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河流长度:">
              <span>{{ detailData.rvLen }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河流面积:">
              <span>{{ detailData.rvBasArea }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="平均比降:">
              <span>{{ detailData.averSlop }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岸别:">
              <span v-if="detailData.bank == 0">不分</span>
              <span v-if="detailData.bank == 1">左</span>
              <span v-if="detailData.bank == 2">右</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <div style="width: 100%; background: #555; height: 400px">
            <min-map
              :geom="curGeom"
              :showTool="false"
              style="width: 100%; height: 100%"
            ></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open2 = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  selectRvListPage,
  addRvInfo,
  getRvInfo,
  updateRvInfo,
  delRvInfo,
  selectStlyList,
  getAdcdTree,
} from "@/api/watershed/ads";
import MinMap from "@/components/Map/plugins/drawTool";
import { onUnmounted, nextTick } from "vue";

defineOptions({
  name: "RiverIndex",
});

const { proxy } = getCurrentInstance();

const lyList = ref([]);
const open = ref(false);
const open2 = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const geoType = ref("polyline");
const curGeom = ref("");
const curTitle = ref("");
const detailData = ref("");
const lyOptions = ref([]);
const adcdOptions = ref([]);
const uploadRef = ref("");
const total = ref(0);
const uploadUrl = import.meta.env.VITE_APP_BASE_API + "/hydro/convert/shp-kml/";
import { getToken } from "@/utils/auth";
const token = getToken();
// 跨界类型
const crOverTypeSelects = [
  {
    label: "未知",
    value: 0,
  },
  {
    label: "跨国并跨省",
    value: 1,
  },
  {
    label: "跨国",
    value: 2,
  },
  {
    label: "跨省",
    value: 3,
  },
  {
    label: "跨市",
    value: 4,
  },
  {
    label: "跨县",
    value: 5,
  },
  {
    label: "县界内",
    value: 6,
  },
];

const data = reactive({
  form: {
    geoType: "people",
  },
  queryParams: {
    rvName: "",
    pageNum: 1,
    pageSize: 20,
    asNext: 1, // 1 关联查询 ， 0 不关联就查自己的
  },
  rules: {
    basinId: [{ required: true, message: "所属流域不能为空", trigger: "blur" }],
    rvName: [{ required: true, message: "河流名称不能为空", trigger: "blur" }],
    rvCode: [{ required: true, message: "河流代码不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询流域列表 */
async function getList() {
  loading.value = true;

  selectRvListPage(queryParams.value).then((res) => {
    lyList.value = res.rows;

    total.value = res.total;
    loading.value = false;
  });
}
getTreeselect();
/** 查询流域下拉树结构 */
function getTreeselect() {
  lyOptions.value = [];
  selectStlyList({}).then((res) => {
    lyOptions.value = formatTreeList(res.data);
  });
  adcdOptions.value = [];
  getAdcdTree().then((res) => {
    adcdOptions.value = res.data[0].children;
  });
}

function formatTreeList(data) {
  // 检查是否是数组
  if (!Array.isArray(data)) {
    return null;
  }
  // 对数组中的每个元素进行处理
  return data.map((item) => {
    // 复制 data 属性并移除 children
    const newData = { ...item.data };
    delete newData.children;
    // 递归处理子元素
    if (item.children && item.children.length > 0) {
      newData.children = formatTreeList(item.children);
    }
    return newData;
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  uploadRef.value?.clearFiles();
  uploadRef.value?.handleRemove();
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    addFlag: flag,
    geoType: "people",
  };
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
  reset(true);
  open.value = true;
  title.value = "添加河流";
}

function parseLyAndAdcds(data) {
  let ls = data.riverAdminList || [];
  let str = "";
  ls.forEach((l) => {
    str += l.adnm + ",";
  });
  return str.substring(0, str.length - 1);
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  open.value = true;
  title.value = "修改河流";
  await nextTick();
  let obj = Object.assign({ geoType: "people" }, row);
  delete obj.children;
  delete obj.createBy;
  delete obj.createTime;
  delete obj.updateBy;
  delete obj.updateTime;
  delete obj.remark;
  delete obj.asNext;
  delete obj.pageNum;
  delete obj.pageSize;
  form.value = obj;
  let rg = await getRvInfo(obj.id);
  form.value.geom = rg.data.geom || "";
  if (rg.data.riverAdminList && rg.data.riverAdminList.length > 0) {
    form.value.flowArea1 = [];
    rg.data.riverAdminList.forEach((el) => {
      form.value.flowArea1.push(el.adcd);
    });
  }
}

function parseKjlx(data) {
  for (let i = 0; i < crOverTypeSelects.length; i++) {
    if (Number(crOverTypeSelects[i].value) == data.crOverType) {
      return crOverTypeSelects[i].label;
    }
  }
}

function hideMap() {
  open2.value = false;
}
async function showMap(row) {
  let res = await getRvInfo(row.id);
  if (res.data) {
    open2.value = true;
    detailData.value = res.data;
    await nextTick();
    curGeom.value = res.data?.geom;
    curTitle.value = row.rvName;
  } else {
    proxy.$modal.msgSuccess("没找到空间数据");
  }
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate((valid) => {
    if (valid) {
      if (
        form.value.geom == null ||
        form.value.geom == "" ||
        form.value.geom == {}
      ) {
        form.value.geom = null;
      }
      form.value.riverAdminList = [];
      if (form.value.flowArea1 && form.value.flowArea1.length > 0) {
        form.value.flowArea1.forEach((el) => {
          form.value.riverAdminList.push({
            adcd: el,
          });
        });
      }
      // form.value.geom = JSON.stringify(geom)
      if (form.value.addFlag) {
        addRvInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      } else {
        updateRvInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.rvName + '"的数据项?')
    .then(function () {
      return delRvInfo({ id: row.id });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
function updateBound(geojson) {
  // 提交geojson数据到后台
  console.log(geojson);
  form.value.geom = geojson;
}
function handleChange(file, fileList) {
  if (fileList.length > 1) {
    fileList[0] = fileList[1];
    fileList.splice(1, 1);
  }
}
function handleRemove(file) {
  form.value.geom = {};
}
function handleSuccess(response) {
  if (response.code == 200) {
    form.value.geom = response.data;
  } else {
    uploadRef.value?.clearFiles();
    proxy.$modal.msgError(response.msg);
  }
}

onMounted(() => {
  window.EventBus.$on("updateBound/update", updateBound);
});
onUnmounted(() => {});

getList();
</script>
