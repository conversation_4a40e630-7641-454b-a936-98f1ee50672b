<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { formatRiverTreeData, getTypeLabel } from "@/utils";
import {
  selectRvList,
  fetchSluiceGateList,
  fetchSluiceGateInfo,
  saveSluiceGate,
  deleteSluiceGate,
} from "@/api/watershed/ads";
import { getAdcdTree } from "@/api/watershed/ads";
import { listDept } from "@/api/system/dept";
import MinMap from "@/components/Map/plugins/drawTool";
import {
  SLUICE_TYPE_OPTIONS,
  ENGINEERING_LEVEL_OPTIONS,
  ENGINEERING_SCALE_OPTIONS,
  BUILDING_LEVEL_OPTIONS,
  CONSTRUCTION_STATUS_OPTIONS,
} from "@/views/system/geo/utils/enum";
import rules from "./utils/rules";

defineOptions({
  name: "SluiceGate",
});

const { proxy } = getCurrentInstance();

// 水闸查询参数
const queryParams = ref({
  gateName: "",
});

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 水闸详情对话框
const detailDialog = ref(false);
const currentDetail = ref({});

// 水闸新增/编辑对话框
const formDialog = ref(false);
const formTitle = ref("");
const formType = ref("add"); // add-新增 edit-编辑

// 表单数据
const formData = ref({
  gateName: "", // 水闸名称
  gateCode: "", // 水闸代码
  district: "", // 所在区县
  river: "", // 所在河流
  longitude: "", // 经度
  latitude: "", // 纬度
  location: "", // 所在位置
  gateType: "", // 水闸类型
  gatePurpose: "", // 水闸用途
  projectGrade: "", // 工程等别
  projectScale: "", // 工程规模
  buildingLevel: "", // 主要建筑物级别
  designFlow: "", // 设计过闸流量
  gateHoleCount: "", // 闸孔数量
  totalNetWidth: "", // 闸孔总净宽
  constructionStatus: "", // 工程建设情况
  completionDate: "", // 建成时间
  managementDept: "", // 所属管理单位
  remarks: "", // 备注
});

// 表单引用
const formRef = ref(null);

// 选择项数据
const dictData = {
  // 水闸类型选项
  gateTypeOptions: SLUICE_TYPE_OPTIONS,
  // 工程等别选项
  projectGradeOptions: ENGINEERING_LEVEL_OPTIONS,
  // 工程规模选项
  projectScaleOptions: ENGINEERING_SCALE_OPTIONS,
  // 主要建筑物级别选项
  buildingLevelOptions: BUILDING_LEVEL_OPTIONS,
  // 工程建设情况选项
  constructionStatusOptions: CONSTRUCTION_STATUS_OPTIONS,
};

// 获取列表数据
const getList = () => {
  loading.value = true;
  fetchSluiceGateList({
    pageNum: pagination.value.pageNum,
    pageSize: pagination.value.pageSize,
    name: queryParams.value.gateName,
  }).then((res) => {
    if (res.code === 200) {
      tableData.value = res.rows;
      pagination.value.total = res.total;
    }
    loading.value = false;
  });
};

// 查询按钮
const handleQuery = () => {
  pagination.value.pageNum = 1;
  getList();
};

// 重置按钮
const handleReset = () => {
  queryParams.value.gateName = "";
  handleQuery();
};

// 查看详情
const handleDetail = (row) => {
  formTitle.value = "水闸 - " + row.gateName;
  fetchSluiceGateInfo(row.id).then((res) => {
    if (res.code === 200) {
      currentDetail.value = res.data || {};
      detailDialog.value = true;
    }
  });
};

// 新增按钮
const handleAdd = () => {
  resetForm();
  formTitle.value = "新增水闸";
  formType.value = "add";
  formDialog.value = true;
};

// 编辑按钮
const handleEdit = (row) => {
  resetForm();
  formTitle.value = "编辑水闸 - " + row.gateName;
  formType.value = "edit";
  fetchSluiceGateInfo(row.id).then((res) => {
    if (res.code === 200) {
      formData.value = res.data || {};
      // 确保district值正确赋值，以便el-tree-select组件能正确回显
      formData.value.district = res.data.districtCode;
      formData.value.river = res.data.riverCode;
      formDialog.value = true;
    }
  });
};

// 删除按钮
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除水闸 ${row.gateName} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteSluiceGate(row.id).then((res) => {
      if (res.code === 200) {
        ElMessage.success("删除成功");
        getList();
      }
    });
  });
};

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      if (formType.value === "add") {
        saveSluiceGate(formData.value).then((res) => {
          if (res.code === 200) {
            getList();
            ElMessage.success("添加成功");
            formDialog.value = false;
          }
        });
      } else {
        // 更新水闸
        saveSluiceGate(formData.value).then((res) => {
          if (res.code === 200) {
            getList();
            formDialog.value = false;
            ElMessage.success("更新成功");
          }
        });
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  formRef.value && formRef.value.resetFields();
};

// 关闭表单对话框
const handleDialogClose = () => {
  resetForm();
};

// 河流数据
const riverOptions = ref([]);
// 区县数据
const adcdOptions = ref([]);
// 部门数据
const deptOptions = ref([]);
// 获取树结构数据
const getTreeselect = () => {
  selectRvList({
    pageNum: 1,
    pageSize: 10000,
    asNext: 0,
  }).then((res) => {
    if (res.code === 200) {
      riverOptions.value = formatRiverTreeData(res.data);
    }
  });

  getAdcdTree().then((res) => {
    adcdOptions.value = res.data[0].children;
  });

  listDept({}).then((response) => {
    deptOptions.value = proxy.handleTree(response.data, "deptId");
  });
};

onMounted(() => {
  getList();
  getTreeselect();
});
</script>

<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" inline class="form-container">
      <el-form-item label="水闸名称" prop="gateName">
        <el-input
          v-model="queryParams.gateName"
          placeholder="请输入水闸名称"
          clearable
        />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button @click="handleReset" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <div class="content content-table">
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd"
          >新增</el-button
        >
      </div>
      <!-- 表格区域 -->
      <el-table v-loading="loading" :data="tableData" stripe>
        <el-table-column type="index" label="序号" width="65" align="center" />
        <el-table-column prop="gateName" label="水闸名称" align="center" />
        <el-table-column prop="gateCode" label="水闸代码" align="center" />
        <el-table-column prop="district" label="所在区县" align="center" />
        <el-table-column prop="projectGrade" label="工程等别" align="center">
          <template #default="scope">
            {{
              getTypeLabel(scope.row.projectGrade, ENGINEERING_LEVEL_OPTIONS)
            }}
          </template>
        </el-table-column>
        <el-table-column prop="projectScale" label="工程规模" align="center">
          <template #default="scope">
            {{
              getTypeLabel(scope.row.projectScale, ENGINEERING_SCALE_OPTIONS)
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="designFlow"
          label="设计过闸流量 (m³/s)"
          align="center"
        />
        <el-table-column
          prop="gateHoleCount"
          label="闸孔数量 (孔)"
          align="center"
        />
        <el-table-column
          prop="totalNetWidth"
          label="闸孔总净宽 (m)"
          align="center"
        />
        <el-table-column label="操作" align="center" width="210">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="View"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <Pagination
        :total="pagination.total"
        v-model:page="pagination.pageNum"
        v-model:limit="pagination.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialog"
      :title="formTitle"
      width="800px"
      class="detail-dialog"
    >
      <div class="detail-container">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="水闸名称">{{
            currentDetail.gateName
          }}</el-descriptions-item>
          <el-descriptions-item label="水闸代码">{{
            currentDetail.gateCode
          }}</el-descriptions-item>
          <el-descriptions-item label="所在区县">{{
            currentDetail.districtName
          }}</el-descriptions-item>
          <el-descriptions-item label="所在河流">{{
            currentDetail.riverName
          }}</el-descriptions-item>
          <el-descriptions-item label="所在位置">{{
            currentDetail.location || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="水闸类型">{{
            getTypeLabel(currentDetail.gateType, SLUICE_TYPE_OPTIONS) || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="水闸用途">{{
            currentDetail.gatePurpose || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="工程等别">{{
            getTypeLabel(
              currentDetail.projectGrade,
              ENGINEERING_LEVEL_OPTIONS
            ) || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="工程规模">{{
            getTypeLabel(
              currentDetail.projectScale,
              ENGINEERING_SCALE_OPTIONS
            ) || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="主要建筑物级别">{{
            getTypeLabel(currentDetail.buildingLevel, BUILDING_LEVEL_OPTIONS) ||
            "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="设计过闸流量 (m³/s)">{{
            currentDetail.designFlow || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="闸孔数量 (孔)">{{
            currentDetail.gateHoleCount || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="闸孔总净宽 (m)">{{
            currentDetail.totalNetWidth || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="工程建设情况">{{
            getTypeLabel(
              currentDetail.constructionStatus,
              CONSTRUCTION_STATUS_OPTIONS
            ) || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="建成时间">{{
            currentDetail.completionDate || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="所属管理单位">{{
            currentDetail.managementDept || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{
            currentDetail.remarks || "--"
          }}</el-descriptions-item>
        </el-descriptions>

        <!-- 水闸位置预览 -->
        <div class="location-preview">
          <h3>水闸空间信息预览</h3>
          <div class="map-container">
            <min-map
              :points="[currentDetail.longitude, currentDetail.latitude]"
              class="w-full h-full"
            ></min-map>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="formDialog"
      :title="formTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="140px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="水闸名称" prop="gateName">
              <el-input
                v-model="formData.gateName"
                placeholder="请输入水闸名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="水闸代码" prop="gateCode">
              <el-input
                v-model="formData.gateCode"
                placeholder="请输入水闸代码"
                :disabled="formType === 'edit'"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在区县" prop="district">
              <el-tree-select
                v-model="formData.district"
                :data="adcdOptions"
                clearable
                :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
                value-key="adcd"
                auto-expand-parent
                placeholder="请选择所在区县"
                check-strictly
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在河流" prop="river">
              <el-tree-select
                v-model="formData.river"
                :data="riverOptions"
                filterable
                clearable
                :props="{
                  value: 'rvCode',
                  label: 'rvName',
                  children: 'children',
                }"
                value-key="id"
                placeholder="请选择所属河流"
                check-strictly
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number
                v-model="formData.longitude"
                placeholder="请输入经度"
                :precision="6"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number
                v-model="formData.latitude"
                placeholder="请输入纬度"
                :precision="6"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在位置" prop="location">
              <el-input
                v-model="formData.location"
                placeholder="请输入所在位置"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="水闸类型" prop="gateType">
              <el-select
                v-model="formData.gateType"
                placeholder="请选择水闸类型"
                clearable
                class="w-full"
              >
                <el-option
                  v-for="item in dictData.gateTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="水闸用途" prop="gatePurpose">
              <el-input
                v-model="formData.gatePurpose"
                placeholder="请输入水闸用途"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程等别" prop="projectGrade">
              <el-select
                v-model="formData.projectGrade"
                placeholder="请选择工程等别"
                clearable
                class="w-full"
              >
                <el-option
                  v-for="item in dictData.projectGradeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工程规模" prop="projectScale">
              <el-select
                v-model="formData.projectScale"
                placeholder="请选择工程规模"
                clearable
                class="w-full"
              >
                <el-option
                  v-for="item in dictData.projectScaleOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主要建筑物级别" prop="buildingLevel">
              <el-select
                v-model="formData.buildingLevel"
                placeholder="请选择主要建筑物级别"
                clearable
                class="w-full"
              >
                <el-option
                  v-for="item in dictData.buildingLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设计过闸流量(m³/s)" prop="designFlow">
              <el-input-number
                v-model="formData.designFlow"
                placeholder="请输入设计过闸流量"
                :precision="2"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="闸孔数量(孔)" prop="gateHoleCount">
              <el-input-number
                v-model="formData.gateHoleCount"
                placeholder="请输入闸孔数量"
                :precision="0"
                :min="0"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="闸孔总净宽(m)" prop="totalNetWidth">
              <el-input-number
                v-model="formData.totalNetWidth"
                placeholder="请输入闸孔总净宽"
                :precision="2"
                :min="0"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程建设情况" prop="constructionStatus">
              <el-select
                v-model="formData.constructionStatus"
                placeholder="请选择工程建设情况"
                clearable
                class="w-full"
              >
                <el-option
                  v-for="item in dictData.constructionStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="建成时间" prop="completionDate">
              <el-date-picker
                v-model="formData.completionDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择建成时间"
                clearable
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属管理单位" prop="managementDept">
              <el-tree-select
                v-model="formData.managementDept"
                :data="deptOptions"
                :props="{
                  value: 'deptId',
                  label: 'deptName',
                  children: 'children',
                }"
                value-key="deptId"
                placeholder="选择上级部门"
                check-strictly
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="formData.remarks"
            type="textarea"
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="formDialog = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.map-container {
  height: 400px;
  margin-top: 15px;
}

.map-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #909399;

  .el-icon {
    font-size: 36px;
    margin-bottom: 10px;
  }
}

.location-preview {
  margin-top: 20px;

  h3 {
    font-size: 16px;
    margin-bottom: 15px;
    font-weight: 500;
  }
}

:deep(.detail-dialog) {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px 24px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
      border-radius: 3px;
    }
  }
}
</style>
