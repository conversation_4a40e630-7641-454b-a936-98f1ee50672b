export default {
  gateName: [
    { required: true, message: "水闸名称不能为空", trigger: "blur" },
    { max: 15, message: "水闸名称不能超过15个字", trigger: "blur" },
  ],
  gateCode: [
    { required: true, message: "水闸代码不能为空", trigger: "blur" },
    { min: 18, max: 18, message: "水闸代码必须为18位字符", trigger: "blur" },
  ],
  district: [
    { required: true, message: "所在区县不能为空", trigger: "change" },
  ],
  longitude: [
    {
      validator: (rule, value, callback) => {
        if (value && (parseFloat(value) < 73 || parseFloat(value) > 136)) {
          callback(new Error("经度范围为73~136"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  latitude: [
    {
      validator: (rule, value, callback) => {
        if (value && (parseFloat(value) < 3 || parseFloat(value) > 54)) {
          callback(new Error("纬度范围为3~54"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  location: [{ max: 25, message: "所在位置不能超过25个字", trigger: "blur" }],
  gatePurpose: [
    { max: 25, message: "水闸用途不能超过25个字", trigger: "blur" },
  ],
  designFlow: [
    {
      pattern: /^\d+(\.\d{1,2})?$/,
      message: "设计过闸流量必须为非负数字，保留2位小数",
      trigger: "blur",
    },
  ],
  gateHoleCount: [
    { pattern: /^\d+$/, message: "闸孔数量必须为非负整数", trigger: "blur" },
  ],
  totalNetWidth: [
    {
      pattern: /^\d+(\.\d{1,2})?$/,
      message: "闸孔总净宽必须为非负数字，保留2位小数",
      trigger: "blur",
    },
  ],
};
