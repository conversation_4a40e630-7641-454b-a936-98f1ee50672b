<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="form-container">
      <el-form-item label="水库名称" prop="resName">
        <el-input v-model="queryParams.resName" placeholder="请输入水库名称" clearable
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="注册登记号" prop="registerCode">
        <el-input v-model="queryParams.registerCode" placeholder="请输入注册登记号" clearable
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="行政区划" prop="adcd">
        <!-- <el-select v-model="queryParams.engScal" placeholder="请选择工程规模" clearable style="width: 200px">
          <el-option v-for="item in engScalOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select> -->
        <el-tree-select v-model="queryParams.adcd" :data="adcdOptions"
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="adcd" placeholder="选择所属县区"
          check-strictly />
      </el-form-item>
      <el-form-item label="工程规模" prop="engScal">
        <el-select v-model="queryParams.engScal" placeholder="请选择工程规模" clearable>
          <el-option v-for="item in engScalOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
           </el-col>
           <el-col :span="1.5">
            <el-button type="info" plain icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="lyList" row-key="lycode" stripe>
        <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
        <el-table-column prop="resName" label="水库名称" :show-overflow-tooltip="true"></el-table-column>
        <!-- <el-table-column prop="resCode" label="水库代码" :show-overflow-tooltip="true"></el-table-column> -->
        <el-table-column prop="registerCode" label="注册登记号" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="countryName" label="区县" align="center"></el-table-column>
        <el-table-column prop="engScal" label="工程规模" :show-overflow-tooltip="true" align="center">
          <template #default="scope">
            <span>{{ getengScal(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="engStat" label="工程建设情况" :show-overflow-tooltip="true" align="center">
          <template #default="scope">
            <span v-if="scope.row.engStat == '1'">在建</span>
            <span v-if="scope.row.engStat == '2'">已建</span>
          </template>
        </el-table-column>
        <el-table-column prop="admDep" label="管理单位" align="center"></el-table-column>
        <!-- <el-table-column prop="geom" label="空间数据" align="center" :show-overflow-tooltip="true">
          <template #default="scope">
            <span><el-button type="success" size="small" plain @click="showMap(scope.row)">预览</el-button></span>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" align="center" width="210" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="showMap(scope.row)">查看</el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
        @pagination="getList" />
    </div>

    <!-- 添加或修改流域对话框 -->
    <el-dialog :title="title" v-model="open" width="720px" append-to-body @close="cancel">
      <el-form ref="rsvRef" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="选择所属流域" prop="basinId">
              <el-tree-select v-model="form.basinId" :data="lyOptions"
                :props="{ value: 'basinId', label: 'name', children: 'children' }" value-key="basinId"
                placeholder="选择流域" check-strictly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="水库名称" prop="resName">
              <el-input v-model="form.resName" placeholder="请输入水库名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="注册登记号" prop="registerCode">
              <el-input :disabled="!form.addFlag" v-model="form.registerCode" placeholder="请输入注册登记号"
                :readonly="!form.addFlag" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属河流" prop="riverId">
              <el-tree-select v-model="form.riverId" :data="riverSelect"
                :props="{ value: 'id', label: 'rvName', children: 'children' }" value-key="id" placeholder="选择所属河流"
                check-strictly />
              <!-- <el-select v-model="form.crOverType" placeholder="所属河流" clearable style="width: 200px">
                <el-option v-for="dict in riverSelect" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择所属区县" prop="countryCode">
              <el-tree-select v-model="form.countryCode" :data="adcdOptions" :render-after-expand="false"
                :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="adcd" placeholder="选择所属县区"
                check-strictly @node-click="handleNodeClick" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属乡镇" prop="townCode">
              <el-select v-model="form.townCode" placeholder="所属乡镇" clearable style="width: 200px">
                <el-option v-for="dict in townList" :key="dict.adcd" :label="dict.adnm" :value="dict.adcd" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经度" prop="lgtd">
              <el-input v-model="form.lgtd" placeholder="请输入经度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="lttd">
              <el-input v-model="form.lttd" placeholder="请输入纬度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="水库所在位置" prop="resLoc">
              <el-input v-model="form.resLoc" placeholder="请输入水库所在位置" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="水库类型" prop="resType">
              <el-select v-model="form.resType" placeholder="水库类型" clearable style="width: 200px">
                <el-option v-for="dict in resTypeSelects" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程等别" prop="engGrad">
              <el-select v-model="form.engGrad" placeholder="工程等别" clearable style="width: 200px">
                <el-option v-for="dict in engGradSelects" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="工程规模" prop="engScal">
              <el-select v-model="form.engScal" placeholder="工程规模" clearable style="width: 200px">
                <el-option v-for="dict in engScalOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="集水面积(km²)" prop="catchmentArea">
              <el-input v-model="form.catchmentArea" placeholder="请输入集水面积" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="防洪高水位(m)" prop="uppLevFlco">
              <el-input v-model="form.uppLevFlco" placeholder="请输入防洪高水位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="正常蓄水位(m)" prop="normWatLev">
              <el-input v-model="form.normWatLev" placeholder="请输入正常蓄水位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="死水位(m)" prop="deadLev">
              <el-input v-model="form.deadLev" placeholder="请输入死水位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总库容(万m³)" prop="totCap">
              <el-input v-model="form.totCap" placeholder="请输入总库容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="兴利库容(万m³)" prop="benResCap">
              <el-input v-model="form.benResCap" placeholder="请输入兴利库容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="死库容(万m³)" prop="deadCap">
              <el-input v-model="form.deadCap" placeholder="请输入死库容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程建设情况" prop="engStat">
              <el-select v-model="form.engStat" placeholder="工程建设情况" clearable style="width: 200px">
                <el-option label="在建" value="1" />
                <el-option label="已建" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="管理单位" prop="admDep">
              <el-input v-model="form.admDep" placeholder="请输入管理单位" />
            </el-form-item>
          </el-col>


          <!-- <el-col :span="24">
            <el-form-item label="空间数据" prop="geoType">
              <el-radio-group v-model="form.geoType">
                <el-radio label="zip">矢量数据(shp-zip,kml,geojson文件)</el-radio>
                <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                <el-radio label="people">人工绘制</el-radio>
              </el-radio-group>

            </el-form-item>
          </el-col>
          <div style="width: 100%;height: 130px;" v-show="form.geoType === 'zip'">
            <el-upload class="upload-demo" style="width: 100%;" ref="uploadRef" drag name="multipartFile"
              :action="uploadUrl" :data="{
              }" :headers="{ 'Authorization': token }" :limit="1" :on-success="handleSuccess">
              <div class="el-upload__text">
                拖拽上传 或 <em>点击上传</em>
              </div>
            </el-upload>
          </div>
          <div style="width: 100%;height: 350px;background: #555">
            <min-map @updateBound="updateBound" :geoType="geoType" :geom="form.geom"
              :show-tool="form.geoType === 'people'" style="width: 100%;height:100%"></min-map>
          </div> -->
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="curTitle" v-model="open2" width="920px" height="900px" append-to-body class="station-detail-dialog">
      <div class="station-detail-wrapper">
        <!-- 基本信息区域 -->
        <div class="station-form-container">
          <div class="station-title-bar">
            <h3 class="station-subtitle">基本信息</h3>
          </div>
          <div class="station-info">
            <div class="info-item">
              <span class="label">水库名称:</span>
              <span class="value">{{ detailData.resName }}</span>
            </div>
            <div class="info-item">
              <span class="label">注册登记号:</span>
              <span class="value">{{ detailData.registerCode }}</span>
            </div>
            <div class="info-item">
              <span class="label">水库类型:</span>
              <span class="value">{{ getresType(detailData) || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属河流:</span>
              <span class="value">{{ detailData.riverName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属区县:</span>
              <span class="value">{{ detailData.countryName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属乡镇:</span>
              <span class="value">{{ detailData.townName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">所在位置:</span>
              <span class="value">{{ detailData.resLoc || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">管理单位:</span>
              <span class="value">{{ detailData.admDep || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">工程等别:</span>
              <span class="value">{{ getengGrad(detailData) || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">工程规模:</span>
              <span class="value">{{ getengScal(detailData) || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">工程建设情况:</span>
              <span class="value">
                <el-tag size="small" type="info" v-if="detailData.engStat == 1">在建</el-tag>
                <el-tag size="small" type="info" v-if="detailData.engStat == 2">已建</el-tag>
                <span v-if="detailData.engStat != 1 && detailData.engStat != 2">-</span>
              </span>
            </div>
            <div class="info-item">
              <span class="label">防洪高水位:</span>
              <span class="value">{{ detailData.uppLevFlco ? detailData.uppLevFlco + ' m' : '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">正常蓄水位:</span>
              <span class="value">{{ detailData.normWatLev ? detailData.normWatLev + ' m' : '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">死水位:</span>
              <span class="value">{{ detailData.deadLev ? detailData.deadLev + ' m' : '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">总库容:</span>
              <span class="value">{{ detailData.totCap ? detailData.totCap + ' 万m³' : '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">兴利库容:</span>
              <span class="value">{{ detailData.benResCap ? detailData.benResCap + ' 万m³' : '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">死库容:</span>
              <span class="value">{{ detailData.deadCap ? detailData.deadCap + ' 万m³' : '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">集水面积:</span>
              <span class="value">{{ detailData.catchmentArea ? detailData.catchmentArea + ' km²' : '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 空间信息区域 -->
        <div class="station-form-container">
          <div class="station-title-bar">
            <h3 class="station-subtitle">空间信息</h3>
          </div>
          <div class="station-info">
            <div style="width: 100%; height: 300px; margin-top: 20px;">
              <mini-map
              :geom="detailData.geom"
              :showTool="false"
              :icon="[
                {
                  icon: 'reservoir.png',
                  label: '水库站',
                },
                ]"
              style="width: 100%; height: 100%"
            ></mini-map>
            </div>
          </div>
        </div>

      </div>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
        :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
        :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">保 存</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  selectRsList, addRsInfo, updateRsInfo,
  delRsInfo, getRsInfo, selectRvList,
  getAdcdTree,
  selectStlyList,
  selectTsList
} from "@/api/watershed/ads";
import { onUnmounted, nextTick } from "vue";
import MiniMap from '@/components/Map/plugins/drawTool'
import * as turf from '@turf/turf'
defineOptions({
  name: 'ReservoirIndex'
})

const { proxy } = getCurrentInstance();

const lyList = ref([]);
const open = ref(false);
const open2 = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const geoType = ref('point');
const curGeom = ref("");
const curTitle = ref("");
const lyOptions = ref([]);
const adcdOptions = ref([]);
const riverSelect = ref([]);
const townList = ref([])
const detailData = ref('')

const refreshTable = ref(true);
const uploadRef = ref('')
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
import { getToken } from '@/utils/auth'
const token = getToken()
/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,

  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/system/reservoir/reservoirExcel"
});

// 工程等别
const engGradSelects = [
  {
    label: 'I',
    value: '1'
  },
  {
    label: 'II',
    value: '2'
  },
  {
    label: 'III',
    value: '3'
  },
  {
    label: 'IV',
    value: '4'
  },
  {
    label: 'V',
    value: '5'
  },
]
const total = ref(0)
const resTypeSelects = [
  { label: '山丘水库', value: '1' },
  { label: '平原水库', value: '2' },
  { label: '地下水库', value: '3' },
]
const engScalOptions = [
  { label: '大(1)型', value: '1' },
  { label: '大(2)型', value: '2' },
  { label: '中型', value: '3' },
  { label: '小(1)型', value: '4' },
  { label: '小(2)型', value: '5' },
  { label: '其他', value: '6' },
]

const data = reactive({
  form: {
    geoType: 'people'
  },
  queryParams: {
    resName: '',
    registerCode: '',
    engScal: '',
    asNext: 1, // 1 关联查询 ， 0 不关联就查自己的
    pageNum: 1,
    pageSize: 20,
  },
  rules: {
    basinId: [{ required: true, message: "流域不能为空", trigger: "blur" }],
    resName: [{ required: true, message: "水库名称不能为空", trigger: "blur" }],
    registerCode: [{ required: true, message: "注册登记号不能为空", trigger: "blur" },
    {
      pattern: /^[\d\w\W]{14}$/, message: '注册登记号为14位', trigger: 'blur'
    }
    ],
    countryCode: [{ required: true, message: "所属区县不能为空", trigger: "blur" }],
    engScal: [{ required: true, message: "工程规模不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询水库列表 */
async function getList() {
  loading.value = true;

  // let params = {
  //   lyCode: queryParams.value.rvName,
  //   pageNum: 1,
  //   pageSize: 1000,
  //   asNext: 1 // 加个asNext 在查询接口里 1是0否
  // }
  selectRsList(queryParams.value).then(res => {
    lyList.value = res.data.records
    total.value = res.data.total
    loading.value = false;
  });
}

getRiver()
getadcdTreeselect()
getLyOptions()
getTown()
/** 查询区县下拉树结构 */
function getadcdTreeselect() {
  adcdOptions.value = [];
  getAdcdTree().then(res => {
    adcdOptions.value = res.data[0].children

  })
}
/** 查询河流下拉树结构 */
function getRiver() {
  selectRvList({
    pageNum: 1,
    pageSize: 1000,
  }).then(res => {
    riverSelect.value = convertDataFormat(res.data);
  })
}
/** 查询流域下拉树结构 */
function getLyOptions() {
  selectStlyList({
    pageNum: 1,
    pageSize: 1000,
  }).then(res => {
    lyOptions.value = convertDataFormat(res.data);
  })
}
function getTown() {
  selectTsList({
    pageNum: 1,
    pageSize: 1000,
  }).then(res => {
    townList.value = res.rows || []
  })
}
function convertDataFormat(data) {
  // 检查是否是数组
  if (!Array.isArray(data)) {
    return null;
  }
  // 对数组中的每个元素进行处理
  return data.map(item => {
    // 复制 data 属性并移除 children
    const newData = { ...item.data };
    delete newData.children;
    // 递归处理子元素
    if (item.children && item.children.length > 0) {
      newData.children = convertDataFormat(item.children);
    }
    return newData;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();

}
/** 表单重置 */
function reset(flag) {
  form.value = {
    addFlag: flag,
    geoType: 'people'
  };
  proxy.resetForm("rsvRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
  reset(true);
  open.value = true;
  title.value = "新增水库";
}

function handleNodeClick(node) {
  console.log(node.children);
  if (!node.children || node.children.length === 0) {
    // 只选中最后一个层级节点
    form.value.countryCode = node.adcd;
  } else {
    // 清空选中
    // form.value.lyCode = '';
    proxy.$modal.msgError("请选择区县");
    nextTick(() => {
      form.value.countryCode = '';
      console.log(form.value);

    })

  }
}


/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  open.value = true;
  title.value = "修改水库 - " + row.resName;

  let obj = Object.assign({ geoType: 'people' }, row);
  delete obj.children
  delete obj.createBy
  delete obj.createTime
  delete obj.updateBy
  delete obj.updateTime
  delete obj.remark
  delete obj.asNext
  delete obj.pageNum
  delete obj.pageSize
  form.value = obj
  let rg = await getRsInfo(row.registerCode)
  form.value.geom = rg.data && rg.data.geom ? JSON.parse(rg.data.geom) : {}
}

function getengScal(data) {
  for (let i = 0; i < engScalOptions.length; i++) {
    if (Number(engScalOptions[i].value) == data.engScal) {
      return engScalOptions[i].label
    }
  }
}
function getengGrad(data) {
  for (let i = 0; i < engGradSelects.length; i++) {
    if (Number(engGradSelects[i].value) == data.engGrad) {
      return engGradSelects[i].label
    }
  }
}
function getresType(data) {
  for (let i = 0; i < resTypeSelects.length; i++) {
    if (Number(resTypeSelects[i].value) == data.resType) {
      return resTypeSelects[i].label
    }
  }
}


function hideMap() {
  open2.value = false
}
async function showMap(row) {
  let res = await getRsInfo(row.registerCode)
  if (res.data) {
    open2.value = true
    delete row.riverName
    delete row.townName
    if(res.data.lgtd && res.data.lttd){
      let lgtd = res.data.lgtd;
      let lttd = res.data.lttd;
      // null 值处理
      let Coordinate = turf.point([lgtd, lttd]);
      res.data.geom = Coordinate;
    }
    detailData.value = Object.assign(res.data, row)
    await nextTick()
    curGeom.value = res.data.geom ? JSON.parse(res.data.geom) : {}
    curTitle.value = row.resName
  } else {
    proxy.$modal.msgSuccess("没找到空间数据");
  }
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["rsvRef"].validate(valid => {
    if (valid) {
      // form.value.geom = JSON.stringify(geom)
      console.log(form.value);
      if (form.value.addFlag) {
        addRsInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      } else {
        updateRsInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.resName + '"的数据项?').then(function () {
    return delRsInfo({ resCode: row.registerCode });
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}
function updateBound(geojson) {
  // 提交geojson数据到后台
  console.log(geojson)
  form.value.geom = geojson
}

function handleSuccess(
  response
) {
  if (response.code == 200) {
    form.value.geom = response.data
  } else {
    uploadRef.value?.clearFiles()
    proxy.$modal.msgSuccess(response.msg);
  }
}
/** 导入按钮操作 */
function handleImport() {
  upload.title = "水库导入";
  upload.open = true;
};
/** 下载模板操作 */
function importTemplate() {
  proxy.download("file/download", {
    fileName: 'a802001c-f046-4ed4-b73d-e8d791f8cdaf_水库导入模板.xlsx',
    bucketName: 'excel-template'
  }, `水库导入模板.xlsx`);
};
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
};
/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
};

onMounted(() => {
  window.EventBus.$on('updateBound/update', updateBound)
})
onUnmounted(() => {

})

getList();
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

/* 测站详情对话框样式 */
:deep(.station-detail-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    margin-right: 0;
  }

  .el-dialog__body {
    padding: 20px 24px;
    max-height: 75vh;
    overflow-y: auto;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 14px 20px;
  }
}

.station-detail-wrapper {
  padding: 0;
}

.station-form-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.station-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.station-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  padding: 16px 0 8px;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: #409eff;
  }
}

.station-info {
  display: flex;
  flex-wrap: wrap;
  padding: 8px 16px;
  gap: 16px;

  .info-item {
    min-width: 250px;
    flex: 1 0 calc(33.33% - 16px);
    display: flex;
    align-items: center;

    @media (max-width: 1200px) {
      flex: 1 0 calc(50% - 16px);
    }

    @media (max-width: 768px) {
      flex: 1 0 100%;
    }

    .label {
      width: 90px;
      color: #606266;
      font-weight: 500;
      margin-right: 8px;
      text-align: right;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      font-size: 14px;
    }
  }
}
</style>
