<script setup>
import { ref, reactive, onMounted, getCurrentInstance, nextTick, watch, onUnmounted, computed } from "vue";
import { ElMessage } from "element-plus";
import * as echarts from "echarts";
// 引入接口方法
import { getSafetyMonitorInfo, getDeviceMonitorInfo } from '@/api/waterGate/index';
import { fetchSafetyMonitoringStationList, fetchSafetyMonitoringSectionList, fetchSafetyMonitoringPointList } from '@/api/watershed/ads/index';
import { getTypeLabel } from '@/utils';
import { MONITOR_ITEM_OPTIONS } from '@/views/system/geo/utils/enum';
//水闸安全监测
defineOptions({
  name: "SluiceGateMonitoring",
});

const { proxy } = getCurrentInstance();

// 安全监测站相关
const stationList = ref([]); // 安全监测站列表
const selectedStation = ref(null); // 选中的安全监测站

// 断面、测点、监测项目查询参数
const detailQueryParams = ref({
  section: "",
  measurePoint: "",
  monitorItem: "0",
  time: "",
});

// 断面、测点选项
const sectionOptions = ref([]); // 断面下拉
const pointOptions = ref([]);   // 测点下拉

// 监测项目选项
const monitorItemOptions = [
  { value: "0", label: "渗压" },
  { value: "1", label: "温度" },
];

// 监测项目y轴名称映射表
const monitorItemYAxisNameMap = {
  "0": "渗压(Kpa)",
  "1": "温度(°C)"
};

// 监测数据
const monitorData = ref([]);
const monitorLoading = ref(false);

// 设备详情对话框等原有逻辑保持不变
const deviceDetailDialog = ref(false);
const currentDevice = ref({});
const detailViewType = ref(1); // 1: 图表, 2: 表格
const chartTimeRange = ref([]); // [start, end]
const chartData = ref([]); // 图表和表格共用
const chartLoading = ref(false);

const treeRef = ref();

// 搜索关键字
const searchKeyword = ref("");

// 树节点过滤方法
const filterNode = (value, data) => {
  if (!value) return true;
  return data.stationName && data.stationName.indexOf(value) !== -1;
};

// 查询按钮：执行树过滤
const handleTreeSearch = () => {
  treeRef.value && treeRef.value.filter(searchKeyword.value);
};

// 获取安全监测站列表
const getStationList = async () => {
  try {
    const res = await fetchSafetyMonitoringStationList();
    if (res && res.code === 200 && Array.isArray(res.rows) && res.rows.length > 0) {
      stationList.value = res.rows.map(item => ({
        ...item,
        stationCode: String(item.stationCode)
      }));
      selectedStation.value = stationList.value[0];
      await getSectionList();
      nextTick(() => {
        treeRef.value?.setCurrentKey(selectedStation.value?.stationCode);
      });
      await getMonitorData();
    } else {
      stationList.value = [];
      selectedStation.value = null;
      sectionOptions.value = [];
      pointOptions.value = [];
      monitorData.value = [];
    }
  } catch (e) {
    stationList.value = [];
    selectedStation.value = null;
    sectionOptions.value = [];
    pointOptions.value = [];
    monitorData.value = [];
  }
};

// 获取断面列表（根据选中监测站）
const getSectionList = async () => {
  if (!selectedStation.value) return;
  try {
    const res = await fetchSafetyMonitoringSectionList({ stationCode: selectedStation.value.stationCode });
    if (res && res.code === 200 && Array.isArray(res.rows)) {
      sectionOptions.value = res.rows.map(item => ({ value: item.sectionCode, label: item.sectionCode }));
      // 默认不选断面
      detailQueryParams.value.section = '';
      pointOptions.value = [];
      detailQueryParams.value.measurePoint = '';
    } else {
      sectionOptions.value = [];
      pointOptions.value = [];
      detailQueryParams.value.section = '';
      detailQueryParams.value.measurePoint = '';
    }
  } catch (e) {
    sectionOptions.value = [];
    pointOptions.value = [];
    detailQueryParams.value.section = '';
    detailQueryParams.value.measurePoint = '';
  }
};

// 获取测点列表（根据选中断面）
const getPointList = async () => {
  if (!selectedStation.value || !detailQueryParams.value.section) {
    pointOptions.value = [];
    detailQueryParams.value.measurePoint = '';
    return;
  }
  try {
    const res = await fetchSafetyMonitoringPointList({
      stationCode: selectedStation.value.stationCode,
      sectionCode: detailQueryParams.value.section
    });
    if (res && res.code === 200 && Array.isArray(res.rows)) {
      pointOptions.value = res.rows.map(item => ({ value: item.pointCode, label: item.pointCode }));
      detailQueryParams.value.measurePoint = '';
    } else {
      pointOptions.value = [];
      detailQueryParams.value.measurePoint = '';
    }
  } catch (e) {
    pointOptions.value = [];
    detailQueryParams.value.measurePoint = '';
  }
};

// 查询监测数据
const getMonitorData = async () => {
  if (!selectedStation.value) return;
  monitorLoading.value = true;
  try {
    const params = {
      stationCode: selectedStation.value.stationCode,
      sectionCode: detailQueryParams.value.section,
      pointCode: detailQueryParams.value.measurePoint,
      monitorItem: detailQueryParams.value.monitorItem,
      monitorDate: detailQueryParams.value.time
    };
    const res = await getSafetyMonitorInfo(params);
    if (res && res.code === 200 && Array.isArray(res.rows)) {
      monitorData.value = res.rows;
    } else {
      monitorData.value = [];
    }
  } catch (e) {
    monitorData.value = [];
  } finally {
    monitorLoading.value = false;
  }
};

// 断面变化时，加载测点
const handleSectionChange = async () => {
  await getPointList();
};

// 查询按钮
const handleDetailQuery = () => {
  getMonitorData();
};

// 重置按钮
const handleDetailReset = async () => {
  if (stationList.value.length > 0) {
    selectedStation.value = stationList.value[0];
    nextTick(() => {
      treeRef.value?.setCurrentKey(selectedStation.value?.stationCode);
    });
  } else {
    selectedStation.value = null;
  }
  detailQueryParams.value = {
    section: '',
    measurePoint: '',
    monitorItem: '0',
    time: ''
  };
  pointOptions.value = [];
  searchKeyword.value = '';
  treeRef.value && treeRef.value.filter("");
  await getMonitorData();
};

// 新增：监测站切换事件
const handleStationChange = (node) => {
  if (!node) return;
  selectedStation.value = { ...node, stationCode: String(node.stationCode) };
  getSectionList();
  detailQueryParams.value.section = '';
  detailQueryParams.value.measurePoint = '';
  pointOptions.value = [];
  monitorData.value = [];
  nextTick(() => {
    treeRef.value?.setCurrentKey(selectedStation.value?.stationCode);
  });
  // 切换监测站后自动请求表格数据
  getMonitorData();
};

// 获取默认时间范围：当前时间和一个月前
function getDefaultTimeRange() {
  const end = new Date();
  const start = new Date();
  start.setMonth(start.getMonth() - 1);
  return [new Date(start), new Date(end)]; // 确保为Date对象
}

// 时间格式化 yyyy-MM-dd HH:mm:ss
function formatDate(date) {
  const pad = n => n < 10 ? '0' + n : n;
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` +
         `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
}

// 打开设备详情弹窗
const viewDeviceDetail = async (row) => {
  currentDevice.value = row;
  deviceDetailDialog.value = true;
  detailViewType.value = 1;
  chartTimeRange.value = getDefaultTimeRange();
  await fetchDeviceMonitorData();
};

// 获取设备监测数据
const fetchDeviceMonitorData = async () => {
  if (!currentDevice.value.deviceCode || !chartTimeRange.value.length) return;
  chartLoading.value = true;
  try {
    // 确保chartTimeRange为Date对象数组
    let [start, end] = chartTimeRange.value;
    if (!(start instanceof Date)) start = new Date(start);
    if (!(end instanceof Date)) end = new Date(end);
    chartTimeRange.value = [start, end];
    const params = {
      startTime: formatDate(start),
      endTime: formatDate(end),
      device_code: currentDevice.value.deviceCode,
    };
    const res = await getDeviceMonitorInfo(params);
    if (res && res.code === 200 && Array.isArray(res.rows)) {
      chartData.value = res.rows;
    } else {
      chartData.value = [];
    }
  } catch (e) {
    chartData.value = [];
  } finally {
    chartLoading.value = false;
  }
};

// 查询按钮
const handleChartQuery = () => {
  fetchDeviceMonitorData();
};

// 重置按钮
const handleChartReset = () => {
  chartTimeRange.value = getDefaultTimeRange();
  fetchDeviceMonitorData();
};

// 切换图/表
const toggleDetailViewType = () => {
  detailViewType.value = detailViewType.value === 1 ? 2 : 1;
};

// 初始化
onMounted(() => {
  getStationList();
});

// 设备详情、图表等原有逻辑保持不变
// ... existing code ...

watch(searchKeyword, (val) => {
  if (val === "" && treeRef.value) {
    treeRef.value.filter("");
  }
});

const chartInstance = ref(null);
let echartsInstance = null;

// 渲染echarts折线图
function renderChart() {
  nextTick(() => {
    if (detailViewType.value !== 1) return;
    const dom = chartInstance.value;
    if (!dom) return;
    if (echartsInstance) {
      echartsInstance.dispose();
      echartsInstance = null;
    }
    echartsInstance = echarts.init(dom);
    // 动态获取y轴名称
    let monitorItem = currentDevice.value && currentDevice.value.monitorItem != null
      ? currentDevice.value.monitorItem
      : detailQueryParams.value.monitorItem;
    monitorItem = String(monitorItem);
    const yAxisName = monitorItemYAxisNameMap[monitorItem] || '监测值';
    const option = {
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: chartData.value.map(item => item.monitorDate),
        name: '时间',
        axisLabel: {
          interval: 'auto',
          // rotate: 45,
          fontSize: 10,
          overflow: 'break'
        }
      },
      yAxis: {
        type: 'value',
        name: yAxisName
      },
      series: [
        {
          name: '监测值',
          type: 'line',
          data: chartData.value.map(item => item.monitorValue),
          smooth: true,
          showSymbol: false
        }
      ],
      grid: { left: 40, right: 40, bottom: 40, top: 40 }
    };
    echartsInstance.setOption(option, true);
    echartsInstance.resize();
  });
}

// 监听弹窗显示和数据变化，渲染图表
watch([deviceDetailDialog, detailViewType, chartData], ([dialog, type]) => {
  if (dialog && type === 1) {
    nextTick(() => {
      renderChart();
    });
  }
});

// 弹窗关闭时销毁echarts实例
watch(deviceDetailDialog, (val) => {
  if (!val && echartsInstance) {
    echartsInstance.dispose();
    echartsInstance = null;
  }
});

onUnmounted(() => {
  if (echartsInstance) {
    echartsInstance.dispose();
    echartsInstance = null;
  }
});

// 表格数据倒序（不影响图表）
const tableDataSorted = computed(() => {
  return [...chartData.value].sort((a, b) => {
    return new Date(b.monitorDate) - new Date(a.monitorDate);
  });
});

// 监测项目label转换方法
const getMonitorItemLabel = (item) => {
  if (!item) return '';
  return getTypeLabel(item, MONITOR_ITEM_OPTIONS);
};
</script>

<template>
  <div class="app-container">
    <div class="content">
      <!-- 左侧安全监测站列表 -->
      <div class="left-tree">
        <div class="search-container">
          <div class="search-row">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入监测站名称"
              clearable
            />
            <el-button type="primary" icon="Search" @click="handleTreeSearch"></el-button>
          </div>
        </div>
        <div class="sluice-list" v-loading="!stationList.length">
          <el-tree
            ref="treeRef"
            :data="stationList"
            :props="{ label: 'stationName' }"
            node-key="stationCode"
            :default-expanded-keys="selectedStation ? [selectedStation.stationCode] : []"
            :highlight-current="true"
            :current-node-key="selectedStation ? selectedStation.stationCode : ''"
            :expand-on-click-node="false"
            :show-checkbox="false"
            @current-change="handleStationChange"
            :filter-node-method="filterNode"
            :filter="searchKeyword"
          />
        </div>
      </div>
      <!-- 右侧监测数据 -->
      <div class="right-table">
        <el-form :model="detailQueryParams" inline class="form-container">
          <el-form-item label="断面" prop="section">
            <el-select
              v-model="detailQueryParams.section"
              placeholder="请选择断面"
              @change="handleSectionChange"
              clearable
            >
              <el-option
                v-for="item in sectionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="测点" prop="measurePoint">
            <el-select
              v-model="detailQueryParams.measurePoint"
              placeholder="请选择测点"
              clearable
            >
              <el-option
                v-for="item in pointOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="监测项目" prop="monitorItem">
            <el-select
              v-model="detailQueryParams.monitorItem"
              placeholder="请选择监测项目"
            >
              <el-option
                v-for="item in monitorItemOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="时间" prop="time">
            <el-date-picker
              v-model="detailQueryParams.time"
              type="datetime"
              placeholder="请选择时间"
              style="width: 180px;"
            ></el-date-picker>
          </el-form-item>
          <el-form-item class="form-item-button">
            <el-button type="primary" icon="Search" @click="handleDetailQuery"
              >查询</el-button
            >
            <el-button
              icon="Refresh"
              type="primary"
              plain
              @click="handleDetailReset"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <div class="data-table-container" v-loading="monitorLoading">
          <el-table :data="monitorData" stripe>
            <el-table-column
              type="index"
              label="序号"
              width="65"
              align="center"
            />
            <el-table-column
              prop="stationName"
              label="监测站名称"
              align="center"
            />
            <el-table-column
              prop="stationCode"
              label="监测站编码"
              align="center"
            />
            <el-table-column prop="sectionCode" label="断面" align="center" />
            <el-table-column prop="pointCode" label="测点" align="center" />
            <el-table-column label="设备编码" align="center">
              <template #default="scope">
                <el-button
                  type="primary"
                  link
                  @click="viewDeviceDetail(scope.row)"
                >
                  {{ scope.row.deviceCode }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="监测项目" align="center">
              <template #default="scope">
                {{ getMonitorItemLabel(scope.row.monitorItem) }}
              </template>
            </el-table-column>
            <el-table-column prop="monitorDate" label="时间" align="center" />
            <el-table-column prop="monitorValue" label="监测值" align="center" />
          </el-table>
        </div>
      </div>
    </div>
    <!-- 设备详情对话框 -->
    <el-dialog
      v-model="deviceDetailDialog"
      :title="`${currentDevice.stationName || ''} ( ${currentDevice.pointCode || ''} ) ( ${currentDevice.deviceCode || ''} )`"
      width="700px"
      destroy-on-close
    >
      <div class="device-detail-content">
        <div class="device-detail-header">
          <!-- 日期范围选择器 -->
          <div class="header-block">
            <el-date-picker
              v-model="chartTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="date-picker"
            />
          </div>
          <!-- 查询/重置按钮 -->
          <div class="header-block button-group">
            <el-button type="primary" icon="Search" @click="handleChartQuery">查询</el-button>
            <el-button icon="Refresh" @click="handleChartReset">重置</el-button>
          </div>
          <!-- 切换按钮 -->
          <div class="header-block">
            <el-button type="primary" @click="toggleDetailViewType">
              {{ detailViewType === 1 ? '数据' : '图' }}
            </el-button>
          </div>
        </div>
        <!-- 内容区 -->
        <div v-loading="chartLoading">
          <div v-if="detailViewType === 1" style="height: 350px;">
            <!-- e-charts 折线图 -->
            <div ref="chartInstance" style="width: 100%; height: 100%;"></div>
          </div>
          <div v-else>
            <!-- 表格 -->
            <el-table :data="tableDataSorted" stripe style="width: 100%;">
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="monitorItem" label="监测项目" align="center" />
              <el-table-column prop="monitorDate" label="时间" align="center" />
              <el-table-column prop="monitorValue" label="监测值" align="center" />
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.left-tree {
  max-width: 300px;
  min-width: 180px;
  width: 300px;
  border-right: 1px solid #e6e6e6;
  padding-right: 10px;
  margin-right: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-container {
    .search-row {
      display: flex;
      gap: 10px;
      align-items: center;

      .el-form-item {
        margin: 0;
      }
    }
  }

  .sluice-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;

    // 滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #e6e6e6;
    }

    .sluice-item {
      padding: 10px 15px;
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;
      }

      &.active {
        background-color: #ecf5ff;
        color: #409eff;
        font-weight: bold;
      }
    }
  }
}

// 详情对话框样式
.detail-container {
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .chart-container {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 10px;
  }

  .table-container {
    height: 400px;
    overflow-y: auto;
  }
}

/* 增强el-tree选中节点高亮效果 */
::v-deep .el-tree-node.is-current > .el-tree-node__content {
  background-color: #ecf5ff !important;
  color: #409eff !important;
  font-weight: bold;
}

// 设备详情弹窗内容区样式
.device-detail-content {
  padding: 16px 8px 10px 8px;
}
.device-detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 2px;
  flex-wrap: wrap;
}
.header-block {
  display: flex;
  align-items: center;
}
.date-picker {
  width: 120px;
}
.button-group .el-button + .el-button {
  margin-left: 8px;
}
.device-detail-header > .header-block + .header-block {
  margin-left: 2px;
}
// 图表和表格自适应弹窗宽度
.device-detail-content .el-table,
.device-detail-content [ref="chartInstance"] {
  width: 100% !important;
  min-width: 100% !important;
  box-sizing: border-box;
  min-height: 300px;
}
// 表格列宽适当缩小
.device-detail-content .el-table th,
.device-detail-content .el-table td {
  padding-left: 4px;
  padding-right: 4px;
}
</style>
