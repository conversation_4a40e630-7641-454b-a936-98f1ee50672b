<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { getWaterGateWarnList, saveWaterGateWarn } from "@/api/waterGateWarn";

defineOptions({
  name: "SafetyWarnConfig",
});

// 查询参数
const queryParams = ref({
  gateName: "", // 水闸名称
  monitorItem: "", // 安全监测项目，空为全部
});

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 编辑对话框
const formDialog = ref(false);
const formTitle = ref("");

// 表单数据
const formData = ref({
  id: "",
  gateName: "", // 水闸名称
  district: "", // 所在区县
  monitorItem: "", // 安全监测项目
  unit: "", // 测量单位
  warningLevel: "", // 预警等级
  warningValue: "", // 预警值
  rateWarningValue: "", // 变化率预警值
});

// 表单规则
const rules = reactive({
  warningValue: [
    {
      pattern: /^(\d+)(\.\d{1,2})?$/,
      message: "预警值格式不正确，最多2位非负小数",
      trigger: "blur",
    },
  ],
  rateWarningValue: [
    {
      pattern: /^(\d+)(\.\d{1})?$/,
      message: "变化率预警值格式不正确，最多1位非负小数",
      trigger: "blur",
    },
  ],
});

// 表单引用
const formRef = ref(null);

// 监控项目枚举
const ITEM_OPTIONS = [
  { value: 0, label: "渗压", unit: "kPa" },
  { value: 1, label: "温度", unit: "℃" },
  // 可扩展
];

const LEVEL_OPTIONS = [
  { value: 1, label: "Ⅰ级" },
  { value: 2, label: "Ⅱ级" },
  { value: 3, label: "Ⅲ级" },
  { value: 4, label: "Ⅳ级" },
];

// 监测项目选项（用于查询表单）
const MONITOR_ITEM_OPTIONS = [
  // { value: '', label: '全部' },
  { value: 0, label: "渗压" },
  { value: 1, label: "温度" },
];

// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      gateName: queryParams.value.gateName,
      monitorItem: queryParams.value.monitorItem,
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
    };
    const res = await getWaterGateWarnList(params);
    // 数据适配：拍平成一维数组
    tableData.value = [];
    (res.rows || []).forEach((row) => {
      (row.waterGateWarningPOList || []).forEach((child) => {
        tableData.value.push({
          id: child.id || `${row.gateCode}_${child.item}_${child.level}`,
          gateCode: row.gateCode,
          gateName: row.gateName,
          adnm: row.adnm,
          item: child.item,
          level: child.level,
          value: child.value,
          gradientValue: child.gradientValue,
        });
      });
    });
    pagination.value.total = res.total || 0;
  } catch (e) {
    tableData.value = [];
    pagination.value.total = 0;
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

// 查询按钮
const handleQuery = () => {
  pagination.value.pageNum = 1;
  getList();
};

// 重置按钮
const handleReset = () => {
  queryParams.value = {
    gateName: "",
    monitorItem: "",
  };
  handleQuery();
};

// 编辑按钮
const handleEdit = (row) => {
  formTitle.value = "编辑水闸安全预警阈值";
  formData.value = {
    id: row.id || "",
    gateCode: row.gateCode,
    gateName: row.gateName,
    adnm: row.adnm,
    item: row.item,
    itemLabel: getItemLabel(row.item),
    unit: getItemUnit(row.item),
    level: row.level,
    levelLabel: getLevelLabel(row.level),
    warningValue: row.value,
    rateWarningValue: row.gradientValue,
  };
  formDialog.value = true;
};

// 重置表单
const resetForm = () => {
  formData.value = {
    id: "",
    gateName: "",
    district: "",
    monitorItem: "",
    unit: "",
    warningLevel: "",
    warningValue: "",
    rateWarningValue: "",
  };
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 提交表单
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      // 适配接口字段，做必要的映射
      const payload = {
        id: formData.value.id !== "" ? Number(formData.value.id) : undefined,
        gateCode: formData.value.gateCode,
        gateName: formData.value.gateName,
        item:
          formData.value.item !== "" ? Number(formData.value.item) : undefined,
        level:
          formData.value.level !== ""
            ? Number(formData.value.level)
            : undefined,
        value:
          formData.value.warningValue !== ""
            ? Number(formData.value.warningValue)
            : undefined,
        gradientValue:
          formData.value.rateWarningValue !== ""
            ? Number(formData.value.rateWarningValue)
            : undefined,
        adnm: formData.value.adnm,
      };
      try {
        await saveWaterGateWarn(payload);
        ElMessage.success("更新成功");
        formDialog.value = false;
        getList();
      } catch (e) {
        ElMessage.error("保存失败");
      }
    }
  });
};

// 表格展示时item、level字段数字转中文
function getItemLabel(val) {
  const found = ITEM_OPTIONS.find((i) => i.value === val || i.label === val);
  return found ? found.label : val;
}
function getItemUnit(val) {
  const found = ITEM_OPTIONS.find((i) => i.value === val || i.label === val);
  return found ? found.unit : "";
}
function getLevelLabel(val) {
  const found = LEVEL_OPTIONS.find((i) => i.value === val || i.label === val);
  return found ? found.label : val;
}

// 合并单元格方法
defineExpose({ tableSpanMethod });
function tableSpanMethod({ row, column, rowIndex, columnIndex }) {
  // 只对 gateName 和 adnm 字段合并
  const mergeFields = ["gateName"];
  const field = column.property;
  if (!mergeFields.includes(field)) return [1, 1];

  // 找到当前字段的分组
  let startRow = rowIndex;
  let endRow = rowIndex;
  for (let i = rowIndex - 1; i >= 0; i--) {
    if (tableData.value[i][field] === row[field]) {
      startRow = i;
    } else {
      break;
    }
  }
  for (let i = rowIndex + 1; i < tableData.value.length; i++) {
    if (tableData.value[i][field] === row[field]) {
      endRow = i;
    } else {
      break;
    }
  }
  if (startRow === rowIndex) {
    // 该分组的第一行，合并行数 = endRow - startRow + 1
    return [endRow - startRow + 1, 1];
  } else {
    // 其他行隐藏
    return [0, 0];
  }
}

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="水闸名称" prop="gateName">
        <el-input
          v-model="queryParams.gateName"
          placeholder="请输入水闸名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安全监测项目">
        <el-select
          v-model="queryParams.monitorItem"
          placeholder="请选择安全监测项目"
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in MONITOR_ITEM_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button @click="handleReset" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <div class="content content-table">
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        row-key="id"
        stripe
        default-expand-all
        :span-method="tableSpanMethod"
      >
        <el-table-column
          prop="gateName"
          label="水闸名称"
          align="left"
          width="135"
        />
        <el-table-column
          prop="adnm"
          label="所在区县"
          align="center"
          width="135"
        />
        <el-table-column prop="item" label="安全监测项目" align="center">
          <template #default="scope">
            {{ getItemLabel(scope.row.item) }}
          </template>
        </el-table-column>
        <el-table-column prop="level" label="预警等级" align="center">
          <template #default="scope">
            {{ getLevelLabel(scope.row.level) }}
          </template>
        </el-table-column>
        <el-table-column prop="value" label="预警值" align="center" />
        <el-table-column
          prop="gradientValue"
          label="变化率预警值（%）"
          align="center"
        />
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        v-model:page="pagination.pageNum"
        v-model:limit="pagination.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 编辑对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="formDialog"
      width="800px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-row gutter="20">
          <el-col :span="12">
            <el-form-item label="水闸名称">
              <el-input
                v-model="formData.gateName"
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="安全监测项目">
              <el-input
                v-model="formData.itemLabel"
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row gutter="20">
          <el-col :span="12">
            <el-form-item label="测量单位">
              <el-input v-model="formData.unit" disabled style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预警等级">
              <el-input
                v-model="formData.levelLabel"
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row gutter="20">
          <el-col :span="12">
            <el-form-item label="预警值" prop="warningValue">
              <el-input
                v-model="formData.warningValue"
                placeholder="请输入预警值"
                style="width: 100%"
              >
                <template #append>{{ formData.unit }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="变化率预警值" prop="rateWarningValue">
              <el-input
                v-model="formData.rateWarningValue"
                placeholder="请输入变化率预警值"
                style="width: 100%"
              >
                <template #append>%</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="formDialog = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>
