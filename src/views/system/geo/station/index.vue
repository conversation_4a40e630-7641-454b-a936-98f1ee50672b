<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="测站名称" prop="stnm">
        <el-input
          v-model="queryParams.stnm"
          placeholder="请输入测站名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测站编码" prop="stcd">
        <el-input
          v-model="queryParams.stcd"
          placeholder="请输入测站编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行政区划" prop="addvcd">
        <!-- <el-select v-model="queryParams.type" placeholder="请选择工程规模" clearable style="width: 200px">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select> -->
        <el-tree-select
          v-model="queryParams.addvcd"
          :data="adcdOptions"
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
          value-key="adcd"
          placeholder="选择所属县区"
          check-strictly
        />
      </el-form-item>
      <el-form-item label="测站类型" prop="sttp">
        <el-select
          v-model="queryParams.sttp"
          placeholder="请选择测站类型"
          clearable
        >
          <el-option
            v-for="item in sttpSelects"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd"
              >新增</el-button
            >
            <!-- <el-button type="primary" plain @click="handleImport">导入</el-button> -->
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Upload" @click="handleImport"
              >导入</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </div>
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="lyList"
        row-key="lycode"
        stripe
      >
        <el-table-column
          type="index"
          label="序号"
          width="65"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="stcd"
          label="测站编码"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="stnm"
          label="测站名称"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="adnm"
          label="区县"
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="sttp"
          label="测站类型"
          :show-overflow-tooltip="true"
          align="center"
        >
          <template #default="scope">
            <span>{{ getSttp(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="rvnm"
          label="河流"
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="210"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="showMap(scope.row)"
              >查看</el-button
            >
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
            <!-- <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 添加或修改流域对话框 -->
    <el-dialog :title="title" v-model="open" width="720px" append-to-body>
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="测站名称" prop="stnm">
              <el-input v-model="form.stnm" placeholder="请输入测站名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测站编码" prop="stcd">
              <el-input
                v-model="form.stcd"
                placeholder="请输入测站编码"
                :readonly="!form.addFlag"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测站类型" prop="sttp">
              <el-select
                v-model="form.sttp"
                placeholder="测站类型"
                clearable
                style="width: 200px"
                @change="changeSttp"
              >
                <el-option
                  v-for="dict in sttpSelects"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属流域" prop="sttp">
              <el-tree-select
                v-model="form.basinId"
                :data="transformDataForTreeSelect(waterAreaList)"
                check-strictly
                clearable
                :render-after-expand="false"
                placeholder="请选择流域"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="所属河流"
              prop="riverId"
              :required="form.sttp !== 'PP' && form.sttp !== 'RR'"
            >
              <el-tree-select
                v-model="form.riverId"
                :data="riverSelect"
                filterable
                :render-after-expand="false"
                :props="{ value: 'id', label: 'rvName', children: 'children' }"
                value-key="id"
                placeholder="选择所属河流"
                check-strictly
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择所属区县" prop="addvcd">
              <el-tree-select
                v-model="form.addvcd"
                :data="adcdOptions"
                :render-after-expand="false"
                @node-click="handleNodeClick"
                :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
                :default-expand-all="true"
                value-key="adcd"
                placeholder="选择所属区县"
                check-strictly
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站址" prop="stlc">
              <el-input v-model="form.stlc" placeholder="请输入站址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经度" prop="lgtd">
              <el-input v-model="form.lgtd" placeholder="请输入经度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="lttd">
              <el-input v-model="form.lttd" placeholder="请输入纬度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基面名称" prop="dtmnm">
              <el-input v-model="form.dtmnm" placeholder="请输入基面名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基面高程(m)" prop="dtmel">
              <el-input v-model="form.dtmel" placeholder="请输入基面高程" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报汛等级" prop="frgrd">
              <el-select
                v-model="form.frgrd"
                placeholder="报汛等级"
                clearable
                style="width: 200px"
              >
                <el-option label="中央报汛站" value="1" />
                <el-option label="省级重点报汛站" value="2" />
                <el-option label="省级一般报汛站" value="3" />
                <el-option label="其他报汛站" value="4" />
                <el-option label="山洪报汛站" value="5" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="建站年月" prop="esstym">
              <!-- <el-input v-model="form.esstym" placeholder="请输入建站年月" /> -->
              <el-date-picker
                v-model="form.esstym"
                type="month"
                clearable
                value-format="YYYYMM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="始报年月" prop="bgfrym">
              <!-- <el-input v-model="form.bgfrym" placeholder="请输入始报年月" /> -->
              <el-date-picker
                v-model="form.bgfrym"
                type="month"
                clearable
                value-format="YYYYMM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测站岸别" prop="stbk">
              <el-select
                v-model="form.stbk"
                placeholder="测站岸别"
                clearable
                style="width: 200px"
              >
                <el-option label="左岸" value="0" />
                <el-option label="右岸" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测站方位" prop="stazt">
              <el-input v-model="form.stazt" placeholder="请输入测站方位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="至河口距离(km)" prop="dstrvm">
              <el-input v-model="form.dstrvm" placeholder="请输入至河口距离" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="集水面积(k㎡)" prop="drna">
              <el-input v-model="form.drna" placeholder="请输入集水面积" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
            <el-form-item label="空间数据" prop="geoType">
              <el-radio-group v-model="form.geoType">
                <el-radio label="zip">矢量数据(shp-zip,kml,geojson文件)</el-radio>
                <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                <el-radio label="people">人工绘制</el-radio>
              </el-radio-group>

            </el-form-item>
          </el-col>
          <div style="width: 100%;height: 130px;" v-show="form.geoType === 'zip'">
            <el-upload class="upload-demo" style="width: 100%;" ref="uploadRef" drag name="multipartFile"
              :action="uploadUrl" :data="{
              }" :headers="{ 'Authorization': token }" :limit="1" :on-success="handleSuccess">
              <div class="el-upload__text">
                拖拽上传 或 <em>点击上传</em>
              </div>
            </el-upload>
          </div>
          <div style="width: 100%;height: 350px;background: #555">
            <min-map @updateBound="updateBound" :geoType="geoType" :geom="form.geom"
              :show-tool="form.geoType === 'people'" style="width: 100%;height:100%"></min-map>
          </div> -->
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      :title="curTitle"
      v-model="open2"
      width="920px"
      height="900px"
      append-to-body
      class="station-detail-dialog"
    >
      <div class="station-detail-wrapper">
        <!-- 基本信息区域 -->
        <div class="station-form-container">
          <div class="station-title-bar">
            <h3 class="station-subtitle">基本信息</h3>
          </div>
          <div class="station-info">
            <div class="info-item">
              <span class="label">测站名称:</span>
              <span class="value">{{ detailData.stnm }}</span>
            </div>
            <div class="info-item">
              <span class="label">测站编码:</span>
              <span class="value">{{ detailData.stcd }}</span>
            </div>
            <div class="info-item">
              <span class="label">测站类型:</span>
              <span class="value">{{ getSttp(detailData) }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属河流:</span>
              <span class="value">{{ detailData.rvnm || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属流域:</span>
              <span class="value">{{
                getBasicName(detailData.basinId) || "-"
              }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属区县:</span>
              <span class="value">{{ detailData.adnm || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label">站址:</span>
              <span class="value">{{ detailData.stlc || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label">基面名称:</span>
              <span class="value">{{ detailData.dtmnm || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label">基面高程:</span>
              <span class="value">{{
                detailData.dtmel ? detailData.dtmel + " m" : "-"
              }}</span>
            </div>
            <div class="info-item">
              <span class="label">建站年月:</span>
              <span class="value">{{ detailData.esstym || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label">始报年月:</span>
              <span class="value">{{ detailData.bgfrym || "-" }}</span>
            </div>
            <!-- <div class="info-item">
              <span class="label">经度:</span>
              <span class="value">{{ detailData.lgtd || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">纬度:</span>
              <span class="value">{{ detailData.lttd || '-' }}</span>
            </div> -->
            <div class="info-item">
              <span class="label">测站岸别:</span>
              <span class="value">
                <el-tag size="small" type="info" v-if="detailData.stbk == 0"
                  >左岸</el-tag
                >
                <el-tag size="small" type="info" v-if="detailData.stbk == 1"
                  >右岸</el-tag
                >
                <span v-if="detailData.stbk != 0 && detailData.stbk != 1"
                  >-</span
                >
              </span>
            </div>
            <div class="info-item">
              <span class="label">测站方位:</span>
              <span class="value">{{ detailData.stazt || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label">至河口距离:</span>
              <span class="value">{{
                detailData.dstrvm ? detailData.dstrvm + " km" : "-"
              }}</span>
            </div>
            <div class="info-item">
              <span class="label">集水面积:</span>
              <span class="value">{{
                detailData.drna ? detailData.drna + " k㎡" : "-"
              }}</span>
            </div>
          </div>
        </div>

        <!-- 空间信息区域 -->
        <div class="station-form-container">
          <div class="station-title-bar">
            <h3 class="station-subtitle">空间信息</h3>
          </div>
          <div class="station-info">
            <!-- 目前测站的经纬度移除 使用地图的组件来展示 -->
            <div style="width: 100%; height: 300px;">
              <mini-map
              :geom="detailData.geom"
              :showTool="false"
              :icon="getIcon(detailData.sttp)"
              style="width: 100%; height: 100%"
            ></mini-map>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      v-model="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">保 存</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  addStaInfo,
  delStaInfo,
  getAdcdTree,
  getStaInfo,
  selectStaList,
  updateStaInfo,
  selectRvList,
} from "@/api/watershed/ads";
import { onUnmounted, nextTick } from "vue";
import { selectStlyList } from "@/api/watershed/ads";
import MiniMap from "@/components/Map/plugins/drawTool";
import * as turf from "@turf/turf";
defineOptions({
  name: "StationIndex",
});

const { proxy } = getCurrentInstance();

const lyList = ref([]);
const total = ref(0);
let waterAreaList = ref([]);
const open = ref(false);
const open2 = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const curGeom = ref("");
const curTitle = ref("");
const adcdOptions = ref([]); // 行政区树 默认获取一下
const detailData = ref("");
const options = ref([]);
const refreshTable = ref(true);
const riverSelect = ref([]);
const uploadRef = ref("");
import { getToken } from "@/utils/auth";
/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,

  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/system/station/station-sys/excel",
});

const sttpSelects = [
  { label: "雨量站", value: "PP" },
  { label: "河道水文站", value: "ZQ" },
  { label: "河道水位站", value: "ZZ" },
  { label: "水库水文站", value: "RR" },
  { label: "堰闸水文站", value: "DD" },
];

const data = reactive({
  form: {
    geoType: "people",
  },
  queryParams: {
    asNext: 1, // 1 关联查询 ， 0 不关联就查自己的
    pageNum: 1,
    pageSize: 20,
    stnm: "",
    stcd: "",
    addvcd: "",
  },
});
const findNameInArray = (array, basinId) => {
  for (const item of array) {
    const result = findNameById(item.data, basinId);
    if (result) {
      return result;
    }
    if (item.children && item.children.length > 0) {
      const result = findNameInArray(item.children, basinId);
      if (result) {
        return result;
      }
    }
  }
  return "";
};
const findNameById = (data, basinId) => {
  if (!data) return "";

  if (data.basinId === basinId) {
    return data.name;
  }

  if (data.children && data.children.length > 0) {
    for (const child of data.children) {
      const result = findNameById(child.data, basinId);
      if (result) {
        return result;
      }
    }
  }

  return "";
};
const getBasicName = (basicId) => {
  //根据waterareaList 数据格式遍历出basicId 对应的name
  return findNameInArray(waterAreaList.value, basicId);
};
const { queryParams, form } = toRefs(data);

/** 查询流域列表 */
async function getList() {
  loading.value = true;
  selectStaList(queryParams.value).then((res) => {
    lyList.value = res.data.records;
    total.value = res.data.total;
    loading.value = false;
  });
}
getTreeselect();
getRiver();
/** 查询区县下拉树结构 */
function getTreeselect() {
  adcdOptions.value = [];
  getAdcdTree().then((res) => {
    adcdOptions.value = res.data[0].children;
  });
}
/** 查询河流下拉树结构 */
function getRiver() {
  selectRvList({
    pageNum: 1,
    pageSize: 1000,
  }).then((res) => {
    riverSelect.value = convertDataFormat(res.data);
  });
}
function convertDataFormat(data) {
  // 检查是否是数组
  if (!Array.isArray(data)) {
    return null;
  }
  // 对数组中的每个元素进行处理
  return data.map((item) => {
    // 复制 data 属性并移除 children
    const newData = { ...item.data };
    delete newData.children;
    // 递归处理子元素
    if (item.children && item.children.length > 0) {
      newData.children = convertDataFormat(item.children);
    }
    return newData;
  });
}

function getSttp(data) {
  for (let i = 0; i < sttpSelects.length; i++) {
    if (sttpSelects[i].value == data.sttp) {
      return sttpSelects[i].label;
    }
  }
}

const rules = ref({
  stnm: [{ required: true, message: "测站名称不能为空", trigger: "blur" }],
  stcd: [
    { required: true, message: "测站编码不能为空", trigger: "blur" },
    {
      pattern: /^[\d\w\W]{8}$/,
      message: "测站编码为8位",
      trigger: "blur",
    },
  ],
  sttp: [{ required: true, message: "测站类型不能为空", trigger: "change" }],
  riverId: [], // 河流ID的验证规则将在changeSttp函数中动态设置
  addvcd: [{ required: true, message: "所属区县不能为空", trigger: "blur" }],
  dstrvm: [
    {
      pattern: /^\d+(\.\d)?$/,
      message: "至河口距离为非负数字,保留1位小数",
      trigger: "blur",
    },
  ],
  drna: [
    {
      pattern: /^(0|[1-9]\d*)$/,
      message: "集水面积为非负整数",
      trigger: "blur",
    },
  ],
  stazt: [
    {
      pattern: /^([0-9]|[0-9]\d|360)$/,
      message: "测站方位为0～360间整数",
      trigger: "blur",
    },
  ],
});

function changeSttp() {
  console.log(form.value.sttp);
  if (form.value.sttp == "PP" || form.value.sttp == "RR") {
    rules.value = {
      stnm: [{ required: true, message: "测站名称不能为空", trigger: "blur" }],
      stcd: [
        { required: true, message: "测站编码不能为空", trigger: "blur" },
        {
          pattern: /^[\d\w\W]{8}$/,
          message: "测站编码为8位",
          trigger: "blur",
        },
      ],
      sttp: [
        { required: true, message: "测站类型不能为空", trigger: "change" },
      ],
      riverId: [], // 雨量站和水库水文站不需要必填河流
      addvcd: [
        { required: true, message: "所属区县不能为空", trigger: "blur" },
      ],
      dstrvm: [
        {
          pattern: /^\d+(\.\d)?$/,
          message: "至河口距离为非负数字,保留1位小数",
          trigger: "blur",
        },
      ],
      drna: [
        {
          pattern: /^(0|[1-9]\d*)$/,
          message: "集水面积为非负整数",
          trigger: "blur",
        },
      ],
      stazt: [
        {
          pattern: /^([0-9]|[0-9]\d|360)$/,
          message: "测站方位为0～360间整数",
          trigger: "blur",
        },
      ],
    };
  } else {
    rules.value = {
      stnm: [{ required: true, message: "测站名称不能为空", trigger: "blur" }],
      stcd: [
        { required: true, message: "测站编码不能为空", trigger: "blur" },
        {
          pattern: /^[\d\w\W]{8}$/,
          message: "测站编码为8位",
          trigger: "blur",
        },
      ],
      sttp: [
        { required: true, message: "测站类型不能为空", trigger: "change" },
      ],
      riverId: [
        { required: true, message: "所属河流不能为空", trigger: "blur" },
      ],
      addvcd: [
        { required: true, message: "所属区县不能为空", trigger: "blur" },
      ],
      dstrvm: [
        {
          pattern: /^\d+(\.\d)?$/,
          message: "至河口距离为非负数字,保留1位小数",
          trigger: "blur",
        },
      ],
      drna: [
        {
          pattern: /^(0|[1-9]\d*)$/,
          message: "集水面积为非负整数",
          trigger: "blur",
        },
      ],
      stazt: [
        {
          pattern: /^([0-9]|[0-9]\d|360)$/,
          message: "测站方位为0～360间整数",
          trigger: "blur",
        },
      ],
    };
  }
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    addFlag: flag,
    geoType: "people",
  };
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
  reset(true);
  open.value = true;
  title.value = "添加测站";
  // 设置默认验证规则
  nextTick(() => {
    changeSttp();
  });
}
async function showMap(row) {
  let res = await getStaInfo(row.stcd);
  if (res.data) {
    if (res.data.lgtd && res.data.lttd) {
      let lgtd = res.data.lgtd;
      let lttd = res.data.lttd;
      // null 值处理
      let Coordinate = turf.point([lgtd, lttd]);
      res.data.geom = Coordinate;
    }
    detailData.value = res.data;
    open2.value = true;
    curTitle.value = "测站 - " + row.stnm;
  } else {
    proxy.$modal.msgSuccess("没找到详情数据");
  }
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  open.value = true;
  title.value = "修改测站 - " + row.stnm;
  await nextTick();
  let obj = Object.assign({ geoType: "people" }, row);
  delete obj.children;
  delete obj.createBy;
  delete obj.createTime;
  delete obj.updateBy;
  delete obj.updateTime;
  delete obj.remark;
  delete obj.asNext;
  delete obj.pageNum;
  delete obj.pageSize;
  form.value = obj;
  let rg = await getStaInfo(obj.stcd);
  form.value.addvcd = rg.data.addvcd;
  if (
    rg.data.riverStationRelDtoList &&
    rg.data.riverStationRelDtoList.length > 0
  ) {
    form.value.riverId = rg.data.riverStationRelDtoList[0].riverId;
  }

  // 根据测站类型设置验证规则
  changeSttp();
}

function parseKjlx(data) {
  for (let i = 0; i < sttpSelects.length; i++) {
    if (Number(sttpSelects[i].value) === data.lyjb) {
      return sttpSelects[i].label;
    }
  }
}
function handleNodeClick(node) {
  if (!node.children || node.children.length === 0) {
    // 只选中最后一个层级节点
    form.value.addvcd = node.adcd;
    // console.log(node);
  } else {
    // 清空选中
    // form.value.lyCode = '';
    proxy.$modal.msgError("请选择区县");
    nextTick(() => {
      form.value.addvcd = "";
    });
  }
}

function hideMap() {
  open2.value = false;
}
async function handleDetail(row) {
  let res = await getStaInfo(row.id);
  if (res.data) {
    open2.value = true;
    await nextTick();
    curGeom.value = res.data?.geom;
    curTitle.value = row.rvName;
  } else {
    proxy.$modal.msgSuccess("没找到空间数据");
  }
}
/** 提交按钮 */
function submitForm() {
  let basin = form.value.basinId;
  if (!basin && form.value.sttp == "PP") {
    proxy.$modal.msgError("雨量站新增请选择流域");
    return false;
  }
  proxy.$refs["menuRef"].validate((valid) => {
    if (valid) {
      // 只有当测站类型是雨量站或水库水文站时，才需要添加河流ID
      if (
       ( form.value.sttp == "PP" ||
        form.value.sttp == "RR" ) &&
        form.value.riverId
      ) {
        form.value.riverIds = [form.value.riverId];
      }
      // form.value.geom = JSON.stringify(geom)
      if (form.value.addFlag) {
        addStaInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      } else {
        updateStaInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.stnm + '"的数据项?')
    .then(function () {
      return delStaInfo({ stcd: row.stcd });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
function updateBound(geojson) {
  // 提交geojson数据到后台
  console.log(geojson);
  form.value.geom = geojson;
}

function handleSuccess(response) {
  if (response.code == 200) {
    form.value.geom = response.data;
  } else {
    uploadRef.value?.clearFiles();
    proxy.$modal.msgSuccess(response.msg);
  }
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "测站导入";
  upload.open = true;
}
/** 下载模板操作 */
function importTemplate() {
  proxy.download(
    "file/download",
    {
      fileName: "b8b342eb-f951-40a5-8c81-95cd380a1fb9_测站导入.xlsx",
      bucketName: "excel-template",
    },
    `测站导入模板.xlsx`
  );
}
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};
/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}

onMounted(() => {
  window.EventBus.$on("updateBound/update", updateBound);
  getAllWater();
});
const transformDataForTreeSelect = (data) => {
  // 递归地转换数据以匹配 el-tree-select 的需求
  return data.map((item) => ({
    label: item.data.name, // 使用 'name' 属性作为标签
    value: item.data.basinId, // 使用 'basinId' 属性作为值
    children: item.children ? transformDataForTreeSelect(item.children) : [], // 递归转换子节点
  }));
};
const getAllWater = async () => {
  let res = await selectStlyList({ pageNum: 1, pageSize: 999 })
  waterAreaList.value = res.data || []
}

const getIcon = (sttp) => {
  // console.log(sttp)
  if (sttp == 'PP') {
    return [
      {
        icon: 'rain.png',
        label: '雨量站',
      },
    ]
  } else if (sttp == 'RR') {
    return [
      {
        icon: 'reservoir.png',
        label: '水库水文站',
      },
    ]
  } else if (sttp == 'ZZ') {
    return [
      {
        icon: 'river.png',
        label: '河道水位站',
      },
    ]
  } else if (sttp == 'ZQ') {
    return [
      {
        icon: 'river.png',
        label: '河道水文站',
      },
    ]
  } else {
    return []
  }
}
onUnmounted(() => {

})

getList();
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

/* 测站详情对话框样式 */
:deep(.station-detail-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    margin-right: 0;
  }

  .el-dialog__body {
    padding: 20px 24px;
    max-height: 75vh;
    overflow-y: auto;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 14px 20px;
  }
}

.station-detail-wrapper {
  padding: 0;
}

.station-form-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.station-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.station-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  padding: 16px 0 8px;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: #409eff;
  }
}

.station-info {
  display: flex;
  flex-wrap: wrap;
  padding: 8px 16px;
  gap: 16px;

  .info-item {
    min-width: 250px;
    flex: 1 0 calc(33.33% - 16px);
    display: flex;
    align-items: center;

    @media (max-width: 1200px) {
      flex: 1 0 calc(50% - 16px);
    }

    @media (max-width: 768px) {
      flex: 1 0 100%;
    }

    .label {
      width: 90px;
      color: #606266;
      font-weight: 500;
      margin-right: 8px;
      text-align: right;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      font-size: 14px;
    }
  }
}
</style>
