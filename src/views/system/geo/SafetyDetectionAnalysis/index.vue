<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import * as echarts from "echarts";
import moment from "moment";
import { mockSluiceData, mockMonitoringData } from "./mock";

defineOptions({
  name: "SafetyDetectionAnalysis",
});

// 查询参数
const queryParams = ref({
  gateName: "", // 水闸名称
});

// 水闸树数据
const sluiceTreeData = ref([]);
// 选中的监测项
const selectedItems = ref([]);
// 表格数据
const tableData = ref([]);
// 图表实例数组
const chartInstances = ref([]);
// 加载状态
const loading = ref(false);
// 树组件引用
const treeRef = ref(null);

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 获取水闸树数据
const getSluiceTreeData = () => {
  loading.value = true;
  // 模拟接口调用
  setTimeout(() => {
    let filteredData = [...mockSluiceData];

    if (queryParams.value.gateName) {
      filteredData = filteredData.filter((item) =>
        item.name.includes(queryParams.value.gateName)
      );
    }

    sluiceTreeData.value = filteredData;
    loading.value = false;
  }, 300);
};

// 查询按钮
const handleQuery = () => {
  getSluiceTreeData();
};

// 添加对selectedItems的监听
watch(
  selectedItems,
  (newVal) => {
    if (newVal.length >= 2 && newVal.length <= 3) {
      // 使用nextTick确保DOM已更新
      nextTick(() => {
        generateCorrelationData();
      });
    } else {
      tableData.value = [];

      // 清除所有图表
      clearAllCharts();

      // 清空图表容器
      const chartContainer = document.getElementById(
        "correlationChartContainer"
      );
      if (chartContainer) {
        chartContainer.innerHTML = "";
      }
    }
  },
  { deep: true }
);

// 清除所有图表
const clearAllCharts = () => {
  if (chartInstances.value && chartInstances.value.length > 0) {
    chartInstances.value.forEach((chart) => {
      if (chart && !chart.isDisposed()) {
        chart.dispose();
      }
    });
    chartInstances.value = [];
  }
};

// 监测项选择变化 - 此函数已不再使用
// const handleCheckChange = (checkedNode, checkedStatus) => {
//   console.log("节点状态变化:", checkedNode, checkedStatus);

//   // 获取所有选中的节点（仅获取item类型的监测项）
//   const checkedNodes = treeRef.value.getCheckedNodes();
//   const filteredNodes = checkedNodes.filter((node) => node.type === "item");

//   // 当前正在尝试选中节点
//   if (checkedStatus && checkedNode.type === "item") {
//     // 如果已经有3个监测项被选中且当前节点不在已选中列表中
//     if (filteredNodes.length > 3) {
//       ElMessage.warning("最多只能选择3个监测项");
//       // 取消当前节点的选中状态
//       treeRef.value.setChecked(checkedNode.id, false, false);
//       return;
//     }
//   }

//   // 更新选中项
//   selectedItems.value = treeRef.value.getCheckedNodes().filter((node) => node.type === "item");
//   console.log(selectedItems.value, "更新后的选中项");
// };

// 处理自定义复选框变化
const handleNodeCheckChange = (data, checked) => {
  // 后续可以修改 可以不使用 treeRef.value 上的方法
  console.log("节点状态变化:", data, checked);

  // 如果当前要选中节点
  if (checked) {
    // 获取当前选中的节点（不含正在选中的）
    const currentChecked = treeRef.value
      .getCheckedNodes()
      .filter((node) => node.type === "item");

    // 检查是否已经选中了3个节点
    if (currentChecked.length >= 3) {
      ElMessage.warning("最多只能选择3个监测项");
      return; // 不执行选中操作
    }
  }

  // 设置节点的选中状态
  treeRef.value.setChecked(data.id, checked, false);

  // 更新选中项
  selectedItems.value = treeRef.value
    .getCheckedNodes()
    .filter((node) => node.type === "item");
  console.log(selectedItems.value, "更新后的选中项");
};

// 生成关联数据
const generateCorrelationData = () => {
  // 确保选择了至少2个监测项
  if (selectedItems.value.length < 2) return;

  // 获取选中的监测项数据
  const itemsData = selectedItems.value.map((item) => {
    const data = mockMonitoringData[item.id] || [];
    return {
      id: item.id,
      name: item.name,
      unit: item.unit,
      data: data,
    };
  });

  // 处理表格数据
  const tableRows = [];
  // 假设所有数据的时间点都相同，以第一个监测项的时间序列为基准
  if (itemsData[0] && itemsData[0].data) {
    itemsData[0].data.forEach((item, index) => {
      const row = {
        time: item.time,
      };

      // 填充每个监测项的值
      itemsData.forEach((itemData) => {
        if (itemData.data[index]) {
          row[itemData.id] = itemData.data[index].value;
        } else {
          row[itemData.id] = null;
        }
      });

      tableRows.push(row);
    });
  }

  tableData.value = tableRows;
  pagination.value.total = tableRows.length;

  // 生成散点图
  renderScatterChart(itemsData);
};

// 渲染散点图
const renderScatterChart = (itemsData) => {
  if (itemsData.length < 2) return;

  // 清除现有图表
  clearAllCharts();

  // 创建图表容器
  const chartContainer = document.getElementById("correlationChartContainer");
  if (!chartContainer) return;

  chartContainer.innerHTML = "";

  // 图表颜色方案
  const colors = [
    ["#5470c6", "#91cc75", "#fac858"],
    ["#ee6666", "#73c0de", "#3ba272"],
    ["#fc8452", "#9a60b4", "#ea7ccc"],
  ];

  console.log(itemsData, "itemsData");

  // 为每个监测项组合创建图表
  for (let i = 0; i < itemsData.length; i++) {
    for (let j = i + 1; j < itemsData.length; j++) {
      // 创建新的图表DOM元素
      const chartDiv = document.createElement("div");
      chartDiv.id = `correlationChart-${i}-${j}`;
      chartDiv.className = "correlation-chart";
      chartContainer.appendChild(chartDiv);

      // 获取两组监测项数据
      const series1 = itemsData[i];
      const series2 = itemsData[j];

      // 准备数据
      const series1Data = [];
      const series2Data = [];

      // 假设两个数据系列有相同的时间点
      series1.data.forEach((item, index) => {
        if (series2.data[index]) {
          series1Data.push([item.time, item.value]);
          series2Data.push([item.time, series2.data[index].value]);
        }
      });

      // 计算这两个系列的相关系数
      const coefficient = calculateTwoSeriesCorrelation(
        series1.data,
        series2.data
      );

      // 确保DOM已经渲染完成再初始化图表
      nextTick(() => {
        try {
          // 获取当前DOM元素
          const currentChartDiv = document.getElementById(
            `correlationChart-${i}-${j}`
          );
          if (!currentChartDiv) return;

          // 初始化图表
          const chart = echarts.init(currentChartDiv);
          chartInstances.value.push(chart);

          const option = {
            title: {
              text: `${series1.name} ~ ${series2.name}相关系数: ${coefficient}`,
              textStyle: {
                fontSize: 14,
                fontWeight: "normal",
                color: "#000",
              },
              left: "center",
            },
            tooltip: {
              trigger: "axis",
              formatter: function (params) {
                const date = new Date(params[0].value[0]);
                const formattedDate = moment(date).format(
                  "YYYY-MM-DD HH:mm:ss"
                );
                let result = `${formattedDate}<br/>`;
                params.forEach((param) => {
                  result += `${param.seriesName}: ${param.value[1]} <br/>`;
                });
                return result;
              },
              axisPointer: {
                type: "cross",
              },
            },
            grid: {
              top: "10%",
              left: "10%",
              right: "10%",
              bottom: "15%",
            },
            xAxis: {
              type: "time",
              name: `${series2.name}(${series2.unit})`,
              nameLocation: "middle",
              nameGap: 30,
              axisLabel: {
                formatter: function (value) {
                  const date = new Date(value);
                  return moment(date).format("MM-DD HH:mm");
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dashed",
                },
              },
            },
            yAxis: {
              type: "value",
              name: `${series1.name}(${series1.unit})`,
              nameLocation: "middle",
              nameGap: 30,
              splitLine: {
                lineStyle: {
                  type: "dashed",
                },
              },
            },
            series: [
              {
                name: `${series1.name}(${series1.unit})`,
                type: "line",
                data: series1Data,
                symbol: "none",
                smooth: true,
                itemStyle: {
                  color: colors[i % 3][0],
                },
              },
              {
                name: `${series2.name}(${series2.unit})`,
                type: "line",
                data: series2Data,
                symbol: "none",
                smooth: true,
                itemStyle: {
                  color: colors[j % 3][1],
                },
              },
            ],
          };

          chart.setOption(option);
          chart.resize(); // 强制重新计算大小
        } catch (error) {
          console.error("图表初始化失败:", error);
        }
      });
    }
  }
};

// 计算两个系列的相关系数
const calculateTwoSeriesCorrelation = (series1, series2) => {
  // 确保数据长度相同
  const minLength = Math.min(series1.length, series2.length);

  if (minLength < 2) {
    return "N/A";
  }

  // 提取数值
  const x = series1.slice(0, minLength).map((item) => item.value);
  const y = series2.slice(0, minLength).map((item) => item.value);

  // 计算均值
  const xMean = x.reduce((sum, val) => sum + val, 0) / x.length;
  const yMean = y.reduce((sum, val) => sum + val, 0) / y.length;

  // 计算相关系数的分子和分母
  let numerator = 0;
  let denominator1 = 0;
  let denominator2 = 0;

  for (let i = 0; i < x.length; i++) {
    const xDiff = x[i] - xMean;
    const yDiff = y[i] - yMean;

    numerator += xDiff * yDiff;
    denominator1 += xDiff * xDiff;
    denominator2 += yDiff * yDiff;
  }

  // 相关系数计算
  const correlation = numerator / Math.sqrt(denominator1 * denominator2);

  // 保留三位小数
  return correlation.toFixed(3);
};

// 组件卸载前清理资源
onUnmounted(() => {
  // 清除所有图表
  clearAllCharts();

  // 移除resize事件监听
  window.removeEventListener("resize", handleResize);
});

// 窗口大小变化处理函数
const handleResize = () => {
  chartInstances.value.forEach((chart) => {
    chart && !chart.isDisposed() && chart.resize();
  });
};

// 当窗口大小变化时重新渲染图表
window.addEventListener("resize", handleResize);

// 生命周期钩子
onMounted(() => {
  getSluiceTreeData();
});
</script>

<template>
  <div class="app-container">
    <div class="content">
      <div class="left-tree" v-loading="loading">
        <div class="form-box">
          <el-input
            v-model="queryParams.gateName"
            placeholder="请输入水闸名称"
            clearable
            @keyup.enter="handleQuery"
          />
          <el-button type="primary" icon="Search" @click="handleQuery"
            >查询</el-button
          >
        </div>
        <el-tree
          :data="sluiceTreeData"
          :props="{
            label: 'name',
            children: 'children',
          }"
          node-key="id"
          default-expand-all
          :show-checkbox="false"
          ref="treeRef"
        >
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <span>
                <el-checkbox
                  v-if="data.type === 'item'"
                  :model-value="node.checked"
                  @change="(val) => handleNodeCheckChange(data, val)"
                ></el-checkbox>
                {{ node.label }}
              </span>
              <span v-if="data.unit">({{ data.unit }})</span>
            </span>
          </template>
        </el-tree>
      </div>

      <div class="right-table">
        <div class="chart-container" v-show="selectedItems.length >= 2">
          <!-- 图表区域 -->
          <div id="correlationChartContainer" class="charts-wrapper"></div>
        </div>

        <!-- 数据表格 -->
        <template v-if="tableData.length > 0">
          <el-table :data="tableData" stripe style="width: 100%">
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
            />
            <el-table-column prop="time" label="时间" align="center" />
            <el-table-column
              v-for="item in selectedItems"
              :key="item.id"
              :prop="item.id.toString()"
              :label="item.name + (item.unit ? `(${item.unit})` : '')"
              align="center"
            />
          </el-table>

          <!-- 分页 -->
          <Pagination
            :total="pagination.total"
            v-model:page="pagination.pageNum"
            v-model:limit="pagination.pageSize"
            @pagination="() => {}"
          />
        </template>

        <!-- 空状态提示 -->
        <div class="w-full h-full flex-c" v-if="selectedItems.length < 2">
          <el-empty
            description="请在左侧选择2-3个监测项进行相关性分析"
            :image-size="220"
          ></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.form-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .el-input {
    margin-right: 10px;
  }
}

.left-tree {
  width: 300px;
  margin-right: 20px;
  border-right: 1px solid #e2e8f0;
  padding-right: 10px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
}

/* 图表相关样式 */
.charts-wrapper {
  width: 100%;
  height: 300px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

:deep(.correlation-chart) {
  flex: 1;
  height: 300px;
}
</style>
