// Mock数据 - 水闸信息
export const mockSluiceData = [
  {
    id: 1,
    name: "龙湾水闸",
    children: [
      {
        id: 101,
        name: "降雨量",
        type: "item",
      },
      {
        id: 102,
        name: "闸上水位",
        type: "upperLevel",
      },
      {
        id: 103,
        name: "闸下水位",
        type: "lowerLevel",
      },
      {
        id: 104,
        name: "安全监测断面1",
        type: "section",
        children: [
          {
            id: 1041,
            name: "监测点A",
            type: "point",
            children: [
              {
                id: 10411,
                name: "渗流量",
                unit: "m³/min",
                type: "item",
              },
              {
                id: 10412,
                name: "渗透压力",
                unit: "kPa",
                type: "item",
              },
            ],
          },
          {
            id: 1042,
            name: "监测点B",
            type: "point",
            children: [
              {
                id: 10421,
                name: "表面水平位移",
                unit: "mm",
                type: "item",
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: 2,
    name: "西溪水闸",
    children: [
      {
        id: 201,
        name: "降雨量",
        type: "rainfall",
      },
      {
        id: 202,
        name: "闸上水位",
        type: "upperLevel",
      },
      {
        id: 203,
        name: "安全监测断面1",
        type: "section",
        children: [
          {
            id: 2031,
            name: "监测点C",
            type: "point",
            children: [
              {
                id: 20311,
                name: "表面垂直位移",
                unit: "mm",
                type: "item",
              },
            ],
          },
        ],
      },
    ],
  },
];

// Mock监测数据
export const mockMonitoringData = {
  // 龙湾水闸 - 降雨量
  101: [
    { time: "2025-05-13 09:00:00", value: 1.2 },
    { time: "2025-05-13 10:00:00", value: 1.3 },
    { time: "2025-05-13 11:00:00", value: 1.4 },
    { time: "2025-05-13 12:00:00", value: 1.5 },
    { time: "2025-05-13 13:00:00", value: 1.6 },
    { time: "2025-05-13 14:00:00", value: 1.7 },
  ],
  // 龙湾水闸 - 渗流量
  10411: [
    { time: "2025-05-13 09:00:00", value: 4.2 },
    { time: "2025-05-13 10:00:00", value: 4.3 },
    { time: "2025-05-13 11:00:00", value: 4.4 },
    { time: "2025-05-13 12:00:00", value: 4.5 },
    { time: "2025-05-13 13:00:00", value: 4.6 },
    { time: "2025-05-13 14:00:00", value: 4.7 },
  ],
  // 龙湾水闸 - 渗透压力
  10412: [
    { time: "2025-05-13 09:00:00", value: 6.2 },
    { time: "2025-05-13 10:00:00", value: 6.3 },
    { time: "2025-05-13 11:00:00", value: 6.4 },
    { time: "2025-05-13 12:00:00", value: 6.5 },
    { time: "2025-05-13 13:00:00", value: 6.6 },
    { time: "2025-05-13 14:00:00", value: 6.7 },
  ],
  // 龙湾水闸 - 表面水平位移
  10421: [
    { time: "2025-05-13 09:00:00", value: 5.2 },
    { time: "2025-05-13 10:00:00", value: 5.3 },
    { time: "2025-05-13 11:00:00", value: 5.5 },
    { time: "2025-05-13 12:00:00", value: 5.8 },
    { time: "2025-05-13 13:00:00", value: 6.1 },
    { time: "2025-05-13 14:00:00", value: 6.5 },
  ],
  // 西溪水闸 - 表面垂直位移
  20311: [
    { time: "2025-05-13 09:00:00", value: 7.2 },
    { time: "2025-05-13 10:00:00", value: 7.3 },
    { time: "2025-05-13 11:00:00", value: 7.4 },
    { time: "2025-05-13 12:00:00", value: 7.5 },
    { time: "2025-05-13 13:00:00", value: 7.6 },
    { time: "2025-05-13 14:00:00", value: 7.7 },
  ],
};