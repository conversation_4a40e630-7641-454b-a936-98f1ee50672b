<script setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { deepClone, getTypeLabel } from "@/utils";
import {
  CUTOFF_WALL_TYPE_OPTIONS,
  MONITOR_ITEM_OPTIONS,
  MONITOR_POINT_TYPE_OPTIONS,
} from "@/views/system/geo/utils/enum";
import {
  fetchSafetyMonitoringSectionList,
  saveSafetyMonitoringSection,
  deleteSafetyMonitoringSection,
  fetchSafetyMonitoringStationList,
  fetchSafetyMonitoringSectionPointList,
} from "@/api/watershed/ads";
import rules from "./utils/rules";

defineOptions({
  name: "SafetyMonitoringSection",
});

// 查询参数
const queryParams = ref({
  stationName: "", // 测站名称
  stationCode: "", // 测站编码
  sectionCode: "", // 断面编号
});

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 详情对话框
const detailDialog = ref(false);
const currentDetail = ref({});

// 监测点列表
const monitorPointList = ref([]);

// 新增/编辑对话框
const formDialog = ref(false);
const formTitle = ref("");
const formType = ref("add"); // add-新增 edit-编辑

// 表单数据
const formData = ref({
  stationCode: "", // 测站编码
  sectionName: "", // 断面名称
  sectionCode: "", // 断面编号
  pileNumber: "", // 桩号
  wallType: "", // 防渗墙类型
  bottomWidth: "", // 断面底宽
  bottomElevation: "", // 断面底高程
  averageHeight: "", // 断面平均高度
  topStartDistance: "", // 断面顶起点距
  topWidth: "", // 断面顶宽
});

// 表单引用
const formRef = ref(null);

// 安全监测站列表
const stationOptions = ref([]);

// 获取安全监测站列表
const getStationOptions = async () => {
  const res = await fetchSafetyMonitoringStationList({
    pageNum: 1,
    pageSize: 1000,
    name: "",
    stationCode: "",
    district: "",
  });
  if (res.code === 200) {
    stationOptions.value = res.rows;
  }
};

// 获取列表数据
const getList = async () => {
  loading.value = true;

  const res = await fetchSafetyMonitoringSectionList({
    ...queryParams.value,
    pageNum: pagination.value.pageNum,
    pageSize: pagination.value.pageSize,
  });
  if (res.code === 200) {
    tableData.value = res.rows;
    pagination.value.total = res.total;
  }
  loading.value = false;
};

// 查询按钮
const handleQuery = () => {
  pagination.value.pageNum = 1;
  getList();
};

// 重置按钮
const handleReset = () => {
  queryParams.value = {
    stationName: "",
    stationCode: "",
    sectionCode: "",
  };
  handleQuery();
};

// 查看详情
const handleDetail = async (row) => {
  formTitle.value = "安全监测断面详情 - " + row.sectionCode;
  currentDetail.value = { ...row };
  const res = await fetchSafetyMonitoringSectionPointList(row.id);
  if (res.code === 200) {
    monitorPointList.value = res.rows || [];
  }
  detailDialog.value = true;
};

// 新增按钮
const handleAdd = () => {
  resetForm();
  formTitle.value = "新增安全监测断面";
  formType.value = "add";
  formDialog.value = true;
};

// 编辑按钮
const handleEdit = (row) => {
  resetForm();
  formTitle.value = "编辑安全监测断面";
  formType.value = "edit";
  formData.value = { ...row };
  formDialog.value = true;
};

// 删除按钮
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确认要删除该安全监测断面[${row.sectionCode}]吗？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(async () => {
    const res = await deleteSafetyMonitoringSection(row.id);
    if (res.code === 200) {
      ElMessage.success("删除成功");
      getList();
    }
  });
};

// 重置表单
const resetForm = () => {
  formData.value = {
    stationCode: "",
    sectionName: "",
    sectionCode: "",
    pileNumber: "",
    wallType: "",
    bottomWidth: "",
    bottomElevation: "",
    averageHeight: "",
    topStartDistance: "",
    topWidth: "",
  };
  if (formRef.value) {
    formRef.value.resetFields();
  }
  monitorPointList.value = [];
};

// 提交表单
const submitForm = () => {
  const copyFormData = deepClone(formData.value);
  // 移除stationName字段
  delete copyFormData.stationName;
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (formType.value === "add") {
        const res = await saveSafetyMonitoringSection(copyFormData);
        if (res.code === 200) {
          ElMessage.success("添加成功");
          formDialog.value = false;
          getList();
        }
      } else {
        const res = await saveSafetyMonitoringSection(copyFormData);
        if (res.code === 200) {
          ElMessage.success("更新成功");
          formDialog.value = false;
          getList();
        }
      }
      formDialog.value = false;
      getList();
    }
  });
};

onMounted(() => {
  getList();
  getStationOptions();
});
</script>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="测站名称" prop="stationCode">
        <el-select
          v-model="queryParams.stationCode"
          placeholder="请选择安全监测站"
          class="w-full"
          clearable
          filterable
        >
          <el-option
            v-for="item in stationOptions"
            :key="item.stationCode"
            :label="item.stationName"
            :value="item.stationCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="断面编号" prop="sectionCode">
        <el-input
          v-model="queryParams.sectionCode"
          placeholder="请输入断面编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button @click="handleReset" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="content content-table">
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd"
          >新增</el-button
        >
      </div>
      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableData" stripe>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="stationName" label="测站名称" align="center" />
        <el-table-column prop="sectionName" label="断面名称" align="center" />
        <el-table-column prop="stationCode" label="测站编码" align="center" />
        <el-table-column prop="sectionCode" label="断面编号" align="center" />
        <el-table-column prop="pileNumber" label="桩号" align="center" />
        <el-table-column prop="wallType" label="防渗墙类型" align="center">
          <template #default="scope">
            {{ getTypeLabel(scope.row.wallType, CUTOFF_WALL_TYPE_OPTIONS) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="bottomWidth"
          label="断面底宽(m)"
          align="center"
        />
        <el-table-column
          prop="bottomElevation"
          label="断面底高程(m)"
          align="center"
        />
        <el-table-column prop="topWidth" label="断面顶宽(m)" align="center" />
        <el-table-column
          prop="averageHeight"
          label="断面平均高度(m)"
          align="center"
        />
        <el-table-column
          prop="topStartDistance"
          label="断面顶起点距(m)"
          align="center"
        />
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="View"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        v-model:page="pagination.pageNum"
        v-model:limit="pagination.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="formDialog"
      width="700px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="安全监测站" prop="stationCode">
              <el-select
                v-model="formData.stationCode"
                placeholder="请选择安全监测站"
                class="w-full"
                clearable
                :disabled="formType === 'edit'"
              >
                <el-option
                  v-for="item in stationOptions"
                  :key="item.stationCode"
                  :label="item.stationName"
                  :value="item.stationCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="断面名称" prop="sectionName">
              <el-input
                v-model="formData.sectionName"
                placeholder="请输入断面名称"
                :maxlength="18"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="断面编号" prop="sectionCode">
              <el-input
                v-model="formData.sectionCode"
                placeholder="请输入断面编号"
                :disabled="formType === 'edit'"
                :maxlength="18"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="桩号" prop="pileNumber">
              <el-input
                v-model="formData.pileNumber"
                placeholder="请输入桩号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="防渗墙类型" prop="wallType">
              <el-select
                v-model="formData.wallType"
                placeholder="请选择防渗墙类型"
                class="w-full"
                clearable
              >
                <el-option
                  v-for="item in CUTOFF_WALL_TYPE_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="断面底宽(m)" prop="bottomWidth">
              <el-input
                v-model="formData.bottomWidth"
                placeholder="请输入断面底宽"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="断面底高程(m)" prop="bottomElevation">
              <el-input
                v-model="formData.bottomElevation"
                placeholder="请输入断面底高程"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="断面平均高度(m)" prop="averageHeight">
              <el-input
                v-model="formData.averageHeight"
                placeholder="请输入断面平均高度"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="断面顶起点距(m)" prop="topStartDistance">
              <el-input
                v-model="formData.topStartDistance"
                placeholder="请输入断面顶起点距"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="断面顶宽(m)" prop="topWidth">
              <el-input
                v-model="formData.topWidth"
                placeholder="请输入断面顶宽"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="formDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="detailDialog"
      width="800px"
      append-to-body
      class="detail-dialog"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="安全监测站名称">{{
          currentDetail.stationName
        }}</el-descriptions-item>
        <el-descriptions-item label="安全监测站编码">{{
          currentDetail.stationCode
        }}</el-descriptions-item>
        <el-descriptions-item label="断面名称">{{
          currentDetail.sectionName
        }}</el-descriptions-item>
        <el-descriptions-item label="断面编号">{{
          currentDetail.sectionCode
        }}</el-descriptions-item>
        <el-descriptions-item label="桩号">{{
          currentDetail.pileNumber || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="防渗墙类型">{{
          getTypeLabel(currentDetail.wallType, CUTOFF_WALL_TYPE_OPTIONS) || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="断面底宽(m)">{{
          currentDetail.bottomWidth || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="断面底高程(m)">{{
          currentDetail.bottomElevation || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="断面平均高度(m)">{{
          currentDetail.averageHeight || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="断面顶起点距(m)">{{
          currentDetail.topStartDistance || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="断面顶宽(m)">{{
          currentDetail.topWidth || "--"
        }}</el-descriptions-item>
      </el-descriptions>

      <!-- 监测点列表 -->
      <div class="monitor-points-section">
        <h3>监测点列表</h3>
        <el-table :data="monitorPointList" border class="w-full">
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column label="测点类型" align="center">
            <template #default="scope">
              {{
                getTypeLabel(scope.row.pointType, MONITOR_POINT_TYPE_OPTIONS)
              }}
            </template>
          </el-table-column>
          <el-table-column prop="pointCode" label="测点编码" align="center" />
          <el-table-column label="监测项目" align="center">
            <template #default="scope">
              {{ getTypeLabel(scope.row.monitorItem, MONITOR_ITEM_OPTIONS) }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceCode" label="设备编码" align="center" />
          <el-table-column prop="axisDistance" label="轴距(m)" align="center" />
          <el-table-column prop="elevation" label="高程(m)" align="center" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.monitor-points-section {
  margin-top: 20px;

  h3 {
    font-size: 16px;
    margin-bottom: 15px;
    font-weight: 500;
  }
}

:deep(.detail-dialog) {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px 24px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
      border-radius: 3px;
    }
  }
}
</style>
