<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getTypeLabel } from "@/utils";
import {
  MONITOR_ITEM_OPTIONS,
  MONITOR_POINT_TYPE_OPTIONS,
} from "@/views/system/geo/utils/enum";
import {
  fetchSafetyMonitoringEquipmentList,
  saveSafetyMonitoringEquipment,
  deleteSafetyMonitoringEquipment,
  fetchSafetyMonitoringStationList,
  fetchSafetyMonitoringSectionList,
  fetchSafetyMonitoringPointList,
} from "@/api/watershed/ads";
import { deepClone } from "@/utils";

defineOptions({
  name: "SeepagePressurePoint",
});

// 查询参数
const queryParams = ref({
  stationName: "", // 测站名称
  stationCode: "", // 测站编码
  sectionCode: "", // 断面编号
  pointCode: "", // 测点编码
  deviceCode: "", // 设备编码
});

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 详情对话框
const detailDialog = ref(false);
const currentDetail = ref({});

// 新增/编辑对话框
const formDialog = ref(false);
const formTitle = ref("");
const formType = ref("add"); // add-新增 edit-编辑

// 安全监测站选项
const stationOptions = ref([]);
// 断面选项
const sectionOptions = ref([]);
// 测点选项
const pointOptions = ref([]);

// 表单数据
const formData = ref({
  stationCode: "", // 安全监测站ID
  stationName: "", // 安全监测站名称
  sectionCode: "", // 断面编号
  pointCode: "", // 测点编码
  instrumentCode: "", // 仪器编号
  longitude: "", // 经度
  latitude: "", // 纬度
  pipeOutletElevation: "", // 管口高程
  pipeBottomElevation: "", // 管底高程
  installElevation: "", // 安装高程
  axisDistance: "", // 轴距
  waterDepthThreshold: "", // 水深阈值
  remarks: "", // 备注
  deviceCode: "", // 设备编码
  monitorItem: "", // 监测项目
  measureUnit: "", // 测量单位
  thresholdValue: "", // 测量阈值
  measureAccuracy: "", // 测量精度
  installDate: "", // 安装日期
  measureDate: "", // 测定日期
});

// 表单规则
const rules = reactive({
  stationCode: [
    { required: true, message: "安全监测站不能为空", trigger: "change" },
  ],
  sectionCode: [
    { required: true, message: "断面编号不能为空", trigger: "change" },
  ],
  pointCode: [
    { required: true, message: "测点编码不能为空", trigger: "blur" },
    { min: 8, max: 8, message: "测点编码必须为8位字符", trigger: "blur" },
  ],
  deviceCode: [
    { required: true, message: "设备编码不能为空", trigger: "blur" },
    { min: 0, max: 25, message: "设备编码必须为25位字符", trigger: "blur" },
  ],
  monitorItem: [
    { required: true, message: "监测项目不能为空", trigger: "change" },
  ],
  thresholdValue: [],
  measureAccuracy: [],
  installDate: [],
  measureDate: [],
  remarks: [{ max: 100, message: "备注不能超过100个字", trigger: "blur" }],
});

// 表单引用
const formRef = ref(null);

// 获取列表数据
const getList = () => {
  loading.value = true;
  fetchSafetyMonitoringEquipmentList({
    pageNum: pagination.value.pageNum,
    pageSize: pagination.value.pageSize,
    ...queryParams.value,
  })
    .then((res) => {
      tableData.value = res.rows;
      pagination.value.total = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
};

// 查询按钮
const handleQuery = () => {
  pagination.value.pageNum = 1;
  getList();
};

// 重置按钮
const handleReset = () => {
  queryParams.value = {
    stationName: "",
    stationCode: "",
    sectionCode: "",
    pointCode: "",
    deviceCode: "",
  };
  handleQuery();
};

// 查看详情
const handleDetail = (row) => {
  formTitle.value =
    "安全监测设备 - " +
    row.stationName +
    " - " +
    row.sectionName +
    " - " +
    row.pointCode;
  currentDetail.value = { ...row };
  detailDialog.value = true;
};

// 加载监测站选项
const loadStationOptions = async () => {
  const res = await fetchSafetyMonitoringStationList();
  if (res.code === 200) {
    stationOptions.value = res.rows;
  }
};

// 监测站变化时更新断面选项
const handleStationChange = async (stationCode) => {
  if (!stationCode) {
    sectionOptions.value = [];
    formData.value.sectionCode = "";
    pointOptions.value = [];
    formData.value.pointCode = "";
    return;
  }
  const res = await fetchSafetyMonitoringSectionList({
    stationCode: stationCode,
  });
  if (res.code === 200) {
    sectionOptions.value = res.rows;
  }
  formData.value.sectionCode = "";
  pointOptions.value = [];
  formData.value.pointCode = "";
};

// 断面变化时更新测点选项
const handleSectionChange = async (sectionCode) => {
  if (!sectionCode) {
    pointOptions.value = [];
    formData.value.pointCode = "";
    return;
  }
  const res = await fetchSafetyMonitoringPointList({ sectionCode });
  if (res.code === 200) {
    pointOptions.value = res.rows || [];
  } else {
    pointOptions.value = [];
  }
  formData.value.pointCode = "";
};

// 新增按钮
const handleAdd = () => {
  resetForm();
  formTitle.value = "新增安全监测点";
  formType.value = "add";
  formDialog.value = true;
};

// 编辑按钮
const handleEdit = async (row) => {
  resetForm();
  formTitle.value = "编辑安全监测点";
  formType.value = "edit";

  // 1. 先赋值stationCode
  formData.value.stationCode = row.stationCode;

  // 2. 加载断面选项
  await handleStationChange(row.stationCode);
  // 3. 赋值sectionCode
  formData.value.sectionCode = row.sectionCode;

  // 4. 加载测点选项
  await handleSectionChange(row.sectionCode);
  // 5. 赋值pointCode
  formData.value.pointCode = row.pointCode;

  // 6. 赋值其他字段
  formData.value = {
    ...formData.value,
    ...row,
    stationCode: row.stationCode,
    sectionCode: row.sectionCode,
    pointCode: row.pointCode,
  };

  formDialog.value = true;
};

// 删除按钮
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认要删除该渗压监测点[${row.pointCode}]吗？`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await deleteSafetyMonitoringEquipment(row.id);
    if (res.code === 200) {
      ElMessage.success("删除成功");
      getList();
    }
  });
};

// 重置表单
const resetForm = () => {
  formData.value = {
    stationCode: "", // 安全监测站ID
    stationName: "", // 安全监测站名称
    sectionCode: "", // 断面编号
    pointCode: "", // 测点编码
    instrumentCode: "", // 仪器编号
    longitude: "", // 经度
    latitude: "", // 纬度
    pipeOutletElevation: "", // 管口高程
    pipeBottomElevation: "", // 管底高程
    installElevation: "", // 安装高程
    axisDistance: "", // 轴距
    waterDepthThreshold: "", // 水深阈值
    remarks: "", // 备注
    deviceCode: "", // 设备编码
    monitorItem: "", // 监测项目
    measureUnit: "", // 测量单位
    thresholdValue: "", // 测量阈值
    measureAccuracy: "", // 测量精度
    installDate: "", // 安装日期
    measureDate: "", // 测定日期
  };

  sectionOptions.value = [];
  // 重置测点选项
  pointOptions.value = [];

  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 提交表单
const submitForm = () => {
  const copyFormData = deepClone(formData.value);
  delete copyFormData.stationName;
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (formType.value === "add") {
        const res = await saveSafetyMonitoringEquipment(copyFormData);
        if (res.code === 200) {
          ElMessage.success("添加成功");
          formDialog.value = false;
          getList();
        }
      } else {
        const res = await saveSafetyMonitoringEquipment(copyFormData);
        if (res.code === 200) {
          ElMessage.success("更新成功");
          formDialog.value = false;
          getList();
        }
      }
    }
  });
};

onMounted(() => {
  getList();
  loadStationOptions();
});
</script>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="测站名称" prop="stationCode">
        <el-select
          v-model="queryParams.stationCode"
          placeholder="请选择安全监测站"
          clearable
          class="w-full"
        >
          <el-option
            v-for="item in stationOptions"
            :key="item.id"
            :label="item.stationName"
            :value="item.stationCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="测点类型" prop="pointType">
        <el-select
          v-model="queryParams.pointType"
          placeholder="请选择测点类型"
          clearable
          class="w-full"
          filterable
        >
          <el-option
            v-for="item in MONITOR_POINT_TYPE_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="断面编号" prop="sectionCode">
        <el-input
          v-model="queryParams.sectionCode"
          placeholder="请输入断面编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测点编码" prop="pointCode">
        <el-input
          v-model="queryParams.pointCode"
          placeholder="请输入测点编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备编码" prop="deviceCode">
        <el-input
          v-model="queryParams.deviceCode"
          placeholder="请输入设备编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button @click="handleReset" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="content content-table">
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd"
          >新增</el-button
        >
      </div>
      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableData" stripe>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="stationName" label="测站名称" align="center" />
        <el-table-column prop="stationCode" label="测站编码" align="center" />
        <el-table-column prop="sectionName" label="断面名称" align="center" />
        <el-table-column prop="sectionCode" label="断面编号" align="center" />
        <el-table-column prop="pointType" label="测点类型" align="center">
          <template #default="scope">
            {{ getTypeLabel(scope.row.pointType, MONITOR_POINT_TYPE_OPTIONS) }}
          </template>
        </el-table-column>
        <el-table-column prop="pointCode" label="测点编码" align="center" />
        <el-table-column label="监测项目" align="center">
          <template #default="scope">
            {{ getTypeLabel(scope.row.monitorItem, MONITOR_ITEM_OPTIONS) }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceCode" label="设备编码" align="center" />
        <el-table-column prop="measureUnit" label="测量单位" align="center" />
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="View"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        v-model:page="pagination.pageNum"
        v-model:limit="pagination.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="formDialog"
      width="700px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="安全监测站" prop="stationCode">
              <el-select
                v-model="formData.stationCode"
                placeholder="请选择安全监测站"
                clearable
                class="w-full"
                @change="handleStationChange"
                :disabled="formType === 'edit'"
              >
                <el-option
                  v-for="item in stationOptions"
                  :key="item.id"
                  :label="item.stationName"
                  :value="item.stationCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="断面名称" prop="sectionCode">
              <el-select
                v-model="formData.sectionCode"
                placeholder="请选择断面名称"
                clearable
                class="w-full"
                :disabled="formType === 'edit'"
                @change="handleSectionChange"
              >
                <el-option
                  v-for="item in sectionOptions"
                  :key="item.id"
                  :label="item.sectionName"
                  :value="item.sectionCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="测点编码" prop="pointCode">
              <el-select
                v-model="formData.pointCode"
                placeholder="请选择测点编码"
                :disabled="formType === 'edit'"
                clearable
                filterable
                class="w-full"
              >
                <el-option
                  v-for="item in pointOptions"
                  :key="item.pointCode"
                  :label="item.pointCode"
                  :value="item.pointCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备编码" prop="deviceCode">
              <el-input
                v-model="formData.deviceCode"
                placeholder="请输入设备编码"
                class="w-full"
                maxlength="25"
                :disabled="formType === 'edit'"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="监测项目" prop="monitorItem">
              <el-select
                v-model="formData.monitorItem"
                placeholder="请选择监测项目"
                clearable
                class="w-full"
                :disabled="formType === 'edit'"
              >
                <el-option
                  v-for="item in MONITOR_ITEM_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测量单位" prop="measureUnit">
              <el-input
                v-model="formData.measureUnit"
                placeholder="请输入测量单位"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="测量阈值" prop="thresholdValue">
              <el-input-number
                v-model="formData.thresholdValue"
                placeholder="请输入测量阈值"
                :measureAccuracy="2"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测量精度" prop="measureAccuracy">
              <el-input-number
                v-model="formData.measureAccuracy"
                placeholder="请输入测量精度"
                :measureAccuracy="2"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="安装日期" prop="installDate">
              <el-date-picker
                v-model="formData.installDate"
                type="date"
                placeholder="请选择安装日期"
                class="w-full"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测定日期" prop="measureDate">
              <el-date-picker
                v-model="formData.measureDate"
                type="date"
                placeholder="请选择测定日期"
                class="w-full"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="formData.remarks"
                type="textarea"
                placeholder="请输入备注"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="formDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="detailDialog"
      width="800px"
      append-to-body
      class="detail-dialog"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="安全监测站名称">{{
          currentDetail.stationName
        }}</el-descriptions-item>
        <el-descriptions-item label="安全监测站编码">{{
          currentDetail.stationCode
        }}</el-descriptions-item>
        <el-descriptions-item label="断面名称">{{
          currentDetail.sectionName
        }}</el-descriptions-item>
        <el-descriptions-item label="断面编号">{{
          currentDetail.sectionCode
        }}</el-descriptions-item>
        <el-descriptions-item label="测点编码">{{
          currentDetail.pointCode
        }}</el-descriptions-item>
        <el-descriptions-item label="测点类型">{{
          getTypeLabel(currentDetail.pointType, MONITOR_POINT_TYPE_OPTIONS)
        }}</el-descriptions-item>
        <el-descriptions-item label="设备编码">{{
          currentDetail.deviceCode || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="监测项目">{{
          getTypeLabel(currentDetail.monitorItem, MONITOR_ITEM_OPTIONS) || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="测量单位">{{
          currentDetail.measureUnit || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="测量阈值">{{
          currentDetail.thresholdValue || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="测量精度">{{
          currentDetail.measureAccuracy || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="安装日期">{{
          currentDetail.installDate || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="测定日期">{{
          currentDetail.measureDate || "--"
        }}</el-descriptions-item>
      </el-descriptions>

      <!-- 备注信息 -->
      <div class="remarks-section">
        <h3>备注</h3>
        <div class="remarks-content">{{ currentDetail.remarks }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.remarks-section {
  margin-top: 20px;

  h3 {
    font-size: 16px;
    margin-bottom: 15px;
    font-weight: 500;
  }
}

.remarks-content {
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 50px;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.detail-dialog) {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px 24px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
      border-radius: 3px;
    }
  }
}
</style>
