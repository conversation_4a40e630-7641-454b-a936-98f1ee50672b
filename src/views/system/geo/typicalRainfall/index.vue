<template>
    <div class="app-container">
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        class="form-container"
      >
        <el-form-item label="典型降雨编号" prop="code">
          <el-input
            v-model="queryParams.code"
            placeholder="请输入典型降雨编号"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属小流域" prop="subBasinCode">
          <el-select
            v-model="queryParams.subBasinCode"
            placeholder="请选择所属小流域"
            clearable
            filterable
            style="width: 200px"
            @change="handleQuery"
          >
            <el-option
              v-for="item in subBasinOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        
  
        <el-form-item class="form-item-btn">
          <el-button type="primary" icon="Search" @click="handleQuery"
            >查询</el-button
          >
          <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
  
      <div class="content content-table">
        <div class="table-header">
          <el-row :gutter="10">
            <el-col :span="1.5">
              <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
              <el-button type="success" icon="DocumentAdd" @click="importDialogVisible = true" style="margin-left: 8px;">导入</el-button>
            </el-col>
            <right-toolbar
              v-model:showSearch="showSearch"
              @queryTable="getList"
            ></right-toolbar>
          </el-row>
        </div>
        <el-table
          v-if="refreshTable"
          v-loading="loading"
          :data="rainfallList"
          row-key="id"
          stripe
        >
          <el-table-column
            type="index"
            label="序号"
            width="65"
            align="center"
            :index="indexMethod"
          ></el-table-column>
          <el-table-column
            prop="code"
            label="典型降雨编号"
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="subBasinName"
            label="所属小流域"
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="period"
            label="时段长"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="totalRainfall"
            label="总降雨量"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="maxRainfall"
            label="最大降雨量"
            align="center"
          ></el-table-column>
          
          <el-table-column
            label="操作"
            align="center"
            width="280"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="View"
                @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <el-button
                link
                type="danger"
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
  
  
      <!-- 添加或修改对话框 -->
      <el-dialog
        :title="isEdit ? '编辑典型降雨' : isView ? '查看典型降雨' : '新增典型降雨'"
        v-model="editDialogVisible"
        width="1200px"
        append-to-body
        @close="resetEditDialog"
        @opened="onEditDialogOpened"
      >
        <el-form v-if="!isView" ref="editFormRef" :model="editForm" :rules="editRules" label-width="110px" style="margin-bottom: 0;">
          <el-row :gutter="5">
            <el-col :span="6">
              <el-form-item label="小流域" prop="subBasinCode">
                <el-select v-model="editForm.subBasinCode" placeholder="请选择小流域" filterable clearable :disabled="isView">
                  <el-option v-for="item in subBasinOptions" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="雨量站" prop="stationCode">
                <el-select v-model="editForm.stationCode" placeholder="请选择雨量站" filterable clearable :disabled="isView">
                  <el-option v-for="item in stationOptions" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="选择时间段" prop="time">
                <el-date-picker v-model="editForm.time" type="datetimerange" value-format="YYYY-MM-DD HH:mm" format="YYYY-MM-DD HH:mm" placeholder="选择时间" style="width: 190px;" :disabled="isView" />
              </el-form-item>
            </el-col>
            <el-col :span="2" v-if="!isView" style="padding-left: 20px;">
              <el-button type="primary" @click="handleRainFallData">查询</el-button>
            </el-col>
          </el-row>
          <el-col :span="24" style="margin-top: 0px; padding-right: 20px;">
            <el-form-item label="典型降雨编号" prop="code" required>
              <el-input v-model="editForm.code" placeholder="请输入典型降雨编号" clearable :readonly="isView" />
            </el-form-item>
          </el-col>
        </el-form>
        <el-descriptions v-if="isView" :column="2" border style="margin-bottom: 20px;">
          <el-descriptions-item label="典型降雨编号">{{ editForm.code }}</el-descriptions-item>
          <el-descriptions-item label="小流域">{{ subBasinOptions.find(i => i.code === editForm.subBasinCode)?.name || editForm.subBasinCode }}</el-descriptions-item>
          <el-descriptions-item label="雨量站">{{ stationOptions.find(i => i.code === editForm.stationCode)?.name || editForm.stationCode }}</el-descriptions-item>
          <el-descriptions-item label="时间范围">{{ editForm.time && editForm.time.length === 2 ? editForm.time[0] + ' ~ ' + editForm.time[1] : '' }}</el-descriptions-item>
          <el-descriptions-item label="总降雨量(mm)">{{ viewStats.totalRainfall }}</el-descriptions-item>
          <el-descriptions-item label="最大1小时降雨量(mm)">{{ viewStats.max1HourRainfall }}</el-descriptions-item>
          <el-descriptions-item label="最大3小时降雨量(mm)">{{ viewStats.max3HourRainfall }}</el-descriptions-item>
          <el-descriptions-item label="最大6小时降雨量(mm)">{{ viewStats.max6HourRainfall }}</el-descriptions-item>
          <el-descriptions-item label="最大12小时降雨量(mm)">{{ viewStats.max12HourRainfall }}</el-descriptions-item>
          <el-descriptions-item label="最大24小时降雨量(mm)">{{ viewStats.max24HourRainfall }}</el-descriptions-item>
        </el-descriptions>
        <div class="rainfall-flex-container" style="margin-top: 0;">
          <!-- 左侧表格 -->
          <div class="rainfall-table-panel">
            <el-table :data="editRainfallTableData" height="360" style="width: 100%;" >
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="time" label="时间" align="center">
                <template #default="scope">
                  {{ scope.row.time }}
                </template>
              </el-table-column>
              <el-table-column prop="rainfallValue" label="雨量" align="center">
                <template #default="scope">
                  <el-input-number v-if="!isView" v-model="scope.row.rainfallValue" :min="0" :max="1000" :step="0.1" :precision="1" size="small" style="width: 100px;" placeholder="请输入" />
                  <span v-else>{{ scope.row.rainfallValue }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="rainfall-divider"></div>
          <!-- 右侧图表 -->
          <div class="rainfall-chart-panel">
            <div ref="editRainfallChartRef" class="rainfall-echart"></div>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" :loading="editSaving" @click="submitEditForm" v-if="!isView">保存</el-button>
            <el-button @click="resetEditDialog" v-if="!isView">取消</el-button>
          </div>
        </template>
      </el-dialog>
      
      <el-dialog title="典型降雨导入" v-model="importDialogVisible" width="420px" @close="resetImportDialog">
        <el-upload
          ref="importUploadRef"
          :action="''"
          :http-request="handleImportUpload"
          :auto-upload="false"
          :show-file-list="true"
          :accept="'.xls,.xlsx'"
          :limit="1"
          drag
          style="width: 100%"
        >
          <i class="el-icon-upload" style="font-size: 48px; color: #bfbfbf;"></i>
          <div class="el-upload__text" style="margin: 16px 0;">将文件拖到此处，或<em>点击上传</em></div>
        </el-upload>
        <div style="margin: 12px 0 16px 0; text-align: center;">
          <span>仅允许导入xls，xlsx格式文件。</span>
          <a href="javascript:void(0);" style="color: #409EFF; text-decoration: underline; margin-left: 12px;" @click="downloadTemplate">下载模板</a>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitImport">保存</el-button>
            <el-button @click="resetImportDialog">取消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { onUnmounted, nextTick, reactive, ref, watch} from "vue";
  import {
    selectStaList,
  } from "@/api/watershed/ads";
  import * as echarts from 'echarts';
  import { getTypicalRainfallList, deleteTypicalRainfall, saveTypicalRainfall, getTypicalRainfallInfo, getRainfallInfo, uploadTypicalRainfall } from '@/api/watershed/forecast/index.js';
  import { getSubBasinList } from '@/api/watershed/index.js';
  import { downloadByMain } from '@/api/warning/index.js';
  
  defineOptions({
    name: "TownshipIndex",
  });
  
  const { proxy } = getCurrentInstance();
  const rainfallList = ref([]);
  const loading = ref(true);
  const showSearch = ref(true);
  const refreshTable = ref(true);
  const total = ref(0);
  const uploadUrl = import.meta.env.VITE_APP_BASE_API + "/hydro/convert/shp-kml/";
  import { getToken } from "@/utils/auth";
  const token = getToken();
  
  const data = reactive({
    total: 0,
    form: {
      geoType: "people",
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      // asNext: 1 // 加个asNext 在查询接口里 1是0否
    },
    rules: {
      parentAdcd: [
        { required: true, message: "所属县区不能为空", trigger: "blur" },
      ],
      adcd: [
        { required: true, message: "乡镇代码不能为空", trigger: "blur" },
        {
          pattern: /^\d{12}$/,
          message: "乡镇代码必须为12位数字",
          trigger: ["blur", "change"],
        },
      ],
      adnm: [{ required: true, message: "乡镇名称不能为空", trigger: "blur" }],
      area: [
        {
          pattern: /^\d+(\.\d+)?$/,
          message: "请输入正确的数字格式",
          trigger: ["blur", "change"],
        },
      ],
    },
  });
  
  const { queryParams } = toRefs(data);
  
  const subBasinOptions = ref([]);
  const stationOptions = ref([]);
  
  function indexMethod(index) {
    return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
  }
  
  async function getList() {
    loading.value = true;
    const params = {
      code: queryParams.value.code,
      subBasinCode: queryParams.value.subBasinCode,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize
    };
    try {
      const res = await getTypicalRainfallList(params);
      rainfallList.value = res.rows || [];
      total.value = res.total || 0;
    } finally {
      loading.value = false;
    }
  }
  
  async function initOptions() {
    // 小流域
    const subRes = await getSubBasinList({ pageNum: 1, pageSize: 999999 });
    subBasinOptions.value = (subRes.rows || []).map(item => ({ code: item.code, name: item.name }));
    // 雨量站
    const staRes = await selectStaList({ asNext: 1, pageNum: 1, pageSize: 999999, sttp: 'PP' });
    stationOptions.value = (staRes.data?.records || []).map(item => ({ code: item.stcd, name: `${item.stnm}(${item.stcd})` }));
  }
  
  onMounted(async () => {
    await initOptions();
    await getList();
    nextTick(() => {
      if (rainfallChartRef.value) {
        rainfallChartInstance = echarts.init(rainfallChartRef.value);
        updateRainfallChart();
      }
    });
  });
  
  function handleDelete(row) {
    proxy.$modal.confirm('是否确认删除编号为"' + row.code + '"的典型降雨?')
      .then(() => deleteTypicalRainfall(row.id))
      .then(() => {
        proxy.$modal.msgSuccess('删除成功');
        getList();
      })
      .catch(() => {});
  }
  
 
  /** 搜索按钮操作 */
  function handleQuery() {
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }
  /** 新增按钮操作 */
  function handleAdd() {
    isView.value = false;
    openEditDialog();
  }
  /** 修改按钮操作 */
  async function handleUpdate(row) {
    isView.value = false;
    openEditDialog(row);
  }
 
  
  onUnmounted(() => {});
  
  getList();
  const rainfallWeightForm = ref({
    adcd: "",
    adnm: "",
    stations: [
      {
        stationId: "",
        stationName: "",
        weight: null,
      },
    ],
  });

  // 存储初始表单数据，用于重置操作
  const originalRainfallWeightForm = ref({
    adcd: "",
    adnm: "",
    stations: [
      {
        stationId: "",
        stationName: "",
        weight: null,
      },
    ],
  });
  
  const rainfallChartRef = ref(null);
  let rainfallChartInstance = null;
  
  function updateRainfallChart() {
    if (!rainfallChartInstance) return;
    // 图表数据正序排列
    const chartData = [...rainfallList.value].sort((a, b) => a.time.localeCompare(b.time));
    rainfallChartInstance.setOption({
      tooltip: { trigger: 'axis' },
      grid: { left: 50, right: 50, top: 30, bottom: 40 },
      xAxis: {
        type: 'category',
        data: chartData.map(item => item.time),
        axisLabel: { rotate: 0, fontSize: 12, margin: 16, interval: 'auto' },
        name: '时间',
      },
      yAxis: {
        type: 'value',
        name: '降雨量(mm)',
        min: 0,
      },
      series: [
        {
          name: '降雨量',
          type: 'line',
          data: chartData.map(item => item.rainfall),
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: { width: 2 },
          itemStyle: { color: '#409EFF' },
        },
      ],
    });
  }
  
  watch(rainfallList, updateRainfallChart, { deep: true });
  
  const editDialogVisible = ref(false);
  const isEdit = ref(false);
  const editFormRef = ref();
  const editForm = ref({
    id: undefined,
    code: '',
    subBasinCode: '',
    stationCode: '',
    time: [], // [start, end]
  });
  const editRainfallTableData = ref([]);
  const editRainfallChartRef = ref(null);
  let editRainfallChartInstance = null;
  const editSaving = ref(false);
  
  function validateTimeRange(rule, value, callback) {
    if (!value || value.length !== 2) {
      callback(new Error('请选择时间范围'));
      return;
    }
    const start = new Date(value[0]).getTime();
    const end = new Date(value[1]).getTime();
    if (isNaN(start) || isNaN(end)) {
      callback(new Error('请选择有效的时间范围'));
      return;
    }
    const hours = (end - start) / (1000 * 60 * 60);
    if (hours <= 0) {
      callback(new Error('结束时间需大于开始时间'));
      return;
    }
    if (hours >= 360) {
      callback(new Error('时间范围不能超过360小时'));
      return;
    }
    callback();
  }
  
  const editRules = {
    code: [
      { required: true, message: '请输入典型降雨编号', trigger: 'blur' },
      { max: 30, message: '编号不能超过30字符', trigger: 'blur' }
    ],
    subBasinCode: [{ required: true, message: '请选择小流域', trigger: 'change' }],
    // stationCode: 可为空，无需校验
    time: [
      { required: true, message: '请选择时间范围', trigger: 'change' },
      { validator: validateTimeRange, trigger: 'change' }
    ]
  };

  
  function openEditDialog(row) {
    isEdit.value = !!row;
    editDialogVisible.value = true;
    if (row) {
      // 编辑，拉取详情
      getTypicalRainfallInfo({ code: row.code }).then(res => {
        const data = res.data || {};
        editForm.value = {
          id: data.id,
          code: data.code,
          subBasinCode: data.subBasinCode,
          stationCode: data.stationCode,
          time: data.startTime && data.endTime ? [data.startTime, data.endTime] : [],
        };
        editRainfallTableData.value = (data.rainfallData || []).map(item => ({
          time: item.time,
          rainfallValue: item.rainfallValue
        })).sort((a, b) => b.time.localeCompare(a.time));
        nextTick(updateEditRainfallChart);
      });
    } else {
      // 新增
      editForm.value = { id: undefined, code: '', subBasinCode: '', stationCode: '', time: [] };
      editRainfallTableData.value = [];
      nextTick(updateEditRainfallChart);
    }
  }
  
  function resetEditDialog() {
    editDialogVisible.value = false;
    editForm.value = { id: undefined, code: '', subBasinCode: '', stationCode: '', time: [] };
    editRainfallTableData.value = [];
    viewStats.value = {
      totalRainfall: '',
      max1HourRainfall: '',
      max3HourRainfall: '',
      max6HourRainfall: '',
      max12HourRainfall: '',
      max24HourRainfall: '',
    };
    isView.value = false;
    if (typeof isEdit !== 'undefined') isEdit.value = false;
    editSaving.value = false;
  }
  
  function validateEditRainfallTable() {
    // 校验：时间、雨量必填，时间不可重复
    const times = editRainfallTableData.value.map(r => r.time);
    if (times.some(t => !t)) {
      proxy.$modal.msgError('请填写所有时序数据的时间');
      return false;
    }
    if (editRainfallTableData.value.some(r => r.rainfallValue === null || r.rainfallValue === undefined || r.rainfallValue === '')) {
      proxy.$modal.msgError('请填写所有时序数据的雨量');
      return false;
    }
    if (new Set(times).size !== times.length) {
      proxy.$modal.msgError('时序数据时间不能重复');
      return false;
    }
    return true;
  }
  
  function submitEditForm() {
    editFormRef.value.validate(async valid => {
      if (!valid) return;
      if (!validateEditRainfallTable()) return;
      if (editRainfallTableData.value.length === 0) {
        proxy.$modal.msgError('请至少添加一条时序数据');
        return;
      }
      editSaving.value = true;
      // 自动推算startTime, endTime
      const sorted = [...editRainfallTableData.value].sort((a, b) => a.time.localeCompare(b.time));
      // 优先用表单时间范围
      const [startTime, endTime] = editForm.value.time;
      const typicalRainfallDataList = editRainfallTableData.value.map(r => ({ time: r.time, rainfallValue: r.rainfallValue }));
      const params = {
        ...editForm.value,
        startTime,
        endTime,
        typicalRainfallDataList
      };
      try {
        await saveTypicalRainfall(params);
        proxy.$modal.msgSuccess('保存成功');
        editDialogVisible.value = false;
        getList();
      } finally {
        editSaving.value = false;
      }
    });
  }
  
  function updateEditRainfallChart() {
    if (!editRainfallChartRef.value) return;
    if (!editRainfallChartInstance) {
      editRainfallChartInstance = echarts.init(editRainfallChartRef.value);
    }
    // 图表数据正序排列
    const chartData = [...editRainfallTableData.value].sort((a, b) => a.time.localeCompare(b.time));
    editRainfallChartInstance.setOption({
      tooltip: { trigger: 'axis' },
      grid: { left: 50, right: 40, top: 30, bottom: 40 },
      xAxis: {
        type: 'category',
        data: chartData.map(item => item.time),
        axisLabel: { rotate: 0, fontSize: 12, margin: 16, interval: 'auto' },
        name: '时间',
      },
      yAxis: {
        type: 'value',
        name: '降雨量(mm)',
        min: 0,
      },
      series: [
        {
          name: '降雨量',
          type: 'bar',
          data: chartData.map(item => item.rainfallValue),
          itemStyle: { color: '#409EFF' },
        },
      ],
    });
    editRainfallChartInstance.resize();
  }
  
  function onEditDialogOpened() {
    updateEditRainfallChart();
    if (editRainfallChartInstance) {
      editRainfallChartInstance.resize();
    }
  }
  
  async function handleRainFallData() {
    if (!editForm.value.subBasinCode) {
      proxy.$modal.msgError('请选择小流域');
      return;
    }
    if (!editForm.value.time || editForm.value.time.length !== 2) {
      proxy.$modal.msgError('请选择时间范围');
      return;
    }
    const params = {
      subBasinCode: editForm.value.subBasinCode,
      stationCode: editForm.value.stationCode || '',
      startTime: editForm.value.time[0],
      endTime: editForm.value.time[1]
    };
    editSaving.value = true;
    try {
      const res = await getRainfallInfo(params);
      const rows = res.rows || [];
      // rows: [{serialNumber, stationCode, time, rainfallValue}]
      editRainfallTableData.value = rows.sort((a, b) => b.time.localeCompare(a.time));
      updateEditRainfallChart();
    } catch (e) {
      proxy.$modal.msgError('获取降雨量信息失败');
    } finally {
      editSaving.value = false;
    }
  }
  
  const isView = ref(false);
  const viewStats = ref({
    totalRainfall: '',
    max1HourRainfall: '',
    max3HourRainfall: '',
    max6HourRainfall: '',
    max12HourRainfall: '',
    max24HourRainfall: '',
  });
  
  function handleView(row) {
    isView.value = true;
    editDialogVisible.value = true;
    getTypicalRainfallInfo({ code: row.code }).then(res => {
      const data = res.data || {};
      editForm.value = {
        id: data.id,
        code: data.code,
        subBasinCode: data.subBasinCode,
        stationCode: data.stationCode,
        time: data.startTime && data.endTime ? [data.startTime, data.endTime] : [],
      };
      // 统计字段
      viewStats.value = {
        totalRainfall: data.totalRainfall,
        max1HourRainfall: data.max1HourRainfall,
        max3HourRainfall: data.max3HourRainfall,
        max6HourRainfall: data.max6HourRainfall,
        max12HourRainfall: data.max12HourRainfall,
        max24HourRainfall: data.max24HourRainfall,
      };
      editRainfallTableData.value = (data.rainfallData || []).map(item => ({
        time: item.time,
        rainfallValue: item.rainfallValue
      })).sort((a, b) => b.time.localeCompare(a.time));
      nextTick(updateEditRainfallChart);
    });
  }
  
  const importDialogVisible = ref(false);
  const importUploadRef = ref();
  
  function resetImportDialog() {
    importDialogVisible.value = false;
    if (importUploadRef.value) importUploadRef.value.clearFiles();
  }
  
  function handleImportUpload(option) {
    // option.file为上传的文件
    const formData = new FormData();
    formData.append('file', option.file);
    uploadTypicalRainfall(formData).then(() => {
      proxy.$modal.msgSuccess('导入成功');
      resetImportDialog();
      getList();
    }).catch(() => {
      proxy.$modal.msgError('导入失败');
    });
  }
  
  function submitImport() {
    if (importUploadRef.value) {
      importUploadRef.value.submit();
    }
  }
  const downloadFileUrl = import.meta.env.VITE_APP_BASE_API + "/file/download";
  function downloadTemplate() {
   // 真实文件名和桶名
  const realFileName = 'a3f670ec-fc78-419e-bb4d-d548dcf54dab_典型降雨导入模板01.xlsx';
  const bucketName = 'watershed';
  const downloadUrl = downloadFileUrl;
  const token = getToken();
  const params = new URLSearchParams({
    bucketName,
    fileName: realFileName
  }).toString();
  const urlWithParams = downloadUrl + (downloadUrl.includes('?') ? '&' : '?') + params;
  fetch(urlWithParams, {
    method: 'POST',
    headers: {
      Authorization: 'Bearer ' + token
    }
  })
    .then(response => {
      if (!response.ok) throw new Error('文件下载失败');
      return response.blob();
    })
    .then(blob => {
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = '典型降雨导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(link.href);
    })
    .catch(() => {
      proxy.$modal.msgError('文件下载失败');
    });
  }
  </script>
  
<style scoped>
.rainfall-flex-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 420px;
  min-height: 320px;
  margin-top: 10px;
}
.rainfall-table-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 6px 0 0 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 16px 8px 16px 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.rainfall-divider {
  width: 2px;
  background: #e5e6eb;
  margin: 0 8px;
  border-radius: 2px;
  height: 100%;
  align-self: stretch;
}
.rainfall-chart-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 0 6px 6px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 16px 16px 16px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.rainfall-echart {
  width: 100%;
  height: 100%;
  min-height: 320px;
}
</style>
  