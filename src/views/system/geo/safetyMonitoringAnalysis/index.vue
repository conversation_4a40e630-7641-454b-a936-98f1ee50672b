<script setup>
import { onMounted, onUnmounted, nextTick, reactive, ref, computed } from "vue";
import * as echarts from "echarts";
import {
  getSafetyMonitorPointInfo,
  getSafetyMonitorTree,
} from "@/api/watershed/forecast/index.js";
import moment from "moment";

defineOptions({
  name: "TownshipIndex",
});

const { proxy } = getCurrentInstance();
const accuracyList = ref([]);
const total = ref(0);
const loading = ref(false);
const refreshTable = ref(true);

// 分页处理
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  forecastSchemeId: undefined,
  status: 1,
});

const indexMethod = (index) => {
  return (queryParams.pageNum - 1) * queryParams.pageSize + index + 1;
};

// 左侧树过滤相关
const filterText = ref("");
const treeRef = ref(null);

const treeData = ref([]);

// 获取树列表
const getSafetyMonitorTreeList = async () => {
  try {
    const res = await getSafetyMonitorTree({
      name: filterText.value,
    });
    if (res.code === 200) {
      // 处理树数据，添加节点类型
      treeData.value = processTreeData(res.rows || []);

      console.log(treeData.value, "treeData.value");
    }
  } catch (error) {
    console.log(error);
  }
};

// 处理树数据，添加节点类型
const processTreeData = (data) => {
  return data.map((item) => {
    const processedItem = { ...item };

    // 处理二级节点（有children且nodeCode为null的节点）
    if (
      processedItem.children &&
      processedItem.children.length > 0 &&
      !processedItem.nodeCode
    ) {
      processedItem.children = processedItem.children.map((child) => {
        const processedChild = { ...child };

        // 根据二级节点的nodeName判断类型
        if (processedChild.nodeName === "雨量站") {
          processedChild.nodeType = 0;
        } else if (processedChild.nodeName === "堰闸水文站") {
          processedChild.nodeType = 1;
        } else if (
          processedChild.nodeName.includes("左岸") ||
          processedChild.nodeName.includes("右岸")
        ) {
          processedChild.nodeType = 3;
        }

        // 处理三级节点
        if (processedChild.children && processedChild.children.length > 0) {
          processedChild.children = processedChild.children.map(
            (grandChild) => {
              const processedGrandChild = { ...grandChild };
              // 三级节点继承父级的节点类型
              processedGrandChild.nodeType = processedChild.nodeType;
              return processedGrandChild;
            }
          );
        }

        return processedChild;
      });
    }

    return processedItem;
  });
};

// 判断是否为三级节点（叶子节点）
const isThirdLevelNode = (data) => {
  // 有 nodeCode 值且 children 为 null 或空数组
  return data.nodeCode && (!data.children || data.children.length === 0);
};

const checkedNodes = ref([]);
// 处理节点选中状态变化
const handleNodeCheckChange = (data, checked) => {
  // 只有三级节点才能被选中
  if (!isThirdLevelNode(data)) {
    return;
  }

  // 如果是要选中节点，检查是否已经选择了两个节点
  if (checked && checkedNodes.value.length >= 2) {
    proxy.$modal.msgWarning("最多只能选择两个监测点");
    return;
  }

  // 如果是要选中节点，检查是否与已选节点类型相同
  if (checked && checkedNodes.value.length === 1) {
    const existingNodeType = checkedNodes.value[0].nodeType;
    if (data.nodeType === existingNodeType) {
      proxy.$modal.msgWarning("请选择不同类型的监测点进行对比");
      return;
    }
  }

  // 设置节点的选中状态
  treeRef.value.setChecked(data.nodeCode, checked, false);

  // 获取所有选中的三级节点
  checkedNodes.value = treeRef.value
    .getCheckedNodes()
    .filter((node) => isThirdLevelNode(node));
  console.log(checkedNodes.value, "checkedNodes.value");

  // 重新初始化数据类型选择
  handleCheckedNodesChange();
};

// 图表相关
const chartRef = ref(null);
let chartInstance = null;

// 日期时间选择器相关
const dateTimeRange = ref([]);
const dateTimePickerDisabledDate = (time) => {
  // 禁用当前时间之后的时间
  return time.getTime() > Date.now();
};

// 初始化日期时间范围（最近7天）
const initDateTimeRange = () => {
  const start = moment().subtract(7, "days").format("YYYY-MM-DD HH:mm");
  const end = moment().format("YYYY-MM-DD HH:mm");
  dateTimeRange.value = [start, end];
};

const renderChart = () => {
  nextTick(() => {
    if (!chartRef.value) return;
    if (!chartInstance) {
      chartInstance = echarts.init(chartRef.value);
    }

    // 如果没有数据或没有选中的数据类型，清空图表并返回
    if (!accuracyList.value.length || !selectedDataTypes.value.length) {
      if (chartInstance) {
        chartInstance.clear();
      }
      return;
    }

    const dataLabels = getSelectedDataLabels();
    const xData = accuracyList.value.map((item) => item.time);

    // 动态生成系列数据
    const series = [];
    const legendData = [];
    const colors = ["#409EFF", "#E6A23C", "#67C23A", "#F56C6C"];

    dataLabels.forEach((dataLabel, index) => {
      const seriesData = accuracyList.value.map((item) => {
        // 根据索引获取对应的参数值
        return index === 0 ? item.parameterOne : item.parameterTwo;
      });

      series.push({
        name: `${dataLabel.label}(${dataLabel.unit})`,
        type: "line",
        data: seriesData,
        yAxisIndex: index,
        smooth: true,
        symbol: "circle",
        symbolSize: 8,
        lineStyle: { width: 2 },
        itemStyle: { color: colors[index] },
      });

      legendData.push(`${dataLabel.label}(${dataLabel.unit})`);
    });

    // 动态生成Y轴
    const yAxis = dataLabels.map((dataLabel, index) => ({
      type: "value",
      name: `${dataLabel.label}(${dataLabel.unit})`,
      min: 0,
      position: index === 0 ? "left" : "right",
    }));

    chartInstance.setOption({
      tooltip: { trigger: "axis" },
      legend: { data: legendData },
      grid: { left: 50, right: 50, top: 30, bottom: 40 },
      xAxis: [
        {
          type: "category",
          data: xData,
          axisLabel: { rotate: 0, fontSize: 12, margin: 16, interval: "auto" },
          name: "时间",
        },
      ],
      yAxis: yAxis,
      series: series,
    });
    chartInstance.resize();
  });
};
const handleChartResize = () => {
  if (chartInstance) chartInstance.resize();
};

const handleSearch = async () => {
  try {
    if (checkedNodes.value.length === 0) {
      proxy.$modal.msgWarning("请选择监测点");
      return;
    }
    if (checkedNodes.value.length !== 2) {
      proxy.$modal.msgWarning("必须选择两个监测点");
      return;
    }
    // 检查选择的两个节点类型是否相同
    if (checkedNodes.value[0].nodeType === checkedNodes.value[1].nodeType) {
      proxy.$modal.msgWarning("请选择不同类型的监测点进行对比");
      return;
    }
    if (dateTimeRange.value.length === 0) {
      proxy.$modal.msgWarning("请选择时间");
      return;
    }
    loading.value = true;
    const res = await getSafetyMonitorPointInfo({
      list: selectedDataTypes.value,
      begin: dateTimeRange.value[0],
      end: dateTimeRange.value[1],
    });

    console.log(res, "res");

    if (res.code === 200) {
      accuracyList.value = res.rows;
      total.value = res.total || 0;
      // 重新渲染图表
      renderChart();
    }
  } catch (e) {
    console.log(e);
  } finally {
    loading.value = false;
  }
};

const handleReset = () => {
  // 重置日期时间范围到最近7天
  initDateTimeRange();

  handleSearch();
};

const getTreeLabel = (data) => {
  let label = "";
  if (data.nodeName) {
    label = data.nodeName;
    if (data.nodeCode) {
      label += "(" + data.nodeCode + ")";
    }
  } else if (data.nodeCode) {
    label = data.nodeCode;
  }
  return label;
};

// 获取节点类型对应的监测数据类型
const getNodeTypeDataTypes = (nodeType) => {
  switch (nodeType) {
    case 0: // 雨量站
      return [{ label: "降雨量", value: 0, unit: "mm" }];
    case 1: // 堰闸水文站
      return [
        { label: "闸上水位", value: 0, unit: "m" },
        { label: "闸下水位", value: 1, unit: "m" },
      ];
    case 3: // 安全检测站
      return [
        { label: "渗压", value: 0, unit: "kPa" },
        { label: "温度", value: 1, unit: "°C" },
      ];
    default:
      return [];
  }
};

// 获取节点类型名称
const getNodeTypeName = (nodeType) => {
  switch (nodeType) {
    case 0:
      return "雨量站";
    case 1:
      return "堰闸水文站";
    case 3:
      return "安全检测站";
    default:
      return "未知类型";
  }
};

// 根据节点类型和项目值获取数据类型信息
const getDataTypeInfo = (nodeType, item) => {
  const dataTypes = getNodeTypeDataTypes(nodeType);
  return dataTypes.find((type) => type.value === item) || dataTypes[0];
};

// 获取选中数据类型的标签和单位
const getSelectedDataLabels = () => {
  return selectedDataTypes.value.map((selected) => {
    const dataType = getDataTypeInfo(selected.nodeType, selected.item);
    return {
      label: dataType.label,
      unit: dataType.unit,
      nodeName: getNodeTypeName(selected.nodeType),
    };
  });
};

// 为每个选中的节点添加数据类型选择
const selectedDataTypes = ref([]);

// 判断是否有图表数据
const hasChartData = computed(() => {
  return accuracyList.value.length > 0 && selectedDataTypes.value.length > 0;
});

watch(
  () => selectedDataTypes.value,
  (val) => {
    console.log(val, "val");
    // 当数据类型选择变化时，重新渲染图表
    if (accuracyList.value.length > 0) {
      renderChart();
    }
  },
  {
    deep: true,
  }
);

// 初始化选中节点的数据类型
const initSelectedDataTypes = () => {
  selectedDataTypes.value = [];
  checkedNodes.value.forEach((node) => {
    const dataTypes = getNodeTypeDataTypes(node.nodeType);
    if (dataTypes.length > 0) {
      selectedDataTypes.value.push({
        nodeType: node.nodeType,
        nodeCode: node.nodeCode,
        item: dataTypes[0].value,
      });
    }
  });
};

// 获取指定节点编码的选中值
const getSelectedItemValue = (nodeCode) => {
  const item = selectedDataTypes.value.find(
    (item) => item.nodeCode === nodeCode
  );
  return item ? item.item : null;
};

// 更新指定节点的选中值
const updateSelectedItem = (nodeCode, value) => {
  const index = selectedDataTypes.value.findIndex(
    (item) => item.nodeCode === nodeCode
  );
  if (index !== -1) {
    selectedDataTypes.value[index].item = value;
  }
};

// 监听选中节点变化，重新初始化数据类型选择
const handleCheckedNodesChange = () => {
  initSelectedDataTypes();
};

onMounted(async () => {
  getSafetyMonitorTreeList();
  initDateTimeRange();
  window.addEventListener("resize", handleChartResize);
  // 初始化数据类型选择
  initSelectedDataTypes();
  // 初始化图表（但不渲染，因为没有数据）
  nextTick(() => {
    if (chartRef.value && !chartInstance) {
      chartInstance = echarts.init(chartRef.value);
    }
  });
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", handleChartResize);
});
</script>

<template>
  <div class="app-container">
    <div class="analysis-flex-container">
      <!-- 左侧区域 -->
      <div class="analysis-flex-left">
        <el-row :gutter="8" style="margin-bottom: 12px">
          <el-col :span="17">
            <el-input
              v-model="filterText"
              placeholder="输入关键字过滤"
              size="small"
              clearable
            />
          </el-col>
          <el-col :span="7">
            <el-button
              type="primary"
              size="small"
              @click="getSafetyMonitorTreeList"
              icon="Search"
              >查询</el-button
            >
          </el-col>
        </el-row>
        <el-tree
          ref="treeRef"
          :data="treeData"
          node-key="nodeCode"
          :props="{
            label: 'nodeName',
            value: 'nodeCode',
            children: 'children',
          }"
          default-expand-all
        >
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <span v-if="isThirdLevelNode(data)" class="checkbox-container">
                <el-checkbox
                  :model-value="node.checked"
                  :disabled="
                    !node.checked &&
                    (checkedNodes.length >= 2 ||
                      (checkedNodes.length === 1 &&
                        checkedNodes[0].nodeType === data.nodeType))
                  "
                  @change="(checked) => handleNodeCheckChange(data, checked)"
                ></el-checkbox>
              </span>
              <span>{{ getTreeLabel(data) }}</span>
            </span>
          </template>
        </el-tree>
      </div>
      <div class="analysis-flex-right">
        <!-- 选择需要分析的类型 -->
        <div class="chart-type">
          <div
            class="chart-type-item"
            v-for="(item, index) in checkedNodes"
            :key="item.value"
          >
            <div class="chart-type-item-label">
              {{ getTreeLabel(item) }}
            </div>
            <div class="chart-type-item-value">
              <div class="node-type-info">
                <span class="node-type-label">{{
                  getNodeTypeName(item.nodeType)
                }}</span>
              </div>
              <div class="data-type-selector">
                <el-radio-group
                  v-model="selectedDataTypes[index].item"
                  size="small"
                >
                  <el-radio
                    v-for="dataType in getNodeTypeDataTypes(item.nodeType)"
                    :key="dataType.value"
                    :label="dataType.value"
                    class="data-type-radio"
                  >
                    {{ dataType.label }}({{ dataType.unit }})
                  </el-radio>
                </el-radio-group>
              </div>
            </div>
          </div>
        </div>

        <!-- 日期时间选择器 -->
        <div class="chart-header">
          <el-form inline>
            <el-form-item>
              <el-date-picker
                v-model="dateTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                :disabled-date="dateTimePickerDisabledDate"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" icon="Search"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                @click="handleReset"
                icon="Refresh"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div v-show="hasChartData" ref="chartRef" class="chart-container"></div>
        <!-- 缺省样式 -->
        <div
          class="chart-empty-container"
          v-if="!hasChartData"
          v-loading="loading"
        >
          <div class="chart-empty">
            <div class="chart-empty-icon">
              <el-icon size="48" color="#c0c4cc">
                <TrendCharts />
              </el-icon>
            </div>
            <div class="chart-empty-text">
              <p>暂无数据</p>
              <p>请选择监测点并查询数据</p>
            </div>
          </div>
        </div>

        <el-table
          v-if="refreshTable"
          v-loading="loading"
          :data="accuracyList"
          row-key="id"
          stripe
          class="flex-half-table"
        >
          <el-table-column
            type="index"
            label="序号"
            width="65"
            align="center"
            :index="indexMethod"
          ></el-table-column>
          <el-table-column
            prop="time"
            label="时间"
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            v-for="(dataLabel, index) in getSelectedDataLabels()"
            :key="index"
            :prop="index === 0 ? 'parameterOne' : 'parameterTwo'"
            :label="`${dataLabel.label}(${dataLabel.unit})`"
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleSearch"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.analysis-flex-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.analysis-flex-left {
  width: 300px;
  min-width: 220px;
  max-width: 340px;
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.03);
  padding: 12px 8px 12px 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;

  .el-tree {
    flex: 1;
    overflow: auto;

    // 美化滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 3px;
    }
  }
}
.analysis-flex-right {
  flex: 1;
  min-width: 0;
  padding-left: 18px;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .chart-type {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 10px;

    .chart-type-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .chart-type-item-label {
        font-size: 14px;
        color: #333;
        font-weight: 500;
        flex: 1;
      }

      .chart-type-item-value {
        display: flex;
        gap: 8px;
        align-items: center;

        .node-type-info {
          .node-type-label {
            font-size: 12px;
            color: #909399;
            background: #f0f2f5;
            padding: 2px 6px;
            border-radius: 3px;
          }
        }

        .data-type-selector {
          .el-radio-group {
            display: flex;
            gap: 6px;

            .data-type-radio {
              margin-right: 0;

              .el-radio__label {
                font-size: 12px;
                color: #606266;
              }

              .el-radio__input.is-checked + .el-radio__label {
                color: #409eff;
              }
            }
          }
        }
      }
    }
  }
}
.chart-container {
  width: 100%;
  min-height: 180px;
  flex: 1 1 0%;
  position: relative;
}

.chart-empty-container {
  width: 100%;
  min-height: 180px;
  flex: 1 1 0%;
  position: relative;

  .chart-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 1;

    .chart-empty-icon {
      margin-bottom: 16px;

      .el-icon {
        opacity: 0.6;
      }
    }

    .chart-empty-text {
      color: #909399;
      font-size: 14px;
      line-height: 1.6;

      p {
        margin: 4px 0;

        &:first-child {
          font-size: 16px;
          font-weight: 500;
          color: #606266;
        }
      }
    }
  }
}

.el-table.flex-half-table {
  flex: 1 1 0%;
  min-height: 180px;
}

/* 自定义树节点样式 */
.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}

.checkbox-container {
  margin-right: 8px;
}

/* 确保树节点内容对齐 */
:deep(.el-tree-node__content) {
  padding-right: 0;
}
</style>
