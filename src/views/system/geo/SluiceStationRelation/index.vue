<script setup>
//  水闸关联测站
import { ref, reactive, onMounted, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getTypeLabel } from "@/utils";
import { MONITOR_TYPE_OPTIONS } from "@/views/system/geo/utils/enum";
import {
  getGateStationRelationList,
  getGateStationRelationDetail,
  addOrUpdateOrDeleteGateStationRelation
} from "@/api/waterGate";
import {
  selectStaList, // 雨情/水情
  fetchSafetyMonitoringStationList, // 安全
  // 如有视频/图像相关接口可补充
} from "@/api/watershed/ads/index";
import { getVideoMonitorStationList } from '@/api/video/index'
import { measuringStationInfo, riverWaterLevel } from '@/api/watershed/query/index'

defineOptions({
  name: "SluiceStationRelation",
});

const { proxy } = getCurrentInstance();

// 水闸查询参数
const queryParams = ref({
  sluiceName: "",
});

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 水闸详情对话框
const detailDialog = ref(false);
const currentDetail = ref({});

// 水闸编辑对话框
const editDialog = ref(false);
const editTitle = ref("");
const editData = ref({
  sluiceName: "",
  sluiceCode: "",
  stationList: [],
});

// 测站选项映射
const stationOptions = reactive({
  1: { list: [], pageNum: 1, pageSize: 100, total: 0, keyword: '', loading: false }, // 雨情测站
  2: { list: [], pageNum: 1, pageSize: 100, total: 0, keyword: '', loading: false }, // 水情测站
  3: { list: [], pageNum: 1, pageSize: 100, total: 0, keyword: '', loading: false }, // 安全测站
  4: { list: [], pageNum: 1, pageSize: 100, total: 0, keyword: '', loading: false }  // 视频测站
});

// 获取水闸列表（真实接口）
const getSluiceList = async () => {
  loading.value = true;
  try {
    const params = {
      gateName: queryParams.value.sluiceName,
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize
    };
    const res = await getGateStationRelationList(params);
    // 适配后端返回结构
    tableData.value = (res.rows || []).map((item, idx) => ({
      ...item,
      serialNumber: (pagination.value.pageNum - 1) * pagination.value.pageSize + idx + 1
    }));
    pagination.value.total = res.total || 0;
  } catch (e) {
    tableData.value = [];
    pagination.value.total = 0;
  } finally {
    loading.value = false;
  }
};

// 查询按钮
const handleQuery = () => {
  pagination.value.pageNum = 1;
  getSluiceList();
};

// 重置按钮
const handleReset = () => {
  queryParams.value.sluiceName = "";
  handleQuery();
};

// 查看关联测站（真实接口）
const handleView = async (row) => {
  try {
    const res = await getGateStationRelationDetail(row.gateCode);
    // 适配后端返回结构
    currentDetail.value = {
      sluiceName: row.gateName,
      sluiceCode: row.gateCode,
      stationList: (res.data && res.data.gateStationRelationPOList) || []
    };
    detailDialog.value = true;
  } catch (e) {
    ElMessage.error('获取详情失败');
  }
};

// 编辑关联测站（真实接口）
const handleEdit = async (row) => {
  editTitle.value = "编辑水闸关联测站 - " + row.gateName;
  try {
    const res = await getGateStationRelationDetail(row.gateCode);
    // 字段适配，确保与表单字段一致
    editData.value = {
      id: row.id,
      sluiceName: res.data.gateName,
      sluiceCode: res.data.gateCode,
      stationList: (res.data.gateStationRelationPOList || []).map(item => ({
        id: item.id,
        tenantId: item.tenantId,
        category: String(item.category), // 保证为字符串
        stationCode: item.stationCode, // 统一用 stationCode
        stationName: item.stationName,
        lastCollectTime: item.lastCollectionTime
      }))
    };
    // 保证所有已选测站都在 options list 里，便于回显
    editData.value.stationList.forEach(item => {
      const type = item.category;
      const optionsList = stationOptions[type]?.list;
      if (optionsList && !optionsList.some(opt => opt.value === item.stationCode)) {
        optionsList.unshift({
          value: item.stationCode,
          label: `${item.stationName} ${item.stationCode}`,
          stationName: item.stationName,
          stationCode: item.stationCode
        });
      }
    });
    editDialog.value = true;
  } catch (e) {
    ElMessage.error('获取详情失败');
  }
};

// 添加测站行
const addStationRow = () => {
  // 验证当前所有行是否都已填写
  const emptyRow = editData.value.stationList.find(
    (item) => !item.category || !item.stationCode
  );
  if (emptyRow) {
    ElMessage.warning("监测分类和测站不可为空！");
    return;
  }

  editData.value.stationList.push({
    id: Date.now(),
    category: "",
    stationCode: "",
    stationName: "",
    lastCollectTime: "无数据",
  });
};

// 删除测站行
const removeStationRow = (index) => {
  editData.value.stationList.splice(index, 1);
};

// 监测分类变更
const handleMonitorTypeChange = (row) => {
  // 当监测分类变更时，清空测站选择
  row.stationCode = "";
  row.stationName = "";
  row.lastCollectTime = "";
};

// 测站变更
const handleStationChange = (row) => {
  if (row.stationCode) {
    // 根据选中的测站值获取测站信息
    const selectedOption = stationOptions[row.category]?.list.find(
      (option) => option.value === row.stationCode
    );
    if (selectedOption) {
      row.stationName = selectedOption.stationName;
    }
  } else {
    row.stationName = "";
  }
  row.lastCollectTime = "";
};

// 时间工具函数：获取当前到一个月前的时间范围
function getTimeRange() {
  const end = new Date();
  const start = new Date();
  start.setMonth(start.getMonth() - 1);
  const pad = n => n < 10 ? '0' + n : n;
  const format = d => `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
  return { startTime: format(start), endTime: format(end) };
}

// 测试数据
const testStationData = async (row) => {
  row.lastCollectTime = '查询中...';
  const { startTime, endTime } = getTimeRange();
  try {
    if (row.category == 1) {
      // 雨情测站
      const res = await measuringStationInfo({
        stcd: row.stationCode,
        startTime,
        endTime,
        pageNum: 1,
        pageSize: 1,
        rainStandard: 0,
        rainType: 0,
      });
      const records = res.data || [];
      row.lastCollectTime = records.length > 0 ? (records[records.length - 1].tm  || '无数据') : '无数据';
    } else if (row.category == 2) {
      // 水情测站
      const res = await riverWaterLevel({
        stcd: row.stationCode,
        startTime,
        endTime,
        pageNum: 1,
        pageSize: 1
      });
      const records = res.data?.records || [];
      row.lastCollectTime = records.length > 0 ? (records[0].tm || records[0].collectTime || '无数据') : '无数据';
    } else {
      // 安全/视频测站 mock
      row.lastCollectTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      }).replace(/\//g, '-');
    }
    ElMessage.success('数据测试成功');
  } catch (e) {
    row.lastCollectTime = '无数据';
    ElMessage.error('数据测试失败');
  }
};

// 保存关联测站
const saveStationRelation = async () => {
  // 验证是否存在空数据
  const emptyRow = editData.value.stationList.find(
    (item) => !item.category || !item.stationCode
  );
  if (emptyRow) {
    ElMessage.warning("监测分类和测站不可为空！");
    return;
  }

  // 验证是否存在重复的监测分类和测站组合
  const uniqueSet = new Set();
  let hasDuplicate = false;
  for (const item of editData.value.stationList) {
    const key = `${item.category}-${item.stationCode}`;
    if (uniqueSet.has(key)) {
      hasDuplicate = true;
      break;
    }
    uniqueSet.add(key);
  }
  if (hasDuplicate) {
    ElMessage.warning("监测分类和测站不可重复！");
    return;
  }

  // 组装参数
  const gateStationRelationPOList = editData.value.stationList.map(item => ({
    id: item.id,
    tenantId: item.tenantId || null,
    category: item.category,
    stationCode: item.stationCode,
    gateCode: editData.value.sluiceCode,
    lastCollectionTime: item.lastCollectTime || ""
  }));

  const params = {
    gateCode: editData.value.sluiceCode,
    tenantId: null, // 如有租户ID请补充
    gateStationRelationPOList
  };

  try {
    await addOrUpdateOrDeleteGateStationRelation(params);
    ElMessage.success("保存成功");
    editDialog.value = false;
    getSluiceList();
  } catch (e) {
    ElMessage.error("保存失败");
  }
};

// 按类型、关键字、页码请求测站数据
const fetchStationListByType = async (type, keyword = '', pageNum = 1) => {
  const pageSize = stationOptions[type].pageSize;
  stationOptions[type].loading = true;
  try {
    let res, records = [], total = 0;
    if (type === 1) {
      // 雨情测站
      res = await selectStaList({ type: 'rain', pageNum, pageSize, keyword });
      records = res.data.records || [];
      total = res.data.total || 0;
      stationOptions[1].list = pageNum === 1 ? [] : stationOptions[1].list;
      stationOptions[1].list.push(...records.map(st => ({
        value: st.stcd,
        label: `${st.stnm} ${st.stcd}`,
        stationName: st.stnm,
        stationCode: st.stcd
      })));
    } else if (type === 2) {
      // 水情测站
      res = await selectStaList({ type: 'water', pageNum, pageSize, keyword });
      records = res.data.records || [];
      total = res.data.total || 0;
      stationOptions[2].list = pageNum === 1 ? [] : stationOptions[2].list;
      stationOptions[2].list.push(...records.map(st => ({
        value: st.stcd,
        label: `${st.stnm} ${st.stcd}`,
        stationName: st.stnm,
        stationCode: st.stcd
      })));
    } else if (type === 3) {
      // 安全测站
      res = await fetchSafetyMonitoringStationList({ pageNum, pageSize, keyword });
      records = res.rows || [];
      total = res.total || 0;
      stationOptions[3].list = pageNum === 1 ? [] : stationOptions[3].list;
      stationOptions[3].list.push(...records.map(st => ({
        value: st.stationCode,
        label: `${st.stationName} ${st.stationCode}`,
        stationName: st.stationName,
        stationCode: st.stationCode
      })));
    } else if (type === 4) {
      // 视频监控测站
      res = await getVideoMonitorStationList({ pageNum, pageSize, keyword });
      records = res.rows || [];
      total = res.total || 0;
      stationOptions[4].list = pageNum === 1 ? [] : stationOptions[4].list;
      stationOptions[4].list.push(...records.map(st => ({
        value: st.stationCode,
        label: `${st.stationName} ${st.stationCode}`,
        stationName: st.stationName,
        stationCode: st.stationCode
      })));
    }
    stationOptions[type].total = total;
    stationOptions[type].pageNum = pageNum;
    stationOptions[type].keyword = keyword;
  } catch (e) {
    stationOptions[type].list = [];
    stationOptions[type].total = 0;
  } finally {
    stationOptions[type].loading = false;
  }
};

// 初始化各类型测站选项
const initStationOptions = () => {
  [1, 2, 3, 4].forEach(type => {
    fetchStationListByType(type, '', 1);
  });
};

// 远程搜索
const handleStationSearch = (type, keyword) => {
  // 重置分页，重新请求第一页
  fetchStationListByType(type, keyword, 1);
};

// 下拉滚动加载
const handleStationScroll = (type, event) => {
  const optionList = stationOptions[type];
  // 判断是否还有更多数据
  if (optionList.loading) return;
  const target = event.target;
  if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
    // 还有更多数据
    if (optionList.list.length < optionList.total) {
      fetchStationListByType(type, optionList.keyword, optionList.pageNum + 1);
    }
  }
};

// 初始化
onMounted(() => {
  getSluiceList();
  initStationOptions();
});
</script>

<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" inline class="form-container">
      <el-form-item label="水闸名称" prop="sluiceName">
        <el-input
          v-model="queryParams.sluiceName"
          placeholder="请输入水闸名称"
          clearable
        />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="Refresh" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <!-- 表格区域 -->
      <el-table v-loading="loading" :data="tableData" stripe>
        <el-table-column type="index" label="序号" width="65" align="center" />
        <el-table-column prop="gateName" label="水闸名称" align="center" />
        <el-table-column prop="gateCode" label="水闸代码" align="center" />
        <el-table-column prop="districtName" label="所在区县" align="center" />
        <el-table-column
          prop="rainStationInfo"
          label="雨情监测"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="waterStationInfo"
          label="水情监测"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="safetyStationInfo"
          label="安全监测"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="videoStationInfo"
          label="视频监控"
          align="center"
          show-overflow-tooltip
        />
        <!-- <el-table-column
          prop="pictureStationInfo"
          label="图像监控"
          align="center"
          show-overflow-tooltip
        /> -->
        <el-table-column label="操作" align="center" width="180">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="View"
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <Pagination
        :total="pagination.total"
        v-model:page="pagination.pageNum"
        v-model:limit="pagination.pageSize"
        @pagination="getSluiceList"
      />
    </div>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="detailDialog"
      :title="`查看水闸关联测站 - ${currentDetail.sluiceName}`"
      width="800px"
    >
      <div class="detail-container">
        <div class="detail-header">
          <div class="header-item">
            <span class="label">水闸名称：</span>
            <span class="value">{{ currentDetail.sluiceName }}</span>
          </div>
          <div class="header-item">
            <span class="label">水闸代码：</span>
            <span class="value">{{ currentDetail.sluiceCode }}</span>
          </div>
        </div>

        <el-table
          :data="currentDetail.stationList"
          border
          style="margin-top: 20px"
        >
          <el-table-column
            type="index"
            label="序号"
            width="65"
            align="center"
          />
          <el-table-column label="监测分类" align="center">
            <template #default="scope">
              {{ getTypeLabel(scope.row.category, MONITOR_TYPE_OPTIONS) }}
            </template>
          </el-table-column>
          <el-table-column label="测站" align="center" width="350">
            <template #default="scope">
              {{ scope.row.stationName }} {{ scope.row.stationCode }}
            </template>
          </el-table-column>
          <el-table-column
            prop="lastCollectionTime"
            label="最近数据采集时间"
            align="center"
          />
        </el-table>
      </div>
    </el-dialog>

    <!-- 编辑关联测站对话框 -->
    <el-dialog v-model="editDialog" :title="editTitle" width="900px">
      <div class="edit-container">
        <div class="edit-header">
          <div class="header-item">
            <span class="label">水闸名称：</span>
            <span class="value">{{ editData.sluiceName }}</span>
          </div>
          <div class="header-item">
            <span class="label">水闸代码：</span>
            <span class="value">{{ editData.sluiceCode }}</span>
          </div>
        </div>

        <div class="button-container">
          <el-button type="primary" icon="Plus" @click="addStationRow"
            >新增</el-button
          >
        </div>

        <el-table :data="editData.stationList" border style="margin-top: 15px">
          <el-table-column
            type="index"
            label="序号"
            width="65"
            align="center"
          />
          <el-table-column label="监测分类" align="center" width="180">
            <template #default="scope">
              <el-select
                v-model="scope.row.category"
                placeholder="请选择监测分类"
                style="width: 150px;"
                @change="() => handleMonitorTypeChange(scope.row)"
              >
                <el-option
                  v-for="item in MONITOR_TYPE_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="测站" align="center" width="280">
            <template #default="scope">
              <el-select
                style="width: 250px;"
                v-model="scope.row.stationCode"
                :placeholder="scope.row.category ? '请选择测站' : '请先选择监测分类'"
                :disabled="!scope.row.category"
                filterable
                remote
                :remote-method="(keyword) => handleStationSearch(scope.row.category, keyword)"
                :loading="stationOptions[scope.row.category]?.loading || false"
                :no-data-text="'暂无数据'"
                @scroll="(e) => handleStationScroll(scope.row.category, e)"
                @change="() => handleStationChange(scope.row)"
              >
                <el-option
                  v-for="item in stationOptions[scope.row.category]?.list || []"
                  :key="item.value"
                  :label="item.label"
                  :value="item.stationCode"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="最近数据采集时间" align="center">
            <template #default="scope">
              <span v-if="scope.row.category == 1 || scope.row.category == 2">
                {{ scope.row.lastCollectTime || "无数据" }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="180">
            <template #default="scope">
              <el-button
                v-if="scope.row.category == 1 || scope.row.category == 2"
                type="primary"
                link
                @click="testStationData(scope.row)"
              >数据测试</el-button>
              <el-button
                type="danger"
                link
                @click="removeStationRow(scope.$index)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="saveStationRelation"
            >保存</el-button
          >
          <el-button @click="editDialog = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.button-container {
  margin-top: 15px;
}

.detail-header,
.edit-header {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 15px;

  .header-item {
    margin-right: 40px;

    .label {
      font-weight: bold;
      margin-right: 5px;
    }
  }
}
</style>
