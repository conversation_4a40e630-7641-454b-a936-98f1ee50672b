<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  fetchSafetyMonitoringPointList,
  saveSafetyMonitoringPoint,
  fetchSafetyMonitoringPointDetail,
  deleteSafetyMonitoringPoint,
  fetchSafetyMonitoringStationList, //安全监测站
  fetchSafetyMonitoringSectionList, //安全监测断面
} from "@/api/watershed/ads/index";
import { MONITOR_POINT_TYPE_OPTIONS } from "@/views/system/geo/utils/enum";
import MinMap from "@/components/Map/plugins/drawTool";
import * as turf from "@turf/turf";
import { deepClone, getTypeLabel } from "@/utils";

defineOptions({
  name: "SeepagePressurePoint",
});

// 查询参数
const queryParams = ref({
  stationName: "", // 测站名称
  stationCode: "", // 测站编码
  sectionCode: "", // 断面编号
  pointCode: "", // 测点编码
  pointType: "", // 测点类型
});

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 详情对话框
const detailDialog = ref(false);
const currentDetail = ref({});

// 新增/编辑对话框
const formDialog = ref(false);
const formTitle = ref("");
const formType = ref("add"); // add-新增 edit-编辑

// 安全监测站选项
const stationOptions = ref([]);
// 断面选项
const sectionOptions = ref([]);

// 表单数据
const formData = ref({
  stationId: "", // 安全监测站ID
  stationName: "", // 安全监测站名称
  stationCode: "", // 安全监测站编码
  sectionCode: "", // 断面编号
  pointCode: "", // 测点编码
  instrumentCode: "", // 仪器编号
  longitude: "", // 经度
  latitude: "", // 纬度
  pipeOutletelevation: "", // 管口高程
  pipeBottomelevation: "", // 管底高程
  elevation: "", // 安装高程
  axisDistance: "", // 轴距
  waterDepthThreshold: "", // 水深阈值
  remarks: "", // 备注
});

// 表单规则
const rules = reactive({
  stationCode: [
    { required: true, message: "安全监测站不能为空", trigger: "change" },
  ],
  sectionCode: [
    { required: true, message: "断面编号不能为空", trigger: "change" },
  ],
  pointCode: [
    { required: true, message: "测点编码不能为空", trigger: "blur" },
    { min: 8, max: 8, message: "测点编码必须为8位字符", trigger: "blur" },
  ],
  pointType: [
    { required: true, message: "监测点类型不能为空", trigger: "change" },
  ],
  remarks: [{ max: 100, message: "备注不能超过100个字", trigger: "blur" }],
});

// 表单引用
const formRef = ref(null);
// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    // 合并分页参数和查询参数
    const params = {
      ...queryParams.value,
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
    };
    // 调用接口
    const res = await fetchSafetyMonitoringPointList(params);
    // 适配接口返回结构
    // 假设接口返回 { data: { list: [], total: 0 } }
    tableData.value = res?.rows || [];
    pagination.value.total = res?.total || 0;
  } catch (e) {
    ElMessage.error("获取安全监测点列表失败");
  } finally {
    loading.value = false;
  }
};

// 查询按钮
const handleQuery = () => {
  pagination.value.pageNum = 1;
  getList();
};

// 重置按钮
const handleReset = () => {
  queryParams.value = {
    stationName: "",
    stationCode: "",
    sectionCode: "",
    pointCode: "",
    pointType: "",
  };
  handleQuery();
};

// 查看详情
const handleDetail = async (row) => {
  formTitle.value = "安全监测点 - " + row.pointCode;
  loading.value = true;
  try {
    // 用接口获取详情
    const res = await fetchSafetyMonitoringPointDetail(row.id);
    currentDetail.value = res?.data || {};
    // 若有经纬度，生成point用于地图
    if (currentDetail.value.longitude && currentDetail.value.latitude) {
      currentDetail.value.point = turf.point([
        currentDetail.value.longitude,
        currentDetail.value.latitude,
      ]);
    }
    detailDialog.value = true;
  } catch (e) {
    ElMessage.error("获取详情失败");
  } finally {
    loading.value = false;
  }
};

// 加载监测站选项（接口）
const loadStationOptions = async () => {
  try {
    const res = await fetchSafetyMonitoringStationList({
      pageNum: 1,
      pageSize: 99999,
    });
    // 假设接口返回 { data: [{ id, stationName, stationCode }] }
    stationOptions.value = (res?.rows || []).map((item) => ({
      value: item.stationCode,
      label: item.stationName,
      stationCode: item.stationCode,
      stationName: item.stationName,
    }));
  } catch (e) {
    stationOptions.value = [];
    ElMessage.error("获取安全监测站列表失败");
  }
};

// 加载断面选项（接口）
const loadSectionOptions = async (stationCode) => {
  if (!stationCode) {
    sectionOptions.value = [];
    return;
  }
  try {
    const res = await fetchSafetyMonitoringSectionList({
      stationCode,
      pageNum: 1,
      pageSize: 99999,
    });
    sectionOptions.value = (res?.rows || []).map((section) => ({
      value: section.sectionCode,
      label: section.sectionName,
    }));
  } catch (e) {
    sectionOptions.value = [];
  }
};

// 监测站变化时更新断面选项
const handleStationChange = async (stationCode) => {
  if (!stationCode) {
    sectionOptions.value = [];
    formData.value.sectionCode = "";
    return;
  }
  // 选中监测站后自动填充stationCode和stationName
  const station = stationOptions.value.find(
    (item) => item.value === stationCode
  );
  if (station) {
    formData.value.stationCode = station.stationCode;
    formData.value.stationName = station.stationName;
  }
  await loadSectionOptions(stationCode);
};

// 新增按钮
const handleAdd = async () => {
  resetForm();
  formTitle.value = "新增安全监测点";
  formType.value = "add";
  formDialog.value = true;
};

// 编辑按钮
const handleEdit = async (row) => {
  resetForm();
  formTitle.value = "编辑安全监测点";
  formType.value = "edit";
  loading.value = true;
  try {
    const res = await fetchSafetyMonitoringPointDetail(row.id);
    formData.value = { ...res?.data };
    await handleStationChange(formData.value.stationCode);
    formDialog.value = true;
  } catch (e) {
    ElMessage.error("获取详情失败");
  } finally {
    loading.value = false;
  }
};

// 删除按钮
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确认要删除该安全监测点-[${row.pointCode}]吗？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(async () => {
      const res = await deleteSafetyMonitoringPoint(row.id);
      if (res.code === 200) {
        ElMessage.success("删除成功");
        getList();
      }
    })
    .catch(() => {});
};

// 重置表单
const resetForm = () => {
  formData.value = {
    stationId: "",
    stationName: "",
    stationCode: "",
    sectionCode: "",
    pointCode: "",
    instrumentCode: "",
    longitude: "",
    latitude: "",
    pipeOutletelevation: "",
    pipeBottomelevation: "",
    elevation: "",
    axisDistance: "",
    waterDepthThreshold: "",
    remarks: "",
  };

  sectionOptions.value = [];

  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 提交表单
const submitForm = async () => {
  const copyFormData = deepClone(formData.value);
  delete copyFormData.stationName;
  formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        await saveSafetyMonitoringPoint(copyFormData);
        ElMessage.success(formType.value === "add" ? "添加成功" : "更新成功");
        formDialog.value = false;
        getList();
      } catch (e) {
        ElMessage.error((formType.value === "add" ? "添加" : "更新") + "失败");
      } finally {
        loading.value = false;
      }
    }
  });
};

onMounted(() => {
  getList();
  loadStationOptions();
});
</script>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="测站名称" prop="stationCode">
        <el-select
          v-model="queryParams.stationCode"
          placeholder="请选择安全监测站"
          clearable
          class="w-full"
          filterable
        >
          <el-option
            v-for="item in stationOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="测点类型" prop="pointType">
        <el-select
          v-model="queryParams.pointType"
          placeholder="请选择测点类型"
          clearable
          class="w-full"
          filterable
        >
          <el-option
            v-for="item in MONITOR_POINT_TYPE_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="断面编号" prop="sectionCode">
        <el-input
          v-model="queryParams.sectionCode"
          placeholder="请输入断面编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测点编码" prop="pointCode">
        <el-input
          v-model="queryParams.pointCode"
          placeholder="请输入测点编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button @click="handleReset" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="content content-table">
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd"
          >新增</el-button
        >
      </div>
      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableData" stripe>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="stationName" label="测站名称" align="center" />
        <el-table-column prop="stationCode" label="测站编码" align="center" />

        <el-table-column prop="sectionName" label="断面名称" align="center" />
        <el-table-column prop="sectionCode" label="断面编号" align="center" />
        <el-table-column prop="pointCode" label="测点编码" align="center" />
        <el-table-column prop="pointType" label="测点类型" align="center">
          <template #default="scope">
            {{ getTypeLabel(scope.row.pointType, MONITOR_POINT_TYPE_OPTIONS) }}
          </template>
        </el-table-column>
        <el-table-column prop="axisDistance" label="轴距(m)" align="center" />
        <el-table-column prop="elevation" label="高程(m)" align="center" />
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="View"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        v-model:page="pagination.pageNum"
        v-model:limit="pagination.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="formDialog"
      width="700px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="安全监测站" prop="stationCode">
              <el-select
                v-model="formData.stationCode"
                placeholder="请选择安全监测站"
                clearable
                class="w-full"
                @change="handleStationChange"
                :disabled="formType === 'edit'"
              >
                <el-option
                  v-for="item in stationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="断面名称" prop="sectionCode">
              <el-select
                v-model="formData.sectionCode"
                placeholder="请选择断面"
                clearable
                class="w-full"
                :disabled="formType === 'edit'"
              >
                <el-option
                  v-for="item in sectionOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="测点编码" prop="pointCode">
              <el-input
                v-model="formData.pointCode"
                placeholder="请输入测点编码"
                :disabled="formType === 'edit'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="监测点类型" prop="pointType">
              <el-select
                v-model="formData.pointType"
                placeholder="请选择监测点类型"
                clearable
                class="w-full"
                :disabled="formType === 'edit'"
              >
                <el-option
                  v-for="item in MONITOR_POINT_TYPE_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="高程(m)" prop="elevation">
              <el-input-number
                v-model="formData.elevation"
                placeholder="请输入安装高程"
                :precision="2"
                class="w-full"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number
                v-model="formData.longitude"
                placeholder="请输入经度"
                :precision="6"
                :min="73"
                :max="136"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number
                v-model="formData.latitude"
                placeholder="请输入纬度"
                :precision="6"
                :min="3"
                :max="54"
                class="w-full"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="轴距(m)" prop="axisDistance">
              <el-input-number
                v-model="formData.axisDistance"
                placeholder="请输入轴距"
                :precision="2"
                class="w-full"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="formData.remarks"
                type="textarea"
                placeholder="请输入备注"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="formDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="detailDialog"
      width="800px"
      append-to-body
      class="detail-dialog"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="安全监测站名称">{{
          currentDetail.stationName
        }}</el-descriptions-item>
        <el-descriptions-item label="安全监测站编码">{{
          currentDetail.stationCode
        }}</el-descriptions-item>
        <el-descriptions-item label="断面名称">{{
          currentDetail.sectionName
        }}</el-descriptions-item>
        <el-descriptions-item label="断面编号">{{
          currentDetail.sectionCode
        }}</el-descriptions-item>
        <el-descriptions-item label="测点编码">{{
          currentDetail.pointCode
        }}</el-descriptions-item>
        <el-descriptions-item label="监测点类型">{{
          getTypeLabel(currentDetail.pointType, MONITOR_POINT_TYPE_OPTIONS)
        }}</el-descriptions-item>
        <el-descriptions-item label="高程(m)">{{
          currentDetail.elevation || "--"
        }}</el-descriptions-item>

        <el-descriptions-item label="轴距(m)">{{
          currentDetail.axisDistance || "--"
        }}</el-descriptions-item>
      </el-descriptions>

      <!-- 备注信息 -->
      <div class="remarks-section" v-if="currentDetail.remarks">
        <h3>备注</h3>
        <div class="remarks-content">{{ currentDetail.remarks }}</div>
      </div>

      <!-- mini地图 -->
      <div class="miniMap-container" v-if="currentDetail.point">
        <h3>安全监测点空间信息预览</h3>
        <MinMap
          :geom="currentDetail.point"
          :icon="[
            {
              icon: 'warning.png',
              label: '安全监测点',
            },
          ]"
        />
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.remarks-section {
  margin-top: 20px;

  h3 {
    font-size: 16px;
    margin-bottom: 15px;
    font-weight: 500;
  }
}
.miniMap-container {
  margin-top: 20px;
  margin-bottom: 20px;
  // padding: 0 10px 0 10px;
  height: 300px;
  h3 {
    font-size: 16px;
    margin-bottom: 15px;
    font-weight: 500;
  }
}

.remarks-content {
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 50px;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.detail-dialog) {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px 24px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
      border-radius: 3px;
    }
  }
}
</style>
