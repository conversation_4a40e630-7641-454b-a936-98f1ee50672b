<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getTypeLabel } from "@/utils";
import MinMap from "@/components/Map/plugins/drawTool";
import {
  CAMERA_TYPE_OPTIONS,
  DATA_TYPE_OPTIONS,
} from "@/views/system/geo/utils/enum";
// 引入接口方法
import {
  getVideoMonitorPointList,
  saveOrUpdateVideoMonitorPoint,
  getVideoMonitorPointDetail,
  deleteVideoMonitorPoint,
  getVideoMonitorStationList
} from "@/api/video/index";

defineOptions({
  name: "VideoSurveillancePoint",
});

// 查询参数
const queryParams = ref({
  stationName: "", // 测站名称
  stationCode: "", // 测站编码
  pointCode: "", // 测点编码
  dataType: [], // 数据类型
});

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 详情对话框
const detailDialog = ref(false);
const currentDetail = ref({});

// 新增/编辑对话框
const formDialog = ref(false);
const formTitle = ref("");
const formType = ref("add"); // add-新增 edit-编辑

// 表单数据
const formData = ref({
  stationName: "", // 视频监控站
  stationCode: "", // 视频监控站编码
  pointCode: "", // 监控点编码
  location: "", // 监控部位
  rtuCode: "", // RTU编码
  longitude: "", // 经度
  latitude: "", // 纬度
  cameraName: "", // 摄像头名称
  cameraCode: "", // 摄像头编码
  cameraType: "", // 摄像头类型
  dataType: [], // 数据类型
  cameraBrand: "", // 摄像头品牌
  cameraModel: "", // 摄像头型号
  zoom: "", // 变焦
  resolution: "", // 像素
  frequency: "", // 拍照频率
  videoPlatform: "", // 视频平台
  channelCode: "", // 通道编码
  channelNum: "", // 通道数
  remark: "", // 备注
});

// 表单规则
const rules = reactive({
  stationCode: [
    { required: true, message: "视频监控站不能为空", trigger: "change" },
  ],
  pointCode: [
    { required: true, message: "监控点编码不能为空", trigger: "blur" },
    { min: 18, max: 18, message: "监控点编码必须为18位字符", trigger: "blur" },
  ],
  location: [
    { max: 15, message: "监控部位不能超过15个字", trigger: "blur" },
  ],
  rtuCode: [
    { required: true, message: "RTU编码不能为空", trigger: "blur" },
    { min: 18, max: 18, message: "RTU编码必须为18位字符", trigger: "blur" },
  ],
  longitude: [
    {
      type: "number",
      min: 73,
      max: 136,
      message: "经度范围应为73~136",
      trigger: "blur",
    },
  ],
  latitude: [
    {
      type: "number",
      min: 3,
      max: 54,
      message: "纬度范围应为3~54",
      trigger: "blur",
    },
  ],
  cameraName: [
    { max: 15, message: "摄像头名称不能超过15个字", trigger: "blur" },
  ],
  cameraCode: [
    { min: 18, max: 18, message: "摄像头编码必须为18位字符", trigger: "blur" },
  ],
  dataType: [
    { required: true, message: "数据类型不能为空", trigger: "change" },
    {
      type: "array",
      min: 1,
      message: "至少选择一种数据类型",
      trigger: "change",
    },
  ],
  cameraBrand: [
    { max: 15, message: "摄像头品牌不能超过15个字", trigger: "blur" },
  ],
  cameraModel: [
    { max: 15, message: "摄像头型号不能超过15个字", trigger: "blur" },
  ],
  zoom: [
    {
      pattern: /^\d*$/,
      message: "变焦必须为非负整数",
      trigger: "blur",
    },
  ],
  resolution: [
    {
      pattern: /^\d*$/,
      message: "像素必须为非负整数",
      trigger: "blur",
    },
  ],
  frequency: [
    {
      pattern: /^\d*$/,
      message: "拍照频率必须为非负整数",
      trigger: "blur",
    },
  ],
  remark: [{ max: 100, message: "备注不能超过100个字", trigger: "blur" }],
});

// 动态验证规则
const validationRules = computed(() => {
  const baseRules = { ...rules };

  // 只在数据类型包含视频时添加相关验证规则
  if (isVideoType.value) {
    baseRules.videoPlatform = [
      { required: true, message: "视频平台不能为空", trigger: "change" },
    ];
    baseRules.channelCode = [
      { required: true, message: "通道编码不能为空", trigger: "blur" },
      { min: 18, max: 18, message: "通道编码必须为18位字符", trigger: "blur" },
    ];
    baseRules.channelNum = [
      { required: true, message: "通道数不能为空", trigger: "blur" },
      { min: 1, max: 1, message: "通道数必须为1位字符", trigger: "blur" },
    ];
  }

  return baseRules;
});

// 表单引用
const formRef = ref(null);

// 视频监控站选项
const stationOptions = ref([]);

// 视频平台选项
const videoPlatformOptions = ref([
  { label: "平台一", value: "platform1" },
  { label: "平台二", value: "platform2" },
  { label: "平台三", value: "platform3" },
]);

// 判断是否为视频数据类型
const isVideoType = computed(() => {
  return formData.value.dataType.includes("video");
});

// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    // 适配查询参数
    const params = {
      stationName: queryParams.value.stationName,
      stationCode: queryParams.value.stationCode,
      pointCode: queryParams.value.pointCode,
      dataTypes: queryParams.value.dataType && queryParams.value.dataType.length > 0 ? queryParams.value.dataType.join(",") : undefined,
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize
    };
    const res = await getVideoMonitorPointList(params);
    // 适配接口分页结构
    tableData.value = (res.rows || []).map((item, idx) => ({
      ...item,
      // 适配部分字段（如有需要）
      location: item.monitorLocation,
      // 特殊处理：dataTypes为'3'时，回显为['1','2']
      dataType: item.dataTypes === "3" ? ["1","2"] : (item.dataTypes ? item.dataTypes.split(",") : []),
      resolution: item.pixel,
      frequency: item.captureFrequency,
      remark: item.remarks
    }));
    pagination.value.total = res.total || 0;
  } catch (e) {
    ElMessage.error("获取视频监控点列表失败");
  } finally {
    loading.value = false;
  }
};

// 查询按钮
const handleQuery = () => {
  pagination.value.pageNum = 1;
  getList();
};

// 重置按钮
const handleReset = () => {
  queryParams.value = {
    stationName: "",
    stationCode: "",
    pointCode: "",
    dataType: [],
  };
  pagination.value.pageNum = 1;
  getList();
};

// 查看详情
const handleDetail = async (row) => {
  formTitle.value = "视频监控点详情 - " + row.pointCode;
  try {
    const res = await getVideoMonitorPointDetail(row.id);
    // 字段适配
    currentDetail.value = {
      ...res.data,
      location: res.data.monitorLocation,
      // 特殊处理：dataTypes为'3'时，回显为['1','2']
      dataType: res.data.dataTypes === "3" ? ["1","2"] : (res.data.dataTypes ? res.data.dataTypes.split(",") : []),
      resolution: res.data.pixel,
      frequency: res.data.captureFrequency,
      remark: res.data.remarks,
      videoPlatform: res.data.videoPlatformCode,
      channelNum: res.data.channelCount
    };
    detailDialog.value = true;
  } catch (e) {
    ElMessage.error("获取详情失败");
  }
};

// 新增按钮
const handleAdd = () => {
  resetForm();
  formTitle.value = "新增视频监控点";
  formType.value = "add";
  formDialog.value = true;
};

// 编辑按钮
const handleEdit = async (row) => {
  resetForm();
  formTitle.value = "编辑视频监控点";
  formType.value = "edit";
  try {
    const res = await getVideoMonitorPointDetail(row.id);
    // 字段适配，与handleDetail一致
    formData.value = {
      ...res.data,
      stationName: res.data.stationName,
      stationCode: res.data.stationCode,
      pointCode: res.data.pointCode,
      location: res.data.monitorLocation,
      rtuCode: res.data.rtuCode,
      longitude: res.data.longitude,
      latitude: res.data.latitude,
      cameraName: res.data.cameraName,
      cameraCode: res.data.cameraCode,
      cameraType: res.data.cameraType,
      // 特殊处理：dataTypes为'3'时，回显为['1','2']
      dataType: res.data.dataTypes === "3" ? ["1","2"] : (res.data.dataTypes ? res.data.dataTypes.split(",") : []),
      cameraBrand: res.data.cameraBrand,
      cameraModel: res.data.cameraModel,
      zoom: res.data.zoom,
      resolution: res.data.pixel,
      frequency: res.data.captureFrequency,
      videoPlatform: res.data.videoPlatformCode,
      channelCode: res.data.channelCode,
      channelNum: res.data.channelCount,
      remark: res.data.remarks
    };
    formDialog.value = true;
  } catch (e) {
    ElMessage.error("获取编辑详情失败");
  }
};

// 删除按钮
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认要删除该视频监控点[${row.pointCode}]吗？`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        await deleteVideoMonitorPoint(row.id);
        ElMessage.success("删除成功");
        getList();
      } catch (e) {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};

// 重置表单
const resetForm = () => {
  formData.value = {
    stationName: "", // 视频监控站
    stationCode: "", // 视频监控站编码
    pointCode: "", // 监控点编码
    location: "", // 监控部位
    rtuCode: "", // RTU编码
    longitude: "", // 经度
    latitude: "", // 纬度
    cameraName: "", // 摄像头名称
    cameraCode: "", // 摄像头编码
    cameraType: "", // 摄像头类型
    dataType: [], // 数据类型
    cameraBrand: "", // 摄像头品牌
    cameraModel: "", // 摄像头型号
    zoom: "", // 变焦
    resolution: "", // 像素
    frequency: "", // 拍照频率
    videoPlatform: "", // 视频平台
    channelCode: "", // 通道编码
    channelNum: "", // 通道数
    remark: "", // 备注
  };
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 监控站下拉框选择处理
const handleStationChange = (stationCode) => {
  if (!stationCode) return;
  const station = stationOptions.value.find((opt) => opt.value === stationCode);
  if (station) {
    formData.value.stationName = station.stationName;
  }
};

// 监听数据类型变化
const handleDataTypeChange = () => {
  // 当数据类型不包含视频时，清空视频相关字段
  if (!isVideoType.value) {
    formData.value.videoPlatform = "";
    formData.value.channelCode = "";
    formData.value.channelNum = "";
  }
};

// 提交表单
const submitForm = () => {
  // 自定义验证：如果包含视频类型但缺少相关字段，则提示错误
  if (isVideoType.value) {
    if (!formData.value.videoPlatform) {
      ElMessage.error("数据类型包含视频时，视频平台不能为空");
      return;
    }
    if (!formData.value.channelCode) {
      ElMessage.error("数据类型包含视频时，通道编码不能为空");
      return;
    }
    if (!formData.value.channelNum) {
      ElMessage.error("数据类型包含视频时，通道数不能为空");
      return;
    }
  }

  formRef.value.validate(async (valid) => {
    if (valid) {
      // 字段适配
      // 特殊处理：如果数据类型为["1","2"]或["2","1"]，则dataTypes传"3"，否则按原有逻辑
      let dataTypesValue = formData.value.dataType;
      if (Array.isArray(dataTypesValue)) {
        const sorted = [...dataTypesValue].sort();
        if (sorted.length === 2 && sorted[0] === "1" && sorted[1] === "2") {
          dataTypesValue = "3";
        } else {
          dataTypesValue = dataTypesValue.join(",");
        }
      }
      const submitData = {
        ...formData.value,
        monitorLocation: formData.value.location,
        dataTypes: dataTypesValue,
        pixel: formData.value.resolution,
        captureFrequency: formData.value.frequency,
        remarks: formData.value.remark
      };
      // 移除前端多余字段
      delete submitData.location;
      delete submitData.dataType;
      delete submitData.resolution;
      delete submitData.frequency;
      delete submitData.remark;
      try {
        await saveOrUpdateVideoMonitorPoint(submitData);
        ElMessage.success(formType.value === "add" ? "添加成功" : "更新成功");
        formDialog.value = false;
        getList();
      } catch (e) {
        ElMessage.error(formType.value === "add" ? "添加失败" : "更新失败");
      }
    }
  });
};

// 获取视频平台名称
const getVideoPlatformLabel = (value) => {
  const platform = videoPlatformOptions.value.find(
    (item) => item.value === value
  );
  return platform ? platform.label : value || "--";
};

// 监控站下拉动态获取
const initStationOptions = async () => {
  try {
    const res = await getVideoMonitorStationList({
      pageNum: 1,
      pageSize: 9999
    });
    stationOptions.value = (res.rows || []).map(item => ({
      label: item.stationName,
      value: item.stationCode,
      stationName: item.stationName
    }));
  } catch (e) {
    stationOptions.value = [];
  }
};

onMounted(() => {
  initStationOptions();
  getList();
});
</script>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="form-container">
      <el-form-item label="监控站名称" prop="stationName">
        <el-input
          v-model="queryParams.stationName"
          placeholder="请输入监控站名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="监控站编码" prop="stationCode">
        <el-input
          v-model="queryParams.stationCode"
          placeholder="请输入监控站编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测点编码" prop="pointCode">
        <el-input
          v-model="queryParams.pointCode"
          placeholder="请输入测点编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据类型" prop="dataType">
        <el-select
          v-model="queryParams.dataType"
          placeholder="请选择数据类型"
          clearable
          multiple
          class="w-full"
          collapse-tags
          collapse-tags-tooltip
        >
          <el-option
            v-for="item in DATA_TYPE_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button @click="handleReset" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="content content-table">
      <!-- 操作按钮 -->
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd"
          >新增</el-button
        >
      </div>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableData" stripe>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="stationName" label="测站名称" align="center" />
        <el-table-column prop="stationCode" label="测站编码" align="center" />
        <el-table-column prop="location" label="监控部位" align="center" />
        <el-table-column prop="pointCode" label="监控点编码" align="center" />
        <el-table-column label="摄像头类型" align="center">
          <template #default="scope">
            {{ getTypeLabel(scope.row.cameraType, CAMERA_TYPE_OPTIONS) }}
          </template>
        </el-table-column>
        <el-table-column label="数据类型" align="center">
          <template #default="scope">
            {{ getTypeLabel(scope.row.dataType, DATA_TYPE_OPTIONS) }}
          </template>
        </el-table-column>
        <el-table-column label="视频平台" align="center">
          <template #default="scope">
            <span v-if="scope.row.dataType.includes('video')">
              {{ getVideoPlatformLabel(scope.row.videoPlatform) }}
            </span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="通道编码" align="center">
          <template #default="scope">
            <span v-if="scope.row.dataType.includes('video')">
              {{ scope.row.channelCode || "--" }}
            </span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="通道数" align="center" width="80">
          <template #default="scope">
            <span v-if="scope.row.dataType.includes('video')">
              {{ scope.row.channelNum || "--" }}
            </span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="rtuCode" label="RTU编码" align="center" />
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="View"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        v-model:page="pagination.pageNum"
        v-model:limit="pagination.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="formDialog"
      width="700px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="validationRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="视频监控站" prop="stationCode">
              <el-select
                v-model="formData.stationCode"
                placeholder="请选择视频监控站"
                clearable
                class="w-full"
                @change="handleStationChange"
                :disabled="formType === 'edit'"
              >
                <el-option
                  v-for="item in stationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="监控点编码" prop="pointCode">
              <el-input
                v-model="formData.pointCode"
                placeholder="请输入监控点编码"
                :disabled="formType === 'edit'"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="监控部位" prop="location">
              <el-input
                v-model="formData.location"
                placeholder="请输入监控部位"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="RTU编码" prop="rtuCode">
              <el-input
                v-model="formData.rtuCode"
                placeholder="请输入RTU编码"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number
                v-model="formData.longitude"
                placeholder="请输入经度"
                :precision="6"
                :min="73"
                :max="136"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number
                v-model="formData.latitude"
                placeholder="请输入纬度"
                :precision="6"
                :min="3"
                :max="54"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="摄像头名称" prop="cameraName">
              <el-input
                v-model="formData.cameraName"
                placeholder="请输入摄像头名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="摄像头编码" prop="cameraCode">
              <el-input
                v-model="formData.cameraCode"
                placeholder="请输入摄像头编码"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="摄像头类型" prop="cameraType">
              <el-select
                v-model="formData.cameraType"
                placeholder="请选择摄像头类型"
                clearable
                class="w-full"
              >
                <el-option
                  v-for="item in CAMERA_TYPE_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据类型" prop="dataType">
              <el-select
                v-model="formData.dataType"
                placeholder="请选择数据类型"
                multiple
                class="w-full"
                :disabled="formType === 'edit'"
                @change="handleDataTypeChange"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="item in DATA_TYPE_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="摄像头品牌" prop="cameraBrand">
              <el-input
                v-model="formData.cameraBrand"
                placeholder="请输入摄像头品牌"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="摄像头型号" prop="cameraModel">
              <el-input
                v-model="formData.cameraModel"
                placeholder="请输入摄像头型号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="变焦(mm)" prop="zoom">
              <el-input v-model="formData.zoom" placeholder="请输入变焦" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="像素(px)" prop="resolution">
              <el-input
                v-model="formData.resolution"
                placeholder="请输入像素"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="拍照频率(Hz)" prop="frequency">
              <el-input
                v-model="formData.frequency"
                placeholder="请输入拍照频率"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="isVideoType">
          <el-col :span="12">
            <el-form-item label="视频平台" prop="videoPlatform">
              <el-select
                v-model="formData.videoPlatform"
                placeholder="请选择视频平台"
                clearable
                class="w-full"
              >
                <el-option
                  v-for="item in videoPlatformOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="通道编码" prop="channelCode">
              <el-input
                v-model="formData.channelCode"
                placeholder="请输入通道编码"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="isVideoType">
          <el-col :span="12">
            <el-form-item label="通道数" prop="channelNum">
              <el-input
                v-model="formData.channelNum"
                placeholder="请输入通道数"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="formDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="detailDialog"
      width="800px"
      append-to-body
      class="detail-dialog"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="视频监控站名称">{{
          currentDetail.stationName
        }}</el-descriptions-item>
        <el-descriptions-item label="视频监控站编码">{{
          currentDetail.stationCode
        }}</el-descriptions-item>
        <el-descriptions-item label="监控部位">{{
          currentDetail.location || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="监控点编码">{{
          currentDetail.pointCode
        }}</el-descriptions-item>
        <el-descriptions-item label="摄像头名称">{{
          currentDetail.cameraName || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="摄像头编码">{{
          currentDetail.cameraCode || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="摄像头类型">{{
          getTypeLabel(currentDetail.cameraType, CAMERA_TYPE_OPTIONS)
        }}</el-descriptions-item>
        <el-descriptions-item label="数据类型">{{
          getTypeLabel(currentDetail.dataType, DATA_TYPE_OPTIONS)
        }}</el-descriptions-item>
        <el-descriptions-item label="摄像头品牌">{{
          currentDetail.cameraBrand || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="摄像头型号">{{
          currentDetail.cameraModel || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="变焦(mm)">{{
          currentDetail.zoom || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="像素(px)">{{
          currentDetail.resolution || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="拍照频率(Hz)">{{
          currentDetail.frequency || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="RTU编码">{{
          currentDetail.rtuCode
        }}</el-descriptions-item>

        <!-- 只有当数据类型包含视频时才显示视频相关信息 -->
        <template
          v-if="
            currentDetail.dataType && currentDetail.dataType.includes('video')
          "
        >
          <el-descriptions-item label="视频平台">{{
            getVideoPlatformLabel(currentDetail.videoPlatform)
          }}</el-descriptions-item>
          <el-descriptions-item label="通道编码">{{
            currentDetail.channelCode || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="通道数">{{
            currentDetail.channelNum || "--"
          }}</el-descriptions-item>
        </template>

        <el-descriptions-item label="备注" :span="2">{{
          currentDetail.remark || "--"
        }}</el-descriptions-item>
      </el-descriptions>

      <!-- 空间信息预览 -->
      <div
        class="location-preview"
        v-if="currentDetail.longitude && currentDetail.latitude"
      >
        <h3>视频监控点空间信息预览</h3>
        <div class="map-container">
          <MinMap
            :points="[currentDetail.longitude, currentDetail.latitude]"
            class="w-full h-full"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.map-container {
  height: 300px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-top: 15px;
}

.location-preview {
  margin-top: 20px;

  h3 {
    font-size: 16px;
    margin-bottom: 15px;
    font-weight: 500;
  }
}

:deep(.detail-dialog) {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px 24px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
      border-radius: 3px;
    }
  }
}
</style>
