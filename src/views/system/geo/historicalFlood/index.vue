<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="历史洪水编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入历史洪水编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属流域" prop="subBasinCode">
        <el-tree-select
          v-model="queryParams.subBasinCode"
          :data="subBasinOptions"
          :props="{ value: 'code', label: 'name', children: 'children' }"
          placeholder="请选择所属流域"
          clearable
          filterable
          style="width: 200px"
          @change="handleQuery"
        />
      </el-form-item>


      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
            <el-button type="success" icon="DocumentAdd" @click="importDialogVisible = true" style="margin-left: 8px;">导入</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </div>
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="rainfallList"
        row-key="code"
        stripe
      >
        <el-table-column type="index" label="序号" width="65" align="center" :index="indexMethod" />
        <el-table-column prop="code" label="历史洪水编号" :show-overflow-tooltip="true" align="center" />
        <el-table-column prop="basinName" label="流域" :show-overflow-tooltip="true" align="center" />
        <el-table-column prop="stnm" label="测站" :show-overflow-tooltip="true" align="center" />
        <el-table-column prop="timeFrame" label="时段长(小时)" align="center" />
        <el-table-column prop="rainfall" label="降雨量（mm）" align="center" />
        <el-table-column prop="peakFlow" label="洪峰流量（m³/s）" align="center" />
        <el-table-column prop="peakFlowTime" label="峰现时间" align="center" />
        <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>


    <!-- 添加或修改对话框 -->
    <el-dialog
      :title="isEdit ? '编辑历史洪水' : isView ? '查看历史洪水' : '新增历史洪水'"
      v-model="editDialogVisible"
      width="1200px"
      append-to-body
      @close="resetEditDialog"
      @opened="onEditDialogOpened"
    >
      <el-form v-if="!isView" ref="editFormRef" :model="editForm" :rules="editRules" label-width="110px" style="margin-bottom: 0;">
        <el-row :gutter="5">
          <el-col :span="6">
            <el-form-item label="流域" prop="subBasinCode">
              <el-tree-select
                v-model="editForm.subBasinCode"
                :data="subBasinOptions"
                :props="{ value: 'basinId', label: 'name', children: 'children' }"
                placeholder="请选择流域"
                clearable
                filterable
                :disabled="isView"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="测站" prop="stationCode">
              <el-select v-model="editForm.stationCode" placeholder="请选择测站" filterable clearable :disabled="isView">
                <el-option v-for="item in stationOptions" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="选择时间段" prop="time">
              <el-date-picker v-model="editForm.time" type="datetimerange" value-format="YYYY-MM-DD HH:mm" format="YYYY-MM-DD HH:mm" placeholder="选择时间" style="width: 190px;" :disabled="isView" :disabled-date="disabledDate" />
            </el-form-item>
          </el-col>
          <el-col :span="2" v-if="!isView" style="padding-left: 20px;">
            <el-button type="primary" @click="handleRainFallData" :loading="rainfallQueryLoading">查询</el-button>
          </el-col>
          <el-col :span="24" style="margin-top: 0px; padding-right: 20px;">
            <el-form-item label="历史洪水编号" prop="code" required>
              <el-input v-model="editForm.code" placeholder="请输入历史洪水编号" clearable :readonly="isView" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-descriptions v-if="isView" :column="2" border style="margin-bottom: 20px;">
        <el-descriptions-item label="历史洪水编号">{{ editForm.code }}</el-descriptions-item>
        <el-descriptions-item label="流域">{{ subBasinOptions.find(i => i.code === editForm.subBasinCode)?.name || editForm.subBasinCode }}</el-descriptions-item>
        <el-descriptions-item label="测站">{{ stationOptions.find(i => i.code === editForm.stationCode)?.name || editForm.stationCode }}</el-descriptions-item>
        <el-descriptions-item label="时段长（小时）">{{ editForm.timeFrame}}</el-descriptions-item>
        <el-descriptions-item label="降雨量（mm）">{{ editForm.rainfall }}</el-descriptions-item>
        <el-descriptions-item label="洪峰流量（m³/s）">{{ editForm.peakFlow }}</el-descriptions-item>
        <el-descriptions-item label="峰现时间">{{ editForm.peakFlowTime }}</el-descriptions-item>
        <!-- <el-descriptions-item label="租户ID">{{ editForm.tenantId }}</el-descriptions-item> -->
      </el-descriptions>
      <div class="rainfall-flex-container" style="margin-top: 0;">
        <!-- 左侧表格 -->
        <div class="rainfall-table-panel">
          <el-table :data="editRainfallTableData" height="360" style="width: 100%;" >
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="time" label="时间" align="center">
              <template #default="scope">
                {{ scope.row.time }}
              </template>
            </el-table-column>
            <el-table-column prop="rainfallValue" label="雨量(mm)" align="center">
              <template #default="scope">
                <el-input-number v-if="!isView" v-model="scope.row.rainfallValue" :min="0" :max="1000" :step="0.1" :precision="1" size="small" style="width: 100px;" placeholder="请输入" />
                <span v-else>{{ scope.row.rainfallValue }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="flowValue" label="流量(m³/s)" align="center">
              <template #default="scope">
                <el-input-number v-if="!isView" v-model="scope.row.flowValue" :min="0" :max="99999" :step="0.1" :precision="1" size="small" style="width: 100px;" placeholder="请输入" />
                <span v-else>{{ scope.row.flowValue }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="rainfall-divider"></div>
        <!-- 右侧图表 -->
        <div class="rainfall-chart-panel">
          <div ref="editRainfallChartRef" class="rainfall-echart"></div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="editSaving" @click="submitEditForm" v-if="!isView">保存</el-button>
          <el-button @click="resetEditDialog" v-if="!isView">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog title="历史洪水导入" v-model="importDialogVisible" width="420px" @close="resetImportDialog">
      <el-upload
        ref="importUploadRef"
        :action="downloadParams.action"
        :data="downloadParams.data"
        :headers="downloadParams.headers"
        :http-request="handleImportUpload"
        :auto-upload="false"
        :show-file-list="true"
        :accept="'.xls,.xlsx'"
        :limit="1"
        drag
        style="width: 100%"
      >
        <i class="el-icon-upload" style="font-size: 48px; color: #bfbfbf;"></i>
        <div class="el-upload__text" style="margin: 16px 0;">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <div style="margin: 12px 0 16px 0; text-align: center;">
        <span>仅允许导入xls，xlsx格式文件。</span>
        <a href="javascript:void(0);" style="color: #409EFF; text-decoration: underline; margin-left: 12px;" @click="downloadTemplate">下载模板</a>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitImport">保存</el-button>
          <el-button @click="resetImportDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { onUnmounted, nextTick, reactive, ref, watch} from "vue";
import {
  selectStaList,
  selectStlyList,
} from "@/api/watershed/ads";
import * as echarts from 'echarts';
import { addFloodHistory, modifyFloodHistory, getFloodHistoryList, deleteFloodHistory, getRainLevelData, getFloodHistoryInfo } from '@/api/watershed/forecast/index.js';


defineOptions({
  name: "TownshipIndex",
});

const { proxy } = getCurrentInstance();
const rainfallList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const refreshTable = ref(true);
const total = ref(0);
// const uploadFileUrl = import.meta.env.VITE_APP_BASE_API + "/file/upload";
const downloadFileUrl = import.meta.env.VITE_APP_BASE_API + "/file/download";
import { getToken } from "@/utils/auth";
const token = getToken();

const data = reactive({
  total: 0,
  form: {
    geoType: "people",
  },
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    // asNext: 1 // 加个asNext 在查询接口里 1是0否
  },
  rules: {
    parentAdcd: [
      { required: true, message: "所属县区不能为空", trigger: "blur" },
    ],
    adcd: [
      { required: true, message: "乡镇代码不能为空", trigger: "blur" },
      {
        pattern: /^\d{12}$/,
        message: "乡镇代码必须为12位数字",
        trigger: ["blur", "change"],
      },
    ],
    adnm: [{ required: true, message: "乡镇名称不能为空", trigger: "blur" }],
    area: [
      {
        pattern: /^\d+(\.\d+)?$/,
        message: "请输入正确的数字格式",
        trigger: ["blur", "change"],
      },
    ],
  },
});
const downloadParams = reactive({
  action: downloadFileUrl,
  data: {
    bucketName: 'watershed'
  },
  headers: {
    Authorization: 'Bearer ' + token
  }
});
const { queryParams } = toRefs(data);

const subBasinOptions = ref([]);
const stationOptions = ref([]);

function indexMethod(index) {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
}

// 递归处理流域树结构，将data字段提升到节点本身
function transformBasinTree(tree) {
  return (tree || []).map(node => {
    const { data, children } = node;
    return {
      ...data,
      children: transformBasinTree(children)
    };
  });
}

async function getList() {
  loading.value = true;
  const params = {
    code: queryParams.value.code,
    basinCode: queryParams.value.subBasinCode,
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize
  };
  try {
    const res = await getFloodHistoryList(params);
    rainfallList.value = res.rows || [];
    total.value = res.total || 0;
  } catch (e) {
    proxy.$modal.msgError('获取历史洪水列表失败');
  } finally {
    loading.value = false;
  }
}

async function initOptions() {
  // 流域
  const basinRes = await selectStlyList({});
  subBasinOptions.value = transformBasinTree(basinRes.data || []);
  // 雨量站
  const stationRes = await selectStaList({ asNext: 1, pageNum: 1, pageSize: 999999, sttp: '' });
  stationOptions.value = (stationRes.data?.records || []).map(item => ({ code: item.stcd, name: `${item.stnm}(${item.stcd})` }));
}

onMounted(async () => {
  await initOptions();
  await getList();
  nextTick(() => {
    if (rainfallChartRef.value) {
      rainfallChartInstance = echarts.init(rainfallChartRef.value);
      updateRainfallChart();
    }
  });
});

function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除编号为"' + row.code + '"的历史洪水?')
    .then(() => deleteFloodHistory(row.code))
    .then(() => {
      proxy.$modal.msgSuccess('删除成功');
      getList();
    })
    .catch(e => {
      if (e && e.msg) proxy.$modal.msgError(e.msg);
    });
}


/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd() {
  isView.value = false;
  openEditDialog();
}
/** 修改按钮操作 */
async function handleUpdate(row) {
  isView.value = false;
  isEdit.value = true;
  openEditDialog(row);
}


const rainfallChartRef = ref(null);
let rainfallChartInstance = null;

function updateRainfallChart() {
  if (!rainfallChartInstance) return;
  // 图表数据正序排列
  const chartData = [...rainfallList.value].sort((a, b) => a.time.localeCompare(b.time));
  rainfallChartInstance.setOption({
    tooltip: { trigger: 'axis' },
    grid: { left: 60, right: 50, top: 30, bottom: 40 },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.time),
      axisLabel: { rotate: 0, fontSize: 12, margin: 16, interval: 'auto' },
      name: '时间',
    },
    yAxis: {
      type: 'value',
      name: '降雨量(mm)',
      min: 0,
    },
    series: [
      {
        name: '降雨量',
        type: 'line',
        data: chartData.map(item => item.rainfall),
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: { width: 2 },
        itemStyle: { color: '#409EFF' },
      },
    ],
  });
}

watch(rainfallList, updateRainfallChart, { deep: true });

const editRainfallTableData = ref([]);
// 新增：联动弹窗表格与图表
watch(editRainfallTableData, updateEditRainfallChart, { deep: true });

const editDialogVisible = ref(false);
const isEdit = ref(false);
const editFormRef = ref();
const editForm = ref({
  code: '',
  subBasinCode: '',
  stationCode: '',
  time: [], // [start, end]
});
const editRainfallChartRef = ref(null);
let editRainfallChartInstance = null;
const editSaving = ref(false);
const rainfallQueryLoading = ref(false);

const disabledDate = (time) => {
  // 禁用“此刻”及之后的所有时间
  return time.getTime() >= Date.now();
};

const editRules = {
  code: [
    { required: true, message: '请输入历史洪水编号', trigger: 'blur' },
    { max: 30, message: '编号不能超过30字符', trigger: 'blur' }
  ],
  subBasinCode: [{ required: true, message: '请选择流域', trigger: 'change' }],
  stationCode: [{  message: '请选择测站', trigger: 'change' }],
  time: [{ required: true, message: '请选择时间范围', trigger: 'change' }],
  rainfall: [{ required: true, message: '请输入降雨量', trigger: 'change' }],
  peakFlow: [{ required: true, message: '请输入洪峰流量', trigger: 'change' }],
  peakFlowTime: [{ required: true, message: '请输入峰现时间', trigger: 'change' }],
  tenantId: [{ required: true, message: '请输入租户ID', trigger: 'change' }]
};

function openEditDialog(row) {
  editDialogVisible.value = true;
  if (row) {
    getFloodHistoryInfo(row.code).then(res => {
      const data = res.data || {};
      // 处理时间范围
      let timeRange = ['', ''];
      if (Array.isArray(data.dataList) && data.dataList.length > 0) {
        timeRange = [
          data.dataList[0].time,
          data.dataList[data.dataList.length - 1].time
        ];
      }
      // 主表单
      editForm.value = {
        code: data.code,
        subBasinCode: data.basinCode,
        stationCode: data.stcd,
        time: timeRange,
        rainfall: data.rainfall,
        peakFlow: data.peakFlow,
        peakFlowTime: data.peakFlowTime,
        tenantId: data.tenantId,
        timeFrame: data.timeFrame
      };
      // 明细表格
      editRainfallTableData.value = (data.dataList || []).map(item => ({
        time: item.time,
        rainfallValue: item.rainfall,
        flowValue: item.flow,
        waterLevel: item.waterLevel
      }));
    });
  } else {
    editForm.value = { code: '', subBasinCode: '', stationCode: '', time: [], rainfall: '', peakFlow: '', peakFlowTime: '', tenantId: '' };
    editRainfallTableData.value = [];
  }
}

function resetEditDialog() {
  editDialogVisible.value = false;
  editForm.value = { code: '', subBasinCode: '', stationCode: '', time: [], rainfall: '', peakFlow: '', peakFlowTime: '', tenantId: '' };
  editRainfallTableData.value = [];
  viewStats.value = {
    totalRainfall: '',
    max1HourRainfall: '',
    max3HourRainfall: '',
    max6HourRainfall: '',
    max12HourRainfall: '',
    max24HourRainfall: '',
  };
  isView.value = false;
  if (typeof isEdit !== 'undefined') isEdit.value = false;
  editSaving.value = false;
}

function submitEditForm() {
  editFormRef.value.validate(async valid => {
    if (!valid) return;
    editSaving.value = true;
    // 组装dataList字段
    const dataList = editRainfallTableData.value.map(item => ({
      floodHistoryCode: editForm.value.code,
      time: item.time,
      rainfall: item.rainfallValue,
      flow: item.flowValue,
      waterLevel: item.waterLevel
    }));
    // 适配接口字段
    const params = {
      code: editForm.value.code,
      basinCode: editForm.value.subBasinCode,
      stcd: editForm.value.stationCode,
      timeFrame: editForm.value.time && editForm.value.time.length === 2 ? (new Date(editForm.value.time[1]).getTime() - new Date(editForm.value.time[0]).getTime()) / (1000 * 60 * 60) : '',
      rainfall: editForm.value.rainfall, // 可根据业务逻辑补充
      flowSum: editForm.value.flowSum,   // 可根据业务逻辑补充
      peakFlow: editForm.value.peakFlow, // 可根据业务逻辑补充
      peakFlowTime: editForm.value.peakFlowTime, // 可根据业务逻辑补充
      tenantId: editForm.value.tenantId, // 可根据业务逻辑补充
      dataList
    };
    try {
      if (isEdit.value) {
        await modifyFloodHistory(params);
        proxy.$modal.msgSuccess('修改成功');
      } else {
        await addFloodHistory(params);
        proxy.$modal.msgSuccess('新增成功');
      }
      editDialogVisible.value = false;
      getList();
    } catch (e) {
      proxy.$modal.msgError((e && e.msg) || '保存失败');
    } finally {
      editSaving.value = false;
    }
  });
}

function updateEditRainfallChart() {
  if (!editRainfallChartRef.value) return;
  if (!editRainfallChartInstance) {
    editRainfallChartInstance = echarts.init(editRainfallChartRef.value);
  }
  // 图表数据正序排列
  const chartData = [...editRainfallTableData.value].sort((a, b) => a.time.localeCompare(b.time));
  editRainfallChartInstance.setOption({
    tooltip: { trigger: 'axis' },
    legend: { data: ['降雨量', '流量'] },
    grid: { left: 60, right: 120, top: 30, bottom: 70 },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.time),
      axisLabel: { rotate: 0, fontSize: 12, margin: 16, interval: 'auto' },
      name: '时间',
    },
    yAxis: [
      { type: 'value', name: '降雨量(mm)', min: 0 },
      { type: 'value', name: '流量(m³/s)', min: 0, position: 'right', offset: 60 },
      { type: 'value', name: '', min: 0, position: 'right' }
    ],
    series: [
      { name: '降雨量', type: 'bar', data: chartData.map(item => item.rainfallValue), yAxisIndex: 0, itemStyle: { color: '#409EFF' } },
      { name: '流量', type: 'line', data: chartData.map(item => item.flowValue), yAxisIndex: 1, smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { width: 2 }, itemStyle: { color: '#E6A23C' } },
      { name: '水位', type: 'line', data: chartData.map(item => item.waterLevel), yAxisIndex: 2, smooth: true, symbol: 'circle', symbolSize: 8, lineStyle: { width: 2 }, itemStyle: { color: '#67C23A' } }
    ],
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: 0,
        height: 24,
        bottom: 10
      }
    ]
  });
  editRainfallChartInstance.resize();
}

function onEditDialogOpened() {
  updateEditRainfallChart();
  if (editRainfallChartInstance) {
    editRainfallChartInstance.resize();
  }
}

async function handleRainFallData() {
  if (!editForm.value.subBasinCode) {
    proxy.$modal.msgError('请选择流域');
    return;
  }
  if (!editForm.value.time || editForm.value.time.length !== 2) {
    proxy.$modal.msgError('请选择有效的时间范围');
    return;
  }
  const params = {
    startTime: editForm.value.time[0],
    endTime: editForm.value.time[1],
    stcd: editForm.value.stationCode,
    rainType: 0, //时段降雨
    pageNum: 1,
    pageSize: 1000
  };
  rainfallQueryLoading.value = true;
  try {
    const res = await getRainLevelData(params);
    const records = (res.rows || res.data?.rows || []);
    // 适配表格/图数据，直接用接口字段
    editRainfallTableData.value = records.map(item => ({
      time: item.time,
      rainfallValue: item.rainfall,
      flowValue: item.waterFlow || item.flow, // 兼容接口字段
      waterLevel: item.waterLevel
    }));
    updateEditRainfallChart();
  } catch (e) {
    proxy.$modal.msgError('获取实时降雨量失败');
  } finally {
    rainfallQueryLoading.value = false;
  }
}

const isView = ref(false);
const viewStats = ref({
  totalRainfall: '',
  max1HourRainfall: '',
  max3HourRainfall: '',
  max6HourRainfall: '',
  max12HourRainfall: '',
  max24HourRainfall: '',
});

function handleView(row) {
  isView.value = true;
  isEdit.value = false;
  openEditDialog(row);
}

const importDialogVisible = ref(false);
const importUploadRef = ref();

function resetImportDialog() {
  importDialogVisible.value = false;
  if (importUploadRef.value) importUploadRef.value.clearFiles();
}

const importUrl = import.meta.env.VITE_APP_BASE_API + '/system/flood-history/import';

function handleImportUpload(option) {
  const formData = new FormData();
  formData.append('file', option.file);
  fetch(importUrl, {
    method: 'POST',
    headers: {
      Authorization: 'Bearer ' + token
    },
    body: formData
  })
    .then(res => res.json())
    .then(res => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess(res.msg || '导入成功');
        resetImportDialog();
        getList();
      } else {
        proxy.$modal.msgError(res.msg || '导入失败');
      }
    })
    .catch(() => {
      proxy.$modal.msgError('导入失败');
    });
}

function submitImport() {
  if (importUploadRef.value) {
    importUploadRef.value.submit();
  }
}

function downloadTemplate() {
  // 真实文件名和桶名
  const realFileName = '0eaae0f9-f198-4ee9-baa2-1b23c932dfe2_历史洪水导入模板01.xlsx';
  const bucketName = 'watershed';
  const downloadUrl = downloadFileUrl;
  const token = getToken();
  const params = new URLSearchParams({
    bucketName,
    fileName: realFileName
  }).toString();
  const urlWithParams = downloadUrl + (downloadUrl.includes('?') ? '&' : '?') + params;
  fetch(urlWithParams, {
    method: 'POST',
    headers: {
      Authorization: 'Bearer ' + token
    }
  })
    .then(response => {
      if (!response.ok) throw new Error('文件下载失败');
      return response.blob();
    })
    .then(blob => {
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = '历史洪水导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(link.href);
    })
    .catch(() => {
      proxy.$modal.msgError('文件下载失败');
    });
}
</script>

<style scoped>
.rainfall-flex-container {
display: flex;
flex-direction: row;
width: 100%;
height: 420px;
min-height: 320px;
margin-top: 10px;
}
.rainfall-table-panel {
flex: 1;
min-width: 0;
background: #fff;
border-radius: 6px 0 0 6px;
box-shadow: 0 2px 8px rgba(0,0,0,0.04);
padding: 16px 8px 16px 16px;
display: flex;
flex-direction: column;
justify-content: flex-start;
}
.rainfall-divider {
width: 2px;
background: #e5e6eb;
margin: 0 8px;
border-radius: 2px;
height: 100%;
align-self: stretch;
}
.rainfall-chart-panel {
flex: 1;
min-width: 0;
background: #fff;
border-radius: 0 6px 6px 0;
box-shadow: 0 2px 8px rgba(0,0,0,0.04);
padding: 16px 16px 16px 8px;
display: flex;
flex-direction: column;
justify-content: flex-start;
}
.rainfall-echart {
width: 100%;
height: 100%;
min-height: 320px;
}
</style>
