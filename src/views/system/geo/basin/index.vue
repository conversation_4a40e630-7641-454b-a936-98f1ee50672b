<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="form-container">
      <el-form-item label="流域名称/代码" prop="searchValue">
        <el-input v-model="queryParams.searchValue" placeholder="请输入流域名称或者编码" clearable
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="跨界类型" prop="overType">
        <el-select v-model="queryParams.overType" placeholder="跨界类型" clearable>
          <el-option v-for="dict in kjlxSelects" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="流域级别" prop="grade">
        <el-select v-model="queryParams.grade" placeholder="流域级别" clearable>
          <el-option v-for="dict in lyjbSelects" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
          </el-col>

          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </div>
      <el-table v-if="refreshTable" v-loading="loading" :data="lyList" row-key="basinId" :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" stripe>
        <el-table-column label="流域名称" prop="name" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column prop="code" label="流域代码" align="center">

        </el-table-column>
        <!-- <el-table-column prop="parentBasinId" label="父流域代码" align="center"></el-table-column>
        <el-table-column prop="pLyname" label="父流域名称" :show-overflow-tooltip="true" align="center"></el-table-column> -->
        <el-table-column prop="lyjb" label="流域级别" :show-overflow-tooltip="true" align="center">
          <template #default="scope">
            <span>{{ parseLyjb(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="kjlx" label="跨界类型" :show-overflow-tooltip="true">
          <template #default="scope">
            <span>{{ parseKjlx(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="lyAndAdcds" label="流域所属行政区" :show-overflow-tooltip="true" align="center">
          <template #default="scope">
            <span>{{ parseLyAndAdcds(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="area" label="流域面积(km²)" :show-overflow-tooltip="true" align="center">

        </el-table-column>
        <el-table-column prop="geom" label="空间数据" align="center" :show-overflow-tooltip="true">
          <template #default="scope">
            <span><el-button type="success" size="small" plain @click="showMap(scope.row)">预览</el-button></span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="210" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
              :disabled="scope.row.pcode">编辑</el-button>
            <!-- <el-button link type="primary" icon="Plus" v-if="scope.row.parentBasinId"
              @click="handleAdd(scope.row)">新增</el-button> -->
            <el-button link type="primary" icon="Delete" v-if="scope.row.parentBasinId"
              @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 添加或修改流域对话框 -->
    <el-dialog :title="title" v-model="open" width="720px" append-to-body @close="cancel">
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="130px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级流域">
              <el-tree-select :disabled="title == '修改流域'" v-model="form.parentBasinId" :data="lyList"
                :props="{ value: 'basinId', label: 'name', children: 'children' }" value-key="basinId"
                placeholder="选择上级流域" check-strictly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流域名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入流域名称" :disabled="isRoot" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流域代码" prop="code">
              <el-input v-model="form.code" placeholder="请输入流域名称" :disabled="!form.addFlag" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="跨界类型" prop="overType">
              <el-select v-model="form.overType" placeholder="跨界类型" clearable style="width: 200px" :disabled="isRoot">
                <el-option v-for="dict in kjlxSelects" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流域级别" prop="grade">
              <el-select v-model="form.grade" placeholder="流域级别" clearable style="width: 200px" :disabled="isRoot">
                <el-option v-for="dict in lyjbSelects" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属行政区" prop="adcds">
              <el-tree-select v-model="form.adcds" :data="adcdOptions" multiple :render-after-expand="false"
                :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="adcd" placeholder="选择所属县区"
                check-strictly clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流域面积(km²)" prop="area">
              <el-input v-model="form.area" placeholder="请输入面积值" :disabled="isRoot" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="空间数据" prop="geoType">
              <el-radio-group v-model="form.geoType" :disabled="isRoot">
                <el-radio label="zip">矢量数据(shp-zip,kml,geojson文件)</el-radio>
                <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                <el-radio label="people">人工绘制</el-radio>
              </el-radio-group>

            </el-form-item>
          </el-col>
          <div style="width: 100%;height: 130px;" v-show="form.geoType === 'zip'">
            <el-upload class="upload-demo" style="width: 100%;" ref="uploadRef" drag name="multipartFile"
              :on-change="handleChange" :disabled="isRoot" :action="uploadUrl" :data="{
              }" :headers="{ 'Authorization': token }" :limit="2" :on-success="handleSuccess">
              <div class="el-upload__text">
                拖拽上传 或 <em>点击上传</em>
              </div>
            </el-upload>
          </div>
          <div style="width: 100%;height: 350px;background: #555">
            <min-map v-if="open" @updateBound="updateBound" :geom="form.geom" :show-tool="form.geoType === 'people'"
              style="width: 100%;height:100%"></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="curTitle" v-model="open2" width="920px" height="900px" append-to-body>
      <el-form ref="menuRef" label-width="130px" height="900px">
        <el-row>
          <div style="width: 100%;height: 600px;background: #555;">
            <min-map :geom="curGeom" :showTool="false" style="width: 100%;height:100%"></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open2 = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { nextTick } from "vue";
import {
  selectStlyList,
  addLy,
  updateLy,
  selectStlyInfo,
  delLy,
  getAdcdTree,
} from "@/api/watershed/ads";
import MinMap from "@/components/Map/plugins/drawTool";
import { getToken } from '@/utils/auth'

defineOptions({
  name: 'BasinIndex'
})

const { proxy } = getCurrentInstance();

const lyList = ref([]);
const open = ref(false);
const open2 = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const curGeom = ref("");
const curTitle = ref("");
const adcdOptions = ref([]); // 行政区树 默认获取一下
const isExpandAll = ref(false);
const isRoot = ref(false);
const refreshTable = ref(true);
const uploadRef = ref('')
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
const token = getToken()
// 跨界类型
const kjlxSelects = [
  {
    label: '未知',
    value: 1
  },
  {
    label: '跨国并跨省',
    value: 2
  },
  {
    label: '跨国',
    value: 3
  },
  {
    label: '跨省',
    value: 4
  },
  {
    label: '跨市',
    value: 5
  },
  {
    label: '跨县',
    value: 6
  },
  {
    label: '县内',
    value: 7
  },
]
// 流域级别
const lyjbSelects = [
  {
    label: '一级',
    value: 1
  },
  {
    label: '二级',
    value: 2
  },
  {
    label: '三级',
    value: 3
  },
  {
    label: '四级',
    value: 4
  },
  {
    label: '五级',
    value: 5
  },
  {
    label: '六级',
    value: 6
  },
  {
    label: '七级',
    value: 7
  },
]
const data = reactive({
  form: {
    geoType: 'people',
    adcds: []
  },
  queryParams: {
    searchValue: undefined,
    overType: undefined,
    grade: undefined,
  },
  rules: {
    name: [{ required: true, message: "流域名称不能为空", trigger: "blur" }],
    code: [{ required: true, message: "流域代码不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询流域列表 */
function getList() {
  loading.value = true;
  selectStlyList(queryParams.value).then(res => {
    lyList.value = convertDataFormat(res.data)
    lyList.value.forEach(item => {
      // 第一级是根，根不允许编辑
      item.root = true
    })
    console.log(lyList.value);
    loading.value = false;
  });
}
getTreeselect();
/** 查询区县下拉树结构 */
function getTreeselect() {
  adcdOptions.value = [];
  getAdcdTree({}).then(res => {

    adcdOptions.value = res.data[0].children
  });
}
function convertDataFormat(data) {
  // 检查是否是数组
  if (!Array.isArray(data)) {
    return null;
  }
  // 对数组中的每个元素进行处理
  return data.map(item => {
    // 复制 data 属性并移除 children
    const newData = { ...item.data };
    delete newData.children;
    // 递归处理子元素
    if (item.children && item.children.length > 0) {
      newData.children = convertDataFormat(item.children);
    }
    return newData;
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  uploadRef.value?.clearFiles()
  uploadRef.value?.handleRemove()
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    addFlag: flag,
    geoType: 'people',
    adcds: [],
  };
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
  reset(true);
  isRoot.value = false
  if (row != null && row.basinId) {
    form.value.parentBasinId = row.basinId;
  } else {
    form.value.parentBasinId = '';
  }
  open.value = true;
  title.value = "添加流域";
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  isRoot.value = !!row.root;

  // 处理级联行政区
  // let adcds = handerCasadcds(row.lyAndAdcds)
  // form.value.adcds = adcds || []
  // 处理几何数据
  let res = await selectStlyInfo(row.basinId)
  open.value = true;
  title.value = "修改流域";
  form.value.basinId = res.data.basinId
  form.value.parentBasinId = res.data.parentBasinId
  form.value.name = res.data.name
  form.value.code = res.data.code
  form.value.overType = res.data.overType
  form.value.grade = res.data.grade
  form.value.area = res.data.area
  if (res.data.sysBasinAdminDistrictRelationsVos.length > 0) {
    let arr = []
    res.data.sysBasinAdminDistrictRelationsVos.forEach(el => {
      arr.push(el.adcd)
    });
    form.value.adcds = arr
  }
  // form.value.geoType = 'people'
  // form.value = res.data
  await nextTick()
  form.value.geom = JSON.parse(res.data.geom) || ''
}
function parseLyjb(data) {
  for (let i = 0; i < lyjbSelects.length; i++) {
    if (lyjbSelects[i].value == data.grade) {
      return lyjbSelects[i].label
    }
  }
}
function parseKjlx(data) {
  for (let i = 0; i < kjlxSelects.length; i++) {
    if (Number(kjlxSelects[i].value) === data.overType) {
      return kjlxSelects[i].label
    }
  }
}
function parseLyAndAdcds(data) {
  let ls = data.adminInfoList || []
  let str = ''
  ls.forEach(l => {
    str += l.adnm + ','
  })
  return str.substring(0, str.length - 1)
}

async function showMap(row) {
  let res = await selectStlyInfo(row.basinId)
  if (res.data) {
    open2.value = true
    await nextTick()
    //检测res.data.geom是什么类型，是否需要parse
    if (res.data.geom) {
      if (typeof res.data.geom == 'string') {
        curGeom.value = JSON.parse(res.data.geom)
      } else {
        curGeom.value = res.data.geom
      }
    }
    curTitle.value = row.name
  } else {
    proxy.$modal.msgSuccess("没找到空间数据");
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate(valid => {
    if (valid) {
      // 处理关联行政区
      form.value.lyAndAdcds = []
      if (form.value.geom == null || form.value.geom == '' || form.value.geom == {}) {
        form.value.geom = null
      }
      // if (form.value.adcds?.length > 0) {
      //   let ll = []
      //   form.value.adcds.forEach(item => {
      //     if (item.length > 1) {
      //       ll.push({
      //         adnm: '',
      //         adcd: item[item.length - 1]
      //       })
      //     } else {
      //       ll.push({
      //         adnm: '',
      //         adcd: item[0]
      //       })
      //     }

      //   })
      //   form.value.lyAndAdcds = ll
      // }
      if (form.value.addFlag) {
        addLy(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      } else {
        console.log('修改');
        updateLy(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.name + '"的数据项?').then(function () {
    return delLy(row.basinId);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}
function updateBound(geojson) {
  // 提交geojson数据到后台
  console.log(geojson)
  form.value.geom = geojson
}
function handleChange(file, fileList) {
  if (fileList.length > 1) {
    fileList[0] = fileList[1]
    fileList.splice(1, 1);
  }
}

function handleSuccess(
  response
) {
  if (response.code == 200) {
    form.value.geom = response.data
  } else {
    uploadRef.value?.clearFiles()
    proxy.$modal.msgError(response.msg);
  }
}

onMounted(() => {
  window.EventBus.$on('updateBound/update', updateBound)
})
getList();
</script>
