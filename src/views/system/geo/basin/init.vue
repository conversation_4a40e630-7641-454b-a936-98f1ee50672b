<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="form-container">
      <el-form-item label="空间名称/代码" prop="searchValue">
        <el-input v-model="queryParams.searchValue" placeholder="请输入空间名称或者编码" clearable
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
          </el-col>

          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="lyList" stripe>
        <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
        <el-table-column prop="lynm" label="空间名称" :show-overflow-tooltip="true">
          <template #default="scope">
            <span>{{ scope.row.data.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="lycode" label="空间代码" align="center">
          <template #default="scope">
            <span>{{ scope.row.data.code }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="geom" label="空间数据" align="center" :show-overflow-tooltip="true">
          <template #default="scope">
            <span><el-button type="success" size="small" plain @click="showMap(scope.row)">预览</el-button></span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="210" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 添加或修改流域对话框 -->
    <el-dialog :title="title" v-model="open" width="720px" append-to-body @close="cancel">
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="130px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="空间名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入空间名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="空间代码" prop="code">
              <el-input v-model="form.code" placeholder="请输入空间名称"  />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属行政区" prop="adcds">
              <el-tree-select v-model="form.adcds" :data="adcdOptions" clearable multiple :render-after-expand="false"
                :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="adcd" placeholder="请选择行政区"
                check-strictly />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="空间数据" prop="geoType">
              <el-radio-group v-model="form.geoType">
                <el-radio label="zip">矢量数据(shp-zip,kml,geojson文件)</el-radio>
                <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                <el-radio label="people">人工绘制</el-radio>
              </el-radio-group>

            </el-form-item>
          </el-col>
          <div style="width: 100%;height: 130px;" v-show="form.geoType === 'zip'">
            <el-upload class="upload-demo" style="width: 100%;" ref="uploadRef" drag name="multipartFile"
              :on-remove="handleRemove" :on-change="handleChange" :action="uploadUrl" :data="{
              }" :headers="{ 'Authorization': token }" :limit="2" :on-success="handleSuccess">
              <div class="el-upload__text">
                拖拽上传 或 <em>点击上传</em>
              </div>
            </el-upload>
          </div>
          <div style="width: 100%;height: 350px;background: #555">
            <min-map v-if="open" @updateBound="updateBound" :geom="form.geom" :show-tool="form.geoType === 'people'"
              style="width: 100%;height:100%"></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :title="curTitle" v-model="open2" width="920px" height="900px" append-to-body>
      <el-form ref="menuRef" label-width="130px" height="900px">
        <el-row>
          <div style="width: 100%;height: 600px;background: #555;">
            <min-map :geom="curGeom" :showTool="false" style="width: 100%;height:100%"></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open2 = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { nextTick } from "vue";
import { addLy, delLy, selectStlyInfo, selectStlyList, updateLy, selectAdcdList } from "@/api/watershed/ads";
import MinMap from "@/components/Map/plugins/drawTool";
import { getToken } from '@/utils/auth'

defineOptions({
  name: 'BasinInit'
})

const { proxy } = getCurrentInstance();

const lyList = ref([]);
const open = ref(false);
const open2 = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const curGeom = ref("");
const curTitle = ref("");
const lyOptions = ref([]);
const adcdOptions = ref([]);
const uploadRef = ref('')
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
const token = getToken()
// 跨界类型
const kjlxSelects = [
  {
    label: '未知',
    value: 1
  },
  {
    label: '跨国并跨省',
    value: 2
  },
  {
    label: '跨国',
    value: 3
  },
  {
    label: '跨省',
    value: 4
  },
  {
    label: '跨市',
    value: 5
  },
  {
    label: '跨县',
    value: 6
  },
  {
    label: '县内',
    value: 7
  },
]
// 流域级别
const lyjbSelects = [
  {
    label: '一级',
    value: 1
  },
  {
    label: '二级',
    value: 2
  },
  {
    label: '三级',
    value: 3
  },
  {
    label: '四级',
    value: 4
  },
  {
    label: '五级',
    value: 5
  },
  {
    label: '六级',
    value: 6
  },
  {
    label: '七级',
    value: 7
  },
]
const data = reactive({
  form: {
    geoType: 'people'
  },
  queryParams: {
    searchValue: undefined,
  },
  rules: {
    name: [{ required: true, message: "空间名称不能为空", trigger: "blur" }],
    code: [{ required: true, message: "空间代码不能为空", trigger: "blur" }],
    adcds: [{ required: true, message: "行政区划不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询流域列表 */
function getList() {
  loading.value = true;
  selectStlyList({ searchValue: queryParams.value.searchValue }).then(res => {
    lyList.value = res.data
    loading.value = false
  }).finally(() => {
    loading.value = false;
  })
  // listMenu(queryParams.value).then(response => {
  //   lyList.value = proxy.handleTree(response.data, "menuId");
  //   loading.value = false;
  // });
}
function getAdcd() {
  selectAdcdList({}).then((res) => {
    adcdOptions.value = res.data
  })
}
/** 查询流域下拉树结构 */
function getTreeselect() {
  lyOptions.value = [];
  selectStlyList({}).then(res => {
    lyOptions.value = res.data
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  uploadRef.value?.clearFiles()
  uploadRef.value?.handleRemove()
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    code: '',
    addFlag: flag,
    geoType: 'people',
    name: undefined,
    icon: undefined,
  };
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
  reset(true);
  getTreeselect();
  if (row != null && row.lycode) {
    form.value.code = row.lycode;
  } else {
    form.value.code = '';
  }
  open.value = true;
  title.value = "添加空间";
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  // reset();
  // await getTreeselect();
  // let obj = Object.assign({ geoType: 'people' }, row);
  // delete obj.children
  // delete obj.createBy
  // delete obj.createTime
  // delete obj.updateBy
  // delete obj.updateTime
  // delete obj.remark
  form.value.name = row.data.name
  form.value.code = row.data.code
  form.value.basinId = row.data.basinId
  // 处理几何数据
  let res = await selectStlyInfo(row.data.basinId)
  if (res.data.sysBasinAdminDistrictRelationsVos.length > 0) {
    let arr = []
    res.data.sysBasinAdminDistrictRelationsVos.forEach(el => {
      arr.push(el.adcd)
    });
    form.value.adcds = arr
  }
  open.value = true;
  title.value = "修改空间";
  await nextTick()
  form.value.geom = JSON.parse(res.data.geom) || ''
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate(valid => {
    if (valid) {
      if (form.value.geom == null || form.value.geom == '' || form.value.geom == {}) {
        form.value.geom = null
      }
      if (form.value.addFlag) {
        // 判断新增是否geom 有数据 --
        if (form.value.geom?.features[0]?.geometry) {
        } else {
          proxy.$modal.msgSuccess("请绘制空间边界");
          return;
        }
        addLy(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      } else {
        updateLy(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });

      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.data.name + '"的数据项?').then(function () {
    return delLy(row.data.basinId);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

function updateBound(geojson) {
  // 提交geojson数据到后台
  console.log(geojson)
  form.value.geom = geojson
}

function handleChange(file, fileList) {
  if (fileList.length > 1) {
    fileList[0] = fileList[1]
    fileList.splice(1, 1);
  }
}
function handleRemove(file) {
  form.value.geom = {}
}
function handleSuccess(
  response
) {
  if (response.code == 200) {
    form.value.geom = response.data
  } else {
    uploadRef.value?.clearFiles()
    proxy.$modal.msgError(response.msg);
  }
}

async function showMap(row) {
  let res = await selectStlyInfo(row.data.basinId)
  if (res.data) {
    open2.value = true
    await nextTick()
    //检测res.data.geom是什么类型，是否需要parse

    if (typeof res.data.geom == 'string') {
      curGeom.value = JSON.parse(res.data.geom)
    } else {
      curGeom.value = res.data.geom
    }
    // curGeom.value = JSON.parse(res.data.geom)
    curTitle.value = row.data.name
  } else {
    proxy.$modal.msgSuccess("没找到空间数据");
  }
}


getList();
getAdcd();
onMounted(() => {
  window.EventBus.$on('updateBound/update', updateBound)
})
</script>
