<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getTypeLabel } from "@/utils";
import { getAdcdTree } from "@/api/watershed/ads";
import MinMap from "@/components/Map/plugins/drawTool";
import { VIDEO_POINT_TYPE_OPTIONS } from "@/views/system/geo/utils/enum";
import {
  getVideoMonitorStationList,
  saveOrUpdateVideoMonitorStation,
  deleteVideoMonitorStation,
  getVideoMonitorStationDetail,
  getVideoMonitorStationPoints,
} from "@/api/video/index";
import * as turf from "@turf/turf";
defineOptions({
  name: "VideoSurveillanceStation",
});

// 查询参数
const queryParams = ref({
  stationName: "", // 测站名称
  stationCode: "", // 测站编码
  district: "", // 行政区划
  monitorType: "", // 监测类型
});

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 详情对话框
const detailDialog = ref(false);
const currentDetail = ref({});
const detailLoading = ref(false);

// 新增/编辑对话框
const formDialog = ref(false);
const formTitle = ref("");
const formType = ref("add"); // add-新增 edit-编辑
const formLoading = ref(false); // 编辑弹窗加载状态

// 表单数据
const formData = ref({
  stationName: "", // 测站名称
  stationCode: "", // 测站编码
  district: "", // 所在区县
  location: "", // 站址
  longitude: "", // 经度
  latitude: "", // 纬度
  baseName: "", // 基面名称
  baseElevation: "", // 基面高程
  buildDate: "", // 建站年月
  startReportDate: "", // 始报年月
});

// 表单规则
const rules = reactive({
  stationName: [
    { required: true, message: "测站名称不能为空", trigger: "blur" },
    { max: 15, message: "测站名称不能超过15个字", trigger: "blur" },
  ],
  stationCode: [
    { required: true, message: "测站编码不能为空", trigger: "blur" },
    { min: 18, max: 18, message: "测站编码必须为18位字符", trigger: "blur" },
  ],
  district: [
    { required: true, message: "所在区县不能为空", trigger: "change" },
  ],
  location: [{ max: 25, message: "站址不能超过25个字", trigger: "blur" }],
  baseName: [{ max: 8, message: "基面名称不能超过8个字", trigger: "blur" }],
});

// 表单引用
const formRef = ref(null);

// Mock数据
// const mockData = [
//   ...
// ];

// 获取列表数据
const getList = () => {
  loading.value = true;
  // 组装查询参数
  const params = {
    stationName: queryParams.value.stationName,
    stationCode: queryParams.value.stationCode,
    districtCode: queryParams.value.district, // district -> districtCode
    data_types: queryParams.value.monitorType, // 监测类型
    pageNum: pagination.value.pageNum,
    pageSize: pagination.value.pageSize,
  };
  getVideoMonitorStationList(params)
    .then((res) => {
      if (res && res.code === 200) {
        tableData.value = res.rows || [];
        pagination.value.total = res.total || 0;
      } else {
        tableData.value = [];
        pagination.value.total = 0;
      }
    })
    .catch(() => {
      tableData.value = [];
      pagination.value.total = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};

// 查询按钮
const handleQuery = () => {
  pagination.value.pageNum = 1;
  getList();
};

// 重置按钮
const handleReset = () => {
  queryParams.value = {
    stationName: "",
    stationCode: "",
    district: "",
    monitorType: "",
  };
  handleQuery();
};

// 查看详情
const handleDetail = async (row) => {
  formTitle.value = "视频监控站 - " + row.stationName;
  detailDialog.value = true;
  detailLoading.value = true; // 打开弹窗时立即 loading
  currentDetail.value = {};
  try {
    // 获取详情
    const res = await getVideoMonitorStationDetail(row.id);
    if (res && res.code === 200 && res.data) {
      currentDetail.value = res.data;
      if (res.data.latitude && res.data.longitude) {
        currentDetail.value.geom = turf.point([
          res.data.longitude,
          res.data.latitude,
        ]);
        console.log(currentDetail.value.geom);
      }
    } else {
      currentDetail.value = {};
    }
    // 获取监测点列表
    const pointRes = await getVideoMonitorStationPoints(row.id);
    if (pointRes && pointRes.code === 200 && Array.isArray(pointRes.rows)) {
      currentDetail.value.monitorPoints = pointRes.rows;
    } else {
      currentDetail.value.monitorPoints = [];
    }
  } catch (e) {
    currentDetail.value = {};
    currentDetail.value.monitorPoints = [];
  } finally {
    detailLoading.value = false;
  }
};

// 新增按钮
const handleAdd = () => {
  resetForm();
  formTitle.value = "新增视频监控站";
  formType.value = "add";
  formLoading.value = false; // 新增时无异步加载，直接关闭 loading
  formDialog.value = true;
};

// 编辑按钮
const handleEdit = async (row) => {
  resetForm();
  formTitle.value = "编辑视频监控站";
  formType.value = "edit";
  formLoading.value = true; // 打开弹窗时立即 loading
  formDialog.value = true;
  try {
    const res = await getVideoMonitorStationDetail(row.id);
    if (res && res.code === 200 && res.data) {
      formData.value = {
        ...formData.value, // 保证有默认值
        ...res.data,
        district: res.data.districtCode || "",
        baseName: res.data.datumName || "",
        baseElevation: res.data.datumElevation || "",
        buildDate: res.data.establishDate || "",
        startReportDate: res.data.reportStartDate || "",
      };
    } else {
      ElMessage.error(res && res.msg ? res.msg : "获取详情失败");
      formDialog.value = false;
    }
  } catch (e) {
    ElMessage.error("获取详情失败");
    formDialog.value = false;
  } finally {
    formLoading.value = false;
  }
};

// 删除按钮
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确认要删除该视频监控站[${row.stationName}]吗？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      deleteVideoMonitorStation(row.id)
        .then((res) => {
          if (res && res.code === 200) {
            ElMessage.success("删除成功");
            getList();
          } else {
            ElMessage.error(res && res.msg ? res.msg : "删除失败");
          }
        })
        .catch(() => {
          ElMessage.error("删除失败");
        });
    })
    .catch(() => {});
};

// 重置表单
const resetForm = () => {
  formData.value = {
    stationName: "",
    stationCode: "",
    district: "",
    location: "",
    longitude: "",
    latitude: "",
    baseName: "",
    baseElevation: "",
    buildDate: "",
    startReportDate: "",
  };
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 组装提交数据
      const data = {
        ...formData.value,
        // 字段适配
        districtCode: formData.value.district,
        datumName: formData.value.baseName,
        datumElevation: formData.value.baseElevation,
        establishDate: formData.value.buildDate,
        reportStartDate: formData.value.startReportDate,
      };
      // 新增时不传id，编辑时传id
      if (formType.value === "add") {
        delete data.id;
      }
      saveOrUpdateVideoMonitorStation(data)
        .then((res) => {
          if (res && res.code === 200) {
            ElMessage.success(
              formType.value === "add" ? "添加成功" : "更新成功"
            );
            formDialog.value = false;
            getList();
          } else {
            ElMessage.error(res && res.msg ? res.msg : "操作失败");
          }
        })
        .catch(() => {
          ElMessage.error("操作失败");
        });
    }
  });
};

// 区县数据
const adcdOptions = ref([]);
// 获取树结构数据
const getTreeselect = () => {
  getAdcdTree().then((res) => {
    adcdOptions.value = res.data[0].children;
  });
};

onMounted(() => {
  getList();
  getTreeselect();
});
</script>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="测站名称" prop="stationName">
        <el-input
          v-model="queryParams.stationName"
          placeholder="请输入测站名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测站编码" prop="stationCode">
        <el-input
          v-model="queryParams.stationCode"
          placeholder="请输入测站编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行政区划" prop="district">
        <el-tree-select
          v-model="queryParams.district"
          :data="adcdOptions"
          clearable
          :props="{
            value: 'adcd',
            label: 'adnm',
            children: 'children',
          }"
          value-key="id"
          placeholder="请选择行政区划"
          check-strictly
        />
      </el-form-item>
      <el-form-item label="监测类型" prop="monitorType">
        <el-select
          v-model="queryParams.monitorType"
          placeholder="请选择监测类型"
          clearable
        >
          <el-option :value="1" label="视频" />
          <el-option :value="2" label="图像" />
          <el-option :value="3" label="全部" />
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button @click="handleReset" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <!-- 操作按钮 -->
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd"
          >新增</el-button
        >
      </div>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableData" stripe>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="stationName" label="测站名称" align="center" />
        <el-table-column prop="stationCode" label="测站编码" align="center" />
        <el-table-column prop="districtName" label="所在区县" align="center" />
        <el-table-column
          prop="videoCount"
          label="视频监控点数量"
          align="center"
        />
        <el-table-column
          prop="pictureCount"
          label="图像监控点数量"
          align="center"
        />
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="View"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        v-model:page="pagination.pageNum"
        v-model:limit="pagination.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="formDialog"
      width="700px"
      append-to-body
    >
      <el-spin v-if="formLoading" tip="加载中..." style="min-height: 200px" />
      <el-form
        v-else
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="视频监控站名称" prop="stationName">
              <el-input
                v-model="formData.stationName"
                placeholder="请输入视频监控站名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="视频监控站编码" prop="stationCode">
              <el-input
                v-model="formData.stationCode"
                placeholder="请输入视频监控站编码"
                :disabled="formType === 'edit'"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在区县" prop="district">
              <el-tree-select
                v-model="formData.district"
                :data="adcdOptions"
                clearable
                :props="{
                  value: 'adcd',
                  label: 'adnm',
                  children: 'children',
                }"
                :render-after-expand="false"
                value-key="id"
                placeholder="请选择所在区县"
                check-strictly
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站址" prop="location">
              <el-input v-model="formData.location" placeholder="请输入站址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number
                v-model="formData.longitude"
                placeholder="请输入经度"
                :precision="6"
                :min="73"
                :max="136"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number
                v-model="formData.latitude"
                placeholder="请输入纬度"
                :precision="6"
                :min="3"
                :max="54"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="基面名称" prop="baseName">
              <el-input
                v-model="formData.baseName"
                placeholder="请输入基面名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基面高程(m)" prop="baseElevation">
              <el-input-number
                v-model="formData.baseElevation"
                placeholder="请输入基面高程"
                :precision="3"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="建站年月" prop="buildDate">
              <el-date-picker
                v-model="formData.buildDate"
                type="month"
                placeholder="请选择建站年月"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="始报年月" prop="startReportDate">
              <el-date-picker
                v-model="formData.startReportDate"
                type="month"
                placeholder="请选择始报年月"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="formDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="detailDialog"
      width="800px"
      append-to-body
      class="detail-dialog"
    >
      <template v-if="detailLoading">
        <div style="text-align: center; padding: 40px 0">
          <el-skeleton :rows="6" animated />
        </div>
      </template>
      <template v-else>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="视频监控站名称">{{
            currentDetail.stationName
          }}</el-descriptions-item>
          <el-descriptions-item label="视频监控站编码">{{
            currentDetail.stationCode
          }}</el-descriptions-item>
          <el-descriptions-item label="所在区县">{{
            currentDetail.districtName
          }}</el-descriptions-item>
          <el-descriptions-item label="站址">{{
            currentDetail.location || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="基面名称">{{
            currentDetail.datumName || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="基面高程(m)">{{
            currentDetail.datumElevation || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="建站年月">{{
            currentDetail.establishDate || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="始报年月">{{
            currentDetail.reportStartDate || "--"
          }}</el-descriptions-item>
        </el-descriptions>

        <!-- 空间信息预览 -->
        <div class="location-preview">
          <h3>视频监控站空间信息预览</h3>
          <div class="map-container">
            <MinMap
              :geom="currentDetail.geom"
              :icon="[{ icon: 'endPoi.png', label: '视频监控站' }]"
              class="w-full h-full"
            />
          </div>
        </div>

        <!-- 监测点列表 -->
        <div class="monitor-points-section">
          <h3>监测点列表</h3>
          <el-table
            :data="currentDetail.monitorPoints || []"
            border
            style="width: 100%"
          >
            <el-table-column
              prop="monitorLocation"
              label="部位"
              align="center"
            />
            <el-table-column prop="pointCode" label="监控点" align="center" />
            <el-table-column label="监测类型" align="center">
              <template #default="scope">
                {{
                  scope.row.dataTypes === "1"
                    ? "视频"
                    : scope.row.dataTypes === "2"
                    ? "图像"
                    : scope.row.dataTypes === "3"
                    ? "都选"
                    : "--"
                }}
              </template>
            </el-table-column>
          </el-table>
          <!-- <div
            v-if="!currentDetail.monitorPoints || currentDetail.monitorPoints.length === 0"
            class="no-data"
          >
            暂无监测点数据
          </div> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.map-container {
  height: 300px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-top: 15px;
}

.location-preview,
.monitor-points-section {
  margin-top: 20px;

  h3 {
    font-size: 16px;
    margin-bottom: 15px;
    font-weight: 500;
  }
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

:deep(.detail-dialog) {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px 24px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
      border-radius: 3px;
    }
  }
}
</style>
