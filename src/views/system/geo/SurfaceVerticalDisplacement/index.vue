<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

defineOptions({
  name: "SurfaceVerticalDisplacement",
});

// 查询参数
const queryParams = ref({
  stationName: "", // 测站名称
  stationCode: "", // 测站编码
  sectionCode: "", // 断面编号
  pointCode: "", // 测点编码
});

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 详情对话框
const detailDialog = ref(false);
const currentDetail = ref({});

// 新增/编辑对话框
const formDialog = ref(false);
const formTitle = ref("");
const formType = ref("add"); // add-新增 edit-编辑

// 安全监测站选项
const stationOptions = ref([]);
// 断面选项
const sectionOptions = ref([]);

// 表单数据
const formData = ref({
  stationId: "", // 安全监测站ID
  stationName: "", // 安全监测站名称
  stationCode: "", // 安全监测站编码
  sectionCode: "", // 断面编号
  pointCode: "", // 测点编码
  instrumentCode: "", // 仪器编号
  longitude: "", // 经度
  latitude: "", // 纬度
  axisDistance: "", // 轴距
  initialElevation: "", // 初始高程
  displacementThreshold: "", // 位移阈值
  installDate: "", // 安装日期
  measurementDate: "", // 测定日期
  remarks: "", // 备注
});

// 表单规则
const rules = reactive({
  stationId: [
    { required: true, message: "安全监测站不能为空", trigger: "change" },
  ],
  sectionCode: [
    { required: true, message: "断面编号不能为空", trigger: "change" },
  ],
  pointCode: [
    { required: true, message: "测点编码不能为空", trigger: "blur" },
    { min: 8, max: 8, message: "测点编码必须为8位字符", trigger: "blur" },
  ],
  instrumentCode: [
    { min: 8, max: 8, message: "仪器编号必须为8位字符", trigger: "blur" },
  ],
  remarks: [
    { max: 100, message: "备注不能超过100个字", trigger: "blur" },
  ],
});

// 表单引用
const formRef = ref(null);

// Mock安全监测站数据
const mockStations = [
  {
    id: 1,
    stationName: "1号监测站",
    stationCode: "600100016001000101",
    sections: [{ sectionCode: "S001" }, { sectionCode: "S002" }],
  },
  {
    id: 2,
    stationName: "2号监测站",
    stationCode: "600100016001000102",
    sections: [{ sectionCode: "S003" }, { sectionCode: "S004" }],
  },
];

// Mock表面垂直位移监测点数据
const mockPoints = [
  {
    id: 1,
    stationId: 1,
    stationName: "1号监测站",
    stationCode: "600100016001000101",
    sectionCode: "S001",
    pointCode: "P0010001",
    instrumentCode: "YQ010001",
    longitude: 91.123456,
    latitude: 29.654321,
    axisDistance: 120.5,
    initialElevation: 3650.25,
    displacementThreshold: 3.25,
    installDate: "2023-05-15",
    measurementDate: "2023-05-20",
    remarks: "每日定时监测",
    hasData: true,
  },
  {
    id: 2,
    stationId: 1,
    stationName: "1号监测站",
    stationCode: "600100016001000101",
    sectionCode: "S002",
    pointCode: "P0020001",
    instrumentCode: "YQ010002",
    longitude: 91.124567,
    latitude: 29.665432,
    axisDistance: 85.3,
    initialElevation: 3655.75,
    displacementThreshold: 2.8,
    installDate: "2023-06-10",
    measurementDate: "2023-06-15",
    remarks: "实时监测",
    hasData: false,
  },
  {
    id: 3,
    stationId: 2,
    stationName: "2号监测站",
    stationCode: "600100016001000102",
    sectionCode: "S003",
    pointCode: "P0030001",
    instrumentCode: "YQ020001",
    longitude: 91.234567,
    latitude: 29.765432,
    axisDistance: 95.2,
    initialElevation: 3660.45,
    displacementThreshold: 3.5,
    installDate: "2023-07-05",
    measurementDate: "2023-07-10",
    remarks: "每周监测",
    hasData: false,
  },
];

// 获取列表数据
const getList = () => {
  loading.value = true;
  // 模拟接口调用
  setTimeout(() => {
    // 过滤数据
    let filteredData = [...mockPoints];

    if (queryParams.value.stationName) {
      filteredData = filteredData.filter((item) =>
        item.stationName.includes(queryParams.value.stationName)
      );
    }

    if (queryParams.value.stationCode) {
      filteredData = filteredData.filter(
        (item) => item.stationCode === queryParams.value.stationCode
      );
    }

    if (queryParams.value.sectionCode) {
      filteredData = filteredData.filter(
        (item) => item.sectionCode === queryParams.value.sectionCode
      );
    }

    if (queryParams.value.pointCode) {
      filteredData = filteredData.filter(
        (item) => item.pointCode === queryParams.value.pointCode
      );
    }

    // 按测站编码、断面编号、测点编码排序
    filteredData.sort((a, b) => {
      // 先按测站编码排序
      if (a.stationCode !== b.stationCode) {
        return a.stationCode.localeCompare(b.stationCode);
      }
      // 再按断面编号排序
      if (a.sectionCode !== b.sectionCode) {
        return a.sectionCode.localeCompare(b.sectionCode);
      }
      // 最后按测点编码排序
      return a.pointCode.localeCompare(b.pointCode);
    });

    tableData.value = filteredData;
    pagination.value.total = filteredData.length;
    loading.value = false;
  }, 300);
};

// 查询按钮
const handleQuery = () => {
  pagination.value.pageNum = 1;
  getList();
};

// 重置按钮
const handleReset = () => {
  queryParams.value = {
    stationName: "",
    stationCode: "",
    sectionCode: "",
    pointCode: "",
  };
  handleQuery();
};

// 查看详情
const handleDetail = (row) => {
  formTitle.value = "表面垂直位移监测点 - " + row.pointCode;
  currentDetail.value = { ...row };
  detailDialog.value = true;
};

// 加载监测站选项
const loadStationOptions = () => {
  stationOptions.value = mockStations.map((item) => ({
    value: item.id,
    label: item.stationName,
    stationCode: item.stationCode,
    stationName: item.stationName,
  }));
};

// 监测站变化时更新断面选项
const handleStationChange = (stationId) => {
  if (!stationId) {
    sectionOptions.value = [];
    formData.value.sectionCode = "";
    return;
  }

  const station = mockStations.find((item) => item.id === stationId);
  if (station) {
    formData.value.stationCode = station.stationCode;
    formData.value.stationName = station.stationName;
    sectionOptions.value = station.sections.map((section) => ({
      value: section.sectionCode,
      label: section.sectionCode,
    }));
  } else {
    sectionOptions.value = [];
  }
};

// 新增按钮
const handleAdd = () => {
  resetForm();
  formTitle.value = "新增表面垂直位移监测点";
  formType.value = "add";
  formDialog.value = true;
  loadStationOptions();
};

// 编辑按钮
const handleEdit = (row) => {
  resetForm();
  formTitle.value = "编辑表面垂直位移监测点";
  formType.value = "edit";

  // 加载监测站选项
  loadStationOptions();

  // 填充表单数据
  formData.value = { ...row };

  // 加载该监测站的断面选项
  handleStationChange(row.stationId);

  formDialog.value = true;
};

// 删除按钮
const handleDelete = (row) => {
  // 检查是否有关联的监测数据
  if (row.hasData) {
    ElMessage.error("存在关联的表面垂直位移监测信息，删除失败！");
    return;
  }

  ElMessageBox.confirm(
    `确认要删除该表面垂直位移监测点[${row.pointCode}]吗？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      // 模拟删除操作
      const index = mockPoints.findIndex((item) => item.id === row.id);
      if (index !== -1) {
        mockPoints.splice(index, 1);
        ElMessage.success("删除成功");
        getList();
      }
    })
    .catch(() => {});
};

// 重置表单
const resetForm = () => {
  formData.value = {
    stationId: "",
    stationName: "",
    stationCode: "",
    sectionCode: "",
    pointCode: "",
    instrumentCode: "",
    longitude: "",
    latitude: "",
    axisDistance: "",
    initialElevation: "",
    displacementThreshold: "",
    installDate: "",
    measurementDate: "",
    remarks: "",
  };

  sectionOptions.value = [];

  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      if (formType.value === "add") {
        // 检查是否存在相同的安全监测站、断面和测点编码组合
        const exists = mockPoints.some(
          (item) =>
            item.stationCode === formData.value.stationCode &&
            item.sectionCode === formData.value.sectionCode &&
            item.pointCode === formData.value.pointCode
        );

        if (exists) {
          ElMessage.error("表面垂直位移监测点已存在，请勿重复添加！");
          return;
        }

        // 添加新表面垂直位移监测点
        const newPoint = {
          id: mockPoints.length + 1,
          ...formData.value,
          hasData: false,
        };

        mockPoints.push(newPoint);
        ElMessage.success("添加成功");
      } else {
        // 更新表面垂直位移监测点
        const index = mockPoints.findIndex(
          (item) => item.id === formData.value.id
        );

        if (index !== -1) {
          // 保留原有的hasData标志
          const hasData = mockPoints[index].hasData;
          mockPoints[index] = {
            ...formData.value,
            hasData,
          };

          ElMessage.success("更新成功");
        }
      }

      formDialog.value = false;
      getList();
    }
  });
};

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="form-container">
      <el-form-item label="测站名称" prop="stationName">
        <el-input
          v-model="queryParams.stationName"
          placeholder="请输入测站名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测站编码" prop="stationCode">
        <el-input
          v-model="queryParams.stationCode"
          placeholder="请输入测站编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="断面编号" prop="sectionCode">
        <el-input
          v-model="queryParams.sectionCode"
          placeholder="请输入断面编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测点编码" prop="pointCode">
        <el-input
          v-model="queryParams.pointCode"
          placeholder="请输入测点编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button @click="handleReset" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="content content-table">
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd"
          >新增</el-button
        >
      </div>
      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableData" stripe>
        <el-table-column type="index" label="序号" width="50" align="center" />
        <el-table-column prop="stationName" label="测站名称" align="center" />
        <el-table-column prop="stationCode" label="测站编码" align="center" />
        <el-table-column prop="sectionCode" label="断面编号" align="center" />
        <el-table-column prop="pointCode" label="测点编码" align="center" />
        <el-table-column prop="axisDistance" label="轴距(m)" align="center" />
        <el-table-column
          prop="initialElevation"
          label="初始高程(m)"
          align="center"
        />
        <el-table-column
          prop="displacementThreshold"
          label="位移阈值(m)"
          align="center"
        />
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="View"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        v-model:page="pagination.pageNum"
        v-model:limit="pagination.pageSize"
        @pagination="getList"
      />
    </div>


    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="formDialog"
      width="700px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="150px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="安全监测站" prop="stationId">
              <el-select
                v-model="formData.stationId"
                placeholder="请选择安全监测站"
                clearable
                class="w-full"
                @change="handleStationChange"
                :disabled="formType === 'edit'"
              >
                <el-option
                  v-for="item in stationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="断面编号" prop="sectionCode">
              <el-select
                v-model="formData.sectionCode"
                placeholder="请选择断面编号"
                clearable
                class="w-full"
                :disabled="formType === 'edit'"
              >
                <el-option
                  v-for="item in sectionOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="测点编码" prop="pointCode">
              <el-input
                v-model="formData.pointCode"
                placeholder="请输入测点编码"
                :disabled="formType === 'edit'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仪器编号" prop="instrumentCode">
              <el-input
                v-model="formData.instrumentCode"
                placeholder="请输入仪器编号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number
                v-model="formData.longitude"
                placeholder="请输入经度"
                :precision="6"
                :min="73"
                :max="136"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number
                v-model="formData.latitude"
                placeholder="请输入纬度"
                :precision="6"
                :min="3"
                :max="54"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="轴距(m)" prop="axisDistance">
              <el-input-number
                v-model="formData.axisDistance"
                placeholder="请输入轴距"
                :precision="2"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="初始高程(m)" prop="initialElevation">
              <el-input-number
                v-model="formData.initialElevation"
                placeholder="请输入初始高程"
                :precision="2"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="位移阈值(m)" prop="displacementThreshold">
              <el-input-number
                v-model="formData.displacementThreshold"
                placeholder="请输入位移阈值"
                :precision="2"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="安装日期" prop="installDate">
              <el-date-picker
                v-model="formData.installDate"
                type="date"
                placeholder="请选择安装日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测定日期" prop="measurementDate">
              <el-date-picker
                v-model="formData.measurementDate"
                type="date"
                placeholder="请选择测定日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="formData.remarks"
                type="textarea"
                placeholder="请输入备注"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="formDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="detailDialog"
      width="800px"
      append-to-body
      class="detail-dialog"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="安全监测站名称">{{
          currentDetail.stationName
        }}</el-descriptions-item>
        <el-descriptions-item label="安全监测站编码">{{
          currentDetail.stationCode
        }}</el-descriptions-item>
        <el-descriptions-item label="断面编号">{{
          currentDetail.sectionCode
        }}</el-descriptions-item>
        <el-descriptions-item label="测点编码">{{
          currentDetail.pointCode
        }}</el-descriptions-item>
        <el-descriptions-item label="仪器编号">{{
          currentDetail.instrumentCode || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="经度">{{
          currentDetail.longitude || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="纬度">{{
          currentDetail.latitude || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="轴距(m)">{{
          currentDetail.axisDistance || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="初始高程(m)">{{
          currentDetail.initialElevation || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="位移阈值(m)">{{
          currentDetail.displacementThreshold || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="安装日期">{{
          currentDetail.installDate || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="测定日期">{{
          currentDetail.measurementDate || "--"
        }}</el-descriptions-item>
      </el-descriptions>

      <!-- 备注信息 -->
      <div class="remarks-section" v-if="currentDetail.remarks">
        <h3>备注</h3>
        <div class="remarks-content">{{ currentDetail.remarks }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.remarks-section {
  margin-top: 20px;

  h3 {
    font-size: 16px;
    margin-bottom: 15px;
    font-weight: 500;
  }
}

.remarks-content {
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 50px;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.detail-dialog) {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px 24px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
      border-radius: 3px;
    }
  }
}
</style>
