<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="区域类型" prop="regionType">
        <el-select v-model="queryParams.regionType" style="width: 200px">
          <el-option label="区域" value="region" />
          <el-option label="流域" value="basin" />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="queryParams.regionType === 'region'"
        label="行政区"
        prop="regionCode"
      >
        <el-select
          v-model="queryParams.regionCode"
          placeholder="请选择行政区"
          clearable
          filterable
          style="width: 200px"
        >
          <el-option
            v-for="item in regionOptions"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="queryParams.regionType === 'basin'"
        label="流域"
        prop="basinCode"
      >
        <el-select
          v-model="queryParams.basinCode"
          placeholder="请选择流域"
          clearable
          filterable
          style="width: 200px"
        >
          <el-option
            v-for="item in basinOptions"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预报时段" prop="period">
        <el-select
          v-model="queryParams.period"
          placeholder="请选择预报时段"
          style="width: 200px"
        >
          <el-option
            v-for="item in periodOptions"
            :key="item"
            :label="item + '小时'"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预报时间" prop="forecastTime">
        <el-date-picker
          v-model="queryParams.forecastTime"
          type="datetime"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm"
          placeholder="选择预报时间"
          :disabled-date="disabledForecastDate"
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="Refresh" type="primary" plain @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">

          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </div>
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        stripe
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        default-expand-all
      >
        <!-- <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
          :index="indexMethod"
        ></el-table-column> -->
        <el-table-column
          :prop="
            queryParams.regionType === 'region' ? 'regionName' : 'basinName'
          "
          :label="queryParams.regionType === 'region' ? '区域' : '流域'"
          align="left"
        ></el-table-column>
        <el-table-column
          prop="period"
          label="预报时段(h)"
          width="180"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="totalRainfall"
          label="累计面雨量(mm)"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="maxIntensity"
          label="最大雨强(mm/h)"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="maxPointRainfall"
          label="最大点雨量(mm)"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="primary" @click="handleView(scope.row)" icon="View">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增弹窗：新增预报任务 -->
    <el-dialog
      :title="dialogTitle"
      v-model="addDialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        :model="addForm"
        :rules="addRules"
        ref="addFormRef"
        label-width="140px"
      >
        <el-form-item label="预报方案" prop="forecastSchemeId" required>
          <el-select
            v-model="addForm.forecastSchemeId"
            placeholder="请选择预报方案"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in forecastSchemeOptions"
              :key="item.id"
              :label="item.schemeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属流域" prop="basinCode" required>
          <el-tree-select
            v-model="addForm.basinCode"
            :data="treeData"
            placeholder="请选择流域"
            check-strictly
            :render-after-expand="false"
            style="width: 100%"
            :props="{ label: 'name', value: 'code', children: 'children' }"
            clearable
            filterable
          />
        </el-form-item>
        <el-form-item label="选择历史洪水" prop="floodHistoryCode" required>
          <el-select
            v-model="addForm.floodHistoryCode"
            placeholder="选择历史洪水"
            style="width: 100%"
          >
            <el-option
              v-for="item in floodHistoryOptions"
              :key="item.code"
              :label="item.code"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary">评价</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增：查看详情弹窗 -->
    <el-dialog
      :title="viewDialogTitle"
      v-model="viewDialogVisible"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="历史洪水编号">{{
          viewDetail.floodEventCode
        }}</el-descriptions-item>
        <el-descriptions-item label="方案名称">{{
          viewDetail.forecastSchemeName
        }}</el-descriptions-item>
        <el-descriptions-item label="测站">{{
          viewDetail.stationName
        }}</el-descriptions-item>
        <el-descriptions-item label="时间范围">{{
          viewDetail.timeRange
        }}</el-descriptions-item>
        <el-descriptions-item label="确定性系数">{{
          viewDetail.nse
        }}</el-descriptions-item>
        <el-descriptions-item label="评价结果">{{
          viewDetail.evaluationResult
        }}</el-descriptions-item>
        <el-descriptions-item label="降雨量">{{
          viewDetail.rainfall
        }}</el-descriptions-item>
        <el-descriptions-item label="实测洪量">{{
          viewDetail.measuredVolume
        }}</el-descriptions-item>
        <el-descriptions-item label="计算洪量">{{
          viewDetail.calculatedVolume
        }}</el-descriptions-item>
        <el-descriptions-item label="洪量相对误差"
          >{{ viewDetail.volumeRelError }}%</el-descriptions-item
        >
        <el-descriptions-item label="实测洪峰流量">{{
          viewDetail.measuredPeakFlow
        }}</el-descriptions-item>
        <el-descriptions-item label="计算洪峰流量">{{
          viewDetail.calculatedPeakFlow
        }}</el-descriptions-item>
        <el-descriptions-item label="洪峰流量相对误差"
          >{{ viewDetail.peakFlowRelError }}%</el-descriptions-item
        >
        <el-descriptions-item label="实测峰现时间">{{
          viewDetail.measuredPeakTime
        }}</el-descriptions-item>
        <el-descriptions-item label="计算峰现时间">{{
          viewDetail.calculatedPeakTime
        }}</el-descriptions-item>
        <el-descriptions-item label="峰现时延"
          >{{ viewDetail.peakTimeDeviation }}小时</el-descriptions-item
        >
      </el-descriptions>
      <div style="margin: 20px 0 0 0">
        <el-radio-group
          v-model="detailViewType"
          size="small"
          style="margin-bottom: 12px"
        >
          <el-radio-button label="chart">
            <el-icon style="vertical-align: middle; margin-right: 4px"
              ><Picture /></el-icon
            >图
          </el-radio-button>
          <el-radio-button label="table">
            <el-icon style="vertical-align: middle; margin-right: 4px"
              ><Tickets /></el-icon
            >表格
          </el-radio-button>
        </el-radio-group>
        <div v-if="detailViewType === 'chart'">
          <div ref="detailChartRef" style="width: 100%; height: 320px"></div>
        </div>
        <el-table
          v-if="detailViewType === 'table'"
          :data="viewDetailTableData"
          style="width: 100%; margin-top: 12px"
          height="320"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column prop="time" label="时间" align="center" />
          <el-table-column prop="rainfall" label="降雨量" align="center" />
          <el-table-column
            prop="measuredFlow"
            label="实测流量"
            align="center"
          />
          <el-table-column
            prop="calculatedFlow"
            label="计算流量"
            align="center"
          />
          <el-table-column prop="relError" label="相对误差(%)" align="center" />
        </el-table>
      </div>
      <template #footer>
        <!-- 仅保留footer插槽，不显示关闭按钮 -->
      </template>
    </el-dialog>
  </div>
</template>

  <script setup>
import moment from "moment";
import { computed, onUnmounted, reactive, ref } from "vue";

defineOptions({
  name: "TownshipIndex",
});

const { proxy } = getCurrentInstance();
const accuracyList = ref([
  {
    id: 1,
    forecastSchemeName: "方案A",
    floodEventName: "202106洪水",
    basinName: "流域1",
    volumeRelError: 5.2,
    peakFlowRelError: 3.8,
    peakTimeDeviation: 1.5,
    nse: 0.92,
    evaluationResult: "优秀",
    evaluationTime: "2024-06-01 10:00",
  },
  {
    id: 2,
    forecastSchemeName: "方案B",
    floodEventName: "202107洪水",
    basinName: "流域2",
    volumeRelError: 8.1,
    peakFlowRelError: 6.4,
    peakTimeDeviation: 2.0,
    nse: 0.85,
    evaluationResult: "良好",
    evaluationTime: "2024-06-02 09:00",
  },
  {
    id: 3,
    forecastSchemeName: "方案C",
    floodEventName: "202108洪水",
    basinName: "流域3",
    volumeRelError: 12.3,
    peakFlowRelError: 10.2,
    peakTimeDeviation: 3.1,
    nse: 0.78,
    evaluationResult: "一般",
    evaluationTime: "2024-06-03 08:00",
  },
  {
    id: 4,
    forecastSchemeName: "方案D",
    floodEventName: "202109洪水",
    basinName: "流域4",
    volumeRelError: 18.5,
    peakFlowRelError: 15.2,
    peakTimeDeviation: 4.2,
    nse: 0.65,
    evaluationResult: "较差",
    evaluationTime: "2024-06-04 07:00",
  },
]);
const loading = ref(false);
const showSearch = ref(true);
const refreshTable = ref(true);

const data = reactive({
  total: 0,
  form: {
    geoType: "people",
  },
  queryParams: {
    regionType: "region", // 区域类型，默认区域
    regionCode: "", // 行政区编码，默认空
    basinCode: "", // 流域编码，默认空
    period: 3, // 预报时段，默认3小时
    forecastTime: moment().subtract(1, "hour").format("YYYY-MM-DD HH:00"), // 默认当前时间前一小时整点
  },
  rules: {
    parentAdcd: [
      { required: true, message: "所属县区不能为空", trigger: "blur" },
    ],
    adcd: [
      { required: true, message: "乡镇代码不能为空", trigger: "blur" },
      {
        pattern: /^\d{12}$/,
        message: "乡镇代码必须为12位数字",
        trigger: ["blur", "change"],
      },
    ],
    adnm: [{ required: true, message: "乡镇名称不能为空", trigger: "blur" }],
    area: [
      {
        pattern: /^\d+(\.\d+)?$/,
        message: "请输入正确的数字格式",
        trigger: ["blur", "change"],
      },
    ],
  },
  forecastSchemeOptions: [],
  statusOptions: [
    { label: "运行中", value: 1 },
    { label: "未开始", value: 2 },
    { label: "已过期", value: 3 },
  ],
});

const { queryParams } = toRefs(data);

// mock数据生成函数
function generateMockTableData(params) {
  const isRegion = params.regionType === "region";
  if (isRegion) {
    return [
      {
        id: "1000",
        regionName: "拉萨市",
        period: params.period,
        totalRainfall: (Math.random() * 100 + 50).toFixed(1),
        maxIntensity: (Math.random() * 30 + 10).toFixed(1),
        maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
        children: [
          {
            id: "1001",
            regionName: "城关区",
            period: params.period,
            totalRainfall: (Math.random() * 100 + 50).toFixed(1),
            maxIntensity: (Math.random() * 30 + 10).toFixed(1),
            maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
          },
          {
            id: "1002",
            regionName: "堆龙德庆区",
            period: params.period,
            totalRainfall: (Math.random() * 100 + 50).toFixed(1),
            maxIntensity: (Math.random() * 30 + 10).toFixed(1),
            maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
          },
          {
            id: "1003",
            regionName: "达孜区",
            period: params.period,
            totalRainfall: (Math.random() * 100 + 50).toFixed(1),
            maxIntensity: (Math.random() * 30 + 10).toFixed(1),
            maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
          },
          {
            id: "1004",
            regionName: "墨竹工卡县",
            period: params.period,
            totalRainfall: (Math.random() * 100 + 50).toFixed(1),
            maxIntensity: (Math.random() * 30 + 10).toFixed(1),
            maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
          },
          {
            id: "1005",
            regionName: "林周县",
            period: params.period,
            totalRainfall: (Math.random() * 100 + 50).toFixed(1),
            maxIntensity: (Math.random() * 30 + 10).toFixed(1),
            maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
          },
          {
            id: "1006",
            regionName: "当雄县",
            period: params.period,
            totalRainfall: (Math.random() * 100 + 50).toFixed(1),
            maxIntensity: (Math.random() * 30 + 10).toFixed(1),
            maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
          },
          {
            id: "1007",
            regionName: "尼木县",
            period: params.period,
            totalRainfall: (Math.random() * 100 + 50).toFixed(1),
            maxIntensity: (Math.random() * 30 + 10).toFixed(1),
            maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
          },
          {
            id: "1008",
            regionName: "曲水县",
            period: params.period,
            totalRainfall: (Math.random() * 100 + 50).toFixed(1),
            maxIntensity: (Math.random() * 30 + 10).toFixed(1),
            maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
          },
        ],
      },
    ];
  } else {
    return [
      {
        id: "B000",
        basinName: "拉萨河流域",
        period: params.period,
        totalRainfall: (Math.random() * 100 + 50).toFixed(1),
        maxIntensity: (Math.random() * 30 + 10).toFixed(1),
        maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
        children: [
          {
            id: "B001",
            basinName: "中拉萨河",
            period: params.period,
            totalRainfall: (Math.random() * 100 + 50).toFixed(1),
            maxIntensity: (Math.random() * 30 + 10).toFixed(1),
            maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
          },
          {
            id: "B002",
            basinName: "上拉萨河",
            period: params.period,
            totalRainfall: (Math.random() * 100 + 50).toFixed(1),
            maxIntensity: (Math.random() * 30 + 10).toFixed(1),
            maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
          },
          {
            id: "B003",
            basinName: "下拉萨河",
            period: params.period,
            totalRainfall: (Math.random() * 100 + 50).toFixed(1),
            maxIntensity: (Math.random() * 30 + 10).toFixed(1),
            maxPointRainfall: (Math.random() * 50 + 20).toFixed(1),
          },
        ],
      },
    ];
  }
}

// 统一获取数据（mock或接口，后续可扩展）
async function fetchTableData(params) {
  // TODO: 后续可通过变量useMock或环境判断切换数据源
  // return await realApi(params);
  return generateMockTableData(params);
}

/** 搜索按钮操作 */
async function handleQuery() {
  loading.value = true;
  try {
    tableData.value = await fetchTableData(queryParams.value);
  } finally {
    loading.value = false;
  }
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}


// 存储初始表单数据，用于重置操作


const addDialogVisible = ref(false);
const addFormRef = ref();
const addForm = reactive({
  forecastSchemeId: undefined,
  startTime: getDefaultStartTime(),
  step: 60,
  preheatPeriod: 24,
  forecastPeriod: 24,
  interval: 1,
  endTime: "",
});
const addRules = {
  forecastSchemeId: [
    { required: true, message: "请选择预报方案", trigger: "change" },
  ],
  startTime: [{ required: true, message: "请选择开始时间", trigger: "change" }],
  step: [{ required: true, message: "请选择计算步长", trigger: "change" }],
  preheatPeriod: [
    {
      type: "number",
      min: 0,
      max: 360,
      message: "预热期范围0-360",
      trigger: "change",
    },
  ],
  forecastPeriod: [
    { required: true, message: "请输入预见期", trigger: "change" },
    {
      type: "number",
      min: 0,
      max: 360,
      message: "预见期范围0-360",
      trigger: "change",
    },
  ],
  interval: [
    {
      type: "number",
      min: 1,
      max: 360,
      message: "计算间隔范围1-360",
      trigger: "change",
    },
  ],
  endTime: [],
};

function getDefaultStartTime() {
  // 返回当前最近的整点时间
  const now = moment();
  return now.minute() === 0
    ? now.format("YYYY-MM-DD HH:00")
    : now.add(1, "hour").startOf("hour").format("YYYY-MM-DD HH:00");
}




// 弹窗模式与标题
const dialogMode = ref("add"); // 'add' | 'edit' | 'view'
const taskName = ref("");
const dialogTitle = computed(() => {
  if (dialogMode.value === "edit") {
    return taskName.value || "编辑滚动预报任务";
  }
  return "新增滚动预报任务";
});

// 查看详情弹窗相关
const viewDialogVisible = ref(false);
const viewDetail = reactive({
  floodEventCode: "202106洪水",
  forecastSchemeName: "方案A",
  stationName: "测站X",
  timeRange: "2024-06-01 10:00 ~ 2024-06-03 10:00",
  nse: 0.92,
  evaluationResult: "优秀",
  rainfall: 120.5,
  measuredVolume: 3000,
  calculatedVolume: 3100,
  volumeRelError: 3.3,
  measuredPeakFlow: 500,
  calculatedPeakFlow: 520,
  peakFlowRelError: 4.0,
  measuredPeakTime: "2024-06-02 08:00",
  calculatedPeakTime: "2024-06-02 08:30",
  peakTimeDeviation: 0.5,
});
const viewDialogTitle = computed(() =>
  viewDetail.forecastSchemeName
    ? `滚动预报任务详情 - ${viewDetail.forecastSchemeName}`
    : "滚动预报任务详情"
);
// mock 详情表格/图数据
const viewDetailTableData = ref([
  {
    time: "2024-06-01 10:00",
    rainfall: 10,
    measuredFlow: 100,
    calculatedFlow: 110,
    relError: 10,
  },
  {
    time: "2024-06-01 11:00",
    rainfall: 15,
    measuredFlow: 120,
    calculatedFlow: 125,
    relError: 4.2,
  },
  {
    time: "2024-06-01 12:00",
    rainfall: 8,
    measuredFlow: 130,
    calculatedFlow: 128,
    relError: 1.5,
  },
  {
    time: "2024-06-01 13:00",
    rainfall: 0,
    measuredFlow: 140,
    calculatedFlow: 138,
    relError: 1.4,
  },
  {
    time: "2024-06-01 14:00",
    rainfall: 5,
    measuredFlow: 150,
    calculatedFlow: 155,
    relError: 3.3,
  },
]);
const detailViewType = ref("chart"); // chart or table
const detailChartRef = ref(null);
let detailChartInstance = null;



onMounted(() => {
  handleQuery();
});
onUnmounted(() => {
  if (detailChartInstance) {
    detailChartInstance.dispose();
    detailChartInstance = null;
  }
  window.removeEventListener("resize", handleDetailChartResize);
});
function handleDetailChartResize() {
  if (detailChartInstance) detailChartInstance.resize();
}

// 查看按钮逻辑
function handleView(row) {
  viewDetail.forecastSchemeName = row.name || "";
  viewDetail.startTime = row.startTime || "";
  viewDetail.delayTime = row.delayTime || "";
  viewDetail.step = row.step || "";
  viewDetail.preheatPeriod = row.preheatPeriod || "";
  viewDetail.forecastPeriod = row.forecastPeriod || "";
  viewDetail.interval = row.interval || "";
  viewDetail.endTime = row.endTime || "";
  viewDialogVisible.value = true;
}



const treeData = ref([]);
const floodHistoryOptions = ref([]);

// 递归适配流域树结构
function transformBasinTreeNode(node) {
  return {
    ...node.data,
    children: (node.children || []).map(transformBasinTreeNode),
  };
}


// mock 行政区和流域数据
const regionOptions = ref([
  { code: "1001", name: "城区" },
  { code: "1002", name: "郊区" },
  { code: "1003", name: "开发区" },
  { code: "1004", name: "新区" },
]);
const basinOptions = ref([
  { code: "B001", name: "流域A" },
  { code: "B002", name: "流域B" },
  { code: "B003", name: "流域C" },
]);
const periodOptions = [3, 6, 12, 24];
// 只能选择历史时间
function disabledForecastDate(date) {
  return date.getTime() > Date.now();
}
const tableData = ref([]);
</script>

  <style scoped>
.rainfall-flex-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 420px;
  min-height: 320px;
  margin-top: 10px;
}
.rainfall-table-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 6px 0 0 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 16px 8px 16px 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.rainfall-divider {
  width: 2px;
  background: #e5e6eb;
  margin: 0 8px;
  border-radius: 2px;
  height: 100%;
  align-self: stretch;
}
.rainfall-chart-panel {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 0 6px 6px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 16px 16px 16px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.rainfall-echart {
  width: 100%;
  height: 100%;
  min-height: 320px;
}
.apple-glass-tag {
  display: inline-block;
  min-width: 64px;
  padding: 0 22px;
  height: 32px;
  line-height: 30px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 999px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  color: #fff;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.1);
  user-select: none;
  cursor: default;
  position: relative;
  overflow: hidden;
  transition: box-shadow 0.3s;
  animation: apple-breath 2.4s infinite cubic-bezier(0.4, 0, 0.2, 1);
}
.apple-glass-tag--excellent {
  background: linear-gradient(90deg, #4ade80 0%, #22d3ee 100%);
  box-shadow: 0 0 16px 0 #4ade8044;
}
.apple-glass-tag--good {
  background: linear-gradient(90deg, #60a5fa 0%, #2563eb 100%);
  box-shadow: 0 0 16px 0 #60a5fa44;
}
.apple-glass-tag--normal {
  background: linear-gradient(90deg, #fbbf24 0%, #f59e42 100%);
  box-shadow: 0 0 16px 0 #fbbf2444;
}
.apple-glass-tag--bad {
  background: linear-gradient(90deg, #f87171 0%, #f43f5e 100%);
  box-shadow: 0 0 16px 0 #f8717144;
}
@keyframes apple-breath {
  0%,
  100% {
    box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.1);
    opacity: 1;
  }
  50% {
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.18);
    opacity: 0.92;
  }
}
</style>
