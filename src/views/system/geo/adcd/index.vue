<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="form-container">
      <el-form-item label="行政区名称/代码" prop="adcd">
        <el-input v-model="queryParams.adcd" placeholder="请输入行政区名称/代码" clearable
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button disabled type="info" plain icon="Sort" @click="toggleExpandAll">批量导入</el-button>
          </el-col> -->
          <!--         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
        </el-row>
      </div>
      <el-table v-loading="loading" :data="adcdList" row-key="adcd" :default-expand-all="false" stripe>
        <el-table-column prop="adnm" label="行政区名称" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="adcd" label="行政区代码" align="center"></el-table-column>
        <el-table-column prop="shortName" label="行政区简称" :show-overflow-tooltip="true" align="center"></el-table-column>
        <el-table-column prop="area" label="土地面积(km²)" :show-overflow-tooltip="true" align="center"></el-table-column>
        <el-table-column prop="geom" label="空间数据" align="center" :show-overflow-tooltip="true">
          <template #default="scope">
            <span><el-button type="success" size="small" plain @click="showMap(scope.row)">预览</el-button></span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center">
          <template #default="scope">
            <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
              @change="handleStatusChange(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="210" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)">新增</el-button>
            <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" v-model="open" width="720px" append-to-body @close="cancel">
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="130px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级行政区">
              <el-tree-select v-model="form.parentAdcd" :data="adcdTreeOptions" :disabled="editDis"
                :render-after-expand="false" :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
                value-key="adcd" placeholder="选择上级政区" check-strictly />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="行政区代码" prop="adcd">
              <el-input v-model="form.adcd" placeholder="请输入行政区代码" :disabled="editDis" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="行政区名称" prop="adnm">
              <el-input v-model="form.adnm" placeholder="请输入行政区名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="行政区划简称" prop="shortName">
              <el-input v-model="form.shortName" placeholder="请输入行政区名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="土地面积(km²)" prop="area">
              <el-input v-model="form.area" placeholder="请输入面积值" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="空间数据" prop="geoType">
              <el-radio-group v-model="form.geoType">
                <el-radio label="zip">矢量数据(shp-zip,kml,geojson文件)</el-radio>
                <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                <el-radio label="people">人工绘制</el-radio>
              </el-radio-group>

            </el-form-item>
          </el-col>
          <div style="width: 100%;height: 130px;" v-show="form.geoType === 'zip'">
            <el-upload class="upload-demo" style="width: 100%;" ref="uploadRef" drag name="multipartFile"
              :on-change="handleChange" :action="uploadUrl" :data="{
              }" :headers="{ 'Authorization': token }" :limit="2" :on-success="handleSuccess">
              <div class="el-upload__text">
                拖拽上传 或 <em>点击上传</em>
              </div>
            </el-upload>
          </div>
          <div style="width: 100%;height: 350px;background: #555">
            <min-map v-if="open" @updateBound="updateBound" :geom="form.geom" :show-tool="form.geoType === 'people'"
              style="width: 100%;height:100%"></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="curTitle" v-model="open2" width="920px" height="900px" append-to-body>
      <el-form ref="menuRef" label-width="130px" height="900px">
        <el-row>
          <div style="width: 100%;height: 600px;background: #555;">
            <min-map :geom="curGeom" :showTool="false" style="width: 100%;height:100%"></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open2 = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  addAdcd,
  delAdcd,
  selectAdcdList,
  updateAdcd,
  batchStatus,
  selecAdcdtInfo
} from "@/api/watershed/ads";
const { proxy } = getCurrentInstance();
import MinMap from "@/components/Map/plugins/drawTool";

defineOptions({
  name: 'AdcdIndex'
})

const adcdList = ref([]);
const open = ref(false);
const open2 = ref(false);
const curTitle = ref("");
const loading = ref(false);
const showSearch = ref(true);
const title = ref("");
const curGeom = ref("");
const code = ref(""); // 当前的行政区编码
const adcdTreeOptions = ref([]);
const isExpandAll = ref(false);
const refreshTable = ref(true);
const isRoot = ref(false);
const editDis = ref(false);

const uploadRef = ref('')
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
import { getToken } from '@/utils/auth'
const token = getToken()
const data = reactive({
  form: {
    geoType: 'people'
  },
  queryParams: {
    adcd: null,
  },
  rules: {
    adnm: [{ required: true, message: "行政区名称不能为空", trigger: "blur" }],
    adcd: [{ required: true, message: "行政区编码不能为空", trigger: "blur" }]
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询菜单列表 */
function getList() {
  loading.value = true;
  selectAdcdList(queryParams.value).then((res) => {
    loading.value = false;
    adcdList.value = res.data
  }).finally(() => {
    loading.value = false;
  })
}
getList1()
function getList1() {
  selectAdcdList({}).then((res) => {

    adcdTreeOptions.value = res.data
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  uploadRef.value?.clearFiles()
  uploadRef.value?.handleRemove()
  reset();
}
/** 表单重置 */
function reset(flag) {
  editDis.value = false
  form.value = {
    geoType: 'people',
    adcd: undefined,
    addFlag: flag,
    parentAdcd: undefined,
    adnm: '',
    shortName: '',
    area: 0,
  };
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
async function handleAdd(row) {
  reset(true);
  isRoot.value = false
  editDis.value = false
  // await getTreeselect('');
  if (row != null && row.adcd) {
    form.value.parentAdcd = row.adcd;
  } else {
    form.value.parentAdcd = '';
  }
  open.value = true;
  title.value = "添加菜单";
}
/** 展开/折叠操作 */
function toggleExpandAll() {
  // 弹框打开文件导入 应该
}
/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  editDis.value = true

  // await getTreeselect();
  // 去查询接口查询 当前行政区的详情

  let obj = Object.assign({ geoType: 'people' }, row);
  delete obj.children
  delete obj.createBy
  delete obj.createTime
  delete obj.updateBy
  delete obj.updateTime
  delete obj.remark
  form.value = obj
  open.value = true;
  title.value = "修改行政区";
  selecAdcdtInfo(row.adcd).then(response => {
    form.value.geom = JSON.parse(response.data.geom) || ''

  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate(valid => {
    if (valid) {
      if (form.value.geom == null || form.value.geom == '' || form.value.geom == {}) {
        form.value.geom = null
      }
      if (form.value.addFlag) {
        addAdcd(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      } else {
        updateAdcd(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
      open.value = false;
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.adnm + '"的数据项?').then(function () {
    return delAdcd(row.adcd);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}
/** 用户状态修改  */
function handleStatusChange(row) {
  let text = row.status == 0 ? "启用" : "停用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.adnm + '"吗?').then(function () {
    return batchStatus({ status: row.status == 0 ? 0 : 1, adcd: row.adcd });
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
    getList()
  }).catch(function () {
    row.status = row.status == 0 ? 1 : 0;
  });
};
async function showMap(row) {
  let res = await selecAdcdtInfo(row.adcd)
  if (res.data) {
    open2.value = true
    await nextTick()
    //检测res.data.geom是什么类型，是否需要parse
    if (res.data.geom) {
      if (typeof res.data.geom == 'string') {
        curGeom.value = JSON.parse(res.data.geom)
      } else {
        curGeom.value = res.data.geom
      }
    }
    // curGeom.value = res.data.geom ? JSON.parse(res.data.geom) : {}
    curTitle.value = row.adnm
  } else {
    proxy.$modal.msgSuccess("没找到空间数据");
  }
}
function updateBound(geojson) {
  // 提交geojson数据到后台
  console.log(geojson)
  form.value.geom = geojson
}
function handleChange(file, fileList) {
  if (fileList.length > 1) {
    fileList[0] = fileList[1]
    fileList.splice(1, 1);
  }
}
function handleRemove(file) {
  form.value.geom = {}
}
function handleSuccess(
  response
) {
  if (response.code == 200) {
    form.value.geom = response.data
  } else {
    uploadRef.value?.clearFiles()
    proxy.$modal.msgError(response.msg);
  }
}

getList();
</script>
