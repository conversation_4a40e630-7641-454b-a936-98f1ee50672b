<script setup>
import { onMounted, reactive, ref, onUnmounted } from "vue";
import { getCurrentInstance } from "vue";
import * as echarts from "echarts";
import {
  getSluiceWaterStationList,
  getSluiceWaterDetail,
} from "@/api/waterGate/index";
import trend_up_img from "@/assets/icon/trend_up.png";
import trend_down_img from "@/assets/icon/trend_down.png";
import trend_img from "@/assets/icon/trend.png";
import moment from "moment";
import { deepClone } from "@/utils";

defineOptions({
  name: "SluiceWaterStatus",
});

const { proxy } = getCurrentInstance();

// 数据定义
const loading = ref(false);
const detailLoading = ref(false);
const showSearch = ref(true);
const sluiceStationList = ref([]);
const total = ref(0);
const detailVisible = ref(false);
const detailTitle = ref("堰闸水情详情");
const detailData = ref([]);
const showTable = ref(false);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  stationName: "", // 测站名称
  stationCode: "", // 测站编码
  startTime: "", // 查询时间
});

// 详情查询参数
const detailQueryParams = reactive({
  stationCode: "", // 测站编码
  stationName: "", // 测站名称
  timeRange: [], // 查询时间
});

// 初始化日期范围为最近7天
const initDateRange = () => {
  detailQueryParams.timeRange = [
    moment().subtract(7, "days").format("YYYY-MM-DD HH:mm:ss"),
    moment().format("YYYY-MM-DD HH:mm:ss"),
  ];
};

// 获取堰闸水情列表
const getSluiceWaterList = async () => {
  try {
    loading.value = true;
    // 注释实际的API调用，使用模拟数据
    const res = await getSluiceWaterStationList(queryParams);

    if (res.code == 200) {
      sluiceStationList.value = res.rows;
      total.value = res.total;
    }
  } catch (error) {
    console.error("获取堰闸水情列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 重置查询表单
const resetQuery = () => {
  proxy.resetForm("queryRef");
  queryParams.pageNum = 1;
  handleQuery();
};

// 查询按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1;
  getSluiceWaterList();
};

// 查看详情
const handleDetail = (row) => {
  detailQueryParams.stationCode = row.stcd;
  detailQueryParams.stationName = row.stnm;
  initDateRange(); // 初始化为最近7天
  detailVisible.value = true;
  showTable.value = false;
  getStationDetailData();
  detailTitle.value = row.stnm;
};

// 获取测站详情数据
const getStationDetailData = async () => {
  try {
    detailLoading.value = true;
    const res = await getSluiceWaterDetail(detailQueryParams.stationCode, {
      startTime: detailQueryParams.timeRange[0],
      endTime: detailQueryParams.timeRange[1],
    });

    if (res.code == 200) {
      // 按时间倒序
      detailData.value = res.data?.vos || [];
      detailData.value = detailData.value.sort((a, b) => {
        return new Date(b.tm) - new Date(a.tm);
      });
      nextTick(() => {
        initChart(detailData.value);
      });
    }
  } catch (error) {
    console.error("获取测站详情数据失败:", error);
  } finally {
    detailLoading.value = false;
  }
};

// 详情查询按钮操作
const handleDetailQuery = () => {
  getStationDetailData();
};

// 重置详情查询
const resetDetailQuery = () => {
  initDateRange();
  getStationDetailData();
};

// 切换图表/表格显示
const toggleView = () => {
  showTable.value = !showTable.value;
  if (showTable.value) {
    initChart(detailData.value);
  }
};

const sluiceWaterChartRef = ref(null);
// 初始化图表
const initChart = (data) => {
  // 按时间正序
  const chatData = deepClone(data).sort((a, b) => {
    return new Date(a.tm) - new Date(b.tm);
  });
  const myChart = echarts.init(sluiceWaterChartRef.value);

  const times = chatData
    .map((item) => item.tm)
    .map((item) => moment(item).format("MM-DD HH:mm"));
  const upzValues = chatData.map((item) => item.upz);
  const dwzValues = chatData.map((item) => item.dwz);
  const qValues = chatData.map((item) => item.tgtq);

  const option = {
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: ["闸上水位", "闸下水位", "总过闸流量"],
      top: 10,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "5%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: times,
    },
    yAxis: [
      {
        type: "value",
        name: "水位(m)",
        position: "left",
        boundaryGap: ["12.5%", "12.5%"],
      },
      {
        type: "value",
        name: "流量(m³/s)",
        position: "right",
        boundaryGap: ["12.5%", "12.5%"],
      },
    ],
    series: [
      {
        name: "闸上水位",
        type: "line",
        data: upzValues,
        yAxisIndex: 0,
        symbol: "circle",
        symbolSize: 6,
      },
      {
        name: "闸下水位",
        type: "line",
        data: dwzValues,
        yAxisIndex: 0,
        symbol: "circle",
        symbolSize: 6,
      },
      {
        name: "总过闸流量",
        type: "line",
        data: qValues,
        yAxisIndex: 1,
        symbol: "circle",
        symbolSize: 6,
      },
    ],
  };

  myChart.setOption(option);

  // 监听窗口大小变化，重绘图表
  window.addEventListener("resize", () => {
    myChart.resize();
  });
};

// 初始化
onMounted(() => {
  getSluiceWaterList();
  // handleDetail({
  //   stationCode: "1234567890",
  //   stationName: "测试站",
  // });
});

onUnmounted(() => {
  window.removeEventListener("resize", () => {});
});
</script>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="测站名称" prop="stationName">
        <el-input
          v-model="queryParams.stationName"
          placeholder="请输入测站名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测站编码" prop="stationCode">
        <el-input
          v-model="queryParams.stationCode"
          placeholder="请输入测站编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="时间" prop="startTime">
        <el-date-picker
          v-model="queryParams.startTime"
          type="datetime"
          placeholder="选择查询时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          clearable
        />
      </el-form-item>

      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="Refresh" type="primary" plain @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 数据列表 -->
    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getSluiceWaterList"
          ></right-toolbar>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="sluiceStationList" stripe>
        <el-table-column type="index" label="序号" width="65" align="center" />
        <el-table-column
          label="测站名称"
          :show-overflow-tooltip="true"
          align="center"
        >
          <template #default="scope">
            <el-link type="primary" @click="handleDetail(scope.row)">{{
              scope.row.stnm
            }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="stcd" label="测站编码" align="center" />
        <el-table-column prop="tm" label="时间" align="center" width="160" />
        <el-table-column prop="upz" label="闸上水位(m)" align="center" />
        <el-table-column prop="supwptn" label="闸上水势" align="center">
          <template #default="scope">
            <span v-if="scope.row.supwptn == '涨'"
              ><img :src="trend_up_img" style="width: 18px; padding: 2px"
            /></span>
            <span v-if="scope.row.supwptn == '平'"
              ><img :src="trend_img" style="width: 18px; padding: 2px"
            /></span>
            <span v-if="scope.row.supwptn == '降'"
              ><img :src="trend_down_img" style="width: 18px; padding: 2px"
            /></span>
          </template>
        </el-table-column>
        <el-table-column prop="dwz" label="闸下水位(m)" align="center" />
        <el-table-column prop="sdwwptn" label="闸下水势" align="center">
          <template #default="scope">
            <span v-if="scope.row.sdwwptn == '涨'"
              ><img :src="trend_up_img" style="width: 18px; padding: 2px"
            /></span>
            <span v-if="scope.row.sdwwptn == '平'"
              ><img :src="trend_img" style="width: 18px; padding: 2px"
            /></span>
            <span v-if="scope.row.sdwwptn == '降'"
              ><img :src="trend_down_img" style="width: 18px; padding: 2px"
            /></span>
          </template>
        </el-table-column>
        <el-table-column prop="tgtq" label="总过闸流量(m³/s)" align="center" />
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getSluiceWaterList"
      />
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      :title="detailTitle"
      v-model="detailVisible"
      width="800"
      append-to-body
    >
      <div class="detail-search">
        <el-form :inline="true">
          <el-form-item>
            <el-date-picker
              v-model="detailQueryParams.timeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleDetailQuery"
              >查询</el-button
            >
            <el-button type="primary" plain @click="resetDetailQuery"
              >重置</el-button
            >
            <el-button type="primary" plain @click="toggleView">
              {{ showTable ? "图" : "表" }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div v-loading="detailLoading" class="detail-content">
        <!-- 图表/表格切换显示 -->
        <div
          v-show="!showTable"
          ref="sluiceWaterChartRef"
          class="chart-container"
        ></div>
        <div v-show="showTable" class="table-container">
          <el-table :data="detailData" stripe height="400">
            <el-table-column
              type="index"
              label="序号"
              width="65"
              align="center"
            />
            <el-table-column prop="tm" label="时间" align="center" />
            <el-table-column prop="upz" label="闸上水位(m)" align="center" />
            <el-table-column prop="dwz" label="闸下水位(m)" align="center" />
            <el-table-column
              prop="tgtq"
              label="总过闸流量(m³/s)"
              align="center"
            />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.detail-search {
  margin-bottom: 20px;

  .range-separator {
    margin: 0 10px;
  }
}

.detail-content {
  height: 420px;

  .chart-container {
    width: 100%;
    height: 400px;
  }

  .table-container {
    width: 100%;
  }
}
</style>
