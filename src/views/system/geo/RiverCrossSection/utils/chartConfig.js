export const chartOption = {
  grid: {
    left: "50px",
    right: "70px",
    bottom: "60px",
    top: "50px",
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "cross",
      crossStyle: { color: "#555" },
      label: {
        backgroundColor: "#555",
      },
    },
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderColor: "#ccc",
    borderWidth: 1,
    textStyle: {
      color: "#333",
    },
    formatter: function (params) {
      const point = params[0];
      return `<div style="padding: 3px 6px;">
                <div style="margin-bottom: 4px;font-weight: bold;color: #666;">断面数据</div>
                <div style="display: flex; justify-content: space-between;">
                    <span>起点距:</span>
                    <span style="font-weight: bold;margin-left: 10px;">${point.name} m</span>
                </div>
                <div style="display: flex; justify-content: space-between;">
                    <span>河底高程:</span>
                    <span style="font-weight: bold;margin-left: 10px;">${point.value} m</span>
                </div>
            </div>`;
    },
  },
  xAxis: {
    type: "category",
    name: "起点距(m)",
    data: [],
    boundaryGap: false,
    axisPointer: {
      type: "shadow",
    },
    axisLabel: {
      color: "#666",
      fontSize: 11,
    },
    axisLine: {
      lineStyle: {
        color: "#ccc",
      },
    },
    axisTick: {
      show: false,
    },
    nameTextStyle: {
      fontWeight: "bold",
      color: "#333",
    },
  },
  dataZoom: [
    {
      type: "slider",
      show: true,
      start: 0,
      end: 100,
      xAxisIndex: [0],
      bottom: 10,
      height: 20,
      borderColor: "#ccc",
      fillerColor: "rgba(64, 158, 255, 0.15)",
      handleStyle: {
        color: "#409eff",
      },
      textStyle: {
        color: "#666",
      },
    },
    {
      type: "inside",
      zoomOnMouseWheel: false,
      moveOnMouseMove: true,
      moveOnMouseWheel: true,
    },
  ],
  yAxis: [
    {
      type: "value",
      name: "河底高程(m)",
      min: 0,
      scale: true,
      nameTextStyle: {
        fontWeight: "bold",
        color: "#333",
        padding: [0, 30, 0, 0],
      },
      axisLabel: {
        color: "#666",
        fontSize: 11,
        formatter: function (value) {
          return value.toFixed(1);
        },
      },
      splitLine: {
        lineStyle: {
          color: "#eee",
          type: "dashed",
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#ccc",
        },
      },
    },
  ],
  series: [
    {
      name: "河底高程",
      type: "line",
      symbol: "circle",
      symbolSize: 8,
      color: "#D2B48C",
      lineStyle: {
        width: 3,
      },
      emphasis: {
        itemStyle: {
          color: "#D2B48C",
          borderColor: "#fff",
          borderWidth: 2,
          shadowColor: "rgba(0, 0, 0, 0.3)",
          shadowBlur: 10,
        },
      },
      areaStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: "rgba(210, 180, 140, 0.7)",
            },
            {
              offset: 1,
              color: "rgba(210, 180, 140, 0.1)",
            },
          ],
        },
        shadowBlur: 10,
        shadowColor: "rgba(210, 180, 140, 0.5)",
        opacity: 0.8,
      },
      data: [],
    },
  ],
};