<script setup>
import { ref, reactive, nextTick, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import * as echarts from "echarts";
import moment from "moment";
import { saveRiverSection, getSubBasinList } from "@/api/watershed";
import { selectRvList, selectStaList } from "@/api/watershed/ads";
import { deepClone } from "@/utils";
import { chartOption } from "../utils/chartConfig";

defineOptions({
  name: "RiverSectionFormDialog",
});

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  sectionData: {
    type: Array,
    default: () => [],
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:visible", "success"]);

// 河流选项
const riverOptions = ref([]);

// 站点选项
const stationOptions = ref([]);

// 小流域选项
const subBasinOptions = ref([]);
const subBasinLoading = ref(false);

// 表单引用
const formRef = ref(null);

// 表单数据
const form = ref({
  sectionCode: "",
  riverCode: "",
  riverName: "",
  distanceToRiverMouth: null,
  stcd: "",
  stnm: "",
  longitude: null,
  latitude: null,
  leftEmbankmentElevation: null,
  rightEmbankmentElevation: null,
  measurementDate: "",
  startingShore: 0, // 默认为左岸
  isSet: 1, // 是否控制断面，默认为否(1-否，0-是)
  subBasinId: "", // 控制小流域代码
  sectionFlow: {
    // 50年一遇流量
    fiftyYear: "",
    // 500年一遇流量
    fiveHundredYear: "",
    // 5年一遇流量
    fiveYear: "",
    // 100年一遇流量
    hundredYear: "",
    // 河道断面ID
    sectionId: "",
    // 10年一遇流量
    tenYear: "",
    // 30年一遇流量
    thirtyYear: "",
    // 1000年一遇流量
    thousandYear: "",
    // 20年一遇流量
    twentyYear: "",
    // 200年一遇流量
    twoHundredYear: "",
    // 2年一遇流量
    twoYear: "",
  },
});

// 断面数据
const sectionData = ref([]);

// 表单验证规则
const rules = reactive({
  sectionCode: [
    { required: true, message: "请输入河道断面代码", trigger: "blur" },
  ],
  riverCode: [{ required: true, message: "请选择所属河流", trigger: "change" }],
  startingShore: [
    { required: true, message: "请选择起测岸别", trigger: "change" },
  ],
  isSet: [{ required: true, message: "请选择是否控制断面", trigger: "change" }],
  subBasinId: [
    {
      validator: (rule, value, callback) => {
        if (form.value.isSet === 0 && (!value || value === "")) {
          callback(new Error("当选择控制断面为是时，必须选择控制小流域"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  measurementDate: [
    { required: true, message: "请选择施测时间", trigger: "change" },
  ],
});

// 图表实例
let sectionChart = null;

// 禁用未来日期
const disabledFutureDates = (time) => {
  return time.getTime() > Date.now();
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 弹窗打开时初始化数据
      form.value = { ...props.formData };
      sectionData.value = [...props.sectionData];

      nextTick(() => {
        initChart("sectionChart", sectionData.value);
      });
    } else {
      // 弹窗关闭时清理图表
      if (sectionChart) {
        sectionChart.dispose();
        sectionChart = null;
      }
    }
  }
);

// 监听控制断面字段变化
watch(
  () => form.value.isSet,
  (newVal) => {
    if (newVal === 1) {
      // 当选择否时，清空控制小流域
      form.value.subBasinId = "";
    }
  }
);

const convertDataFormat = (data) => {
  // 检查是否是数组
  if (!Array.isArray(data)) {
    return null;
  }
  // 对数组中的每个元素进行处理
  return data.map((item) => {
    // 复制 data 属性并移除 children
    const newData = { ...item.data };
    delete newData.children;
    // 递归处理子元素
    if (item.children && item.children.length > 0) {
      newData.children = convertDataFormat(item.children);
    }
    return newData;
  });
};

// 远程搜索站点
const getStationList = async () => {
  try {
    const res = await selectStaList({
      pageNum: 1,
      pageSize: 999,
      stationTpyeList: ["ZQ", "ZZ", "RR"],
    });

    if (res.code === 200) {
      stationOptions.value = res.data.records || [];
    }
  } catch (error) {
    console.error("搜索站点失败", error);
  }
};

// 获取小流域列表
const loadSubBasinList = async () => {
  subBasinLoading.value = true;
  try {
    const res = await getSubBasinList({
      pageNum: 1,
      pageSize: 999,
    });
    if (res.code === 200) {
      subBasinOptions.value = res.rows || [];
    }
  } catch (error) {
    console.error("获取小流域列表失败", error);
  } finally {
    subBasinLoading.value = false;
  }
};

// 初始化图表
const initChart = (elementId, data) => {
  const chartContainer = document.getElementById(elementId);

  if (!chartContainer) return;

  // 销毁已存在的图表实例
  echarts.dispose(chartContainer);

  // 创建新图表实例
  const chart = echarts.init(chartContainer);

  // 准备数据
  const distances = data.map((item) => item.startingDistance);
  const elevations = data.map((item) => item.bottomElevation);

  // 设置图表数据
  const option = deepClone(chartOption);
  option.xAxis.data = distances;
  option.series[0].data = elevations;

  // 设置Y轴最大值
  if (elevations.length > 0) {
    const maxElevation = Math.max(...elevations.filter((val) => !isNaN(val)));
    option.yAxis[0].max = parseFloat((maxElevation * 1.2).toFixed(1));
  }

  chart.setOption(option);

  // 保存图表实例引用
  sectionChart = chart;

  return chart;
};

// 添加断面数据行
const addDataRow = () => {
  console.log(sectionData.value, "sectionData.value");
  // 验证最后一行数据是否完整
  if (sectionData.value.length > 0) {
    const lastRow = sectionData.value[sectionData.value.length - 1];
    console.log(lastRow, "lastRow");

    if (
      lastRow.startingDistance === "" ||
      lastRow.startingDistance === null ||
      lastRow.bottomElevation === "" ||
      lastRow.bottomElevation === null
    ) {
      ElMessage.warning("请先完成当前行数据再添加新行");
      return;
    }

    // 验证起点距是否递增
    if (sectionData.value.length > 1) {
      const lastIndex = sectionData.value.length - 1;
      const lastDistance = Number(
        sectionData.value[lastIndex].startingDistance
      );
      const prevDistance = Number(
        sectionData.value[lastIndex - 1].startingDistance
      );

      if (lastDistance <= prevDistance) {
        ElMessage.warning("起点距非递增！");
        return;
      }
    }
  }

  // 添加新行
  sectionData.value.push({
    startingDistance: "",
    bottomElevation: "",
  });

  // 如果有数据，更新图表
  if (
    sectionData.value.length > 0 &&
    sectionData.value[0].startingDistance !== "" &&
    sectionData.value[0].bottomElevation !== ""
  ) {
    nextTick(() => {
      initChart("sectionChart", sectionData.value);
    });
  }
};

// 删除最后一行
const removeLastRow = () => {
  if (sectionData.value.length > 1) {
    sectionData.value.pop();
    nextTick(() => {
      initChart("sectionChart", sectionData.value);
    });
  } else {
    ElMessage.warning("至少保留一条数据！");
  }
};

// 删除指定行
const removeRow = (index) => {
  if (sectionData.value.length > 1) {
    sectionData.value.splice(index, 1);
    nextTick(() => {
      initChart("sectionChart", sectionData.value);
    });
  } else {
    ElMessage.warning("至少保留一条数据！");
  }
};

// 移动行
const moveRow = (index, direction) => {
  if (direction === "up" && index > 0) {
    // 向上移动
    const temp = sectionData.value[index];
    sectionData.value[index] = sectionData.value[index - 1];
    sectionData.value[index - 1] = temp;
  } else if (direction === "down" && index < sectionData.value.length - 1) {
    // 向下移动
    const temp = sectionData.value[index];
    sectionData.value[index] = sectionData.value[index + 1];
    sectionData.value[index + 1] = temp;
  }

  nextTick(() => {
    initChart("sectionChart", sectionData.value);
  });
};

// 验证并提交表单
const submitForm = () => {
  // 验证断面数据
  if (sectionData.value.length === 0) {
    ElMessage.warning("请至少添加一组断面数据");
    return;
  }

  // 验证断面数据完整性
  for (const item of sectionData.value) {
    if (
      item.startingDistance === "" ||
      item.startingDistance === null ||
      item.bottomElevation === "" ||
      item.bottomElevation === null
    ) {
      ElMessage.warning("断面数据不完整，请检查");
      return;
    }
  }

  // 验证起点距递增
  for (let i = 1; i < sectionData.value.length; i++) {
    if (
      Number(sectionData.value[i].startingDistance) <=
      Number(sectionData.value[i - 1].startingDistance)
    ) {
      ElMessage.warning("起点距非递增！");
      return;
    }
  }

  // 表单验证
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await saveRiverSection({
          ...form.value,
          riverSectionElevationList: sectionData.value.map((item, index) => ({
            ...item,
            lineNumber: index + 1,
          })),
        });

        if (res.code === 200) {
          ElMessage.success("保存成功");
          emit("update:visible", false);
          emit("success");
        }
      } catch (error) {
        console.error("保存失败", error);
      }
    }
  });
};

// 关闭弹窗
const handleClose = () => {
  emit("update:visible", false);
};

const getRiverList = async () => {
  const res = await selectRvList({
    pageNum: 1,
    pageSize: 999,
  });
  if (res.code === 200) {
    riverOptions.value = convertDataFormat(res.data || []);
  }
};

onMounted(() => {
  // 初始化河流列表
  getRiverList();
  // 初始化监测站点列表
  getStationList("");
  // 加载小流域列表
  loadSubBasinList();
});
</script>

<template>
  <!-- 新增/编辑对话框 -->
  <el-dialog
    :title="title"
    :model-value="visible"
    @update:model-value="handleClose"
    width="1000px"
    destroy-on-close
    :close-on-click-modal="false"
    class="section-dialog"
  >
    <div class="section-form-container">
      <div class="section-title-bar">
        <h3 class="section-subtitle">基础信息</h3>
      </div>
      <el-form
        ref="formRef"
        :model="form"
        label-width="120px"
        :rules="rules"
        class="base-info-form"
      >
        <div class="form-row">
          <el-form-item label="河道断面代码" prop="sectionCode">
            <el-input
              v-model="form.sectionCode"
              placeholder="请输入河道断面代码"
              :disabled="isEdit"
              maxlength="8"
            ></el-input>
          </el-form-item>
          <el-form-item label="所属河流" prop="riverCode">
            <el-select
              v-model="form.riverCode"
              filterable
              placeholder="请选择所属河流"
              clearable
              :disabled="isEdit"
              class="w-full"
            >
              <el-option
                v-for="item in riverOptions"
                :key="item.id"
                :label="item.rvName"
                :value="item.rvCode"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="距河口距离(km)" prop="distanceToRiverMouth">
            <el-input-number
              v-model="form.distanceToRiverMouth"
              placeholder="请输入距河口距离"
              :precision="1"
              :min="0"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="监测站点" prop="stcd">
            <el-select
              v-model="form.stcd"
              filterable
              placeholder="请输入站点名称进行搜索"
              clearable
              class="w-full"
            >
              <el-option
                v-for="item in stationOptions"
                :key="item.stcd"
                :label="item.stnm"
                :value="item.stcd"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="经度" prop="longitude">
            <el-input-number
              v-model="form.longitude"
              placeholder="请输入经度"
              :precision="6"
              :min="73"
              :max="136"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="纬度" prop="latitude">
            <el-input-number
              v-model="form.latitude"
              placeholder="请输入纬度"
              :precision="6"
              :min="3"
              :max="54"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item> -->
          <el-form-item label="是否控制断面" prop="isSet">
            <el-select
              v-model="form.isSet"
              placeholder="请选择是否控制断面"
              clearable
              class="w-full"
            >
              <el-option label="是" :value="0"></el-option>
              <el-option label="否" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="控制小流域"
            prop="subBasinId"
            :required="form.isSet === 0"
          >
            <el-select
              v-model="form.subBasinId"
              filterable
              placeholder="请选择控制小流域"
              clearable
              :loading="subBasinLoading"
              class="w-full"
              :disabled="form.isSet !== 0"
            >
              <el-option
                v-for="item in subBasinOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="左堤高程(m)" prop="leftEmbankmentElevation">
            <el-input-number
              v-model="form.leftEmbankmentElevation"
              placeholder="请输入左堤高程"
              :precision="3"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="右堤高程(m)" prop="rightEmbankmentElevation">
            <el-input-number
              v-model="form.rightEmbankmentElevation"
              placeholder="请输入右堤高程"
              :precision="3"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="施测时间" prop="measurementDate">
            <el-date-picker
              v-model="form.measurementDate"
              type="date"
              placeholder="请选择施测时间"
              :disabled-date="disabledFutureDates"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              class="w-full"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="起测岸别" prop="startingShore">
            <el-radio-group v-model="form.startingShore">
              <el-radio :label="0">左岸</el-radio>
              <el-radio :label="1">右岸</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 断面数据部分 -->
    <div class="section-form-container">
      <div class="section-title-bar">
        <h3 class="section-subtitle">断面数据</h3>
        <div class="section-actions">
          <el-tooltip content="添加一行" placement="top">
            <el-button type="primary" circle size="small" @click="addDataRow">
              <el-icon><Plus /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除末行" placement="top">
            <el-button type="danger" circle size="small" @click="removeLastRow">
              <el-icon><Minus /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>

      <div class="section-data-content">
        <div class="data-table-container">
          <el-table
            :data="sectionData"
            border
            stripe
            size="small"
            max-height="330px"
            highlight-current-row
            class="section-data-table"
          >
            <el-table-column
              type="index"
              label="垂线号"
              width="80"
              align="center"
            />
            <el-table-column
              label="起点距(m)"
              prop="startingDistance"
              align="center"
            >
              <template #default="{ row }">
                <el-input-number
                  v-model="row.startingDistance"
                  placeholder="请输入起点距"
                  :precision="3"
                  :controls="false"
                  size="small"
                  class="w-full"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="河底高程(m)"
              prop="bottomElevation"
              align="center"
            >
              <template #default="{ row }">
                <el-input-number
                  v-model="row.bottomElevation"
                  placeholder="请输入河底高程"
                  :controls="false"
                  size="small"
                  :precision="3"
                  class="w-full"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120">
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  circle
                  @click="moveRow(scope.$index, 'up')"
                  :disabled="scope.$index === 0"
                >
                  <el-icon><ArrowUp /></el-icon>
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  circle
                  @click="moveRow(scope.$index, 'down')"
                  :disabled="scope.$index === sectionData.length - 1"
                >
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  circle
                  @click="removeRow(scope.$index)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="chart-container">
          <div id="sectionChart"></div>
        </div>
      </div>
    </div>

    <!-- 重现期流量部分 -->
    <div class="section-form-container">
      <div class="section-title-bar">
        <h3 class="section-subtitle">重现期流量（单位：m³/s）</h3>
      </div>
      <el-form :model="form" label-width="120px" class="flow-form">
        <div class="flow-row">
          <el-form-item label="2年">
            <el-input-number
              v-model="form.sectionFlow.twoYear"
              placeholder="请输入2年重现期流量"
              :precision="3"
              :min="0"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="5年">
            <el-input-number
              v-model="form.sectionFlow.fiveYear"
              placeholder="请输入5年重现期流量"
              :precision="3"
              :min="0"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="10年">
            <el-input-number
              v-model="form.sectionFlow.tenYear"
              placeholder="请输入10年重现期流量"
              :precision="3"
              :min="0"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="20年">
            <el-input-number
              v-model="form.sectionFlow.twentyYear"
              placeholder="请输入20年重现期流量"
              :precision="3"
              :min="0"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="30年">
            <el-input-number
              v-model="form.sectionFlow.thirtyYear"
              placeholder="请输入30年重现期流量"
              :precision="3"
              :min="0"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="50年">
            <el-input-number
              v-model="form.sectionFlow.fiftyYear"
              placeholder="请输入50年重现期流量"
              :precision="3"
              :min="0"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="100年">
            <el-input-number
              v-model="form.sectionFlow.hundredYear"
              placeholder="请输入100年重现期流量"
              :precision="3"
              :min="0"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="200年">
            <el-input-number
              v-model="form.sectionFlow.twoHundredYear"
              placeholder="请输入200年重现期流量"
              :precision="3"
              :min="0"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="500年">
            <el-input-number
              v-model="form.sectionFlow.fiveHundredYear"
              placeholder="请输入500年重现期流量"
              :precision="3"
              :min="0"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="1000年">
            <el-input-number
              v-model="form.sectionFlow.thousandYear"
              placeholder="请输入1000年重现期流量"
              :precision="3"
              :min="0"
              controls-position="right"
              class="w-full"
            ></el-input-number>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <el-button type="primary" @click="submitForm">保 存</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.section-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    margin-right: 0;
  }

  .el-dialog__body {
    padding: 20px 24px;
    max-height: 75vh;
    overflow-y: auto;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 14px 20px;
  }
}

.section-form-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  padding: 16px 0 8px;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: #409eff;
  }
}

.base-info-form {
  padding: 0 16px;

  .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 0 50px;

    :deep(.el-form-item) {
      margin-bottom: 16px;
      flex: 1;
      min-width: 280px;

      .el-form-item__label {
        font-weight: 500;
      }

      .el-input__wrapper,
      .el-select__wrapper {
        transition: all 0.3s;
      }

      .el-input.is-disabled .el-input__wrapper {
        background-color: #f8f9fa;
      }
    }
  }
}

.flow-form {
  padding: 0 16px;

  .flow-row {
    display: flex;
    flex-wrap: wrap;
    gap: 0 50px;

    :deep(.el-form-item) {
      margin-bottom: 16px;
      flex: 1;
      min-width: 280px;

      .el-form-item__label {
        font-weight: 500;
        width: 80px !important;
      }

      .el-input__wrapper {
        transition: all 0.3s;
      }
    }
  }
}

.section-actions {
  display: flex;
  gap: 8px;
}

.section-data-content {
  display: flex;
  padding: 0 16px;
  gap: 24px;

  @media screen and (max-width: 1200px) {
    flex-direction: column;
  }
}

.data-table-container {
  flex: 1;

  .section-data-table {
    width: 100%;
  }
}

.chart-container {
  flex: 1.2;

  #sectionChart {
    height: 350px;
    width: 100%;
  }
}
</style>
