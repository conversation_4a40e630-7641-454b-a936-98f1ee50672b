<script setup>
import { ref, nextTick, watch } from "vue";
import * as echarts from "echarts";
import { deepClone } from "@/utils";
import { chartOption } from "../utils/chartConfig";

defineOptions({
  name: "RiverSectionDetailDialog",
});

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  detailData: {
    type: Object,
    default: () => ({}),
  },
  sectionData: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["update:visible"]);

// 图表实例
let detailChart = null;

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 弹窗打开时初始化图表
      nextTick(() => {
        initChart("detailChart", props.sectionData, true);
      });
    } else {
      // 弹窗关闭时清理图表
      if (detailChart) {
        detailChart.dispose();
        detailChart = null;
      }
    }
  }
);

// 初始化图表
const initChart = (elementId, data, isDetail = false) => {
  const chartContainer = document.getElementById(elementId);

  if (!chartContainer) return;

  // 销毁已存在的图表实例
  echarts.dispose(chartContainer);

  // 创建新图表实例
  const chart = echarts.init(chartContainer);

  // 准备数据
  const distances = data.map((item) => item.startingDistance);
  const elevations = data.map((item) => item.bottomElevation);

  // 设置图表数据
  const option = deepClone(chartOption);
  option.xAxis.data = distances;
  option.series[0].data = elevations;

  // 设置Y轴最大值
  if (elevations.length > 0) {
    const maxElevation = Math.max(...elevations.filter((val) => !isNaN(val)));
    option.yAxis[0].max = parseFloat((maxElevation * 1.2).toFixed(1));
  }

  chart.setOption(option);

  // 保存图表实例引用
  if (isDetail) {
    detailChart = chart;
  }

  return chart;
};

// 关闭弹窗
const handleClose = () => {
  emit("update:visible", false);
};
</script>

<template>
  <!-- 详情对话框 -->
  <el-dialog
    title="河道断面详情"
    :model-value="visible"
    @update:model-value="handleClose"
    width="70vw"
    destroy-on-close
    class="section-dialog"
  >
    <div class="section-form-container">
      <div class="section-title-bar">
        <h3 class="section-subtitle">基础信息</h3>
      </div>
      <div class="station-info">
        <div class="info-item">
          <span class="label">河道断面代码:</span>
          <span class="value">{{ detailData.sectionCode }}</span>
        </div>
        <div class="info-item">
          <span class="label">所属河流:</span>
          <span class="value">{{ detailData.riverName }}</span>
        </div>
        <div class="info-item">
          <span class="label">距河口距离:</span>
          <span class="value">{{
            detailData.distanceToRiverMouth
              ? detailData.distanceToRiverMouth + " km"
              : "-"
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">监测站点:</span>
          <span class="value">{{ detailData.stnm || "-" }}</span>
        </div>
        <div class="info-item">
          <span class="label">控制断面:</span>
          <span class="value">{{
            detailData.isSet === 0 ? "是" : detailData.isSet === 1 ? "否" : "-"
          }}</span>
        </div>
        <div class="info-item" v-if="detailData.isSet === 0">
          <span class="label">控制小流域:</span>
          <span class="value">{{ detailData.subBasinName || "-" }}</span>
        </div>
        <div class="info-item">
          <span class="label">左堤高程:</span>
          <span class="value">{{
            detailData.leftEmbankmentElevation
              ? detailData.leftEmbankmentElevation + " m"
              : "-"
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">右堤高程:</span>
          <span class="value">{{
            detailData.rightEmbankmentElevation
              ? detailData.rightEmbankmentElevation + " m"
              : "-"
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">施测时间:</span>
          <span class="value">{{ detailData.measurementDate || "-" }}</span>
        </div>
        <div class="info-item">
          <span class="label">起测岸别:</span>
          <span class="value">{{
            detailData.startingShore == 0
              ? "左岸"
              : detailData.startingShore == 1
              ? "右岸"
              : "-"
          }}</span>
        </div>
      </div>
    </div>

    <!-- 断面数据部分 -->
    <div class="section-form-container">
      <div class="section-title-bar">
        <h3 class="section-subtitle">断面数据</h3>
      </div>
      <div class="section-data-content">
        <div class="data-table-container">
          <el-table
            :data="sectionData"
            border
            stripe
            size="small"
            max-height="330px"
            highlight-current-row
            class="section-data-table"
          >
            <el-table-column
              type="index"
              label="垂线号"
              width="80"
              align="center"
            />
            <el-table-column
              label="起点距(m)"
              prop="startingDistance"
            ></el-table-column>
            <el-table-column
              label="河底高程(m)"
              prop="bottomElevation"
            ></el-table-column>
          </el-table>
        </div>
        <div class="chart-container">
          <div id="detailChart"></div>
        </div>
      </div>
    </div>

    <!-- 重现期流量部分 -->
    <div class="section-form-container" v-if="detailData.sectionFlow">
      <div class="section-title-bar">
        <h3 class="section-subtitle">重现期流量（单位：m³/s）</h3>
      </div>
      <div class="flow-info">
        <div class="info-item">
          <span class="label">2年:</span>
          <span class="value">{{ detailData.sectionFlow.twoYear || "-" }}</span>
        </div>
        <div class="info-item">
          <span class="label">5年:</span>
          <span class="value">{{
            detailData.sectionFlow.fiveYear || "-"
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">10年:</span>
          <span class="value">{{ detailData.sectionFlow.tenYear || "-" }}</span>
        </div>
        <div class="info-item">
          <span class="label">20年:</span>
          <span class="value">{{
            detailData.sectionFlow.twentyYear || "-"
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">30年:</span>
          <span class="value">{{
            detailData.sectionFlow.thirtyYear || "-"
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">50年:</span>
          <span class="value">{{
            detailData.sectionFlow.fiftyYear || "-"
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">100年:</span>
          <span class="value">{{
            detailData.sectionFlow.hundredYear || "-"
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">200年:</span>
          <span class="value">{{
            detailData.sectionFlow.twoHundredYear || "-"
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">500年:</span>
          <span class="value">{{
            detailData.sectionFlow.fiveHundredYear || "-"
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">1000年:</span>
          <span class="value">{{
            detailData.sectionFlow.thousandYear || "-"
          }}</span>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.section-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    margin-right: 0;
  }

  .el-dialog__body {
    padding: 20px 24px;
    max-height: 75vh;
    overflow-y: auto;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 14px 20px;
  }
}

.section-form-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  padding: 16px 0 8px;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: #409eff;
  }
}

.section-data-content {
  display: flex;
  padding: 0 16px;
  gap: 24px;

  @media screen and (max-width: 1200px) {
    flex-direction: column;
  }
}

.data-table-container {
  flex: 1;

  .section-data-table {
    width: 100%;
  }
}

.chart-container {
  flex: 1.2;

  #detailChart {
    height: 350px;
    width: 100%;
  }
}

.station-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding: 0 16px 16px;

  .info-item {
    display: flex;
    align-items: center;

    .label {
      min-width: 100px;
      color: #606266;
      font-weight: 500;
    }

    .value {
      color: #303133;
      flex: 1;
    }
  }
}

.flow-info {
  display: grid;
  // 一行两个列
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 0 16px 16px;

  .info-item {
    display: flex;
    align-items: center;

    .label {
      min-width: 60px;
      color: #606266;
      font-weight: 500;
    }

    .value {
      color: #303133;
      flex: 1;
    }
  }
}
</style>
