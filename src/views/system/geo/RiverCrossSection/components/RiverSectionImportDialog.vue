<script setup>
import { ref, reactive } from "vue";
import { getToken } from "@/utils/auth";
import { getCurrentInstance } from "vue";

defineOptions({
  name: "RiverSectionImportDialog",
});

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:visible", "success"]);

const { proxy } = getCurrentInstance();

// 导入对话框状态
const importState = reactive({
  uploading: false,
  activeTab: "section",
});

// 导入URL
const importUrls = reactive({
  section:
    import.meta.env.VITE_APP_BASE_API + "/system/river_section/upload_section",
  bottomElevation:
    import.meta.env.VITE_APP_BASE_API +
    "/system/river_section/upload_elevation",
});

// HTTP请求头
const headers = reactive({
  Authorization: "Bearer " + getToken(),
});

// 下载模板
const downloadTemplate = (type) => {
  let fileName, displayName;
  if (type === "section") {
    fileName = "河道断面导入模板.xlsx";
    displayName = "河道断面数据模板.xlsx";
  } else {
    fileName = "河底高程导入模板.xlsx";
    displayName = "河底高程数据模板.xlsx";
  }

  proxy.download(
    "file/download",
    {
      fileName: fileName,
      bucketName: "excel-template",
    },
    displayName
  );
};

// 文件上传进度
const handleFileUploadProgress = () => {
  importState.uploading = true;
};

// 文件上传成功
const handleFileSuccess = (response) => {
  importState.uploading = false;

  if (response.code === 200) {
    emit("update:visible", false);
    proxy.$modal.msgSuccess("导入成功");
    emit("success");
  } else {
    proxy.$modal.msgError(response.msg || "导入失败");
  }
};

// 提交上传
const submitUpload = () => {
  if (importState.activeTab === "section") {
    proxy.$refs.sectionUploadRef.submit();
  } else {
    proxy.$refs.elevationUploadRef.submit();
  }
};

// 关闭弹窗
const handleClose = () => {
  emit("update:visible", false);
};

// 打开弹窗时重置状态
const handleOpen = () => {
  importState.uploading = false;
  importState.activeTab = "section";
};
</script>

<template>
  <!-- 导入对话框 -->
  <el-dialog
    title="河道断面数据导入"
    :model-value="visible"
    @update:model-value="handleClose"
    @open="handleOpen"
    width="400px"
    append-to-body
  >
    <el-tabs v-model="importState.activeTab">
      <el-tab-pane label="河道断面导入" name="section">
        <el-upload
          ref="sectionUploadRef"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="headers"
          :action="importUrls.section"
          :disabled="importState.uploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip text-center">
              <span>仅允许导入xls、xlsx格式文件。</span>
              <el-link
                type="primary"
                :underline="false"
                style="font-size: 12px; vertical-align: baseline"
                @click="downloadTemplate('section')"
                >下载模板</el-link
              >
            </div>
          </template>
        </el-upload>
      </el-tab-pane>
      <el-tab-pane label="河底高程导入" name="bottomElevation">
        <el-upload
          ref="elevationUploadRef"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="headers"
          :action="importUrls.bottomElevation"
          :disabled="importState.uploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip text-center">
              <span>仅允许导入xls、xlsx格式文件。</span>
              <el-link
                type="primary"
                :underline="false"
                style="font-size: 12px; vertical-align: baseline"
                @click="downloadTemplate('bottomElevation')"
                >下载模板</el-link
              >
            </div>
          </template>
        </el-upload>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button type="primary" @click="submitUpload">保 存</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.el-upload__tip {
  margin-top: 8px;

  .el-link {
    margin-left: 8px;
  }
}
</style>
