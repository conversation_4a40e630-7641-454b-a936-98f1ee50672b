<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { selectRvList } from "@/api/watershed/ads";
import { getCurrentInstance } from "vue";
import {
  getRiverSectionList,
  getRiverSectionInfo,
  deleteRiverSection,
} from "@/api/watershed";
import RiverSectionFormDialog from "./components/RiverSectionFormDialog.vue";
import RiverSectionDetailDialog from "./components/RiverSectionDetailDialog.vue";
import RiverSectionImportDialog from "./components/RiverSectionImportDialog.vue";

defineOptions({
  name: "RiverCrossSection",
});

const { proxy } = getCurrentInstance();

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  sectionCode: "",
  riverCode: "",
  isSet: "",
  isControl: "",
});

// 表格数据
const tableList = ref([]);
const total = ref(0);
const loading = ref(false);

// 河流选项
const riverOptions = ref([]);

// 对话框相关
const dialog = reactive({
  visible: false,
  title: "",
});

// 详情对话框
const detailDialog = reactive({
  visible: false,
});

// 导入对话框
const importDialog = reactive({
  visible: false,
});

// 详情数据
const detailData = ref({});
const detailSectionData = ref([]);

// 表单数据（用于传递给弹窗组件）
const formData = ref({});
const sectionData = ref([]);

// 查询列表
const getList = async () => {
  try {
    loading.value = true;
    const res = await getRiverSectionList(queryParams);
    if (res.code === 200) {
      tableList.value = res.rows || [];
      total.value = res.total || 0;
    }
  } catch (error) {
    console.error("获取列表失败", error);
  } finally {
    loading.value = false;
  }
};

// 重置查询
const resetQuery = () => {
  queryParams.sectionCode = "";
  queryParams.riverCode = "";
  queryParams.isSet = "";
  queryParams.isControl = "";
  queryParams.pageNum = 1;
  getList();
};

// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

const convertDataFormat = (data) => {
  // 检查是否是数组
  if (!Array.isArray(data)) {
    return null;
  }
  // 对数组中的每个元素进行处理
  return data.map((item) => {
    // 复制 data 属性并移除 children
    const newData = { ...item.data };
    delete newData.children;
    // 递归处理子元素
    if (item.children && item.children.length > 0) {
      newData.children = convertDataFormat(item.children);
    }
    return newData;
  });
};

const isEdit = ref(false);

// 新增断面
const handleAdd = () => {
  dialog.title = "新增河道断面";
  isEdit.value = false;
  formData.value = {
    sectionCode: "",
    riverCode: "",
    riverName: "",
    distanceToRiverMouth: null,
    stcd: "",
    stnm: "",
    longitude: null,
    latitude: null,
    leftEmbankmentElevation: null,
    rightEmbankmentElevation: null,
    measurementDate: "",
    startingShore: 0, // 默认为左岸
    isSet: 0, // 是否控制断面，默认为否(0-否，1-是)
    subBasinId: "", // 控制小流域代码
    sectionFlow: {},
  };

  // 添加一个初始数据行
  sectionData.value = [
    {
      startingDistance: "",
      bottomElevation: "",
    },
  ];

  dialog.visible = true;
};

// 编辑断面
const handleEdit = async (row) => {
  isEdit.value = true;
  const res = await getRiverSectionInfo(row.id);
  if (res.code === 200) {
    formData.value = {
      ...res.data,
      sectionFlow: res.data?.sectionFlow || {},
    };
    sectionData.value = res.data?.riverSectionElevationList || [];

    dialog.title = "编辑河道断面 - " + row.sectionCode;
    dialog.visible = true;
  }
};

// 查看详情
const handleDetail = async (row) => {
  const res = await getRiverSectionInfo(row.id);
  if (res.code === 200) {
    detailData.value = res.data;
    detailData.value.sectionFlow = res.data?.sectionFlow || {};
    detailSectionData.value = res.data?.riverSectionElevationList || [];
    detailDialog.visible = true;
  }
};

// 删除断面
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除河道断面 ${row.sectionCode} 吗？`, "系统提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        const res = await deleteRiverSection(row.sectionCode);

        if (res.code === 200) {
          ElMessage.success("删除成功");
          getList();
        }
      } catch (error) {
        console.error("删除失败", error);
      }
    })
    .catch(() => {});
};

// 导入
const handleImport = () => {
  importDialog.visible = true;
};

// 处理导入成功
const handleImportSuccess = () => {
  getList();
};

// 处理表单保存成功
const handleFormSuccess = () => {
  getList();
};

const getRiverList = async () => {
  const res = await selectRvList({
    pageNum: 1,
    pageSize: 999,
  });
  if (res.code === 200) {
    riverOptions.value = convertDataFormat(res.data || []);
  }
};

// 生命周期钩子
onMounted(() => {
  getRiverList();
  getList();
});
</script>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="河道断面代码">
        <el-input
          v-model="queryParams.sectionCode"
          placeholder="请输入河道断面代码"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="所属河流">
        <el-select
          v-model="queryParams.riverCode"
          filterable
          placeholder="请选择所属河流"
          clearable
        >
          <el-option
            v-for="item in riverOptions"
            :key="item.id"
            :label="item.rvName"
            :value="item.rvCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设置测站">
        <el-select v-model="queryParams.isSet" placeholder="请选择" clearable>
          <el-option label="是" :value="1"></el-option>
          <el-option label="否" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="控制断面">
        <el-select
          v-model="queryParams.isControl"
          placeholder="请选择"
          clearable
        >
          <el-option label="是" :value="0"></el-option>
          <el-option label="否" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-btn">
        <el-button type="primary" @click="handleQuery" icon="Search"
          >查询</el-button
        >
        <el-button @click="resetQuery" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="content content-table">
      <div class="table-header">
        <el-button type="primary" icon="Plus" @click="handleAdd"
          >新增</el-button
        >
        <el-button type="info" plain icon="Upload" @click="handleImport"
          >导入</el-button
        >
      </div>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableList" stripe height="100%">
        <el-table-column label="序号" width="60" type="index"></el-table-column>
        <el-table-column label="河道断面代码" prop="sectionCode" />
        <el-table-column label="所属河流" prop="riverName" />
        <el-table-column label="距河口距离(km)" prop="distanceToRiverMouth" />
        <el-table-column label="河底高程(m)" prop="bottomElevation" />
        <el-table-column label="左堤高程(m)" prop="leftEmbankmentElevation" />
        <el-table-column label="右堤高程(m)" prop="rightEmbankmentElevation" />
        <el-table-column label="监测站点" prop="stnm" />
        <el-table-column label="操作" width="240" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              icon="View"
              link
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              icon="Edit"
              link
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              icon="Delete"
              link
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <RiverSectionFormDialog
      v-model:visible="dialog.visible"
      :title="dialog.title"
      :form-data="formData"
      :section-data="sectionData"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 详情对话框 -->
    <RiverSectionDetailDialog
      v-model:visible="detailDialog.visible"
      :detail-data="detailData"
      :section-data="detailSectionData"
    />

    <!-- 导入对话框 -->
    <RiverSectionImportDialog
      v-model:visible="importDialog.visible"
      @success="handleImportSuccess"
    />
  </div>
</template>
