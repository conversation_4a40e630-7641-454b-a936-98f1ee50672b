export default {
  stationName: [
    { required: true, message: "测站名称不能为空", trigger: "blur" },
    { max: 15, message: "测站名称不能超过15个字", trigger: "blur" },
  ],
  stationCode: [
    { required: true, message: "测站编码不能为空", trigger: "blur" },
  ],
  district: [
    { required: true, message: "所在区县不能为空", trigger: "change" },
  ],
  location: [{ max: 25, message: "站址不能超过25个字", trigger: "blur" }],
  longitude: [
    {
      validator: (rule, value, callback) => {
        if (value && (parseFloat(value) < 73 || parseFloat(value) > 136)) {
          callback(new Error("经度范围为73~136"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  latitude: [
    {
      validator: (rule, value, callback) => {
        if (value && (parseFloat(value) < 3 || parseFloat(value) > 54)) {
          callback(new Error("纬度范围为3~54"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  datumName: [{ max: 8, message: "基面名称不能超过8个字", trigger: "blur" }],
};
