<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import moment from "moment";
import { getTypeLabel } from "@/utils";
import {
  getAdcdTree,
  fetchSafetyMonitoringStationList,
  fetchSafetyMonitoringStationInfo,
  saveSafetyMonitoringStation,
  fetchSafetyMonitoringStationPointList,
  deleteSafetyStation,
} from "@/api/watershed/ads";
import MinMap from "@/components/Map/plugins/drawTool";
import {
  MONITOR_ITEM_OPTIONS,
  MONITOR_POINT_TYPE_OPTIONS,
} from "@/views/system/geo/utils/enum";
import rules from "./utils/rules";

defineOptions({
  name: "SafetyMonitoringStation",
});

// 查询参数
const queryParams = ref({
  stationName: "", // 测站名称
  stationCode: "", // 测站编码
  district: "", // 行政区划
});

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);

// 分页参数
const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 监测点列表分页参数
const pointPagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

// 详情对话框
const detailDialog = ref(false);
const currentDetail = ref({});
const currentStationCode = ref("");

// 监测点列表
const monitorPointList = ref([]);
// 监测点列表加载状态
const monitorPointLoading = ref(false);

// 新增/编辑对话框
const formDialog = ref(false);
const formTitle = ref("");
const formType = ref("add"); // add-新增 edit-编辑

// 表单数据
const formData = ref({
  stationName: "", // 测站名称
  stationCode: "", // 测站编码
  district: "", // 所在区县
  location: "", // 站址
  longitude: null, // 经度
  latitude: null, // 纬度
  datumName: "", // 基面名称
  datumElevation: null, // 基面高程
  establishDate: "", // 建站年月
  reportStartDate: "", // 始报年月
});

// 表单引用
const formRef = ref(null);

// 获取列表数据
const getList = async () => {
  loading.value = true;

  const res = await fetchSafetyMonitoringStationList({
    pageNum: pagination.value.pageNum,
    pageSize: pagination.value.pageSize,
    ...queryParams.value,
  });
  tableData.value = res.rows;
  pagination.value.total = res.total;
  loading.value = false;
};

// 查询按钮
const handleQuery = () => {
  pagination.value.pageNum = 1;
  getList();
};

// 重置按钮
const handleReset = () => {
  queryParams.value = {
    stationName: "",
    stationCode: "",
    district: "",
  };
  handleQuery();
};

// 获取监测点列表
const getMonitorPointList = async () => {
  if (!currentStationCode.value) return;

  monitorPointLoading.value = true;
  const res = await fetchSafetyMonitoringStationPointList({
    stationCode: currentStationCode.value,
    pageNum: pointPagination.value.pageNum,
    pageSize: pointPagination.value.pageSize,
  });

  if (res.code === 200) {
    monitorPointList.value = res.rows || [];
    pointPagination.value.total = res.total || 0;
  }
  monitorPointLoading.value = false;
};

// 查看详情
const handleDetail = async (row) => {
  formTitle.value = "安全监测站 - " + row.stationName;
  const res = await fetchSafetyMonitoringStationInfo(row.id);
  if (res.code === 200) {
    currentDetail.value = res.data;
    currentStationCode.value = row.stationCode;
    // 重置监测点分页
    pointPagination.value.pageNum = 1;
    await getMonitorPointList();
    detailDialog.value = true;
  }
};

// 监测点分页变化
const handleMonitorPointPagination = () => {
  getMonitorPointList();
};

// 新增按钮
const handleAdd = () => {
  formTitle.value = "新增安全监测站";
  formType.value = "add";
  formDialog.value = true;
};

// 编辑按钮
const handleEdit = async (row) => {
  formTitle.value = "编辑安全监测站";
  formType.value = "edit";
  const res = await fetchSafetyMonitoringStationInfo(row.id);
  if (res.code === 200) {
    formData.value = res.data;
    formData.value.district = res.data.districtCode;
    formDialog.value = true;
  }
};

// 删除按钮
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确认要删除该安全监测站 - ${row.stationName}吗？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(async () => {
    const res = await deleteSafetyStation(row.id);
    if (res.code === 200) {
      ElMessage.success("删除成功");
      getList();
    }
  });
};

// 重置表单
const resetForm = () => {
  formData.value = {
    stationName: "",
    stationCode: "",
    district: "",
    location: "",
    longitude: null,
    latitude: null,
    datumName: "",
    datumElevation: null,
    establishDate: "",
    reportStartDate: "",
  };
  formRef.value && formRef.value.resetFields();
  monitorPointList.value = [];
};

// 提交表单
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (formType.value === "add") {
        const res = await saveSafetyMonitoringStation(formData.value);
        if (res.code === 200) {
          ElMessage.success("添加成功");
          formDialog.value = false;
          getList();
        }
      } else {
        const res = await saveSafetyMonitoringStation(formData.value);
        if (res.code === 200) {
          ElMessage.success("更新成功");
          formDialog.value = false;
          getList();
        }
      }
    }
  });
};

// 区县数据
const adcdOptions = ref([]);
// 获取树结构数据
const getTreeselect = () => {
  getAdcdTree().then((res) => {
    adcdOptions.value = res.data[0].children;
  });
};

/**
 * 根据监测项目枚举值字符串，返回label字符串
 * @param {string} itemsStr 逗号分隔的枚举值字符串
 * @returns {string} label字符串
 */
const getMonitorItemsLabel = (itemsStr) => {
  if (!itemsStr) return "";
  return itemsStr
    .split(",")
    .map((v) => getTypeLabel(v, MONITOR_ITEM_OPTIONS))
    .filter(Boolean)
    .join(", ");
};

onMounted(() => {
  getList();
  getTreeselect();
});
</script>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="测站名称" prop="stationName">
        <el-input
          v-model="queryParams.stationName"
          placeholder="请输入测站名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测站编码" prop="stationCode">
        <el-input
          v-model="queryParams.stationCode"
          placeholder="请输入测站编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行政区划" prop="district">
        <el-tree-select
          v-model="queryParams.district"
          :data="adcdOptions"
          clearable
          :props="{
            value: 'adcd',
            label: 'adnm',
            children: 'children',
          }"
          value-key="id"
          placeholder="请选择行政区划"
          check-strictly
        />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button @click="handleReset" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="content content-table">
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd"
          >新增</el-button
        >
      </div>
      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableData" stripe>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="stationName" label="测站名称" align="center" />
        <el-table-column prop="stationCode" label="测站编码" align="center" />
        <el-table-column prop="district" label="所在区县" align="center" />
        <el-table-column
          prop="sectionCount"
          label="监测断面数量"
          align="center"
        />
        <el-table-column prop="pointCount" label="监测点数量" align="center" />
        <el-table-column label="监测项目" align="center">
          <template #default="{ row }">
            {{ getMonitorItemsLabel(row.monitoringItems) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="View"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        v-model:page="pagination.pageNum"
        v-model:limit="pagination.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="formDialog"
      width="700px"
      append-to-body
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="安全监测站名称" prop="stationName">
              <el-input
                v-model="formData.stationName"
                placeholder="请输入安全监测站名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="安全监测站编码" prop="stationCode">
              <el-input
                v-model="formData.stationCode"
                placeholder="请输入安全监测站编码"
                :disabled="formType === 'edit'"
                :maxlength="18"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在区县" prop="district">
              <el-tree-select
                v-model="formData.district"
                :data="adcdOptions"
                clearable
                :props="{
                  value: 'adcd',
                  label: 'adnm',
                  children: 'children',
                }"
                value-key="adcd"
                auto-expand-parent
                placeholder="请选择所在区县"
                check-strictly
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站址" prop="location">
              <el-input v-model="formData.location" placeholder="请输入站址" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number
                v-model="formData.longitude"
                placeholder="请输入经度"
                :precision="6"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number
                v-model="formData.latitude"
                placeholder="请输入纬度"
                :precision="6"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="基面名称" prop="datumName">
              <el-input
                v-model="formData.datumName"
                placeholder="请输入基面名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基面高程(m)" prop="datumElevation">
              <el-input-number
                v-model="formData.datumElevation"
                placeholder="请输入基面高程"
                :precision="3"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="建站年月" prop="establishDate">
              <el-date-picker
                v-model="formData.establishDate"
                type="month"
                placeholder="请选择建站年月"
                value-format="YYYY-MM-01"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="始报年月" prop="reportStartDate">
              <el-date-picker
                v-model="formData.reportStartDate"
                type="month"
                placeholder="请选择始报年月"
                value-format="YYYY-MM-01"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="formDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="detailDialog"
      width="800px"
      append-to-body
      class="detail-dialog"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="安全监测站名称">{{
          currentDetail.stationName
        }}</el-descriptions-item>
        <el-descriptions-item label="安全监测站编码">{{
          currentDetail.stationCode
        }}</el-descriptions-item>
        <el-descriptions-item label="所在区县">{{
          currentDetail.districtName
        }}</el-descriptions-item>
        <el-descriptions-item label="站址">{{
          currentDetail.location || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="基面名称">{{
          currentDetail.datumName || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="基面高程(m)">{{
          currentDetail.datumElevation || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="建站年月">{{
          currentDetail.establishDate
            ? moment(currentDetail.establishDate).format("YYYY-MM")
            : "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="始报年月">{{
          currentDetail.reportStartDate
            ? moment(currentDetail.reportStartDate).format("YYYY-MM")
            : "--"
        }}</el-descriptions-item>
      </el-descriptions>

      <!-- 空间信息预览 -->
      <div class="location-preview">
        <h3>空间信息预览</h3>
        <div class="map-container">
          <MinMap
            :points="[currentDetail.longitude, currentDetail.latitude]"
            class="w-full h-full"
          />
        </div>
      </div>

      <!-- 监测点列表 -->
      <div class="monitor-points-section">
        <h3>监测点列表</h3>
        <el-table
          :data="monitorPointList"
          border
          class="w-full"
          v-loading="monitorPointLoading"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column prop="sectionName" label="断面名称" align="center" />
          <el-table-column prop="sectionCode" label="断面编码" align="center" />
          <el-table-column prop="pointType" label="测点类型" align="center">
            <template #default="scope">
              {{
                getTypeLabel(scope.row.pointType, MONITOR_POINT_TYPE_OPTIONS)
              }}
            </template>
          </el-table-column>
          <el-table-column prop="pointCode" label="测点编码" align="center" />
          <el-table-column label="监测项目" align="center">
            <template #default="scope">
              {{ getTypeLabel(scope.row.monitorItem, MONITOR_ITEM_OPTIONS) }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceCode" label="监测设备" align="center" />
        </el-table>

        <!-- 监测点列表分页 -->
        <Pagination
          :total="pointPagination.total"
          v-model:page="pointPagination.pageNum"
          v-model:limit="pointPagination.pageSize"
          @pagination="handleMonitorPointPagination"
        />
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.table-container {
  height: calc(100vh - 290px);
  margin-top: 10px;
}

.detail-dialog {
  :deep(.el-dialog__body) {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px 24px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
      border-radius: 3px;
    }
  }

  .map-container {
    height: 300px;
  }

  .location-preview,
  .monitor-points-section {
    margin-top: 20px;

    h3 {
      font-size: 16px;
      margin-bottom: 15px;
      font-weight: 500;
    }
  }

  .monitor-points-section {
    .el-pagination {
      margin-top: 15px;
      justify-content: flex-end;
    }
  }
}
</style>
