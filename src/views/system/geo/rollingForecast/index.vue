<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="预报方案" prop="schemeId">
        <el-select
          v-model="queryParams.schemeId"
          placeholder="请选择预报方案"
          clearable
          filterable
          style="width: 200px"

        >
          <el-option
            v-for="item in forecastSchemeOptions"
            :key="item.id"
            :label="item.schemeName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="运行状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择运行状态"
          clearable
          style="width: 200px"

        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>


      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
            <!-- <el-button type="success" icon="DocumentAdd" @click="importDialogVisible = true" style="margin-left: 8px;">导入</el-button> -->
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </div>
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="rainfallList"
        row-key="id"
        stripe
      >
        <el-table-column
          type="index"
          label="序号"
          width="65"
          align="center"
          :index="indexMethod"
        ></el-table-column>
        <el-table-column
          prop="schemeName"
          label="预报方案"
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="latestForecastTime"
          label="最新预报时间"
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="nextForecastTime"
          label="下次预报时间"
          align="center"
        ></el-table-column>
        <el-table-column prop="warmupPeriod" label="预热期（小时）" align="center"></el-table-column>
        <el-table-column prop="forecastPeriod" label="预见期（小时）" align="center"></el-table-column>
        <el-table-column label="运行状态" align="center">
          <template #default="scope">
            <span class="apple-glass-tag" :class="`apple-glass-tag--${mainStatusMap[scope.row.status]?.type || 'unknown'}`">
              <span v-if="scope.row.status === 0" class="tag-icon pending-animate">
                <svg viewBox="0 0 24 24" width="16" height="16" fill="none"><circle cx="12" cy="12" r="10" stroke="#B45309" stroke-width="4" fill="#fff" opacity="0.9"/><path d="M12 6v6l4 2" stroke="#B45309" stroke-width="2.2" stroke-linecap="round"/></svg>
              </span>
              <span v-else-if="scope.row.status === 1" class="tag-icon spinner-rotate">
                <svg viewBox="0 0 24 24" width="16" height="16" fill="none"><circle cx="12" cy="12" r="10" stroke="#fff" stroke-width="4" fill="none" stroke-dasharray="60" stroke-dashoffset="30"></circle></svg>
              </span>
              <span v-else-if="scope.row.status === 2" class="tag-icon expired-icon">
                <svg viewBox="0 0 24 24" width="16" height="16" fill="none"><path d="M12 2v2m-8 8H2m4.314-5.686L4.9 5.9m12.786 0l1.414 1.414M22 12h-2m-3 7.071l-1.414 1.414M5.686 17.686L4.272 19.1M12 20v2m-2-10h4" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
              </span>
              <span class="tag-text">{{ mainStatusMap[scope.row.status]?.text || '未知' }}</span>
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="280"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="View"
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 新增弹窗：新增预报任务 -->
    <el-dialog
      :title="dialogTitle"
      v-model="addDialogVisible"
      width="500px"
      append-to-body
    >
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="140px">
        <el-form-item label="预报方案" prop="schemeId" required>
          <el-select
            v-model="addForm.schemeId"
            placeholder="请选择预报方案"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in forecastSchemeOptions"
              :key="item.id"
              :label="item.schemeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime" required>
          <el-date-picker
            v-model="addForm.forecastStartTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm"
            format="YYYY-MM-DD HH:mm"
            :disabled-date="disabledDate"
            :default-value="defaultStartTime"
            placeholder="请选择开始时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="延迟时间(分)" prop="delayTime">
          <el-input-number v-model="addForm.delayTime" :min="0" :step="1" :precision="0" placeholder="请输入延迟时间" style="width: 100%" />
        </el-form-item>
        <el-form-item label="计算步长(分)" prop="calculationStep" required>
          <el-select v-model="addForm.calculationStep" placeholder="请选择步长" style="width: 100%">
            <el-option v-for="item in stepOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="预热期(小时)" prop="warmupPeriod">
          <el-input-number
            v-model="addForm.warmupPeriod"
            :min="0"
            :max="360"
            :step="1"
            :precision="0"
            placeholder="请输入预热期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="预见期(小时)" prop="forecastPeriod" required>
          <el-input-number
            v-model="addForm.forecastPeriod"
            :min="0"
            :max="360"
            :step="1"
            :precision="0"
            placeholder="请输入预见期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="计算间隔(小时)" prop="calculationInterval">
          <el-input-number
            v-model="addForm.calculationInterval"
            :min="1"
            :max="360"
            :step="1"
            :precision="0"
            placeholder="请输入计算间隔"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="forecastEndTime">
          <el-date-picker
            v-model="addForm.forecastEndTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm"
            format="YYYY-MM-DD HH:mm"
            :disabled-date="disabledEndDate"
            placeholder="请选择结束时间"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增：查看详情弹窗 -->
    <el-dialog
      :title="viewDialogTitle"
      v-model="viewDialogVisible"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="预报方案">{{ viewDetail.forecastSchemeName }}</el-descriptions-item>
        <el-descriptions-item label="开始时间（分）">{{ viewDetail.startTime }}</el-descriptions-item>
        <el-descriptions-item label="延迟时间（分）">{{ viewDetail.delayTime }}</el-descriptions-item>
        <el-descriptions-item label="计算步长（分）">{{ viewDetail.step }}</el-descriptions-item>
        <el-descriptions-item label="预热期（小时）">{{ viewDetail.preheatPeriod }}</el-descriptions-item>
        <el-descriptions-item label="预见期（小时）">{{ viewDetail.forecastPeriod }}</el-descriptions-item>
        <el-descriptions-item label="计算间隔（小时）">{{ viewDetail.interval }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ viewDetail.endTime }}</el-descriptions-item>
      </el-descriptions>
      <div style="margin: 20px 0 0 0;">
        <el-table
          :data="viewTableDataSorted"
          style="width: 100%; margin-top: 20px;"
          height="300"
          border
        >
          <el-table-column type="index" label="序号" width="60" align="center"/>
          <el-table-column prop="name" label="成果名称" align="center"/>
          <el-table-column prop="forecastTime" label="预报时间" align="center"/>
          <el-table-column prop="completeTime" label="计算完成时间" align="center"/>
          <el-table-column label="成果状态" align="center">
            <template #default="{ row }">
              <span
                class="apple-glass-tag"
                :class="`apple-glass-tag--${statusMap[row.status]?.type || 'unknown'}`"
              >
                <span v-if="row.status === 0" class="tag-icon pencil-icon">
                  <svg viewBox="0 0 24 24" width="16" height="16"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z" fill="#6B7280"/></svg>
                </span>
                <span v-else-if="row.status === 1" class="tag-icon doing-pulse">
                  <span class="dot"></span><span class="dot"></span><span class="dot"></span>
                </span>
                <span v-else-if="row.status === 2" class="tag-icon done-animate">
                  <svg viewBox="0 0 24 24" width="16" height="16" fill="none"><circle cx="12" cy="12" r="12" fill="#4ADE80"/><path d="M7 13l3 3 7-7" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
                </span>
                <span v-else-if="row.status === 3" class="tag-icon failed-shake">
                  <svg viewBox="0 0 24 24" width="16" height="16" fill="none"><circle cx="12" cy="12" r="12" fill="#EF4444"/><path d="M15 9l-6 6M9 9l6 6" stroke="#fff" stroke-width="2" stroke-linecap="round"/></svg>
                </span>
                <span v-else-if="row.status === 4" class="tag-icon published-rotate">
                  <svg viewBox="0 0 24 24" width="16" height="16" fill="none"><path d="M4 12h16m-7-8l7 8-7 8" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
                </span>
                <span class="tag-text">{{ statusMap[row.status]?.text || row.status }}</span>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, nextTick, reactive, ref, watch, computed} from "vue";
import {
  selectStaList,
  selectStlyList,
} from "@/api/watershed/ads";
import * as echarts from 'echarts';
import { getRollingForecastTaskList, saveOrUpdateRollingForecastTask, deleteRollingForecastTask, getRollingForecastTaskDetail } from '@/api/watershed/forecast/index.js'

import { getForecastSchemeList } from '@/api/scheduling/index'
import moment from 'moment'


defineOptions({
  name: "TownshipIndex",
});

const { proxy } = getCurrentInstance();
const rainfallList = ref([])
const total = ref(0)
const loading = ref(false);
const showSearch = ref(true);
const refreshTable = ref(true);
const uploadUrl = import.meta.env.VITE_APP_BASE_API + "/hydro/convert/shp-kml/";
import { getToken } from "@/utils/auth";
const token = getToken();

const data = reactive({
  total: 0,
  form: {
    geoType: "people",
  },
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    schemeId: undefined,
    status: 1,
  },
  rules: {
    parentAdcd: [
      { required: true, message: "所属县区不能为空", trigger: "blur" },
    ],
    adcd: [
      { required: true, message: "乡镇代码不能为空", trigger: "blur" },
      {
        pattern: /^\d{12}$/,
        message: "乡镇代码必须为12位数字",
        trigger: ["blur", "change"],
      },
    ],
    adnm: [{ required: true, message: "乡镇名称不能为空", trigger: "blur" }],
    area: [
      {
        pattern: /^\d+(\.\d+)?$/,
        message: "请输入正确的数字格式",
        trigger: ["blur", "change"],
      },
    ],
  },
  statusOptions: [
    { label: '运行中', value: 1 },
    { label: '未开始', value: 0 },
    { label: '已过期', value: 2 }
  ],
});
const forecastSchemeOptions = ref([])
const { queryParams,statusOptions } = toRefs(data);

const subBasinOptions = ref([]);
const stationOptions = ref([]);

function indexMethod(index) {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
}

// 递归处理流域树结构，将data字段提升到节点本身
function transformBasinTree(tree) {
  return (tree || []).map(node => {
    const { data, children } = node;
    return {
      ...data,
      children: transformBasinTree(children)
    };
  });
}

async function initOptions() {
  // 流域
  const basinRes = await selectStlyList({});
  subBasinOptions.value = transformBasinTree(basinRes.data || []);
  // 雨量站
  const stationRes = await selectStaList({ asNext: 1, pageNum: 1, pageSize: 999999, sttp: 'PP' });
  stationOptions.value = (stationRes.data?.records || []).map(item => ({ code: item.stcd, name: `${item.stnm}(${item.stcd})` }));
}

// 添加 intervalRef 和 silentUpdate
let intervalRef = null
async function silentUpdate() {
  try {
    const res = await getRollingForecastTaskList(queryParams.value)
    const newList = res.rows || []
    const newTotal = res.total || 0
    const idMap = new Map(rainfallList.value.map(item => [item.id, item]))
    newList.forEach(newItem => {
      const existing = idMap.get(newItem.id)
      if (existing) {
        if (existing.status !== newItem.status) {
          Object.assign(existing, newItem)
        }
      } else {
        rainfallList.value.unshift(newItem)
      }
    })
    total.value = newTotal
  } catch (e) {
    console.error('更新失败', e)
  }
}

onMounted(async () => {
  await getList()
  await loadForecastSchemeOptions()
  intervalRef = setInterval(silentUpdate, 60000)
});

onUnmounted(() => {
  if (intervalRef) clearInterval(intervalRef)
})

function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除该滚动预报任务?')
    .then(() => deleteRollingForecastTask(row.id))
    .then(() => {
      proxy.$modal.msgSuccess('删除成功')
      getList()
    })
    .catch(() => {})
}


/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
async function handleAdd() {
  dialogMode.value = 'add'
  addForm.id = undefined
  addForm.schemeId = undefined
  addForm.forecastStartTime = getDefaultStartTime()
  addForm.delayTime = 10
  addForm.calculationStep = 60
  addForm.warmupPeriod = 24
  addForm.forecastPeriod = 24
  addForm.calculationInterval = 1
  addForm.forecastEndTime = ''
  addDialogVisible.value = true
}
/** 修改按钮操作 */
async function handleUpdate(row) {
  dialogMode.value = 'edit'
  const res = await getRollingForecastTaskDetail(row.id)
  const detail = res.data
  addForm.id = detail.id
  addForm.schemeId = detail.schemeId
  addForm.forecastStartTime = detail.forecastStartTime
  addForm.delayTime = detail.delayTime || 10
  addForm.calculationStep = detail.calculationStep || 60
  addForm.warmupPeriod = detail.warmupPeriod || 24
  addForm.forecastPeriod = detail.forecastPeriod || 24
  addForm.calculationInterval = detail.calculationInterval || 1
  addForm.forecastEndTime = detail.forecastEndTime
  taskName.value = detail.schemeName || ''
  addDialogVisible.value = true
}


const getList = async () => {
  loading.value = true
  try {
    const res = await getRollingForecastTaskList(queryParams.value)
    rainfallList.value = res.rows || []
    total.value = res.total || 0
  } catch (e) {
    proxy.$message.error('获取列表失败')
  } finally {
    loading.value = false
  }
}
const rainfallWeightForm = ref({
  adcd: "",
  adnm: "",
  stations: [
    {
      stationId: "",
      stationName: "",
      weight: null,
    },
  ],
});

// 存储初始表单数据，用于重置操作
const originalRainfallWeightForm = ref({
  adcd: "",
  adnm: "",
  stations: [
    {
      stationId: "",
      stationName: "",
      weight: null,
    },
  ],
});

const rainfallChartRef = ref(null);
let rainfallChartInstance = null;

function updateRainfallChart() {
  if (!rainfallChartInstance) return;
  // 图表数据正序排列
  const chartData = [...rainfallList.value].sort((a, b) => a.time.localeCompare(b.time));
  rainfallChartInstance.setOption({
    tooltip: { trigger: 'axis' },
    grid: { left: 50, right: 50, top: 30, bottom: 40 },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.time),
      axisLabel: { rotate: 0, fontSize: 12, margin: 16, interval: 'auto' },
      name: '时间',
    },
    yAxis: {
      type: 'value',
      name: '降雨量(mm)',
      min: 0,
    },
    series: [
      {
        name: '降雨量',
        type: 'line',
        data: chartData.map(item => item.rainfall),
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: { width: 2 },
        itemStyle: { color: '#409EFF' },
      },
    ],
  });
}

watch(rainfallList, updateRainfallChart, { deep: true });

const addDialogVisible = ref(false)
const addFormRef = ref()
const addForm = reactive({
  id: undefined,
  schemeId: undefined,
  startTime: getDefaultStartTime(),
  step: 60,
  preheatPeriod: 24,
  forecastPeriod: 24,
  interval: 1,
  endTime: ''
})
const addRules = {
  schemeId: [ { required: true, message: '请选择预报方案', trigger: 'change' } ],
  startTime: [ { required: true, message: '请选择开始时间', trigger: 'change' } ],
  step: [ { required: true, message: '请选择计算步长', trigger: 'change' } ],
  preheatPeriod: [
    { type: 'number', min: 0, max: 360, message: '预热期范围0-360', trigger: 'change' }
  ],
  forecastPeriod: [
    { required: true, message: '请输入预见期', trigger: 'change' },
    { type: 'number', min: 0, max: 360, message: '预见期范围0-360', trigger: 'change' }
  ],
  interval: [
    { type: 'number', min: 1, max: 360, message: '计算间隔范围1-360', trigger: 'change' }
  ],
  endTime: [],
  delayTime: [{ type: 'number', min: 0, message: '延迟时间不能小于0', trigger: 'change' }]
}
const stepOptions = [5, 10, 30, 60, 120, 180]
function getDefaultStartTime() {
  // 返回当前最近的整点时间
  const now = moment()
  return now.minute() === 0 ? now.format('YYYY-MM-DD HH:00') : now.add(1, 'hour').startOf('hour').format('YYYY-MM-DD HH:00')
}
const defaultStartTime = moment(getDefaultStartTime(), 'YYYY-MM-DD HH:mm').toDate()
// 更新 disabledDate
function disabledDate(time) {
  const now = moment().startOf('minute').valueOf()
  const oneYearLater = moment().add(1, 'year').endOf('day').valueOf()
  return time.getTime() < now || time.getTime() > oneYearLater
}
// 更新 disabledEndDate
function disabledEndDate(time) {
  const now = moment().startOf('minute').valueOf()
  const oneYearLater = moment().add(1, 'year').endOf('day').valueOf()
  return time.getTime() < now || time.getTime() > oneYearLater
}

// 更新 loadForecastSchemeOptions
async function loadForecastSchemeOptions() {
  try {
    const res = await getForecastSchemeList({ pageNum: 1, pageSize: 9999 });
    if (res && Array.isArray(res.rows)) {
      forecastSchemeOptions.value = res.rows;
    }
  } catch (e) {
    proxy.$message.error('获取预报方案失败');
  }
}

// 弹窗模式与标题
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const taskName = ref('')
const dialogTitle = computed(() => {
  if (dialogMode.value === 'edit') {
    return taskName.value || '编辑滚动预报任务'
  }
  return '新增滚动预报任务'
})

// 查看详情弹窗相关
const viewDialogVisible = ref(false)
const viewDetail = reactive({
  forecastSchemeName: '',
  startTime: '',
  delayTime: '',
  step: '',
  preheatPeriod: '',
  forecastPeriod: '',
  interval: '',
  endTime: ''
})
const viewDialogTitle = computed(() => viewDetail.forecastSchemeName ? `滚动预报任务详情 - ${viewDetail.forecastSchemeName}` : '滚动预报任务详情')
// mock成果表格数据
const viewTableData = ref([
  { id: 1, name: '成果A', forecastTime: '2024-06-10 10:00', finishTime: '2024-06-10 11:00', status: 0 },
  { id: 2, name: '成果B', forecastTime: '2024-06-09 10:00', finishTime: '2024-06-09 11:00', status: 1 },
  { id: 3, name: '成果C', forecastTime: '2024-06-08 10:00', finishTime: '', status: 2 },
  { id: 4, name: '成果D', forecastTime: '2024-06-07 10:00', finishTime: '', status: 3 }
])
const viewTableDataSorted = computed(() => {
  return [...viewTableData.value].sort((a, b) => b.forecastTime.localeCompare(a.forecastTime))
})
// 更新 statusMap
const statusMap = {
  0: { text: '草稿', type: 'draft' },
  1: { text: '计算中', type: 'calculating' },
  2: { text: '已完成', type: 'done' },
  3: { text: '计算失败', type: 'failed' },
  4: { text: '已发布', type: 'published' }
}

// 添加 mainStatusMap
const mainStatusMap = {
  0: { text: '未开始', type: 'pending' },
  1: { text: '运行中', type: 'running' },
  2: { text: '已过期', type: 'expired' }
}

// 查看按钮逻辑
async function handleView(row) {
  try {
    const res = await getRollingForecastTaskDetail(row.id)
    const detail = res.data
    viewDetail.forecastSchemeName = detail.schemeName
    viewDetail.startTime = detail.forecastStartTime
    viewDetail.delayTime = detail.delayTime
    viewDetail.step = detail.calculationStep
    viewDetail.preheatPeriod = detail.warmupPeriod
    viewDetail.forecastPeriod = detail.forecastPeriod
    viewDetail.interval = detail.calculationInterval
    viewDetail.endTime = detail.forecastEndTime
    viewTableData.value = detail.rollingForecastResultVOList || []
    viewDialogVisible.value = true
  } catch (e) {
    proxy.$message.error('获取详情失败')
  }
}

function tagBounce(e) {
  const el = e.currentTarget;
  el.classList.remove('bounce');
  void el.offsetWidth;
  el.classList.add('bounce');
}

async function handleSave() {
  await addFormRef.value.validate()
  try {
    await saveOrUpdateRollingForecastTask(addForm)
    proxy.$modal.msgSuccess(dialogMode.value === 'add' ? '新增成功' : '编辑成功')
    addDialogVisible.value = false
    getList()
  } catch (e) {
    proxy.$message.error('保存失败')
  }
}
</script>

<style scoped>
.rainfall-flex-container {
display: flex;
flex-direction: row;
width: 100%;
height: 420px;
min-height: 320px;
margin-top: 10px;
}
.rainfall-table-panel {
flex: 1;
min-width: 0;
background: #fff;
border-radius: 6px 0 0 6px;
box-shadow: 0 2px 8px rgba(0,0,0,0.04);
padding: 16px 8px 16px 16px;
display: flex;
flex-direction: column;
justify-content: flex-start;
}
.rainfall-divider {
width: 2px;
background: #e5e6eb;
margin: 0 8px;
border-radius: 2px;
height: 100%;
align-self: stretch;
}
.rainfall-chart-panel {
flex: 1;
min-width: 0;
background: #fff;
border-radius: 0 6px 6px 0;
box-shadow: 0 2px 8px rgba(0,0,0,0.04);
padding: 16px 16px 16px 8px;
display: flex;
flex-direction: column;
justify-content: flex-start;
}
.rainfall-echart {
width: 100%;
height: 100%;
min-height: 320px;
}
.apple-glass-tag {
  display: inline-flex;
  align-items: center;
  min-width: 80px;
  padding: 0 12px;
  height: 28px;
  line-height: 26px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 999px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  color: #fff;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10);
  background: linear-gradient(90deg, #4ADE80 0%, #22D3EE 100%);
  user-select: none;
  cursor: default;
  position: relative;
  overflow: hidden;
}
.apple-glass-tag--draft { background: linear-gradient(90deg, #9CA3AF 0%, #6B7280 100%); color: #fff; }
.apple-glass-tag--calculating { background: linear-gradient(90deg, #60A5FA 0%, #2563EB 100%); color: #fff; }
.apple-glass-tag--done { background: linear-gradient(90deg, #4ADE80 0%, #22D3EE 100%); color: #fff; }
.apple-glass-tag--failed { background: linear-gradient(90deg, #EF4444 0%, #B91C1C 100%); color: #fff; }
.apple-glass-tag--published { background: linear-gradient(90deg, #A855F7 0%, #7C3AED 100%); color: #fff; }
.apple-glass-tag--pending { background: linear-gradient(90deg, #FBBF24 0%, #F59E42 100%); color: #fff; }
.apple-glass-tag--running { background: linear-gradient(90deg, #60A5FA 0%, #2563EB 100%); color: #fff; }
.apple-glass-tag--expired { background: linear-gradient(90deg, #9CA3AF 0%, #6B7280 100%); color: #fff; }
.tag-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}
.tag-text {
  display: inline-block;
  vertical-align: middle;
}
.done-animate svg {
  animation: done-bounce 1.8s infinite cubic-bezier(.4,2,.2,1);
}
@keyframes done-bounce {
  0%, 100% { transform: scale(1);}
  10% { transform: scale(1.1);}
  20% { transform: scale(0.95);}
  30% { transform: scale(1);}
}
.doing-pulse {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 20px;
}
.doing-pulse .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #60A5FA;
  opacity: 0.5;
  animation: dot-pulse 1.5s infinite cubic-bezier(.4,0,.2,1);
}
.doing-pulse .dot:nth-child(2) {
  animation-delay: 0.3s;
}
.doing-pulse .dot:nth-child(3) {
  animation-delay: 0.6s;
}
@keyframes dot-pulse {
  0%, 100% { opacity: 0.5; transform: scale(1);}
  50% { opacity: 1; transform: scale(1.3);}
}
.pending-animate svg {
  animation: pending-breath 2.2s ease-in-out infinite;
}
@keyframes pending-breath {
  0%, 100% { opacity: 0.7;}
  50% { opacity: 1;}
}
.tag-icon svg {
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.10));
}
.pencil-icon { opacity: 0.9; }
.failed-shake { animation: shake 0.5s infinite; }
@keyframes shake { 0%, 100% { transform: translateX(0); } 25% { transform: translateX(-2px); } 75% { transform: translateX(2px); } }
.published-rotate { animation: rotate 2s linear infinite; }
@keyframes rotate { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
.expired-icon { opacity: 0.9; }
.spinner-rotate svg { animation: rotate 3s linear infinite; }
</style>
