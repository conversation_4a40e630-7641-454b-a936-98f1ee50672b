<script setup>
import { getDamList, getDamInfo, saveDam, deleteDam } from "@/api/watershed";
import { selectRsList } from "@/api/watershed/ads";
import { getWaterGateList } from "@/api/waterGate";
import {
  ENGINEERING_TYPE_OPTIONS,
  ENGINEERING_LEVEL_OPTIONS,
  BUILDING_LEVEL_OPTIONS,
  WATER_BLOCKING_METHOD_OPTIONS,
  FILL_AND_DRAIN_METHOD_OPTIONS,
  FILL_AND_DRAIN_MEDIA_OPTIONS,
} from "../utils/enum";
import { deepClone, getTypeLabel } from "@/utils";
import MinMap from "@/components/Map/plugins/drawTool";

defineOptions({
  name: "DamMonitoring",
});

const { proxy } = getCurrentInstance();

const damList = ref([]);
const openVisible = ref(false);
const openDetail = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const title = ref("");
const refreshTable = ref(true);
const total = ref(0);
const detailForm = ref({});

const damRef = ref(null);
const form = ref({
  damName: "",
  damCode: "",
  projectType: "",
  projectCode: "",
  longitude: null,
  latitude: null,
  damHeight: null,
  damLength: null,
  crestElevation: null,
  projectGrade: "",
  buildingLevel: "",
  waterRetentionType: "",
  fillingDrainageType: "",
  inflationMedium: "",
  holeCount: null,
  singleSpanWidth: null,
  geom: null,
});
const queryRef = ref(null);
const queryParams = ref({
  pageNum: 1,
  pageSize: 20,
  name: "",
});
const rules = reactive({
  damCode: [{ required: true, message: "大坝代码不能为空", trigger: "blur" }],
  damName: [{ required: true, message: "大坝名称不能为空", trigger: "blur" }],
  projectType: [
    { required: true, message: "所属工程类型不能为空", trigger: "blur" },
  ],
  projectCode: [
    { required: true, message: "所属工程不能为空", trigger: "blur" },
  ],
});

const addFlag = ref(true);

// 所属工程
const projectOptions = ref([]);

/** 查询大坝列表 */
const getList = async () => {
  try {
    loading.value = true;
    const res = await getDamList(queryParams.value);
    if (res.code === 200) {
      damList.value = res.rows;
      total.value = res.total;
    }
  } catch (error) {
    console.error("获取大坝列表失败:", error);
  } finally {
    loading.value = false;
  }
};

/** 取消按钮 */
const cancel = () => {
  openVisible.value = false;
  reset();
};

/** 表单重置 */
const reset = () => {
  form.value = {
    damName: "",
    damCode: "",
    projectType: "",
    projectCode: "",
    longitude: null,
    latitude: null,
    damHeight: null,
    damLength: null,
    crestElevation: null,
    projectGrade: "",
    buildingLevel: "",
    waterRetentionType: "",
    fillingDrainageType: "",
    inflationMedium: "",
    holeCount: null,
    singleSpanWidth: null,
    geom: null,
  };
  damRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value?.resetFields();
  handleQuery();
};

/** 新增按钮操作 */
const handleAdd = () => {
  addFlag.value = true;
  reset();
  openVisible.value = true;
  title.value = "添加大坝";
};

/** 查看详情 */
const handleDetail = async (row) => {
  const res = await getDamInfo(row.id);
  if (res.code === 200) {
    detailForm.value = res.data;
    openDetail.value = true;
  }
};

/** 提交表单 */
const submitForm = () => {
  damRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        const res = await saveDam(form.value);
        if (res.code === 200) {
          proxy.$modal.msgSuccess(addFlag.value ? "新增成功" : "修改成功");
          openVisible.value = false;
          getList();
        }
      } catch (error) {
        console.error("保存失败:", error);
        proxy.$modal.msgError("保存失败");
      }
    }
  });
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  addFlag.value = false;
  reset();
  const res = await getDamInfo(row.id);
  if (res.code === 200) {
    form.value = res.data;
    updateProjectOptions();

    openVisible.value = true;
    title.value = "修改大坝";
  }
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.damName + '"的大坝?')
    .then(async () => {
      try {
        const res = await deleteDam(row.id);
        if (res.code === 200) {
          proxy.$modal.msgSuccess("删除成功");
          getList();
        }
      } catch (error) {
        console.error("删除失败:", error);
        proxy.$modal.msgError("删除失败");
      }
    });
};

/** 所属工程类型变化时重置所属工程 */
const handleProjectTypeChange = () => {
  form.value.projectCode = "";
  updateProjectOptions();
};

const updateProjectOptions = () => {
  if (form.value.projectType === 0) {
    projectOptions.value = deepClone(reservoirList.value);
  } else {
    projectOptions.value = deepClone(waterGateList.value);
  }
};

const reservoirList = ref([]);
// 获取水库列表
const getReservoirList = async () => {
  const res = await selectRsList({ pageNum: 1, pageSize: 1000 });

  if (res.code === 200) {
    reservoirList.value = res.data.records.map((item) => ({
      code: item.resCode,
      name: item.resName,
    }));
  }
};

const waterGateList = ref([]);
// 获取水闸列表
const fetchWaterGateList = async () => {
  const res = await getWaterGateList({ pageNum: 1, pageSize: 1000 });
  if (res.code === 200) {
    waterGateList.value = res.rows.map((item) => ({
      code: item.gateCode,
      name: item.gateName,
    }));
  }
};

// 初始化数据
onMounted(() => {
  getList();
  getReservoirList();
  fetchWaterGateList();
});
</script>

<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="大坝名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入大坝名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="Refresh" type="primary" plain @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd"
              >新增</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </div>
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="damList"
        row-key="id"
        stripe
      >
        <el-table-column type="index" label="序号" width="65" align="center" />
        <el-table-column
          prop="damName"
          label="大坝名称"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column prop="damCode" label="大坝代码" align="center" />
        <el-table-column
          prop="projectName"
          label="所属工程"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column prop="projectGrade" label="工程等别" align="center">
          <template #default="{ row }">
            {{ getTypeLabel(row.projectGrade, ENGINEERING_LEVEL_OPTIONS) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="waterRetentionType"
          label="挡水方式"
          :show-overflow-tooltip="true"
          align="center"
        >
          <template #default="{ row }">
            {{
              getTypeLabel(
                row.waterRetentionType,
                WATER_BLOCKING_METHOD_OPTIONS
              )
            }}
          </template>
        </el-table-column>
        <el-table-column prop="inflationMedium" label="充胀介质" align="center">
          <template #default="{ row }">
            {{
              getTypeLabel(row.inflationMedium, FILL_AND_DRAIN_MEDIA_OPTIONS)
            }}
          </template>
        </el-table-column>
        <el-table-column prop="holeCount" label="坝孔数量(孔)" align="center" />
        <el-table-column
          prop="singleSpanWidth"
          label="单跨宽度(m)"
          align="center"
        />
        <el-table-column
          label="操作"
          align="center"
          width="240"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="View"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改大坝对话框 -->
    <el-dialog
      :title="title"
      v-model="openVisible"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form ref="damRef" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="大坝名称" prop="damName">
              <el-input
                v-model="form.damName"
                placeholder="请输入大坝名称"
                maxlength="15"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="大坝代码" prop="damCode">
              <el-input
                :disabled="!addFlag"
                v-model="form.damCode"
                placeholder="请输入大坝代码"
                maxlength="18"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属工程类型" prop="projectType">
              <el-select
                v-model="form.projectType"
                placeholder="请选择所属工程类型"
                class="w-full"
                @change="handleProjectTypeChange"
              >
                <el-option
                  v-for="item in ENGINEERING_TYPE_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属工程" prop="projectCode">
              <el-select
                v-model="form.projectCode"
                placeholder="请选择所属工程"
                class="w-full"
                :disabled="form.projectType === ''"
              >
                <el-option
                  v-for="item in projectOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number
                v-model="form.longitude"
                placeholder="请输入经度"
                :min="73"
                :max="136"
                :precision="6"
                :step="0.1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number
                v-model="form.latitude"
                placeholder="请输入纬度"
                :min="3"
                :max="54"
                :precision="6"
                :step="0.1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="坝高(m)" prop="damHeight">
              <el-input-number
                v-model="form.damHeight"
                placeholder="请输入坝高"
                :min="0"
                :precision="2"
                :step="0.01"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="坝长(m)" prop="damLength">
              <el-input-number
                v-model="form.damLength"
                placeholder="请输入坝长"
                :min="0"
                :precision="2"
                :step="0.01"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="坝顶高程(m)" prop="crestElevation">
              <el-input-number
                v-model="form.crestElevation"
                placeholder="请输入坝顶高程"
                :min="0"
                :precision="2"
                :step="0.01"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程等别" prop="projectGrade">
              <el-select
                v-model="form.projectGrade"
                placeholder="请选择工程等别"
                class="w-full"
                clearable
              >
                <el-option
                  v-for="item in ENGINEERING_LEVEL_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="Number(item.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主要建筑物级别" prop="buildingLevel">
              <el-select
                v-model="form.buildingLevel"
                placeholder="请选择主要建筑物级别"
                class="w-full"
                clearable
              >
                <el-option
                  v-for="item in BUILDING_LEVEL_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="Number(item.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="挡水方式" prop="waterRetentionType">
              <el-select
                v-model="form.waterRetentionType"
                placeholder="请选择挡水方式"
                class="w-full"
                clearable
              >
                <el-option
                  v-for="item in WATER_BLOCKING_METHOD_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充排方式" prop="fillingDrainageType">
              <el-select
                v-model="form.fillingDrainageType"
                placeholder="请选择充排方式"
                class="w-full"
                clearable
              >
                <el-option
                  v-for="item in FILL_AND_DRAIN_METHOD_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充胀介质" prop="inflationMedium">
              <el-select
                v-model="form.inflationMedium"
                placeholder="请选择充胀介质"
                class="w-full"
                clearable
              >
                <el-option
                  v-for="item in FILL_AND_DRAIN_MEDIA_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="坝孔数量(孔)" prop="holeCount">
              <el-input-number
                v-model="form.holeCount"
                placeholder="请输入坝孔数量"
                :min="0"
                :precision="0"
                :step="1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单跨宽度(m)" prop="singleSpanWidth">
              <el-input-number
                v-model="form.singleSpanWidth"
                placeholder="请输入单跨宽度"
                :min="0"
                :precision="2"
                :step="0.01"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>

    <!-- 查看大坝详情对话框 -->
    <el-dialog
      title="大坝详情"
      v-model="openDetail"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="大坝名称">{{
          detailForm.damName
        }}</el-descriptions-item>
        <el-descriptions-item label="大坝代码">{{
          detailForm.damCode
        }}</el-descriptions-item>
        <el-descriptions-item label="所属工程类型">{{
          getTypeLabel(detailForm.projectType, ENGINEERING_TYPE_OPTIONS)
        }}</el-descriptions-item>
        <el-descriptions-item label="所属工程">{{
          detailForm.projectName
        }}</el-descriptions-item>
        <el-descriptions-item label="经度">{{
          detailForm.longitude
        }}</el-descriptions-item>
        <el-descriptions-item label="纬度">{{
          detailForm.latitude
        }}</el-descriptions-item>
        <el-descriptions-item label="坝高(m)">{{
          detailForm.damHeight
        }}</el-descriptions-item>
        <el-descriptions-item label="坝长(m)">{{
          detailForm.damLength
        }}</el-descriptions-item>
        <el-descriptions-item label="坝顶高程(m)">{{
          detailForm.crestElevation
        }}</el-descriptions-item>
        <el-descriptions-item label="工程等别">{{
          getTypeLabel(detailForm.projectGrade, ENGINEERING_LEVEL_OPTIONS)
        }}</el-descriptions-item>
        <el-descriptions-item label="主要建筑物级别">{{
          getTypeLabel(detailForm.buildingLevel, BUILDING_LEVEL_OPTIONS)
        }}</el-descriptions-item>
        <el-descriptions-item label="挡水方式">{{
          getTypeLabel(
            detailForm.waterRetentionType,
            WATER_BLOCKING_METHOD_OPTIONS
          )
        }}</el-descriptions-item>
        <el-descriptions-item label="充排方式">{{
          getTypeLabel(
            detailForm.fillingDrainageType,
            FILL_AND_DRAIN_METHOD_OPTIONS
          )
        }}</el-descriptions-item>
        <el-descriptions-item label="充胀介质">{{
          getTypeLabel(detailForm.inflationMedium, FILL_AND_DRAIN_MEDIA_OPTIONS)
        }}</el-descriptions-item>
        <el-descriptions-item label="坝孔数量(孔)">{{
          detailForm.holeCount
        }}</el-descriptions-item>
        <el-descriptions-item label="单跨宽度(m)">{{
          detailForm.singleSpanWidth
        }}</el-descriptions-item>
      </el-descriptions>
      <div class="min-map-container">
        <min-map
          :points="[detailForm.longitude, detailForm.latitude]"
          :showTool="false"
        />
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.min-map-container {
  width: 100%;
  height: 400px;
  background: #555;
  margin-top: 20px;
}
</style>
