<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="乡镇名称" prop="adnm">
        <el-input
          v-model="queryParams.adnm"
          placeholder="请输入乡镇名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="乡镇代码" prop="adcd">
        <el-input
          v-model="queryParams.adcd"
          placeholder="请输入乡镇代码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行政区划" prop="parentAdcd">
        <el-tree-select
          v-model="queryParams.parentAdcd"
          :data="adcdOptions"
          clearable
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
          value-key="adcd"
          placeholder="选择所属县区"
          check-strictly
        />
      </el-form-item>

      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd"
              >新增</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </div>
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="lyList"
        row-key="lycode"
        stripe
      >
        <el-table-column
          type="index"
          label="序号"
          width="65"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="adnm"
          label="乡镇名称"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="adcd"
          label="乡镇代码"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="parentName"
          label="所属区县"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="shortName"
          label="简称"
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="area"
          label="面积"
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="geom"
          label="空间数据"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span
              ><el-button
                type="success"
                size="small"
                plain
                @click="showMap(scope.row)"
                >预览</el-button
              ></span
            >
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="280"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Setting"
              @click="handleRainfallWeight(scope.row)"
              >设置降雨权重</el-button
            >
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>


    <!-- 添加或修改流域对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="720px"
      append-to-body
      @close="cancel"
    >
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="选择所属县区" prop="parentAdcd">
              <el-tree-select
                :disabled="!form.addFlag"
                v-model="form.parentAdcd"
                :data="adcdOptions"
                :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
                value-key="adcd"
                placeholder="选择所属县区"
                check-strictly
                @node-click="handleNodeClick"
                :render-after-expand="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="乡镇代码" prop="adcd">
              <el-input
                :disabled="!form.addFlag"
                v-model="form.adcd"
                placeholder="请输入乡镇代码"
                :formatter="
                  (value) => `${value}`.replace(/[^\d]/g, '').substring(0, 12)
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="乡镇名称" prop="adnm">
              <el-input v-model="form.adnm" placeholder="请输入乡镇名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="乡镇简称" prop="shortName">
              <el-input v-model="form.shortName" placeholder="请输入乡镇简称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="乡镇面积(km²)" prop="area">
              <el-input
                v-model="form.area"
                placeholder="请输入乡镇面积"
                :formatter="
                  (value) =>
                    `${value}`
                      .replace(/[^\d.]/g, '')
                      .replace(/^\./, '0.')
                      .replace(/\.{2,}/g, '.')
                      .replace(/^0+(\d)/, '$1')
                "
                @input="
                  (value) => {
                    if (value.split('.').length > 2)
                      form.area = value.substring(0, value.lastIndexOf('.'));
                  }
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="空间数据" prop="geoType">
              <el-radio-group v-model="form.geoType">
                <el-radio label="zip"
                  >矢量数据(shp-zip,kml,geojson文件)</el-radio
                >
                <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                <el-radio label="people">人工绘制</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <div
            style="width: 100%; height: 130px"
            v-show="form.geoType === 'zip'"
          >
            <el-upload
              class="upload-demo"
              style="width: 100%"
              ref="uploadRef"
              drag
              name="multipartFile"
              :on-change="handleChange"
              :action="uploadUrl"
              :data="{}"
              :headers="{ Authorization: token }"
              :limit="2"
              :on-success="handleSuccess"
            >
              <div class="el-upload__text">拖拽上传 或 <em>点击上传</em></div>
            </el-upload>
          </div>
          <div style="width: 100%; height: 350px; background: #555">
            <min-map
              @updateBound="updateBound"
              :geom="form.geom"
              :show-tool="form.geoType === 'people'"
              style="width: 100%; height: 100%"
            ></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      :title="curTitle"
      v-model="open2"
      width="920px"
      height="900px"
      append-to-body
    >
      <el-form ref="menuRef" label-width="130px" height="900px">
        <el-row>
          <div style="width: 100%; height: 600px; background: #555">
            <min-map
              :geom="curGeom"
              :showTool="false"
              style="width: 100%; height: 100%"
            ></min-map>
          </div>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open2 = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 降雨权重设置对话框 -->
    <el-dialog
      title="设置降雨权重"
      v-model="rainfallWeightOpen"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="cancelRainfallWeight"
      style="padding: 0 10px"
    >
      <div style="margin-bottom: 20px; font-size: 16px; font-weight: bold">
        {{ townName }}
      </div>
      <el-form
        ref="rainfallWeightRef"
        :model="rainfallWeightForm"
        :rules="rainfallWeightRules"
        label-width="120px"
      >
        <!-- <div class="text-gray-400 text-sm mb-2" style="color: #909399;">注：当前租户下共有 {{ stationOptions.length }} 个测站</div> -->
        <el-table
          :data="rainfallWeightForm.stations"
          border
          style="width: 100%"
          :show-summary="true"
          :summary-method="getSummaries"
          v-loading="stationLoading"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column label="测站" prop="stationName">
            <template #default="{ row }">
              <el-select
                v-model="row.stationId"
                placeholder="请选择测站"
                clearable
                style="width: 100%"
                @change="() => handleStationChange(row)"
                :disabled="!!row.stationId"
              >
                <el-option
                  v-for="item in stationOptions"
                  :key="item.stationId"
                  :label="item.stationName"
                  :value="item.stationId"
                  :disabled="
                    rainfallWeightForm.stations.some(
                      (station) => station.stationId === item.stationId
                    )
                  "
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="权重" prop="weight" width="200">
            <template #default="{ row }">
              <el-input-number
                :id="'weightInput' + row.stationId"
                v-model="row.weight"
                :min="0"
                :max="1"
                :precision="1"
                :step="0.1"
                style="width: 160px"
                placeholder="请输入权重"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center">
            <template #default="{ $index }">
              <el-button
                link
                type="primary"
                icon="Plus"
                @click="handleAddStation"
                v-if="$index === rainfallWeightForm.stations.length - 1"
              >
                添加
              </el-button>
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDeleteStation($index)"
                v-if="rainfallWeightForm.stations.length > 1"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="mt-2 text-right text-gray-500" style="margin-top: 10px">
          注：权重范围0-1，保留1位小数，所有测站权重之和必须为1
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRainfallWeight"
            >确 定</el-button
          >
          <el-button type="warning" @click="resetRainfallWeight"
            >重 置</el-button
          >
          <el-button @click="cancelRainfallWeight">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { onUnmounted, nextTick, reactive, computed, ref } from "vue";
import {
  selectTsList,
  addTsInfo,
  updateTsInfo,
  delTsInfo,
  getTsInfo,
  getAdcdTree,
  selectStaList,
  saveStationWeights,
  getStationWeights,
} from "@/api/watershed/ads";
import MinMap from "@/components/Map/plugins/drawTool";

defineOptions({
  name: "TownshipIndex",
});

const { proxy } = getCurrentInstance();
const uploadRef = ref("");

const totalWeight = computed(() => {
  return rainfallWeightForm.value.stations
    .reduce((sum, station) => {
      return sum + (Number(station.weight) || 0);
    }, 0)
    .toFixed(1);
});

const lyList = ref([]);
const open = ref(false);
const open2 = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const curGeom = ref("");
const curTitle = ref("");
const adcdOptions = ref([]); // 行政区树 默认获取一下
const refreshTable = ref(true);
const total = ref(0);
const uploadUrl = import.meta.env.VITE_APP_BASE_API + "/hydro/convert/shp-kml/";
import { getToken } from "@/utils/auth";
import { deepClone } from "@/utils";
const token = getToken();

const data = reactive({
  total: 0,
  form: {
    geoType: "people",
  },
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    // asNext: 1 // 加个asNext 在查询接口里 1是0否
  },
  rules: {
    parentAdcd: [
      { required: true, message: "所属县区不能为空", trigger: "blur" },
    ],
    adcd: [
      { required: true, message: "乡镇代码不能为空", trigger: "blur" },
      {
        pattern: /^\d{12}$/,
        message: "乡镇代码必须为12位数字",
        trigger: ["blur", "change"],
      },
    ],
    adnm: [{ required: true, message: "乡镇名称不能为空", trigger: "blur" }],
    area: [
      {
        pattern: /^\d+(\.\d+)?$/,
        message: "请输入正确的数字格式",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询流域列表 */
async function getList() {
  loading.value = true;
  selectTsList(queryParams.value).then((res) => {
    lyList.value = res.rows || [];
    loading.value = false;
    total.value = res.total || 0;
  });
}
getTreeselect();
/** 查询区县下拉树结构 */
function getTreeselect() {
  adcdOptions.value = [];
  getAdcdTree().then((res) => {
    adcdOptions.value = res.data[0].children;
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  uploadRef.value?.clearFiles();
  uploadRef.value?.handleRemove();
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    addFlag: flag,
    geoType: "people",
  };
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd() {
  reset(true);

  open.value = true;
  title.value = "添加乡镇";
}

function handleNodeClick(node) {
  // console.log(node.children);
  if (!node.children || node.children.length === 0) {
    // 只选中最后一个层级节点
    form.value.parentAdcd = node.adcd;
    // console.log(node);
    form.value.adcd = node.adcd.slice(0, 6);
  } else {
    // 清空选中
    // form.value.lyCode = '';
    proxy.$modal.msgError("请选择区县");
    nextTick(() => {
      form.value.parentAdcd = "";
      // console.log(form.value);
    });
  }
}

function hideMap() {
  open2.value = false;
}
async function showMap(row) {
  let res = await getTsInfo(row.adcd);
  if (res.data) {
    open2.value = true;
    await nextTick();
    curGeom.value = res.data.geom ? JSON.parse(res.data.geom) : "";
    curTitle.value = row.adnm;
  } else {
    proxy.$modal.msgSuccess("没找到空间数据");
  }
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate((valid) => {
    if (valid) {
      // console.log(form.value.geom);
      // console.log(form.value.geom == {});
      // console.log(JSON.stringify(form.value.geom) == {});
      if (
        form.value.geom == null ||
        form.value.geom == "" ||
        JSON.stringify(form.value.geom) == {}
      ) {
        form.value.geom = null;
      }
      if (form.value.addFlag) {
        addTsInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      } else {
        updateTsInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.adnm + '"的数据项?')
    .then(function () {
      return delTsInfo({ adcd: row.adcd });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset(false);
  const adcd = row.adcd;
  const res = await getTsInfo(adcd);
  if (res.code === 200) {
    form.value = {
      ...res.data,
      addFlag: false,
      geoType: res.data.geom ? "zip" : "people",
    };
    if (form.value.geom && typeof form.value.geom === "string") {
      form.value.geom = JSON.parse(form.value.geom);
    }
    open.value = true;
    title.value = "修改乡镇";
  }
}

function updateBound(geojson) {
  // 提交geojson数据到后台
  // console.log(geojson)
  form.value.geom = geojson;
}

function handleChange(file, fileList) {
  if (fileList.length > 1) {
    fileList[0] = fileList[1];
    fileList.splice(1, 1);
  }
}
function handleRemove(file) {
  form.value.geom = {};
}
function handleSuccess(response) {
  if (response.code == 200) {
    form.value.geom = response.data;
  } else {
    uploadRef.value?.clearFiles();
    proxy.$modal.msgError(response.msg);
  }
}

onMounted(() => {
  window.EventBus.$on("updateBound/update", updateBound);
  // getAdcdTree({ adcd: ''}).then((res) => {
  // getAdcdTreeByAccount({ adcd: '' }).then((res) => {
  //   adcdOptions.value = res.data
  // })
});
onUnmounted(() => {});

getList();
const townName = ref("");
const rainfallWeightOpen = ref(false);
const rainfallWeightForm = ref({
  adcd: "",
  adnm: "",
  stations: [
    {
      stationId: "",
      stationName: "",
      weight: null,
    },
  ],
});
const stationOptions = ref([]); // 雨量站选项
const rainfallWeightRules = {
  stations: {
    type: "array",
    required: true,
    trigger: "change",
    validator: (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error("请至少添加一个测站"));
        return;
      }

      // 获取有效的测站（已填写stationId和weight的测站）
      const validStations = value.filter(
        (station) =>
          station.stationId &&
          station.weight !== null &&
          station.weight !== undefined
      );

      // 检查是否有重复测站
      const stationIds = validStations.map((item) => item.stationId);
      if (new Set(stationIds).size !== stationIds.length) {
        callback(new Error("不能重复选择同一个测站"));
        return;
      }

      // 检查每个测站的必填项和权重范围
      for (const station of validStations) {
        if (!station.stationId) {
          callback(new Error("请选择测站"));
          return;
        }
        if (
          station.weight === null ||
          station.weight === undefined ||
          station.weight === ""
        ) {
          callback(new Error("请输入权重值"));
          return;
        }
        if (station.weight < 0 || station.weight > 1) {
          callback(new Error("权重值必须在0-1之间"));
          return;
        }
      }

      // 计算权重总和
      const totalWeight = validStations.reduce(
        (sum, station) => sum + (Number(station.weight) || 0),
        0
      );

      // 检查是否只有一个测站且权重不为1
      if (validStations.length === 1 && Math.abs(totalWeight - 1) > 0.001) {
        callback(new Error("只有一个测站时，权重必须为1"));
        return;
      }

      // 检查权重总和是否为1
      if (Math.abs(totalWeight - 1) > 0.001) {
        callback(new Error("所有测站权重之和必须为1"));
        return;
      }

      callback();
    },
  },
};

const stationLoading = ref(false);
// 存储初始表单数据，用于重置操作
const originalRainfallWeightForm = ref({
  adcd: "",
  adnm: "",
  stations: [
    {
      stationId: "",
      stationName: "",
      weight: null,
    },
  ],
});

/** 获取测站列表 */
async function getStationList() {
  try {
    stationLoading.value = true;
    const res = await selectStaList({
      pageNum: 1,
      pageSize: 99999,
    });
    if (res.data) {
      stationOptions.value = res.data.records.map((item) => ({
        stationId: item.stcd,
        stationName: `${item.stnm}(${item.stcd})`,
        type: item.sttp,
      }));
    }
  } catch (error) {
    console.error("获取测站列表失败:", error);
    proxy.$modal.msgError("获取测站列表失败");
  } finally {
    stationLoading.value = false;
  }
}

/** 打开降雨权重设置 */
async function handleRainfallWeight(row) {
  try {
    // 获取测站列表
    getStationList();

    // 获取已设置的权重数据
    const weightRes = await getStationWeights(row.adcd);
    townName.value = row.adnm + row.adcd;
    rainfallWeightOpen.value = true;

    // 构建表单数据
    const formData = {
      adcd: row.adcd,
      adnm: row.adnm,
      stations:
        weightRes.data && weightRes.data.length > 0
          ? weightRes.data.map((item) => ({
              stationId: item.stationCode,
              stationName:
                stationOptions.value.find(
                  (s) => s.stationId === item.stationCode
                )?.stationName || "",
              weight: item.weight,
            }))
          : [
              {
                stationId: "",
                stationName: "",
                weight: null,
              },
            ],
    };

    // 设置表单和保存初始数据（用于重置）
    originalRainfallWeightForm.value = deepClone(formData);
    rainfallWeightForm.value = formData;
  } catch (error) {
    console.error("获取数据失败:", error);
    proxy.$modal.msgError("获取数据失败");
  }
}

/** 测站选择变化 */
function handleStationChange(row) {
  const station = stationOptions.value.find(
    (item) => item.stationId === row.stationId
  );
  if (station) {
    row.stationName = station.stationName;
  } else {
    row.stationName = "";
  }
}

/** 添加雨量站行 */
function handleAddStation() {
  rainfallWeightForm.value.stations.push({
    stationId: "",
    stationName: "",
    weight: null,
  });
}

/** 删除雨量站行 */
function handleDeleteStation(index) {
  rainfallWeightForm.value.stations.splice(index, 1);
}

/** 取消降雨权重设置 */
function cancelRainfallWeight() {
  rainfallWeightOpen.value = false;
  rainfallWeightForm.value = {
    adcd: "",
    adnm: "",
    stations: [
      {
        stationId: "",
        stationName: "",
        weight: null,
      },
    ],
  };
}

/** 提交降雨权重设置 */
function submitRainfallWeight() {
  // 获取有效的测站（已填写stationId的测站）
  const validStations = rainfallWeightForm.value.stations.filter(
    (station) =>
      station.stationId &&
      station.weight !== null &&
      station.weight !== undefined
  );

  // 计算权重总和
  const totalWeight = validStations.reduce(
    (sum, station) => sum + (Number(station.weight) || 0),
    0
  );

  // 检查是否只有一个测站且权重不为1
  if (validStations.length === 1) {
    if (Math.abs(totalWeight - 1) > 0.001) {
      proxy.$modal.msgError("只有一个测站时，权重必须为1");
      return;
    }
  } else if (Math.abs(totalWeight - 1) > 0.001) {
    // 多个测站时检查权重总和是否为1
    proxy.$modal.msgError("所有测站权重之和必须为1");
    return;
  }

  // 表单验证
  proxy.$refs["rainfallWeightRef"].validate(async (valid) => {
    if (valid) {
      try {
        // 构造请求数据
        const requestData = validStations.map((station) => ({
          townCode: rainfallWeightForm.value.adcd,
          stationCode: station.stationId,
          weight: station.weight,
        }));

        await saveStationWeights(requestData);
        proxy.$modal.msgSuccess("保存成功");
        rainfallWeightOpen.value = false;
      } catch (error) {
        console.error("保存失败:", error);
        proxy.$modal.msgError("保存失败");
      }
    }
  });
}

/** 重置降雨权重设置 */
function resetRainfallWeight() {
  proxy.$modal
    .confirm("确定要重置所有数据吗?")
    .then(() => {
      // 将表单恢复到初始状态
      rainfallWeightForm.value = deepClone(originalRainfallWeightForm.value);
      proxy.$modal.msgSuccess("重置成功");
    })
    .catch(() => {});
}

/** 计算表格的合计行 */
function getSummaries() {
  return ["", "总计", totalWeight.value];
}
</script>
