<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" class="form-container">
      <el-form-item label="测站名称" prop="stnm">
        <el-input v-model="queryParams.stnm" placeholder="请输入测站名称" clearable
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="测站代码" prop="stcd">
        <el-input v-model="queryParams.stcd" placeholder="请输入测站代码" clearable
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="行政区划" prop="adcd">
        <!-- <el-select v-model="queryParams.rsvrtp" placeholder="请选择工程规模" clearable style="width: 200px">
          <el-option v-for="item in engScalOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select> -->
        <el-tree-select v-model="queryParams.adcd" :data="adcdOptions"
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="adcd" placeholder="选择所属县区"
          check-strictly />
      </el-form-item>
      <el-form-item label="工程规模" prop="rsvrtp">
        <el-select v-model="queryParams.rsvrtp" placeholder="请选择工程规模" clearable>
          <el-option v-for="item in engScalOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-button type="info" plain icon="Upload" @click="handleImport">导入</el-button>
      </div>
      <el-table v-loading="loading" :data="lyList" stripe>
        <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
        <el-table-column prop="stcd" label="测站编码" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="stnm" label="测站名称" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="countryName" label="区县" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="rsvrtp" label="水库工程规模" :show-overflow-tooltip="true">
          <template #default="scope">
            <span>{{ getengScal(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="damel" label="坝顶高程(m)" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="ckflz" label="校核洪水位(m)" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="dsflz" label="设计洪水位(m)" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="hhrz" label="历史最高水位(m)" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="fsltdz" label="主汛期限制水位(m)" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="操作" align="center" width="210" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
        @pagination="getList" />
    </div>

    <el-dialog :title="title" v-model="open" width="900px" :close-on-click-modal="false" @close="cancel" class="station-dialog">
      <!-- 基本信息区域 -->
      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">基本信息</h3>
        </div>
        <div class="station-info">
          <div class="info-item">
            <span class="label">测站名称:</span>
            <span class="value">{{ form.stnm }}</span>
          </div>
          <div class="info-item">
            <span class="required-asterisk">*</span>
            <span class="label">工程规模:</span>
            <el-select v-model="form.rsvrtp" placeholder="请选择工程规模" style="width: 200px" clearable required>
              <el-option v-for="item in engScalOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>

      <!-- 水库站防洪指标区域 -->
      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">水库站防洪指标</h3>
        </div>
        <el-form ref="rsvRef" :model="form" :rules="rules" label-width="170px" class="flood-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="坝顶高程(m)" prop="damel">
                <el-input v-model="form.damel" placeholder="请输入坝顶高程" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="校核洪水位(m)" prop="ckflz">
                <el-input v-model="form.ckflz" placeholder="请输入校核洪水位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设计洪水位(m)" prop="dsflz">
                <el-input v-model="form.dsflz" placeholder="请输入设计洪水位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="正常高水位(m)" prop="normz">
                <el-input v-model="form.normz" placeholder="请输入正常高水位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="死水位(m)" prop="ddz">
                <el-input v-model="form.ddz" placeholder="请输入死水位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="兴利水位(m)" prop="registactzerCode">
                <el-input v-model="form.actz" placeholder="请输入兴利水位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="总库容(百万m³)" prop="ttcp">
                <el-input v-model="form.ttcp" placeholder="请输入总库容" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="防洪库容(百万m³)" prop="fldcp">
                <el-input v-model="form.fldcp" placeholder="请输入防洪库容" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="兴利库容(百万m³)" prop="actcp">
                <el-input v-model="form.actcp" placeholder="请输入兴利库容" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="死库容(百万m³)" prop="ddcp">
                <el-input v-model="form.ddcp" placeholder="请输入死库容" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="历史最高水位(m)" prop="hhrz">
                <el-input v-model="form.hhrz" placeholder="请输入历史最高水位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="历史最高水位出现时间" prop="hhrztm">
                <el-date-picker v-model="form.hhrztm" placeholder="请输入历史最高水位出现时间" type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 水库站汛限水位区域 -->
      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">水库站汛限水位</h3>
          <div class="station-actions">
            <el-button type="primary" icon="Plus" @click="handleType">新增</el-button>
          </div>
        </div>
        <div class="water-flow-content" style="padding: 0 16px;">
          <el-table :data="floodControlLevelDtos" border stripe size="small" highlight-current-row>
            <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
            <el-table-column prop="fstp" label="汛期类别">
              <template #default="scope">
                <el-select v-model="scope.row.fstp" @change="changeFstp" size="small">
                  <el-option v-for="item in staOptions" :key="item.value" :label="item.label" :value="item.value"
                    :disabled="item.disabled" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="monitorItems" label="开始时间">
              <template #default="scope">
                <el-date-picker v-model="scope.row.bgmd" type="date" value-format="MMDD" format="MM-DD"
                  style="width: 150px;" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="monitorItems" label="结束时间">
              <template #default="scope">
                <el-date-picker v-model="scope.row.edmd" type="date" value-format="MMDD" format="MM-DD"
                  style="width: 150px;" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="fsltdz" label="汛期水位(m)">
              <template #default="scope">
                <el-input v-model="scope.row.fsltdz" size="small"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <el-button link type="danger" size="small" @click="handleDel(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 库容曲线区域 -->
      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">库容曲线</h3>
          <div class="station-actions">
            <el-button type="primary" icon="Plus" @click="handleLine">新增</el-button>
          </div>
        </div>
        <div class="water-flow-content">
          <div class="water-flow-table">
            <el-table :data="storageCapDtos" border stripe size="small" highlight-current-row>
              <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
              <el-table-column prop="w" label="水位(m)">
                <template #default="scope">
                  <el-input v-model="scope.row.w" size="small" placeholder="请输入水位"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="rz" label="库容(百万m³)">
                <template #default="scope">
                  <el-input v-model="scope.row.rz" size="small" placeholder="请输入库容"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template #default="scope">
                  <el-button link type="danger" size="small" @click="handleDelLine(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="water-flow-chart">
            <div id="lineChart"></div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel" plain>取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="800px" append-to-body>
      <div style="display: flex; justify-content: space-between;">
        <div>
          <span>水库站防洪指标导入</span>
          <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
            :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" drag>
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <template #tip>
              <div class="el-upload__tip text-center">
                <span>仅允许导入xls、xlsx格式文件。</span>
                <div>
                  <span>水库站防洪指标导入模板</span>
                  <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                    @click="importTemplate">下载模板</el-link>
                </div>
              </div>
            </template>
          </el-upload>
          <!-- <el-button type="primary" @click="submitFileForm">确 定</el-button> -->
        </div>
        <div>
          <span>汛限水位导入</span>
          <el-upload ref="uploadRef2" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url2"
            :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" drag>
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <template #tip>
              <div class="el-upload__tip text-center">
                <span>仅允许导入xls、xlsx格式文件。</span>
                <div>
                  <span>汛限水位导入模板</span>
                  <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                    @click="importTemplate2">下载模板</el-link>
                </div>

              </div>
            </template>
          </el-upload>
          <!-- <el-button type="primary" @click="submitFileForm2">确 定</el-button> -->
        </div>
        <div>
          <span>库容曲线导入</span>
          <el-upload ref="uploadRef3" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url3"
            :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" drag>
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <template #tip>
              <div class="el-upload__tip text-center">
                <span>仅允许导入xls、xlsx格式文件。</span>
                <div>
                  <span>库容曲线导入模板</span>
                  <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                    @click="importTemplate3">下载模板</el-link>
                </div>
              </div>
            </template>
          </el-upload>
          <!-- <el-button type="primary" @click="submitFileForm3">确 定</el-button> -->
        </div>
      </div>
      <!-- <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template> -->
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance, nextTick } from "vue";
import * as echarts from "echarts";
import {
  getAdcdTree,
  selectResFloList,
  getResFloInfo,
  controlResFlo
} from "@/api/watershed/ads";
import { getToken } from '@/utils/auth'
export default defineComponent({
  setup() {
    const { proxy } = getCurrentInstance();
    const total = ref(0)
    const state = reactive({
      queryParams: {
        stnm: '',
        stcd: '',
        adcdL: '',
        rsvrtp: '',
        pageNum: 1,
        pageSize: 20,
      },
      loading: false,
      lyList: [],
      engScalOptions: [{ label: '大(1)型', value: '1' },
      { label: '大(2)型', value: '2' },
      { label: '中型', value: '3' },
      { label: '小(1)型', value: '4' },
      { label: '小(2)型', value: '5' },
      { label: '其他', value: '6' },],
      adcdOptions: [],
      open: false,
      form: {

      },
      rules: {
        rsvrtp: [
          { required: true, message: "工程规模不能为空", trigger: "blur" }
        ]
      },
      floodControlLevelDtos: [],
      staOptions: [
        { label: '主汛期', value: '1', disabled: false },
        { label: '后汛期', value: '2', disabled: false },
        { label: '过渡期', value: '3', disabled: false },
        { label: '其他', value: '4', disabled: false },
      ],
      storageCapDtos: [],
      option: {
        xAxis: {
          type: 'category',
          name: '库容',
          data: []
        },
        yAxis: {
          name: '水位',
          type: 'value'
        },
        series: [
          {
            data: [],
            type: 'line'
          }
        ]
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,

        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: import.meta.env.VITE_APP_BASE_API + "/hydro/station/resFloodExcel",
        url2: import.meta.env.VITE_APP_BASE_API + "/hydro/station/floodControlExcel",
        url3: import.meta.env.VITE_APP_BASE_API + "/hydro/station/storageCapacityExcel"
      },
      title: ""
    })
    const handleQuery = () => {
      state.loading = true
      selectResFloList(state.queryParams).then(res => {
        state.lyList = res.data.records
        total.value = res.data.total
        state.loading = false
      })
    }
    const resetQuery = () => {
      proxy.resetForm("queryRef");
      handleQuery();
    }
    const getengScal = (data) => {
      for (let i = 0; i < state.engScalOptions.length; i++) {
        if (state.engScalOptions[i].value == data.rsvrtp) {
          return state.engScalOptions[i].label
        }
      }
    }
    /** 查询区县下拉树结构 */
    const getTreeselect = () => {
      getAdcdTree().then(res => {
        state.adcdOptions = res.data[0].children
      })
    }
    const handleUpdate = (row) => {
      state.title = row.stnm
      getResFloInfo(row.stcd).then(res => {
        state.form = res.data
        state.floodControlLevelDtos = res.data.floodControlLevelDtos
        state.storageCapDtos = res.data.storageCapDtos
        changeFstp()
        state.open = true
        state.option.xAxis.data = []
        state.option.series[0].data = []
        state.storageCapDtos.forEach(el => {
          state.option.xAxis.data.push(el.rz)
          state.option.series[0].data.push(el.w)
        })
        nextTick(() => {
          echarts
            .init(document.getElementById('lineChart'))
            .dispose()
          let myEchart = echarts.init(
            document.getElementById('lineChart')
          )
          myEchart.setOption(state.option)
        })
      })
    }
    const changeFstp = () => {
      state.staOptions[0].disabled = false
      state.staOptions[1].disabled = false
      state.staOptions[2].disabled = false
      state.staOptions[3].disabled = false
      state.floodControlLevelDtos.forEach(el => {
        state.staOptions.forEach(e => {
          if (el.fstp == e.value) {
            e.disabled = true
          }
        })
      })
    }
    const handleType = () => {
      if (state.floodControlLevelDtos.length > 0) {
        if (state.floodControlLevelDtos[state.floodControlLevelDtos.length - 1].fstp && state.floodControlLevelDtos[state.floodControlLevelDtos.length - 1].bgmd && state.floodControlLevelDtos[state.floodControlLevelDtos.length - 1].edmd && state.floodControlLevelDtos[state.floodControlLevelDtos.length - 1].fsltdz) {
          state.floodControlLevelDtos.push({
            fstp: null,
            bgmd: null,
            edmd: null,
            fsltdz: null,
          })
        } else {
          proxy.$modal.msgError("请补全上条信息");
        }
      } else {
        state.floodControlLevelDtos.push({
          fstp: null,
          bgmd: null,
          edmd: null,
          fsltdz: null,
        })
      }
    }
    const handleDel = (index) => {
      state.floodControlLevelDtos.splice(index, 1)
      changeFstp()
    }
    const handleLine = () => {
      state.storageCapDtos.push({
        rz: 0,
        w: 0
      })
      state.option.xAxis.data = []
      state.option.series[0].data = []
      state.storageCapDtos.forEach(el => {
        state.option.xAxis.data.push(el.rz)
        state.option.series[0].data.push(el.w)
      })
      echarts
        .init(document.getElementById('lineChart'))
        .dispose()
      let myEchart = echarts.init(
        document.getElementById('lineChart')
      )
      myEchart.setOption(state.option)
    }
    const handleDelLine = (index) => {
      state.storageCapDtos.splice(index, 1)
      state.storageCapDtos.forEach(el => {
        state.option.xAxis.data.push(el.rz)
        state.option.series[0].data.push(el.w)
      })
      echarts
        .init(document.getElementById('lineChart'))
        .dispose()
      let myEchart = echarts.init(
        document.getElementById('lineChart')
      )
      myEchart.setOption(state.option)
    }
    const submitForm = () => {
      proxy.$refs["rsvRef"].validate(valid => {
        if (valid) {
          if (!state.form.rsvrtp) {
            proxy.$modal.msgWarning("请选择工程规模")
            return
          }
          let bol = false
          state.floodControlLevelDtos.forEach(el => {
            if (Number(el.fsltdz) < Number(state.form.ddz) || Number(el.fsltdz) > Number(state.form.damel) || Number(el.fsltdz) > Number(state.form.ckflz) || Number(el.fsltdz) > Number(state.form.dsflz)) {
              bol = true
            }
          })

          if (bol) {
            proxy.$modal.msgError("汛限水位设置不合理");

            return
          }
          let data = state.form
          data.storageCapDtos = state.storageCapDtos
          data.floodControlLevelDtos = state.floodControlLevelDtos
          controlResFlo(data).then(res => {
            proxy.$modal.msgSuccess("保存成功");
            cancel()
            handleQuery();
          })
        }
      })
    }
    const cancel = () => {
      state.open = false
      state.form = {}
      state.storageCapDtos = []
      state.floodControlLevelDtos = []
    }


    /** 导入按钮操作 */
    const handleImport = () => {
      state.upload.title = "导入";
      state.upload.open = true;
    };
    /** 下载模板操作 */
    const importTemplate = () => {
      proxy.download("file/download", {
        fileName: 'beebf988-5f9e-4409-8c32-a88f4bdae235_水库防洪指标导入.xlsx',
        bucketName: 'excel-template'
      }, `水库站防洪指标导入模板.xlsx`);
    };
    /** 下载模板操作 */
    const importTemplate2 = () => {
      proxy.download("file/download", {
        fileName: 'b01631c5-a667-4d7a-a4f0-0e4bb24b5848_汛限水位导入模板.xlsx',
        bucketName: 'excel-template'
      }, `汛限水位导入模板.xlsx`);
    };
    /** 下载模板操作 */
    const importTemplate3 = () => {
      proxy.download("file/download", {
        fileName: '18fe1059-b754-4951-8669-4448841ac7b7_库容曲线导入模板.xlsx',
        bucketName: 'excel-template'
      }, `库容曲线导入模板.xlsx`);
    };
    /**文件上传中处理 */
    const handleFileUploadProgress = (event, file, fileList) => {
      state.upload.isUploading = true;
    };
    /** 文件上传成功处理 */
    const handleFileSuccess = (response, file, fileList) => {
      state.upload.open = false;
      state.upload.isUploading = false;
      proxy.$refs["uploadRef"].handleRemove(file);
      proxy.$refs["uploadRef2"].handleRemove(file);
      proxy.$refs["uploadRef3"].handleRemove(file);
      proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      handleQuery()
    };
    /** 提交上传文件 */
    const submitFileForm = () => {
      proxy.$refs["uploadRef"].submit();
    };
    onMounted(() => {
      handleQuery()
      getTreeselect()


    })
    return {
      ...toRefs(state),
      getengScal,
      handleQuery,
      handleLine,
      handleType,
      handleDel,
      handleDelLine,
      handleUpdate,
      submitForm,
      cancel,
      changeFstp,
      resetQuery,
      handleImport,
      importTemplate,
      importTemplate2,
      importTemplate3,
      handleFileUploadProgress,
      handleFileSuccess,
      submitFileForm,
      total
    };
  },
});
</script>
<style scoped lang="scss">
.topList {
  display: flex;
  flex-wrap: wrap;

  .item {
    margin-left: 140px;
    width: calc(50% - 140px);
    // text-align: center;
    margin-bottom: 10px;
    font-size: 16px;
    text-align: left;
  }
}

:deep(.station-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    margin-right: 0;
  }

  .el-dialog__body {
    padding: 20px 24px;
    max-height: 75vh;
    overflow-y: auto;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 14px 20px;
  }
}

.station-form-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.station-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.station-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  padding: 16px 0 8px;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: #409eff;
  }
}

.station-info {
  display: flex;
  flex-wrap: wrap;
  padding: 16px;
  gap: 30px;

  .info-item {
    min-width: 250px;
    display: flex;
    align-items: center;

    .label {
      color: #606266;
      font-weight: 500;
      margin-right: 8px;
    }

    .value {
      color: #303133;
      font-size: 15px;
    }

    .required-asterisk {
      color: #f56c6c;
      margin-right: 4px;
      font-size: 18px;
    }
  }
}

.flood-form {
  padding: 0 16px;

  :deep(.el-form-item) {
    margin-bottom: 20px;

    .el-form-item__label {
      font-weight: 500;
    }

    .el-input__wrapper {
      transition: all 0.3s;
    }
  }
}

.station-actions {
  display: flex;
  gap: 8px;
}

.water-flow-content {
  display: flex;
  padding: 0 16px;
  gap: 24px;

  @media screen and (max-width: 1200px) {
    flex-direction: column;
  }
}

.water-flow-table {
  flex: 1;
  max-height: 300px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

.water-flow-chart {
  flex: 1;

  #lineChart {
    height: 300px;
    width: 100%;
  }
}
</style>