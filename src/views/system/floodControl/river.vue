<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" class="form-container">
      <el-form-item label="测站名称/编码" prop="searchValue">
        <el-input
          v-model="queryParams.searchValue"
          placeholder="请输入测站名称/编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属河流" prop="riverName">
        <el-tree-select
          v-model="queryParams.riverName"
          :data="riverSelect"
          filterable
          clearable
          :props="{ value: 'rvName', label: 'rvName', children: 'children' }"
          value-key="id"
          placeholder="选择所属河流"
          check-strictly
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行政区划" prop="adcd">
        <el-tree-select
          v-model="queryParams.adcd"
          :data="adcdOptions"
          clearable
          :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
          value-key="adcd"
          placeholder="选择所属县区"
          check-strictly
        />
      </el-form-item>
      <el-form-item class="form-item-btn">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button type="primary" icon="Refresh" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="content content-table">
      <div class="table-header">
        <el-button type="info" plain icon="Upload" @click="handleImport"
          >导入</el-button
        >
      </div>
      <el-table v-loading="loading" :data="lyList" stripe>
        <el-table-column
          type="index"
          label="序号"
          width="65"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="stcd"
          label="测站编码"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="stnm"
          label="测站名称"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="countryName"
          label="区县"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column prop="rvnm" label="河流" :show-overflow-tooltip="true">
          <template #default="scope">
            <span
              v-if="
                scope.row.riverStationRelDtoList &&
                scope.row.riverStationRelDtoList.length > 0
              "
              >{{ scope.row.riverStationRelDtoList[0].riverName }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="ldkel"
          label="左堤高程(m)"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="rdkel"
          label="右堤高程(m)"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="wrz"
          label="警戒水位(m)"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="grz"
          label="保证水位(m)"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="obhtz"
          label="实测最高水位(m)"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="210"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <el-dialog
      :title="title"
      v-model="open"
      width="900px"
      :close-on-click-modal="false"
      @close="cancel"
      class="station-dialog"
    >
      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">基本信息</h3>
        </div>
        <div class="station-info">
          <div class="info-item">
            <span class="label">测站名称:</span>
            <span class="value">{{ form.stnm }}</span>
          </div>
          <div class="info-item">
            <span class="label">测站编码:</span>
            <span class="value">{{ form.stcd }}</span>
          </div>
        </div>
      </div>

      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">河道站防洪指标</h3>
        </div>
        <el-form
          ref="rsvRef"
          :model="form"
          :rules="rules"
          label-width="170px"
          class="flood-form"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="左堤高程(m)" prop="ldkel">
                <el-input v-model="form.ldkel" placeholder="请输入左堤高程" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="右堤高程(m)" prop="rdkel">
                <el-input v-model="form.rdkel" placeholder="请输入右堤高程" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="警戒水位(m)" prop="wrz">
                <el-input v-model="form.wrz" placeholder="请输入警戒水位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="保证水位(m)" prop="grz">
                <el-input v-model="form.grz" placeholder="请输入保证水位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实测最高水位(m)" prop="obhtz">
                <el-input
                  v-model="form.obhtz"
                  placeholder="请输入实测最高水位"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实测最高水位出现时间" prop="obhtztm">
                <el-date-picker
                  v-model="form.obhtztm"
                  placeholder="请选择实测最高水位出现时间"
                  type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="历史最低水位(m)" prop="hlz">
                <el-input v-model="form.hlz" placeholder="请输入历史最低水位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="历史最低水位出现时间" prop="hlztm">
                <el-date-picker
                  v-model="form.hlztm"
                  placeholder="请选择历史最低水位出现时间"
                  type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">水位流量关系</h3>
          <div class="station-actions">
            <el-button type="primary" icon="Plus" @click="handleLine"
              >新增</el-button
            >
          </div>
        </div>
        <div class="water-flow-content">
          <div class="water-flow-table">
            <el-table
              :data="riverFloodWaterFlowDtos"
              border
              stripe
              size="small"
              highlight-current-row
            >
              <el-table-column
                type="index"
                label="序号"
                width="60"
                align="center"
              ></el-table-column>
              <el-table-column prop="z" label="水位(m)">
                <template #default="scope">
                  <el-input
                    v-model="scope.row.z"
                    placeholder="请输入水位"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="q" label="流量(m³/s)">
                <template #default="scope">
                  <el-input
                    v-model="scope.row.q"
                    placeholder="请输入流量"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template #default="scope">
                  <el-button
                    link
                    type="danger"
                    size="small"
                    @click="handleDelLine(scope.$index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="water-flow-chart">
            <div id="lineChart"></div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel" plain>取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      v-model="upload.open"
      width="500px"
      append-to-body
    >
      <div style="display: flex; justify-content: space-between">
        <div>
          <el-upload
            ref="uploadRef"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="upload.headers"
            :action="upload.url"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            drag
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip text-center">
                <span>仅允许导入xls、xlsx格式文件。</span>
                <div>
                  <span>河道站防洪指标导入模板</span>
                  <el-link
                    type="primary"
                    :underline="false"
                    style="font-size: 12px; vertical-align: baseline"
                    @click="importTemplate"
                    >下载模板</el-link
                  >
                </div>
              </div>
            </template>
          </el-upload>
        </div>
        <div>
          <el-upload
            ref="uploadRef2"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="upload.headers"
            :action="upload.url2"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            drag
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip text-center">
                <span>仅允许导入xls、xlsx格式文件。</span>
                <div>
                  <span>水位流量关系导入模板</span>
                  <el-link
                    type="primary"
                    :underline="false"
                    style="font-size: 12px; vertical-align: baseline"
                    @click="importTemplate2"
                    >下载模板</el-link
                  >
                </div>
              </div>
            </template>
          </el-upload>
        </div>
      </div>
      <!-- <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template> -->
    </el-dialog>
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  getCurrentInstance,
  nextTick,
} from "vue";
import * as echarts from "echarts";
import { getToken } from "@/utils/auth";
import {
  getAdcdTree,
  selectRvFloList,
  getRvFloInfo,
  controlRvFlo,
  selectRvList,
} from "@/api/watershed/ads";
export default defineComponent({
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      total: 0,
      queryParams: {
        searchValue: "",
        pageNum: 1,
        pageSize: 200,
      },
      loading: false,
      riverSelect: [],
      lyList: [],
      engScalOptions: [
        { label: "大(1)型", value: "1" },
        { label: "大(2)型", value: "2" },
        { label: "中型", value: "3" },
        { label: "小(1)型", value: "4" },
        { label: "小(2)型", value: "5" },
        { label: "其他", value: "6" },
      ],
      adcdOptions: [],
      open: false,
      form: {},
      rules: {},
      riverFloodWaterFlowDtos: [],
      option: {
        xAxis: {
          type: "category",
          data: [],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [],
            type: "line",
          },
        ],
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,

        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url:
          import.meta.env.VITE_APP_BASE_API +
          "/hydro/station/river-flood-excel",
        url2:
          import.meta.env.VITE_APP_BASE_API +
          "/hydro/station/river-water-level-excel",
      },
      title: "",
    });
    const handleQuery = () => {
      state.loading = true;
      selectRvFloList(state.queryParams).then((res) => {
        state.lyList = res.data.records;
        state.total = res.data.total;
        state.loading = false;
      });
    };
    const resetQuery = () => {
      proxy.resetForm("queryRef");
      handleQuery();
    };
    const getengScal = (data) => {
      for (let i = 0; i < state.engScalOptions.length; i++) {
        if (state.engScalOptions[i].value == data.rsvrtp) {
          return state.engScalOptions[i].label;
        }
      }
    };
    /** 查询区县下拉树结构 */
    const getTreeselect = () => {
      getAdcdTree().then((res) => {
        state.adcdOptions = res.data[0].children;
      });
      selectRvList({
        pageNum: 1,
        pageSize: 1000,
      }).then((res) => {
        state.riverSelect = convertDataFormat(res.data);
      });
    };
    const convertDataFormat = (data) => {
      // 检查是否是数组
      if (!Array.isArray(data)) {
        return null;
      }
      // 对数组中的每个元素进行处理
      return data.map((item) => {
        // 复制 data 属性并移除 children
        const newData = { ...item.data };
        delete newData.children;
        // 递归处理子元素
        if (item.children && item.children.length > 0) {
          newData.children = convertDataFormat(item.children);
        }
        return newData;
      });
    };
    const handleUpdate = (row) => {
      state.title = "修改河道站防洪指标 - " + row.stnm;
      getRvFloInfo(row.stcd).then((res) => {
        state.form = res.data;
        state.open = true;
        state.riverFloodWaterFlowDtos = res.data.riverFloodWaterFlowDtos || [];
        state.option.xAxis.data = [];
        state.option.series[0].data = [];
        state.riverFloodWaterFlowDtos.forEach((el) => {
          state.option.xAxis.data.push(el.z);
          state.option.series[0].data.push(el.q);
        });
        nextTick(() => {
          echarts.init(document.getElementById("lineChart")).dispose();
          let myEchart = echarts.init(document.getElementById("lineChart"));
          myEchart.setOption(state.option);
        });
      });
    };

    const handleLine = () => {
      if (state.riverFloodWaterFlowDtos.length > 1) {
        if (
          state.riverFloodWaterFlowDtos[
            state.riverFloodWaterFlowDtos.length - 1
          ].z <
          state.riverFloodWaterFlowDtos[
            state.riverFloodWaterFlowDtos.length - 2
          ].z
        ) {
          proxy.$modal.msgError("水位请递增");
          return;
        }
        if (
          state.riverFloodWaterFlowDtos[
            state.riverFloodWaterFlowDtos.length - 1
          ].q <
          state.riverFloodWaterFlowDtos[
            state.riverFloodWaterFlowDtos.length - 2
          ].q
        ) {
          proxy.$modal.msgError("流量请递增");
          return;
        }
      }
      state.riverFloodWaterFlowDtos.push({
        z: 0,
        q: 0,
      });
      state.option.xAxis.data = [];
      state.option.series[0].data = [];
      state.riverFloodWaterFlowDtos.forEach((el) => {
        state.option.xAxis.data.push(el.z);
        state.option.series[0].data.push(el.q);
      });
      echarts.init(document.getElementById("lineChart")).dispose();
      let myEchart = echarts.init(document.getElementById("lineChart"));
      myEchart.setOption(state.option);
    };
    const handleDelLine = (index) => {
      state.riverFloodWaterFlowDtos.splice(index, 1);
      state.riverFloodWaterFlowDtos.forEach((el) => {
        state.option.xAxis.data.push(el.z);
        state.option.series[0].data.push(el.q);
      });
      echarts.init(document.getElementById("lineChart")).dispose();
      let myEchart = echarts.init(document.getElementById("lineChart"));
      myEchart.setOption(state.option);
    };
    const submitForm = () => {
      if (state.riverFloodWaterFlowDtos.length > 1) {
        if (
          state.riverFloodWaterFlowDtos[
            state.riverFloodWaterFlowDtos.length - 1
          ].z <
          state.riverFloodWaterFlowDtos[
            state.riverFloodWaterFlowDtos.length - 2
          ].z
        ) {
          proxy.$modal.msgError("水位请递增");
          return;
        }
        if (
          state.riverFloodWaterFlowDtos[
            state.riverFloodWaterFlowDtos.length - 1
          ].q <
          state.riverFloodWaterFlowDtos[
            state.riverFloodWaterFlowDtos.length - 2
          ].q
        ) {
          proxy.$modal.msgError("流量请递增");
          return;
        }
      }
      let data = state.form;
      data.riverFloodWaterFlowDtos = state.riverFloodWaterFlowDtos;
      controlRvFlo(data).then((res) => {
        proxy.$modal.msgSuccess("保存成功");
        cancel();
        handleQuery();
      });
    };
    const cancel = () => {
      state.open = false;
      state.form = {};
      state.riverFloodWaterFlowDtos = [];
    };
    /** 导入按钮操作 */
    const handleImport = () => {
      state.upload.title = "导入";
      state.upload.open = true;
    };
    /** 下载模板操作 */
    const importTemplate = () => {
      proxy.download(
        "file/download",
        {
          fileName:
            "68d4ec67-d9ba-45b0-beb1-04f700446569_河道站防洪指标导入.xlsx",
          bucketName: "excel-template",
        },
        `河道站防洪指标导入模板.xlsx`
      );
    };
    /** 下载模板操作 */
    const importTemplate2 = () => {
      proxy.download(
        "file/download",
        {
          fileName:
            "1db3e803-c099-457b-8faf-022d54f39489_水位流量关系导入.xlsx",
          bucketName: "excel-template",
        },
        `河道站水位流量导入模板.xlsx`
      );
    };
    /**文件上传中处理 */
    const handleFileUploadProgress = (event, file, fileList) => {
      state.upload.isUploading = true;
    };
    /** 文件上传成功处理 */
    const handleFileSuccess = (response, file, fileList) => {
      state.upload.open = false;
      state.upload.isUploading = false;
      proxy.$refs["uploadRef"].handleRemove(file);
      proxy.$refs["uploadRef2"].handleRemove(file);
      proxy.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      handleQuery();
    };
    /** 提交上传文件 */
    const submitFileForm = () => {
      proxy.$refs["uploadRef"].submit();
    };
    onMounted(() => {
      handleQuery();
      getTreeselect();
    });
    return {
      ...toRefs(state),
      getengScal,
      handleQuery,
      handleLine,
      handleDelLine,
      handleUpdate,
      submitForm,
      cancel,
      resetQuery,
      handleImport,
      importTemplate,
      handleFileUploadProgress,
      handleFileSuccess,
      submitFileForm,
      importTemplate2,
    };
  },
});
</script>
<style scoped lang="scss">
.topList {
  display: flex;
  flex-wrap: wrap;

  .item {
    margin-left: 140px;
    width: calc(50% - 140px);
    // text-align: center;
    margin-bottom: 10px;
    font-size: 16px;
    text-align: left;
  }
}

/* 新增样式 */
:deep(.station-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    margin-right: 0;
  }

  .el-dialog__body {
    padding: 20px 24px;
    max-height: 75vh;
    overflow-y: auto;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 14px 20px;
  }
}

.station-form-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.station-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.station-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  padding: 16px 0 8px;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: #409eff;
  }
}

.station-info {
  display: flex;
  flex-wrap: wrap;
  padding: 16px;
  gap: 30px;

  .info-item {
    min-width: 250px;
    display: flex;
    align-items: center;

    .label {
      color: #606266;
      font-weight: 500;
      margin-right: 8px;
    }

    .value {
      color: #303133;
      font-size: 15px;
    }
  }
}

.flood-form {
  padding: 0 16px;

  :deep(.el-form-item) {
    margin-bottom: 20px;

    .el-form-item__label {
      font-weight: 500;
    }

    .el-input__wrapper {
      transition: all 0.3s;
    }
  }
}

.station-actions {
  display: flex;
  gap: 8px;
}

.water-flow-content {
  display: flex;
  padding: 0 16px;
  gap: 24px;

  @media screen and (max-width: 1200px) {
    flex-direction: column;
  }
}

.water-flow-table {
  flex: 1;
  max-height: 300px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

.water-flow-chart {
  flex: 1;

  #lineChart {
    height: 300px;
    width: 100%;
  }
}
</style>
