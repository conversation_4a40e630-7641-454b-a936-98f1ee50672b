<template>
  <div class="app-container">
    <div class="tenanttip">
      <p>特别提醒,租户管理员初始密码采用邮件通知方式发放,保证安全性,请填写正确的邮箱!</p>
    </div>
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px" class="form-container">
      <el-form-item label="租户名称" prop="tenantName">
        <el-input v-model="queryParams.tenantName" placeholder="请输入租户名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="管理员账号" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入管理员账号" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号码" prop="userPhone">
        <el-input v-model="queryParams.userPhone" placeholder="请输入手机号码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="租赁结束时间">
        <el-date-picker v-model="daterangeTenantTime" style="width: 240px" value-format="YYYY-MM-DD" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
           >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
             >导出</el-button>
          </el-col> -->
          <right-toolbar :showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="tenantList" @selection-change="handleSelectionChange" stripe>
        <el-table-column label="租户编码" align="center" prop="id" />
        <el-table-column label="租户名称" align="center" prop="tenantName" />
        <el-table-column label="管理员账号" align="center" prop="userName" />
        <el-table-column label="手机号码" align="center" prop="userPhone" />
        <el-table-column label="邮箱地址" align="center" prop="userEmail" />
        <el-table-column label="租户权限" align="center" prop="tenantPackage" width="180">
          <template v-slot="scope">
            <el-tag>{{ getPackageName(scope.row.tenantPackage) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="租户流域" align="center" prop="basinName" width="180">

        </el-table-column>
        <!-- <el-table-column label="行政区" align="center" prop="tenantPackage" width="180">
          <template v-slot="scope">
            <el-tag>{{ getPackageName(scope.row.tenantPackage) }}</el-tag>
          </template>
        </el-table-column> -->

        <el-table-column label="租赁结束时间" align="center" prop="tenantTime" width="120">
          <template v-slot="scope">
            <span>{{ parseTime(scope.row.tenantTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="租户状态" align="center" prop="status">
          <template v-slot="scope">
            <dict-tag :options="sys_tenant_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template v-slot="scope">

            <el-button type="primary" link @click="handleUpdate(scope.row)" icon="Edit">修改</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)" icon="Delete">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <!-- 添加或修改租户管理对话框 -->
    <el-dialog :title="title" v-model="open" width="550px" append-to-body>
      <el-form ref="form" :model="diaForm" :rules="rules" label-width="120px">
        <el-form-item label="租户编码" prop="id" v-if="false">
          <el-input v-model="diaForm.id" placeholder="请输入租户编码" :disabled="this.updateOrAdd" />
        </el-form-item>
        <el-form-item label="租户名称" prop="tenantName">
          <el-input v-model="diaForm.tenantName" placeholder="请输入租户名称" />
        </el-form-item>
        <el-form-item label="管理员账号" prop="userName">
          <el-input v-model="diaForm.userName" placeholder="请输入管理员账号" :disabled="this.updateOrAdd" />
        </el-form-item>
        <el-form-item label="手机号码" prop="userPhone">
          <el-input v-model="diaForm.userPhone" placeholder="请输入手机号码" />
        </el-form-item>

        <el-form-item label="邮箱地址" prop="userEmail">
          <el-input v-model="diaForm.userEmail" placeholder="请输入邮箱地址" />
        </el-form-item>
        <el-form-item label="租户权限" prop="tenantPackage">
          <el-select v-model="diaForm.tenantPackage" placeholder="请选择租户权限" clearable>
            <el-option v-for="item in packageList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="租户流域" prop="basinId">
          <el-tree-select v-model="diaForm.basinId" :data="lycodeOptions" :disabled="this.updateOrAdd"
            :props="{ value: 'basinId', label: 'name', children: 'children' }" value-key="id" placeholder="请选择租户流域"
            check-strictly @change="changeCode" />
        </el-form-item>
        <el-form-item label="电子沙盘名称" prop="screenName">
          <el-input v-model="diaForm.screenName" placeholder="请输入电子沙盘名称" />
        </el-form-item>
        <el-form-item label="工作台名称" prop="workBench">
          <el-input v-model="diaForm.workBench" placeholder="请输入工作台名称" />
        </el-form-item>
        <el-form-item label="租赁结束时间" prop="tenantTime">
          <el-date-picker :disabled-date="disabledDate" clearable v-model="diaForm.tenantTime" type="datetime"
            placeholder="请选择租赁结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="租户状态">
          <el-radio-group v-model="diaForm.status">
            <el-radio v-for="dict in sys_tenant_status" :key="dict.value" :label="parseInt(dict.value)">{{
              dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="diaForm.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, reactive, getCurrentInstance, toRefs } from "vue";
import { listTenant, getTenant, delTenant, addTenant, updateTenant } from "@/api/system/tenant";
import { getTenantPackageList } from "@/api/system/tenantpackage";
import { parseTime } from "@/utils/ruoyi";
import {
  selectStlyList
} from "@/api/watershed/ads";
import moment from "moment";
export default defineComponent({
  setup() {
    // name: "Tenant",
    //   dicts: ['sys_tenant_status'],
    const { proxy } = getCurrentInstance();
    const { sys_tenant_status } = proxy.useDict("sys_tenant_status");
    const state = reactive({
      // expireTimeOPtion: {
      //   disabledDate(time) {
      //     return time.getTime() < Date.now() //如果没有后面的-8.64e7就是不可以选择今天的
      //   }
      // },
      disabledDate: '',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 租户管理表格数据
      tenantList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 租户权限列表
      packageList: [],
      // 备注时间范围
      daterangeTenantTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantName: null,
        userName: null,
        userPhone: null,
        tenantTime: null,
        status: null,
      },
      // 表单参数
      diaForm: {
        tenantName: null,
        userName: null,
        userPhone: null,
        userEmail: null,
        tenantPackage: null,
        tenantTime: null,
        status: 0,
        // deleteFlag: null,
        remark: null,
        basinId: null
      },
      // 是否更新 默认是插入 不禁用无法修改的字段
      updateOrAdd: false,
      // 表单校验
      rules: {
        // id: [
        //   { required: true, message: "租户编码不能为空", trigger: "blur" },
        //   {
        //     pattern: /^[1-9]\d*$/,
        //     message: "租户编码只能为数字类型",
        //     trigger: "blur"
        //   }
        // ],
        tenantName: [
          { required: true, message: "租户名称不能为空", trigger: "blur" }
        ],
        userName: [
          { required: true, message: "管理员账号不能为空", trigger: "blur" }
        ],
        userPhone: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^((0\d{2,3}-\d{7,8})|(1[34578]\d{9}))$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ],
        userEmail: [
          { required: true, message: "邮箱地址不能为空", trigger: "blur" },
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"]
          }
        ],
        tenantPackage: [
          { required: true, message: "租户权限不能为空", trigger: "blur" }
        ],
        basinId: [
          { required: true, message: "租户流域不能为空", trigger: "blur" }
        ],
        tenantTime: [
          { required: true, message: "租赁结束时间不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "租户状态不能为空", trigger: "blur" }
        ],
      },
      code: '',
      adcdList: [],
      lycodeOptions: [],
      sys_tenant_status
    })
    return {
      ...toRefs(state),
      sys_tenant_status,
    }
  },
  created() {
    this.getList();
    this.getTreeselect()
    this.disabledDate = (time) => {
      // return time.getTime() < new Date()
      // return time.getTime() < Date.now()
      return (time.getTime() > moment().add(5, 'year').valueOf() || time.getTime() < Date.now())
    }

    // 获得租户权限列表
    getTenantPackageList().then(response => {
      this.packageList = response.data;
    })
  },
  methods: {
    /** 查询租户管理列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeTenantTime && '' != this.daterangeTenantTime) {
        this.queryParams.beginTenantTime = this.daterangeTenantTime[0];
        this.queryParams.endTenantTime = this.daterangeTenantTime[1];
      }
      listTenant(this.queryParams).then(response => {
        this.tenantList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 查询流域下拉树结构 */
    getTreeselect() {
      selectStlyList({}).then(res => {
        // console.log(this.flattenData(res.data));
        this.lycodeOptions = this.flattenData(res.data)
      });
    },
    flattenData(data) {
      let flattenedData = [];
      data.forEach(item => {
        flattenedData.push(item.data);
        if (item.children.length > 0) {
          flattenedData = flattenedData.concat(this.flattenData(item.children));
        }
      });
      return flattenedData;
    },
    changeCode(v) {
      // console.log(this.findObject(this.lycodeOptions, v));
      this.diaForm.screenName = this.findObject(this.lycodeOptions, v) + '电子沙盘'
      this.diaForm.workBench = this.findObject(this.lycodeOptions, v) + '四预工作台'
    },
    findObject(arr, targetName) {
      let name = '';
      for (let i = 0; i < arr.length; i++) {
        console.log(arr[i]);
        if (targetName == arr[i].basinId) {
          name = arr[i].name
        }
      }
      return name
    },
    // 表单重置
    reset() {
      this.diaForm = {
        tenantName: null,
        userName: null,
        userPhone: null,
        userEmail: null,
        tenantPackage: null,
        tenantTime: null,
        status: 0,
        // deleteFlag: null,
        remark: null,
        basinId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeTenantTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.updateOrAdd = false;
      this.title = "新增租户";
      //每次修改租户的时候都会添加租户角色校验
      getTenantPackageList().then(response => {
        this.packageList = response.data;
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      getTenantPackageList().then(response => {
        this.packageList = response.data;
      })
      this.updateOrAdd = true;
      const id = row.id || this.ids
      getTenant(id).then(response => {
        this.diaForm = response.data;
        this.open = true;
        this.title = "修改租户";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.diaForm.tenantTime = moment(this.diaForm.tenantTime).format('YYYY-MM-DD HH:mm:ss')
          let data = JSON.parse(JSON.stringify(this.diaForm))
          // data.adcds = data.adcd.join(",")
          // delete data.adcd
          if (this.diaForm.id != null && this.updateOrAdd) {
            updateTenant(data).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTenant(data).then(response => {
              this.$modal.msgSuccess("新增成功,密码已发送至邮箱");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除当前租户数据项？').then(function () {
        return delTenant(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/tenant/export', {
        ...this.queryParams
      }, `租户管理_${parseTime(new Date().getTime(), '{y}-{m}-{d}')}.xlsx`)
    },

    getPackageName(packageId) {
      let t_name = "";
      for (const t_item of this.packageList) {
        if (t_item.id === parseInt(packageId)) {
          t_name = t_item.name;
        }
      }
      return t_name;
    },
    findLyName(arr, basinId) {
      for (let obj of arr) {
        if (obj.basinId === basinId) {
          return obj.lynm;
        }
        if (obj.children.length > 0) {
          const result = this.findLyName(obj.children, basinId);
          if (result) {
            return obj.lynm;
          }
        }
      }
      return null
    }
  },
})
</script>

<style lang="scss" scoped>
.tenanttip {
  padding: 8px 16px;
  background-color: #ecf8ff;
  border-radius: 4px;
  border-left: 5px solid #ef8708;
  margin: 0 0 20px 0;

  p {
    font-weight: 400;
    color: #1f2f3d;
  }
}
</style>