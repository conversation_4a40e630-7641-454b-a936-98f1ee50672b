
<template>
   <div class="app-container">
      <div class="hedgingBox">
         <div class="top">
            <div class="items">
               <div class="value">300 <span>人</span></div>
               <div class="name">已安置</div>
            </div>
            <div class="person">
               <div>责任人 —— 镇长：张明 18762815525</div>
               <div>地址 —— 乐亭县城区幸福路198号</div>
            </div>
         </div>
         <div class="form">
            <div>
               姓名:
               <el-input v-model="value" size="small" style="width: 130px;margin-right: 10px;"></el-input>
               <el-button type="primary" size="small">查询</el-button>
               <el-button size="small">重置</el-button>
            </div>
            <el-table class="productTable" :data="list1" :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
               :cell-style="{ color: '#ffffff', textAlign: 'center', fontSize: 13 + 'px' }" border>
               <el-table-column prop="risk" label="转移状态"></el-table-column>
               <el-table-column prop="village" label="姓名"></el-table-column>
               <el-table-column prop="resident" label="转移负责人"></el-table-column>
               <el-table-column prop="people" label="居住地址"></el-table-column>
               <el-table-column prop="type" label="安置方式"></el-table-column>
               <el-table-column prop="adcd" label="转移安置区域"></el-table-column>
            </el-table>
         </div>
      </div>
   </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";
export default defineComponent({
   setup() {
      const state = reactive({
         transferList: [
            {
               text: '正在转移',
               people: '300',
               township: '2',
               village: '9',
            },
            {
               text: '准备转移',
               people: '300',
               township: '2',
               village: '9',
            },
            {
               text: '已转移',
               people: '300',
               township: '2',
               village: '9',
            },
         ],
         value: 'all',
         list1: [
            {
               risk: '123'
            },
            {
               risk: '123'
            },
            {
               risk: '123'
            },
            {
               risk: '123'
            },
         ]
      })
      return {
         ...toRefs(state),
      };
   },
});
</script>
<style scoped lang="scss">
.hedgingBox {
   width: 530px;
   background-color: #00356c;
   color: #fff;
   padding: 10px;

   .top {
      display: flex;

      .items {
         width: 149px;
         height: 121px;
         text-align: center;
         background-image: url(../../screen//image/greenBack.png);
         color: #fff;
         font-size: 14px;
         overflow: hidden;
         margin-left: 30px;


         .value {
            margin: 30px 0 0;
            font-size: 22px;

            span {
               font-size: 12px;
            }
         }
      }

      .person {
         display: flex;
         flex-direction: column;
         justify-content: space-around;
         padding: 30px 0;
         font-size: 14px;
      }
   }

   .people {
      padding: 10px 20px;
      font-size: 14px;
   }

   .form {
      font-size: 14px;
   }

   .title {
      color: #8CD2FF;
      font-size: 16px;
   }
}

:deep(.productTable) {
   background-color: transparent !important;
   margin: 10px 0 20px;

   tr {
      background-color: transparent !important;

      th {
         background-color: #083f86 !important;
         color: #fff !important;
         border-bottom: 1px solid #17365a;
         border-color: #005e9d !important;
      }

      td {
         color: #92b7e9 !important;
         border-bottom: 1px solid #005e9d;
         border-color: #005e9d !important;

      }
   }

   --el-table-row-hover-bg-color: transparent;
   --el-table-border-color:#005e9d;

   .el-table__inner-wrapper {
      &::before {
         background-color: #005e9d;
      }
   }
}
</style>