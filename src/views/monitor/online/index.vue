<template>
   <div class="app-container">
      <div class="box">
         <div class="head">
            <div class="items" v-for="item in list1" :key="item.id">
               {{ item.name }}
            </div>
         </div>
         <div class="form">
            <el-date-picker v-model="time" type="datetimerange" range-separator="至" start-placeholder="开始时间"
               end-placeholder="结束时间" size="small" style="width: 100px;margin-right: 10px;" />
            <el-button type="primary" size="small" @click="query">查询</el-button>
            <el-button type="primary" plain size="small">重置</el-button>
            <el-button type="primary" plain size="small">数据</el-button>
            <el-button type="primary" plain size="small">导出</el-button>
         </div>
         <div class="rainfallChart" id="rainfallChart"></div>
      </div>
   </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import * as echarts from "echarts";
export default defineComponent({
   setup() {
      const state = reactive({
         list1: [
            {
               id: 0,
               name: '今日08时至今: 100mm'
            },
            {
               id: 1,
               name: '最近1小时: 无雨'
            },
            {
               id: 2,
               name: '最近3小时: 10.2mm'
            },
            {
               id: 3,
               name: '最近06小时: 20.4mm'
            },
            {
               id: 4,
               name: '最近12小时: 20.4mm'
            },
            {
               id: 5,
               name: '最近24小时: 20.4mm'
            },
         ],
         time: [],
         option: {
            grid: {
               left: '50px',  //距左边距 留够name的宽度
               right: '30px',
               bottom: '20px',
               top: '30px',
               // containLabel: true
            },

            xAxis: {
               type: 'category',
               data: ['10-01', '10-02', '10-03', '10-04', '10-05', '10-06', '10-07', '10-08'],
               // boundaryGap: false,
               axisPointer: {
                  type: 'shadow'
               },
               axisLabel: {
                  interval: "0",
                  color: "#fff"
               },
               axisLine: {
                  lineStyle: {
                     color: "#fff"
                  }
               }
            },
            yAxis: [
               {
                  type: 'value',
                  name: '水位(m)',
                  scale: true,
                  nameTextStyle: {
                     color: "#fff"
                  },
                  axisLabel: {
                     color: "#fff"
                  },
                  splitLine: {
                     lineStyle: {
                        color: "#005b99"
                     }
                  }
               },
               {
                  type: 'value',
                  name: '流量m³/s',
                  // scale: true,
                  nameTextStyle: {
                     color: "#fff"
                  },
                  axisLabel: {
                     color: "#fff"
                  },
                  splitLine: {
                     show: false,
                     lineStyle: {
                        color: "#fff"
                     }
                  }
               }
            ],
            series: [
               {
                  name: '水位',
                  type: 'bar',
                  // smooth: true,
                  symbol: "none",
                  color: "#059DFE",
                  data: [208.11, 208.17, 208.22, 208.13, 208.15, 208.11, 208.20, 208.19]
               },

            ]

         },
      })
      const query = () => { }
      onMounted(() => {
         echarts
            .init(document.getElementById('rainfallChart'))
            .dispose()
         let myEchart = echarts.init(
            document.getElementById('rainfallChart')
         )
         myEchart.setOption(state.option)
      })
      return {
         ...toRefs(state),
         query
      };
   },
});
</script>
<style scoped lang="scss">
.box {
   width: 530px;
   background-color: #00356c;
   padding: 10px;
   color: #FFFFFF;

   .head {
      display: flex;
      flex-wrap: wrap;

      .items {
         width: 33%;
         font-size: 14px;
      }
   }

   .form {
      display: flex;
      margin: 20px 0;
   }

   .rainfallChart {
      width: 100%;
      height: 230px;
   }

}
</style>
