<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="form-container">
      <el-form-item label="AppName" prop="appname">
        <el-input v-model="queryParams.appname" placeholder="请输入AppName" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="名称" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
      </div>
      <!-- 执行器列表 -->
      <el-table v-loading="loading" :data="groupList" stripe>
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column label="AppName" prop="appname" />
        <el-table-column label="名称" prop="title" />
        <el-table-column label="注册方式" prop="addressType">
          <template #default="scope">
            {{ scope.row.addressType === 0 ? "自动注册" : "手动录入" }}
          </template>
        </el-table-column>
        <el-table-column label="OnLine 机器地址" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="handleViewRegistry(scope.row)"> 查看 ({{ scope.row.registryList?.length || 0 }}) </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-dropdown split-button type="primary">
              操作
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleUpdate(scope.row)" icon="Edit"> 编辑 </el-dropdown-item>
                  <el-dropdown-item divided @click="handleDelete(scope.row)" icon="Delete"> 删除 </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>

    <!-- 添加或修改执行器对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="AppName" prop="appname">
          <el-input v-model="form.appname" placeholder="请输入AppName" />
        </el-form-item>
        <el-form-item label="名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="注册方式" prop="addressType">
          <el-radio-group v-model="form.addressType">
            <el-radio :label="0">自动注册</el-radio>
            <el-radio :label="1">手动录入</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="机器地址" prop="addressList">
          <el-input v-model="form.addressList" type="textarea" :disabled="form.addressType === 0" placeholder="请输入执行器地址列表，多个地址逗号分隔" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 注册节点查看弹窗 -->
    <el-dialog title="注册节点" v-model="registryDialog.visible" width="400px" append-to-body>
      <div class="registry-list">
        <template v-if="registryDialog.list && registryDialog.list.length > 0">
          <div v-for="(address, index) in registryDialog.list" :key="index" class="registry-item">
            <div class="registry-index">{{ index + 1 }}.</div>
            <el-tag type="success">{{ address }}</el-tag>
          </div>
        </template>
        <el-empty v-else description="暂无注册节点" />
      </div>
      <template #footer>
        <el-button type="primary" @click="registryDialog.visible = false">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue"
import { listGroup, addGroup, updateGroup, delGroup } from "@/api/monitor/job"

const { proxy } = getCurrentInstance()

/** 遮罩层 */
const loading = ref(false)
/** 显示搜索区域 */
const showSearch = ref(true)
/** 弹出层标题 */
const title = ref("")
/** 是否显示弹出层 */
const open = ref(false)
/** 执行器列表 */
const groupList = ref([])
/** 总条数 */
const total = ref(0)

/** 表单参数 */
const form = ref({
  id: undefined,
  appname: undefined,
  title: undefined,
  addressType: 0,
  addressList: undefined,
})

/** 表单校验规则 */
const rules = {
  appname: [
    { required: true, message: "请输入AppName", trigger: "blur" },
    { min: 4, max: 64, message: "AppName长度必须在4到64个字符之间", trigger: "blur" },
    {
      pattern: /^[a-z][a-z0-9-]*$/,
      message: "限制以小写字母开头，由小写字母、数字和中划线组成",
      trigger: "blur",
    },
  ],
  title: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { min: 4, max: 12, message: "名称长度必须在4到12个字符之间", trigger: "blur" },
  ],
  addressType: [{ required: true, message: "请选择注册方式", trigger: "blur" }],
  addressList: [
    {
      required: true,
      message: "请输入机器地址",
      trigger: "blur",
      validator: (rule, value, callback) => {
        if (form.value.addressType === 1 && (!value || value.trim() === "")) {
          callback(new Error("请输入机器地址"))
        } else {
          callback()
        }
      },
    },
  ],
}

/** 查询参数 */
const queryParams = ref({
  pageNum: 1, // 当前页码
  pageSize: 10, // 每页显示条数
  appname: undefined, // AppName
  title: undefined, // 名称
})

/** 查询执行器列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await listGroup(queryParams.value)
    groupList.value = res.data
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

const queryRef = ref()
/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value?.resetFields()
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    appname: undefined,
    title: undefined,
  }
  handleQuery()
}

/** 取消按钮 */
const cancel = () => {
  reset()
  open.value = false
}

/** 表单重置 */
const reset = () => {
  form.value = {
    id: undefined,
    appname: undefined,
    title: undefined,
    addressType: 0,
    addressList: undefined,
  }
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset()
  open.value = true
  title.value = "添加执行器"
}

/** 修改按钮操作 */
const handleUpdate = (row) => {
  reset()
  form.value = { ...row }
  open.value = true
  title.value = "修改执行器"
}

/** 提交按钮 */
const submitForm = () => {
  proxy.$refs["formRef"].validate(async (valid) => {
    if (valid) {
      const submitFunction = form.value.id ? updateGroup : addGroup
      try {
        await submitFunction(form.value)
        proxy.$modal.msgSuccess(form.value.id ? "修改成功" : "新增成功")
        open.value = false
        getList()
      } catch (error) {
        console.error(error)
      }
    }
  })
}

/** 删除按钮操作 */
const handleDelete = (row) => {
  proxy.$modal.confirm("是否确认删除该执行器？").then(async () => {
    try {
      await delGroup(row.id)
      proxy.$modal.msgSuccess("删除成功")
      getList()
    } catch (error) {
      console.error(error)
    }
  })
}

/** 机器地址弹窗数据 */
const registryDialog = ref({
  visible: false,
  list: [],
})

/** 查看机器地址 */
const handleViewRegistry = (row) => {
  registryDialog.value.list = row.registryList || []
  registryDialog.value.visible = true
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.registry-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;

  .registry-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    .registry-index {
      width: 40px;
      color: #909399;
      font-size: 13px;
    }

    .el-tag {
      flex: 1;
      text-align: left;
      font-family: monospace;
    }
  }
}
</style>
