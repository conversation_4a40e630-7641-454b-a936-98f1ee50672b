<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" class="form-container">
      <el-form-item label="执行器" prop="jobGroup">
        <el-select v-model="queryParams.jobGroup" placeholder="请选择执行器" clearable @change="handleJobGroupChange">
          <el-option label="全部" value=""/>
          <el-option v-for="dict in sys_job_group" :key="dict.id" :label="dict.title" :value="dict.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="任务" prop="jobId">
        <el-select v-model="queryParams.jobId" placeholder="请选择任务" clearable>
          <el-option label="全部" value=""/>
          <el-option v-for="job in jobList" :key="job.id" :label="job.jobDesc" :value="job.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="logStatus">
        <el-select v-model="queryParams.logStatus" placeholder="请选择状态" clearable>
          <el-option label="全部" :value="-1"/>
          <el-option label="成功" :value="1"/>
          <el-option label="失败" :value="2"/>
          <el-option label="进行中" :value="3"/>
        </el-select>
      </el-form-item>
      <el-form-item label="调度时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
        />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
        <el-button icon="Delete" @click="handleClear">清理</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <!-- 日志列表 -->
      <el-table v-loading="loading" :data="jobLogList" stripe>
        <el-table-column label="任务ID" prop="jobId" width="80" align="center"/>
        <el-table-column label="任务描述" prop="jobDesc" align="center" width="160" show-overflow-tooltip>

        </el-table-column>
        <el-table-column label="调度时间" align="center" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.triggerTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="调度结果" align="center" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.triggerCode === 200 ? 'success' : 'danger'">
              {{ scope.row.triggerCode === 200 ? "成功" : "失败" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="调度备注" align="center" width="100">
          <template #default="scope">
            <el-button link type="primary" @click="viewTriggerMsg(scope.row.triggerMsg)"> 查看</el-button>
          </template>
        </el-table-column>
        <el-table-column label="执行时间" align="center" width="160">
          <template #default="scope">
            <span>{{ scope.row.handleTime ? parseTime(scope.row.handleTime) : "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="执行结果" align="center" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.handleTime" :type="scope.row.handleCode === 200 ? 'success' : 'danger'">
              {{ scope.row.handleCode === 200 ? "成功" : "失败" }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="执行备注" align="center" prop="handleMsg" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-dropdown split-button type="primary" v-if="scope.row.triggerCode === 200">
              操作
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleViewLog(scope.row)" icon="List"> 执行日志</el-dropdown-item>
                  <el-dropdown-item divided @click="handleKill(scope.row)" icon="CircleClose"
                                    v-if="!scope.row.handleTime && !scope.row.handleCode"> 终止任务
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination :total="total" v-model:page="queryParams.pageNum"
                  v-model:limit="queryParams.pageSize" @pagination="getList"/>
    </div>
    <!-- 调度备注弹窗 -->
    <el-dialog title="调度备注" v-model="triggerMsgDialog.visible" width="600px" append-to-body>
      <div v-html="triggerMsgDialog.content"></div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="triggerMsgDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 清理日志弹窗 -->
    <el-dialog title="清理日志" v-model="clearDialog.visible" width="400px" append-to-body>
      <el-form :model="clearDialog.form" label-width="100px">
        <el-form-item label="执行器">
          <el-select v-model="clearDialog.form.jobGroup" disabled style="width: 100%">
            <el-option
              :label="queryParams.jobGroup ? sys_job_group.find((item) => item.id === queryParams.jobGroup)?.title : '全部'"
              :value="queryParams.jobGroup || ''"/>
          </el-select>
        </el-form-item>
        <el-form-item label="任务">
          <el-select v-model="clearDialog.form.jobId" disabled style="width: 100%">
            <el-option
              :label="queryParams.jobId ? jobList.find((item) => item.id === queryParams.jobId)?.jobDesc : '全部'"
              :value="queryParams.jobId || ''"/>
          </el-select>
        </el-form-item>
        <el-form-item label="清理方式">
          <el-select v-model="clearDialog.form.type" placeholder="请选择清理方式" style="width: 100%">
            <el-option label="清理一个月之前日志数据" :value="1"/>
            <el-option label="清理三个月之前日志数据" :value="2"/>
            <el-option label="清理六个月之前日志数据" :value="3"/>
            <el-option label="清理一年之前日志数据" :value="4"/>
            <el-option label="清理一千条以前日志数据" :value="5"/>
            <el-option label="清理一万条以前日志数据" :value="6"/>
            <el-option label="清理三万条以前日志数据" :value="7"/>
            <el-option label="清理十万条以前日志数据" :value="8"/>
            <el-option label="清理所有日志数据" :value="9"/>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmClear">确 定</el-button>
          <el-button @click="clearDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 执行日志弹窗 -->
    <el-dialog title="执行日志" v-model="logDialog.visible" width="800px" append-to-body>
      <div class="log-header">
        <el-button type="primary" icon="Refresh" @click="refreshLog" :loading="logDialog.loading"> 刷新</el-button>
      </div>
      <div class="log-content">
        <pre v-if="logDialog.content">{{ logDialog.content }}</pre>
        <div v-else class="empty-log">暂无执行日志</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="logDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {ref, onMounted} from "vue"
import {listJobLog, listJob, clearJobLog, getJobLog, killJob, listGroup} from "@/api/monitor/job"
import {parseTime} from "@/utils/ruoyi"
import {useRoute} from "vue-router"

defineOptions({
  name: "JobLog",
})

const {proxy} = getCurrentInstance()
const route = useRoute()

// ========== 基础数据 ==========
/** 任务组名字典 */
const sys_job_group = ref([])
/** 加载状态 */
const loading = ref(false)
/** 总条数 */
const total = ref(0)
/** 日志列表数据 */
const jobLogList = ref([])
/** 任务列表数据 */
const jobList = ref([])

// ========== 查询参数 ==========
/** 查询参数对象 */
const queryParams = ref({
  pageNum: 1, // 当前页码
  pageSize: 10, // 每页条数
  jobGroup: "", // 执行器
  jobId: "", // 任务ID
  logStatus: -1, // 执行状态
})

// ========== 弹窗数据 ==========
/** 调度备注弹窗 */
const triggerMsgDialog = ref({
  visible: false, // 是否显示
  content: "", // 备注内容
})

/** 执行日志弹窗 */
const logDialog = ref({
  visible: false, // 是否显示
  content: "", // 日志内容
  loading: false, // 刷新按钮loading状态
  currentLogId: null, // 当前查看的日志ID
})

/** 清理日志弹窗 */
const clearDialog = ref({
  visible: false, // 是否显示
  form: {
    jobGroup: "", // 执行器
    jobId: "", // 任务ID
    type: 1, // 清理方式
  },
})

// ========== 日期处理 ==========
/** 初始化日期范围为今天 */
const initDateRange = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, "0")
  const day = String(today.getDate()).padStart(2, "0")
  return [`${year}-${month}-${day} 00:00:00`, `${year}-${month}-${day} 23:59:59`]
}

/** 日期范围 */
const dateRange = ref(initDateRange())

// ========== 方法定义 ==========
/** 获取日志列表数据 */
const getList = () => {
  loading.value = true
  // 处理查询参数
  const params = {
    ...queryParams.value,
    filterTime: dateRange.value ? dateRange.value.join(" - ") : undefined,
  }

  listJobLog(params).then((response) => {
    jobLogList.value = response.data
    total.value = response.total
    loading.value = false
  })
}

/** 执行器变化时获取对应的任务列表 */
const handleJobGroupChange = async (value) => {
  queryParams.value.jobId = ""
  if (value) {
    try {
      const res = await listJob({jobGroup: value})
      jobList.value = res.data
    } catch (error) {
      console.error("获取任务列表失败:", error)
      jobList.value = []
    }
  } else {
    jobList.value = []
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = initDateRange()
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    jobGroup: "",
    jobId: "",
    logStatus: -1,
  }
  jobList.value = []
  handleQuery()
}

/** 查看调度备注 */
const viewTriggerMsg = (msg) => {
  triggerMsgDialog.value.content = msg
  triggerMsgDialog.value.visible = true
}

/** 查看执行日志 */
const handleViewLog = async (row) => {
  logDialog.value.visible = true
  logDialog.value.content = "加载中..."
  logDialog.value.currentLogId = row.id
  await refreshLog()
}

/** 刷新执行日志 */
const refreshLog = async () => {
  if (!logDialog.value.currentLogId) return

  logDialog.value.loading = true
  try {
    // TODO: 接口报错
    const res = await getJobLog(logDialog.value.currentLogId, 1)
    logDialog.value.content = res.data || "暂无执行日志"
  } catch (error) {
    console.error("获取执行日志失败:", error)
    logDialog.value.content = "获取执行日志失败"
  } finally {
    logDialog.value.loading = false
  }
}

/** 终止任务 */
const handleKill = (row) => {
  proxy.$modal.confirm("确认终止该任务吗？").then(() => {
    // TODO: 接口报错
    killJob(row.id).then((res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess("终止成功")
        getList()
      }
    })
  })
}

/** 打开清理日志弹窗 */
const handleClear = () => {
  clearDialog.value.visible = true
  clearDialog.value.form = {
    jobGroup: queryParams.value.jobGroup,
    jobId: queryParams.value.jobId,
    type: 1,
  }
}

/** 确认清理日志 */
const confirmClear = () => {
  proxy.$modal.confirm("确认清理日志吗？").then(() => {
    clearJobLog(clearDialog.value.form).then((res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess("清理成功")
        clearDialog.value.visible = false
        getList()
      }
    })
  })
}

/** 查询任务组列表 */
const getListGroup = () => {
  listGroup().then((response) => {
    sys_job_group.value = response.data
  })
}

onMounted(async () => {
  getListGroup()
  // 初始化查询
  if (route.query && route.query.jobId && route.query.jobGroup) {
    loading.value = true
    const jobGroup = Number(route.query.jobGroup)
    const jobId = Number(route.query.jobId)
    await handleJobGroupChange(jobGroup)
    queryParams.value.jobGroup = jobGroup
    queryParams.value.jobId = jobId

    getList()
  } else {
    getList()
  }
})
</script>

<style lang="scss" scoped>
/** 日志弹窗头部 */
.log-header {
  margin-bottom: 10px;
  text-align: right;
}

/** 日志内容区域 */
.log-content {
  height: 500px;
  overflow-y: auto;
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 16px;
  border-radius: 4px;
  font-family: Consolas, Monaco, "Courier New", monospace;

  /** 日志文本 */
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  /** 空日志提示 */
  .empty-log {
    text-align: center;
    padding: 20px;
    color: #909399;
  }
}

.text-end {
  text-align: right;
  margin-bottom: 10px;
}
</style>
