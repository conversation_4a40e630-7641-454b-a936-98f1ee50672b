<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="form-container">
      <el-form-item label="执行器" prop="jobGroup">
        <el-select v-model="queryParams.jobGroup" placeholder="请选择执行器" clearable>
          <el-option v-for="dict in sys_job_group" :key="dict.id" :label="dict.title" :value="dict.id" />
        </el-select>
      </el-form-item>
      <el-form-item prop="triggerStatus">
        <el-select v-model="queryParams.triggerStatus" placeholder="请选择状态" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="停用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item prop="jobDesc">
        <el-input v-model="queryParams.jobDesc" placeholder="请输入任务描述" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item prop="executorHandler">
        <el-input v-model="queryParams.executorHandler" placeholder="请输入JobHandler" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item prop="author">
        <el-input v-model="queryParams.author" placeholder="请输入负责人" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
          <el-button type="success" icon="CirclePlus" @click="handleAdd">新增</el-button>
      </div>
      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="jobList" @selection-change="handleSelectionChange" stripe>
        <el-table-column label="任务ID" prop="id" width="80" />
        <el-table-column label="任务描述" prop="jobDesc" :show-overflow-tooltip="true" />
        <el-table-column label="调度类型" prop="scheduleType" :show-overflow-tooltip="true">
          <template #default="scope"> {{ scope.row.scheduleType }}：{{ scope.row.scheduleConf }}</template>
        </el-table-column>
        <el-table-column label="运行模式" prop="glueType" :show-overflow-tooltip="true">
          <template #default="scope"> {{ scope.row.glueType }}：{{ scope.row.executorHandler }}</template>
        </el-table-column>
        <el-table-column label="负责人" prop="author" />
        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.triggerStatus === 1 ? 'success' : 'info'">
              {{ scope.row.triggerStatus === 1 ? "启用" : "停用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-dropdown split-button type="primary" @command="(command) => handleCommand(command, scope.row)">
              操作
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="executeOnce" icon="VideoPlay">执行一次</el-dropdown-item>
                  <el-dropdown-item command="log" icon="List">查询日志</el-dropdown-item>
                  <el-dropdown-item command="queryNode" icon="Monitor">注册节点</el-dropdown-item>
                  <el-dropdown-item command="nextTime" icon="Timer">下次执行时间</el-dropdown-item>
                  <el-dropdown-item command="triggerStatus" :icon="scope.row.triggerStatus === 1 ? 'VideoPause' : 'VideoPlay'" divided>
                    {{ scope.row.triggerStatus === 1 ? "停止" : "启动" }}
                  </el-dropdown-item>
                  <el-dropdown-item command="edit" icon="Edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="delete" icon="Delete">删除</el-dropdown-item>
                  <el-dropdown-item command="copy" icon="DocumentCopy">复制</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>



    <!-- 添加或修改任务对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body @before-close="reset">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <div class="dialog-subtitle">基础配置</div>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="jobGroup" label="执行器：">
              <el-select v-model="form.jobGroup" placeholder="请选择执行器">
                <el-option v-for="dict in sys_job_group" :key="dict.id" :label="dict.title" :value="dict.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="jobDesc" label="任务描述：">
              <el-input v-model="form.jobDesc" placeholder="请输入任务描述" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="author" label="负责人：">
              <el-input v-model="form.author" placeholder="请输入负责人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="alarmEmail" label="报警邮件：">
              <el-input v-model="form.alarmEmail" placeholder="请输入报警邮件，多个用逗号分隔" />
            </el-form-item>
          </el-col>
        </el-row>

        <div class="dialog-subtitle">调度配置</div>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="scheduleType" label="调度类型：">
              <el-select v-model="form.scheduleType" @change="handleScheduleTypeChange">
                <el-option v-for="item in enumAdjustType" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.scheduleType !== 'NONE'">
            <el-form-item :label="form.scheduleType === 'CRON' ? 'Cron表达式' : '固定速度(秒)'" prop="scheduleConf">
              <el-input v-model="form.scheduleConf">
                <template #append v-if="form.scheduleType === 'CRON'">
                  <el-button @click="handleShowCron">
                    <el-icon>
                      <Edit />
                    </el-icon>
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="dialog-subtitle">执行配置</div>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="glueType" label="运行模式：">
              <el-select v-model="form.glueType">
                <el-option v-for="item in enumRunMode" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="executorHandler" label="JobHandler：">
              <el-input v-model="form.executorHandler" :disabled="form.glueType !== 'BEAN'" placeholder="请输入JobHandler" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="executorParam" label="任务参数：">
              <el-input v-model="form.executorParam" type="textarea" placeholder="请输入任务参数" />
            </el-form-item>
          </el-col>
        </el-row>

        <div class="dialog-subtitle">高级配置</div>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="executorRouteStrategy" label="路由策略：">
              <el-select v-model="form.executorRouteStrategy">
                <el-option v-for="item in executorRouteStrategyEnum" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="childJobId" label="子任务ID：">
              <el-input v-model="form.childJobId" placeholder="请输入子任务ID，多个逗号分隔" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="misfireStrategy" label="调度过期策略：">
              <el-select v-model="form.misfireStrategy">
                <el-option v-for="item in misfireStrategyEnum" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="executorBlockStrategy" label="阻塞处理策略：">
              <el-select v-model="form.executorBlockStrategy">
                <el-option v-for="item in executorBlockStrategyEnum" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="executorTimeout" label="任务超时时间：">
              <el-input v-model="form.executorTimeout" placeholder="任务超时时间，单位秒，大于 0 时生效" type="number" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="executorFailRetryCount" label="失败重试次数：">
              <el-input v-model="form.executorFailRetryCount" placeholder="失败重试次数，大于 0 时生效" type="number" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog title="注册节点" v-model="nodeDialog.visible" width="400px">
      <div class="registry-list">
        <template v-if="nodeDialog.list && nodeDialog.list.length > 0">
          <div v-for="(item, index) in nodeDialog.list" :key="index" class="registry-item">
            <div class="registry-index">{{ index + 1 }}.</div>
            <el-tag type="success">{{ item }}</el-tag>
          </div>
        </template>
        <el-empty v-else description="暂无注册节点" />
      </div>
      <template #footer>
        <el-button type="primary" @click="nodeDialog.visible = false">确 定</el-button>
      </template>
    </el-dialog>

    <!-- 下次执行时间对话框 -->
    <el-dialog title="下次执行时间" v-model="nextTimeDialog.visible" width="600px" append-to-body>
      <div class="next-time">
        <template v-if="nextTimeDialog.times && nextTimeDialog.times.length > 0">
          <div v-for="(time, index) in nextTimeDialog.times" :key="index" class="next-time-item">
            <div class="time-index">{{ index + 1 }}.</div>
            <el-tag type="success">{{ time }}</el-tag>
          </div>
        </template>
        <el-empty v-else description="暂无执行时间" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="nextTimeDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Cron表达式生成器对话框 -->
    <el-dialog title="Cron表达式生成器" v-model="openCron" append-to-body destroy-on-close>
      <crontab ref="crontabRef" @hide="openCron = false" @fill="crontabFill" :expression="expression"></crontab>
    </el-dialog>

    <!-- 执行一次弹窗 -->
    <el-dialog title="执行一次" v-model="executeDialog.visible" width="500px" append-to-body>
      <el-form :model="executeDialog.form" label-width="100px">
        <el-form-item label="任务参数：">
          <el-input v-model="executeDialog.form.executorParam" type="textarea" :rows="4" placeholder="请输入任务参数" />
        </el-form-item>
        <el-form-item label="机器地址：">
          <el-input v-model="executeDialog.form.executorAddress" type="textarea" :rows="4" placeholder="请输入本次执行的机器地址，为空则从执行器获取" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmExecute">保 存</el-button>
          <el-button @click="executeDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getCurrentInstance, ref, onMounted } from "vue"
import { enumAdjustType, enumRunMode, executorRouteStrategyEnum, misfireStrategyEnum, executorBlockStrategyEnum } from "./enum"
import { listJob, delJobInfo, addJobInfo, updateJobInfo, runJob, listGroup, getNextTriggerTime, getJobDetail, startJob, stopJob, getJob } from "@/api/monitor/job"
import Crontab from "@/components/Crontab"

defineOptions({
  name: "JobInfo",
})

const { proxy } = getCurrentInstance()

// 遮罩层
const loading = ref(false)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 定时任务表格数据
const jobList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)
// 任务组名字典
const sys_job_group = ref([])

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  jobGroup: undefined,
  triggerStatus: undefined,
  jobDesc: undefined,
  executorHandler: undefined,
  author: undefined,
})

// 表单参数
const form = ref({
  id: undefined,
  jobGroup: undefined,
  jobDesc: "",
  author: "",
  alarmEmail: "",
  scheduleType: "NONE",
  scheduleConf: "",
  glueType: "BEAN",
  executorHandler: "",
  executorParam: "",
  executorRouteStrategy: "FIRST",
  childJobId: "",
  misfireStrategy: "DO_NOTHING",
  executorBlockStrategy: "SERIAL_EXECUTION",
  executorTimeout: "",
  executorFailRetryCount: "",
})

// 表单验证
const rules = {
  jobGroup: [{ required: true, message: "请选择任务组名", trigger: "change" }],
  jobDesc: [{ required: true, message: "请输入任务描述", trigger: "blur" }],
  author: [{ required: true, message: "请输入负责人", trigger: "blur" }],
  scheduleType: [{ required: true, message: "请选择调度类型", trigger: "change" }],
  glueType: [{ required: true, message: "请选择运行模式", trigger: "change" }],
  scheduleConf: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (form.value.scheduleType === "NONE") {
          callback()
        } else if (!value) {
          callback(new Error(form.value.scheduleType === "CRON" ? "请输入Cron表达式" : "请输入固定速度"))
        } else if (form.value.scheduleType === "FIXED_RATE" && !/^\d+$/.test(value)) {
          callback(new Error("固定速度必须为正整数"))
        } else {
          callback()
        }
      },
      trigger: "blur",
    },
  ],
  executorHandler: [{ required: true, message: "请输入JobHandler", trigger: "blur" }],
  executorTimeout: [{ required: true, message: "请输入任务超时时间：", trigger: "blur" }],
  executorFailRetryCount: [{ required: true, message: "请输入失败重试次数", trigger: "blur" }],
}

// 节点对话框数据
const nodeDialog = ref({
  visible: false,
  list: [],
})

// 下次执行时间对话框数据
const nextTimeDialog = ref({
  visible: false,
  times: [],
})

// 添加 form 的 ref 声明
const formRef = ref(null)
const queryRef = ref(null)

// 添加响应式变量
const openCron = ref(false)
const expression = ref("")
const executeDialog = ref({
  visible: false,
  form: {
    id: undefined,
    executorParam: "",
    executorAddress: "",
  },
})

/** 查询定时任务列表 */
function getList() {
  loading.value = true
  listJob(queryParams.value).then((response) => {
    jobList.value = response.data
    total.value = response.total
    loading.value = false
  })
}

/** 查询任务组列表 */
function getListGroup() {
  listGroup().then((response) => {
    sys_job_group.value = response.data
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    jobGroup: undefined,
    jobDesc: "",
    author: "",
    alarmEmail: "",
    scheduleType: "NONE",
    scheduleConf: "",
    glueType: "BEAN",
    executorHandler: "",
    executorParam: "",
    executorRouteStrategy: "FIRST",
    childJobId: "",
    misfireStrategy: "DO_NOTHING",
    executorBlockStrategy: "SERIAL_EXECUTION",
    executorTimeout: "",
    executorFailRetryCount: "",
  }
  formRef.value?.resetFields()
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryRef.value?.resetFields()
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 任务状态修改 */
function handleStatusChange(row) {
  let text = row.triggerStatus === 0 ? "启用" : "停用"
  proxy.$modal
    .confirm("确认要" + text + "任务吗？")
    .then(function () {
      if (row.triggerStatus === 0) {
        startJob(row.id).then(() => {
          proxy.$modal.msgSuccess("启用成功")
          getList()
        })
      } else {
        stopJob(row.id).then(() => {
          proxy.$modal.msgSuccess("停用成功")
          getList()
        })
      }
    })
    .catch((e) => {})
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case "executeOnce":
      handleRun(row)
      break
    case "log":
      handleLog(row)
      break
    case "queryNode":
      handleQueryNode(row)
      break
    case "nextTime":
      handleNextTime(row)
      break
    case "triggerStatus":
      handleStatusChange(row)
      break
    case "edit":
      handleUpdate(row)
      break
    case "delete":
      handleDelete(row)
      break
    case "copy":
      handleCopy(row)
      break
    default:
      break
  }
}

/** 立即执行一次 */
async function handleRun(row) {
  executeDialog.value.visible = true
  executeDialog.value.form.jobId = row.id
  executeDialog.value.form.executorParam = row.executorParam || ""
}

/** 确认执行 */
async function confirmExecute() {
  try {
    await runJob(executeDialog.value.form)
    proxy.$modal.msgSuccess("执行成功")
    executeDialog.value.visible = false
  } catch (error) {
    console.error("执行失败:", error)
  }
}

/** 查看节点 */
async function handleQueryNode(row) {
  try {
    const res = await getJob(row.id)
    nodeDialog.value.list = res.data.registryList || []
    nodeDialog.value.visible = true
  } catch (error) {
    console.error("获取节点失败:", error)
  }
}

/** 查看下次执行时间 */
async function handleNextTime(row) {
  if (row.scheduleType !== "CRON") {
    proxy.$modal.msgError("只有CRON类型的任务支持查看下次执行时间")
    return
  }
  try {
    const response = await getNextTriggerTime({
      scheduleConf: row.scheduleConf,
      scheduleType: row.scheduleType,
    })
    nextTimeDialog.value.times = response.data
    if (nextTimeDialog.value.times.length === 0) {
      proxy.$modal.msg("该任务没有下次执行时间")
      return
    }
    nextTimeDialog.value.visible = true
  } catch (error) {
    console.error("获取下次执行时间失败:", error)
  }
}

/** 复制任务 */
async function handleCopy(row) {
  try {
    reset()
    getJobDetail(row.id).then((res) => {
      form.value = res.data
      form.value.id = null
      open.value = true
      title.value = "复制任务"
    })
  } catch (error) {
    console.error("复制失败:", error)
  }
}

/** 任务日志 */
function handleLog(row) {
  const jobId = row.id
  proxy.$router.push({ path: "/job/log", query: { jobId: jobId, jobGroup: row.jobGroup } })
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加任务"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const jobId = row.id
  getJobDetail(jobId).then((response) => {
    form.value = response.data
    open.value = true
    title.value = "修改任务"
  })
}

/** 提交按钮 */
function submitForm() {
  formRef.value?.validate((valid) => {
    if (valid) {
      if (form.value.id) {
        updateJobInfo(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addJobInfo(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const jobIds = row.id || ids.value.join(",")
  if (!jobIds) return
  proxy.$modal.confirm("是否确认删除任务").then(function () {
    return delJobInfo(jobIds).then((res) => {
      if (res.code === 200) {
        getList()
        proxy.$modal.msgSuccess("删除成功")
      }
    })
  })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "monitor/job/export",
    {
      ...queryParams.value,
    },
    `job_${new Date().getTime()}.xlsx`
  )
}

/** 调度类型变化 */
function handleScheduleTypeChange() {
  form.value.scheduleConf = ""
}

/** 添加 Cron 表达式处理函数 */
function handleShowCron() {
  expression.value = form.value.scheduleConf
  openCron.value = true
}

function crontabFill(value) {
  form.value.scheduleConf = value
}

onMounted(() => {
  getList()
  getListGroup()
})
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  margin: 0 0 15px;
  padding-bottom: 10px;
  font-size: 15px;
  color: #606266;
  border-bottom: 1px solid #dcdfe6;
}

.next-time {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;

  .next-time-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    .time-index {
      width: 40px;
      color: #909399;
      font-size: 13px;
    }

    .el-tag {
      flex: 1;
      text-align: left;
      font-family: monospace;
      padding: 8px 12px;
      font-size: 14px;
    }
  }
}

.registry-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;

  .registry-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    .registry-index {
      width: 40px;
      color: #909399;
      font-size: 13px;
    }

    .el-tag {
      flex: 1;
      text-align: left;
      font-family: monospace;
    }
  }
}
</style>
