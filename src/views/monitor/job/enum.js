/**
 * Enum.js
 */

/**
 * 调配类型
 */
export const enumAdjustType = [
  {
    value: "NONE",
    label: "无",
  },
  {
    value: "CRON",
    label: "CRON",
  },
  {
    value: "FIX_RATE",
    label: "固定速度",
  },
]

/**
 * 运行模式
 */
export const enumRunMode = [
  {
    value: "BEAN",
    label: "BEAN",
  },
  {
    value: "GLUE_GROOVY",
    label: "GLUE(Java)",
  },
  {
    value: "GLUE_SHELL",
    label: "GLUE(Shell)",
  },
  {
    value: "GLUE_PYTHON",
    label: "GLUE(Python)",
  },
  {
    value: "GLUE_PHP",
    label: "GLUE(PHP)",
  },
  {
    value: "GLUE_NODEJS",
    label: "GLUE(Nodejs)",
  },
  {
    value: "GLUE_POWERSHELL",
    label: "GLUE(PowerShell)",
  },
]

// 添加更多枚举选项
export const executorRouteStrategyEnum = [
  { value: "FIRST", label: "第一个" },
  { value: "LAST", label: "最后一个" },
  { value: "ROUND", label: "轮询" },
  { value: "RANDOM", label: "随机" },
  { value: "CONSISTENT_HASH", label: "一致性HASH" },
  { value: "LEAST_FREQUENTLY_USED", label: "最不经常使用" },
  { value: "LEAST_RECENTLY_USED", label: "最近最久未使用" },
  { value: "FAILOVER", label: "故障转移" },
  { value: "BUSYOVER", label: "忙碌转移" },
  { value: "SHARDING_BROADCAST", label: "分片广播" },
]

export const misfireStrategyEnum = [
  { value: "DO_NOTHING", label: "忽略" },
  { value: "FIRE_ONCE_NOW", label: "立即执行一次" },
]

export const executorBlockStrategyEnum = [
  { value: "SERIAL_EXECUTION", label: "单机串行" },
  { value: "DISCARD_LATER", label: "丢弃后续调度" },
  { value: "COVER_EARLY", label: "覆盖之前调度" },
]
