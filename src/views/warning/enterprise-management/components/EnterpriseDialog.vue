<script setup>
import { ref, defineProps, defineEmits, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { addOrUpdateEnterprise } from "@/api/warning";
import MinMap from "@/components/Map/plugins/drawTool";
import { ENTERPRISE_TYPES } from "@/enums/warning";

const visible = defineModel("visible", { default: false });

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  villageOptions: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["getList"]);

const isEdit = computed(() => {
  return !!props.formData.id;
});

// 表单数据
const form = ref({
  id: null,
  name: "",
  type: "",
  villageAdcd: "",
  staffCount: null,
  houseCount: null,
  area: null,
  address: "",
  longitude: null,
  latitude: null,
});

// 表单校验规则
const rules = {
  name: [
    { required: true, message: "请输入企事业单位名称", trigger: "blur" },
    { max: 50, message: "企事业单位名称不能超过50个字符", trigger: "blur" },
  ],
  address: [
    { max: 100, message: "企事业单位地址不能超过100个字符", trigger: "blur" },
  ],
};

// 表单引用
const formRef = ref(null);

// 保存表单
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (!valid) return;

    try {
      const res = await addOrUpdateEnterprise(form.value);
      if (res.code === 200) {
        ElMessage.success(form.value.id ? "修改成功" : "新增成功");
        visible.value = false;
        emit("getList");
      }
    } catch (error) {
      console.error("保存失败:", error);
    }
  });
};

const closeDialog = () => {
  visible.value = false;
  formRef.value.resetFields();
  form.value = {};
};

// 监听visible变化
watch(
  () => visible.value,
  (val) => {
    if (val) {
      form.value = { ...props.formData };
    }
  }
);
</script>

<template>
  <el-dialog
    :title="title"
    v-model="visible"
    width="800px"
    @close="closeDialog"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="企事业单位名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入企事业单位名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企事业单位类型" prop="type">
            <el-select
              v-model="form.type"
              placeholder="请选择企事业单位类型"
              class="w-full"
              clearable
            >
              <el-option
                v-for="item in ENTERPRISE_TYPES"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经度" prop="longitude">
            <el-input-number
              class="w-full"
              v-model="form.longitude"
              placeholder="请输入经度"
              clearable
              :min="-180"
              :max="180"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="latitude">
            <el-input-number
              class="w-full"
              v-model="form.latitude"
              placeholder="请输入纬度"
              clearable
              :min="-90"
              :max="90"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在村社" prop="villageAdcd">
            <el-select
              v-model="form.villageAdcd"
              placeholder="请选择所在村社"
              class="w-full"
              clearable
            >
              <el-option
                v-for="item in villageOptions"
                :key="item.adcd"
                :label="item.adnm"
                :value="item.adcd"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地址" prop="address">
            <el-input
              v-model="form.address"
              placeholder="请输入地址"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="在岗人数（人）" prop="staffCount">
            <el-input-number
              v-model="form.staffCount"
              :min="0"
              :max="999999"
              :precision="0"
              placeholder="请输入在岗人数"
              class="w-full"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="房屋数量（座）" prop="houseCount">
            <el-input-number
              v-model="form.houseCount"
              :min="0"
              :max="9999"
              :precision="0"
              placeholder="请输入房屋数量"
              class="w-full"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="占地面积（亩）" prop="area">
            <el-input-number
              v-model="form.area"
              type="number"
              :min="0"
              :precision="2"
              placeholder="请输入占地面积"
              class="w-full"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="24" v-if="isEdit">
          <el-form-item label="空间预览" />
        </el-col>
      </el-row>
    </el-form>
    <div class="map-preview" v-if="isEdit">
      <min-map
        :geom="form.geom"
        :geoType="'point'"
        :points="[form.longitude, form.latitude]"
        :show-tool="false"
        class="w-full h-full"
      ></min-map>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.map-preview {
  width: 100%;
  height: 350px;
}
</style>
