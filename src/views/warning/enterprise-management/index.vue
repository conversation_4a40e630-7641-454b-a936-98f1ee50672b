<script setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  queryEnterpriseList,
  deleteEnterprise,
  villageList,
} from "@/api/warning";
import EnterpriseDialog from "./components/EnterpriseDialog.vue";
import { ENTERPRISE_TYPES, getTypeLabel } from "@/enums/warning";

defineOptions({
  name: "EnterpriseManagement",
});

// 查询参数
const queryParams = ref({
  name: "",
  type: "",
  villageAdcd: "",
});

// 表格数据
const tableData = ref([]);
const total = ref(0);

// 表格加载状态
const loading = ref(false);

// 分页参数
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});

// 弹窗相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const dialogFormData = ref({});

// 查询列表
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      ...queryParams.value,
      pageNum: pageParams.value.pageNum,
      pageSize: pageParams.value.pageSize,
    };
    const res = await queryEnterpriseList(params);
    if (res.code === 200) {
      tableData.value = res.rows;
      total.value = res.total;
    }
  } catch (error) {
    console.error("获取企业列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 重置查询
const resetQuery = () => {
  queryParams.value = {
    name: "",
    type: "",
    villageAdcd: "",
  };
  getList();
};

// 打开新增弹窗
const handleAdd = () => {
  dialogVisible.value = true;
  dialogTitle.value = "新增企事业单位";
  dialogFormData.value = {};
};

// 打开编辑弹窗
const handleEdit = (row) => {
  dialogVisible.value = true;
  dialogTitle.value = "编辑企事业单位 - " + row.name;
  dialogFormData.value = row;
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("确认要删除该企事业单位信息吗？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      const res = await deleteEnterprise(row.id);
      if (res.code === 200) {
        ElMessage.success("删除成功");
        getList();
      }
    } catch (error) {
      console.error("删除失败:", error);
    }
  });
};

// 村社选项
const villageOptions = ref([]);

// 获取村社列表
const getVillageList = async () => {
  try {
    const res = await villageList({
      pageNum: 1,
      pageSize: 9999,
    });
    if (res.code === 200) {
      villageOptions.value = res.data.records || [];
    }
  } catch (error) {
    console.error("获取村社列表失败:", error);
  }
};
onMounted(() => {
  getVillageList();
  getList();
});
</script>

<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="企事业单位名称">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入企事业单位名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="企事业单位类型">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择企事业单位类型"
          clearable
        >
          <el-option
            v-for="item in ENTERPRISE_TYPES"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所在村社">
        <el-select
          v-model="queryParams.villageAdcd"
          placeholder="请选择所在村社"
          clearable
        >
          <el-option
            v-for="item in villageOptions"
            :key="item.adcd"
            :label="item.adnm"
            :value="item.adcd"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search"
          >查询</el-button
        >
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <!-- 操作按钮 -->
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd"
          >新增</el-button
        >
      </div>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableData" stripe>
        <el-table-column type="index" label="序号" width="50" />
        <el-table-column prop="name" label="企事业单位名称" />
        <el-table-column prop="type" label="企事业单位类型">
          <template #default="{ row }">
            {{ getTypeLabel(row.type, "ENTERPRISE_TYPES") }}
          </template>
        </el-table-column>
        <el-table-column prop="staffCount" label="在岗人数（人）" />
        <el-table-column prop="houseCount" label="房屋数量（座）" />
        <el-table-column prop="area" label="占地面积（亩）" />
        <el-table-column prop="address" label="地址" />
        <el-table-column prop="updateTime" label="更新时间" />
        <el-table-column label="操作" width="180">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)" icon="Edit"
              >编辑</el-button
            >
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
              icon="Delete"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-model:page="pageParams.pageNum"
        v-model:limit="pageParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 新增/编辑弹窗 -->
    <EnterpriseDialog
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :form-data="dialogFormData"
      :village-options="villageOptions"
      @getList="getList"
    />
  </div>
</template>

<style lang="scss" scoped>
.el-table {
  height: calc(100vh - 310px);
}
</style>
