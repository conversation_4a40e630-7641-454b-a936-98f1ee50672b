<script setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { queryRiskProblem, deleteRiskProblem } from "@/api/warning";
import { selectRvList } from "@/api/watershed/ads";
import { formatRiverTreeData } from "@/utils";
import { RISK_TYPES, getTypeLabel } from "@/enums/warning";
import RiskDialog from "./components/RiskDialog.vue";
import VillageDialog from "./components/VillageDialog.vue";

defineOptions({
  name: "HiddenRisks",
});

// 查询参数
const queryParams = ref({
  name: "",
  type: "",
  rvCode: "",
  villageNames: "",
});

// 表格数据
const tableData = ref([]);
const total = ref(0);

// 河流数据
const riverOptions = ref([]);

// 表格加载状态
const loading = ref(false);

// 分页参数
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});

// 弹窗表单
const dialog = ref(false);
const dialogTitle = ref("");
const form = ref({
  id: null,
  name: "",
  type: "",
  rvCode: "",
  longitude: "",
  latitude: "",
});

// 威胁村社弹窗
const villageDialog = ref(false);
const currentRiskName = ref("");
const currentRiskId = ref(null);

// 查询列表
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      ...queryParams.value,
      pageNum: pageParams.value.pageNum,
      pageSize: pageParams.value.pageSize,
    };
    const res = await queryRiskProblem(params);
    if (res.code === 200) {
      tableData.value = res.rows;
      total.value = res.total;
    }
  } catch (error) {
    console.error("获取列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 重置查询
const resetQuery = () => {
  queryParams.value = {
    name: "",
    type: "",
    rvCode: "",
    villageNames: "",
  };
  getList();
};

// 打开新增弹窗
const handleAdd = () => {
  dialog.value = true;
  dialogTitle.value = "新增风险隐患";
  form.value = {};
};

// 打开编辑弹窗
const handleEdit = (row) => {
  dialog.value = true;
  dialogTitle.value = "编辑风险隐患 - " + row.name;
  form.value = {
    id: row.id,
    name: row.name,
    type: row.type,
    rvCode: row.rvCode,
    longitude: row.longitude,
    latitude: row.latitude,
  };
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("确认要删除该风险隐患吗？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      const res = await deleteRiskProblem(row.id);
      if (res.code === 200) {
        ElMessage.success("删除成功");
        getList();
      }
    } catch (error) {
      console.error("删除失败:", error);
    }
  });
};

// 打开威胁村社弹窗
const handleVillage = (row) => {
  villageDialog.value = true;
  currentRiskName.value = row.name;
  currentRiskId.value = row.id;
};

// 查询河流列表
const getRiverList = async () => {
  const res = await selectRvList({
    pageNum: 1,
    pageSize: 10000,
    asNext: 0,
  });
  if (res.code === 200) {
    riverOptions.value = formatRiverTreeData(res.data);
  }
};

onMounted(() => {
  getRiverList();
  getList();
});
</script>

<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="风险隐患名称">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入风险隐患名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="风险隐患类型">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择风险隐患类型"
          clearable
        >
          <el-option
            v-for="item in RISK_TYPES"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属河流">
        <el-tree-select
          v-model="queryParams.rvCode"
          :data="riverOptions"
          filterable
          clearable
          :props="{ value: 'rvCode', label: 'rvName', children: 'children' }"
          value-key="id"
          placeholder="请选择所属河流"
          check-strictly
        />
      </el-form-item>
      <el-form-item label="威胁村社">
        <el-input
          v-model="queryParams.villageNames"
          placeholder="请输入威胁村社"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList" icon="Search"
          >查询</el-button
        >
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <!-- 操作按钮 -->
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd"
          >新增</el-button
        >
      </div>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableData" stripe>
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="name" label="风险隐患名称" />
        <el-table-column prop="type" label="风险隐患类型">
          <template #default="{ row }">
            {{ getTypeLabel(row.type, "RISK_TYPES") }}
          </template>
        </el-table-column>
        <el-table-column prop="riverName" label="所在河流" />
        <el-table-column prop="villageNames" label="威胁村社" />
        <el-table-column prop="updateTime" label="更新时间" />
        <el-table-column label="操作" width="260">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleVillage(row)"
              >威胁村社</el-button
            >
            <el-button type="primary" link @click="handleEdit(row)" icon="Edit"
              >编辑</el-button
            >
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
              icon="Delete"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-model:page="pageParams.pageNum"
        v-model:limit="pageParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 新增/编辑弹窗 -->
    <RiskDialog
      v-model:visible="dialog"
      :title="dialogTitle"
      :form-data="form"
      @getList="getList"
    />

    <!-- 威胁村社弹窗 -->
    <VillageDialog
      v-model:visible="villageDialog"
      :risk-name="currentRiskName"
      :risk-id="currentRiskId"
      @getList="getList"
    />
  </div>
</template>

<style lang="scss" scoped>
.el-table {
  height: calc(100vh - 310px);
}
</style>
