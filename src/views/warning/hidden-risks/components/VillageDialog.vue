<script setup>
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  queryRiskProblemFactor,
  addOrUpdateRiskProblemFactor,
} from "@/api/warning";
import { THREAT_TYPES } from "@/enums/warning";
import { villageList } from "@/api/warning/index";

const visible = defineModel("visible", { default: false });

const props = defineProps({
  riskName: {
    type: String,
    default: "",
  },
  riskId: {
    type: [String, Number],
    default: null,
  },
});

const emit = defineEmits(["getList"]);

// 威胁村社表格数据
const villageTableData = ref([]);

// 村社数据
const villageOptions = ref([]);

const loading = ref(false);
// 获取村社列表
const getVillageOptions = async () => {
  try {
    villageOptions.value = [];
    loading.value = true;
    const res = await villageList({
      pageNum: 1,
      pageSize: 1000,
      geoType: "pepole",
    });
    if (res.code === 200) {
      villageOptions.value = res.data.records.map((item) => ({
        value: item.adcd,
        label: item.adnm,
      }));
    }
  } catch (error) {
    console.error("获取村社列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 获取威胁村社数据
const getVillageList = async () => {
  try {
    const res = await queryRiskProblemFactor(props.riskId);
    if (res.code === 200) {
      villageTableData.value = res.rows.map((item) => ({
        id: item.id,
        villageAdcd: item.villageAdcd,
        type: item.type,
      }));
    }
  } catch (error) {
    console.error("获取威胁村社失败:", error);
  }
};

const handleClose = () => {
  visible.value = false;
  villageTableData.value = [];
};

// 保存威胁村社
const saveVillage = async () => {
  // 验证数据
  const invalidData = villageTableData.value.some(
    (item) => !item.villageAdcd || !item.type
  );
  if (invalidData) {
    ElMessage.warning("所有村社名称和威胁类型不能为空");
    return;
  }

  try {
    const data = villageTableData.value.map((item) => ({
      id: item.id,
      type: item.type,
      riskProblemId: props.riskId,
      villageAdcd: item.villageAdcd,
    }));
    const res = await addOrUpdateRiskProblemFactor({
      riskProblemId: props.riskId,
      dangerFactors: data,
    });
    if (res.code === 200) {
      ElMessage.success("保存成功");
      visible.value = false;
      emit("getList");
    }
  } catch (error) {
    console.error("保存威胁村社失败:", error);
  }
};

// 添加威胁村社
const handleAddVillage = () => {
  villageTableData.value.push({
    id: null,
    villageAdcd: "",
    type: "",
  });
};

// 删除威胁村社
const handleDeleteVillage = async (index) => {
  villageTableData.value.splice(index, 1);
  // const item = villageTableData.value[index];
  // if (item.id) {
  //   try {
  //     const res = await deleteRiskProblemFactor(item.id);
  //     if (res.code === 200) {
  //       ElMessage.success("删除成功");
  //       villageTableData.value.splice(index, 1);
  //     }
  //   } catch (error) {
  //     console.error("删除威胁村社失败:", error);
  //   }
  // } else {
  //   villageTableData.value.splice(index, 1);
  // }
};

// 监听visible变化
watch(
  () => visible.value,
  (val) => {
    if (val && props.riskId) {
      getVillageList();
      getVillageOptions();
    }
  }
);
</script>

<template>
  <el-dialog
    :title="'威胁村社 - ' + riskName"
    v-model="visible"
    width="600px"
    append-to-body
    @close="handleClose"
  >
    <div v-loading="loading">
      <el-button type="primary" @click="handleAddVillage">添加村社</el-button>
      <el-table :data="villageTableData" style="margin-top: 10px">
        <el-table-column label="村社名称">
          <template #default="{ row }">
            <el-select v-model="row.villageAdcd" placeholder="请选择村社">
              <el-option
                v-for="item in villageOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="威胁类型">
          <template #default="{ row }">
            <el-select v-model="row.type" placeholder="请选择威胁类型">
              <el-option
                v-for="item in THREAT_TYPES"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="{ $index }">
            <el-button type="danger" link @click="handleDeleteVillage($index)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <el-button type="primary" @click="saveVillage">保 存</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </template>
  </el-dialog>
</template>
