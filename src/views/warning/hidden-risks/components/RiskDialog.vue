<script setup>
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { addOrUpdateRiskProblem } from "@/api/warning";
import { selectRvList } from "@/api/watershed/ads";
import { formatRiverTreeData } from "@/utils";
import { RISK_TYPES } from "@/enums/warning";
import MinMap from "@/components/Map/plugins/drawTool";

const visible = defineModel("visible", { default: false });

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["getList"]);

// 是否为编辑
const isEdit = computed(() => !!props.formData.id);

// 表单 ref
const formRef = ref(null);

// 河流数据
const riverOptions = ref([]);

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入风险隐患名称", trigger: "blur" }],
  type: [{ required: true, message: "请选择风险隐患类型", trigger: "change" }],
  longitude: [
    {
      pattern:
        /^(-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/,
      message: "经度范围必须在-180到180之间",
      trigger: "blur",
    },
  ],
  latitude: [
    {
      pattern: /^(-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/,
      message: "纬度范围必须在-90到90之间",
      trigger: "blur",
    },
  ],
};

// 查询河流列表
const getRiverList = async () => {
  const res = await selectRvList({
    pageNum: 1,
    pageSize: 10000,
    asNext: 0,
  });
  if (res.code === 200) {
    riverOptions.value = formatRiverTreeData(res.data);
  }
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await addOrUpdateRiskProblem(props.formData);
        if (res.code === 200) {
          ElMessage.success("保存成功");
          visible.value = false;
          emit("getList");
        }
      } catch (error) {
        console.error("保存失败:", error);
      }
    }
  });
};

// 监听visible变化
watch(
  () => visible.value,
  (val) => {
    if (val) {
      getRiverList();
    }
  }
);
</script>

<template>
  <el-dialog
    :title="title"
    v-model="visible"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="110px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="风险隐患名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="风险隐患类型" prop="type">
            <el-select
              v-model="formData.type"
              placeholder="请选择风险隐患类型"
              class="w-full"
              clearable
            >
              <el-option
                v-for="item in RISK_TYPES"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经度" prop="longitude">
            <el-input
              v-model.number="formData.longitude"
              placeholder="请输入"
              type="number"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="latitude">
            <el-input
              v-model.number="formData.latitude"
              placeholder="请输入"
              type="number"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="所在河流">
            <el-tree-select
              v-model="formData.rvCode"
              :data="riverOptions"
              filterable
              clearable
              :props="{
                value: 'rvCode',
                label: 'rvName',
                children: 'children',
              }"
              value-key="id"
              placeholder="请选择所属河流"
              check-strictly
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="空间预览" v-if="isEdit" />
        </el-col>
      </el-row>
    </el-form>
    <div class="map-preview" v-if="isEdit">
      <min-map
        :geom="formData.geom"
        :geoType="'point'"
        :points="[formData.longitude, formData.latitude]"
        :show-tool="false"
        class="w-full h-full"
      ></min-map>
    </div>
    <template #footer>
      <el-button type="primary" @click="submitForm">保 存</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.map-preview {
  width: 100%;
  height: 350px;
}
</style>
