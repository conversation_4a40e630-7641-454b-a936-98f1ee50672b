<script setup>
import { testStcdRecord } from "@/api/warning";
import { getTypeLabel } from "@/enums/warning";

const dialogVisible = defineModel("dialogVisible", { default: false });
const addTable = defineModel("addTable", { default: () => [] });

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  villageData: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  stationData: {
    type: Array,
    required: true,
    default: () => [],
  },
  stationList: {
    type: Array,
    required: true,
    default: () => [],
  },
});

const emit = defineEmits(["save"]);

// 获取当前组件实例
const { proxy } = getCurrentInstance();

/**
 * 测试单个测站的数据记录
 * @param {Object} row - 当前行的数据对象
 */
const testOne = (row) => {
  if (!row.stcd || !row.warnType) {
    proxy.$modal.msgError("测站或预警类型不可为空！");
    return false;
  }
  testStcdRecord(row.stcd, row.warnType).then((res) => {
    row.date = res.msg || "无数据";
  });
};

/**
 * 验证测站和预警类型是否重复
 * @param {Object} row - 当前行的数据对象
 */
const validateDuplicate = (row) => {
  if (!row.stcd || !row.warnType) return;

  const duplicates = addTable.value.filter(
    (item) =>
      item !== row && item.stcd === row.stcd && item.warnType === row.warnType
  );

  if (duplicates.length > 0) {
    proxy.$modal.msgError("测站和预警类型不可重复！");
    row.stcd = "";
    row.warnType = "";
  }
};

/**
 * 删除指定行数据
 * @param {Object} row - 要删除的行数据
 */
const deleteOne = (row) => {
  if (addTable.value.length > 0) {
    addTable.value.splice(addTable.value.indexOf(row), 1);
  }
};

/**
 * 添加新行数据
 * 在添加新行前检查现有数据是否完整
 */
const addRow = () => {
  let status = true;
  addTable.value.forEach((item) => {
    if (!item.stcd || !item.warnType) {
      status = false;
    }
  });
  if (status) {
    addTable.value.push({
      stcd: "",
      warnType: "",
      date: "",
    });
  } else {
    proxy.$modal.msgWarning("请完善数据！确保数据完整性");
  }
};

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false;
};

/**
 * 保存数据
 * 进行数据完整性验证和重复性检查
 */
const handleSave = () => {
  if (addTable.value.length === 0) {
    proxy.$modal.msgError("请添加村社关联测站信息！");
    return false;
  }

  // 验证所有行的数据完整性
  const invalidData = addTable.value.some(
    (item) => !item.stcd || !item.warnType
  );
  if (invalidData) {
    proxy.$modal.msgError("请完善测站和预警类型信息！");
    return false;
  }

  // 验证是否有重复的测站和预警类型组合
  const combinations = new Set();
  const hasDuplicates = addTable.value.some((item) => {
    const key = `${item.stcd}-${item.warnType}`;
    if (combinations.has(key)) {
      return true;
    }
    combinations.add(key);
    return false;
  });

  if (hasDuplicates) {
    proxy.$modal.msgError("存在重复的测站和预警类型组合！");
    return false;
  }

  emit("save", addTable.value);
};
</script>

<template>
  <!-- 村社测站关联对话框组件 -->
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
    @close="handleClose"
  >
    <!-- 对话框头部信息 -->
    <div class="dialog-header">
      <div class="header-content">
        <div class="header-item">
          <span class="label">村社名称：</span>
          <span class="value">{{ villageData?.villageName }}</span>
        </div>
        <div class="header-item">
          <span class="label">防治区类型：</span>
          <span class="value">{{
            getTypeLabel(
              villageData.preventionZonesType,
              "PREVENTION_ZONES_TYPES"
            )
          }}</span>
        </div>
      </div>
    </div>
    <!-- 新增按钮 -->
    <el-button
      type="primary"
      plain
      icon="Plus"
      style="margin-bottom: 20px"
      @click="addRow"
      v-if="title.includes('编辑村社测站关联')"
      >新增</el-button
    >
    <!-- 测站关联表格 -->
    <el-table :data="addTable" :max-height="300" :style="{ width: '100%' }">
      <el-table-column label="序号" type="index" width="80" />
      <el-table-column prop="stnm" label="测站">
        <template #default="scope">
          <el-select
            v-model="scope.row.stcd"
            placeholder="请选择测站"
            :disabled="title === '查看村社测站关联'"
            @change="validateDuplicate(scope.row)"
          >
            <el-option
              v-for="item in stationList"
              :key="item.stcd"
              :label="item.stnm"
              :value="item.stcd"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="warnType" label="预警类型">
        <template #default="scope">
          <el-select
            v-model="scope.row.warnType"
            placeholder="请选择预警类型"
            :disabled="title === '查看村社测站关联'"
            @change="validateDuplicate(scope.row)"
          >
            <el-option label="雨情" :value="1" />
            <el-option label="河道水情" :value="2" />
            <el-option label="水库水情" :value="3" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="最近数据采集时间" />
      <el-table-column prop="address" label="操作" align="center">
        <template #default="{ row }">
          <el-link
            type="primary"
            size="mini"
            @click="testOne(row)"
            style="margin-right: 10px"
            >数据测试</el-link
          >
          <el-link
            type="danger"
            size="mini"
            @click="deleteOne(row)"
            v-if="title.includes('编辑村社测站关联')"
            style="margin-left: 10px"
            >删除</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 对话框底部按钮 -->
    <template #footer v-if="!title.includes('查看村社测站关联')">
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSave">保 存 </el-button>
        <el-button @click="handleClose" x>取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
// 对话框头部样式
.dialog-header {
  padding: 24px;
  background: #f8f9fc;
  border-radius: 8px;
  margin-bottom: 20px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
  }

  .header-item {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;

    .label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
    }

    .value {
      color: #303133;
      font-weight: 400;
    }
  }
}
</style>
