<script setup>
import { ref, onMounted } from "vue";
import { selectStaList } from "@/api/watershed/ads";
import { PREVENTION_ZONES_TYPES, getTypeLabel } from "@/enums/warning";
import {
  queryVillageStationList,
  saveVillageStationRelation,
  queryVillageStationDetail,
} from "@/api/warning";
import VillageStationDialog from "./components/VillageStationDialog.vue";
import AdcdTree from "@/components/AdcdTree/index.vue";

// 定义组件名称
defineOptions({
  name: "VillageStationRelation",
});

const { proxy } = getCurrentInstance();

// 页面状态管理
const loading = ref(false); // 加载状态
const dialogVisible = ref(false); // 弹窗显示状态
const dialogTitle = ref(""); // 弹窗标题

// 查询表单数据结构
const queryForm = ref({
  villageName: "", // 村社名称
  preventionZonesType: "", // 防治区类型
  pageNum: 1, // 当前页码
  pageSize: 20, // 每页条数
  adcd: "", // 行政区编码
});

// 数据列表相关
const tableData = ref([]); // 表格数据
const stationList = ref([]); // 测站列表数据
const total = ref(0); // 数据总数

// 弹窗数据
const villageData = ref({}); // 村社数据
const stationData = ref([]); // 测站关联数据

/**
 * 获取所有测站数据
 * 获取类型为 ZQ(水文站)、ZZ(水位站)、RR(雨量站)、PP(水库站) 的测站信息
 */
const getAllStation = async () => {
  const res = await selectStaList({
    pageNum: 1,
    pageSize: 999,
    stationTpyeList: ["ZQ", "ZZ", "RR", "PP"],
  });
  stationList.value = res.data.records || [];
};

/**
 * 获取村社测站关联列表数据
 * 根据查询条件获取村社测站关联信息
 */
const getList = () => {
  loading.value = true;
  queryVillageStationList(queryForm.value).then((res) => {
    tableData.value = res.rows || [];
    total.value = res.total || 0;
    loading.value = false;
  });
};

/**
 * 重置查询条件
 * 清空表单并重新获取数据
 */
const resetQuery = () => {
  proxy.$refs.queryFormRef.resetFields();
  getList();
};

/**
 * 查看村社测站关联详情
 * @param {Object} row - 行数据
 */
const handleSee = async (row) => {
  dialogTitle.value = "查看村社测站关联 - " + row.villageName;
  const detail = await queryVillageStationDetail(row.villageCode);
  stationData.value = detail.data || [];
  villageData.value = row;
  dialogVisible.value = true;
};

/**
 * 编辑村社测站关联
 * @param {Object} row - 行数据
 */
const handleEdit = async (row) => {
  dialogTitle.value = "编辑村社测站关联 - " + row.villageName;
  const detail = await queryVillageStationDetail(row.villageCode);
  stationData.value = detail.data || [];
  if (!stationData.value.length) {
    stationData.value = [
      {
        stcd: "",
        warnType: "",
      },
    ];
  }

  villageData.value = row;
  dialogVisible.value = true;
};

/**
 * 保存村社测站关联数据
 * @param {Array} tableData - 测站关联数据
 */
const handleSave = (tableData) => {
  const submitData = tableData.map((item) => ({
    ...item,
  }));

  saveVillageStationRelation(villageData.value.villageCode, submitData).then(
    (res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess("保存成功");
        dialogVisible.value = false;
        getList();
      } else {
        proxy.$modal.msgError(res.msg);
      }
    }
  );
};

onMounted(() => {
  getList();
  getAllStation();
});
</script>

<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form
      ref="queryFormRef"
      :model="queryForm"
      inline
      class="form-container"
    >
      <el-form-item label="村社名称" prop="villageName">
        <el-input
          v-model="queryForm.villageName"
          placeholder="请输入村社名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="防治区类型" prop="preventionZonesType">
        <el-select
          v-model="queryForm.preventionZonesType"
          placeholder="请选择"
          clearable
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="item in PREVENTION_ZONES_TYPES"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search"
          >查询</el-button
        >
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 主体内容 -->
    <div class="content">
      <!-- 左侧树形 -->
      <AdcdTree v-model="queryForm.adcd" @getList="getList" />

      <!-- 右侧表格 -->
      <div class="right-table">
        <el-table
          :data="tableData"
          :style="{ height: '100%' }"
          v-loading="loading"
          stripe
        >
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="villageName" label="村社" />
          <el-table-column prop="preventionZonesType" label="防治区类型">
            <template #default="scope">
              {{
                getTypeLabel(
                  scope.row.preventionZonesType,
                  "PREVENTION_ZONES_TYPES"
                )
              }}
            </template>
          </el-table-column>
          <el-table-column prop="floodControlCapacity" label="防洪能力(年)" />
          <el-table-column prop="rainStationNameList" label="雨情预警测站" />
          <el-table-column
            prop="riverStationNameList"
            label="河道水情预警测站"
          />
          <el-table-column
            prop="reservoirStationNameList"
            label="水库水情预警测站"
          />
          <el-table-column
            prop="address"
            label="操作"
            width="150"
            align="center"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                icon="View"
                @click="handleSee(scope.row)"
              >
                查看
              </el-button>
              <el-button
                type="primary"
                link
                icon="Edit"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination
          :total="total"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 弹窗组件 -->
    <VillageStationDialog
      v-model:dialogVisible="dialogVisible"
      v-model:addTable="stationData"
      :title="dialogTitle"
      :villageData="villageData"
      :stationList="stationList"
      @save="handleSave"
    />
  </div>
</template>

<style scoped lang="scss">
.query-form {
  display: flex;
  flex-wrap: wrap;

  :deep(.el-form-item) {
    margin-right: 0;
  }
}

.query-buttons {
  margin-left: 20px;
}

.right-table {
  margin-left: 20px;
}
</style>
