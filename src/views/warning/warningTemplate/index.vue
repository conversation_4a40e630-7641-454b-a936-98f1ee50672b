<template>
  <div class="app-container">
    <el-form
      ref="queryFormRef"
      :model="queryForm"
      inline
      class="form-container"
    >
      <el-form-item label="预警类型" prop="name">
        <el-select
          v-model="queryForm.types"
          placeholder="请选择预警类型"
        >
          <el-option
            v-for="item in warningTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="预警等级" prop="warningLevel">
        <el-select
          v-model="queryForm.warningLevel"
          placeholder="请选择预警等级"
        >
          <el-option
            v-for="item in warningLevelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search">查询</el-button>
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="content">
      <div class="right-table">
        <div class="table-header">
          <el-button type="success" icon="CirclePlus" @click="addFamily"
            >新增</el-button
          >
        </div>
        <el-table
          :data="tableData"
          stripe
          v-loading="loading"
        >
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="types" label="预警类型">
            <!-- 预警类型（1危险区雨情预警2危险区水情预警3危险区气象预警4江河洪水预警5水库调度预警） -->
            <template #default="scope">
              <span v-if="scope.row.types == 1">危险区雨情预警</span>
              <span v-if="scope.row.types == 2">危险区水情预警</span>
              <span v-if="scope.row.types == 3">危险区气象预警</span>
              <span v-if="scope.row.types == 4">江河洪水预警</span>
              <span v-if="scope.row.types == 5">水库调度预警</span>
            </template>
          </el-table-column>
          <el-table-column prop="levels" label="预警等级">
            <!-- 预警等级（1准备转移2立即转移3可能发生4可能性较大5可能性大6可能性很大7超警戒8超保证9超历史10低于死水位11超汛限12超设计13超校核14超历史15超坝顶） -->
            <template #default="scope">
              <span v-if="scope.row.levels == 1">准备转移</span>
              <span v-if="scope.row.levels == 2">立即转移</span>
              <span v-if="scope.row.levels == 3">可能发生</span>
              <span v-if="scope.row.levels == 4">可能性较大</span>
              <span v-if="scope.row.levels == 5">可能性大</span>
              <span v-if="scope.row.levels == 6">可能性很大</span>
              <span v-if="scope.row.levels == 7">超警戒</span>
              <span v-if="scope.row.levels == 8">超保证</span>
              <span v-if="scope.row.levels == 9">超历史</span>
              <span v-if="scope.row.levels == 10">低于死水位</span>
              <span v-if="scope.row.levels == 11">超汛限</span>
              <span v-if="scope.row.levels == 12">超设计</span>
              <span v-if="scope.row.levels == 13">超校核</span>
              <span v-if="scope.row.levels == 14">超历史</span>
              <span v-if="scope.row.levels == 15">超坝顶</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="content"
            label="预警内容"
            show-overflow-tooltip
          />

          <el-table-column
            prop="address"
            label="操作"
            width="210"
            align="center"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="View"
                @click="handleSee(scope.row)"
                >查看</el-button
              >
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleEdit(scope.row)"
                >编辑</el-button
              >
              <el-button
                link
                type="danger"
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="dialogRules"
        label-width="100px"
        :disabled="dialogTitle === '查看预警模板'"
      >
        <el-form-item label="预警类型" prop="types">
          <el-select
            v-model="form.types"
            placeholder="请选择预警类型"
            style="width: 100%"
            @change="handleWarningTypeChange"
            clearable
          >
            <el-option
              v-for="item in warningTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预警等级" prop="levels">
          <el-select
            v-model="form.levels"
            placeholder="请选择预警等级"
            style="width: 100%"
            clearable
            :disabled="!form.types"
            @change="handleWarningLevelChange"
          >
            <el-option
              v-for="item in warningLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预警内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="5"
            placeholder="请输入预警内容"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer" v-if="dialogTitle !== '查看预警模板'">
          <el-button
          type="primary"
          @click="handleSubmit"
          >保 存</el-button
          >
          <el-button @click="dialogVisible = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs, watch } from "vue";
import { getToken } from "@/utils/auth";
import {
  getWarningTemplate,
  addWarningTemplate,
  updateWarningTemplate,
  deleteWarningTemplate,
  // getWarningTypes, getWarningLevels
} from "@/api/warning";

defineOptions({
  name: 'WarningTemplate'
})

const token = getToken();
const uploadUrl = import.meta.env.VITE_APP_BASE_API + "/hydro/convert/shp-kml/";
const { proxy } = getCurrentInstance();
const uploadRef = ref("");
const loading = ref(false);
const data = reactive({
  total: 0,
  queryForm: {
    pageNum: 1,
    pageSize: 20,
    types: "",
  },
  rules: {
    name: [{ required: true, message: "请输入防汛组织名称", trigger: "blur" }],
  },
  dialogVisible: false,
  dialogTitle: "新增预警模板",
  form: {
    types: "",
    levels: "",
    content: "",
  },
  dialogRules: {
    types: [{ required: true, message: "请选择预警类型", trigger: "change" }],
    levels: [{ required: true, message: "请选择预警等级", trigger: "change" }],
    content: [
      { max: 200, message: "预警内容不能超过200个字符", trigger: "blur" },
    ],
  },
  formRef: null,
  propsTree: {
    value: "adcd",
    label: "adnm",
    children: "children",
  },
  tableData: [],
  areaList: [],
  townList: [],
  filterText: "",
  warningTypeOptions: [
    { value: "1", label: "危险区雨情预警" },
    { value: "2", label: "危险区水情预警" },
    { value: "3", label: "危险区气象预警" },
    { value: "4", label: "江河洪水预警" },
    { value: "5", label: "水库调度预警" },
  ],
  warningLevelOptions: [],
  warningLevelMap: {
    1: [
      { value: "1", label: "准备转移" },
      { value: "2", label: "立即转移" },
    ],
    2: [
      { value: "1", label: "准备转移" },
      { value: "2", label: "立即转移" },
    ],
    3: [
      { value: "3", label: "可能发生" },
      { value: "4", label: "可能性较大" },
      { value: "5", label: "可能性大" },
      { value: "6", label: "可能性很大" },
    ],
    4: [
      { value: "7", label: "超警戒" },
      { value: "8", label: "超保证" },
      { value: "9", label: "超历史" },
    ],
    5: [
      { value: "10", label: "低于死水位" },
      { value: "11", label: "超汛限" },
      { value: "12", label: "超设计" },
      { value: "13", label: "超校核" },
      { value: "14", label: "超历史" },
      { value: "15", label: "超坝顶" },
    ],
  },
});
const {
  queryForm,
  propsTree,
  dialogVisible,
  form,
  dialogRules,
  total,
  townList,
  tableData,
  filterText,
  dialogTitle,
  warningTypeOptions,
  warningLevelOptions,
  warningLevelMap,
  formRef,
} = toRefs(data);

onMounted(() => {
  getList();
});
// watch(filterText, (val) => {
//   proxy.$refs.treeRef.filter(val);
// });
watch(filterText, (val) => {
  if(val) {
    proxy.$refs.treeRef.filter(val)
  } else {
    proxy.$refs.treeRef.filter('')
    // Collapse all expanded nodes when filter is cleared
    proxy.$refs.treeRef?.setExpandedKeys([])
  }
})
const handleSubmit = () => {
  data.formRef.validate((valid) => {
    if (valid) {
      const isEdit = data.dialogTitle === "编辑预警模板";
      const api = isEdit ? updateWarningTemplate : addWarningTemplate;
      //将form中的types,levels字符串转为int类型
      let requestData = {
        ...data.form,
        types: Number(data.form.types),
        levels: Number(data.form.levels),
      };
      api(requestData, data.form.id).then((res) => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess(isEdit ? "修改成功" : "新增成功");
          data.dialogVisible = false;
          getList();
        } else {
          proxy.$modal.msgError(res.msg);
        }
      });
    }
  });
};
const handleSee = (row) => {
  data.dialogTitle = "查看预警模板";
  //将types,levels从int类型转为字符串
  row.types = String(row.types);
  row.levels = String(row.levels);
  // 根据预警类型设置预警等级选项
  data.warningLevelOptions = data.warningLevelMap[row.types] || [];
  Object.assign(data.form, row);
  data.dialogVisible = true;
};
const handleEdit = (row) => {
  data.dialogTitle = "编辑预警模板";
  //将types,levels从int类型转为字符串
  row.types = String(row.types);
  row.levels = String(row.levels);
  // 根据预警类型设置预警等级选项
  data.warningLevelOptions = data.warningLevelMap[row.types] || [];
  Object.assign(data.form, row);
  data.dialogVisible = true;
};
const handleDelete = (row) => {
  proxy.$modal
    .confirm("是否确认删除该预警模板？", "提示", {
      type: "warning",
    })
    .then(() => {
      deleteWarningTemplate(row.id).then((res) => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess("删除成功");
          getList();
        } else {
          proxy.$modal.msgError(res.msg);
        }
      });
    })
    .catch(() => {});
};
const getList = () => {
  loading.value = true;
  getWarningTemplate(data.queryForm).then((res) => {
    data.tableData = res.data.records || [];
    data.total = res.data.total || 0;
    loading.value = false;
  });
};
const resetQuery = () => {
  data.queryForm = {
    pageNum: 1,
    pageSize: 20,
    types: "", // 预警类型
  };
  getList();
};
const addFamily = () => {
  data.dialogTitle = "新增预警模板";
  data.dialogVisible = true;
};

const handleWarningTypeChange = (value) => {
  data.form.levels = "";
  data.warningLevelOptions = data.warningLevelMap[value] || [];
  data.form.content = "";
};

const handleWarningLevelChange = (value) => {
  const type = data.form.types;
  const level = value;

  const contentTemplates = {
    1: {
      1: "【危险区】在【预警开始时间】累计【预警指标名称】【预警值】毫米，超过准备转移阈值【预警指标值】毫米，请及时通知危险区内群众准备转移避险！",
      2: "【危险区】在【预警开始时间】累计【预警指标名称】【预警值】毫米，超过立即转移阈值【预警指标值】毫米，请及时组织危险区内群众进行转移避险！",
    },
    2: {
      1: "【危险区】在【预警开始时间】【预警指标名称】【预警值】米，超过准备转移阈值【预警指标值】米，请及时通知危险区内群众准备转移避险！",
      2: "【危险区】在【预警开始时间】【预警指标名称】【预警值】米，超过立即转移阈值【预警指标值】米，请及时组织危险区内群众进行转移避险！",
    },
    3: {
      3: "【危险区】在【预警开始时间】预报【预警指标名称】【预警值】豪米，可能发生气象风险！",
      4: "【危险区】在【预警开始时间】预报【预警指标名称】【预警值】豪米，发生气象风险的可能性较大！",
      5: "【危险区】在【预警开始时间】预报【预警指标名称】【预警值】豪米，发生气象风险的可能性大！",
      6: "【危险区】在【预警开始时间】预报【预警指标名称】【预警值】豪米，发生气象风险的可能性很大！",
    },
  };

  if (
    type &&
    level &&
    contentTemplates[type] &&
    contentTemplates[type][level]
  ) {
    data.form.content = contentTemplates[type][level];
  }
};
</script>