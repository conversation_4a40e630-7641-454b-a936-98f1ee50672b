<template>
    <div class="app-container">
        <el-form ref="queryFormRef" :model="queryForm" inline class="form-container">
            <!-- 预警时间范围 -->
            <el-form-item label="预警时间" prop="timeRange">
                <el-date-picker
                    v-model="queryForm.timeRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :default-time="defaultTime"
                    :shortcuts="dateShortcuts"
                    value-format="YYYY-MM-DD HH:mm:ss"
                />
            </el-form-item>

            <!-- 预警对象名称 -->
            <el-form-item label="预警对象" prop="name">
                <el-input
                    v-model="queryForm.name"
                    placeholder="请输入预警对象名称"
                    clearable
                />
            </el-form-item>

            <!-- 所属区县 -->
            <el-form-item label="所属区县" prop="adcd">
                <!-- <el-select
                    v-model="queryForm.adcd"
                    placeholder="请选择区县"
                    clearable
                >
                    <el-option
                        v-for="item in districtOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select> -->
                <el-tree-select  v-model="queryForm.adcd" :data="areaList"
                :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="adcd" placeholder="选择所属县区"
                check-strictly @node-click="formAreaHandleNodeClick" />
            </el-form-item>

            <!-- 预警时效 -->
            <el-form-item label="预警时效" prop="timing">
                <el-select
                    v-model="queryForm.timing"
                    placeholder="请选择预警时效"
                    clearable
                >
                    <el-option label="监测预警" :value="1" />
                    <el-option label="短临预警" :value="3" />
                    <el-option label="预报预警" :value="2" />
                </el-select>
            </el-form-item>

            <!-- 预警类型 -->
            <el-form-item label="预警类型" prop="warnType">
                <el-select
                    v-model="queryForm.warnType"
                    placeholder="请选择预警类型"
                    clearable
                >
                    <el-option label="村社雨情预警" :value="1" />
                    <el-option label="村社水情预警" :value="2" />
                    <el-option label="村社气象预警" :value="3" />
                    <el-option label="江河洪水预警" :value="4" />
                    <el-option label="水库调度预警" :value="5" />
                </el-select>
            </el-form-item>

            <!-- 预警状态 -->
            <el-form-item label="预警状态" prop="statusList">
                <el-select
                    v-model="queryForm.statusList"
                    placeholder="请选择预警状态"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                >
                    <el-option label="新产生" :value="1" />
                    <el-option label="预警升级" :value="2" />
                    <el-option label="预警降级" :value="3" />
                    <!-- <el-option label="已关闭" :value="4" /> -->
                </el-select>
            </el-form-item>
            <el-form-item label="是否正在预警" prop="warnStatus" >
                <el-select
                    v-model="queryForm.warnStatus"
                    placeholder="请选择"
                >
                    <el-option label="是" :value="1" />
                    <el-option label="否" :value="0" />
                </el-select>
            </el-form-item>

            <el-form-item class="form-item-button">
                <el-button type="primary" @click="getList" icon="Search">查询</el-button>
                <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="content">

            <div class="right-table">
                <!-- <el-button type="primary" icon="Plus" @click="addFamily()">新增</el-button> -->
                <el-table :data="tableData" stripe
                    v-loading="loading">
                    <el-table-column type="index" label="序号" width="60" align="center" />
                    <el-table-column prop="name" label="预警对象" min-width="120" show-overflow-tooltip />
                    <el-table-column prop="warnType" label="预警类型" min-width="120">
                        <template #default="scope">
                            {{ warningTypeMap[scope.row.warnType] || scope.row.warnType }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="area" label="所在区县" min-width="120">
                        <template #default="scope">
                            {{ districtMap[scope.row.area] || scope.row.area }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="remark" label="预警说明" min-width="200" show-overflow-tooltip />
                    <el-table-column prop="warnLevel" label="预警等级" min-width="100">
                        <template #default="scope">
                            {{ getWarningLevelLabel(scope.row.warnLevel) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="timing" label="预警时效" min-width="100">
                        <template #default="scope">
                            {{ warningPeriodMap[scope.row.timing] || scope.row.timing }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="预警状态" min-width="100">
                        <template #default="scope">
                            <el-tag :type="getWarningStatusType(scope.row.status)">
                                {{ warningStatusMap[scope.row.status] || scope.row.status }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="正在预警" min-width="100" align="center">
                        <template #default="scope">
                            <el-tag :type="scope.row.warnStatus == 1 ? 'success' : 'warning'">
                                {{ scope.row.warnStatus == 1 ? '是' : '否' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="开始预警时间" min-width="160" />
                    <el-table-column prop="endTime" label="结束预警时间" min-width="160" />
                    <el-table-column label="操作" width="250" align="center" fixed="right">
                        <template #default="scope">
                            <el-button
                                type="primary"
                                link
                                @click="handleDetail(scope.row)"
                                :icon="View"
                            >
                                查看
                            </el-button>
                            <el-button
                                type="success"
                                link
                                @click="handleReceive(scope.row)"
                                :icon="Select"
                                v-if="scope.row.status != 4"
                            >
                                收到
                            </el-button>
                            <el-button
                                type="danger"
                                link
                                @click="handleClose(scope.row)"
                                :icon="CircleClose"
                                v-if="scope.row.status != 4"
                            >
                                关闭
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
                    @pagination="getList" />
            </div>
        </div>
        <!-- 查看详情弹出框 -->
        <el-dialog
            v-model="detailDialogVisible"
            :title="title"
            width="75%"
            destroy-on-close
        >
            <div class="warning-detail">
                <div class="detail-main">
                    <!-- 左侧内容 -->
                    <div class="detail-left">
                        <!-- 基本信息部分 -->
                        <div class="detail-card">
                            <div class="section-title">
                                <i class="el-icon-info section-icon"></i>
                                基本信息
                            </div>
                            <el-descriptions :column="4" border>
                                <el-descriptions-item label="测站名称:" :span="2" v-if="detailForm.warnType == 1" label-class-name="desc-label" content-class-name="desc-content">
                                    {{ detailForm.stnms }}
                                </el-descriptions-item>
                                <el-descriptions-item label="预警对象:" :span="2" label-class-name="desc-label" content-class-name="desc-content">
                                    {{ detailForm.name }}
                                </el-descriptions-item>
                                <el-descriptions-item label="所在区县:" :span="2" label-class-name="desc-label" content-class-name="desc-content">
                                    {{ districtMap[detailForm.area] }}
                                </el-descriptions-item>
                                <el-descriptions-item label="预警类型:" :span="2" label-class-name="desc-label" content-class-name="desc-content">
                                    {{ warningTypeMap[detailForm.warnType] }}
                                </el-descriptions-item>
                                <el-descriptions-item label="预警时效:" :span="2" label-class-name="desc-label" content-class-name="desc-content">
                                    {{ warningPeriodMap[detailForm.timing] }}
                                </el-descriptions-item>
                                <el-descriptions-item label="预警等级:" :span=" detailForm.warnType == 1 ? 2 : 4 " label-class-name="desc-label" content-class-name="desc-content">
                                    <el-tag :type="getWarningLevelType(detailForm.warnLevel)" effect="dark" size="small">
                                        {{ getWarningLevelLabel(detailForm.warnLevel) }}
                                    </el-tag>
                                </el-descriptions-item>
                                <el-descriptions-item label="预警内容:" :span="4" label-class-name="desc-label" content-class-name="desc-content">
                                    {{ detailForm.remark }}
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>

                        <!-- 超预警指标情况 -->
                        <div class="detail-card">
                            <div class="section-title">
                                <i class="el-icon-warning section-icon"></i>
                                超预警指标情况
                            </div>
                            <el-table
                                :data="detailForm.warningIndexList"
                                border
                                max-height="150"
                                :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
                                stripe
                                highlight-current-row
                            >
                                <el-table-column prop="warnIndex" label="预警指标" min-width="120" />
                                <el-table-column prop="warnValue" label="指标值" min-width="100">
                                    <template #default="scope">
                                        {{ scope.row.warnValue }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="alValue" label="监测/预报值" min-width="120">
                                    <template #default="scope">
                                        {{ scope.row.alValue }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="exceedValue" label="超预警指标值" min-width="120">
                                    <template #default="scope">
                                        <span :class="{ 'exceed-value': scope.row.exceedValue > 0 }">
                                            {{ scope.row.exceedValue.toFixed(2) }}
                                        </span>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>

                        <!-- 预警反馈情况 -->
                        <div class="detail-card">
                            <div class="section-title">
                                <i class="el-icon-chat-dot-round section-icon"></i>
                                预警反馈情况
                            </div>
                            <el-table
                                :data="detailForm.warnFeedbacks"
                                border
                                max-height="170"
                                :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
                                stripe
                            >
                                <el-table-column type="index" label="序号" width="60" align="center" />
                                <el-table-column prop="memberName" label="接收人" />
                                <el-table-column prop="responsibilityName" label="责任类型" />
                                <el-table-column prop="phone" label="联系方式" />
                                <el-table-column prop="status" label="反馈状态">
                                    <template #default="scope">
                                        <el-tag :type="scope.row.feedbackStatus === 1 ? 'success' : 'warning'" effect="light" size="small">
                                            <i :class="scope.row.feedbackStatus === 1 ? 'el-icon-check' : 'el-icon-time'"></i>
                                            {{ scope.row.feedbackStatus === 1 ? '已反馈' : '未反馈'}}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="createTime" label="反馈时间" />
                            </el-table>
                        </div>
                    </div>

                    <!-- 右侧预警状态 -->
                    <div class="detail-right">
                        <div class="detail-card status-card">
                            <div class="section-title">
                                <i class="el-icon-bell section-icon"></i>
                                预警状态
                            </div>
                            <div class="status-timeline">
                                <el-timeline v-for="item in detailForm.warnStatusPOList" :key="item.id">
                                    <el-timeline-item
                                        :timestamp="item.createTime"
                                        :type="getWarningStatusTimelineType(item.status)"
                                        :icon="getWarningStatusIcon(item.status)"
                                        size="large"
                                    >
                                        <div class="timeline-content">
                                            <span class="timeline-title">{{ warningStatusMap[item.status] }}</span>
                                        </div>
                                    </el-timeline-item>
                                </el-timeline>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs, watch } from "vue";
import { getToken } from '@/utils/auth'
import { getAdcdTree } from "@/api/watershed/ads";
import {
    queryWarningRelease, closeWarningRelease,//关闭预警
    // getWarningDetail
    // getWarningTypes, getWarningLevels
} from "@/api/warning";
import { View, Select, CircleClose } from '@element-plus/icons-vue'

defineOptions({
  name: 'WarningIssue'
})

const token = getToken()
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
const { proxy } = getCurrentInstance();
const uploadRef = ref('')
const loading = ref(false)
const data = reactive({
    total: 0,
    queryForm: {
        timeRange: [],
        startTime: '',
        endTime: '',
        name: '',
        area: '',
        timing: '',
        warnType: '',
        statusList: [1, 2, 3], // 默认选中三个状态
        pageNum: 1,
        pageSize: 20,
        warnStatus: 1, // 默认选中是
    },
    rules: {
        name: [
            { required: true, message: '请输入防汛组织名称', trigger: 'blur' },
        ],


    },
    dialogVisible: false,
    dialogTitle: '新增预警模板',

    formRef: null,
    propsTree: {
        value: 'adcd',
        label: 'adnm',
        children: 'children',
    },
    tableData: [],
    areaList: [],
    townList: [],
    filterText: '',
    warningTypeOptions: [
        { value: '1', label: '村社雨情预警' },
        { value: '2', label: '村社水情预警' },
        { value: '3', label: '村社气象预警' },
        { value: '4', label: '江河洪水预警' },
        { value: '5', label: '水库调度预警' }
    ],
    warningLevelOptions: [],
    warningLevelMap: {
        1: [
            { value: 1, label: '准备转移' },
            { value: 2, label: '立即转移' }
        ],
        2: [
            { value: 1, label: '准备转移' },
            { value: 2, label: '立即转移' }
        ],
        3: [
            { value: 1, label: '可能发生' },
            { value: 2, label: '可能性较大' },
            { value: 3, label: '可能性大' },
            { value: 4, label: '可能性很大' }
        ],
        4: [
            { value: 1, label: '超警戒' },
            { value: 2, label: '超保证' },
            { value: 3, label: '超历史' }
        ],
        5: [
            { value: 1, label: '低于死水位' },
            { value: 2, label: '超汛限' },
            { value: 3, label: '超设计' },
            { value: 4, label: '超校核' },
            { value: 5, label: '超历史' },
            { value: 6, label: '超坝顶' }
        ]
    },
    detailDialogVisible: ref(false),
    detailForm: reactive({
        name: '',
        area: '',
        warnType: '',
        timing: '',
        warnLevel: '',
        remark: '',
        warningIndexList: [], // 修改为数组形式存储指标数据
        warnFeedbacks: [],
        createTime: '',
        upgradeTime: '',
        downgradeTime: '',
        closeTime: '',
        closeType: ''
    })
});
const { queryForm, propsTree, dialogVisible, form, dialogRules, total, townList, tableData, filterText, dialogTitle, warningTypeOptions, warningLevelOptions, warningLevelMap, detailDialogVisible, detailForm, areaList } = toRefs(data)

// 区县选项
const districtOptions = ref([
    // 这里需要根据实际数据补充区县选项
    { value: '330100', label: '杭州市' },
    { value: '330200', label: '宁波市' },
    // ...更多区县
]);

// 默认时间范围为最近72小时
const defaultTime = [
    new Date(2000, 1, 1, 0, 0, 0),
    new Date(2000, 1, 1, 23, 59, 59),
];

// 时间快捷选项
const dateShortcuts = [
    {
        text: '最近72小时',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 72);
            return [start, end];
        },
    },
    {
        text: '最近24小时',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24);
            return [start, end];
        },
    }
];

onMounted(() => {

    getList();
    getAdcdList(); // 获取区县列表
})

watch(filterText, (val) => {
  if(val) {
    proxy.$refs.treeRef.filter(val)
  } else {
    proxy.$refs.treeRef.filter('')
    // Collapse all expanded nodes when filter is cleared
    proxy.$refs.treeRef?.setExpandedKeys([])
  }
})

const title = ref('预警详情');
const handleDetail = (row) => {
    title.value = '预警详情 - ' + row.name
    detailDialogVisible.value = true
    detailForm.value = row
    // detailForm.value.warningIndexList = [{
    //     warnIndex: row.warnIndex,
    //     warnValue: row.warnValue,
    //     alValue: row.alValue,
    //     exceedValue: row.alValue - row.warnValue || 0
    // }]
    detailForm.value.warnInfoDetails.map(item => {
        item.exceedValue = item.alValue - item.warnValue || 0
    })
    detailForm.value.warningIndexList = detailForm.value.warnInfoDetails
}
const handleReceive = (row) => {
    proxy.$modal.confirm('是否确认已收到该预警？', '提示', {
        type: 'warning'
    }).then(() => {
        // 调用收到预警的接口
        // receiveWarning(row.id).then(res => {
        //     if (res.code === 200) {
        //         proxy.$modal.msgSuccess('操作成功')
        //         getList()
        //     } else {
        //         proxy.$modal.msgError(res.msg)
        //     }
        // })
    }).catch(() => {})
}
const handleClose = (row) => {
    proxy.$modal.confirm('是否确认关闭该预警？', '提示', {
        type: 'warning'
    }).then(() => {
        // 调用关闭预警的接口
        closeWarningRelease(row.id).then(res => {
            if (res.code === 200) {
                proxy.$modal.msgSuccess('关闭成功')
                getList()
            } else {
                proxy.$modal.msgError(res.msg)
            }
        })
    }).catch(() => {})
}

const getList = () => {
    loading.value = true
    // console.log(data.queryForm)
    queryWarningRelease({
        ...data.queryForm,
        startTime: data.queryForm.timeRange[0],
        endTime: data.queryForm.timeRange[1],
    }).then(res => {
        data.tableData = res.data.records || []
        data.total = res.data.total || 0
        loading.value = false
    })
}
const resetQuery = () => {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 72);

    data.queryForm = {
        timeRange: [start, end],
        startTime: start,
        endTime: end,
        name: '',
        area: '',
        timing: '',
        warnType: '',
        statusList: [1, 2, 3],
        pageNum: 1,
        pageSize: 20,
        warnStatus: 1, // 默认选中是
    };
    getList();
}

// 添加映射对象
const warningTypeMap = {
    1: '村社雨情预警',
    2: '村社水情预警',
    3: '村社气象预警',
    4: '江河洪水预警',
    5: '水库调度预警'
}

const warningPeriodMap = {
    1: '监测预警',
    3: '短临预警',
    2: '预报预警'
}

const warningStatusMap = {
    1: '新产生',
    2: '预警升级',
    3: '预警降级',
    4: '已关闭'
}

// 将区县选项转换为映射对象
const districtMap = districtOptions.value.reduce((acc, curr) => {
    acc[curr.value] = curr.label
    return acc
}, {})

// 获取预警等级标签
const getWarningLevelLabel = (level) => {
    const levelMap = {
        1: '准备转移',
        2: '立即转移',
        3: '可能发生',
        4: '可能性较大',
        5: '可能性大',
        6: '可能性很大',
        7: '超警戒',
        8: '超保证',
        9: '超历史',
        10: '低于死水位',
        11: '超汛限',
        12: '超设计',
        13: '超校核',
        14: '超历史',
        15: '超坝顶',
    }
    return levelMap[level] || ''
}

// 获取预警状态对应的标签类型
const getWarningStatusType = (status) => {
    const typeMap = {
        1: 'warning',
        2: 'danger',
        3: 'info',
        4: ''
    }
    return typeMap[status] || ''
}
const formAreaHandleNodeClick = (node) => {
    // console.log(node.children);
  if (!node.children || node.children.length === 0) {
    // 只选中最后一个层级节点
    // console.log(node);
    data.queryForm.abcd = node.adcd

  } else {
    // 清空选中
    proxy.$modal.msgError("请选择区县");
    nextTick(() => {
        data.queryForm.abcd = ''
    })

  }
}
const getAdcdList = () => {
    getAdcdTree({
        adcd: ''
    }).then((res) => {
        // console.log(res)
        data.areaList = res.data[0].children || []
    })
}

const getWarningStatusTimelineType = (status) => {
    const typeMap = {
        1: 'warning',
        2: 'danger',
        3: 'info',
        4: 'success'
    }
    return typeMap[status] || 'primary'
}

const getWarningStatusIcon = (status) => {
    const iconMap = {
        1: 'el-icon-notification',
        2: 'el-icon-warning',
        3: 'el-icon-arrow-down',
        4: 'el-icon-circle-check'
    }
    return iconMap[status] || 'el-icon-more'
}

const getWarningLevelType = (level) => {
    // 根据预警等级返回不同的标签类型
    if ([2, 6, 9, 13, 14, 15].includes(Number(level))) {
        return 'danger'  // 高级别预警
    } else if ([1, 4, 5, 8, 11, 12].includes(Number(level))) {
        return 'warning' // 中级别预警
    } else {
        return 'info'    // 低级别预警
    }
}
</script>
<style scoped lang="scss">
:deep(.addForm .el-form--inline .el-form-item) {
    margin-right: 0;
    width: 50%;

}

// 美化预警详情对话框样式
.warning-detail {
    padding: 20px;

    .detail-main {
        display: flex;
        gap: 24px;

        .detail-left {
            flex: 1;
        }

        .detail-right {
            width: 320px;
        }
    }

    // 卡片式设计
    .detail-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        padding: 20px;
        margin-bottom: 24px;
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
        }

        // 特殊样式for状态卡片
        &.status-card {
            height: calc(100% - 24px);
            background-color: #fafafa;

            .status-timeline {
                padding: 10px 5px;
                height: calc(100% - 60px);
                overflow-y: auto;
            }
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-left: 10px;
            border-left: 4px solid var(--el-color-primary);
            display: flex;
            align-items: center;

            .section-icon {
                margin-right: 8px;
                font-size: 18px;
                color: var(--el-color-primary);
            }
        }
    }
}

// 美化描述列表
:deep(.el-descriptions) {
    margin-bottom: 16px;

    .el-descriptions__header {
        margin-bottom: 12px;
    }

    .desc-label {
        min-width: 100px !important;
        width: 100px !important;
    }


    .el-descriptions__body {
        background-color: #fafafa;
    }
}

// 美化表格
:deep(.el-table) {
    border-radius: 6px;
    overflow: hidden;

    .el-table__header-wrapper {
        th {
            font-weight: 600;
        }
    }

    // 超出指标值样式
    .exceed-value {
        color: #f56c6c;
        font-weight: 600;
    }
}

// 美化时间线
:deep(.el-timeline) {
    padding: 12px 8px;

    .el-timeline-item {
        position: relative;
        padding-bottom: 20px;

        &:last-child {
            padding-bottom: 0;
        }

        .el-timeline-item__wrapper {
            padding-left: 28px;
        }

        .el-timeline-item__timestamp {
            color: #909399;
            font-size: 13px;
            margin-bottom: 8px;
        }

        .el-timeline-item__content {
            color: #303133;
        }

        .timeline-content {
            padding: 10px 14px;
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.2s ease;

            &:hover {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            }

            .timeline-title {
                font-weight: 500;
                display: block;
                font-size: 14px;
            }
        }
    }
}

:deep(.el-button--link) {
    padding: 0 8px;
}

// 美化Dialog
:deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;

    .el-dialog__header {
        padding: 20px;
        margin: 0;
    }

    .el-dialog__body {
        padding: 0;
        max-height: 85vh;
        overflow-y: auto;

        /* 美化滚动条 */
        &::-webkit-scrollbar {
        width: 6px;
        }

        &::-webkit-scrollbar-thumb {
        background-color: #dcdfe6;
        border-radius: 3px;
        }

        &::-webkit-scrollbar-track {
        background-color: #f5f7fa;
        }
    }

    .el-dialog__close {
        color: #909399;

        &:hover {
            color: var(--el-color-primary);
        }
    }
}
</style>