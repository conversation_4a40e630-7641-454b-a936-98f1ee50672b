<template>
  <div class="app-container">
    <el-form
      ref="queryFormRef"
      :model="queryForm"
      inline
      class="form-container"
    >
      <el-form-item label="防汛成员名称" prop="name">
        <el-input
          v-model="queryForm.name"
          placeholder="请输入"
        ></el-input>
      </el-form-item>

      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search"
          >查询</el-button
        >
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="content">
      <div class="left-tree">
        <div class="head-container">
          <el-input
            v-model="filterText"
            placeholder="请输入防汛组织名称"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree-v2
            :data="adcdList"
            :props="propsTree"
            :height="600"
            :filter-method="filterNode"
            ref="treeRef"
            @node-click="handleNodeClick"
            highlight-current
          />
        </div>
      </div>
      <div class="right-table">
        <div class="table-header">
          <el-button type="success" icon="CirclePlus" @click="addFamily"
            >新增</el-button
          >
        </div>
        <el-table :data="tableData" stripe v-loading="loading">
          <el-table-column
            type="index"
            label="序号"
            width="80"
            align="center"
          />
          <el-table-column prop="name" label="姓名" align="center" />
          <el-table-column
            prop="dutyPhone"
            label="值班电话"
            width="150"
            align="center"
          />
          <el-table-column
            prop="mobilePhone"
            label="手机号码"
            width="120"
            align="center"
          />
          <el-table-column
            prop="positionName"
            label="防汛岗位"
            width="150"
            align="center"
          />
          <el-table-column
            prop="organizationName"
            label="所属部门"
            align="center"
            width="150"
          />
          <el-table-column
            prop="deptPosition"
            label="部门职务"
            align="center"
          />
          <el-table-column
            prop="responsibleName"
            label="防汛责任"
            align="center"
            width="200"
          />
          <el-table-column prop="objectName" label="负责对象" align="center" />

          <el-table-column
            prop="address"
            label="操作"
            width="210"
            align="center"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                icon="View"
                @click="handleSee(scope.row)"
                >查看</el-button
              >
              <el-button
                type="primary"
                link
                icon="Edit"
                @click="handleEdit(scope.row)"
                >编辑</el-button
              >
              <el-button
                type="danger"
                link
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          class="pagination"
          :total="total"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <el-dialog
      v-model="dialogShow"
      :title="title"
      style="width: 60%"
      @close="resetAddForm"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        label-width="auto"
        inline
        :rules="rules"
        :disabled="title.includes('查看防汛成员信息')"
        style="display: flex; flex-wrap: wrap"
      >
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="addForm.name"
            placeholder="请输入姓名"
            style="width: 180px"
          ></el-input>
        </el-form-item>
        <el-form-item label="身份ID" prop="identityId">
          <el-input
            v-model="addForm.identityId"
            placeholder="请输入身份ID"
            style="width: 180px"
            :formatter="
              (value) => `${value}`.replace(/[^0-9Xx]/g, '').substring(0, 18)
            "
          ></el-input>
        </el-form-item>
        <el-form-item label="值班电话" prop="dutyPhone">
          <el-input
            v-model="addForm.dutyPhone"
            placeholder="请输入值班电话"
            style="width: 180px"
            oninput="value = value.replace(/[^0-9]/g, '').substring(0,11)"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="mobilePhone">
          <el-input
            v-model="addForm.mobilePhone"
            placeholder="请输入手机号码"
            style="width: 180px"
            oninput="value = value.replace(/[^0-9]/g, '').substring(0,11)"
          ></el-input>
        </el-form-item>
        <el-form-item label="防汛组织" prop="organizationId">
          <el-select
            v-model="addForm.organizationId"
            placeholder="请选择"
            style="width: 180px"
            clearable
          >
            <el-option
              :label="item.name"
              :value="item.id"
              highlight-current
              v-for="item in orgList"
              :key="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="防汛岗位" prop="positionId">
          <el-select
            v-model="addForm.positionId"
            placeholder="请选择"
            style="width: 180px"
            clearable
          >
            <el-option
              v-for="item in gangweiList"
              :label="item.name"
              :value="item.id"
              highlight-current
              :key="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属单位" prop="affiliatedUnit">
          <el-input
            v-model="addForm.affiliatedUnit"
            placeholder="请输入所属单位"
            style="width: 180px"
          ></el-input>
        </el-form-item>
        <el-form-item label="部门职务" prop="deptPosition">
          <el-input
            v-model="addForm.deptPosition"
            placeholder="请输入部门职务"
            style="width: 180px"
          ></el-input>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input
            v-model="addForm.sort"
            placeholder="请输入排序"
            style="width: 180px"
            oninput="value = value.replace(/[^0-9.]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item label="绑定用户账号" prop="userId">
          <el-select
            v-model="addForm.userId"
            placeholder="请选择用户"
            clearable
          >
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.userName"
              :value="item.userId"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="addForm.status">
                        <el-radio :label="1">正常</el-radio>
                        <el-radio :label="0">停用</el-radio>
                    </el-radio-group>
                </el-form-item> -->
      </el-form>
      <div style="padding: 20px">
        <el-link
          v-if="!title.includes('查看防汛成员信息')"
          type="primary"
          icon="Plus"
          style="margin-bottom: 20px"
          @click="addRow"
          >新增防汛责任</el-link
        >
        <el-table
          :data="addTable"
          :style="{ width: '100%', maxHeight: '300px' }"
        >
          <el-table-column label="序号" type="index" width="80" />
          <el-table-column prop="responsibleObjectType" label="负责对象类型">
            <template #default="scope">
              <el-select
                v-model="scope.row.responsibleObjectType"
                placeholder="请选择"
                @change="changeResponsibleObjectType"
                :disabled="title.includes('查看防汛成员信息')"
              >
                <el-option label="行政区" :value="1" />
                <el-option label="危险区" :value="2" />
                <el-option label="水库" :value="3" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="objectCode" label="负责对象">
            <template #default="scope"
              ><!-- 需要接口返回对象字段 -->
              <el-select
                v-if="scope.row.responsibleObjectType == 2"
                v-model="scope.row.objectCode"
                placeholder="请选择"
                :disabled="title.includes('查看防汛成员信息')"
              >
                <el-option
                  v-for="item in scope.row.objectList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <el-select
                v-if="scope.row.responsibleObjectType == 3"
                v-model="scope.row.objectCode"
                placeholder="请选择"
                :disabled="title.includes('查看防汛成员信息')"
              >
                <el-option
                  v-for="item in scope.row.objectList"
                  :key="item.registerCode"
                  :label="item.resName"
                  :value="item.registerCode"
                />
              </el-select>

              <el-tree-select
                :disabled="title.includes('查看防汛成员信息')"
                v-if="scope.row.responsibleObjectType == 1"
                v-model="scope.row.objectCode"
                :data="scope.row.objectList"
                :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
                value-key="adcd"
                placeholder="选择所属行政区"
                check-strictly
                :render-after-expand="false"
                @node-click="formAreaHandleNodeClick"
              />
              <!-- render-after-expand="false" 不回显id -->
            </template>
          </el-table-column>
          <el-table-column prop="responsibleId" label="防汛责任类型">
            <template #default="scope">
              <!-- 需要接入防汛责任查询接口 -->
              <el-select
                v-model="scope.row.responsibleId"
                placeholder="请选择"
                :disabled="title.includes('查看防汛成员信息')"
              >
                <el-option
                  v-for="item in responsibleList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column
            prop="address"
            label="操作"
            align="center"
            v-if="!title.includes('查看防汛成员信息')"
          >
            <template #default="scope">
              <el-link
                type="danger"
                size="mini"
                @click="deleteOne(scope.row)"
                style="margin-left: 10px"
                >删除</el-link
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer v-if="!title.includes('查看防汛成员信息')">
        <span class="dialog-footer">
          <el-button
            type="primary"
            @click="submitAdd"
            v-if="title == '新增防汛成员信息'"
            >保 存
          </el-button>
          <el-button
            type="primary"
            @click="submitEdit"
            v-else-if="title.includes('编辑防汛成员信息')"
            >保 存
          </el-button>
          <el-button @click="resetAddForm">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs, watch } from "vue";
import { getToken } from "@/utils/auth";
import {
  getAdcdTree, // 获取行政区树
  selectRsList, // 获取水库列表
} from "@/api/watershed/ads";
import {
  addFloodPreventionMember,
  queryFloodPreventionMember,
  deleteFloodPreventionMember,
  updateFloodPreventionMember,
  queryFloodPreventionOrganizations,
  queryFloodPreventionPositions, // 防汛岗位
  dangerousAreaList, // 危险区
  queryFloodPreventionResponsibility, // 防汛责任
} from "@/api/warning";
import { listUser } from "@/api/system/user";

defineOptions({
  name: "FloodWorker",
});

const token = getToken();
const uploadUrl = import.meta.env.VITE_APP_BASE_API + "/hydro/convert/shp-kml/";
const { proxy } = getCurrentInstance();
const uploadRef = ref("");
const loading = ref(false);
const data = reactive({
  total: 0,
  addTable: [],
  queryForm: {
    name: "", // 姓名
    organizationId: "", // 所属防汛组织
    pageNum: 1,
    pageSize: 20,
  },
  rules: {
    name: [{ required: true, message: "请输入防汛成员名称", trigger: "blur" }],
    identityId: [{ required: true, message: "请输入身份id", trigger: "blur" }],
    mobilePhone: [
      { required: true, message: "请输入手机号码", trigger: "blur" },
    ],
    // organizationId: [
    //   { required: true, message: "请选择所属防汛组织", trigger: "blur" },
    // ],
  },
  dialogShow: false,
  title: "新增防汛成员信息",
  addForm: {
    name: "",
    identityId: "",
    dutyPhone: "",
    mobilePhone: "",
    organizationId: "",
    positionId: "",
    affiliatedUnit: "",
    deptPosition: "",
    sort: "",
    userId: "",
  },
  newTableData: "",
  propsTree: {
    value: "id",
    label: "name",
    children: "children",
  },
  tableData: [],
  adcdList: [],
  orgList: [],
  gangweiList: [],
  townList: [],
  filterText: "",
  objectList: [],
  responsibleList: [], // 责任列表
  responsibleListByCopy: [], // 责任列表复制
  userList: [],
});
const {
  queryForm,
  propsTree,
  adcdList,
  dialogShow,
  addForm,
  title,
  rules,
  total,
  orgList,
  addTable,
  tableData,
  filterText,
  gangweiList,
  objectList,
  responsibleList,
  responsibleListByCopy,
  userList,
} = toRefs(data);
const filterNode = (query, node) => {
  return node.name.includes(query);
};
const addRow = () => {
  let status = true;
  data.addTable.forEach((item) => {
    if (
      !item.responsibleObjectType ||
      !item.responsibleId ||
      !item.objectCode
    ) {
      status = false;
    }
  });
  if (status) {
    data.addTable.push({
      responsibleObjectType: 1,
      responsibleId: "",
      objectCode: "",
    });
    data.addTable.forEach((item, index) => {
      getPoliticalArea(item); // 默认获取政治区域
    });
    data.responsibleList = data.responsibleListByCopy.filter(
      (item) => item.responsibilityType == 1
    ); // 默认获取责任对象类型--政治区域
  } else {
    proxy.$modal.msgWarning("请完善数据！确保数据完整性");
  }
};
const deleteOne = (row) => {
  if (data.addTable.length > 0) {
    data.addTable.splice(data.addTable.indexOf(row), 1);
  }
};
onMounted(() => {
  getList();
  getTreeList();
  getOrgList();
  getGangweiList(); // 获取防汛岗位
  getResponsibleList(); // 获取防汛责任
  getUserList(); // 获取用户
});
// watch(filterText, (val) => {
//   proxy.$refs.treeRef.filter(val);
// });
watch(filterText, (val) => {
  if (val) {
    proxy.$refs.treeRef.filter(val);
  } else {
    proxy.$refs.treeRef.filter("");
    // Collapse all expanded nodes when filter is cleared
    proxy.$refs.treeRef?.setExpandedKeys([]);
  }
});
const resetAddForm = () => {
  data.dialogShow = false;
  console.log(data.addTable);
  data.addTable = [];
};
const getUserList = () => {
  listUser({
    pageNum: 1,
    pageSize: 99999,
  }).then((res) => {
    data.userList = res.data || [];
  });
};
const getResponsibleList = (responsibilityType) => {
  queryFloodPreventionResponsibility({
    pageNum: 1,
    pageSize: 99999,
    responsibilityType: responsibilityType || "",
  }).then((res) => {
    // data.responsibleList = res.data.records || [];
    data.responsibleListByCopy = res.data.records || [];
    data.responsibleList = data.responsibleListByCopy;
  });
};
const getGangweiList = () => {
  queryFloodPreventionPositions({
    pageNum: 1,
    pageSize: 99999,
  }).then((res) => {
    data.gangweiList = res.data.records || [];
  });
};
const getOrgList = () => {
  queryFloodPreventionOrganizations({
    pageNum: 1,
    pageSize: 99999,
  }).then((res) => {
    data.orgList = res.data.records || [];
  });
};
const getTreeList = () => {
  queryFloodPreventionOrganizations({
    pageNum: 1,
    pageSize: 99999,
  }).then((res) => {
    data.adcdList = formatTree(res.data.records);
  });
};
const submitAdd = () => {
  // let flag = false;
  proxy.$refs.addFormRef.validate((valid) => {
    if (!valid) return;

    // 验证防汛责任表格数据
    if (data.addTable != []) {
      data.addTable.forEach((item) => {
        if (
          !item.responsibleObjectType ||
          !item.responsibleId ||
          !item.objectCode
        ) {
          proxy.$modal.msgWarning("请完善数据！确保数据完整性");
          // flag = false;
        } else {
          // switch (item.responsibleObjectType) {
          //   case 1:
          //     // 行政区
          //     item.adcd = item.objectCode;
          //     break;
          //   case 2:
          //     // 危险区
          //     item.hazardousId = item.objectCode;
          //     break;
          //   case 3:
          //     // 水库
          //     item.registerCode = item.objectCode;
          //     break;
          //   default:
          //     break;
          // }
          // flag = true;
        }
      });
    }

    addFloodPreventionMember({ ...data.addForm, dtos: data.addTable }).then(
      (res) => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess("新增成功");
          data.dialogShow = false;
          getList();
        } else {
          proxy.$modal.msgError(res.msg);
        }
      }
    );
  });
};
const submitEdit = () => {
  let flag = false;
  proxy.$refs.addFormRef.validate((valid) => {
    if (valid) {
      // 验证表格数据
      if (data.addTable != []) {
        data.addTable.forEach((item) => {
          if (
            !item.responsibleObjectType ||
            !item.responsibleId ||
            !item.objectCode
          ) {
            proxy.$modal.msgWarning("请完善数据！确保数据完整性");
            flag = false;
          } else {
            // switch (item.responsibleObjectType) {
            //   case 1:
            //     // 行政区
            //     item.adcd = item.objectCode;
            //     break;
            //   case 2:
            //     // 危险区
            //     item.hazardousId = item.objectCode;
            //     break;
            //   case 3:
            //     // 水库
            //     item.registerCode = item.objectCode;
            //     break;
            //   default:
            //     break;
            // }
            flag = true;
          }
        });

        if (flag) {
          updateFloodPreventionMember(
            { ...data.addForm, dtos: data.addTable },
            data.addForm.id
          ).then((res) => {
            if (res.code == 200) {
              proxy.$modal.msgSuccess("修改成功");
              data.dialogShow = false;
              data.addTable = [];
              getList();
            } else {
              proxy.$modal.msgError(res.msg);
            }
          });
        }
      }
    }
  });
};

const formatTree = (data) => {
  let dataList = data.map((item) => {
    return {
      ...item,
      children: item.children && formatTree(item.children),
    };
  });
  //dataList中有children为空的节点 删除
  let dataList2 = dataList.map((item) => {
    if (
      item.children === null ||
      item.children === undefined ||
      item.children.length === 0
    ) {
      delete item.children;
    }
    return {
      ...item,
    };
  });
  return dataList2;
};

const handleSee = (row) => {
  data.title = "查看防汛成员信息 - " + row.name;
  // getAllTown(row.townAdcd);
  data.dialogShow = true;
  proxy.$nextTick(() => {
    for (let i in data.addForm) {
      for (let j in row) {
        if (i == j) {
          data.addForm[i] = row[j];
        }
      }
    }
    data.addForm.id = row.id;
    data.addTable = row.dtos || [];
    console.log(row.dtos);
    console.log(data.addTable);
    formatObjectList();
  });
};
const handleEdit = (row) => {
  data.title = "编辑防汛成员信息 - " + row.name;
  // getAllTown(row.townAdcd);
  data.dialogShow = true;
  proxy.$nextTick(() => {
    for (let i in data.addForm) {
      for (let j in row) {
        if (i == j) {
          data.addForm[i] = row[j];
        }
      }
    }
    data.addForm.id = row.id;
    data.addTable = row.dtos || [];
    formatObjectList();
  });
};
const handleDelete = (row) => {
  proxy.$modal.confirm("是否确认删除该条数据?").then((res) => {
    deleteFloodPreventionMember(row.id).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess("删除成功");
        getList();
      } else {
        proxy.$modal.msgError(res.msg);
      }
    });
  });
};
const getList = () => {
  loading.value = true;
  queryFloodPreventionMember(data.queryForm).then((res) => {
    // data.tableData = formatterMeber(res.data.records) || [];
    console.log(res.data.records, "接口数据");

    data.tableData = res.data.records || [];
    data.tableData.forEach((item) => {
      if (item.dtos) {
        let callbackData = formatIndexTableData(item.dtos);
        item.objectName = callbackData.objectName;
        item.responsibleName = callbackData.responsibleName;
      }
    });
    data.total = res.data.total || 0;
    loading.value = false;
  });
};

const formatIndexTableData = (res) => {
  let callbackData = {
    objectName: [],
    responsibleName: [],
  };
  res.forEach((item) => {
    if (item.objectName || item.responsibleName) {
      callbackData["objectName"].push(item.objectName);
      callbackData["responsibleName"].push(item.responsibleName);
    }
  });
  //callback 去重
  callbackData.objectName = [...new Set(callbackData.objectName)];
  callbackData.responsibleName = [...new Set(callbackData.responsibleName)];
  // console.log(callbackData, "回调数据");
  return callbackData;
};

const resetQuery = () => {
  proxy.$refs.queryFormRef.resetFields();
  getList();
};
const addFamily = () => {
  data.title = "新增防汛成员信息";
  data.dialogShow = true;
  data.addForm = {
    name: "",
    identityId: "",
    dutyPhone: "",
    mobilePhone: "",
    organizationId: "",
    positionId: "",
    affiliatedUnit: "",
    deptPosition: "",
    sort: "",
    userId: "",
  };
  data.addTable = [];
};

function handleNodeClick(el) {
  data.queryForm.organizationId = el.id;
  getList();
}

const changeResponsibleObjectType = (e) => {
  // console.log(e);
  if (e) {
    data.responsibleList = data.responsibleListByCopy.filter(
      (item) => item.responsibilityType == e
    );
  }
  data.addTable.forEach((item, index) => {
    if (item.objectList != [] && index != data.addTable.length - 1) return; //不影响其他数据
    item.objectCode = "";
    item.objectList = [];
    item.responsibleId = "";
    switch (item.responsibleObjectType) {
      case 1:
        getPoliticalArea(item);
        break;
      case 2:
        getDangerArea(item);
        break;
      case 3:
        getReservoir(item);
        break;
    }
  });
};

const formatObjectList = () => {
  if (data.addTable.length > 0) {
    data.addTable.forEach((item, index) => {
      // item.objectCode = "";
      item.objectList = [];

      switch (item.responsibleObjectType) {
        case 1:
          // item.objectCode = item.adcd;
          getPoliticalArea(item);
          break;
        case 2:
          // item.objectCode = item.hazardousId;
          // item.objectCode = Number(item.objectCode)
          getDangerArea(item);
          break;
        case 3:
          // item.objectCode = item.registerCode;
          getReservoir(item);
          break;
      }
    });
  } else {
    proxy.$modal.msgError("防汛责任数据为空");
  }
};
// 获取租户当前所属的行政区，县
const getPoliticalArea = async (item) => {
  // 如果 objectList 已经有值，则直接返回
  if (Array.isArray(item.objectList) && item.objectList.length > 0) return;

  const res = await getAdcdTree({
    adcd: "",
  });

  item.objectList = res.data[0].children || [];
  console.log(item.objectList);
};
const formAreaHandleNodeClick = (el) => {
  console.log(el);
};
const getDangerArea = (item) => {
  dangerousAreaList({
    pageNum: 1,
    pageSize: 9999,
  }).then((res) => {
    item.objectList = res.data.records || [];
  });
};
const getReservoir = (item) => {
  selectRsList({
    pageNum: 1,
    pageSize: 9999,
  }).then((res) => {
    item.objectList = res.data.records || [];
  });
};
</script>
<style scoped lang="scss">
.left-tree {
  width: 300px;
  display: block;
  margin-right: 20px;
}
</style>
