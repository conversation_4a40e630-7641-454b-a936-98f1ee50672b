<template>
    <div class="app-container">
        <el-form ref="queryFormRef" :model="queryForm" inline class="form-container">
            <el-form-item label="户主姓名" prop="name">
                <el-input v-model="queryForm.name" placeholder="请输入户主姓名" clearable ></el-input>
            </el-form-item>
            <el-form-item label="手机号码" prop="phone">
                <el-input v-model="queryForm.phone" placeholder="请输入手机号码" clearable
                :formatter="(value) => `${value}`.replace(/[^\d]/g,'').substring(0, 11)"></el-input>
            </el-form-item>
            <el-form-item label="门牌号码" prop="houseNumber">
                <el-input v-model="queryForm.houseNumber" placeholder="请输入门牌号码"
                    clearable></el-input>
            </el-form-item>
            <el-form-item label="是否临水" prop="nearWater">
                <el-select v-model="queryForm.nearWater" placeholder="请选择" clearable>
                    <el-option label="是" :value="1" />
                    <el-option label="否" :value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否切坡" prop="cutSlope">
                <el-select v-model="queryForm.cutSlope" placeholder="请选择" clearable>
                    <el-option label="是" :value="1" />
                    <el-option label="否" :value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="重点关注类型" prop="focusTypes">
                <el-select v-model="queryForm.focusTypes" placeholder="请选择" clearable>
                    <el-option label="五保户" :value="1" />
                    <el-option label="优抚对象" :value="2" />
                    <el-option label="常年困难户" :value="3" />

                </el-select>
            </el-form-item>
            <el-form-item class="form-item-button">
                <el-button type="primary" @click="getList" icon="Search">查询</el-button>
                <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="content">
            <AdcdTree v-model="queryForm.villageCode" @getList="getList" />
            <div class="right-table">
                <div class="table-header">
                    <el-button type="success" icon="CirclePlus" @click="addFamily()">新增</el-button>
                </div>
                <el-table :data="tableData" stripe
                    v-loading="loading">
                    <el-table-column type="index" label="序号" width="80" />
                    <el-table-column prop="name" label="户主姓名" />
                    <el-table-column prop="phone" label="手机号码" />
                    <el-table-column prop="houseNumber" label="门牌号码" />
                    <el-table-column prop="householdSize" label="家庭人口(人)" />
                    <el-table-column prop="houseArea" label="住房建筑面积(㎡)" />
                    <el-table-column prop="households" label="户数(户)" />
                    <el-table-column prop="nearWater" label="是否临水">
                        <template #default="scope">
                            <el-tag v-if="scope.row.nearWater == 1" type="success">是</el-tag>
                            <el-tag v-else-if="scope.row.nearWater == 0" type="danger">否</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="cutSlope" label="是否切坡">
                        <template #default="scope">
                            <el-tag v-if="scope.row.cutSlope == 1" type="success">是</el-tag>
                            <el-tag v-else-if="scope.row.cutSlope == 0" type="danger">否</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="focusTypes" label="重点关注类型">
                        <template #default="scope">
                            <el-tag v-if="scope.row.focusTypes == 1" type="success">五保户</el-tag>
                            <el-tag v-else-if="scope.row.focusTypes == 2" type="warning">优抚对象</el-tag>
                            <el-tag v-else-if="scope.row.focusTypes == 3" type="danger">常年困难户</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="address" label="操作" width="210" align="center">
                        <template #default="scope">
                            <el-button type="primary" link icon="View" @click="handleSee(scope.row)"
                                >查看</el-button>
                            <el-button type="primary" link icon="Edit" @click="handleEdit(scope.row)"
                                >编辑</el-button>
                            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
                    @pagination="getList" />
            </div>
        </div>
        <el-dialog v-model="dialogShow" :title="title" style="width: 40%;" class="addForm" >
            <el-form ref="addFormRef" :model="addForm" label-width="auto" inline :rules="rules"
                :disabled="title.includes('查看居民信息')" style="display: flex;flex-wrap: wrap;">
                <el-form-item label="户主姓名" prop="name">
                    <el-input v-model="addForm.name" placeholder="请输入户主姓名" :formatter="(value) => `${value}`.substring(0,10)"
                        style="width: 180px;" :maxlength="10"></el-input>
                </el-form-item>
                <el-form-item label="身份ID" prop="identityId">
                    <el-input v-model="addForm.identityId" placeholder="请输入身份ID" style="width: 180px;"
                    :formatter="(value) => `${value}`.replace(/[^0-9Xx]/g, '').substring(0,18)"></el-input>
                </el-form-item>
                <el-form-item label="手机号码" prop="phone">
                    <el-input v-model="addForm.phone" placeholder="请输入手机号码" style="width: 180px;"
                    :formatter="(value) => `${value}`.replace(/[^\d]/g,'').substring(0,11)"></el-input>
                </el-form-item>
                <el-form-item label="所属区县" prop="areaCode">
                    <!-- <el-select v-model="addForm.areaCode" placeholder="请选择" style="width: 180px;" clearable
                        @change="addFormAreaChange">
                        <el-option :label="item.adnm" :value="item.adcd" highlight-current v-for="item in areaList"
                            :key="item.adcd" />

                    </el-select> -->
                    <el-tree-select  v-model="addForm.areaCode" :data="areaList" style="width: 180px;"
                    :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="adcd" placeholder="选择所属县区"
                    :render-after-expand="false"
                    check-strictly @node-click="formAreaHandleNodeClick" />
                </el-form-item>
                <el-form-item label="所属乡镇" prop="townCode">
                    <el-select v-model="addForm.townCode" placeholder="请选择" style="width: 180px;" clearable
                        @change="addFormTownChange">
                        <el-option :label="item.adnm" :value="item.adcd" highlight-current v-for="item in townList"
                            :key="item.adcd" />

                    </el-select>
                </el-form-item>
                <el-form-item label="所属村社(社)" prop="villageCode">
                    <el-select v-model="addForm.villageCode" placeholder="请选择" style="width: 180px;" clearable>
                        <el-option :label="item.adnm" :value="item.adcd" highlight-current v-for="item in villageData"
                            :key="item.adcd" />

                    </el-select>
                </el-form-item>
                <el-form-item label="门牌号码" prop="houseNumber">
                    <el-input v-model="addForm.houseNumber" placeholder="请输入门牌号码" style="width: 180px;" :maxlength="100"></el-input>
                </el-form-item>
                <el-form-item label="房屋坐落位置" prop="houseAddress">
                    <el-input v-model="addForm.houseAddress" placeholder="请输入房屋坐落位置" style="width: 180px;" :maxlength="100"></el-input>
                </el-form-item>
                <el-form-item label="经度" prop="lgtd">
                    <!-- 只允许输入数字 和小数 -->
                    <el-input-number v-model="addForm.lgtd" placeholder="请输入" style="width: 180px;" :min="73" :max="136"
                        :precision="6"></el-input-number>
                </el-form-item>
                <el-form-item label="纬度" prop="lttd">
                    <el-input-number v-model="addForm.lttd" placeholder="请输入" style="width: 180px;" :min="3" :max="54"
                        :precision="6"></el-input-number>
                </el-form-item>
                <el-form-item label="家庭人口(人)" prop="householdSize">
                    <el-input v-model="addForm.householdSize" placeholder="请输入家庭人口(人)" style="width: 180px;"
                    :formatter="(value) => `${value}`.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="户数(户)" prop="householdSize">
                    <el-input v-model="addForm.households" placeholder="请输入户数(户)" style="width: 180px;"
                    :formatter="(value) => `${value}`.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="住房建筑面积(㎡)" prop="houseArea">
                    <el-input v-model="addForm.houseArea" placeholder="请输入住房建筑面积(㎡)" style="width: 180px;"
                    :formatter="(value) => `${value}`.replace(/[^\d.]/g,'')"

                        ></el-input>
                </el-form-item>
                <el-form-item label="住房建筑类型" prop="houseConstructionType">
                    <el-select v-model="addForm.houseConstructionType" placeholder="请选择" style="width: 180px;">
                        <el-option :label="item.label" :value="item.value" v-for="(item, index) in houseType"
                            :key="index" />

                    </el-select>
                </el-form-item>
                <el-form-item label="住房结构形式" prop="houseStructureForm">
                    <el-select v-model="addForm.houseStructureForm" placeholder="请选择" style="width: 180px;">
                        <el-option :label="item.label" :value="item.value" v-for="(item, index) in houseBuildBy"
                            :key="index" />
                    </el-select>
                </el-form-item>
                <el-form-item label="宅基高程(m)" prop="homesteadElevation">
                    <el-input v-model="addForm.homesteadElevation" placeholder="请输入宅基高程(m)" style="width: 180px;"
                        :formatter="(value) => `${value}`.replace(/[^\d.]/g,'')"
                        ></el-input>
                </el-form-item>
                <el-form-item label="是否临水" prop="nearWater">
                    <el-select v-model="addForm.nearWater" placeholder="请选择" style="width: 180px;">
                        <el-option label="是" :value="1" />
                        <el-option label="否" :value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否切坡" prop="cutSlope">
                    <el-select v-model="addForm.cutSlope" placeholder="请选择" style="width: 180px;">
                        <el-option label="是" :value="1" />
                        <el-option label="否" :value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="重点关注类型" prop="focusTypes">
                    <el-select v-model="addForm.focusTypes" placeholder="请选择" style="width: 180px;">
                        <el-option label="五保户" :value="1" />
                        <el-option label="优抚对象" :value="2" />
                        <el-option label="常年困难户" :value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注" prop="notes" style="width: 100%;">
                    <el-input v-model="addForm.notes" placeholder="请输入备注" type="textarea" show-word-limit
                        :maxlength="200" :rows="5" style="width: calc(100% - 60px);"></el-input>
                </el-form-item>
                <el-form-item label="空间预览" prop="geoType" v-if="title != '新增居民信息'">
                    <el-radio-group v-model="addForm.geoType" v-if="false">
                        <el-radio label="zip">矢量数据(shp-zip,kml,geojson文件)</el-radio>
                        <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                        <el-radio label="people">人工绘制</el-radio>
                    </el-radio-group>

                </el-form-item>
                <div style="width: 100%;height: 130px;" v-show="addForm.geoType === 'zip'">
                    <el-upload class="upload-demo" style="width: 100%;" ref="uploadRef" drag name="multipartFile"
                        :on-change="handleChange" :action="uploadUrl" :data="{
                        }" :headers="{ 'Authorization': token }" :limit="2" :on-success="handleSuccess">
                        <div class="el-upload__text">
                            拖拽上传 或 <em>点击上传</em>
                        </div>
                    </el-upload>
                </div>
                <div style="width: 100%;height: 350px;background: #555" v-if="title != '新增居民信息'">
                    <min-map @updateBound="updateBound" :geom="addForm.geom" :geoType="addForm.geoType"
                        :points="[addForm.lgtd, addForm.lttd]" :show-tool="addForm.geoType === 'people'"
                        style="width: 100%;height:100%"></min-map>
                </div>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="submitAdd" v-if="title == '新增居民信息'">保 存</el-button>
                    <el-button type="primary" @click="submitEdit" v-else-if="title.includes('编辑居民信息')">保 存</el-button>
                    <el-button v-if="!title.includes('查看居民信息')" @click="dialogShow = false">取 消</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from "vue";
import { getAdcdTree, selectTsList } from "@/api/watershed/ads";
import { residentList, addResident, updateResident, deleteResident, villageList } from '@/api/warning'
import MinMap from "@/components/Map/plugins/drawTool";
import { getToken } from '@/utils/auth'
import AdcdTree from "@/components/AdcdTree/index.vue";

defineOptions({
  name: 'Resident'
})

const token = getToken()
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
const { proxy } = getCurrentInstance();
const loading = ref(false)
const uploadRef = ref('')
const data = reactive({
    queryForm: {
        name: "",
        villageCode: '',
        phone: "",
        pageNum: 1,
        pageSize: 20,
        nearWater: "",
        cutSlope: "",
        focusTypes: "",
    },
    status: 'add',
    houseType: [
        { label: '一层住宅', value: 1 },
        { label: '二层住宅', value: 2 },
        { label: '三层住宅', value: 3 },
        { label: '四层住宅和其他', value: 4 }
    ],
    houseBuildBy: [
        { label: '砖木结构', value: 1 },
        { label: '钢筋混凝土结构', value: 2 },
        { label: '混合结构', value: 3 },
        { label: '其他结构', value: 4 }
    ],
    rules: {
        name: [
            { required: true, message: '请输入姓名', trigger: 'blur' },
        ],
        // identityId: [
        //     { required: true, message: '请输入身份证号', trigger: 'blur' },
        // ],
        // houseNumber: [
        //     { required: true, message: '请输入门牌号', trigger: 'blur' },
        // ],
        // phone: [
        //     { required: true, message: '请输入手机号', trigger: 'blur' },
        // ],
        // areaCode: [
        //     { required: true, message: '请选择区县', trigger: 'blur' },
        // ],
        // townCode: [
        //     { required: true, message: '请选择乡镇', trigger: 'blur' },
        // ],
        // villageCode: [
        //     { required: true, message: '请选择所属村社', trigger: 'blur' },
        // ],
        // linshui: []
    },
    dialogShow: false,
    title: '新增居民信息',
    addForm: {
        name: "",
        identityId: "",
        phone: "",
        villageCode: "",
        houseNumber: "",
        houseAddress: "",
        lgtd: "",
        lttd: "",
        householdSize: "",
        households: "", // 户数
        houseArea: "",
        houseConstructionType: "",
        houseStructureForm: "",
        homesteadElevation: "",
        nearWater: "",
        cutSlope: "",
        focusTypes: "",
        notes: '',
        geoType: "people",
        geom: ""
    },
    total: 0,
    tableData: [],
    townList: [],
    areaList: [],
    villageData: []
});
const { queryForm, dialogShow, addForm, title, rules, status, townList, areaList, villageData, houseType, houseBuildBy, tableData, total } = toRefs(data)
onMounted(() => {
    getAdcdList()//获取所有的3级行政区
    getList()
})

const addFormAreaChange = (e) => {
    getAllTown(e)
    data.addForm.townCode = ''
    data.townList = []
}
const addFormTownChange = (e) => {
    getAllVillage(e)
    data.addForm.villageCode = ''
    data.villageData = []
}

const getAllTown = (adcd) => {
    selectTsList({ pageNum: 1, pageSize: 9999, parentAdcd: adcd || '' }).then(res => {
        data.townList = res.rows || []
    })
}
const getAllVillage = (adcd) => {
    villageList({ pageNum: 1, pageSize: 9999, parentAdcd: adcd || '' }).then(res => {
        data.villageData = res.data.records || []
    })
}
const handleSee = (row) => {
    console.log(row)
    data.title = '查看居民信息 - ' + row.name
    data.dialogShow = true
    getAreaAndTown(row)
    data.addForm = row

}
const handleEdit = (row) => {
    data.title = '编辑居民信息 - ' + row.name
    data.dialogShow = true
    getAreaAndTown(row)
    data.addForm = row

}
const submitAdd = () => {
    proxy.$refs.addFormRef.validate(valid => {
        if (valid) {
            addResident(data.addForm).then(res => {
                if (res.code == 200) {
                    proxy.$modal.msgSuccess("新增成功");
                    data.dialogShow = false
                    getList()
                } else {
                    proxy.$modal.msgError(res.msg);
                }
            })
        }
    })
}
const submitEdit = () => {
    proxy.$refs.addFormRef.validate(valid => {
        if (valid) {
            updateResident(data.addForm).then(res => {
                if (res.code == 200) {
                    proxy.$modal.msgSuccess("修改成功");
                    data.dialogShow = false
                    getList()
                } else {
                    proxy.$modal.msgError(res.msg);
                }
            })
        }
    })
}
const getAdcdList = () => {
    getAdcdTree({
        adcd: ''
    }).then((res) => {
        data.areaList = res.data[0].children || []
    })
}
const getList = () => {
    loading.value = true
    residentList(data.queryForm).then(res => {
        data.tableData = res.data.records || []
        data.total = res.data.total || 0
        loading.value = false
    })
}

const getAreaAndTown = (row) => {
    getAllTown(row.areaCode)
    getAllVillage(row.townCode)
}

const handleDelete = (row) => {
    // 删除
    proxy.$confirm('确定删除该条数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        // 删除
        deleteResident(row.id).then(res => {
            if (res.code == 200) {
                proxy.$modal.msgSuccess("删除成功");
                getList()
            } else {
                proxy.$modal.msgError(res.msg);
            }

        })
    }).catch(() => {
        proxy.$message({
            type: 'info',
            message: '已取消删除'
        });
    });
}
const resetQuery = () => {
    proxy.$refs.queryFormRef.resetFields()
    data.queryForm.villageCode = ''
    getList()
}
const addFamily = () => {

    data.title = '新增居民信息'
    delete data.addForm.id
    data.dialogShow = true
    proxy.$refs.addFormRef.resetFields()
}
const handleChange = (file, fileList) => {
    if (fileList.length > 1) {
        fileList[0] = fileList[1]
        fileList.splice(1, 1);
    }
}
const handleSuccess = (response) => {
    if (response.code == 200) {
        form.value.geom = response.data
    } else {
        uploadRef.value?.clearFiles()
        proxy.$modal.msgError(response.msg);
    }
}
const updateBound = (geojson) => {
    // 提交geojson数据到后台
    console.log(geojson)
    form.value.geom = geojson
}

const formAreaHandleNodeClick = (node) => {
    console.log(node.children);
  if (!node.children || node.children.length === 0) {
    // 只选中最后一个层级节点
    console.log(node);
    data.addForm.areaCode = node.adcd
    getAllTown(node.adcd)
  } else {
    // 清空选中
    proxy.$modal.msgError("请选择区县");
    nextTick(() => {
        data.addForm.areaCode = ''
    })

  }
}
</script>
<style scoped lang="scss">
:deep(.addForm .el-form--inline .el-form-item) {
    margin-right: 0;
    width: 50%;
}

:deep(.el-form--inline .el-form-item) {
    margin-right: 0;
}

.right-table {
    margin-left: 20px;
}
</style>