<template>
  <div class="app-container">
    <!-- 添加或修改流域对话框 -->
    <el-row>
      <el-form
        ref="queryFormRef"
        :model="queryForm"
        inline
        class="form-container"
      >
        <el-form-item label="预案名称" prop="name">
          <el-input
            v-model="queryForm.name"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="发布年份" prop="releaseYear">
          <el-date-picker
            v-model="queryForm.releaseYear"
            type="year"
            placeholder="请选择发布年份"
            @change="changeYear"
          />
        </el-form-item>

        <el-form-item class="form-item-button">
          <el-button type="primary" @click="getList" icon="Search"
            >查询</el-button
          >
          <el-button @click="resetQuery" type="primary" plain icon="Refresh"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </el-row>
    <div class="content">
      <div class="right-table">
        <div class="table-header">
          <el-button type="success" icon="CirclePlus" @click="addFamily()"
            >新增</el-button
          >
        </div>
        <el-table :data="tableData" v-loading="loading">
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="name" label="预案名称">
            <template #default="scope">
              <el-link
                type="primary"
                size="mini"
                @click="handleSee(scope.row)"
                style="margin-right: 10px"
                >{{ scope.row.name }}</el-link
              >
            </template>
          </el-table-column>
          <el-table-column prop="releaseYear" label="发布年份" />
          <el-table-column prop="managementUnit" label="发布单位" />

          <el-table-column
            prop="address"
            label="操作"
            width="210"
            align="center"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                icon="View"
                @click="handleSee(scope.row)"
                >查看</el-button
              >
              <el-button
                type="primary"
                link
                icon="Edit"
                @click="handleEdit(scope.row)"
                >编辑</el-button
              >
              <el-button
                type="danger"
                link
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <el-dialog v-model="dialogShow" :title="title" style="width: 40%">
      <el-form
        ref="addFormRef"
        :model="addForm"
        label-width="auto"
        inline
        :rules="rules"
        :disabled="title == '查看预案信息'"
        style="display: flex; flex-wrap: wrap"
      >
        <el-form-item label="预案名称" prop="name">
          <el-input
            v-model="addForm.name"
            placeholder="请输入"
            style="width: 220px"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="发布年份" prop="releaseYear">
          <el-date-picker
            v-model="addForm.releaseYear"
            type="year"
            placeholder="请选择年份"
            style="width: 220px"
          />
        </el-form-item>

        <el-form-item label="预案类型" prop="reserveType">
          <el-select
            v-model="addForm.reserveType"
            placeholder="请选择预案类型"
            style="width: 220px"
          >
            <el-option label="区域预案" value="1" />
            <el-option label="工程预案" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="编制对象" prop="object">
          <el-select
            v-if="addForm.reserveType == 2"
            v-model="addForm.object"
            placeholder="请选择水库"
            style="width: 220px"
          >
            <el-option
              v-for="item in shuikuList"
              :label="item.resName"
              :value="item.registerCode"
              :key="item.registerCode"
            />
          </el-select>
          <el-tree-select
            v-else
            v-model="addForm.object"
            :data="areaList"
            style="width: 220px"
            :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
            value-key="adcd"
            placeholder="选择所属县区"
            check-strictly
            @node-click="formAreaHandleNodeClick"
            :render-after-expand="false"
          />
        </el-form-item>
        <el-form-item label="管理单位" prop="managementUnit">
          <el-input
            v-model="addForm.managementUnit"
            placeholder="请输入"
            style="width: 220px"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="notes" style="width: 100%">
          <el-input
            v-model="addForm.notes"
            placeholder="请输入"
            type="textarea"
            :rows="5"
            show-word-limit
            :maxlength="200"
          ></el-input>
        </el-form-item>
        <el-form-item label="附件" prop="fileUrl" style="width: 100%">
          <el-upload
            v-if="title !== '查看预案信息' && addForm.fileUrl == ''"
            :action="uploadParams.action"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="handleBeforeUpload"
            :data="uploadParams.data"
            :headers="uploadParams.headers"
            :before-remove="handleBeforeRemove"
            ref="fileUpload"
            :limit="1"
          >
            <el-button type="primary">上传文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                请上传doc/pdf/docx文件类型,文件大小不超过20M
              </div>
            </template>
          </el-upload>
          <el-link
            style="display: block; width: 90%"
            v-if="title !== '新增预案信息' && !!addForm.fileUrl"
            type="primary"
            @click="downloadFile(addForm.fileUrl)"
          >
            {{ addForm.fileUrl.substring(37) }}
          </el-link>
          <el-link
            v-if="title == '编辑预案信息' && addForm.fileUrl !== ''"
            type="danger"
            @click="
              () => {
                addForm.fileUrl = '';
              }
            "
            style="margin-left: 10px"
          >
            删除
          </el-link>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            type="primary"
            @click="submitAdd"
            v-if="title == '新增预案信息'"
            >保 存
          </el-button>
          <el-button
            type="primary"
            @click="submitEdit"
            v-else-if="title == '编辑预案信息'"
            >保 存
          </el-button>
          <el-button @click="dialogShow = false" v-if="title !== '查看预案信息'"
            >取 消</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs, watch } from "vue";
import { getToken } from "@/utils/auth";
import {
  getTreeOption,
  selectTsList,
  getAdcdTree,
  selectRsList,
} from "@/api/watershed/ads";
import {
  addFloodPreventionPlan,
  queryFloodPreventionPlan,
  deleteFloodPreventionPlan,
  updateFloodPreventionPlan,
  downloadByMain, //下载预案
} from "@/api/warning";

defineOptions({
  name: "PlanList",
});

const token = getToken();
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload");
const { proxy } = getCurrentInstance();
const uploadRef = ref("");
const loading = ref(false);
const data = reactive({
  total: 0,
  queryForm: {
    name: "",
    pageNum: 1,
    pageSize: 20,
    releaseYear: new Date(),
  },
  rules: {
    name: [{ required: true, message: "请输入行政区名称", trigger: "blur" }],
    releaseYear: [
      { required: true, message: "请选择发布年份", trigger: "blur" },
    ],
    reserveType: [
      { required: true, message: "请选择发布类型", trigger: "blur" },
    ],
    adcd: [{ required: true, message: "请选择编制对象", trigger: "blur" }],
  },
  dialogShow: false,
  title: "新增预案信息",
  addForm: {
    name: "",
    releaseYear: "",
    object: "",
    reserveType: "",
    orgName: "",
    mark: "",
    fileUrl: "",
  },
  propsTree: {
    value: "adcd",
    label: "adnm",
    children: "children",
  },
  tableData: [],
  adcdList: [],
  areaList: [],
  townList: [],
  filterText: "",
  uploadParams: {
    action: uploadFileUrl,
    data: {
      bucketName: "watershed",
    },
    headers: {
      Authorization: "Bearer " + getToken(),
    },
  },
  shuikuList: [],
});
const {
  queryForm,
  propsTree,
  adcdList,
  dialogShow,
  addForm,
  title,
  rules,
  total,
  areaList,
  tableData,
  filterText,
  shuikuList,
  uploadParams,
} = toRefs(data);
const beforeUpload = (file) => {
  // console.log(import.meta)
  const allowedExtensions = ["doc", "pdf", "docx"];
  const allowFileSize = 20;
  const extension = file.name.split(".").pop().toLowerCase();
  const isAllowed = allowedExtensions.includes(extension);
  if (!isAllowed) {
    proxy.$modal.msgError("文件类型错误");
    return false;
  }
  // 校检文件大小
  if (allowFileSize) {
    const isLt = file.size / 1024 / 1024 < allowFileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${allowFileSize} MB!`);
      return false;
    }
  }

  return true;
};
const uploadSuccessed = (res) => {
  console.log(res);
  proxy.$modal.msgSuccess("上传成功");
};
const uploadError = (error) => {
  console.log(error);
  proxy.$modal.msgError("上传失败");
};
const filterNode = (query, node) => {
  return node.adnm.includes(query);
};
onMounted(() => {
  getList();
  getTreeList();
  getAdcdList();
  getShuikuList();
});
// watch(filterText, (val) => {
//   proxy.$refs.treeRef.filter(val);
// });
watch(filterText, (val) => {
  if (val) {
    proxy.$refs.treeRef.filter(val);
  } else {
    proxy.$refs.treeRef.filter("");
    // Collapse all expanded nodes when filter is cleared
    proxy.$refs.treeRef?.setExpandedKeys([]);
  }
});
const getAdcdList = () => {
  getAdcdTree({
    adcd: "",
  }).then((res) => {
    data.areaList = res.data[0].children || [];
  });
};
const getShuikuList = () => {
  selectRsList({
    pageNum: 1,
    pageSize: 9999,
  }).then((res) => {
    data.shuikuList = res.data.records || [];
  });
};
const getTreeList = () => {
  getTreeOption(5).then((res) => {
    data.adcdList = formatTree(res.data);
  });
};
const submitAdd = () => {
  proxy.$refs.addFormRef.validate((valid) => {
    if (!valid) return;
    console.log(data.addForm);
    let addQuery = {
      ...data.addForm,
      adcd: data.addForm.reserveType == 1 ? data.addForm.object : "",
      registerCode: data.addForm.reserveType == 2 ? data.addForm.object : "",
      releaseYear: "2025",
    };
    console.log(addQuery);
    addFloodPreventionPlan(addQuery).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess("新增成功");
        data.dialogShow = false;
        getList();
      } else {
        proxy.$modal.msgError(res.msg);
      }
    });
  });
};
const submitEdit = () => {
  proxy.$refs.addFormRef.validate((valid) => {
    if (valid) {
      updateFloodPreventionPlan(data.addForm, data.addForm.id).then((res) => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess("修改成功");
          data.dialogShow = false;
          getList();
        } else {
          proxy.$modal.msgError(res.msg);
        }
      });
    }
  });
};
const handleUploadSuccess = (response, file) => {
  data.addForm.fileUrl = response.msg;
  proxy.$modal.msgSuccess("文件上传成功");
};

const handleUploadError = () => {
  proxy.$modal.msgError("文件上传失败");
};
// 文件上传相关方法
const handleBeforeUpload = (file) => {
  const isValidType = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ].includes(file.type);
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isValidType) {
    proxy.$modal.msgError("只能上传PDF或Word文档！");
    return false;
  }
  if (!isLt20M) {
    proxy.$modal.msgError("文件大小不能超过20MB！");
    return false;
  }
  return true;
};
// 移除文件之前的钩子
const handleBeforeRemove = (file, fileList) => {
  console.log("移除文件：", file, fileList);
  data.addForm.fileUrl = ""; // TODO: 处理移除文件的操作
  proxy.$modal.msgSuccess("文件移除成功");
};

const downloadFile = (file) => {
  try {
    // TODO: 调用文件下载API
    // 使用下载文件的函数
    downloadByMain({ fileName: file, bucketName: "watershed" });
  } catch (error) {
    console.error("文件下载失败:", error);
    proxy.$modal.msgError("文件下载失败");
  }
};
const getAllTown = (adcd) => {
  selectTsList({ pageNum: 1, pageSize: 9999, parentAdcd: adcd || "" }).then(
    (res) => {
      data.townList = res.rows || [];
    }
  );
};
const formatTree = (data) => {
  let dataList = data.map((item) => {
    return {
      ...item.data,
      children: item.children && formatTree(item.children),
    };
  });
  //dataList中有children为空的节点 删除
  let dataList2 = dataList.map((item) => {
    if (item.children.length === 0) {
      delete item.children;
    }
    return {
      ...item,
    };
  });
  return dataList2;
};

const handleSee = (row) => {
  data.title = "查看预案信息";
  getAllTown(row.townAdcd);
  data.dialogShow = true;
  proxy.$nextTick(() => {
    data.addForm = row;
  });
};
const handleEdit = (row) => {
  data.title = "编辑预案信息";
  getAllTown(row.townAdcd);
  data.dialogShow = true;
  proxy.$nextTick(() => {
    data.addForm = row;
  });
};
const handleDelete = (row) => {
  proxy.$modal.confirm("是否确认删除该条数据?").then((res) => {
    deleteFloodPreventionPlan(row.id).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess("删除成功");
        getList();
      } else {
        proxy.$modal.msgError(res.msg);
      }
    });
  });
};
const getList = () => {
  loading.value = true;
  console.log(data.queryForm);
  queryFloodPreventionPlan(data.queryForm).then((res) => {
    data.tableData = res.data.records || [];
    data.total = res.data.total || 0;
    loading.value = false;
  });
};
const resetQuery = () => {
  proxy.$refs.queryFormRef.resetFields();
  getList();
};
const addFamily = () => {
  data.title = "新增预案信息";
  data.dialogShow = true;
};
const changeYear = (e) => {
  console.log(e);
};
const formAreaHandleNodeClick = (node) => {
  console.log(node.children);
  if (!node.children || node.children.length === 0) {
    // 只选中最后一个层级节点
    console.log(node);
    data.addForm.object = node.adcd;
    getAllTown(node.adcd);
  } else {
    // 清空选中
    proxy.$modal.msgError("请选择区县");
    nextTick(() => {
      data.addForm.object = "";
    });
  }
};
</script>
<style scoped lang="scss">
:deep(.addForm .el-form--inline .el-form-item) {
  margin-right: 0;
  width: 50%;
}
</style>
