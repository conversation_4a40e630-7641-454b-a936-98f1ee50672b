<template>
    <div class="app-container">
        <el-form ref="queryFormRef" :model="queryForm" inline class="form-container">
                <el-form-item label="防汛组织名称" prop="name">
                    <el-input v-model="queryForm.organizationName" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-select v-model="queryForm.status" placeholder="请选择">
                        <el-option label="全部" value="" />
                        <el-option label="正常" :value="0" />
                        <el-option label="停用" :value="1" />
                    </el-select>
                </el-form-item>

            <el-form-item class="form-item-button">
                <el-button type="primary" @click="getList" icon="Search">查询</el-button>
                <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
            </el-form-item>
        </el-form>

        <div class="content">
            <div class="left-tree" v-if="false">
                <div class="head-container">
                    <el-input v-model="filterText" placeholder="请输入行政区名称" clearable prefix-icon="Search"
                        style="margin-bottom: 20px" />
                </div>
                <div class="head-container">
                    <el-tree-v2 :data="adcdList" :props="propsTree" :height="600" :filter-method="filterNode"
                        ref="treeRef" @node-click="handleNodeClick" highlight-current />
                </div>
            </div>
            <div class="right-table">
                <div class="table-header">
                    <el-button type="success" icon="CirclePlus" @click="addFamily">新增</el-button>
                </div>
                <el-table :data="tableData" stripe
                    v-loading="loading">
                    <el-table-column type="index" label="序号" width="80" />
                    <el-table-column prop="name" label="防汛组织名称" />
                    <el-table-column prop="dutyPhone" label="值班电话" />
                    <el-table-column prop="phone" label="手机号码" />
                    <el-table-column prop="responsibility" label="职责" />
                    <el-table-column prop="status" label="状态">
                        <template #default="scope">
                            <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">{{ scope.row.status === 0 ?
                                '正常' : '停用' }}</el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column prop="address" label="操作" width="210" align="center">
                        <template #default="scope">
                            <el-button type="primary" link icon="View" @click="handleSee(scope.row)"
                                >查看</el-button>
                            <el-button type="primary" link icon="Edit" @click="handleEdit(scope.row)"
                                >编辑</el-button>
                            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
                    @pagination="getList" />
            </div>
        </div>
        <el-dialog v-model="dialogShow" :title="title" style="width: 40%;">
            <el-form ref="addFormRef" :model="addForm" label-width="auto" inline :rules="rules"
                :disabled="title.includes('查看防汛组织信息')" style="display: flex;flex-wrap: wrap;">
                <el-form-item label="名称" prop="name">
                    <el-input v-model="addForm.name" placeholder="请输入" style="width: 180px;"
                    :formatter="(value) => `${value}`.replace(/[^a-zA-Z\d\u4e00-\u9fa5]*/g, '')"
                    maxlength="50"
                    ></el-input>
                </el-form-item>
                <el-form-item label="上级防汛组织" prop="parentId">
                    <el-select v-model="addForm.parentId" placeholder="请选择" style="width: 180px;" clearable
                        @change="addFormAreaChange">
                        <el-option :label="item.name" :value="item.id" highlight-current v-for="item in areaList"
                            :key="item.id" />

                    </el-select>
                </el-form-item>

                <el-form-item label="值班电话" prop="dutyPhone">
                    <el-input v-model="addForm.dutyPhone" placeholder="请输入" style="width: 180px;"
                    :formatter="(value) => `${value}`.replace(/[^0-9.]/g,'').substring(0,11)"></el-input>
                </el-form-item>
                <el-form-item label="手机号码" prop="phone">
                    <el-input v-model="addForm.phone" placeholder="请输入" style="width: 180px;"
                    :formatter="(value) => `${value}`.replace(/[^0-9.]/g,'').substring(0,11)"></el-input>
                </el-form-item>
                <el-form-item label="职责" prop="responsibility" style="width: 100%;">
                    <el-input v-model="addForm.responsibility" placeholder="请输入" style="width: 90%;" type="textarea"
                        :row="10" maxlength="200" show-word-limit></el-input>
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input v-model="addForm.sort" placeholder="请输入" style="width: 180px;"
                    :formatter="(value) =>  `${value}`.replace(/[^0-9.]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="addForm.status">
                        <el-radio :label="0">正常</el-radio>
                        <el-radio :label="1">停用</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer v-if="!title.includes('查看防汛组织信息')">
                <span class="dialog-footer">
                    <el-button type="primary" @click="submitAdd" v-if="title.includes('新增防汛组织信息')">保 存</el-button>
                    <el-button type="primary" @click="submitEdit" v-else-if="title.includes('编辑防汛组织信息')">保 存</el-button>
                    <el-button @click="dialogShow = false">取消</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs, watch } from "vue";
import { getToken } from '@/utils/auth'
import { getTreeOption, selectTsList } from "@/api/watershed/ads";
import { addFloodPreventionOrganizations, queryFloodPreventionOrganizations, deleteFloodPreventionOrganizations, updateFloodPreventionOrganizations } from "../../../api/warning";

defineOptions({
  name: 'FloodOrganization'
})

const token = getToken()
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
const { proxy } = getCurrentInstance();
const uploadRef = ref('')
const loading = ref(false)
const data = reactive({
    total: 0,
    queryForm: {
        organizationName: "",
        status: "",
        pageNum: 1,
        pageSize: 20
    },
    rules: {
        name: [
            { required: true, message: '请输入防汛组织名称', trigger: 'blur' },
        ],


    },
    dialogShow: false,
    title: '新增防汛组织信息',
    addForm: {
        name: "",
        parentId: '',
        dutyPhone: '',
        phone: "",
        responsibility: '',
        sort: '',
        status: 0,
    },
    propsTree: {
        value: 'adcd',
        label: 'adnm',
        children: 'children',
    },
    tableData: [],
    adcdList: [],
    areaList: [],
    townList: [],
    filterText: '',
});
const { queryForm, propsTree, adcdList, dialogShow, addForm, title, rules, total, areaList, townList, tableData, filterText } = toRefs(data)
const filterNode = (query, node) => {
    return node.adnm.includes(query)
}
onMounted(() => {
    getList()
    getTreeList()
    getParentOrgList()
})
// watch(filterText, (val) => {
//     proxy.$refs.treeRef.filter(val)
// })
watch(filterText, (val) => {
  if(val) {
    proxy.$refs.treeRef.filter(val)
  } else {
    proxy.$refs.treeRef.filter('')
    // Collapse all expanded nodes when filter is cleared
    proxy.$refs.treeRef?.setExpandedKeys([])
  }
})
const getParentOrgList = () => {
    queryFloodPreventionOrganizations({
        ...data.queryForm,
        pageSize:99999
    }).then((res) => {
        data.areaList =  res.data.records
    })
}
const getTreeList = () => {
    getTreeOption(5).then((res) => {

        data.adcdList = formatTree(res.data)
    })
}
const submitAdd = () => {
    proxy.$refs.addFormRef.validate(valid => {
        if (!valid) return;
        addFloodPreventionOrganizations(data.addForm).then(res => {
            if (res.code == 200) {
                proxy.$modal.msgSuccess("新增成功");
                data.dialogShow = false
                getList()
            } else {
                proxy.$modal.msgError(res.msg);
            }
        })
    })
}
const submitEdit = () => {
    proxy.$refs.addFormRef.validate(valid => {
        if (valid) {
            updateFloodPreventionOrganizations(data.addForm, data.addForm.id).then(res => {
                if (res.code == 200) {
                    proxy.$modal.msgSuccess("修改成功");
                    data.dialogShow = false
                    getList()
                } else {
                    proxy.$modal.msgError(res.msg);
                }
            })
        }
    })
}
const addFormAreaChange = (e) => {
    getAllTown(e)
    data.addForm.townCode = ''
    data.townList = []

}
const getAllTown = (adcd) => {
    selectTsList({ pageNum: 1, pageSize: 9999, parentAdcd: adcd || '' }).then(res => {
        data.townList = res.rows || []
    })
}
const formatTree = (data) => {
    let dataList = data.map(item => {
        return {
            ...item.data,
            children: item.children && formatTree(item.children)
        }
    })
    //dataList中有children为空的节点 删除
    let dataList2 = dataList.map(item => {
        if (item.children.length === 0) {
            delete item.children
        }
        return {
            ...item
        }
    })
    return dataList2
};

const handleSee = (row) => {
    data.title = '查看防汛组织信息'
    getAllTown(row.townAdcd)
    data.dialogShow = true
    proxy.$nextTick(() => {
        data.addForm = row

    })
}
const handleEdit = (row) => {
    data.title = '编辑防汛组织信息'
    getAllTown(row.townAdcd)
    data.dialogShow = true
    proxy.$nextTick(() => {
        data.addForm = row

    })
}
const handleDelete = (row) => {
    proxy.$modal.confirm('是否确认删除该条数据?').then(res => {
        deleteFloodPreventionOrganizations(row.id).then(res => {
            if (res.code == 200) {
                proxy.$modal.msgSuccess("删除成功");
                getList()
            } else {
                proxy.$modal.msgError(res.msg);
            }
        })
    })
}
const getList = () => {
    loading.value = true
    queryFloodPreventionOrganizations(data.queryForm).then(res => {
        data.tableData = res.data.records || []
        data.total = res.data.total || 0
        loading.value = false
    })
}
const resetQuery = () => {
    data.queryForm = {
        organizationName: "",
        status: "",
        pageNum: 1,
        pageSize: 20
    }
    getList()
}
const addFamily = () => {
    data.title = '新增防汛组织信息'
    data.dialogShow = true
}
const handleChange = (file, fileList) => {
    if (fileList.length > 1) {
        fileList[0] = fileList[1]
        fileList.splice(1, 1);
    }
}
const handleSuccess = (response) => {
    if (response.code == 200) {
        form.value.geom = response.data
    } else {
        uploadRef.value?.clearFiles()
        proxy.$modal.msgError(response.msg);
    }
}
const updateBound = (geojson) => {
    // 提交geojson数据到后台
    console.log(geojson)
    data.addForm.geom = geojson
}

function handleNodeClick(el) {
    data.queryForm.townAdcd = el.adcd;
    getList()
    //   handleQuery();
};
</script>
<style scoped lang="scss">
.left-tree {
    width: 300px;
    display: block;
    margin-right: 20px;
}
</style>