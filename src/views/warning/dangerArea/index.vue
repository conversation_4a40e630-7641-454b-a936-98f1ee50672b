<template>
    <div class="app-container">
        <!-- 添加或修改流域对话框 -->
        <el-form ref="queryFormRef" :model="queryForm" inline
            class="form-container">
            <el-form-item label="危险区名称" prop="name">
                <el-input v-model="queryForm.name" placeholder="请输入" clearable></el-input>
            </el-form-item>
            <el-form-item label="所属流域" prop="basinId">
                <el-tree-select v-model="queryForm.basinId" :data="transformDataForTreeSelect(waterAreaList)"
                    check-strictly clearable :render-after-expand="false" placeholder="请选择流域"
                    />
            </el-form-item>
            <el-form-item label="危险区等级" prop="grade">
                <el-select v-model="queryForm.grade" placeholder="请选择" clearable>
                    <el-option label="极高风险" :value="1" />
                    <el-option label="高风险" :value="2" />
                    <el-option label="低风险" :value="3" />
                </el-select>
            </el-form-item>

            <el-form-item class="form-item-button">
                <el-button type="primary" @click="getList" icon="Search">查询</el-button>
                <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="content">
            <AdcdTree v-model="queryForm.paramAdcd" @getList="getList" />
            <div class="right-table">
                <div class="table-header">
                    <el-button type="success" icon="CirclePlus" @click="addData">新增</el-button>
                </div>
                <el-table :data="tableData" stripe
                    v-loading="loading">
                    <el-table-column type="index" label="序号" width="60" />
                    <el-table-column prop="name" label="危险区名称" width="100" align="center"/>
                    <el-table-column prop="basinName" label="所属流域" width="100" align="center"/>
                    <el-table-column prop="address" label="危险区等级" width="100" align="center">
                        <template #default="scope">
                            <el-tag :type="scope.row.grade === 1 ? 'danger' : scope.row.grade === 2 ? 'warning' : ''">{{
                                scope.row.grade === 1 ? '极高风险' : scope.row.grade === 2 ? '高风险' : scope.row.grade === 3 ? '低风险' : '未知' }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="peoNum" label="危险区内人口(人)" width="150" align="center"/>
                    <el-table-column prop="floodStandard" label="洪水标准(年)" width="100" align="center"/>
                    <el-table-column prop="disasterConvergenceTime" label="成灾汇流时间(小时)" width="100" align="center"/>

                    <el-table-column prop="oneHouseholds" label="危险区内家庭经济情况(户)" align="center">
                        <el-table-column prop="address" label="总户数">
                            <template #default="scope">
                                {{ scope.row.oneHouseholds + scope.row.twoHouseholds + scope.row.threeHouseholds +
                                    scope.row.fourHouseholds }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="oneHouseholds" label="Ⅰ类" />
                        <el-table-column prop="twoHouseholds" label="Ⅱ类" />
                        <el-table-column prop="threeHouseholds" label="Ⅲ类" />
                        <el-table-column prop="fourHouseholds" label="Ⅳ类" />
                    </el-table-column>
                    <el-table-column prop="address" label="危险区内家庭住房情况(座)" align="center">
                        <el-table-column prop="address" label="总房屋数">
                            <template #default="scope">
                                {{ scope.row.oneHouses + scope.row.twoHouses + scope.row.threeHouses +
                                    scope.row.fourHouses }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="oneHouses" label="Ⅰ类" />
                        <el-table-column prop="twoHouses" label="Ⅱ类" />
                        <el-table-column prop="threeHouses" label="Ⅲ类" />
                        <el-table-column prop="fourHouses" label="Ⅳ类" />
                    </el-table-column>
                    <el-table-column prop="address" label="操作" width="220" align="center" fixed="right">
                        <template #default="scope">
                            <el-button type="primary" link icon="View" @click="handleSee(scope.row)"
                                >查看</el-button>
                            <el-button type="primary" link icon="Edit" @click="handleEdit(scope.row)"
                                >编辑</el-button>
                            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
                    @pagination="getList" />
            </div>
        </div>
        <el-dialog v-model="dialogShow" :title="title" style="width: 40%;">
            <el-form ref="addFormRef" :model="addForm" label-width="auto" inline :rules="rules"
                :disabled="title === '查看危险区信息'" style="display: flex;flex-wrap: wrap;justify-content: space-evenly;">
                <el-form-item label="危险区名称" prop="name">
                    <el-input v-model="addForm.name" placeholder="请输入" style="width: 180px;"></el-input>
                </el-form-item>
                <el-form-item label="危险区代码" prop="code">
                    <el-input v-model="addForm.code" placeholder="请输入" style="width: 180px;" :disabled="!title.includes('新增危险区信息')"
                        oninput="value = value.substring(0,18)"></el-input>
                </el-form-item>
                <el-form-item label="所属区县" prop="areaAdcd">
                    <el-tree-select  v-model="addForm.areaAdcd" :data="areaList" style="width: 180px;"
                    :props="{ value: 'adcd', label: 'adnm', children: 'children', emitPath: false }" value-key="adcd" placeholder="选择所属县区"
                    check-strictly @node-click="formAreaHandleNodeClick" filterable :render-after-expand="false" />
                </el-form-item>
                <el-form-item label="所属乡镇" prop="townAdcd">
                    <el-select v-model="addForm.townAdcd" placeholder="请选择" style="width: 180px;" clearable filterable
                        @change="addFormTownChange">
                        <el-option :label="item.adnm" :value="item.adcd" highlight-current v-for="item in townList"
                            :key="item.adcd" />

                    </el-select>
                </el-form-item>
                <el-form-item label="所属村社(社)" prop="villageAdcd">
                    <el-select v-model="addForm.villageAdcd" placeholder="请选择" style="width: 180px;" clearable filterable>
                        <el-option :label="item.adnm" :value="item.adcd" highlight-current v-for="item in villageData"
                            :key="item.adcd" />

                    </el-select>
                </el-form-item>
                <el-form-item label="所属流域" prop="basinId">
                    <el-tree-select v-model="addForm.basinId" :data="transformDataForTreeSelect(waterAreaList)"
                        check-strictly clearable :render-after-expand="false" placeholder="请选择流域"
                        style="width: 180px;" />
                </el-form-item>
                <el-form-item label="危险区等级" prop="grade">
                    <el-select v-model="addForm.grade" placeholder="请选择" style="width: 180px;">
                        <el-option label="极高风险" :value="1" />
                        <el-option label="高风险" :value="2" />
                        <el-option label="低风险" :value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="危险区内人口(人)" prop="peoNum">
                    <el-input v-model="addForm.peoNum" placeholder="请输入家庭人口(人)" style="width: 180px;"
                        oninput="value = value.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="洪水标准(年)" prop="peoNum">
                    <el-input v-model="addForm.floodStandard" placeholder="请输入洪水标准(年)" style="width: 180px;"
                        oninput="value = value.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="成灾汇流时间(小时)" prop="peoNum">
                    <el-input v-model="addForm.disasterConvergenceTime" placeholder="请输入成灾汇流时间(小时)" style="width: 180px;"
                        oninput="value = value.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="Ⅰ类家庭用户数(户)" prop="oneHouseholds">
                    <el-input v-model="addForm.oneHouseholds" placeholder="请输入家庭人口(人)" style="width: 180px;"
                        oninput="value = value.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="Ⅱ类家庭用户数(户)" prop="twoHouseholds">
                    <el-input v-model="addForm.twoHouseholds" placeholder="请输入家庭人口(人)" style="width: 180px;"
                        oninput="value = value.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="Ⅲ类家庭用户数(户)" prop="threeHouseholds">
                    <el-input v-model="addForm.threeHouseholds" placeholder="请输入家庭人口(人)" style="width: 180px;"
                        oninput="value = value.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="Ⅳ类家庭用户数(户)" prop="fourHouseholds">
                    <el-input v-model="addForm.fourHouseholds" placeholder="请输入家庭人口(人)" style="width: 180px;"
                        oninput="value = value.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="Ⅰ类房屋数(座)" prop="oneHouses">
                    <el-input v-model="addForm.oneHouses" placeholder="请输入家庭人口(人)" style="width: 180px;"
                        oninput="value = value.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="Ⅱ类房屋数(座)" prop="twoHouses">
                    <el-input v-model="addForm.twoHouses" placeholder="请输入家庭人口(人)" style="width: 180px;"
                        oninput="value = value.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="Ⅲ类房屋数(座)" prop="threeHouses">
                    <el-input v-model="addForm.threeHouses" placeholder="请输入家庭人口(人)" style="width: 180px;"
                        oninput="value = value.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="Ⅳ类房屋数(座)" prop="fourHouses">
                    <el-input v-model="addForm.fourHouses" placeholder="请输入家庭人口(人)" style="width: 180px;"
                        oninput="value = value.replace(/[^\d]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="备注" prop="remark" style="width: 100%;" label-width="160px">
                    <el-input v-model="addForm.remark" placeholder="请输入备注" type="textarea"
                        style="width: calc(100% - 40px);" show-word-limit :maxlength="200" :rows="5"></el-input>
                </el-form-item>
                <el-form-item label="空间数据" prop="geoType">
                    <el-radio-group v-model="addForm.geoType">
                        <el-radio label="zip">矢量数据(shp-zip,kml,geojson文件)</el-radio>
                        <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                        <el-radio label="people">人工绘制</el-radio>
                    </el-radio-group>

                </el-form-item>
                <div style="width: 100%;height: 130px;" v-show="addForm.geoType === 'zip'">
                    <el-upload class="upload-demo" style="width: 100%;" ref="uploadRef" drag name="multipartFile"
                        :on-change="handleChange" :action="uploadUrl" :data="{
                        }" :headers="{ 'Authorization': token }" :limit="2" :on-success="handleSuccess" on-progress="handleProgress">
                        <div class="el-upload__text">
                            （只支持面图层）拖拽上传 或 <em>点击上传</em>
                        </div>
                    </el-upload>
                </div>
                <div style="width: 100%;height: 350px;background: #555">
                    <min-map @updateBound="updateBound" :geom="addForm.geom" :show-tool="addForm.geoType === 'people'"
                        style="width: 100%;height:100%"></min-map>
                </div>
            </el-form>
            <template #footer v-if="!title.includes('查看危险区信息')">
                <span class="dialog-footer">
                    <el-button type="primary" @click="submitAdd" v-if="title.includes('新增危险区信息')">保 存</el-button>
                    <el-button type="primary" @click="submitEdit" v-else-if="title.includes('编辑危险区信息')">保 存</el-button>
                    <el-button @click="dialogShow = false">取 消</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from "vue";
import MinMap from "@/components/Map/plugins/drawTool";
import AdcdTree from "@/components/AdcdTree/index.vue";
import { getAdcdTree, selectTsList, selectStlyList } from "@/api/watershed/ads";
import { getToken } from '@/utils/auth'
import { addDangerousArea, updateDangerousArea, deleteDangerousArea, dangerousAreaList, dangerousAreaDetail, villageList, } from '@/api/warning'

defineOptions({
  name: 'DangerArea'
})

const token = getToken()
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
const { proxy } = getCurrentInstance();
const uploadRef = ref('')
const loading = ref(false)
const data = reactive({
    queryForm: {
        name: "",
        basinId: "",
        grade: "",
        pageNum: 1,
        pageSize: 20,
        geoType: 'people',
        paramAdcd: "",
        geom: ""
    },
    rules: {
        name: [
            { required: true, message: '请输入危险区名称', trigger: 'blur' },
        ],
        code: [
            { required: true, message: '请输入危险区代码', trigger: 'blur' },
            { required: true, message: '危险区代码为18位字符', min: 18, trigger: 'blur' },
        ],
        townAdcd: [
            { required: true, message: '请选择乡镇', trigger: 'blur' },
        ],
        areaAdcd: [
            { required: true, message: '请选择区县', trigger: 'blur' },
        ],
        // villageAdcd: [
        //     { required: true, message: '请选择所属村社', trigger: 'blur' },
        // ],
        basinId: [
            { required: true, message: '请选择流域', trigger: 'blur' },
        ],
        grade: [
            { required: true, message: '请选择等级', trigger: 'blur' },
        ],
    },
    dialogShow: false,
    title: '新增危险区信息',
    addForm: {
        name: "",
        idCode: "",
        addressCode: "",
        basinId: "",
        grade: "",
        phone: "",
        city: "",
        town: "",
        linshui: "",
        geoType: "people",
        geom: ""
    },
    tableData: [],
    townList: [],
    areaList: [],
    villageData: [],
    waterAreaList: [],
    total: 0,
});
const { queryForm, tableData, dialogShow, addForm, title, rules, townList, areaList, villageData, waterAreaList, total } = toRefs(data)
onMounted(() => {
    getList()
    getAdcdList()//获取所有的3级行政区
    getAllWater()
})
const getAllWater = async () => {
    let res = await selectStlyList({ pageNum: 1, pageSize: 999 })
    data.waterAreaList = res.data || []
}
const getAdcdList = () => {
    getAdcdTree({
        adcd: ''
    }).then((res) => {
        console.log(res)
        data.areaList = res.data[0].children || []
    })
}

const getAreaAndTown = (row) => {
    getAllTown(row.areaCode)
    getAllVillage(row.townAdcd)
}
const getAllTown = (adcd) => {
    selectTsList({ pageNum: 1, pageSize: 9999, parentAdcd: adcd || '' }).then(res => {
        data.townList = res.rows || []
    })
}
const getAllVillage = (adcd) => {
    villageList({ pageNum: 1, pageSize: 9999, parentAdcd: adcd || '' }).then(res => {
        data.villageData = res.data.records || []
    })
}
const addFormAreaChange = (e) => {
    getAllTown(e)
    data.addForm.townAdcd = ''
    data.addForm.villageAdcd = ''
    data.townList = []
}
const formAreaHandleNodeClick = (node) => {
    console.log(node.children);
  if (!node.children || node.children.length === 0) {
    // 只选中最后一个层级节点
    console.log(node);
    data.addForm.areaAdcd = node.adcd
    getAllTown(node.adcd)
  } else {
    // 清空选中
    proxy.$modal.msgError("请选择区县");
    nextTick(() => {
        data.addForm.areaAdcd = ''
    })

  }
}
const transformDataForTreeSelect = (data) => {
    // 递归地转换数据以匹配 el-tree-select 的需求
    return data.map(item => ({
        label: item.data.name, // 使用 'name' 属性作为标签
        value: item.data.basinId, // 使用 'basinId' 属性作为值
        children: item.children ? transformDataForTreeSelect(item.children) : [], // 递归转换子节点
    }));
}
const addFormTownChange = (e) => {
    getAllVillage(e)
    data.addForm.villageAdcd = ''
    data.villageData = []
}

const getList = () => {
    loading.value = true
    dangerousAreaList(queryForm.value).then(response => {
        data.tableData = response.data.records || []
        data.total = response.data.total || 0
        loading.value = false
    })
}

const resetQuery = () => {
    data.queryForm.paramAdcd = ''
    proxy.$refs.queryFormRef.resetFields()
    getList()
}
const handleSee = (row) => {
    data.title = '查看危险区信息 - ' + row.name
    getAreaAndTown(row)
    data.dialogShow = true
    dangerousAreaDetail(row.id).then(response => {
        data.addForm = response.data
        data.addForm.geom = JSON.parse(response.data.geom)
        data.addForm['geoType'] = 'people'
    })

}
const handleEdit = (row) => {
    // await getInfo(row.id).then(response => {
    //     data.addForm = response.data
    // })
    data.title = '编辑危险区信息 - ' + row.name
    getAreaAndTown(row)
    data.dialogShow = true
    dangerousAreaDetail(row.id).then(response => {
        data.addForm = response.data
        data.addForm.geom = JSON.parse(response.data.geom)
        data.addForm['geoType'] = 'people'
        data.addForm.id = row.id
    })
}
const submitAdd = () => {
    proxy.$refs['addFormRef'].validate(valid => {
        if (valid) {
            // 提交
            let validGeom = data.addForm.geom == "" ? null: data.addForm.geom
            addDangerousArea({...data.addForm, geom: validGeom}).then(response => {
                if (response.code == 200) {
                    proxy.$modal.msgSuccess("新增成功");
                    data.dialogShow = false
                    getList()
                } else {
                    proxy.$modal.msgError(response.msg);
                }
            })

        }
    })
}
const submitEdit = () => {
    proxy.$refs['addFormRef'].validate(valid => {
        if (valid) {
            // 提交
            updateDangerousArea(data.addForm,data.addForm.id).then(response => {
                if (response.code == 200) {
                    proxy.$modal.msgSuccess("修改成功");
                    data.dialogShow = false
                    getList()
                } else {
                    proxy.$modal.msgError(response.msg);
                }
            })
        }
    })
}
const handleDelete = (row) => {
    proxy.$modal.confirm('是否确认删除').then(() => {
        // 删除
        deleteDangerousArea(row.id).then(response => {
            if (response.code == 200) {
                proxy.$modal.msgSuccess("删除成功");
                getList()
            } else {
                proxy.$modal.msgError(response.msg);
            }
        })
    }).catch(() => {

    })
}
const addData = () => {
    data.title = '新增危险区信息'
    data.dialogShow = true
    delete data.addForm.id
    data.addForm = {
        name: "",
        idCode: "",
        addressCode: "",
        basinId: "",
        grade: "",
        phone: "",
        city: "",
        town: "",
        linshui: "",
        geoType: "people",
        geom: ""
    }
    // proxy.$refs.addFormRef.resetFields()
}
const handleChange = (file, fileList) => {
    if (fileList.length > 1) {
        fileList[0] = fileList[1]
        fileList.splice(1, 1);
    }
}
const handleProgress = (event, file, fileList) => {
    console.log(event, file, fileList)
}
const handleSuccess = (response) => {
    if (response.code == 200) {
        data.addForm.geom = response.data
    } else {
        uploadRef.value?.clearFiles()
        proxy.$modal.msgError(response.msg);
    }
}
const updateBound = (geojson) => {
    // 提交geojson数据到后台
    console.log(geojson)
    data.addForm.geom = geojson
}
</script>
<style scoped lang="scss">
.right-table {
    margin-left: 20px;
}
</style>