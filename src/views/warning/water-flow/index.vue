<!-- 水位流量预警指标设置-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="测站名称" prop="stnm">
        <el-input
          v-model="queryParams.stnm"
          placeholder="请输入测站名称"
          clearable
        />
      </el-form-item>

      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="content">
      <AdcdTree
        v-model="queryParams.adcd"
        @getList="getList"
        defaultExpandAll
      />
      <div class="right-table">
        <div class="table-header">
          <!-- <el-button type="success" icon="CirclePlus" @click="handleAdd"
            >新增</el-button
          > -->
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </div>
        <el-table
          v-loading="loading"
          :data="dataList"
          :span-method="objectSpanMethod"
          stripe
          >
          <!--           <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>-->
          <el-table-column
            type="index"
            label="序号"
            align="center"
            width="80"
          ></el-table-column>
          <el-table-column
            prop="stcd"
            label="测站编码"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="stnm"
            label="测站名称"
            align="center"
          ></el-table-column>
          <el-table-column prop="warnLevel" label="预警等级" align="center">
            <template #default="scope">
              <template v-if="scope.row.warnLevel === 2">
                <div style="color: #f78c08">准备转移</div>
              </template>
              <template v-else>
                <div style="color: #fc4949">立即转移</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="waterLevel" label="水位(m)" align="center">
            <template #default="scope">
              <template
                v-if="scope.row.waterLevel >= 0 && scope.row.waterLevel != null"
              >
                <div>{{ scope.row.waterLevel }}</div>
              </template>
              <template v-else>
                <div style="color: rgba(255, 85, 85, 0.82)">未设置</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="waterFlow" label="流量(m³/s)" align="center">
            <template #default="scope">
              <template
                v-if="scope.row.waterFlow >= 0 && scope.row.waterFlow != null"
              >
                <div>{{ scope.row.waterFlow }}</div>
              </template>
              <template v-else>
                <div style="color: rgba(255, 85, 85, 0.82)">未设置</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="210"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <template v-if="true">
                <el-button
                  link
                  type="primary"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                  >编辑</el-button
                >
              </template>
              <!-- <template v-else>
                  <el-button link type="primary" icon="Edit" @click="gotoDangSt(scope.row)">关联危险区</el-button>
                </template> -->
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 添加或修改流域对话框 -->
    <el-dialog :title="title" v-model="open" width="720px" append-to-body>
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="130px">
        <el-row>
          <!--              <el-col :span="12">-->
          <!--                <el-form-item label="危险区名称" prop="dangerousName">-->
          <!--                  <el-input v-model="form.dangerousName" placeholder="请输入危险区名称"   />-->
          <!--                </el-form-item>-->
          <!--              </el-col>-->
          <el-col :span="12">
            <el-form-item label="测站名称" prop="dangId">
              <el-input
                v-model="form.stnm"
                placeholder=""
                disabled
                style="width: 150px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预警等级" prop="warnLevel">
              <div style="font-weight: bold">
                <div style="color: #f78c08" v-if="form.warnLevel === 2">
                  准备转移
                </div>
                <div style="color: #fc4949" v-else>立即转移</div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="水位(m)" prop="waterLevel">
              <el-input-number
                v-model="form.waterLevel"
                :min="0"
                placeholder="请输入数值"
                :precision="3"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流量(m³/s)" prop="waterFlow">
              <el-input-number
                v-model="form.waterFlow"
                :min="0"
                placeholder="请输入数值"
                :precision="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import { useRouter } from "vue-router";
import { delLy } from "@/api/watershed/ads";
import { saveWaterLevelPage, selectWaterLevelPage } from "@/api/warning/index";
import AdcdTree from "@/components/AdcdTree/index.vue";

defineOptions({
  name: "Menu",
});

const { proxy } = getCurrentInstance();
const router = useRouter();
const dataList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const curGeom = ref("");
const curTitle = ref("");
const total = ref(0);

//需要判断的属性组
const spanProps = ["adnm", "stcd", "stnm"];

let rowSpansMap = new Map(); //存需要开始合并的行号，向下合并多少行

/**
 * 根据列表数据得出需要合并的行
 * @param data 列表数据
 */
const spanPropGroup = (data) => {
  let oldRow; //需要合并的行
  rowSpansMap = new Map(); //重置Map

  oldRow = data[0]; //默认第0行为需要合并的行
  rowSpansMap.set(0, 1); //第0行，向下合并一行(其实就是自己单独一行)
  let spanRow = 0; //记录需要开始合并的行号
  for (let i = 1; i < data.length; i++) {
    const item = data[i];
    let isSame = true;
    //遍历需要判断的属性判断对应值是否全部相等
    for (let j = 0; j < spanProps.length; j++) {
      const prop = spanProps[j];
      //只要有一个属性值不相等则记录新的需要合并的行号
      if (item[prop] != oldRow[prop]) {
        oldRow = item;
        rowSpansMap.set(i, 1);
        spanRow = i;
        isSame = false;
        break;
      }
    }
    //如果所有属性值相同则所需要合并的行数+1
    if (isSame) {
      let span = rowSpansMap.get(spanRow);
      rowSpansMap.set(spanRow, span + 1);
    }
  }
};

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 只有前两列需要合并
  if (columnIndex < 3) {
    //根据当前行号从map中获取开始合并的行根据当前行号从map中获取开始合并的行号，向下合并多少行
    const span = rowSpansMap.get(rowIndex);
    if (span != null) {
      return {
        rowspan: span, //向下合并span行
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};

const data = reactive({
  form: {},
  queryParams: {
    adcd: undefined,
    stnm: undefined,
    pageNum: 1,
    pageSize: 10,
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

/** 查询流域列表 */
function getList() {
  loading.value = true;
  selectWaterLevelPage(queryParams.value).then((res) => {
    let ll = res.data.records || [];
    let ls = [];
    // 预警等级（1立即转移2准备转移）
    ll.forEach((item) => {
      let waterLevelWarnList = item.warnLists || [];
      if (waterLevelWarnList.length === 2) {
      } else if (waterLevelWarnList.length === 1) {
        // MD如果是只有一个特么还得补充一个
        let rain = waterLevelWarnList[0];
        if (rain.warnLevel === 1) {
          waterLevelWarnList.push({
            stcd: item.stcd,
            stnm: item.stnm,
            addvcd: item.addvcd,
            adnm: item.adnm,
            danId: item.danId,
            warnLevel: 2,
          });
        } else {
          waterLevelWarnList.push({
            stcd: item.stcd,
            stnm: item.stnm,
            addvcd: item.addvcd,
            adnm: item.adnm,
            danId: item.danId,
            warnLevel: 1,
          });
        }
      } else {
        waterLevelWarnList = [
          {
            addvcd: item.addvcd,
            stcd: item.stcd,
            stnm: item.stnm,
            adcd: item.adcd,
            adnm: item.adnm,
            danId: item.danId,
            warnLevel: 1,
          },
          {
            addvcd: item.addvcd,
            stcd: item.stcd,
            stnm: item.stnm,
            adcd: item.adcd,
            adnm: item.adnm,
            danId: item.danId,
            warnLevel: 2,
          },
        ];
      }
      waterLevelWarnList.forEach((rain) => {
        ls.push({
          addvcd: item.addvcd,
          stcd: item.stcd,
          stnm: item.stnm,
          adnm: item.adnm,
          danId: item.danId,
          id: rain.id,
          warnLevel: rain.warnLevel,
          waterLevel: rain.waterLevel,
          waterFlow: rain.waterFlow,
        });
      });
    });

    total.value = res.data.total || 0;
    dataList.value = ls;
    loading.value = false;

    //进行传递数据
    spanPropGroup(dataList.value);
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    addFlag: flag,
    stnm: undefined,
    adcd: undefined,
  };
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.adcd = "";
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
  reset(true);
  if (row != null && row.lycode) {
    form.value.pcode = row.lycode;
  } else {
    form.value.pcode = "";
  }
  open.value = true;
  title.value = "添加流域";
}

function gotoDangSt(row) {
  router.push({ path: "/warning/dangerAndStcd" });
  // 去危险区页面关联
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  let obj = Object.assign({}, row);
  // console.log(row)
  delete obj.children;
  delete obj.createBy;
  delete obj.createTime;
  delete obj.updateBy;
  delete obj.updateTime;
  delete obj.remark;
  form.value = obj;

  open.value = true;
  title.value = "编辑水情预警 - " + row.stnm;
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate((valid) => {
    if (valid) {
      if (form.value.addFlag) {
        // form.value.adcd = form.value.adcds[form.value.adcds.length-1]
        // saveRainWarn(form.value).then(response => {
        //   proxy.$modal.msgSuccess("新增成功");
        //   open.value = false;
        //   getList();
        // });
      } else {
        saveWaterLevelPage(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.lynm + '"的数据项?')
    .then(function () {
      return delLy(row.lycode);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
:deep(.el-input-number__decrease) {
  display: none;
}

:deep(.el-input-number__increase) {
  display: none;
}

:deep(.el-input-number .el-input__wrapper) {
  padding: 0;
  padding-left: 20px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

.left-tree {
  margin-right: 20px;
}

.table-header {
  display: flex;
  justify-content: flex-end;
}
</style>
