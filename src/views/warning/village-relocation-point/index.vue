<script setup>
import { ref, onMounted } from "vue";
import { selectTsList } from "@/api/watershed/ads";
import { PREVENTION_ZONES_TYPES, getTypeLabel } from "@/enums/warning";
import {
  queryVillagePlacementList,
  queryVillagePlacementDetail,
  deleteVillagePlacement,
} from "@/api/warning";
import VillageRelocationDialog from "./components/VillageRelocationDialog.vue";
import AdcdTree from "@/components/AdcdTree/index.vue";

defineOptions({
  name: "VillageRelocationPoint",
});

const { proxy } = getCurrentInstance();
const loading = ref(false);
const total = ref(0);
const queryForm = ref({
  villageName: "", // 村社名称
  preventionZonesType: "", // 防治区类型
  pageNum: 1,
  pageSize: 20,
});
const dialogShow = ref(false);
const title = ref("新增村社转移安置信息");
const addForm = ref({
  villageCode: "",
  placementId: "",
  geoType: "polyline",
  memberIdList: [],
  geom: null,
  extraData: [],
});

const tableData = ref([]);
const townList = ref([]);

onMounted(() => {
  getList();
});

const getAllTown = (adcd) => {
  selectTsList({ pageNum: 1, pageSize: 9999, parentAdcd: adcd || "" }).then(
    (res) => {
      townList.value = res.rows || [];
    }
  );
};

const handleDetail = (row, type = "see") => {
  title.value =
    type === "see" ? "查看村社转移安置信息 - " + row.villageName : "编辑村社转移安置点 - " + row.villageName ;
  getAllTown(row.townAdcd);
  queryVillagePlacementDetail(row.id).then((res) => {
    addForm.value = res.data;
    if (res.data.geom == null) {
      addForm.value.geom = null;
    }
    if (res.data.dtos && res.data.dtos.length) {
      addForm.value.memberIdList = res.data.dtos.map((item) => item.memberId);
    }
    dialogShow.value = true;
  });
};

const handleDelete = (row) => {
  proxy.$modal.confirm("是否确认删除该条数据?").then((res) => {
    deleteVillagePlacement(row.id).then((res) => {
      if (res.code == 200) {
        proxy.$modal.msgSuccess("删除成功");
        getList();
      } else {
        proxy.$modal.msgError(res.msg);
      }
    });
  });
};

const getList = () => {
  loading.value = true;
  queryVillagePlacementList(queryForm.value).then((res) => {
    tableData.value = res.rows || [];
    total.value = res.total || 0;
    loading.value = false;
  });
};

const resetQuery = () => {
  proxy.$refs.queryFormRef.resetFields();
  getList();
};

const addFamily = () => {
  title.value = "新增村社转移安置信息";
  addForm.value = {
    villageCode: "",
    placementId: "",
    geoType: "polyline",
    memberIdList: [],
    geom: null,
    extraData: [],
  };
  dialogShow.value = true;
};
</script>

<template>
  <div class="app-container">
    <el-form
      ref="queryFormRef"
      :model="queryForm"
      inline
      class="form-container"
    >
      <el-form-item label="村社名称" prop="villageName">
        <el-input
          v-model="queryForm.villageName"
          placeholder="请输入村社名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="防治区类型" prop="preventionZonesType">
        <el-select
          v-model="queryForm.preventionZonesType"
          placeholder="请选择"
          clearable
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="item in PREVENTION_ZONES_TYPES"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search"
          >查询</el-button
        >
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="content">
      <!-- 左侧树形 -->
      <AdcdTree v-model="queryForm.adcd" @getList="getList" />

      <div class="right-table">
        <div class="table-header">
          <el-button type="success" icon="CirclePlus" @click="addFamily"
            >新增</el-button
          >
        </div>
        <el-table :data="tableData" stripe v-loading="loading">
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="villageName" label="村社" />
          <el-table-column prop="preventionZonesType" label="防治区类型">
            <template #default="scope">
              <span>
                {{
                  getTypeLabel(
                    scope.row.preventionZonesType,
                    "PREVENTION_ZONES_TYPES"
                  )
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="floodControlCapacity" label="防洪能力(年)" />
          <el-table-column prop="placementName" label="安置点" />
          <el-table-column
            prop="address"
            label="操作"
            width="220"
            align="center"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="handleDetail(scope.row, 'see')"
                icon="View"
                >查看</el-button
              >
              <el-button
                type="primary"
                link
                @click="handleDetail(scope.row, 'edit')"
                icon="Edit"
                >编辑</el-button
              >
              <el-button
                type="danger"
                link
                @click="handleDelete(scope.row)"
                icon="Delete"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <VillageRelocationDialog
      v-model:visible="dialogShow"
      :title="title"
      :formData="addForm"
      @getList="getList"
    />
  </div>
</template>

<style scoped lang="scss">
:deep(.addForm .el-form--inline .el-form-item) {
  margin-right: 0;
  width: 50%;
}

.right-table {
  margin-left: 20px;
}
</style>
