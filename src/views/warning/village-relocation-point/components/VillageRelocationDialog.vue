<script setup>
import { ref, watch } from "vue";
import MinMap from "@/components/Map/plugins/drawTool";
import * as turf from "@turf/turf";
import { getToken } from "@/utils/auth";
import {
  placeList,
  queryFloodPreventionMember,
  villageList,
  addVillagePlacement,
  updateVillagePlacement,
} from "@/api/warning";

defineOptions({
  name: "VillageRelocationDialog",
});

const visible = defineModel("visible", { default: false });

const props = defineProps({
  title: {
    type: String,
    default: "新增村社转移安置信息",
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  placementData: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  memberData: {
    type: Array,
    required: true,
    default: () => [],
  },
  routeData: {
    type: Object,
    default: () => null,
  },
  villageList: {
    type: Array,
    required: true,
    default: () => [],
  },
  placementList: {
    type: Array,
    required: true,
    default: () => [],
  },
  memberList: {
    type: Array,
    required: true,
    default: () => [],
  },
});

const emit = defineEmits(["getList"]);

const { proxy } = getCurrentInstance();
const uploadRef = ref("");
const token = getToken();
const uploadUrl = import.meta.env.VITE_APP_BASE_API + "/hydro/convert/shp-kml/";

const addForm = ref({
  villageCode: "",
  placementId: "",
  geoType: "polyline",
  memberIdList: [],
  geom: null,
  extraData: [],
});

const residentData = ref([]);
const villageData = ref([]);
const memberData = ref([]);

const rules = ref({
  villageCode: [{ required: true, message: "请选择村社", trigger: "change" }],
  placementId: [{ required: true, message: "请选择安置点", trigger: "change" }],
  memberIdList: [
    { required: true, message: "请选择转移负责人", trigger: "change" },
  ],
});

const extraData = ref(null);

const getVillageList = async () => {
  await villageList({ pageSize: 99999, pageNum: 1 }).then((res) => {
    villageData.value = res.data.records || [];
  });
};

const getResidentList = async () => {
  await placeList({ pageSize: 99999, pageNum: 1 }).then((res) => {
    residentData.value = res.data.records || [];
  });
};

const getMember = async () => {
  await queryFloodPreventionMember({ pageSize: 99999, pageNum: 1 }).then(
    (res) => {
      memberData.value = res.data.records || [];
    }
  );
};

onMounted(() => {
  getVillageList();
  getResidentList();
  getMember();
});

watch(
  () => props.formData,
  (newVal) => {
    if (newVal) {
      addForm.value = { ...newVal };
      if (newVal.dtos && newVal.dtos.length) {
        addForm.value.memberIdList = newVal.dtos.map((item) => item.memberId);
      }
    }
  },
  { immediate: true }
);

watch(
  [() => addForm.value.villageCode, () => addForm.value.placementId],
  async ([newVillageCode, newPlacementId]) => {
    if (newVillageCode && newPlacementId) {
      const village = villageData.value.find((v) => v.adcd === newVillageCode);
      const placement = residentData.value.find(
        (p) => p.id === newPlacementId
      );
      if (village?.lgtd && village?.lttd && placement?.lgtd && placement?.lttd) {
        try {


          const placementPoint = turf.point([
            Number(placement.lgtd),
            Number(placement.lttd),
          ]);
          const villagePoint = turf.point([
            Number(village.lgtd),
            Number(village.lttd),
          ]);

          extraData.value = {
            type: "FeatureCollection",
            features: [villagePoint, placementPoint],
          };
        } catch (error) {
          console.error("处理地理数据时出错:", error);
          extraData.value = null;
        }
      } else {
        extraData.value = null;
      }
    } else {
      extraData.value = null;
    }
  },
  { immediate: true }
);

const handleClose = () => {
  visible.value = false;
};

const updateBound = (geojson) => {
  addForm.value.geom = geojson;
};

const handleChange = (file, fileList) => {
  if (fileList.length > 1) {
    fileList[0] = fileList[1];
    fileList.splice(1, 1);
  }
};

const handleSuccess = (response) => {
  if (response.code == 200) {
    addForm.value.geom = response.data;
  } else {
    uploadRef.value?.clearFiles();
    proxy.$modal.msgError(response.msg);
  }
};

const submitForm = () => {
  proxy.$refs.addFormRef.validate((valid) => {
    if (valid) {
      const formData = addForm.value;
      if (props.title === "新增村社转移安置信息") {
        addVillagePlacement(formData).then((res) => {
          if (res.code == 200) {
            proxy.$modal.msgSuccess("新增成功");
            handleClose();
            emit("getList");
          } else {
            proxy.$modal.msgError(res.msg);
          }
        });
      } else {
        updateVillagePlacement(formData, formData.id).then((res) => {
          if (res.code == 200) {
            proxy.$modal.msgSuccess("修改成功");
            handleClose();
            emit("getList");
          } else {
            proxy.$modal.msgError(res.msg);
          }
        });
      }
    }
  });
};
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="title"
    style="width: 40%"
    @close="handleClose"
  >
    <el-form
      ref="addFormRef"
      :model="addForm"
      label-width="auto"
      inline
      :rules="rules"
      :disabled="title == '查看村社转移安置信息'"
      style="display: flex; flex-wrap: wrap"
    >
      <el-form-item label="村社" prop="villageCode">
        <el-select
          v-model="addForm.villageCode"
          placeholder="请选择"
          :disabled="title == '编辑村社转移安置信息'"
          clearable
        >
          <el-option
            :label="item.adnm"
            :value="item.adcd"
            v-for="(item, index) in villageData"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="安置点" prop="placementId">
        <el-select v-model="addForm.placementId" placeholder="请选择" clearable>
          <el-option
            :label="item.name"
            :value="item.id"
            v-for="(item, index) in residentData"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="转移负责人" prop="memberIdList">
        <el-select v-model="addForm.memberIdList" placeholder="请选择" multiple clearable>
          <el-option
            :label="item.name"
            :value="item.id"
            v-for="(item, index) in memberData"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="转移路线" style="width: 100%"></el-form-item>
      <el-form-item
        prop="geoType"
        style="width: 100%"
        v-if="title != '查看村社转移安置信息'"
      >
        &nbsp; &nbsp; 空间数据 &nbsp;
        <el-radio-group v-model="addForm.geoType">
          <el-radio label="zip">矢量数据(shp-zip,kml,geojson文件)</el-radio>
          <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
          <el-radio label="polyline">人工绘制</el-radio>
        </el-radio-group>
      </el-form-item>

      <div
        style="width: 100%; height: 130px"
        v-show="addForm.geoType === 'zip'"
      >
        <el-upload
          class="upload-demo"
          style="width: 100%"
          ref="uploadRef"
          drag
          name="multipartFile"
          :on-change="handleChange"
          :action="uploadUrl"
          :data="{}"
          :headers="{ Authorization: token }"
          :limit="2"
          :on-success="handleSuccess"
        >
          <div class="el-upload__text">拖拽上传 或 <em>点击上传</em></div>
        </el-upload>
      </div>

      <div style="width: 100%; height: 350px; background: #555">
        <min-map
          @updateBound="updateBound"
          :geom="addForm.geom"
          :geoType="addForm.geoType"
          :extraData="extraData"
          :show-tool="addForm.geoType === 'polyline'"
          style="width: 100%; height: 100%"
        />
      </div>
    </el-form>
    <template #footer v-if="!title.includes('查看村社转移安置信息')">
      <span class="dialog-footer">
        <el-button
        type="primary"
        @click="submitForm"
        >保 存
      </el-button>
      <el-button @click="handleClose">取 消</el-button>
    </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
:deep(.addForm .el-form--inline .el-form-item) {
  margin-right: 0;
  width: 50%;
}
</style>
