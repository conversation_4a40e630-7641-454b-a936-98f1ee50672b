<template>
  <div class="app-container">
      <el-form
        ref="queryFormRef"
        :model="queryForm"
        inline
        class="form-container"
      >
        <el-form-item label="发布人" prop="userName">
          <el-input
            v-model="queryForm.userName"
            placeholder="请输入发布人"
          />
        </el-form-item>

        <el-form-item label="发布时间" prop="timeRange">
          <el-date-picker
            v-model="queryForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            :default-time="defaultTime"
          />
        </el-form-item>

        <el-form-item label="发布对象类型" prop="memberType">
          <el-select
            v-model="queryForm.memberType"
            placeholder="请选择"
          >
            <el-option label="全部" value="" />
            <el-option label="按居民户" :value="2" />
            <el-option label="按行政区" :value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label="发布状态" prop="status">
          <el-select
            v-model="queryForm.status"
            placeholder="请选择"
          >
            <el-option label="全部" value="" />
            <el-option label="发送中" :value="2" />
            <el-option label="发送成功" :value="1" />
            <el-option label="发送失败" :value="3" />
          </el-select>
        </el-form-item>

        <el-form-item class="form-item-button">
          <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
          <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
        </el-form-item>

      </el-form>
    <div class="content">
      <div class="right-table">
        <div class="table-header">
          <el-button type="success" icon="CirclePlus" @click="handleAdd"
            >新增</el-button
          >
        </div>
        <el-table
          :data="tableData"
          stripe
          v-loading="loading"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="createByName" label="发布人" />
          <el-table-column prop="createTime" label="发布时间" />
          <el-table-column prop="status" label="发布状态">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="memberNameList" label="发送对象" />
          <el-table-column
            prop="content"
            label="预警内容"
            show-overflow-tooltip
          />
          <el-table-column label="操作" width="150" align="center">
            <template #default="scope">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)"
                >查看</el-button>
              <!-- <el-link type="primary" @click="handleResend(scope.row)"
                >再次发送</el-link
              > -->
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <!-- 新增外部预警弹窗 -->
    <el-dialog v-model="warningDialogShow" title="新增外部预警" width="80%" class="warning-dialog" :close-on-click-modal="false">
      <el-tabs v-model="contentType" class="demo-tabs" type="border-card">
        <el-tab-pane label="按居民户" name="household">
          <div class="station-form-container">
            <div class="station-title-bar">
              <h3 class="station-subtitle">预警基本信息</h3>
            </div>
            <div class="warning-content">
              <!-- 左侧预警内容 -->
              <div class="warning-left">
                <el-form
                  ref="warningFormRef"
                  :model="warningForm"
                  :rules="warningRules"
                  label-width="100px"
                >
                  <el-form-item label="预警内容" prop="content">
                    <el-input
                      v-model="warningForm.content"
                      type="textarea"
                      :rows="5"
                      placeholder="请输入预警内容（不超过500字）"
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>
                  <!-- <el-form-item label="关联预警" prop="relatedWarning">
                <el-input v-model="warningForm.relatedWarning" placeholder="请输入关联预警信息" style="width: 100%;">
                                  <template #append>
                                      <el-button @click="handleSelectWarning">选择</el-button>
                                  </template>
                              </el-input>
                {{ warningForm.relatedWarning }}
              </el-form-item> -->
                  <el-form-item
                    label="上传预警响应文件"
                    prop="files"
                    label-width="150px"
                  >
                    <el-upload
                      :action="uploadParams.action"
                      :on-success="handleUploadSuccess"
                      :on-error="handleUploadError"
                      :before-upload="handleBeforeUpload"
                      :data="uploadParams.data"
                      :headers="uploadParams.headers"
                      :before-remove="handleBeforeRemove"
                      ref="fileUpload"
                      :limit="1"
                    >
                      <el-button type="primary">上传文件</el-button>
                      <template #tip>
                        <div class="el-upload__tip">
                          请上传doc/pdf/docx文件类型,文件大小不超过20M
                        </div>
                      </template>
                    </el-upload>
                  </el-form-item>
                </el-form>
              </div>

              <!-- 右侧发送成员 -->
              <div class="warning-right">
                <div class="station-title-bar">
                  <h3 class="station-subtitle">发送居民列表</h3>
                </div>
                <el-table
                  :data="selectedMembers"
                  style="width: 100%; margin-top: 10px"
                  border
                  stripe
                  highlight-current-row
                >
                  <el-table-column type="index" label="序号" width="60" />
                  <el-table-column prop="name" label="姓名" width="100" />
                  <el-table-column prop="phone" label="手机号码" width="120" />
                  <el-table-column prop="householdSize" label="家庭人口（人）" width="120" />
                  <el-table-column prop="focusTypes" label="重点关注类型">
                    <template #default="scope">
                      {{ focusTypes(scope.row.focusTypes) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80" fixed="right">
                    <template #default="scope">
                      <el-button type="text" @click="removeMember(scope.row)"
                        >移除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>

          <!-- 底部内容 -->
          <div class="station-form-container">
            <div class="station-title-bar">
              <h3 class="station-subtitle">选择发送成员</h3>
            </div>
            <div class="bottom-content-wrapper">
              <!-- 左侧树 -->
              <div class="org-tree-container">
                <div class="org-tree-header">行政区域</div>
                <div class="org-tree-search">
                  <el-input
                    v-model="filterText"
                    placeholder="请输入行政区名称"
                    clearable
                    prefix-icon="Search"
                  />
                </div>
                <div class="org-tree-body">
                  <el-tree-v2
                    :data="adcdList"
                    :props="propsTree"
                    ref="treeRef"
                    @node-click="handleNodeClick"
                    highlight-current
                    :filter-method="filterNode"
                    :height="400"
                  />
                </div>
              </div>

              <!-- 右侧居民列表 -->
              <div class="member-list-container">
                <div class="member-search-form">
                  <el-form
                    ref="memberFormRef"
                    :model="memberForm"
                    :rules="memberRules"
                    inline
                  >
                    <el-form-item label="成员姓名" prop="name">
                      <el-input
                        v-model="memberForm.name"
                        placeholder="请输入成员姓名"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button @click="resetMemberForm" icon="Refresh">重置</el-button>
                      <el-button type="primary" @click="getMemberList(memberForm.name)" icon="Search">查询</el-button>
                      <el-button type="primary" @click="openMemberSelect">添加到发送成员列表</el-button>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="member-table">
                  <el-table
                    ref="allMembersTable"
                    :data="allMembers"
                    style="width: 100%"
                    max-height="380"
                    border
                    stripe
                    highlight-current-row
                  >
                    <el-table-column type="selection" width="55" />
                    <el-table-column type="index" label="序号" width="60" />
                    <el-table-column prop="name" label="户主姓名" width="100" />
                    <el-table-column prop="phone" label="手机号码" width="120" />
                    <el-table-column prop="houseNumber" label="门牌号码" width="120" />
                    <el-table-column prop="householdSize" label="家庭人口（人）" width="120" />
                    <el-table-column prop="nearWater" label="是否临水" width="120">
                      <template #default="scope">
                        {{ scope.row.nearWater == 1 ? "是" : "否" }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="cutSlope" label="是否切坡" width="120">
                      <template #default="scope">
                        {{ scope.row.cutSlope == 1 ? "是" : "否" }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="focusTypes" label="重点关注类型">
                      <template #default="scope">
                        {{ focusTypes(scope.row.focusTypes) }}
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="按行政区" name="district">
          <div class="station-form-container">
            <div class="station-title-bar">
              <h3 class="station-subtitle">预警基本信息</h3>
            </div>
            <div class="warning-content">
              <!-- 左侧预警内容 -->
              <div class="warning-left">
                <el-form
                  ref="warningFormRef"
                  :model="warningForm"
                  :rules="warningRules"
                  label-width="100px"
                >
                  <el-form-item label="预警内容" prop="content">
                    <el-input
                      v-model="warningForm.content"
                      type="textarea"
                      :rows="5"
                      placeholder="请输入预警内容（不超过500字）"
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>
                  <el-form-item
                    label="上传预警响应文件"
                    prop="files"
                    label-width="150px"
                  >
                    <el-upload
                      :action="uploadParams.action"
                      :on-success="handleUploadSuccess"
                      :on-error="handleUploadError"
                      :before-upload="handleBeforeUpload"
                      :data="uploadParams.data"
                      :headers="uploadParams.headers"
                      :before-remove="handleBeforeRemove"
                      ref="fileUpload"
                      :limit="1"
                    >
                      <el-button type="primary">上传文件</el-button>
                      <template #tip>
                        <div class="el-upload__tip">
                          请上传doc/pdf/docx文件类型,文件大小不超过20M
                        </div>
                      </template>
                    </el-upload>
                  </el-form-item>
                  <el-form-item label="发送区域" prop="area">
                    <el-input
                      v-model="warningForm.area"
                      placeholder="请输入发送区域"
                      :rows="5"
                      type="textarea"
                    />
                  </el-form-item>
                </el-form>
              </div>

              <!-- 右侧选择区域 -->
              <div class="warning-right">
                <div class="station-title-bar">
                  <h3 class="station-subtitle">选择预警区域</h3>
                </div>
                <div class="right-tree">
                  <div class="org-tree-search">
                    <el-input
                      v-model="rightTreeFilterText"
                      placeholder="请输入行政区名称"
                      clearable
                      prefix-icon="Search"
                    />
                  </div>
                  <div class="org-tree-body">
                    <el-tree-v2
                      ref="right-tree-ref"
                      :data="adcdList"
                      :props="propsTree"
                      :filter-method="rightTreeFilterNode"
                      show-checkbox
                      @check="handleRightTreeCheck"
                      :height="400"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitWarning">保 存</el-button>
          <el-button @click="warningDialogShow = false" plain>取 消</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 查看详情弹窗 -->
    <el-dialog v-model="viewDialogShow" title="查看外部预警" width="60%" class="warning-dialog">
      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">预警基本信息</h3>
        </div>
        <div class="detail-form">
          <el-form label-width="120px">
            <el-form-item label="预警内容">
              <el-input
                v-model="viewForm.content"
                type="textarea"
                :rows="5"
                readonly
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 发送对象列表 -->
      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">发送对象列表</h3>
        </div>
        <div class="detail-table-container">
          <!-- 按居民户展示 -->
          <template v-if="viewForm.type === 'household'">
            <el-table :data="viewForm.warnExternalMemberDTOS" border stripe highlight-current-row>
              <el-table-column type="index" label="序号" width="60" />
              <el-table-column prop="residentName" label="户主姓名" />
              <el-table-column prop="phone" label="手机号码" />
              <el-table-column prop="houseNumber" label="门牌号码" />
              <el-table-column prop="householdSize" label="家庭人口(人)" />
              <el-table-column prop="focusTypes" label="重点关注类型" />
              <el-table-column prop="sendStatus" label="发送状态">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.sendStatus)" v-if="scope.row.sendStatus != null">
                    {{ getStatusText(scope.row.sendStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="sendTime" label="发送时间" />
              <!-- <el-table-column prop="failReason" label="失败原因" /> -->
            </el-table>
          </template>

          <!-- 按行政区展示 -->
          <template v-else>
            <el-table :data="viewForm.warnExternalMemberDTOS" border stripe highlight-current-row>
              <el-table-column type="index" label="序号" width="60" />
              <el-table-column prop="adnm" label="行政区" />
              <el-table-column prop="sendStatus" label="发送状态">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.sendStatus)">
                    {{ getStatusText(scope.row.sendStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="sendTime" label="发送时间" />
              <!-- <el-table-column prop="failReason" label="失败原因" /> -->
            </el-table>
          </template>
        </div>
      </div>

      <!-- 预警响应文件 -->
      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">预警响应文件</h3>
        </div>
        <div class="file-container">
          <template v-if="viewForm.fileUrl">
            <el-link
              type="primary"
              icon="Document"
              @click="handleDownloadFile(viewForm.fileUrl)"
            >
              {{ viewForm.fileUrl.substring(37) }}
            </el-link>
          </template>
          <div v-else class="no-file">暂无文件</div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewDialogShow = false" plain>关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs, watch } from "vue";
import { getTreeOption } from "@/api/watershed/ads";
import {
  queryWarningOuter,
  addWarningOuter,
  queryWarningOuterDetail,
  residentList, //居民区列表
  downloadByMain, //下载文件
} from "@/api/warning";

defineOptions({
  name: 'ExternalWarning'
})

const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload");
import { getToken } from "@/utils/auth";
const { proxy } = getCurrentInstance();
const loading = ref(false);
const data = reactive({
  total: 0,
  queryForm: {
    userName: "",
    timeRange: [
      new Date(new Date().getTime() - 72 * 60 * 60 * 1000),
      new Date(),
    ],
    startTime: "",
    endTime: "",
    memberType: "",
    status: "",
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
    // name: [{ required: true, message: "请输入防汛组织名称", trigger: "blur" }],
  },
  dialogShow: false,
  title: "新增防汛组织信息",
  addForm: {
    name: "",
    parentId: "",
    dutyPhone: "",
    phone: "",
    responsibility: "",
    sort: "",
    status: 0,
  },
  propsTree: {
    value: "adcd",
    label: "adnm",
    children: "children",
  },
  tableData: [],
  adcdList: [],
  townList: [],
  filterText: "",
  warningDialogShow: false,
  memberFilterText: "", //防汛组织成员筛选
  memberSelectShow: false, //防汛组织成员选择弹窗
  allMembers: [], //防汛组织成员列表
  contentType: "household",
  areaFilterText: "",
  areaSearchText: "",
  areaTreeData: [],
  selectedAreas: [],
  viewDialogShow: false,
  viewForm: {
    content: "",
    relatedWarning: "",
    type: "household", // household 或 district
    files: [],
    residents: [], // 居民列表
    districts: [], // 行政区列表
  },
  rightTreeFilterText: ref(""),
  selectedAdcds: "", // 存储选中节点的 adcd 值
  uploadParams: {
    action: uploadFileUrl,
    data: {
      bucketName: "watershed",
    },
    headers: {
      Authorization: "Bearer " + getToken(),
    },
  },
});
const {
  queryForm,
  propsTree,
  adcdList,
  total,
  tableData,
  filterText,
  warningDialogShow,
  memberFilterText,
  allMembers,
  contentType,
  areaFilterText,
  areaSearchText,
  areaTreeData,
  selectedAreas,
  viewForm,
  viewDialogShow,
  rightTreeFilterText,
  selectedAdcds,
  uploadParams,
} = toRefs(data);
// 新增数据定义
const warningForm = reactive({
  content: "",
  relatedWarning: "",
  files: [],
  area: "",
});
const filterNode = (query, node) => {
  return node.adnm.includes(query);
};
onMounted(() => {
  getList();
  getTreeList();
});
// watch(filterText, (val) => {
//   proxy.$refs.treeRef.filter(val);
// });
watch(filterText, (val) => {
  if(val) {
    proxy.$refs.treeRef.filter(val)
  } else {
    proxy.$refs.treeRef.filter('')
    // Collapse all expanded nodes when filter is cleared
    proxy.$refs.treeRef?.setExpandedKeys([])
  }
})

const getTreeList = () => {
  getTreeOption(5).then((res) => {
    data.adcdList = formatTree(res.data);
  });
};

const formatTree = (data) => {
  let dataList = data.map((item) => {
    return {
      ...item.data,
      children: item.children && formatTree(item.children),
    };
  });
  //dataList中有children为空的节点 删除
  let dataList2 = dataList.map((item) => {
    if (item.children.length === 0) {
      delete item.children;
    }
    return {
      ...item,
    };
  });
  return dataList2;
};

const getList = () => {
  loading.value = true;
  queryWarningOuter({
    ...data.queryForm,
    startTime: data.queryForm.timeRange[0],
    endTime: data.queryForm.timeRange[1],
  }).then((res) => {
    data.tableData =
      res.data.records.map((res) => {
        return {
          ...res,
          memberNameList: res.memberNameList.join(","),
        };
      }) || [];
    data.total = res.data.total || 0;
    loading.value = false;
  });
};
const resetQuery = () => {
  data.queryForm = {
    userName: "",
    timeRange: [
      new Date(new Date().getTime() - 72 * 60 * 60 * 1000),
      new Date(),
    ],
    memberType: "",
    status: "",
    pageNum: 1,
    pageSize: 10,
  };
  getList();
};
const addFamily = () => {
  data.title = "新增防汛组织信息";
  data.dialogShow = true;
};

function handleNodeClick(el) {
  data.queryForm.townAdcd = el.adcd;
  getList();
  //   handleQuery();
}

const defaultTime = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 1, 1, 23, 59, 59),
];

const getStatusType = (status) => {
  const map = {
    2: "warning",
    1: "success",
    3: "danger",
  };
  return map[status] || "info";
};

const getStatusText = (status) => {
  const map = {
    2: "发送中",
    1: "发送成功",
    3: "发送失败",
  };
  return map[status] || status;
};

const handleQuery = () => {
  getList();
};

const handleView = async (row) => {
  try {
    queryWarningOuterDetail(row.id).then((res) => {
      // console.log(res);
      data.viewForm = {
        ...res.data,
        type:
          res.data.warnExternalMemberDTOS[0]?.memberType == 1
            ? "district"
            : "household",
        // warnExternalMemberDTOS: [{...res.data.warnExternalMemberDTOS[0],
        //   status:res.data.status,
        //   createTime:res.data.createTime,
        // }],
      };
      // console.log(data.viewForm);
    });
    viewDialogShow.value = true;
  } catch (error) {
    console.error("获取预警详情失败:", error);
    proxy.$modal.msgError("获取预警详情失败");
  }
};

const handleResend = (row) => {
  // TODO: 实现再次发送逻辑
  // console.log("再次发送", row);
};

const warningRules = {
  content: [
    { required: true, message: "请输入预警内容", trigger: "blur" },
    { max: 500, message: "预警内容不能超过500字", trigger: "blur" },
  ],
};

const memberForm = reactive({
  name: "",
  phone: "",
  position: "",
  department: "",
  duty: "",
  responsibility: "",
  organization: "",
});

const memberRules = {
  name: [
    // { required: true, message: '请输入成员姓名', trigger: 'blur' }
  ],
};

// 选中的成员列表
const selectedMembers = ref([]);

// 文件上传相关方法
const handleBeforeUpload = (file) => {
  const isValidType = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ].includes(file.type);
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isValidType) {
    proxy.$modal.msgError("只能上传PDF或Word文档！");
    return false;
  }
  if (!isLt20M) {
    proxy.$modal.msgError("文件大小不能超过20MB！");
    return false;
  }
  return true;
};

const handleUploadSuccess = (response, file) => {
  warningForm.files.push({
    name: response.msg,
    url: "",
  });
  proxy.$modal.msgSuccess("文件上传成功");
};

const handleUploadError = () => {
  proxy.$modal.msgError("文件上传失败");
};
// 移除文件之前的钩子
const handleBeforeRemove = (file, fileList) => {
  // console.log("移除文件：", file, fileList);
  warningForm.files.filter((item) => item.name !== file.response.msg); // TODO: 处理移除文件的操作
  proxy.$modal.msgSuccess("文件移除成功");
};
// 成员管理相关方法
const openMemberSelect = async () => {
  // 获取表格选中的行
  const selection = proxy.$refs["allMembersTable"].getSelectionRows();

  // 添加到已选成员列表，去重
  const newMembers = selection.filter(
    (item) => !selectedMembers.value.some((selected) => selected.id === item.id)
  );
  selectedMembers.value = [...selectedMembers.value, ...newMembers];

  // 重新获取成员列表并过滤掉已选成员
  await getMemberList(memberForm.name);

  allMembers.value = allMembers.value.filter(
    (member) =>
      !selectedMembers.value.some((selected) => selected.id === member.id)
  );

  // 清空表格选择
  proxy.$refs["allMembersTable"].clearSelection();
};

const getMemberList = (name) => {
  // 修改为返回 Promise
  return residentList({
    name,
    pageNum: 1,
    pageSize: 9999,
  }).then((response) => {
    allMembers.value = response.data.records;
  });
};

const removeMember = (row) => {
  selectedMembers.value = selectedMembers.value.filter(
    (item) => item.id !== row.id
  );
  getMemberList(memberForm.name);
};

// const confirmMemberSelect = () => {
//   proxy.$refs.memberFormRef.validate((valid) => {
//     if (valid) {
//       // 获取表格选中的行
//       const selection = proxy.$refs.memberTable.getSelectionRows();
//       // 添加到已选成员列表，去重
//       const newMembers = selection.filter(item =>
//         !selectedMembers.value.some(selected => selected.id === item.id)
//       );
//       selectedMembers.value = [...selectedMembers.value, ...newMembers];
//       resetMemberForm();
//     }
//   });
// };

const resetMemberForm = () => {
  memberForm.name = "";
  getMemberList();
};

// 监听成员筛选文本变化
watch(memberFilterText, (val) => {
  if (val) {
    allMembers.value = allMembers.value.filter(
      (item) =>
        item.name.includes(val) ||
        item.phone.includes(val) ||
        item.organization.includes(val)
    );
  } else {
    getMemberList();
  }
});

// 修改handleAdd方法
const handleAdd = () => {
  data.warningDialogShow = true;
  // 重置表单
  warningForm.content = "";
  warningForm.relatedWarning = "";
  warningForm.files = [];
  selectedMembers.value = [];
  // 获取成员列表
  getMemberList();
};

// 修改提交预警函数
const submitWarning = async () => {
  await proxy.$refs.warningFormRef.validate(async (valid) => {
    if (!valid) return;

    try {
      let params = {
        content: warningForm.content,
        fileUrl: warningForm.files[0]?.name || "", // 取第一个文件的URL
        type: contentType.value === "household" ? 2 : 1, // 1-行政区 2-居民户
        adcd: "",
      };

      // 根据不同类型添加不同参数
      if (contentType.value === "household") {
        // 按居民户发送
        if (selectedMembers.value.length === 0) {
          proxy.$modal.msgError("请选择发送成员");
          return;
        }
        params.residentId = selectedMembers.value.map((member) => member.id);
        params.warnExternalMemberDTOS = [
          {
            memberType: params.type,
            // adcd: params.adcd,
            residentId: params.residentId.join(","),
          },
        ];
        delete params.residentId;
      } else {
        // 按行政区发送
        // console.log(selectedAdcds.value);
        if (selectedAdcds.value.length === 0) {
          proxy.$modal.msgError("请选择发送区域");
          return;
        }
        params.warnExternalMemberDTOS = [];
        // params.adcd = selectedAdcds.value;
        selectedAdcds.value.forEach((adcd) => {
          params.warnExternalMemberDTOS.push({
            memberType: params.type,
            adcd,
          });
        });
      }
      const res = await addWarningOuter(params);
      if (res.code === 200) {
        proxy.$modal.msgSuccess("发布成功");
        data.warningDialogShow = false;
        getList();
      } else {
        proxy.$modal.msgError(res.msg || "发布失败");
      }
    } catch (error) {
      // console.error("发布预警失败:", error);
      proxy.$modal.msgError("发布预警失败");
    }
  });
};

const handleAddType = (type) => {
  contentType.value = type;
  data.selectedAdcds = []; // 清空选中的 adcd
  // warningForm.area = ''; // 清空区域文本
};

// 新增行政区相关的响应式数据
const areaTreeProps = {
  label: "adnm",
  children: "children",
};

// 监听区域搜索文本变化
watch(areaSearchText, (val) => {
  proxy.$refs.areaTreeRef?.filter(val);
});

// 区域树过滤方法
const filterAreaNode = (value, data) => {
  if (!value) return true;
  return data.adnm.includes(value);
};

// 处理区域选中
const handleAreaCheck = (data, checked) => {
  const { checkedNodes } = checked;
  selectedAreas.value = checkedNodes.map((node) => ({
    adcd: node.adcd,
    adnm: node.adnm,
  }));
};

// 移除选中区域
const removeArea = (row) => {
  proxy.$refs.areaTreeRef.setChecked(row.adcd, false);
  selectedAreas.value = selectedAreas.value.filter(
    (item) => item.adcd !== row.adcd
  );
};

// 清空选中区域
const clearSelectedAreas = () => {
  proxy.$refs.areaTreeRef.setCheckedKeys([]);
  selectedAreas.value = [];
};

// 选择关联预警
const handleSelectWarning = () => {
  // TODO: 实现关联预警选择逻辑
};

// 文件下载方法
const handleDownloadFile = async (file) => {
  try {
    // TODO: 调用文件下载API
    // 使用下载文件的函数
    downloadByMain({ fileName: file, bucketName: "watershed" });
  } catch (error) {
    // console.error("文件下载失败:", error);
    proxy.$modal.msgError("文件下载失败");
  }
};
const focusTypes = (types) => {
  switch (types) {
    case 1:
      return "五保户";
    case 2:
      return "优抚对象";
    case 3:
      return "常年困难户";
  }
};

// 添加过滤方法
const rightTreeFilterNode = (value, data) => {
  if (!value) return true;
  return data.adnm.includes(value);
};

// 监听搜索框值变化
watch(rightTreeFilterText, (val) => {
  proxy.$refs["right-tree-ref"].filter(val);
});

// 处理节点选中事件
const handleRightTreeCheck = (data, { checkedNodes }) => {
  // 将选中节点的 label 组合成字符串
  warningForm.area = checkedNodes.map((node) => node.adnm).join(",");
  // 存储选中节点的 adcd
  selectedAdcds.value = checkedNodes.map((node) => node.adcd);
  // console.log(selectedAdcds.value);
};

// 监听表单区域值变化，同步更新树的选中状态
watch(
  () => warningForm.area,
  (newVal) => {
    if (!newVal) {
      // 如果表单值为空，清空树的选中状态
      proxy.$refs["right-tree-ref"].setCheckedKeys([]);
      return;
    }

    // 获取当前表单中的区域名称数组
    const areaNames = newVal.split(",");

    // 找到对应的节点并设置选中
    const findNodes = (nodes) => {
      nodes.forEach((node) => {
        if (areaNames.includes(node.adnm)) {
          proxy.$refs["right-tree-ref"].setChecked(node.adcd, true, false);
        }
        if (node.children) {
          findNodes(node.children);
        }
      });
    };

    findNodes(adcdList.value);
  }
);

// 在关闭弹窗时清空数据
// watch(warningDialogShow, (val) => {
//   if (!val) {
//     data.selectedAdcds = []; // 清空选中的 adcd
//     warningForm.area = ''; // 清空区域文本
//   }
// });
</script>
<style scoped lang="scss">

.warning-content {
  display: flex;
  gap: 20px;
  padding: 0 16px;

  .warning-left {
    width: 48%;

    :deep(.el-form-item__content) {
      width: calc(100% - 100px);
    }
  }

  .warning-right {
    width: 48%;
    display: flex;
    flex-direction: column;

    .member-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .member-actions {
        display: flex;
        align-items: center;
      }
    }

    .el-table {
      flex: 1;
      overflow: auto;
    }
  }
}

/* 弹窗样式美化 */
:deep(.warning-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    margin-right: 0;
  }

  .el-dialog__body {
    padding: 20px 24px;
    max-height: 75vh;
    overflow-y: auto;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 14px 20px;
  }
}

.station-form-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.station-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.station-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  padding: 16px 0 8px;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: #409eff;
  }
}

.detail-form {
  padding: 0 16px;
}

.detail-table-container {
  padding: 0 16px;
}

.file-container {
  padding: 16px;
  min-height: 60px;
  display: flex;
  align-items: center;

  .no-file {
    color: #909399;
    font-style: italic;
  }
}

/* 底部内容新布局样式 */
.bottom-content-wrapper {
  display: flex;
  gap: 20px;
  height: 500px;
  padding: 0 16px;

  .org-tree-container {
    width: 32%;
    border: 1px solid #dcdfe6;
    padding: 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    .org-tree-header {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
      color: #303133;
    }

    .org-tree-search {
      margin-bottom: 16px;
    }

    .org-tree-body {
      flex: 1;
      overflow: hidden; /* 防止这里出现滚动条 */

      :deep(.el-tree) {
        background-color: transparent;
      }
    }
  }

  .member-list-container {
    width: 68%;
    display: flex;
    flex-direction: column;

    .member-search-form {
      margin-bottom: 16px;
      padding: 16px 16px 0 16px;
      background-color: #f9fafc;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
    }

    .member-table {
      flex: 1;
      overflow: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #dcdfe6;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f5f7fa;
      }
    }
  }
}

.member-table {
  margin-top: 10px;

  :deep(.el-table) {
    max-height: 300px;
    overflow-y: auto;
  }
}

.upload-list {
  margin-top: 10px;
}

.warning-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .title {
    font-size: 16px;
    font-weight: bold;
  }
}

.area-content {
  border: 1px solid #dcdfe6;
  padding: 10px;
  height: 300px;
  overflow: auto;
}

.selected-areas {
  margin-top: 20px;

  .area-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    span {
      font-weight: bold;
    }
  }
}

.view-content {
  padding: 20px;

  .warning-info {
    margin-bottom: 20px;

    .related-warning {
      .label {
        font-weight: normal;
        color: #606266;
      }
    }
  }

  .target-list {
    margin-bottom: 20px;

    .table-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
    }
  }

  .warning-files {
    .files-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .files-list {
      padding: 10px;
      //   border: 1px solid #dcdfe6;
      border-radius: 4px;

      .file-item {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .no-files {
        color: #909399;
        text-align: center;
        padding: 20px 0;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 修改右侧树样式，避免双滚动条 */
.right-tree {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .org-tree-search {
    margin-bottom: 16px;
  }

  .org-tree-body {
    flex: 1;
    overflow: hidden;
  }
}
</style>