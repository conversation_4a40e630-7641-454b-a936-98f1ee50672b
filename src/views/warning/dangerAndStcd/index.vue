<template>
  <div class="app-container">
    <!-- 添加或修改流域对话框 -->
    <el-row>
      <el-col :span="24"></el-col>
      <el-form ref="queryFormRef" :model="queryForm" label-width="100" inline style="display: flex;flex-wrap: wrap;">
        <el-form-item label="危险区名称" prop="name">
          <el-input v-model="queryForm.name" placeholder="请输入" style="width: 180px;" clearable></el-input>
        </el-form-item>
        <el-form-item label="所属流域" prop="basinId">
          <el-tree-select v-model="queryForm.basinId" :data="transformDataForTreeSelect(waterAreaList)" check-strictly
            clearable :render-after-expand="false" placeholder="请选择流域" style="width: 180px;" />
        </el-form-item>
        <el-form-item label="危险区等级" prop="grade">
          <el-select v-model="queryForm.grade" placeholder="请选择" style="width: 120px;">
            <el-option label="极高风险" :value="1" />
            <el-option label="高风险" :value="2" />
            <el-option label="低风险" :value="3" />
          </el-select>
        </el-form-item>

        <div class="bth">
          <el-button type="primary" @click="getList">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </div>
      </el-form>

    </el-row>
    <div class="content">
      <AdcdTree v-model="queryForm.paramAdcd" @getList="getList" />
      <div class="right-table">
        <!-- <el-button type="primary" icon="Plus" @click="addData()">新增</el-button> -->
        <el-table :data="tableData" :style="{ height: '100%', }" v-loading="loading">
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="name" label="危险区名称" />
          <el-table-column prop="name" label="危险区代码" />
          <el-table-column prop="address" label="危险区等级">
            <template #default="scope">
              <el-tag :type="scope.row.grade === 1 ? 'danger' : scope.row.grade === 2 ? 'warning' : ''">{{
                scope.row.grade === 1 ? '极高风险' : scope.row.grade === 2 ? '高风险' : '低风险'
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="rainStationNameList" label="雨情预警测站" />
          <el-table-column prop="riverStationNameList" label="河道水情预测站" align="center" />
          <el-table-column prop="reservoirStationNameList" label="水库水情预警测站" />
          <el-table-column prop="address" label="操作" width="150">
            <template #default="scope">
              <el-link type="primary" size="mini" @click="handleSee(scope.row)" style="margin-right: 5px;">查看</el-link>
              <el-link type="warning" size="mini" @click="handleEdit(scope.row)" style="margin-left: 5px;">编辑</el-link>
              <!-- <el-button type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
          @pagination="getList" />
      </div>
    </div>
    <el-dialog v-model="dialogShow" :title="title" style="width: 40%;">
      <el-row class="title-box" >
        <div style="display: flex;">
          <div style="font-weight: bold;">危险区名称：</div>
          <span>{{ stationData?.name }}</span>
        </div>
        <div style="display: flex;">
          <div style="font-weight: bold">危险区等级：</div>

          <el-tag :type="stationData.grade === 1 ? 'danger' : stationData.grade === 2 ? 'warning' : ''">{{
            stationData.grade === 1 ? '极高风险' : stationData.grade === 2 ? '高风险' : '低风险'
          }}</el-tag>

        </div>
      </el-row>
      <el-button type="primary" plain icon="Plus" style="margin-bottom: 20px;" @click="addRow" v-if="title == '新增危险区预警测站'">新增</el-button>
      <el-table :data="addTable" :style="{ width: '100%', maxHeight: '300px' }" >
        <el-table-column label="序号" type="index" width="80" />
        <el-table-column prop="stnm" label="测站">
          <template #default="scope">
            <el-select v-model="scope.row.stcd" placeholder="请选择" :disabled="title != '新增危险区预警测站'">
              <el-option v-for="item in stationList" :key="item.stcd" :label="item.stnm" :value="item.stcd" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="warnType" label="预警类型">
          <template #default="scope">
            <el-select v-model="scope.row.warnType" placeholder="请选择" :disabled="title != '新增危险区预警测站'">
              <el-option label="雨情" :value="1" />
              <el-option label="河道水情" :value="2" />
              <el-option label="水库水情" :value="3" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="最近数据采集时间" />
        <el-table-column prop="address" label="操作" align="center">
          <template #default="scope">
            <el-link type="primary" size="mini" @click="testOne(scope.row, scope.$index)"
              style="margin-right: 10px;">数据测试</el-link>
            <el-link type="danger" size="mini" @click="deleteOne(scope.row)" :disabled="title != '新增危险区预警测站'" style="margin-left: 10px;">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogShow = false">取消</el-button>
          <el-button type="primary" @click="submitAdd" v-if="title != '查看危险区预警测站'">保存 </el-button>

        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs, watch } from "vue";
import { getToken } from '@/utils/auth'
import { getTreeOption, selectStlyList, selectStaList } from "@/api/watershed/ads";
import { updateDangerousArea, deleteDangerousArea, queryFloodPreventionDangerAreaStations, testStcdRecord, dangerStationsSave, dangerStationsById } from '@/api/warning'
import AdcdTree from "@/components/AdcdTree/index.vue";

defineOptions({
  name: 'DangerAndStcd'
})

const token = getToken()
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
const { proxy } = getCurrentInstance();
const uploadRef = ref('')
const loading = ref(false)
const data = reactive({
  queryForm: {
    name: "",
    basinId: "",
    grade: "",
    pageNum: 1,
    pageSize: 20,
    geoType: 'people',
    paramAdcd: "",
    geom: ""
  },
  total: 0,
  rules: {
    name: [
      { required: true, message: '请输入危险区名称', trigger: 'blur' },
    ],
    code: [
      { required: true, message: '请输入危险区代码', trigger: 'blur' },
    ],
    basinId: [
      { required: true, message: '请选择流域', trigger: 'blur' },
    ],
    grade: [
      { required: true, message: '请选择等级', trigger: 'blur' },
    ],

    townAdcd: [
      { required: true, message: '请选择乡镇', trigger: 'blur' },
    ],
  },
  dialogShow: false,
  title: '新增危险区预警测站',
  addTable: [],
  tableData: [],
  waterAreaList: [],
  stationList: [],
  detailData: [],
  stationData: {},
});
const { queryForm, tableData, dialogShow, addForm, title, rules, addTable, waterAreaList, stationList, stationData, total } = toRefs(data)
onMounted(() => {
  getList()
  getAllWater()
  getAllStation()
})
const testOne = (row, index) => {
  if (!row.stcd || !row.warnType) {
    proxy.$modal.msgError('测站或预警类型不可为空！')
    return false
  }
  testStcdRecord(row.stcd, row.warnType).then((res) => {
    data.addTable[index].date = res.msg || '无数据'
  })
}
const getAllStation = async () => {
  let res = await selectStaList({ pageNum: 1, pageSize: 999, stationTpyeList: ['ZQ', 'ZZ', 'RR', 'PP'] })
  data.stationList = res.data.records || []
}
const getAllWater = async () => {
  let res = await selectStlyList({ pageNum: 1, pageSize: 999 })
  data.waterAreaList = res.data || []
}

const deleteOne = (row) => {
  if (data.addTable.length > 0) {
    data.addTable.splice(data.addTable.indexOf(row), 1)
  }
}
const addRow = () => {
  let status = true
  data.addTable.forEach((item) => {
    if (!item.stcd || !item.warnType ) {

      status = false
    }
  })
  if (status) {
    data.addTable.push({
      stcd: '',
      warnType: '',
      date: '',
    })
  } else {
    proxy.$modal.msgWarning('请完善数据！确保数据完整性')
  }

}

const getList = () => {
  loading.value = true
  queryFloodPreventionDangerAreaStations(queryForm.value).then(response => {
    console.log(response)

    data.tableData = response.data.records
    console.log(data.tableData)
    data.total = response.data.total || 0
    loading.value = false
  })
}
const transformDataForTreeSelect = (data) => {
  // 递归地转换数据以匹配 el-tree-select 的需求
  return data.map(item => ({
    label: item.data.name, // 使用 'name' 属性作为标签
    value: item.data.basinId, // 使用 'basinId' 属性作为值
    children: item.children ? transformDataForTreeSelect(item.children) : [], // 递归转换子节点
  }));
}

const resetQuery = () => {
  proxy.$refs.queryFormRef.resetFields()
  getList()
}
const handleSee = async (row) => {
  data.title = '查看危险区预警测站'
  let detail = await dangerStationsById(row.id)
  data.addTable = detail.data || []
  data.stationData = row
  data.dialogShow = true
}
const handleEdit = async (row) => {
  // await getInfo(row.id).then(response => {
  //     data.addForm = response.data
  // })
  data.title = '新增危险区预警测站'
  let detail = await dangerStationsById(row.id)
  data.addTable = detail.data || []
  data.stationData = row
  data.dialogShow = true
}
const submitAdd = () => {
  if (data.addTable.length === 0) {
    proxy.$modal.msgError('请添加危险区关联测站信息！')
    return false
  }
  let submitData = data.addTable.map(item => {
    return { hazardousId: data.stationData.id, ...item }
  })
  dangerStationsSave(submitData).then((res) => {
    if (res.code == 200) {
      proxy.$modal.msgSuccess("修改成功");
      data.dialogShow = false
      getList()
    } else {
      proxy.$modal.msgError(res.msg);
    }

  })
}
const submitEdit = () => {
  proxy.$refs['addFormRef'].validate(valid => {
    if (valid) {
      // 提交
      updateDangerousArea(addForm).then(response => {
        if (response.code == 200) {
          proxy.$modal.msgSuccess("修改成功");
          data.dialogShow = false
          getList()
        } else {
          proxy.$modal.msgError(response.msg);
        }
      })
    }
  })
}
const handleDelete = (row) => {
  proxy.$modal.confirm('是否确认删除').then(() => {
    // 删除
    deleteDangerousArea(row.id).then(response => {
      if (response.code == 200) {
        proxy.$modal.msgSuccess("删除成功");
        getList()
      } else {
        proxy.$modal.msgError(response.msg);
      }
    })
  }).catch(() => {

  })
}
const addData = () => {
  data.title = '新增危险区预警测站'
  data.dialogShow = true

}
const handleChange = (file, fileList) => {
  if (fileList.length > 1) {
    fileList[0] = fileList[1]
    fileList.splice(1, 1);
  }
}
const handleSuccess = (response) => {
  if (response.code == 200) {
    form.value.geom = response.data
  } else {
    uploadRef.value?.clearFiles()
    proxy.$modal.msgError(response.msg);
  }
}
const updateBound = (geojson) => {
  // 提交geojson数据到后台
  console.log(geojson)
  form.value.geom = geojson
}
</script>
<style scoped lang="scss">
:deep(.el-form--inline .el-form-item) {
  margin-right: 0;
}

.bth {
  margin-left: 20px;
}

.content {
  display: flex;
  margin-top: 20px;
}

.right-table {
  flex: 1;
  margin-left: 20px;
  height: calc(100vh - 270px);
}

.title-box {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  font-size: 16px;
  color: #515a6e;
}
</style>