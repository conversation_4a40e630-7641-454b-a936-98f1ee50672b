<script setup>
import { onMounted } from "vue";
import { saveRainWarn, selectRainWarnPage } from "@/api/warning/index";
import { deepClone } from "@/utils";
import AdcdTree from "@/components/AdcdTree/index.vue";

defineOptions({
  name: "RainfallWarning",
});

const { proxy } = getCurrentInstance();

// 响应式数据
const dataList = ref([]);
const open = ref(false);
const loading = ref(true);
const title = ref("");
const total = ref(0);
const form = ref({});
const queryParams = ref({
  adcd: "",
  villageName: "",
  pageNum: 1,
  preventionZonesType: "",
  pageSize: 10,
});
const rules = ref({});

// 需要判断的属性组
const SPAN_PROPS = ["villageCode", "villageName"];
const rowSpansMap = ref(new Map()); // 存需要开始合并的行号，向下合并多少行

/**
 * 根据列表数据得出需要合并的行
 * @param {Array} data - 列表数据
 */
const calculateRowSpans = (data) => {
  if (!data || data.length === 0) return;

  rowSpansMap.value = new Map();
  let currentValues = {};
  let spanStartRow = 0;

  // 初始化第一行数据作为比较基准
  SPAN_PROPS.forEach((prop) => {
    currentValues[prop] = data[0]?.[prop];
  });
  rowSpansMap.value.set(0, 1);

  // 从第二行开始遍历比较
  for (let i = 1; i < data.length; i++) {
    const item = data[i];
    // 检查当前行与基准行是否匹配
    const isSame = SPAN_PROPS.every(
      (prop) => item[prop] === currentValues[prop]
    );

    if (isSame) {
      // 匹配时增加合并行数
      const currentSpan = rowSpansMap.value.get(spanStartRow);
      rowSpansMap.value.set(spanStartRow, currentSpan + 1);
    } else {
      // 不匹配时更新基准数据并设置新的合并起点
      SPAN_PROPS.forEach((prop) => {
        currentValues[prop] = item[prop];
      });
      rowSpansMap.value.set(i, 1);
      spanStartRow = i;
    }
  }
};

/**
 * 表格单元格合并方法
 * @param {Object} params - Element Plus表格span-method回调参数
 * @returns {Object|undefined} 合并配置或undefined
 */
const handleCellSpan = ({ row, column, rowIndex, columnIndex }) => {
  // 只处理第一列
  if (columnIndex === 0) {
    const span = rowSpansMap.value.get(rowIndex);

    if (span) {
      // 这是合并的起始行
      return {
        rowspan: span,
        colspan: 1,
      };
    }
    // 这是被合并的行
    return {
      rowspan: 0,
      colspan: 0,
    };
  }
  // 其他列不处理
};

/**
 * 格式化单条雨情预警数据
 * @param {Object} item - 原始数据项
 * @param {Object} rain - 雨情预警项
 * @returns {Object} - 格式化后的数据
 */
const formatRainWarnItem = (item, rain) => {
  return {
    villageCode: item.villageCode,
    id: rain.id,
    name: item.villageName,
    warnLevel: rain.warnLevel,
    thirtyMinuteValue: rain.thirtyMinuteValue,
    oneHourValue: rain.oneHourValue,
    threeHourValue: rain.threeHourValue,
    sixHourValue: rain.sixHourValue,
    twelveHourValue: rain.twelveHourValue,
    twentyFourHourValue: rain.twentyFourHourValue,
    // rainAccumulatedTime: 3,
    // rainAccumulated: rain.rainAccumulated,
  };
};

/**
 * 处理雨情预警列表
 * @param {Object} item - 村社数据项
 * @returns {Array} - 处理后的雨情预警列表
 */
const processRainWarnList = (item) => {
  let rainWarnList = item.dtos || [];

  // 自动补充缺失的预警等级
  if (rainWarnList.length === 1) {
    const rain = rainWarnList[0];
    rainWarnList.push({
      warnLevel: rain.warnLevel === 1 ? 2 : 1,
      name: item.villageName,
    });
  } else if (rainWarnList.length === 0) {
    rainWarnList = [
      {
        warnLevel: 1,
        name: item.villageName,
      },
      {
        warnLevel: 2,
        name: item.villageName,
      },
    ];
  }

  item.dtos = rainWarnList;
  // 对预警等级进行排序
  item.dtos?.sort((a, b) => a.warnLevel - b.warnLevel);

  return rainWarnList;
};

/**
 * 获取雨情预警列表
 */
const fetchRainfallData = () => {
  loading.value = true;

  selectRainWarnPage(queryParams.value)
    .then((response) => {
      const records = response.data?.records || [];
      const formattedData = [];

      // 处理每条记录
      records.forEach((item) => {
        const rainWarnList = processRainWarnList(item);

        // 格式化并添加到结果数组
        rainWarnList.forEach((rain) => {
          formattedData.push(formatRainWarnItem(item, rain));
        });
      });

      total.value = response.data?.total || 0;
      dataList.value = formattedData;
      calculateRowSpans(dataList.value);
    })
    .catch((error) => {
      proxy.$modal.msgError("获取雨情预警数据失败：" + error.message);
    })
    .finally(() => {
      loading.value = false;
    });
};

/**
 * 取消操作
 */
const handleCancel = () => {
  open.value = false;
  resetForm();
};

/**
 * 重置表单
 */
const resetForm = () => {
  form.value = {
    pcode: "",
    geoType: "people",
    lynm: "",
    icon: "",
  };
  proxy.resetForm("menuRef");
};

/**
 * 查询操作
 */
const handleQuery = () => {
  fetchRainfallData();
};

/**
 * 重置查询条件
 */
const handleResetQuery = () => {
  queryParams.value.adcd = "";
  proxy.resetForm("queryRef");
  handleQuery();
};

/**
 * 编辑操作
 */
const handleEdit = async (row) => {
  form.value = deepClone(row);
  open.value = true;
  title.value = "编辑雨情预警 - " + row.name;
};

/**
 * 提交表单
 */
const handleSubmit = () => {
  proxy.$refs["menuRef"].validate((valid) => {
    if (valid) {
      saveRainWarn(form.value).then((response) => {
        if (response.code === 200) {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          fetchRainfallData();
        }
      });
    }
  });
};

onMounted(() => {
  fetchRainfallData();
});
</script>

<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="村社名称" prop="villageName">
        <el-input
          v-model="queryParams.villageName"
          placeholder="请输入村社名称"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="防治区类型"
        prop="preventionZonesType"
      >
        <el-select
          v-model="queryParams.preventionZonesType"
          placeholder="请选择"
        >
          <el-option label="重点防治区" :value="1" />
          <el-option label="一般防治区" :value="2" />
          <el-option label="非防治区" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="handleQuery"
          >查询</el-button
        >
        <el-button @click="handleResetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="content">
      <AdcdTree
        v-model="queryParams.adcd"
        @getList="handleQuery"
        default-expand-all
      />
      <div class="right-table">
        <el-table
          v-loading="loading"
          :data="dataList"
          :span-method="handleCellSpan"
          stripe
        >
          <el-table-column
            prop="name"
            label="村社"
            align="center"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column prop="warnLevel" label="预警等级" align="center">
            <template #default="scope">
              <template v-if="scope.row.warnLevel == 2">
                <div style="color: #f78c08">准备转移</div>
              </template>
              <template v-else>
                <div style="color: #fc4949">立即转移</div>
              </template>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="rainAccumulated"
            label="前期累计雨量(mm)"
            width="150"
            align="center"
          ></el-table-column> -->
          <el-table-column
            prop="thirtyMinuteValue"
            label="30分钟"
            align="center"
          >
            <template #default="scope">
              <template v-if="scope.row.thirtyMinuteValue">
                <div>{{ scope.row.thirtyMinuteValue }}</div>
              </template>
              <template v-else>
                <div style="color: rgba(255, 85, 85, 0.82)">未设置</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="oneHourValue" label="1小时" align="center">
            <template #default="scope">
              <template v-if="scope.row.oneHourValue">
                <div>{{ scope.row.oneHourValue }}</div>
              </template>
              <template v-else>
                <div style="color: rgba(255, 85, 85, 0.82)">未设置</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="threeHourValue" label="3小时" align="center">
            <template #default="scope">
              <template v-if="scope.row.threeHourValue">
                <div>{{ scope.row.threeHourValue }}</div>
              </template>
              <template v-else>
                <div style="color: rgba(255, 85, 85, 0.82)">未设置</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="sixHourValue" label="6小时" align="center">
            <template #default="scope">
              <template v-if="scope.row.sixHourValue">
                <div>{{ scope.row.sixHourValue }}</div>
              </template>
              <template v-else>
                <div style="color: rgba(255, 85, 85, 0.82)">未设置</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="twelveHourValue" label="12小时" align="center">
            <template #default="scope">
              <template v-if="scope.row.twelveHourValue">
                <div>{{ scope.row.twelveHourValue }}</div>
              </template>
              <template v-else>
                <div style="color: rgba(255, 85, 85, 0.82)">未设置</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            prop="twentyFourHourValue"
            label="24小时"
            align="center"
          >
            <template #default="scope">
              <template v-if="scope.row.twentyFourHourValue">
                <div>{{ scope.row.twentyFourHourValue }}</div>
              </template>
              <template v-else>
                <div style="color: rgba(255, 85, 85, 0.82)">未设置</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="210"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleEdit(scope.row)"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="fetchRainfallData"
        />
      </div>
    </div>

    <!-- 添加或修改流域对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="720px"
      append-to-body
      @close="handleCancel"
    >
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="村社" prop="dangerousName">
              <div style="font-weight: bold">{{ form.name }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预警等级" prop="warnLevel">
              <div style="font-weight: bold">
                <div style="color: #f78c08" v-if="form.warnLevel == 2">
                  准备转移
                </div>
                <div style="color: #fc4949" v-else>立即转移</div>
              </div>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item
              label="前期雨量累计时段(时)"
              prop="rainAccumulatedTime"
            >
              <el-input-number
                v-model="form.rainAccumulatedTime"
                :min="0"
                :max="168"
                placeholder="请输入数值"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="前期累计雨量(mm)" prop="rainAccumulated">
              <el-input-number
                v-model="form.rainAccumulated"
                placeholder="请输入数值"
                :precision="1"
                :min="0"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="30分钟(mm)" prop="thirtyMinuteValue">
              <el-input-number
                v-model="form.thirtyMinuteValue"
                placeholder="请输入数值"
                :precision="1"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="1小时(mm)" prop="oneHourValue">
              <el-input-number
                v-model="form.oneHourValue"
                placeholder="请输入数值"
                :precision="1"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="3小时(mm)" prop="threeHourValue">
              <el-input-number
                v-model="form.threeHourValue"
                placeholder="请输入数值"
                :precision="1"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="6小时(mm)" prop="sixHourValue">
              <el-input-number
                v-model="form.sixHourValue"
                placeholder="请输入数值"
                :precision="1"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="12小时(mm)" prop="twelveHourValue">
              <el-input-number
                v-model="form.twelveHourValue"
                placeholder="请输入数值"
                :precision="1"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="24小时(mm)" prop="twentyFourHourValue">
              <el-input-number
                v-model="form.twentyFourHourValue"
                placeholder="请输入数值"
                :precision="1"
                :min="0"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">保 存</el-button>
          <el-button @click="handleCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.left-tree {
  margin-right: 20px;
}
:deep(.el-input-number__decrease) {
  display: none;
}

:deep(.el-input-number__increase) {
  display: none;
}

:deep(.el-input-number .el-input__wrapper) {
  padding: 0;
}
</style>
