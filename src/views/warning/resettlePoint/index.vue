<template>
    <div class="app-container">
        <!-- 添加或修改流域对话框 -->
        <el-row>
            <el-form ref="queryFormRef" :model="queryForm" inline class="form-container">
                <el-form-item label="安置点名称" prop="name">
                    <el-input v-model="queryForm.name" placeholder="请输入" clearable></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-select v-model="queryForm.status" placeholder="请选择" clearable>
                        <el-option label="全部" value="" />
                        <el-option label="正常" :value="1" />
                        <el-option label="停用" :value="0" />
                    </el-select>
                </el-form-item>

                <el-form-item class="form-item-button">
                    <el-button type="primary" @click="getList" icon="Search">查询</el-button>
                    <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
                </el-form-item>
            </el-form>
        </el-row>
        <div class="content">
            <AdcdTree v-model="queryForm.townAdcd" @getList="getList" />
            <div class="right-table">
                <div class="table-header">
                    <el-button type="success" icon="CirclePlus" @click="addFamily">新增</el-button>
                </div>
                <el-table :data="tableData" :style="{ width: '100%', height: '100%' }" stripe
                    v-loading="loading">
                    <el-table-column type="index" label="序号" width="80" />
                    <el-table-column prop="name" label="安置点名称" />
                    <el-table-column prop="address" label="地址" />
                    <el-table-column prop="headerName" label="安置点负责人" />
                    <el-table-column prop="phone" label="联系方式" />
                    <el-table-column prop="resettlementPeoNum" label="可安置人数(人)" />
                    <el-table-column prop="status" label="状态">
                        <template #default="scope">
                            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">{{ scope.row.status === 1 ?
                                '正常' : '停用' }}</el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column prop="address" label="操作" width="210" align="center">
                        <template #default="scope">
                            <el-button type="primary" link icon="View" @click="handleSee(scope.row)"
                                >查看</el-button>
                            <el-button type="primary" link icon="Edit" @click="handleEdit(scope.row)"
                                >编辑</el-button>
                            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
                    @pagination="getList" />
            </div>
        </div>
        <el-dialog v-model="dialogShow" :title="title" style="width: 40%;">
            <el-form ref="addFormRef" :model="addForm" label-width="auto" inline :rules="rules"
                :disabled="title.includes('查看安置点信息')" style="display: flex;flex-wrap: wrap;">
                <el-form-item label="安置点名称" prop="name">
                    <el-input v-model="addForm.name" placeholder="请输入" style="width: 180px;" :maxlength="50" ></el-input>
                </el-form-item>
                <el-form-item label="所在区域" prop="areaAdcd">
                    <!-- <el-select v-model="addForm.areaAdcd" placeholder="请选择" style="width: 180px;" clearable
                        @change="addFormAreaChange">
                        <el-option :label="item.adnm" :value="item.adcd" highlight-current v-for="item in areaList"
                            :key="item.adcd" />

                    </el-select> -->
                    <el-tree-select  v-model="addForm.areaAdcd" :data="areaList" style="width: 180px;"
                    :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="adcd" placeholder="选择所属县区"
                    check-strictly @node-click="formAreaHandleNodeClick" :render-after-expand="false"/>
                </el-form-item>

                <el-form-item label="所属乡镇" prop="townAdcd">
                    <el-select v-model="addForm.townAdcd" placeholder="请选择" style="width: 180px;" clearable>
                        <el-option :label="item.adnm" :value="item.adcd" highlight-current v-for="item in townList"
                            :key="item.adcd" />

                    </el-select>
                </el-form-item>
                <el-form-item label="地址" prop="address">
                    <el-input v-model="addForm.address" placeholder="请输入" style="width: 180px;" :maxlength="100" show-word-limit></el-input>
                </el-form-item>
                <el-form-item label="经度" prop="lgtd">
                    <el-input-number v-model="addForm.lgtd" placeholder="请输入" style="width: 180px;" :min="73" :max="136"
                        :precision="6"></el-input-number>
                </el-form-item>
                <el-form-item label="纬度" prop="lttd">
                    <el-input-number v-model="addForm.lttd" placeholder="请输入" style="width: 180px;" :min="3" :max="54"
                        :precision="6"></el-input-number>
                </el-form-item>
                <el-form-item label="安置点负责人" prop="headerName">
                    <el-input v-model="addForm.headerName" placeholder="请输入" style="width: 180px;" :maxlength="10"></el-input>
                </el-form-item>
                <el-form-item label="联系方式" prop="phone">
                    <el-input v-model="addForm.phone" placeholder="请输入" style="width: 180px;"
                        oninput="value = value.replace(/[^0-9]/g,'').substring(0,11)"></el-input>
                </el-form-item>
                <el-form-item label="可安置人数" prop="resettlementPeoNum">
                    <el-input v-model="addForm.resettlementPeoNum" placeholder="请输入" style="width: 180px;"
                        oninput="value = value.replace(/[^0-9]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="addForm.status">
                        <el-radio :label="1">正常</el-radio>
                        <el-radio :label="0">停用</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="储备物资情况" prop="notes" style="width: 100%;">
                    <el-input v-model="addForm.notes" placeholder="请输入" style="width: 90%;" type="textarea"
                        show-word-limit :maxlength="200" :rows="5"></el-input>
                </el-form-item>
                <el-form-item label="空间预览" prop="geoType" v-if="title != '新增安置点'">
                    <el-radio-group v-model="addForm.geoType" v-if="false">
                        <el-radio label="zip">矢量数据(shp-zip,kml,geojson文件)</el-radio>
                        <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                        <el-radio label="people">人工绘制</el-radio>
                    </el-radio-group>

                </el-form-item>
                <div style="width: 100%;height: 130px;" v-show="addForm.geoType === 'zip'">
                    <el-upload class="upload-demo" style="width: 100%;" ref="uploadRef" drag name="multipartFile"
                        :on-change="handleChange" :action="uploadUrl" :data="{
                        }" :headers="{ 'Authorization': token }" :limit="2" :on-success="handleSuccess">
                        <div class="el-upload__text">
                            拖拽上传 或 <em>点击上传</em>
                        </div>
                    </el-upload>
                </div>
                <div style="width: 100%;height: 350px;background: #555" v-if="title != '新增安置点'">
                    <min-map @updateBound="updateBound" :geom="addForm.geom" :geoType="'point'"
                        :points="[addForm.lgtd, addForm.lttd]" :show-tool="false"
                        style="width: 100%;height:100%"></min-map>
                </div>
            </el-form>
            <template #footer>
                <span class="dialog-footer" v-if="!title.includes('查看安置点')">
                    <el-button type="primary" v-if="title.includes('新增安置点')" @click="submitAdd">保 存</el-button>
                    <el-button type="primary" v-else-if="title.includes('编辑安置点')" @click="submitEdit">保 存</el-button>
                    <el-button @click="dialogShow = false">取 消</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from "vue";
import MinMap from "@/components/Map/plugins/drawTool";
import { getToken } from '@/utils/auth'
import { selectTsList, getAdcdTree } from "@/api/watershed/ads";
import { addPlace, placeList, deletePlace, updatePlace } from "@/api/warning";
import AdcdTree from "@/components/AdcdTree/index.vue";

defineOptions({
  name: 'ResettlePoint'
})

const token = getToken()
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
const { proxy } = getCurrentInstance();
const uploadRef = ref('')
const loading = ref(false)
const data = reactive({
    total: 0,
    queryForm: {
        stnm: "",
        name: "",
        townAdcd: '',
        status: "",
        pageNum: 1,
        pageSize: 20
    },
    rules: {
        name: [
            { required: true, message: '请输入行政区名称', trigger: 'change' },
        ],
        areaAdcd: [
            { required: true, message: '请选择所属行政区', trigger: 'change' },
        ],
        townAdcd: [
            { required: true, message: '请选择所属乡镇', trigger: 'change' },
        ],

    },
    dialogShow: false,
    title: '新增安置点',
    addForm: {
        name: "",
        areaAdcd: "",
        townAdcd: "",
        villageAdcd: "",
        lgtd: "",
        lttd: "",
        headerName: "",
        phone: "",
        resettlementPeoNum: "",
        status: 1,
        notes: "",
        geoType: "people",
        geom: ""
    },
    tableData: [],
    areaList: [],
    townList: [],
});
const { queryForm, dialogShow, addForm, title, rules, total, areaList, townList, tableData } = toRefs(data)

onMounted(() => {
    getList()
    getAdcdList()
})
const getAdcdList = () => {
    getAdcdTree({
        adcd: ''
    }).then((res) => {

        data.areaList = res.data[0].children || []
    })
}
const submitAdd = () => {
    proxy.$refs.addFormRef.validate(valid => {
        if (!valid) return;
        let validGeom = data.addForm.geom == "" ? null: data.addForm.geom
        addPlace({...data.addForm, geom: validGeom}).then(res => {
            if (res.code == 200) {
                proxy.$modal.msgSuccess("新增成功");
                data.dialogShow = false
                getList()
            } else {
                proxy.$modal.msgError(res.msg);
            }
        })
    })
}
const submitEdit = () => {
    proxy.$refs.addFormRef.validate(valid => {
        if (valid) {
            updatePlace(data.addForm, data.addForm.id).then(res => {
                if (res.code == 200) {
                    proxy.$modal.msgSuccess("修改成功");
                    data.dialogShow = false
                    getList()
                } else {
                    proxy.$modal.msgError(res.msg);
                }
            })
        }
    })
}
const addFormAreaChange = (e) => {
    getAllTown(e)
    data.addForm.townCode = ''
    data.townList = []

}
const formAreaHandleNodeClick = (node) => {
    console.log(node.children);
  if (!node.children || node.children.length === 0) {
    // 只选中最后一个层级节点
    console.log(node);
    data.addForm.areaAdcd = node.adcd
    getAllTown(node.adcd)
  } else {
    // 清空选中
    proxy.$modal.msgError("请选择区县");
    nextTick(() => {
        data.addForm.areaAdcd = ''
    })

  }
}
const getAllTown = (adcd) => {
    selectTsList({ pageNum: 1, pageSize: 9999, parentAdcd: adcd || '' }).then(res => {
        data.townList = res.rows || []
    })
}

const handleSee = (row) => {
    data.title = '查看安置点 - ' + row.name
    getAllTown(row.areaAdcd)
    data.dialogShow = true

        data.addForm = row
        data.addForm.geoType = 'people'

}
const handleEdit = (row) => {
    data.title = '编辑安置点 - ' + row.name
    getAllTown(row.areaAdcd)
    data.dialogShow = true

        data.addForm = row
        data.addForm.geoType = 'people'

}
const handleDelete = (row) => {
    proxy.$modal.confirm('是否确认删除该条数据?').then(res => {
        deletePlace(row.id).then(res => {
            if (res.code == 200) {
                proxy.$modal.msgSuccess("删除成功");
                getList()
            } else {
                proxy.$modal.msgError(res.msg);
            }
        })
    })
}
const getList = () => {
    loading.value = true
    placeList(data.queryForm).then(res => {
        data.tableData = res.data.records || []
        data.total = res.data.total || 0
        loading.value = false
    })
}
const resetQuery = () => {
    delete data.addForm.id
    proxy.$refs.queryFormRef.resetFields()
    getList()
}
const addFamily = () => {
    data.title = '新增安置点'
    data.dialogShow = true
    data.addForm = {
        name: "",
        areaAdcd: "",
        townAdcd: "",
        villageAdcd: "",
        lgtd: "",
        lttd: "",
        headerName: "",
        phone: "",
        resettlementPeoNum: "",
        status: 1,
        notes: "",
        geoType: "people",
        geom: ""
    }
}
const handleChange = (file, fileList) => {
    if (fileList.length > 1) {
        fileList[0] = fileList[1]
        fileList.splice(1, 1);
    }
}
const handleSuccess = (response) => {
    if (response.code == 200) {
        form.value.geom = response.data
    } else {
        uploadRef.value?.clearFiles()
        proxy.$modal.msgError(response.msg);
    }
}
const updateBound = (geojson) => {
    // 提交geojson数据到后台
    console.log(geojson)
    data.addForm.geom = geojson
}
</script>
<style scoped lang="scss">
:deep(.addForm .el-form--inline .el-form-item) {
    margin-right: 0;
    width: 50%;

}

.bth {
    margin-left: 20px;
}

.content {
    margin-top: 16px;
}

.left-tree {
    width: 300px;
    display: block;
}

.right-table {
    flex: 1;
    margin-left: 20px;
    height: calc(100vh - 310px);
}
</style>