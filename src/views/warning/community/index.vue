<template>
  <div class="app-container">
    <!-- 添加或修改流域对话框 -->
    <el-form
      ref="queryFormRef"
      :model="queryForm"
      inline
      class="form-container"
    >
      <el-form-item label="名称" prop="adnm">
        <el-input
          v-model="queryForm.adnm"
          placeholder="请输入村社名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="代码" prop="adcd">
        <el-input
          v-model="queryForm.adcd"
          placeholder="请输入村社代码"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="所属区县" prop="areaCode">
        <el-form-item prop="addvcd">
          <el-tree-select
            v-model="queryForm.areaCode"
            :data="areaList"
            :render-after-expand="false"
            @node-click="handleNodeClick"
            :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
            value-key="adcd"
            placeholder="选择所属区县"
            check-strictly
          />
        </el-form-item>
      </el-form-item>
      <el-form-item label="所属乡镇" prop="townCode">
        <el-select
          v-model="queryForm.townCode"
          placeholder="请选择"
          clearable
          filterable
        >
          <el-option
            :label="item.adnm"
            :value="item.adcd"
            :filterNodeMethod="filterNode"
            highlight-current
            v-for="item in townList"
            :key="item.adcd"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="防治区类型" prop="preventionZonesType">
        <el-select
          v-model="queryForm.preventionZonesType"
          placeholder="请选择"
          clearable
        >
          <el-option label="重点防治区" :value="1" />
          <el-option label="一般防治区" :value="2" />
          <el-option label="非防治区" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search"
          >查询</el-button
        >
        <el-button @click="resetQuery" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div class="content">
      <div class="right-table">
        <div class="table-header">
          <el-button type="success" icon="CirclePlus" @click="addData"
            >新增</el-button
          >
        </div>
        <el-table :data="tableData" v-loading="loading" stripe>
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="adnm" label="名称" width="180" />
          <el-table-column prop="adcd" label="代码" width="180" />
          <el-table-column prop="areaName" label="所属区县" />
          <el-table-column prop="townName" label="所属乡镇" />
          <el-table-column prop="floodControlCapacity" label="防洪能力（年）" />
          <el-table-column prop="regionalArea" label="区域面积" />
          <el-table-column prop="registeredPopulation" label="户籍人口" />
          <el-table-column prop="area" label="土地面积(㎡)" />
          <el-table-column prop="totalHouseholds" label="总户数(户)" />
          <el-table-column prop="totalPeoNum" label="总人口(人)" />
          <el-table-column prop="totalHouses" label="房屋数(座)" />
          <el-table-column prop="cultivatedArea" label="耕地面积(亩)" />
          <el-table-column prop="preventionZonesType" label="防治区类型">
            <template #default="scope">
              <el-tag v-if="scope.row.preventionZonesType == 1" type="success"
                >重点防治区</el-tag
              >
              <el-tag v-if="scope.row.preventionZonesType == 2" type="warning"
                >一般防治区</el-tag
              >
              <el-tag v-if="scope.row.preventionZonesType == 3" type="danger"
                >非防治区</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            label="操作"
            width="210"
            align="center"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                icon="View"
                @click="handleSee(scope.row)"
                >查看</el-button
              >
              <el-button
                type="primary"
                link
                icon="Edit"
                @click="handleEdit(scope.row)"
                >编辑</el-button
              >
              <el-button
                type="danger"
                link
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <el-dialog v-model="dialogShow" :title="title" style="width: 40%">
      <el-form
        ref="addFormRef"
        :model="addForm"
        label-width="auto"
        class="addForm"
        inline
        :rules="rules"
        :disabled="title.includes('查看村社信息')"
      >
        <el-form-item label="行政村代码" prop="adcd">
          <el-input
            v-model="addForm.adcd"
            placeholder="请输入"
            style="width: 180px"
          ></el-input>
        </el-form-item>
        <el-form-item label="村社名称" prop="adnm">
          <el-input
            v-model="addForm.adnm"
            placeholder="请输入"
            style="width: 180px"
            :formatter="(value) => `${value}`.substring(0, 50)"
          ></el-input>
        </el-form-item>
        <el-form-item label="所属区县" prop="areaCode">
          <!-- <el-select v-model="addForm.areaCode" placeholder="请选择" style="width: 180px;" clearable
                        @change="addFormAreaChange">
                        <el-option :label="item.adnm" :value="item.adcd" :filterNodeMethod="filterNode"
                            highlight-current v-for="item in areaList" :key="item.adcd" />

                    </el-select> -->
          <el-tree-select
            v-model="addForm.areaCode"
            :data="areaList"
            style="width: 180px"
            :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
            value-key="adcd"
            placeholder="选择所属县区"
            check-strictly
            @node-click="formAreaHandleNodeClick"
            :render-after-expand="false"
          />
        </el-form-item>
        <el-form-item label="所属乡镇" prop="townCode">
          <el-select
            v-model="addForm.townCode"
            placeholder="请选择"
            style="width: 180px"
            clearable
            @change="townSelectChange"
            filterable
          >
            <el-option
              :label="item.adnm"
              :value="item.adcd"
              :filterNodeMethod="filterNode"
              highlight-current
              v-for="item in townList"
              :key="item.adcd"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="经度" prop="lgtd">
          <el-input-number
            v-model="addForm.lgtd"
            placeholder="请输入"
            style="width: 180px"
            :precision="6"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="纬度" prop="lttd">
          <el-input-number
            v-model="addForm.lttd"
            placeholder="请输入"
            style="width: 180px"
            :precision="6"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="区域面积(km²)" prop="regionalArea">
          <el-input
            v-model="addForm.regionalArea"
            placeholder="请输入"
            style="width: 180px"
            :formatter="(value) => `${value}`.replace(/[^\d.]/g, '')"
            onblur="value = parseFloat(value).toFixed(1)"
          ></el-input>
        </el-form-item>
        <el-form-item label="防洪能力（年）" prop="floodControlCapacity">
          <el-input
            v-model="addForm.floodControlCapacity"
            placeholder="请输入"
            style="width: 180px"
            :formatter="(value) => `${value}`.replace(/[^\d.]/g, '')"
          ></el-input>
        </el-form-item>
        <el-form-item label="户籍人口" prop="registeredPopulation">
          <el-input
            v-model="addForm.registeredPopulation"
            placeholder="请输入"
            style="width: 180px"
            :formatter="(value) => `${value}`.replace(/[^\d.]/g, '')"
          ></el-input>
        </el-form-item>
        <el-form-item label="土地面积(km²)" prop="area">
          <el-input
            v-model="addForm.area"
            placeholder="请输入"
            style="width: 180px"
            :formatter="(value) => `${value}`.replace(/[^\d.]/g, '')"
            onblur="value = parseFloat(value).toFixed(1)"
          ></el-input>
        </el-form-item>
        <el-form-item label="总户数(户)" prop="totalHouseholds">
          <el-input
            v-model="addForm.totalHouseholds"
            placeholder="请输入"
            style="width: 180px"
            :formatter="(value) => `${value}`.replace(/[^\d]/g, '')"
          ></el-input>
        </el-form-item>
        <el-form-item label="总人口(人)" prop="totalPeoNum">
          <el-input
            v-model="addForm.totalPeoNum"
            placeholder="请输入"
            style="width: 180px"
            :formatter="(value) => `${value}`.replace(/[^\d]/g, '')"
          ></el-input>
        </el-form-item>
        <el-form-item label="房屋数(座)" prop="totalHouses">
          <el-input
            v-model="addForm.totalHouses"
            placeholder="请输入"
            style="width: 180px"
            :formatter="(value) => `${value}`.replace(/[^\d]/g, '')"
          ></el-input>
        </el-form-item>
        <el-form-item label="耕地面积(亩)" prop="cultivatedArea">
          <el-input
            v-model="addForm.cultivatedArea"
            placeholder="请输入"
            style="width: 180px"
            :formatter="(value) => `${value}`.replace(/[^\d.]/g, '')"
            onblur="value = parseFloat(value).toFixed(1)"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="防治区类型"
          prop="preventionZonesType"
          style="align-self: flex-start"
        >
          <el-select
            v-model="addForm.preventionZonesType"
            placeholder="请选择"
            style="width: 180px"
          >
            <el-option label="重点防治区" :value="1" />
            <el-option label="一般防治区" :value="2" />
            <el-option label="非防治区" :value="3" />
          </el-select>
        </el-form-item>

        <el-form-item
          label="空间预览"
          prop="geoType"
          style="width: 100%"
          v-if="!title.includes('新增村社信息')"
        >
          <el-radio-group v-model="addForm.geoType" v-if="false">
            <el-radio label="zip">矢量数据(shp-zip,kml,geojson文件)</el-radio>
            <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
            <el-radio label="people">人工绘制</el-radio>
          </el-radio-group>
        </el-form-item>
        <div
          style="width: 100%; height: 130px"
          v-show="addForm.geoType === 'zip'"
        >
          <el-upload
            class="upload-demo"
            style="width: 100%"
            ref="uploadRef"
            drag
            name="multipartFile"
            :on-change="handleChange"
            :action="uploadUrl"
            :data="{}"
            :headers="{ Authorization: token }"
            :limit="2"
            :on-success="handleSuccess"
          >
            <div class="el-upload__text">拖拽上传 或 <em>点击上传</em></div>
          </el-upload>
        </div>
        <div
          style="width: 100%; height: 350px; background: #555"
          v-if="!title.includes('新增村社信息')"
        >
          <min-map
            @updateBound="updateBound"
            :geom="addForm.geom"
            :geoType="addForm.geoType"
            :points="[addForm.lgtd, addForm.lttd]"
            :show-tool="addForm.geoType === 'people'"
            style="width: 100%; height: 100%"
          ></min-map>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer" v-if="!title.includes('查看村社信息')">
          <el-button
            type="primary"
            @click="submitAdd"
            v-if="title.includes('新增村社信息')"
            >保 存
          </el-button>
          <el-button
            type="primary"
            @click="submitEdit"
            v-else-if="title.includes('编辑村社信息')"
            >保 存
          </el-button>
          <el-button @click="dialogShow = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from "vue";
import { getAdcdTree, selectTsList } from "@/api/watershed/ads";
import MinMap from "@/components/Map/plugins/drawTool";
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user";
import {
  villageList,
  updateVillage,
  addVillage,
  deleteVillage,
} from "@/api/warning/index";

defineOptions({
  name: "Community",
});

const token = getToken();
const userStore = useUserStore();
const userId = userStore.deptId;
const uploadUrl = import.meta.env.VITE_APP_BASE_API + "/hydro/convert/shp-kml/";
const { proxy } = getCurrentInstance();
const uploadRef = ref("");
const loading = ref(false);
const data = reactive({
  queryForm: {
    name: "",
    areaCode: "",
    preventionZonesType: "",
    phone: "",
    pageNum: 1,
    pageSize: 20,
    geoType: "people",
    geom: "",
  },
  rules: {
    adcd: [
      { required: true, message: "请输入行政区代码", trigger: "change" },
      //自定义校验方法
      {
        validator: (rule, value, callback) => {
          console.log(value);
          // console.log(userId);
          if (userId == 245) {
            //满足曲阳县15位行政区的编码表校验
            if (value.length < 15) {
              callback(new Error("村社代码为15位数字编码"));
            } else {
              if (data.addForm.townCode) {
                let up9 = data.addForm.townCode?.substring(0, 9);
                if (up9 == value.substring(0, 9)) {
                  callback();
                } else {
                  console.log("up9", up9);
                  console.log("value", value);
                  callback(new Error("请输入正确的村社代码"));
                }
              } else {
                callback(new Error("请选择所属乡镇"));
              }
            }
          } else {
            if (value.length < 12) {
              callback(new Error("村社代码为12位数字编码"));
            } else {
              if (data.addForm.townCode) {
                let up9 = data.addForm.townCode?.substring(0, 9);
                if (up9 == value.substring(0, 9)) {
                  callback();
                } else {
                  console.log("up9", up9);
                  console.log("value", value);
                  callback(new Error("请输入正确的村社代码"));
                }
              } else {
                callback(new Error("请选择所属乡镇"));
              }
            }
          }
        },
        trigger: "blur",
      },
    ],
    adnm: [{ required: true, message: "请输入行政区名称", trigger: "blur" }],
    townCode: [{ required: true, message: "请选择所属乡镇", trigger: "blur" }],

    areaCode: [
      { required: true, message: "请选择所属行政区", trigger: "blur" },
    ],
  },
  dialogShow: false,
  title: "新增居民信息",
  addForm: {
    areaCode: "",
    name: "",
    adcd: "",
    lgtd: "",
    lttd: "",
    town: "",
    totalHouseholds: "",
    totalPeoNum: "",
    totalHouses: "",
    cultivatedArea: "",
    preventionZonesType: "",
    geoType: "people",
    geom: "",
  },

  propsTree: {
    value: "adcd",
    label: "adnm",
    children: "children",
  },
  total: 0,
  tableData: [],
  adcdList: [],
  townList: [],
  areaList: [],
});
const {
  queryForm,
  propsTree,
  tableData,
  dialogShow,
  addForm,
  title,
  rules,
  adcdList,
  townList,
  areaList,
  total,
} = toRefs(data);
onMounted(() => {
  getList();
  getAdcdList(); //获取所有的区县/乡镇
  // getTreeList()
  getAllTown(); //获取所有的乡镇
});
const getAllTown = (adcd) => {
  selectTsList({ pageNum: 1, pageSize: 9999, parentAdcd: adcd || "" }).then(
    (res) => {
      data.townList = res.rows || [];
    }
  );
};
const addFormAreaChange = (e) => {
  getAllTown(e);
  data.addForm.townCode = "";
  data.townList = [];
};
const queryAreaChange = (e) => {
  getAllTown(e);
  data.queryForm.townCode = "";
  data.townList = [];
};
const getAdcdList = () => {
  getAdcdTree().then((res) => {
    data.areaList = res.data[0].children || [];
  });
};
const townSelectChange = (e) => {
  if (e) {
    data.addForm.adcd = e.substring(0, 9);
  }
};
// const getTreeList = () => {
//     getTreeOption(5).then((res) => {

//         data.adcdList = formatTree(res.data)
//     })
// }
const filterNode = (query, node) => {
  return node.adnm.includes(query);
};
const formatTree = (data) => {
  let dataList = data.map((item) => {
    return {
      ...item.data,
      children: item.children && formatTree(item.children),
    };
  });
  //dataList中有children为空的节点 删除
  let dataList2 = dataList.map((item) => {
    if (item.children.length === 0) {
      delete item.children;
    }
    return {
      ...item,
    };
  });
  return dataList2;
};
const resetQuery = () => {
  proxy.$refs.queryFormRef.resetFields();
  getList();
};
const getList = () => {
  loading.value = true;
  villageList(data.queryForm).then((res) => {
    data.tableData = res.data.records || [];
    data.total = res.data.total || 0;
    loading.value = false;
  });
};
const handleDelete = (row) => {
  // 删除
  proxy
    .$confirm("确定删除该条数据吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      // 删除
      deleteVillage(row.adcd).then((res) => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess("删除成功");
          getList();
        } else {
          proxy.$modal.msgError(res.msg);
        }
      });
    })
    .catch(() => {
      proxy.$message({
        type: "info",
        message: "已取消删除",
      });
    });
};
const submitAdd = () => {
  proxy.$refs.addFormRef.validate((valid) => {
    if (valid) {
      addVillage(data.addForm).then((res) => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess("新增成功");
          data.dialogShow = false;
          getList();
        } else {
          proxy.$modal.msgError(res.msg);
        }
      });
    }
  });
};
const submitEdit = () => {
  proxy.$refs.addFormRef.validate((valid) => {
    if (valid) {
      updateVillage(data.addForm).then((res) => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess("修改成功");
          data.dialogShow = false;
          getList();
        } else {
          proxy.$modal.msgError(res.msg);
        }
      });
    }
  });
};

const querySelectChange = (res) => {
  if (queryForm.townCode) {
    let el = townList.find((item) => item.adcd == queryForm.townCode);
    if (el.parentAdcd == res) {
      //如果区adcd 和县adcd 上下管理符合则保存 否则清空
    } else {
      queryForm.townCode = "";
    }
  }
};
const handleEdit = (row) => {
  data.title = "编辑村社信息 - " + row.adnm;
  data.addForm = row;
  console.log(row);
  data.dialogShow = true;
  getAllTown(row.areaCode);
};
// const getAreaAndTown = (row) => {
//     getAllVillage(row.townCode)
// }
const handleSee = (row) => {
  data.title = "查看村社信息 - " + row.adnm;
  data.addForm = row;

  data.dialogShow = true;
  getAllTown(row.areaCode);
};
const addData = () => {
  data.title = "新增村社信息";
  data.dialogShow = true;
  delete data.addForm.id;
  proxy.$nextTick(() => {
    console.log(proxy.$refs.addFormRef);
    proxy.$refs.addFormRef.resetFields();
  });
};
const handleChange = (file, fileList) => {
  if (fileList.length > 1) {
    fileList[0] = fileList[1];
    fileList.splice(1, 1);
  }
};
const handleSuccess = (response) => {
  if (response.code == 200) {
    form.value.geom = response.data;
  } else {
    uploadRef.value?.clearFiles();
    proxy.$modal.msgError(response.msg);
  }
};
const updateBound = (geojson) => {
  // 提交geojson数据到后台
  console.log(geojson);
  form.value.geom = geojson;
};

// const handleNodeClick = (el) => {
//   data.queryForm.areaCode = el.adcd;
//   getList();
// };
const handleNodeClick = (node) => {
  if (!node.children || node.children.length === 0) {
    // 只选中最后一个层级节点
    data.queryForm.areaCode = node.adcd;
    // console.log(node);
  } else {
    // 清空选中
    // form.value.lyCode = '';
    proxy.$modal.msgError("请选择区县");
    nextTick(() => {
      data.queryForm.areaCode = "";
    });
  }
};
const formAreaHandleNodeClick = (node) => {
  console.log(node.children);
  if (!node.children || node.children.length === 0) {
    // 只选中最后一个层级节点
    console.log(node);
    data.addForm.areaCode = node.adcd;
    getAllTown(node.adcd);
  } else {
    // 清空选中
    proxy.$modal.msgError("请选择区县");
    nextTick(() => {
      data.addForm.areaCode = "";
    });
  }
};
</script>
<style scoped lang="scss">
:deep(.addForm) {
  display: flex;
  flex-wrap: wrap;

  .el-form-item {
    margin-right: 0;
    width: 50%;
  }
}
</style>
