<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="form-container">
      <el-form-item label="村社名称" prop="name">
        <el-input v-model="queryParams.villageName" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item label="所属流域" prop="basinId">
        <el-tree-select v-model="queryParams.basinId" :data="transformDataForTreeSelect(waterAreaList)"
          check-strictly clearable :render-after-expand="false" placeholder="请选择流域" />
      </el-form-item> -->
      <el-form-item label="防治区类型" prop="type">
        <el-select v-model="queryParams.preventionZonesType" placeholder="请选择" clearable>
          <el-option label="重点防治区" :value="1" />
          <el-option label="一般防治区" :value="2" />
          <el-option label="非防治区" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="content">
      <AdcdTree v-model="queryParams.adcd" @getList="getList" defaultExpandAll/>
      <div class="right-table">
        <div class="table-header">
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </div>
        <el-table v-loading="loading" :data="dataList" :span-method="objectSpanMethod" stripe>
          <!--         <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>-->
          <el-table-column type="index" label="序号" width="80" align="center">
            <template #default="scope">
              <div>{{(scope.$index) / 4 + 1  }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="村社名称" align="center"></el-table-column>
          <el-table-column prop="warnLevel" label="预警等级" align="center">
            <template #default="scope">
              <div>
                <div :style="{ color: getWarnLevelColor(scope.row) }">{{ getWarnLevelName(scope.row) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="sixHourValue" label="6小时" align="center">
            <template #default="scope">
              <template v-if="scope.row.sixHourValue">
                <div>{{ scope.row.sixHourValue }}</div>
              </template>
              <template v-else>
                <div style="color: rgba(255,85,85,0.82)">未设置</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="twelveHourValue" label="12小时" align="center">
            <template #default="scope">
              <template v-if="scope.row.twelveHourValue">
                <div>{{ scope.row.twelveHourValue }}</div>
              </template>
              <template v-else>
                <div style="color: rgba(255,85,85,0.82)">未设置</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="twentyFourHourValue" label="24小时" align="center">
            <template #default="scope">
              <template v-if="scope.row.twentyFourHourValue">
                <div>{{ scope.row.twentyFourHourValue }}</div>
              </template>
              <template v-else>
                <div style="color: rgba(255,85,85,0.82)">未设置</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="210" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </div>
    </div>
    <el-dialog :title="title" v-model="open" width="420px" append-to-body>
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="130px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="村社" prop="warnLevel">
              <div style="font-weight: bold">{{ form.name }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预警等级" prop="warnLevel">
              <div style="font-weight: bold">{{ getWarnLevelName(form) }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="6小时(mm)" prop="sixHourValue">
              <el-input-number
                v-model="form.sixHourValue"
                placeholder="请输入数值"
                :precision="1"
                :min="0"
                @input="(val) => handleInput(val, 'sixHourValue')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="12小时(mm)" prop="twelveHourValue">
              <el-input-number
                v-model="form.twelveHourValue"
                placeholder="请输入数值"
                :precision="1"
                :min="0"
                @input="(val) => handleInput(val, 'twelveHourValue')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="24小时(mm)" prop="twentyFourHourValue">
              <el-input-number
                v-model="form.twentyFourHourValue"
                placeholder="请输入数值"
                :precision="1"
                :min="0"
                @input="(val) => handleInput(val, 'twentyFourHourValue')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { onMounted } from "vue";
import { selectStlyList } from "@/api/watershed/ads";
import { saveFloodWarn, selectFloodWarn } from "@/api/warning/index";
import AdcdTree from "@/components/AdcdTree/index.vue";

defineOptions({
  name: 'ForewarningOmt'
})

const { proxy } = getCurrentInstance();

const dataList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const total = ref(0);
const treeRef = ref(null)
const waterAreaList = ref([])
const expandedKeys = ref([]) // 添加展开节点的key数组

const data = reactive({
  form: {
    geoType: 'people'
  },
  queryParams: {
    basinId: "",
    pageNum: 1,
    pageSize: 10,
    adcd: "", //
    type: "",
  },
  rules: {
  },
});

const { queryParams, form, rules } = toRefs(data);

//需要判断的属性组
const spanProps = ['villageCode', 'name'];

let rowSpansMap = new Map(); //存需要开始合并的行号，向下合并多少行

/**
 * 根据列表数据得出需要合并的行
 * @param data 列表数据
 */
const spanPropGroup = (data) => {
  let oldRow; //需要合并的行
  rowSpansMap = new Map(); //重置Map

  oldRow = data[0]; //默认第0行为需要合并的行
  rowSpansMap.set(0, 1); //第0行，向下合并一行(其实就是自己单独一行)
  let spanRow = 0; //记录需要开始合并的行号
  for (let i = 1; i < data.length; i++) {
    const item = data[i];
    let isSame = true;
    //遍历需要判断的属性判断对应值是否全部相等
    for (let j = 0; j < spanProps.length; j++) {
      const prop = spanProps[j];
      //只要有一个属性值不相等则记录新的需要合并的行号
      if (item[prop] != oldRow[prop]) {
        oldRow = item;
        rowSpansMap.set(i, 1);
        spanRow = i;
        isSame = false;
        break;
      }
    }
    //如果所有属性值相同则所需要合并的行数+1
    if (isSame) {
      let span = rowSpansMap.get(spanRow);
      rowSpansMap.set(spanRow, span + 1);
    }
  }
};

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 只有前两列需要合并
  if (columnIndex < 2) {
    //根据当前行号从map中获取开始合并的行根据当前行号从map中获取开始合并的行号，向下合并多少行
    const span = rowSpansMap.get(rowIndex);
    if (span != null) {
      return {
        rowspan: span, //向下合并span行
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};

const getWarnLevelName = (data) => {
  let name = '未设置'
  if (data.warnLevel === 1) {
    name = '可能发生'
  } else if (data.warnLevel === 2) {
    name = '可能性较大'
  } else if (data.warnLevel === 3) {
    name = '可能性大'
  } else if (data.warnLevel === 4) {
    name = '可能性很大'
  }
  return name
}
const getWarnLevelColor = (data) => {
  let color = '#fff'
  if (data.warnLevel === 1) {
    color = '#67C23A'
  } else if (data.warnLevel === 2) {

    color = '#409EFF'
  } else if (data.warnLevel === 3) {
    color = '#E6A23C'
  } else if (data.warnLevel === 4) {
    color = '#F56C6C'
  }
  return color
}

// 处理数值变化
const handleInput = (value, field) => {
  if (value < 0) {
    proxy.$modal.msgError('雨量为非负1位小数！');
    nextTick(() => {
      form.value[field] = null;
    });
  }
};

// 拼凑数组出来
function getTempWarnList(item, list) {
  let allLevels = [1, 2, 3, 4]
  list.forEach(ii => {
    let index = allLevels.indexOf(ii.warnLevel)
    if (index > -1) { //大于0 代表存在，
      allLevels.splice(index, 1) //存在就删除
    }
  })
  allLevels.forEach(level => {
    list.push({
      padnm: item.padnm,
      adnm: item.adnm,
      adcd: item.adcd,
      warnLevel: level,
    })
  })
  return list
}

/** 查询列表 */
function getList() {
  loading.value = true;

  selectFloodWarn(queryParams.value).then(res => {
    let ll = res.data.records || []
    let ls = []
    ll.forEach(item => {
      item.warnListDtos?.sort((a, b) => {
        return a.warnLevel - b.warnLevel
      })
    })

    // 预警等级（1立即转移2准备转移）
    ll.forEach(item => {
      let rainWarnList = item.warnListDtos || []
      if (rainWarnList.length === 4) { // 那么就是全的
      }
      else {
        rainWarnList = getTempWarnList(item, rainWarnList)
      }
      rainWarnList.forEach(rain => {
        ls.push({
          id: rain.id,
          villageCode: item.villageCode,
          padnm: item.padnm,
          name: item.villageName,
          adcd: item.adcd,
          warnLevel: rain.warnLevel,
          sixHourValue: rain.sixHourValue,
          twelveHourValue: rain.twelveHourValue,
          twentyFourHourValue: rain.twentyFourHourValue,
        })
      })
    })
    total.value = res.data.total || 0
    dataList.value = ls
    loading.value = false

    //进行传递数据
    spanPropGroup(dataList.value);
  });
  // listMenu(queryParams.value).then(response => {
  //   lyList.value = proxy.handleTree(response.data, "menuId");
  //   loading.value = false;
  // });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    pcode: '',
    addFlag: flag,
    geoType: 'people',
    lynm: undefined,
    icon: undefined,
    adcds: [],
  };
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {

  queryParams.value = {
    basinId: "",
    pageNum: 1,
    pageSize: 10,
    adcd: "", //
    type: "",
  }
  proxy.resetForm("queryRef");
  handleQuery();
}

async function handleUpdate(row) {
  reset();
  let obj = Object.assign({}, row);
  delete obj.children
  delete obj.createBy
  delete obj.createTime
  delete obj.updateBy
  delete obj.updateTime
  delete obj.remark
  delete obj.stAdCdBCode
  form.value = obj
  // form.value.hazardousId = row.id
  // delete form.value.id
  open.value = true;
  title.value = "编辑气象预警 - " + row.name;
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate(valid => {
    if (valid) {
      saveFloodWarn(form.value).then(response => {
        proxy.$modal.msgSuccess("编辑成功")
        open.value = false
        getList()
      })
    }
  })
}

const transformDataForTreeSelect = (data) => {
  // 递归地转换数据以匹配 el-tree-select 的需求
  return data.map(item => ({
    label: item.data.name, // 使用 'name' 属性作为标签
    value: item.data.basinId, // 使用 'basinId' 属性作为值
    children: item.children ? transformDataForTreeSelect(item.children) : [], // 递归转换子节点
  }));
}

const getAllWater = async () => {
  let res = await selectStlyList({ pageNum: 1, pageSize: 999 })
  waterAreaList.value = res.data || []
}
onMounted(() => {
  getAllWater()
  getList()
})
</script>
<style lang="scss" scoped>
:deep(.el-input-number__decrease) {
  display: none;
}

:deep(.el-input-number__increase) {
  display: none;
}

:deep(.el-input-number .el-input__wrapper) {
  padding: 0;
  padding-left: 20px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

.left-tree {
  margin-right: 20px;
}

.table-header {
  display: flex;
  justify-content: flex-end;
}
</style>