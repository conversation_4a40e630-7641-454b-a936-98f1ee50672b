<template>
    <div class="app-container">
        <!-- 添加或修改流域对话框 -->
        <el-row>
            <el-col :span="24"></el-col>
            <el-form ref="queryFormRef" :model="queryForm" inline style="display: flex;flex-wrap: wrap;">
                <el-form-item label="危险区名称" prop="hazardousName">
                    <el-input v-model="queryForm.hazardousName" placeholder="请输入" style="width: 180px;"></el-input>
                </el-form-item>
                <el-form-item label="所属流域" prop="basinId">
                    <el-select v-model="queryForm.basinId" placeholder="请选择" style="width: 180px;">
                        <el-option label="全部" value="" />
                        <el-option  :value="item.data.basinId" v-for="item in basinList" :key="item.data.basinId" :label="item.data.name"/>

                    </el-select>
                </el-form-item>
                <el-form-item label="危险区等级" prop="hazardousGrade">
                    <el-select v-model="queryForm.hazardousGrade" placeholder="请选择" style="width: 180px;">
                        <el-option label="极高风险" :value="1" />
                        <el-option label="高风险" :value="2" />
                        <el-option label="低风险" :value="3" />

                    </el-select>
                </el-form-item>

                <div class="bth">
                    <el-button type="primary" @click="getList">查询</el-button>
                    <el-button @click="resetQuery">重置</el-button>
                </div>
            </el-form>

        </el-row>
        <div class="content">
            <AdcdTree v-model="queryForm.adcd" @getList="getList" />
            <div class="right-table">
                <el-button type="primary" plain icon="Plus" @click="addFamily">新增</el-button>
                <el-table :data="tableData" :style="{ width: '100%', height: '100%', marginTop: '20px' }"
                    v-loading="loading">
                    <el-table-column type="index" label="序号" width="80" />
                    <el-table-column prop="hazardousId" label="危险区代码" />
                    <el-table-column prop="hazardousName" label="危险区名称" />
                    <el-table-column prop="hazardousGrade" label="危险区等级" >
                        <template #default="scope">
                            <span>
                                {{ scope.row.hazardousGrade === 1 ? '极高风险' : scope.row.hazardousGrade === 2 ? '高风险' :  '低风险'  }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="placementName" label="安置点" />
                    <el-table-column prop="address" label="操作" width="150" align="center">
                        <template #default="scope">
                            <el-link type="primary" size="mini" @click="handleSee(scope.row)"
                                style="margin-right: 10px;">查看</el-link>
                            <el-link type="primary" size="mini" @click="handleEdit(scope.row)"
                                style="margin-right: 10px;">编辑</el-link>
                            <el-link type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-link>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
                    @pagination="getList" />
            </div>
        </div>
        <el-dialog v-model="dialogShow" :title="title" style="width: 40%;">
            <el-form ref="addFormRef" :model="addForm" label-width="auto" inline :rules="rules"
                :disabled="title == '查看危险区转移安置点信息'" style="display: flex;flex-wrap: wrap;">
                <el-form-item label="危险区名称" prop="hazardousCode">
                    <el-select v-model="addForm.hazardousId" placeholder="请选择">
                        <el-option :label="item.name" :value="item.id" v-for="(item, index) in dangerousAreaData"
                            :key="index" />

                    </el-select>
                </el-form-item>
                <el-form-item label="安置点" prop="placementId">
                    <el-select v-model="addForm.placementId" placeholder="请选择">
                        <el-option :label="item.name" :value="item.id" v-for="(item, index) in residentData"
                            :key="index" />

                    </el-select>
                </el-form-item>
                <el-form-item label="转移负责人" prop="memberId">
                    <el-select v-model="addForm.memberId" placeholder="请选择" multiple="true">
                        <el-option :label="item.name" :value="item.id" v-for="(item, index) in memberData"
                            :key="index" />

                    </el-select>
                </el-form-item>
                <el-form-item label="转移路线"  style="width: 100%;" >
                    {{ addForm.geom == null && title == '查看危险区转移安置点信息' ? '暂无空间数据' : null }}
                </el-form-item>
                <el-form-item  prop="geoType" style="width: 100%;" v-if="title != '查看危险区转移安置点信息'">
                    &nbsp; &nbsp; 空间数据  &nbsp;
                    <el-radio-group v-model="addForm.geoType" v-if="true">
                        <el-radio label="zip">矢量数据(shp-zip,kml,geojson文件)</el-radio>
                        <el-radio label="wfs" disabled>OGC地图服务(wfs)</el-radio>
                        <el-radio label="polyline">人工绘制</el-radio>
                    </el-radio-group>

                </el-form-item>

                <div style="width: 100%;height: 130px;" v-show="addForm.geoType === 'zip'">
                    <el-upload class="upload-demo" style="width: 100%;" ref="uploadRef" drag name="multipartFile"
                        :on-change="handleChange" :action="uploadUrl" :data="{
                        }" :headers="{ 'Authorization': token }" :limit="2" :on-success="handleSuccess">
                        <div class="el-upload__text">
                            拖拽上传 或 <em>点击上传</em>
                        </div>
                    </el-upload>
                </div>
                <div style="width: 100%;height: 350px;background: #555">
                    <min-map @updateBound="updateBound" :geom="addForm.geom" :geoType="addForm.geoType" :extraData="addForm.extraData"
                        :show-tool="addForm.geoType === 'polyline'" style="width: 100%;height:100%"></min-map>
                </div>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogShow = false">取消</el-button>
                    <el-button type="primary" @click="submitAdd" v-if="title == '新增危险区转移安置点信息'">确认 </el-button>
                    <el-button type="primary" @click="submitEdit" v-else-if="title == '编辑危险区转移安置点信息'">修改 </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs, watch } from "vue";
import MinMap from "@/components/Map/plugins/drawTool";
import { getToken } from '@/utils/auth'
import * as turf from '@turf/turf';
import { getTreeOption, selectTsList, getAdcdTree, selectStlyList } from "@/api/watershed/ads";
import { addFloodPreventionDangerArea, queryFloodPreventionDangerArea, queryFloodPreventionDangerAreaDetail,deleteFloodPreventionDangerArea, updateFloodPreventionDangerArea,
    dangerousAreaDetail,//危险区详情
    placeList, //居民管理列表 ywt
    dangerousAreaList,//危险区信息列表
    queryFloodPreventionMember, //查询防汛成员列表
} from "@/api/warning";
import AdcdTree from "@/components/AdcdTree/index.vue";

const token = getToken()
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
const { proxy } = getCurrentInstance();
const uploadRef = ref('')
const loading = ref(false)
const data = reactive({
    total: 0,
    addTable: [],
    queryForm: {
        hazardousName: "", // 危险区名称
        hazardousGrade: "", // 危险区等级
        basinId: "", // 流域
        pageNum: 1,
        pageSize: 20
    },
    rules: {
        hazardousId: [
            { required: true, message: '请选择危险区名称', trigger: 'blur' },
        ],
        placementId: [
            { required: true, message: '请选择安置点', trigger: 'blur' },
        ],
        // memberId: [
        //     { required: true, message: '请选择转移负责人', trigger: 'blur' },
        // ],

    },
    dialogShow: false,
    title: '新增危险区转移安置点信息',
    addForm: {
        hazardousId:'',
        placementId: '',
        geoType: 'polyline',
        transferMemberDtos: [],
        memberId: [],
        geom: null,
        extraData: [], //危险区面图层和安置点点图层
    },
    tableData: [],
    areaList: [],
    townList: [],
    residentData: [],
    dangerousAreaData: [],
    basinList: [],
    memberData: [],
    detail:'',
});
const { queryForm, dialogShow, addForm, title, rules, total, areaList, addTable, tableData, residentData, dangerousAreaData
    , basinList, memberData,
} = toRefs(data)

onMounted(() => {
    getList()
    getAdcdList()
    //获取所有流域
    getBasinList()
    getDangerList()//获取所有危险区
    getResidentList()//获取所有安置点
    getMember()//获取所有防汛成员
})
const getDangerAreaDetail =  async (id) => {
     await dangerousAreaDetail(id).then(res => {
        if (res.code == 200) {
            data.detail = res.data.geom
        }
    })

}
// 监听 hazardousId 和 placementId 的变化
watch([() => addForm.value.hazardousId, () => addForm.value.placementId], async ([newHazardousId, newPlacementId]) => {
    if (newHazardousId && newPlacementId) {
        // 异步获取数据
        await getDangerAreaDetail(newHazardousId)

        const placement = residentData.value.find(item => item.id === newPlacementId);
        if (data.detail && placement?.lgtd && placement?.lttd) {
            try {
                // 创建危险区的 Feature
                const hazardousFeature = typeof data.detail === 'string' ? JSON.parse(data.detail) : data.detail;
                delete hazardousFeature.features[0].id;
                // 使用 turf.point 创建安置点的 Feature
                const point = turf.point([Number(placement.lgtd), Number(placement.lttd)]);

                hazardousFeature.features.push(point)

                addForm.value.extraData = hazardousFeature;
            } catch (error) {
                console.error('处理地理数据时出错:', error);
                addForm.value.extraData = [];
            }
        } else {
            addForm.value.extraData = [];
        }
    } else {
        addForm.value.extraData = [];
    }
}, { immediate: true });

const getBasinList = async () => {
    await selectStlyList({}).then(response => {
        data.basinList = response.data || []
    })
}
//获取所有危险区
const getDangerList = async () => {
    await dangerousAreaList(queryForm.value).then(response => {
        console.log(response.data.records)
        data.dangerousAreaData = response.data.records || []

    })
}
//获取所有安置点
const getResidentList = async () => {
    await placeList({ pageSize: 99999, pageNum: 1 }).then(res => {
        data.residentData = res.data.records || []
    })
}
//获取所有防汛成员
const getMember = async () => {
    await queryFloodPreventionMember({ pageSize: 99999, pageNum: 1 }).then(res => {
        data.memberData = res.data.records || []
    })
}

const getAdcdList = () => {
    getAdcdTree({
        adcd: ''
    }).then((res) => {

        data.areaList = res.data[0].children || []
    })
}

const submitAdd = () => {
    proxy.$refs.addFormRef.validate(valid => {
        console.log(valid)
        if (!valid) return;
        data.addForm.memberId.forEach((item, index) => {
            data.addForm.transferMemberDtos.push({
                memberId: item,
            })
        })
        delete data.addForm.memberId
        data.addForm.geoType = 'people'
        addFloodPreventionDangerArea(data.addForm).then(res => {
            if (res.code == 200) {
                proxy.$modal.msgSuccess("新增成功");
                data.dialogShow = false
                getList()
                data.addForm.memberId = []
            } else {
                proxy.$modal.msgError(res.msg);
            }
        })
    })
}
const submitEdit = () => {
    proxy.$refs.addFormRef.validate(valid => {
        if (valid) {
            data.addForm.geoType = 'people'
            updateFloodPreventionDangerArea(data.addForm, data.addForm.id).then(res => {
                if (res.code == 200) {
                    proxy.$modal.msgSuccess("修改成功");
                    data.dialogShow = false
                    getList()
                } else {
                    proxy.$modal.msgError(res.msg);
                }
            })
        }
    })
}
const getAllTown = (adcd) => {
    selectTsList({ pageNum: 1, pageSize: 9999, parentAdcd: adcd || '' }).then(res => {
        data.townList = res.rows || []
    })
}

const handleSee = (row) => {
    data.title = '查看危险区转移安置点信息'
    getAllTown(row.townAdcd)
    queryFloodPreventionDangerAreaDetail(row.id).then(res => {
        data.addForm = res.data
        data.addForm.memberId = res.data.transferMemberDtos.map(item => item.memberId)
        data.dialogShow = true
    })

}
const handleEdit = (row) => {
    data.title = '编辑危险区转移安置点信息'
    getAllTown(row.townAdcd)
    queryFloodPreventionDangerAreaDetail(row.id).then(res => {
        data.addForm = res.data
        if(res.data.geom == null) {
            data.addForm.geom = null
        }
        data.addForm.memberId = res.data.transferMemberDtos.map(item => item.memberId)
        data.dialogShow = true
    })

}
const handleDelete = (row) => {
    proxy.$modal.confirm('是否确认删除该条数据?').then(res => {
        deleteFloodPreventionDangerArea(row.id).then(res => {
            if (res.code == 200) {
                proxy.$modal.msgSuccess("删除成功");
                getList()
            } else {
                proxy.$modal.msgError(res.msg);
            }
        })
    })
}
const getList = () => {
    loading.value = true
    queryFloodPreventionDangerArea(data.queryForm).then(res => {
        data.tableData = res.data.records || []
        data.total = res.data.total || 0
        loading.value = false
    })
}
const resetQuery = () => {
    proxy.$refs.queryFormRef.resetFields()
    getList()
}
const addFamily = () => {
    data.title = '新增危险区转移安置点信息'
    data.addForm = {
        hazardousId:'',
        placementId: '',
        geoType: 'polyline',
        transferMemberDtos: [],
        memberId: [],
        geom:null,
        extraData: [],
    }

    data.dialogShow = true
    //初始化表单
    }
const handleChange = (file, fileList) => {
    if (fileList.length > 1) {
        fileList[0] = fileList[1]
        fileList.splice(1, 1);
    }
}
const handleSuccess = (response) => {
    if (response.code == 200) {
        form.value.geom = response.data
    } else {
        uploadRef.value?.clearFiles()
        proxy.$modal.msgError(response.msg);
    }
}
const updateBound = (geojson) => {
    // 提交geojson数据到后台
    console.log(geojson)
    data.addForm.geom = geojson
}
</script>
<style scoped lang="scss">
:deep(.addForm .el-form--inline .el-form-item) {
    margin-right: 0;
    width: 50%;

}

.bth {
    margin-left: 20px;
}

.content {
    display: flex;
    margin-top: 20px;
}

.right-table {
    flex: 1;
    margin-left: 20px;
    height: calc(100vh - 310px);
}
</style>