<template>
  <div class="app-container">
    <el-row>
      <div class="status-tabs">
        <div
          class="status-tab"
          :class="{ 'is-active': queryForm.transferStatus === 1 }"
          @click="handleStatusChange(1)"
        >
          <span class="tab-text">未转移</span>
        </div>
        <div
          class="status-tab"
          :class="{ 'is-active': queryForm.transferStatus === 2 }"
          @click="handleStatusChange(2)"
        >
          <span class="tab-text">准备转移</span>
        </div>
        <div
          class="status-tab"
          :class="{ 'is-active': queryForm.transferStatus === 3 }"
          @click="handleStatusChange(3)"
        >
          <span class="tab-text">立即转移</span>
        </div>
      </div>
      <el-form
        ref="queryFormRef"
        :model="queryForm"
        inline
        class="form-container"
      >
        <el-form-item label="村社" prop="villageName">
          <el-input
            v-model="queryForm.villageName"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="防治区类型" prop="preventionZonesType">
          <el-select
            v-model="queryForm.preventionZonesType"
            placeholder="请选择"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in PREVENTION_ZONES_TYPES"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="最近24小时自动预警情况"
          prop="autoWarningStatus"
        >
          <el-select
            v-model="queryForm.autoWarningStatus"
            placeholder="请选择"
            clearable
          >
            <el-option label="准备转移" :value="1" />
            <el-option label="立即转移" :value="2" />
            <el-option label="可能发生" :value="3" />
            <el-option label="可能性较大" :value="4" />
            <el-option label="可能性大" :value="5" />
            <el-option label="可能性很大" :value="6" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="最近24小时内部预警"
          prop="internalWarningStatus"
        >
          <el-select
            v-model="queryForm.internalWarningStatus"
            placeholder="请选择"
            clearable
          >
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="最近24小时外部预警"
          prop="externalWarningStatus"
        >
          <el-select
            v-model="queryForm.externalWarningStatus"
            placeholder="请选择"
            clearable
          >
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item class="form-item-button">
          <el-button type="primary" @click="getList" icon="Search"
            >查询</el-button
          >
          <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <div class="content">
      <AdcdTree v-model="queryForm.adcd" @getList="getList" />
      <div class="right-table">
        <div class="batch-operations">
          <!-- 未转移状态的批量操作按钮 -->
          <template v-if="queryForm.transferStatus === 1">
            <el-button
              type="primary"
              :disabled="!selectedRows.length"
              @click="handleBatchReady"
              >批量准备转移</el-button
            >
            <el-button
              type="primary"
              :disabled="!selectedRows.length"
              @click="handleBatchImmediate"
              >批量立即转移</el-button
            >
          </template>

          <!-- 准备转移状态的批量操作按钮 -->
          <template v-if="queryForm.transferStatus === 2">
            <el-button
              type="primary"
              :disabled="!selectedRows.length"
              @click="handleBatchImmediate"
              >批量立即转移</el-button
            >
            <el-button
              type="warning"
              :disabled="!selectedRows.length"
              @click="handleBatchEndTransfer"
              >批量结束转移</el-button
            >
          </template>

          <!-- 立即转移状态的批量操作按钮 -->
          <template v-if="queryForm.transferStatus === 3">
            <el-button type="primary" @click="handleBatchInputNumber"
              >导入转移人数</el-button
            >
            <el-button
              type="warning"
              :disabled="!selectedRows.length"
              @click="handleBatchEndTransfer"
              >批量结束转移</el-button
            >
          </template>
        </div>

        <el-table
          :data="tableData"
          :style="{ height: '100%', width: '100%' }"
          v-loading="loading"
          row-key="id"
          stripe
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" />
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column
            prop="villageName"
            label="村社"
            width="120"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="preventionZonesType"
            label="防治区类型"
            width="100"
            align="center"
          >
            <template #default="scope">
              {{
                getTypeLabel(
                  scope.row.preventionZonesType,
                  "PREVENTION_ZONES_TYPES"
                )
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="floodControlCapacity"
            label="防洪能力（年）"
            width="120"
            align="center"
          />
          <el-table-column
            prop="oneHouseholds"
            label="最近24小时内"
            align="center"
          >
            <el-table-column
              prop="autoWarnList"
              label="自动预警情况"
              width="200"
              align="center"
            >
            </el-table-column>
            <el-table-column prop="internalWarnStatus" label="内部预警">
              <template #default="scope">
                {{ scope.row.internalWarnStatus === 1 ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column prop="externalWarnStatus" label="外部预警">
              <template #default="scope">
                {{ scope.row.externalWarnStatus === 1 ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="placementName"
              label="安置点"
              width="200"
              align="center"
            />
            <el-table-column
              v-if="queryForm.transferStatus === 2"
              prop="createTime"
              label="准备转移时间"
              width="200"
              align="center"
            />
            <template v-if="queryForm.transferStatus === 3">
              <el-table-column
                prop="nowTransferTime"
                label="转移开始时间"
                width="200"
                align="center"
              />
              <el-table-column prop="hazardousPeoNum" label="应转移（人）" />
              <el-table-column prop="transferPeoNum" label="已转移（人）" />
              <el-table-column
                prop="updateTime"
                label="最近更新时间"
                width="200"
                align="center"
              />
            </template>
          </el-table-column>

          <el-table-column
            prop="address"
            label="操作"
            width="350"
            align="center"
          >
            <template #default="scope">
              <!-- 未转移状态 -->
              <template v-if="queryForm.transferStatus === 1">
                <el-link
                  type="primary"
                  size="mini"
                  @click="handle(scope.row, 'readyTransfer')"
                  style="margin-right: 10px"
                  >准备转移</el-link
                >
                <el-link
                  type="primary"
                  size="mini"
                  @click="handle(scope.row, 'rightNowTransfer')"
                  style="margin-right: 10px"
                  >立即转移</el-link
                >
              </template>

              <!-- 准备转移状态 -->
              <template v-if="queryForm.transferStatus === 2">
                <el-link
                  type="primary"
                  size="mini"
                  @click="handle(scope.row, 'rightNowTransfer')"
                  style="margin-right: 10px"
                  >立即转移</el-link
                >
                <el-link
                  type="warning"
                  size="mini"
                  @click="handle(scope.row, 'endTransfer')"
                  style="margin-right: 10px"
                  >结束转移</el-link
                >
                <el-link
                  type="info"
                  size="mini"
                  @click="handleViewNotice(scope.row)"
                  style="margin-right: 10px"
                  >查看通知文件</el-link
                >
              </template>

              <!-- 立即转移状态 -->
              <template v-if="queryForm.transferStatus === 3">
                <el-link
                  type="primary"
                  size="mini"
                  @click="handleInputNumber(scope.row)"
                  style="margin-right: 10px"
                  >填写转移人数</el-link
                >
                <el-link
                  type="warning"
                  size="mini"
                  @click="handle(scope.row, 'endTransfer')"
                  style="margin-right: 10px"
                  >结束转移</el-link
                >
                <el-link
                  type="info"
                  size="mini"
                  @click="handleViewNotice(scope.row)"
                  style="margin-right: 10px"
                  >查看通知文件</el-link
                >
              </template>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <el-dialog v-model="numberDialogShow" title="填写转移人数" width="500px">
      <el-form
        ref="numberFormRef"
        :model="numberForm"
        :rules="numberRules"
        label-width="260px"
      >
        <el-form-item
          label="截止当前时间累计转移人数（人）"
          prop="transferPeoNum"
        >
          <el-input-number
            v-model="numberForm.transferPeoNum"
            :min="0"
            controls-position="right"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="全部应转移人数" prop="villagePeoNum">
          {{ numberForm.villagePeoNum }}
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitNumber">保 存</el-button>
          <el-button @click="numberDialogShow = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      v-model="viewNoticeDialogShow"
      title="查看通知文件"
      width="500px"
    >
      <div class="notice-files">
        <!-- <div v-for="file in noticeFiles" :key="file.id" class="file-item"> -->
        <!-- <span>{{ file.name }}</span> -->
        <el-link type="primary" @click="downloadFile(viewData.fileUrl)">{{
          viewData.fileUrl
        }}</el-link>
        <!-- </div> -->
      </div>
    </el-dialog>
    <el-dialog
      v-model="TransferDialogShow"
      :title="
        addForm.transferStatus === 2
          ? '编辑准备转移信息'
          : addForm.transferStatus === 3
          ? '编辑立即转移信息'
          : '编辑结束转移信息'
      "
      width="500px"
    >
      <el-form
        ref="endTransferFormRef"
        :model="endTransferForm"
        :rules="endTransferRules"
        label-width="140px"
        label-position="top"
      >
        <el-form-item
          v-if="
            addForm.transferStatus !== 1 &&
            addForm.transferStatus !== 2 &&
            !!addForm.transferStatus
          "
          :label="
            addForm.transferStatus !== 4 ? '转移截止时间' : '结束转移时间'
          "
          prop="endTransferTime"
        >
          <el-date-picker
            v-model="addForm.endTransferTime"
            type="datetime"
            :placeholder="
              addForm.transferStatus !== 4
                ? '选择转移截止时间'
                : '选择结束转移时间'
            "
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :shortcuts="shortcuts"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item
          :label="
            addForm.transferStatus !== 4
              ? '上传转移通知书文件'
              : '上传结束转移通知书文件'
          "
          prop="files"
        >
          <el-upload
            class="upload-demo"
            ref="endUploadNoticeRef"
            drag
            :action="uploadParams.action"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="handleBeforeUpload"
            :data="uploadParams.data"
            :headers="uploadParams.headers"
            :before-remove="handleBeforeRemove"
            :limit="1"
            style="width: 100%"
          >
            <div class="el-upload__text">
              请上传通知书文件<br />
              支持 Word、PDF 格式，单个文件不超过20MB<br />
              拖拽上传 或 <em>点击上传</em>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="confirmUpload">保 存</el-button>
          <el-button
            @click="
              () => {
                TransferDialogShow = false;
                endUploadNoticeRef.clearFiles(); // 清空上传的文件列表
              }
            "
            >取 消</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showImportTransferDialog"
      title="导入转移人数"
      width="500px"
      @close="handleCancelImport"
    >
      <div class="import-content">
        <div class="template-download">
          <span>下载模板：</span>
          <el-link type="primary" @click="downloadTemplate"
            >村社预警响应人数导入模板.xlsx</el-link
          >
        </div>
        <el-upload
          class="upload-demo"
          ref="importUploadRef"
          drag
          :auto-upload="false"
          :action="uploadImportUrl"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          :before-upload="handleBeforeImportUpload"
          :on-change="handleFileChange"
          :headers="uploadParams.headers"
          :limit="1"
          accept=".xlsx,.xls"
          :file-list="importFileList"
        >
          <div class="el-upload__text">
            请选择导入文件<br />
            支持 Excel 格式（.xlsx, .xls），单个文件不超过10MB<br />
            拖拽选择 或 <em>点击选择</em>
          </div>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancelImport">取消</el-button>
          <el-button type="primary" @click="submitImport">导入</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from "vue";
import { PREVENTION_ZONES_TYPES, getTypeLabel } from "@/enums/warning";
import { getToken } from "@/utils/auth";
import {
  updateWarningResponse,
  updateTransferNum,
  queryWarningResponse,
  downloadByMain, //下载文件
  importTransferNum, // 转移人数导入
} from "@/api/warning";
import { deepClone } from "@/utils";
import AdcdTree from "@/components/AdcdTree/index.vue";

defineOptions({
  name: "DangerAreaWarning",
});

const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload");
const uploadImportUrl = ref(
  import.meta.env.VITE_APP_BASE_API +
    "/system/hazardous/warn-feedback/excel/peo-num"
);

const { proxy } = getCurrentInstance();

const loading = ref(false);
const data = reactive({
  queryForm: {
    villageName: "",
    preventionZonesType: "",
    pageNum: 1,
    pageSize: 20,
    autoWarningStatus: "",
    internalWarningStatus: "",
    externalWarningStatus: "",
    adcd: "",
    transferStatus: 1,
  },
  uploadParams: {
    action: uploadFileUrl,
    data: {
      bucketName: "watershed",
    },
    headers: {
      Authorization: "Bearer " + getToken(),
    },
  },
  TransferDialogShow: false,
  rules: {
    name: [{ required: true, message: "请输入危险区名称", trigger: "blur" }],
    code: [
      { required: true, message: "请输入危险区代码", trigger: "blur" },
      {
        required: true,
        message: "危险区代码为18位字符",
        min: 18,
        trigger: "blur",
      },
    ],
    townAdcd: [{ required: true, message: "请选择乡镇", trigger: "blur" }],
    areaAdcd: [{ required: true, message: "请选择区县", trigger: "blur" }],
    // villageAdcd: [
    //     { required: true, message: '请选择所属村社', trigger: 'blur' },
    // ],
    preventionZonesType: [
      { required: true, message: "请选择流域", trigger: "blur" },
    ],
    grade: [{ required: true, message: "请选择等级", trigger: "blur" }],
  },
  dialogShow: false,
  title: "新增危险区信息",
  tableData: [],
  townList: [],
  villageData: [],
  total: 0,
  uploadDialogShow: ref(false),
  uploadNoticeRef: ref(),
  uploadNoticeUrl: import.meta.env.VITE_APP_BASE_API + "/upload/notice",
  currentUploadRow: ref(null),
  transferForm: {
    deadline: "",
    files: [],
    id: null,
  },
  transferRules: {
    deadline: [
      { required: true, message: "请选择转移截至时间", trigger: "change" },
    ],
    files: [
      { required: true, message: "请上传转移通知书文件", trigger: "change" },
    ],
  },
  shortcuts: [
    {
      text: "今天",
      value: new Date(),
    },
    {
      text: "明天",
      value: () => {
        const date = new Date();
        date.setTime(date.getTime() + 3600 * 1000 * 24);
        return date;
      },
    },
    {
      text: "一周后",
      value: () => {
        const date = new Date();
        date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
        return date;
      },
    },
  ],
  numberDialogShow: false,
  numberForm: {
    transferPeoNum: 0,
  },
  numberRules: {
    transferPeoNum: [
      { required: true, message: "请输入累计转移人数", trigger: "blur" },
    ],
  },
  viewNoticeDialogShow: false,
  noticeFiles: [],
  dangerAreaDetail: {},
  endTransferDialogShow: false,
  endTransferForm: {
    id: null,
    endTime: "",
    files: [],
  },
  endTransferRules: {
    endTime: [
      { required: true, message: "请选择结束转移时间", trigger: "change" },
    ],
    files: [
      {
        required: true,
        message: "请上传结束转移通知书文件",
        trigger: "change",
      },
    ],
  },
  endUploadNoticeRef: ref(),
  selectedRows: [],
  addForm: {
    fileUrl: "",
    id: "",
    idList: [],
    transferStatus: 1,
    endTransferTime: "",
  },
  viewData: {
    id: "",
    idList: [],
    fileUrl: "",
    transferPeoNum: "", //累计转移人数
    hazardousPeoNum: "", //危险区人数
  },
  showImportTransferDialog: false, // 导入转移人数对话框
  importFileList: ref([]),
});
const {
  queryForm,
  tableData,
  total,
  uploadDialogShow,
  uploadNoticeRef,
  uploadNoticeUrl,
  currentUploadRow,
  transferDialogShow,
  transferForm,
  transferRules,
  shortcuts,
  numberDialogShow,
  numberForm,
  numberRules,
  viewNoticeDialogShow,
  noticeFiles,
  dangerAreaDetail,
  endTransferDialogShow,
  endTransferForm,
  endTransferRules,
  endUploadNoticeRef,
  selectedRows,
  uploadParams,
  addForm,
  TransferDialogShow,
  viewData,
  showImportTransferDialog,
  importFileList,
} = toRefs(data);

const transformDataForTreeSelect = (data) => {
  // 递归地转换数据以匹配 el-tree-select 的需求
  return data.map((item) => ({
    label: item.data.name, // 使用 'name' 属性作为标签
    value: item.data.preventionZonesType, // 使用 'preventionZonesType' 属性作为值
    children: item.children ? transformDataForTreeSelect(item.children) : [], // 递归转换子节点
  }));
};
const handle = (row, type) => {
  switch (type) {
    case "readyTransfer":
      data.TransferDialogShow = true;
      addForm.value = {
        transferStatus: 2,
        list: [
          {
            ...row,
            transferStatus: 2,
          },
        ],
      };
      break;
    case "rightNowTransfer":
      data.TransferDialogShow = true;

      addForm.value = {
        transferStatus: 3,
        list: [
          {
            ...row,
            transferStatus: 3,
          },
        ],
      };
      break;
    case "endTransfer":
      data.TransferDialogShow = true;
      addForm.value = {
        transferStatus: 4,
        list: [
          {
            ...row,
            transferStatus: 4,
          },
        ],
      };
      break;
  }
  // console.log(row);
};

const getList = () => {
  loading.value = true;
  queryWarningResponse(queryForm.value)
    .then((response) => {
      data.tableData = response.data.records || [];
      data.total = response.data.total || 0;
    })
    .finally(() => {
      loading.value = false;
    });
};

const resetQuery = () => {
  data.queryForm.paramAdcd = "";
  proxy.$refs.queryFormRef.resetFields();
  getList();
};

const confirmUpload = () => {
  if (!data.addForm.fileUrl) {
    proxy.$modal.msgError("请上传转移通知书文件");
    return;
  }
  const list = deepClone(data.addForm.list) || [];
  list.forEach((item) => {
    item.fileUrl = data.addForm.fileUrl;
  });
  updateWarningResponse(list).then((res) => {
    if (res.code === 200) {
      proxy.$modal.msgSuccess("操作成功");

      data.TransferDialogShow = false;
      if (addForm.value.transferStatus === 2) {
        handleStatusChange(2);
      } else if (addForm.value.transferStatus === 3) {
        handleStatusChange(3);
      } else {
        getList();
      }
      endUploadNoticeRef.value.clearFiles(); // 清空上传的文件列表
      data.addForm = {
        transferStatus: 1,
      };
    } else {
      proxy.$modal.msgError(res.msg || "操作失败");
    }
  });
};

const handleStatusChange = (transferStatus) => {
  data.queryForm.transferStatus = transferStatus;
  getList(); // 切换状态后重新获取列表数据
};

const handleViewNotice = (row) => {
  data.viewNoticeDialogShow = true;
  data.viewData = {
    ...data.viewData,
    ...row,
  };
  // TODO: 调用获取通知文件列表接口
  // getNoticeFiles(row.id).then(res => {
  //     if (res.code === 200) {
  //         data.noticeFiles = res.data
  //     }
  // })
};

const downloadFile = (file) => {
  // TODO: 实现文件下载逻辑
  try {
    // TODO: 调用文件下载API
    // 使用下载文件的函数
    downloadByMain({ fileName: file, bucketName: "watershed" });
  } catch (error) {
    console.error("文件下载失败:", error);
    proxy.$modal.msgError("文件下载失败");
  }
};

const handleInputNumber = (row) => {
  data.numberDialogShow = true;
  data.numberForm = deepClone(row);
};

const submitNumber = () => {
  proxy.$refs["numberFormRef"].validate((valid) => {
    if (valid) {
      updateTransferNum(data.numberForm).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess("操作成功");
          data.numberDialogShow = false;
          getList(); // 刷新列表数据
          proxy.$refs["numberFormRef"].resetFields(); // 重置表单
          data.numberForm = {
            transferPeoNum: "",
            villagePeoNum: "",
          };
        } else {
          proxy.$modal.msgError(res.msg || "操作失败");
        }
      });
    }
  });
};

const handleSelectionChange = (selection) => {
  data.selectedRows = selection;
};

const handleBatchReady = () => {
  data.addForm = {
    transferStatus: 2,
    list: data.selectedRows.map((row) => {
      return {
        ...row,
        transferStatus: 2,
      };
    }),
  };
  data.TransferDialogShow = true;
};

const handleBatchImmediate = () => {
  data.addForm = {
    transferStatus: 3,
    list: data.selectedRows.map((row) => {
      return {
        ...row,
        transferStatus: 3,
      };
    }),
  };
  data.TransferDialogShow = true;
};

const handleBatchEndTransfer = () => {
  data.addForm = {
    transferStatus: 4,
    list: data.selectedRows.map((row) => {
      return {
        ...row,
        transferStatus: 4,
      };
    }),
  };
  data.TransferDialogShow = true;
};

// 导入转移人数
const importUploadRef = ref(null);
const handleBatchInputNumber = () => {
  showImportTransferDialog.value = true;
};

// 提交导入
const submitImport = () => {
  if (importFileList.value.length === 0) {
    proxy.$modal.msgError("请先选择要导入的文件");
    return;
  }

  // 准备表单数据
  const formData = new FormData();
  formData.append("file", importFileList.value[0].raw);

  // 直接调用API上传
  importTransferNum(formData).then((res) => {
    if (res.code === 200) {
      proxy.$modal.msgSuccess("导入成功");
      showImportTransferDialog.value = false;
      importFileList.value = [];
      getList(); // 刷新数据
    }
  });
};

const handleImportSuccess = (response) => {
  if (response.code === 200) {
    proxy.$modal.msgSuccess("导入成功");
    showImportTransferDialog.value = false;
    getList(); // 刷新数据
  } else {
    proxy.$modal.msgError(response.msg || "导入失败");
  }
};

const handleImportError = () => {
  proxy.$modal.msgError("文件上传失败");
};

const handleUploadSuccess = (response, file) => {
  data.addForm.fileUrl = response.msg;
  proxy.$modal.msgSuccess("文件上传成功");
};

const handleUploadError = () => {
  proxy.$modal.msgError("文件上传失败");
};

// 移除文件之前的钩子
const handleBeforeRemove = (file, fileList) => {
  console.log("移除文件：", file, fileList);
  data.addForm.fileUrl = "";
};

// 文件上传相关方法
const handleBeforeUpload = (file) => {
  const isValidType = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ].includes(file.type);
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isValidType) {
    proxy.$modal.msgError("只能上传PDF或Word文档！");
    return false;
  }
  if (!isLt20M) {
    proxy.$modal.msgError("文件大小不能超过20MB！");
    return false;
  }
  return true;
};

const downloadTemplate = () => {
  // 下载模板文件
  downloadByMain({
    fileName:
      "711c1fb4-6b6c-4fc2-835e-a8d28f467243_村社预警响应人数导入模板.xlsx",
    bucketName: "watershed",
  });
};

const handleBeforeImportUpload = (file) => {
  // 验证文件类型和大小
  const isExcel = /\.(xlsx|xls)$/.test(file.name);
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isExcel) {
    proxy.$modal.msgError("只能上传Excel文件！");
    return false;
  }
  if (!isLt10M) {
    proxy.$modal.msgError("文件大小不能超过10MB！");
    return false;
  }
  return true;
};

// 文件变化处理
const handleFileChange = (file, fileList) => {
  importFileList.value = fileList;
};

// 取消导入操作
const handleCancelImport = () => {
  showImportTransferDialog.value = false;
  importFileList.value = [];
  if (importUploadRef.value) {
    importUploadRef.value.clearFiles();
  }
};

onMounted(() => {
  getList();
});
</script>
<style scoped lang="scss">
:deep(.el-form--inline .el-form-item) {
  margin-right: 0;
}

.right-table {
  margin-left: 20px;
}

.notice-files {
  max-height: 300px;
  overflow-y: auto;

  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }
  }
}

.batch-operations {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
}

.import-content {
  padding: 10px 0;

  .template-download {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    span {
      margin-right: 10px;
    }
  }

  .el-upload {
    width: 100%;
  }
}

.status-tabs {
  display: flex;
  width: 100%;
  background: #f6f8fa;
  border-radius: 6px;
  padding: 4px;
  margin-bottom: 4px;
  position: relative;

  .status-tab {
    flex: 1;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    color: #606266;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px;
    margin: 0 2px;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    &:hover:not(.is-active) {
      color: #409eff;
      background: rgba(64, 158, 255, 0.04);
    }

    &.is-active {
      color: #409eff;
      background: #fff;
      font-weight: 600;
    }

    .tab-text {
      position: relative;
      z-index: 1;
      padding: 0 8px;
      white-space: nowrap;
    }
  }
}
</style>
