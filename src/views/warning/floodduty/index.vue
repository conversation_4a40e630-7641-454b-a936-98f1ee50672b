<template>
    <div class="app-container">
        <el-form ref="queryFormRef" :model="queryForm" inline class="form-container">
            <el-form-item label="防汛责任名称" prop="name">
                <el-input v-model="queryForm.name" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryForm.status" placeholder="请选择">
                    <el-option label="全部" value="" />
                    <el-option label="正常" :value="0" />
                    <el-option label="停用" :value="1" />
                </el-select>
            </el-form-item>

            <el-form-item class="form-item-button">
                <el-button type="primary" @click="getList" icon="Search">查询</el-button>
                <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
            </el-form-item>
        </el-form>

        <div class="content">
            <div class="left-tree" v-if="false">
                <div class="head-container">
                    <el-input v-model="filterText" placeholder="请输入行政区名称" clearable prefix-icon="Search"
                        style="margin-bottom: 20px" />
                </div>
                <div class="head-container">
                    <el-tree-v2 :data="adcdList" :props="propsTree" :height="600" :filter-method="filterNode"
                        ref="treeRef" @node-click="handleNodeClick" highlight-current />
                </div>
            </div>
            <div class="right-table">
                <div class="table-header">
                    <el-button type="success" icon="CirclePlus" @click="add">新增</el-button>
                </div>
                <el-table :data="tableData" stripe
                    v-loading="loading">
                    <el-table-column type="index" label="序号" width="80" align="center"/>
                    <el-table-column prop="name" label="防汛责任名称" />
                    <el-table-column prop="responsibilityType" label="负责对象类型" >
                        <template #default="scope">
                           <span>
                            {{ scope.row.responsibilityType == 1 ? '行政区'
                            : scope.row.responsibilityType == 2 ?  '危险区'
                            : '水库'
                            }}
                           </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="responsibility" label="职责" />

                    <el-table-column prop="status" label="状态">
                        <template #default="scope">
                            <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">{{ scope.row.status === 1 ?
                                '停用' : '正常' }}</el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column prop="address" label="操作" width="210" align="center">
                        <template #default="scope">
                            <el-button type="primary" link icon="View" @click="handleSee(scope.row)"
                                >查看</el-button>
                            <el-button type="primary" link icon="Edit" @click="handleEdit(scope.row)"
                                >编辑</el-button>
                            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
                    @pagination="getList" />
            </div>
        </div>
        <el-dialog v-model="dialogShow" :title="title" style="width: 40%;">
            <el-form ref="addFormRef" :model="addForm" label-width="auto" inline :rules="rules"
                :disabled="title.includes('查看防汛组织信息')" style="display: flex;flex-wrap: wrap;">
                <el-form-item label="防汛责任名称" prop="name" >
                    <el-input v-model="addForm.name" placeholder="请输入" style="width: 180px;"
                    :formatter="(value) => `${value}`.replace(/[^a-zA-Z\d\u4e00-\u9fa5]*/g, '')"
                    maxlength="50"
                    ></el-input>
                </el-form-item>
                <el-form-item label="负责对象类型" prop="responsibilityType">
                    <el-select v-model="addForm.responsibilityType" placeholder="请选择" style="width: 180px;" clearable
                        >
                        <el-option :label="item.name" :value="item.value" highlight-current v-for="item in areaList"
                            :key="item.value" />

                    </el-select>
                </el-form-item>

                <el-form-item label="职责" prop="responsibility" style="width: 100%;">
                    <el-input v-model="addForm.responsibility" placeholder="请输入" style="width: 90%;" type="textarea"
                        :row="10" maxlength="200" :show-word-limit="true"></el-input>
                </el-form-item>
                <el-form-item label="发布范围" prop="releaseScope">
                    <el-select v-model="addForm.releaseScope" placeholder="请选择" style="width: 180px;" clearable
                        >
                        <el-option label="内部" :value="1" highlight-current
                             />
                        <el-option label="外部" :value="2" highlight-current
                             />

                    </el-select>
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input v-model="addForm.sort" placeholder="请输入" style="width: 180px;"
                        :formatter="(value) =>  `${value}`.replace(/[^0-9.]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="addForm.status">
                        <el-radio :label="0">正常</el-radio>
                        <el-radio :label="1">停用</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer v-if="!title.includes('查看防汛组织信息')">
                <span class="dialog-footer">
                    <el-button type="primary" @click="submitAdd" v-if="title.includes('新增防汛责任信息')">保 存</el-button>
                    <el-button type="primary" @click="submitEdit" v-else-if="title.includes('编辑防汛责任信息')">保 存</el-button>
                    <el-button @click="dialogShow = false">取消</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs, watch } from "vue";
// import MinMap from "@/components/Map/plugins/drawTool";
import { getToken } from '@/utils/auth'
import { getTreeOption } from "@/api/watershed/ads";
import { addFloodPreventionResponsibility, queryFloodPreventionResponsibility, deleteFloodPreventionResponsibility, updateFloodPreventionResponsibility } from "../../../api/warning";

defineOptions({
  name: 'FloodDuty'
})

const token = getToken()
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/hydro/convert/shp-kml/'
const { proxy } = getCurrentInstance();
const uploadRef = ref('')
const loading = ref(false)
const data = reactive({
    total: 0,
    queryForm: {
        // stnm: "",
        name: "",
        // townAdcd: '',
        status: "",
        pageNum: 1,
        pageSize: 20
    },
    rules: {
        name: [
            { required: true, message: '请输入防汛责任名称', trigger: 'blur' },
        ],
        responsibilityType: [
            { required: true, message: '请选择负责对象类型', trigger: 'blur' },
        ],
        releaseScope: [
            { required: true, message: '请选择发布范围', trigger: 'blur' },
        ],

    },
    dialogShow: false,
    title: '新增防汛组织信息',
    addForm: {
        id: '',
        name: "",
        responsibilityType: '',
        responsibility: '',
        releaseScope: 1,
        sort: '',
        status: 0,
    },
    propsTree: {
        value: 'adcd',
        label: 'adnm',
        children: 'children',
    },
    tableData: [],
    adcdList: [],
    areaList: [
        {
            name:"行政区",
            value: 1
        },
        {
            name:"危险区",
            value: 2
        },
        {
            name:"水库",
            value: 3
        },
    ],
    townList: [],
    filterText: '',
});
const { queryForm, propsTree, adcdList, dialogShow, addForm, title, rules, total, areaList, townList, tableData, filterText } = toRefs(data)
const filterNode = (query, node) => {
    return node.adnm.includes(query)
}
onMounted(() => {
    getList()
    getTreeList()
    // getAdcdList()
})
// watch(filterText, (val) => {
//     proxy.$refs.treeRef.filter(val)
// })
watch(filterText, (val) => {
  if(val) {
    proxy.$refs.treeRef.filter(val)
  } else {
    proxy.$refs.treeRef.filter('')
    // Collapse all expanded nodes when filter is cleared
    proxy.$refs.treeRef?.setExpandedKeys([])
  }
})
// const getAdcdList = () => {
//     getAdcdTree({
//         adcd: ''
//     }).then((res) => {

//         data.areaList = res.data[0].children || []
//     })
// }
const getTreeList = () => {
    getTreeOption(5).then((res) => {

        data.adcdList = formatTree(res.data)
    })
}
const submitAdd = () => {
    proxy.$refs.addFormRef.validate(valid => {
        if (!valid) return;
        addFloodPreventionResponsibility(data.addForm).then(res => {
            if (res.code == 200) {
                proxy.$modal.msgSuccess("新增成功");
                data.dialogShow = false
                getList()
            } else {
                proxy.$modal.msgError(res.msg);
            }
        })
    })
}
const submitEdit = () => {
    proxy.$refs.addFormRef.validate(valid => {
        if (valid) {
            updateFloodPreventionResponsibility(data.addForm, data.addForm.id).then(res => {
                if (res.code == 200) {
                    proxy.$modal.msgSuccess("修改成功");
                    data.dialogShow = false
                    getList()
                } else {
                    proxy.$modal.msgError(res.msg);
                }
            })
        }
    })
}
// const addFormAreaChange = (e) => {
//     getAllTown(e)
//     data.addForm.townCode = ''
//     data.townList = []

// }
// const getAllTown = (adcd) => {
//     selectTsList({ pageNum: 1, pageSize: 9999, parentAdcd: adcd || '' }).then(res => {
//         data.townList = res.rows || []
//     })
// }
const formatTree = (data) => {
    let dataList = data.map(item => {
        return {
            ...item.data,
            children: item.children && formatTree(item.children)
        }
    })
    //dataList中有children为空的节点 删除
    let dataList2 = dataList.map(item => {
        if (item.children.length === 0) {
            delete item.children
        }
        return {
            ...item
        }
    })
    return dataList2
};

const handleSee = (row) => {
    data.title = '查看防汛组织信息 - ' + row.name
    // getAllTown(row.townAdcd)
    let middle = JSON.parse(JSON.stringify(row))
    data.dialogShow = true
    proxy.$nextTick(() => {
       for(let key in data.addForm) {
        for(let key2 in middle) {
            if(key === key2) {
                data.addForm[key] = middle[key2]
            }
        }
       }

    })
}
const handleEdit = (row) => {
    data.title = '编辑防汛责任信息 - ' + row.name
    // getAllTown(row.townAdcd)
    let middle = JSON.parse(JSON.stringify(row))
    data.dialogShow = true
    proxy.$nextTick(() => {
       for(let key in data.addForm) {
        for(let key2 in middle) {
            if(key === key2) {
                data.addForm[key] = middle[key2]
            }
        }
       }

    })
}
const handleDelete = (row) => {
    proxy.$modal.confirm('是否确认删除该条数据?').then(res => {
        deleteFloodPreventionResponsibility(row.id).then(res => {
            if (res.code == 200) {
                proxy.$modal.msgSuccess("删除成功");
                getList()
            } else {
                proxy.$modal.msgError(res.msg);
            }
        })
    })
}
const getList = () => {
    loading.value = true
    queryFloodPreventionResponsibility(data.queryForm).then(res => {
        data.tableData = res.data.records || []
        data.total = res.data.total || 0
        loading.value = false
    })
}
const resetQuery = () => {
    proxy.$refs.queryFormRef.resetFields()
    getList()
}
const add = () => {
    data.title = '新增防汛责任信息'
    data.dialogShow = true
    data.addForm =  {
        id: '',
        name: "",
        responsibilityType: '',
        responsibility: '',
        releaseScope: 1,
        sort: '',
        status: 0,
    }
}

function handleNodeClick(el) {
    data.queryForm.townAdcd = el.adcd;
    getList()
    //   handleQuery();
};
</script>
<style scoped lang="scss">
.left-tree {
    width: 300px;
    display: block;
    margin-right: 20px;
}
</style>