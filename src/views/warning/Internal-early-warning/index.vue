<template>
  <div class="app-container">
    <el-form
      ref="queryFormRef"
      :model="queryForm"
      inline
      class="form-container"
    >
      <el-form-item label="发布人" prop="userId">
        <el-input
          v-model="queryForm.userId"
          placeholder="请输入发布人姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="发布时间" prop="timeRange">
        <el-date-picker
          v-model="queryForm.timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="defaultTime"
        />
      </el-form-item>
      <el-form-item label="发布状态" prop="status">
        <el-select
          v-model="queryForm.status"
          placeholder="请选择"
        >
          <el-option label="全部" value="" />
          <el-option label="发送中" :value="2" />
          <el-option label="发送成功" :value="1" />
          <el-option label="发送失败" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList" icon="Search">查询</el-button>
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="content">
      <div class="right-table">
        <div class="table-header">
          <el-button type="success" icon="CirclePlus" @click="openWarningDialog"
            >新增</el-button>
        </div>
        <el-table
          :data="tableData"
          stripe
          v-loading="loading"
        >
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="createByName" label="发布人" />
          <el-table-column prop="createTime" label="发布时间" />
          <el-table-column prop="status" label="发布状态">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="memberNameList"
            label="发送成员"
            show-overflow-tooltip
          />
          <el-table-column
            prop="content"
            label="预警内容"
            show-overflow-tooltip
          />
          <el-table-column
            prop="address"
            label="操作"
            width="150"
            align="center"
            fixed="right"
          >
            <template #default="scope">
              <el-button link type="primary" icon="View" @click="handleSee(scope.row)"
                >查看</el-button>
              <!-- <el-link
                type="primary"
                size="mini"
                @click="handleAgain(scope.row)"
                style="margin-right: 10px"
                >再次发送</el-link
              > -->
              <!-- <el-link
                type="danger"
                size="mini"
                @click="handleDelete(scope.row)"
                >删除</el-link
              > -->
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryForm.pageNum"
          v-model:limit="queryForm.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 新增内部预警弹窗 -->
    <el-dialog v-model="warningDialogShow" title="新增内部预警" width="80%" class="warning-dialog" :close-on-click-modal="false">
      <div class="station-form-container">
        <!-- 左侧预警内容 -->
        <div class="station-title-bar">
          <h3 class="station-subtitle">预警基本信息</h3>
        </div>
        <div class="warning-content">
          <div class="warning-left">
            <el-form
              ref="warningFormRef"
              :model="warningForm"
              :rules="warningRules"
              label-width="150px"
            >
              <el-form-item label="预警内容" prop="content">
                <el-input
                  v-model="warningForm.content"
                  type="textarea"
                  :rows="5"
                  placeholder="请输入预警内容（不超过500字）"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
              <!-- <el-form-item label="关联预警" prop="relatedWarning"> -->
                <!-- <el-input v-model="warningForm.relatedWarning" placeholder="请输入关联预警信息" style="width: 100%;">
                                  <template #append>
                                      <el-button @click="handleSelectWarning">选择</el-button>
                                  </template>
                              </el-input> -->
              <!-- </el-form-item> -->
              <el-form-item
                label="上传预警响应文件"
                prop="file"
              >
                <el-upload
                  :action="uploadParams.action"
                  :on-success="handleUploadSuccess"
                  :on-error="handleUploadError"
                  :before-upload="handleBeforeUpload"
                  :data="uploadParams.data"
                  :headers="uploadParams.headers"
                  :before-remove="handleBeforeRemove"
                  ref="fileUpload"
                  :limit="1"
                >
                  <el-button type="primary">上传文件</el-button>
                  <template #tip>
                <div class="el-upload__tip">
                  请上传doc/pdf/docx文件类型,文件大小不超过20M
                </div>
              </template>
                </el-upload>
              </el-form-item>
            </el-form>
          </div>

          <!-- 右侧发送成员 -->
          <div class="warning-right">
            <div class="station-title-bar">
              <h3 class="station-subtitle">发送成员列表</h3>
            </div>
            <el-table
              :data="selectedMembers"
              style="width: 100%; margin-top: 10px"
              max-height="250"
              border
              stripe
              highlight-current-row
            >
              <el-table-column type="index" label="序号" width="60" />
              <el-table-column prop="name" label="姓名" width="100" />
              <el-table-column prop="phone" label="手机号码" width="120" />
              <el-table-column prop="positionName" label="防汛岗位" width="120" />
              <el-table-column prop="responsibleName" label="防汛责任" />
              <el-table-column prop="objectName" label="负责对象" width="120" />
              <el-table-column label="操作" width="80" fixed="right">
                <template #default="scope">
                  <el-button type="text" @click="removeMember(scope.row)"
                    >移除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!-- 底部内容 -->
      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">选择发送成员</h3>
        </div>
        <div class="bottom-content-wrapper">
          <!-- 左侧防汛组织树 -->
          <div class="org-tree-container">
            <div class="org-tree-header">防汛组织</div>
            <div class="org-tree-search">
              <el-input
                v-model="filterText"
                placeholder="请输入防汛组织名称"
                clearable
                prefix-icon="Search"
              />
            </div>
            <div class="org-tree-body">
              <el-tree-v2
                :data="adcdList"
                :props="propsTree"
                :height="390"
                :filter-method="filterNode"
                ref="treeRef"
                @node-click="handleNodeClick"
                highlight-current
              />
            </div>
          </div>

          <!-- 右侧成员列表 -->
          <div class="member-list-container">
            <div class="member-search-form">
              <el-form
                ref="memberFormRef"
                :model="memberForm"
                :rules="memberRules"
                inline
              >
                <el-form-item label="成员姓名" prop="name">
                  <el-input
                    v-model="memberForm.name"
                    placeholder="请输入成员姓名"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button @click="resetMemberForm" icon="Refresh">重置</el-button>
                  <el-button type="primary" @click="getMemberList(memberForm.name)" icon="Search">查询</el-button>
                  <el-button type="primary" @click="openMemberSelect">添加到发送成员列表</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="member-table">
              <el-table ref='allMembersTable' :data="allMembers" style="width: 100%" max-height="380" border stripe highlight-current-row>
                <el-table-column type="selection" width="55" />
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="name" label="姓名" width="100" />
                <el-table-column prop="phone" label="手机号码" width="120" />
                <el-table-column prop="positionName" label="防汛岗位" width="120" />
                <el-table-column prop="affiliatedUnit" label="所属部门" width="120" />
                <el-table-column prop="deptPosition" label="部门职务" width="120" />
                <el-table-column prop="responsibleName" label="防汛责任" />
                <el-table-column
                  prop="objectName"
                  label="负责对象"
                  width="120"
                />
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitWarning">保 存</el-button>
          <el-button @click="warningDialogShow = false" plain>取 消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加查看/编辑弹窗 -->
    <el-dialog
      v-model="detailDialogShow"
      :title="dialogType === 'view' ? '查看内部预警' : '编辑内部预警'"
      width="60%"
      class="warning-dialog"
      :close-on-click-modal="false"
    >
      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">预警详情</h3>
        </div>
        <el-form
          ref="detailFormRef"
          :model="detailForm"
          :rules="detailRules"
          label-width="120px"
          :disabled="dialogType === 'view'"
          class="detail-form"
        >
          <el-form-item label="预警内容" prop="content">
            <el-input
              v-model="detailForm.content"
              type="textarea"
              :rows="4"
              placeholder="请输入预警内容"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">发送成员列表</h3>
        </div>
        <div class="detail-table-container">
          <el-table :data="detailForm.memberInfoDTOS" style="width: 100%" max-height="300" border stripe highlight-current-row>
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="memberName" label="姓名" width="100" />
            <el-table-column prop="phone" label="手机号码" width="120" />
            <el-table-column prop="positionName" label="防汛岗位" width="170" />
            <el-table-column prop="respName" label="防汛责任" width="270"/>
            <el-table-column prop="respType" label="负责对象" width="200" >
              <template #default="scope">
                <span>{{ showObject(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="sendStatus" label="发送状态" align="center" width="120">
              <template #default="scope">
                <el-tag
                  :type="scope.row.sendStatus === 1 ? 'warning' : scope.row.sendStatus === 2 ? 'success' : 'danger'"
                  effect="light"
                  size="small"
                >
                  <el-icon style="margin-right: 4px;">
                    <Loading v-if="scope.row.sendStatus === 1" />
                    <CircleCheck v-else-if="scope.row.sendStatus === 2" />
                    <CircleClose v-else />
                  </el-icon>
                  {{ scope.row.sendStatus === 1 ? '发送中' : scope.row.sendStatus === 2 ? '发送成功' : '发送失败' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="" label="响应状态" align="center" width="120">
              <template #default="scope">
                <el-tag
                  :type="scope.row.respName === 1 ? 'success' : 'info'"
                  effect="light"
                  size="small"
                >
                  <el-icon style="margin-right: 4px;">
                    <CircleCheck v-if="scope.row.respName === 1" />
                    <Clock v-else />
                  </el-icon>
                  {{ scope.row.respName === 1 ? '已反馈' : '未反馈' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="sendTime" label="发送时间" width="170"/>
            <el-table-column prop="failReason" label="失败原因" width="150"/>
          </el-table>
        </div>
      </div>

      <div class="station-form-container">
        <div class="station-title-bar">
          <h3 class="station-subtitle">预警文件</h3>
        </div>
        <div class="file-container">
          <div v-if="detailForm.fileUrl">
            <el-link type="primary" icon="Document" @click="downloadFile(detailForm.fileUrl)">{{ detailForm.fileUrl.substring(37) }}</el-link>
          </div>
          <div v-else class="no-file">暂无文件</div>
        </div>
      </div>

      <template #footer v-if="dialogType === 'edit'">
        <span class="dialog-footer">
          <el-button type="primary" @click="submitEdit">保存</el-button>
          <el-button @click="detailDialogShow = false" plain>关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs, watch, getCurrentInstance } from "vue";
import { Loading, CircleCheck, CircleClose, Clock } from '@element-plus/icons-vue';
import { getToken } from "@/utils/auth";
import { getTreeOption } from "@/api/watershed/ads";
import {
  addWarningInner,
  queryWarningInner,
  queryWarningInnerDetail,
  queryFloodPreventionMember,
  downloadByMain,
  queryFloodPreventionOrganizations
} from "@/api/warning";

defineOptions({
  name: 'InternalEarlyWarning'
})

const { proxy } = getCurrentInstance();
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload"); // 上传文件服务器地址
const downloadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/download");
const loading = ref(false);
const treeRef = ref(null); // 使用ref来管理树组件引用

const data = reactive({
  total: 0,
  filterText: '',
  propsTree: {
    value: "id",
    label: "name",
    children: "children",
  },
  queryForm: {
    userId:'',
    timeRange: [],
    startTime : "",
    endTime : "",
    status: "",
    memberType: "",
    pageNum: 1,
    pageSize: 20,
  },
  tableData: [],
  warningDialogShow: false,
  warningForm: {
    content: "",
    relatedWarning: "",
    members: [],
    files: [],
  },
  warningRules: {
    content: [{ required: true, message: "请输入预警内容", trigger: "blur" }],
    // members: [{ required: true, message: "请选择预警成员", trigger: "change" }],
  },
  memberForm: {
    name: "",
  },
  memberRules: {
    // name: [{ required: true, message: "请输入成员姓名", trigger: "blur" }],4
  },
  memberTreeData: [], // 防汛组织树形数据
  selectedMembers: [], // 已选成员列表
  memberFilterText: "",
  memberSelectShow: false,
  allMembers: [],
  detailDialogShow: false,
  dialogType: 'view', // view 或 edit
  detailForm: {
    content: '',
    relatedWarning: '',
    members: [],
    files: []
  },
  detailRules: {
    content: [{ required: true, message: '请输入预警内容', trigger: 'blur' }]
  },
  adcdList: [],
  uploadParams: {
    action: uploadFileUrl,
    data: {
      bucketName: 'watershed'
    },
    headers: {
      Authorization: "Bearer " + getToken()
    }
  }
});
const {
  queryForm,
  total,
  tableData,
  warningDialogShow,
  warningForm,
  warningRules,
  memberTreeData,
  selectedMembers,
  memberFilterText,
  memberSelectShow,
  memberForm,
  memberRules,
  allMembers,
  detailDialogShow,
  dialogType,
  detailForm,
  detailRules,
  adcdList,
  propsTree,
  uploadParams,
  filterText,
} = toRefs(data);
onMounted(() => {
  // const end = new Date();
  // const start = new Date(end.getTime() - 72 * 60 * 60 * 1000);
  // data.queryForm.timeRange = [start, end];
  getList();
  getTreeList();
  // getWorkerList();
  getMemberList();
});
const getList = () => {
  loading.value = true;
  queryWarningInner({
        ...data.queryForm,
        startTime: data.queryForm.timeRange[0],
        endTime: data.queryForm.timeRange[1],
    }).then((res) => {
    data.tableData = res.data.records || [];
    //前端手动处理发送成员数组
    data.tableData.forEach((item) => {
      let sendMembers = []
      item.memberInfoDTOS?.forEach((member) => {
        sendMembers.push(member.memberName)
      })
      item.sendMembers = sendMembers.join(',')
    })
    data.total = res.data.total || 0;
    loading.value = false;
  });
};
const resetQuery = () => {
  const end = new Date();
  const start = new Date(end.getTime() - 72 * 60 * 60 * 1000);
  data.queryForm = {
    userId: "",
    timeRange: [start, end],
    publishStatus: "",
    pageNum: 1,
    pageSize: 20,
  };
  getList();
};
const showObject = (row) => {
  if (!row.respType) return '';

  // 将字符串转为数组
  const respTypeArr = row.respType.split(',');

  // 根据不同的类型返回对应的值
  const objectNames = respTypeArr.map(type => {
    if (type === '1') return row.adnm;
    if (type === '2') return row.hazaName;
    if (type === '3') return row.resName;
    return '';
  }).filter(name => name); // 过滤掉空值

  return objectNames.join(',');
};
const getStatusType = (status) => {
  const statusMap = {
    2: "warning",
    1: "success",
    3: "danger",
  };
  return statusMap[status] || "info";
};

const getStatusText = (status) => {
  const statusMap = {
    2: "发送中",
    1: "发送成功",
    3: "发送失败",
  };
  return statusMap[status] || "未知";
};

const memberProps = {
  label: "name",
  children: "children",
};

const warningFormRef = ref(null);
const memberTreeRef = ref(null);

// 监听成员搜索框
watch(
  () => data.memberFilterText,
  (val) => {
    memberTreeRef.value?.filter(val);
  }
);

// 成员树过滤方法
const filterMemberNode = (value, data) => {
  if (!value) return true;
  return data.name.includes(value);
};

// 打开内部预警弹窗
const openWarningDialog = () => {
  data.warningDialogShow = true;
  // 获取防汛组织树形数据
  getMemberTreeData();
};

// 获取防汛组织树形数据
const getMemberTreeData = () => {
  // TODO: 调用接口获取防汛组织树形数据
  // api.getOrganizationTree().then(res => {
  //     data.memberTreeData = res.data;
  // });
};

// 选择关联预警
const handleSelectWarning = () => {
  // TODO: 实现关联预警选择逻辑
};

// 清空已选成员
const clearSelectedMembers = () => {
  data.selectedMembers = [];
  memberTreeRef.value?.setCheckedKeys([]);
};

// 移除单个成员
// const removeMember = (row) => {
//   const index = data.selectedMembers.indexOf(row);
//   if (index > -1) {
//     data.selectedMembers.splice(index, 1);
//     // 同步取消树节点选中状态
//     memberTreeRef.value?.setChecked(row.id, false);
//   }
// };

// 提交预警
const submitWarning = async () => {
  await proxy.$refs.warningFormRef.validate(async (valid) => {
    if (!valid) return;

    try {
      let params = {
        content: warningForm.value.content,
        url: warningForm.value?.files[0]?.name || '', // 取第一个文件的URL
        memberIdList: selectedMembers.value.map(member => member.id)
      };

      if (selectedMembers.value.length === 0) {
        proxy.$modal.msgError('请选择发送成员');
        return;
      }
      const res = await addWarningInner(params);
      if (res.code === 200) {
        proxy.$modal.msgSuccess('发布成功');
        data.warningDialogShow = false;
        getList();
      } else {
        proxy.$modal.msgError(res.msg || '发布失败');
      }
    } catch (error) {
      console.error('发布预警失败:', error);
      proxy.$modal.msgError('发布预警失败');
    }
  });
};

// 打开选择成员弹窗
const openMemberSelect = async () => {
  // 获取表格选中的行
  const selection = proxy.$refs["allMembersTable"].getSelectionRows();

  // 添加到已选成员列表，去重
  const newMembers = selection.filter(
    (item) => !selectedMembers.value.some((selected) => selected.id === item.id)
  );
  selectedMembers.value = [...selectedMembers.value, ...newMembers];

  // 重新获取成员列表并过滤掉已选成员
  await getMemberList(memberForm.value.name);

  allMembers.value = allMembers.value.filter(
    (member) =>
      !selectedMembers.value.some((selected) => selected.id === member.id)
  );

  // 清空表格选择
  proxy.$refs["allMembersTable"].clearSelection();
};

const getMemberList = (name, organizationId) => {
  return queryFloodPreventionMember({
    name,
    pageNum: 1,
    pageSize: 9999,
    organizationId // 添加组织id参数
  }).then((response) => {
    data.allMembers = response.data.records;
    data.allMembers.forEach(item => {
      if(item.dtos) {
        let callbackData = formatIndexTableData(item.dtos)
        item.objectName = callbackData.objectName
        item.responsibleName = callbackData.responsibleName
      }
    })
  });
};

const removeMember = (row) => {
  selectedMembers.value = selectedMembers.value.filter(
    (item) => item.id !== row.id
  );
  getMemberList(memberForm.value.name);
};

const resetMemberForm = () => {
  memberForm.value.name = "";
  getMemberList();
};

// 监听成员筛选文本变化
watch(memberFilterText, (val) => {
  if (val) {
    allMembers.value = allMembers.value.filter(
      (item) =>
        item.name.includes(val) ||
        item.phone.includes(val) ||
        item.organization.includes(val)
    );
  } else {
    getMemberList();
  }
});
const handleBeforeRemove = (file, fileList) => {
  console.log("移除文件：", file, fileList);
  proxy.$modal.msgSuccess("文件移除成功");
  warningForm.value.files.filter((item) => item.name !== file.response.msg); // TODO: 处理移除文件的操作

}; // 移除文件之前的钩子
const handleUploadSuccess = (response, file) => {
  proxy.$modal.msgSuccess("文件上传成功");
  // TODO: 处理上传成功的响应，例如保存文件信息到表单
  // console.log("上传成功：", response, file);
  warningForm.value.files.push({ name: response.msg, url: '' });
};

const handleUploadError = (error) => {
  proxy.$modal.msgError("文件上传失败");
  // console.error("上传失败：", error);
};

const handleBeforeUpload = (file) => {
  // 这里可以添加文件类型和大小的验证
  const isValidType = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ].includes(file.type);
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isValidType) {
    proxy.$modal.msgError("只能上传PDF或Word文档！");
    return false;
  }
  if (!isLt20M) {
    proxy.$modal.msgError("文件大小不能超过20MB！");
    return false;
  }
  return true;
};

// 查看预警详情
const handleSee = (row) => {
  data.dialogType = 'view';
  data.detailDialogShow = true;
  // TODO: 调用获取详情接口
  queryWarningInnerDetail(row.id).then(res => {
    data.detailForm = res.data;
  });
};


const handleAgain = (row) => {
  // data.dialogType = 'edit';
  // data.detailDialogShow = true;
  // TODO: 调用获取详情接口
  // api.getWarningDetail(row.id).then(res => {
  //   data.detailForm = res.data;
  // });
};

// 提交编辑
const submitEdit = () => {
  proxy.$refs.detailFormRef.validate((valid) => {
    if (valid) {
      // TODO: 调用编辑接口
      // api.updateWarning(data.detailForm).then(res => {
      //   if (res.code === 200) {
      //     proxy.$modal.msgSuccess('修改成功');
      //     data.detailDialogShow = false;
      //     getList();
      //   }
      // });
    }
  });
};

// 删除预警
const handleDelete = (row) => {
  proxy.$modal.confirm('是否确认删除该预警？').then(() => {
    // TODO: 调用删除接口
    // api.deleteWarning(row.id).then(res => {
    //   if (res.code === 200) {
    //     proxy.$modal.msgSuccess('删除成功');
    //     getList();
    //   }
    // });
  });
};

// 修改下载文件函数
const downloadFile = (file) => {
  // 使用下载文件的函数
  downloadByMain({ fileName: file, bucketName: 'watershed' });
};
const getTreeList = () => {
  // getTreeOption(5).then((res) => {
  //   data.adcdList = formatTree(res.data);
  // });
  queryFloodPreventionOrganizations({
    pageNum: 1,
    pageSize: 9999,
  }).then((res) => {
    data.adcdList = formatTree(res.data.records);
  });
};
const formatTree = (data) => {
  // 创建一个Map来存储所有节点，方便查找
  const nodeMap = new Map();

  // 第一次遍历，初始化所有节点
  data.forEach(item => {
    nodeMap.set(item.id, {
      ...item,
      children: []
    });
  });

  // 第二次遍历，构建树形结构
  const result = [];
  data.forEach(item => {
    const node = nodeMap.get(item.id);
    if (item.parentId === null || item.parentId === 0 || !nodeMap.has(item.parentId)) {
      // 如果没有父节点或父节点不存在，则作为根节点
      result.push(node);
    } else {
      // 将当前节点添加到父节点的children中
      const parentNode = nodeMap.get(item.parentId);
      parentNode.children.push(node);
    }
  });

  // 清理空的children数组
  const cleanEmptyChildren = (nodes) => {
    nodes.forEach(node => {
      if (node.children.length === 0) {
        delete node.children;
      } else {
        cleanEmptyChildren(node.children);
      }
    });
  };

  cleanEmptyChildren(result);
  console.log(result, "结果");
  return result;
};
// const getWorkerList = () => {
//   queryFloodPreventionMember({
//     pageNum: 1,
//     pageSize: 10000, // TODO: 获取所有成员
//   }).then((res) => {
//     data.allMembers = res.data.records;
//     data.allMembers.forEach(item => {
//       if(item.dtos) {
//         let callbackData = formatIndexTableData(item.dtos)
//         item.objectName = callbackData.objectName
//         item.responsibleName = callbackData.responsibleName
//       }
//     })
//   })
// }
const formatIndexTableData = (res) => {
  let callbackData = {
    objectName: [],
    responsibleName: [],
  };
  res.forEach((item) => {
    if (item.objectName || item.responsibleName) {
      callbackData["objectName"].push(item.objectName);
      callbackData["responsibleName"].push(item.responsibleName);
    }
  });
  //callback 去重
  callbackData.objectName = [...new Set(callbackData.objectName)];
  callbackData.responsibleName = [...new Set(callbackData.responsibleName)];
  console.log(callbackData, "回调数据");
  return callbackData;
};

// 更新watch逻辑
watch(() => data.filterText, (val) => {
  if (treeRef.value) {
    treeRef.value.filter(val);
  }
});

// 实现树的过滤方法
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase());
};

// 处理节点点击事件
const handleNodeClick = (data) => {
  console.log('点击的节点数据:', data);
  memberForm.value.name = '';
  getMemberList(memberForm.value.name, data.id);
};
</script>
<style scoped lang="scss">
/* 弹窗样式美化 */
:deep(.warning-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    margin-right: 0;
  }

  .el-dialog__body {
    padding: 20px 24px;
    max-height: 75vh;
    overflow-y: auto;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 14px 20px;
  }
}

.station-form-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.station-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.station-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  padding: 16px 0 8px;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: #409eff;
  }
}

.detail-form {
  padding: 0 16px;
}

.detail-table-container {
  padding: 0 16px;
}

.file-container {
  padding: 16px;
  min-height: 60px;
  display: flex;
  align-items: center;

  .no-file {
    color: #909399;
    font-style: italic;
  }
}

.member-select-container {
  display: flex;
  gap: 20px;
  height: 400px;

  .member-tree {
    width: 300px;
    border: 1px solid #dcdfe6;
    padding: 10px;
    overflow: auto;
  }

  .selected-members {
    flex: 1;
    border: 1px solid #dcdfe6;
    padding: 10px;
    overflow: auto;

    .member-list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      padding: 0 10px;
    }
  }
}

.warning-content {
  display: flex;
  gap: 20px;
  padding: 0 16px;

  .warning-left {
    width: 48%;

    :deep(.el-form-item__content) {
      width: calc(100% - 150px);
    }
  }

  .warning-right {
    width: 48%;
    display: flex;
    flex-direction: column;

    .member-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .member-actions {
        display: flex;
        align-items: center;
      }
    }

    .el-table {
      flex: 1;
      overflow: auto;
    }
  }
}

/* 底部内容新布局样式 */
.bottom-content-wrapper {
  display: flex;
  gap: 20px;
  height: 500px;
  padding: 0 16px;

  .org-tree-container {
    width: 32%;
    border: 1px solid #dcdfe6;
    padding: 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    .org-tree-header {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
      color: #303133;
    }

    .org-tree-search {
      margin-bottom: 16px;
    }
  }

  .member-list-container {
    width: 68%;
    display: flex;
    flex-direction: column;

    .member-search-form {
      margin-bottom: 16px;
      padding: 16px;
      background-color: #f9fafc;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
    }

    .member-table {
      flex: 1;
      overflow: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #dcdfe6;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f5f7fa;
      }
    }
  }
}

.member-select-container {
  height: 400px;

  .member-tree {
    height: 100%;
    border: 1px solid #dcdfe6;
    padding: 10px;
    overflow: auto;
  }
}

.file-item {
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>