<!--调度成果管理-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="调度成果名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入名称" clearable style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="调度开始时间" prop="warnTime">
        <el-date-picker class="borderColor" v-model="queryParams.time" type="datetimerange" format="YYYY-MM-DD HH:mm"
          :clearable="false" date-format="YYYY/MM/DD ddd" time-format="hh:mm" range-separator="至"
          :disabled-date="disabledDateFn" start-placeholder="开始时间" end-placeholder="结束时间" style="width: 266px;" />
      </el-form-item>

      <el-form-item label="类型" prop="timing">
        <el-select v-model="queryParams.timing" placeholder="请选择" clearable style="width: 200px">
          <el-option v-for="dict in warnTimeTypeSelects" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="isdo">
        <el-select v-model="queryParams.isdo" placeholder="请选择" clearable style="width: 200px">
          <el-option v-for="dict in isdoStatusSelects" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>

      </el-form-item>

    </el-form>
    <el-button type="primary" @click="showTaskCar">任务车</el-button>
    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="lyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="40" />
      <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
      <el-table-column prop="dispcNm" label="名称" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="forecastName" label="类型" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="forecastName" label="完成时间" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="dispcType" label="状态" :show-overflow-tooltip="true" align="center">
        <template #default="scope">
          <span v-if="scope.row.dispcType === '1'">流域调度</span>
          <span v-if="scope.row.dispcType === '2'">单库调度</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="left" width="210" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleInfo(scope.row)">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleAccept(scope.row)">编辑</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>

        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
    <!-- 添加或修改流域对话框 -->
    <el-dialog v-model="taskCar" title="任务车">
      <el-button type="primary">批量删除</el-button>
      <el-table :data="carList" :style="{ width: '100%', marginTop: '20px' }">
        <el-table-column prop="date" type="selection" />
        <el-table-column lable="序号" type="index" />
        <el-table-column prop="date" label="任务提交时间" />
        <el-table-column prop="name" label="方案名称" />
        <el-table-column prop="address" label="任务执行状态" />
        <el-table-column prop="address" label="操作">
          <template #default="scope">
            <el-link type="primary">删除</el-link>
            <el-link type="danger">重新计算</el-link>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="taskCar = false">退出</el-button>
          <el-button type="primary" @click="taskCar = false"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import moment from "moment/moment";
import { getRainTimeByType } from "@/utils/common";
import { deleteDispatch, getDispatchInfo, getDispatchPage } from "@/api/watershed/forecast";

defineOptions({
  name: 'ResultIndex'
})

const { proxy } = getCurrentInstance();
let carList = ref([]);
// 详情里面的列表
const open = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const title = ref("");
const total = ref(0);
const selectedList = ref([]); // table勾选
const multiple = ref(true);
let taskCar = ref(false)
// 预警状态
const isdoStatusSelects = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '未发布',
    value: 1
  },
  {
    label: '已发布',
    value: 0
  }
]
// 预警时效
const warnTimeTypeSelects = [
  {
    label: '作业',
    value: 1
  },
  {
    label: '自动',
    value: 2
  },
]

const data = reactive({
  form: {
  },
  queryParams: {
    time: ['', ''],
    name: undefined,
    // adcd: '130800000000',
    adcd: undefined,
    pageNum: 1,
    pageSize: 10,
  }
});

const { queryParams, form } = toRefs(data);
const showTaskCar = () => {
  taskCar.value = true
}
function getList() {
  loading.value = true;
  // queryParams.value.startTime = moment(queryParams.value.time[0]).format('YYYY-MM-DD HH:mm:ss')
  // queryParams.value.endTime =  moment(queryParams.value.time[1]).format('YYYY-MM-DD HH:mm:ss')
  let params = {
    ...queryParams.value
  }
  delete params.time
  // delete params.startTime
  // delete params.endTime
  getDispatchPage(params).then(res => {
    lyList.value = res.data
    total.value = res.total
    loading.value = false
  })
}
function handleSelectionChange(e) {
  selectedList.value = e
  multiple.value = e.length !== 2;
}
const disabledDateFn = (time) => {
  return time.getTime() > Date.now();
};
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    pcode: '',
    addFlag: flag,
    geoType: 'people',
    lynm: undefined,
    icon: undefined,
    adcds: [],
  };
  cjzbList.value = []
  proxy.resetForm("menuRef");
}
function handleQuery() {
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
function handleAccept(row) {
  // reset(true);
  // if (row != null && row.lycode) {
  //   form.value.pcode = row.lycode;
  // } else {
  //   form.value.pcode = '';
  // }
  // open.value = true;
  // title.value = "添加流域";
  proxy.$modal.confirm('确定收到预警对象为【"' + row.name + '"】的预警?').then(function () {
    proxy.$modal.msgSuccess("收到了");
  })
}

/** 修改按钮操作 */
async function handleInfo(row) {
  reset();
  let res = await getDispatchInfo(row.id)
  console.log(res, '111')
  let obj = Object.assign(row, res.data);
  form.value = obj
  open.value = true;
  cjzbList.value = res.data
  title.value = "详情";
}

function handleDelete(row) {
  proxy.$modal.confirm('确定删除调度成果?').then(function () {
    return deleteDispatch(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("已删除");
  }).catch(() => { });
}

onMounted(() => {
  // 默认查询预警时间近72小时
  queryParams.value.time = getRainTimeByType('72')
  // 结束时间改成23:59
  let endTimeStr = moment(queryParams.value.time[1]).format('YYYY-MM-DD HH:mm:ss')
  endTimeStr = endTimeStr.substring(0, 11) + '23:59:59'
  queryParams.value.time[1] = new Date(endTimeStr)
  //getList();
})
</script>
<style scoped lang="scss">
.card-box {
  padding-right: 0;
  padding-left: 0;
  margin-bottom: 5px;
}

.exceed {
  width: 95%;
  margin: 2px auto;
  height: 150px;
  display: flex;
  color: #555;

  .items {
    flex: 1;
    height: 100%;
    text-align: center;

    .value {
      margin: 24px 0;
      font-size: 18px;
    }

    .name {
      margin: 10px 0;
      color: #555;
      font-size: 16px;
    }
  }

  //.items:hover {
  //  background: linear-gradient(0deg, #07529F 0%, rgba(8, 61, 132, 0) 100%);
  //  box-sizing: border-box;
  //  border: 0px solid #000000;
  //  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
  //  cursor: pointer;
  //}
  //
  //.active {
  //  background: linear-gradient(0deg, #07529F 0%, rgba(8, 61, 132, 0) 100%);
  //  box-sizing: border-box;
  //  border: 0px solid #000000;
  //  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
  //  cursor: pointer;
  //}
}
</style>
