<template>
  <div>
    <el-dialog
      v-model="showTaskResultManage"
      title="任务结果管理"
      width="90%"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      class="task-result-manage-dialog"
      @close="closeDialog"
    >
      <div class="box" v-loading="detailLoading">
        <div class="box-header">
          <el-form
            ref="formTopRef"
            :model="addForm"
            label-width="auto"
            :rules="rules"
            inline
            class="form-container"
          >
            <el-form-item label="预报方案" prop="schemeId">
              <el-select
                v-model="addForm.schemeId"
                placeholder="请选择预报方案"
                @change="changeTask"
                :disabled="manageStatus === 'edit'"
              >
                <el-option
                  :label="item.schemeName"
                  :value="item.id"
                  v-for="(item, index) in taskList"
                  :key="index"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="预报时间" prop="forecastTime">
              <el-date-picker
                v-model="addForm.forecastTime"
                type="datetime"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                placeholder="请选择预报时间"
                :disabled="manageStatus === 'edit'"
                :disabledDate="disabledDate"
                @change="handleForecastTimeChange"
              />
            </el-form-item>
            <el-form-item label="预热期(时)" prop="warmupPeriod">
              <el-input-number
                v-model="addForm.warmupPeriod"
                placeholder="请输入小时数"
                :controls="false"
                :precision="0"
                :min="0"
                :max="360"
              />
            </el-form-item>
            <el-form-item label="预见期(时)" prop="forecastHorizon">
              <el-input-number
                v-model="addForm.forecastHorizon"
                placeholder="请输入小时数"
                :controls="false"
                :precision="0"
                :min="1"
                :max="360"
              />
            </el-form-item>
            <el-form-item label="计算步长(分)" prop="timeStep">
              <el-select
                v-model="addForm.timeStep"
                placeholder="请选择计算步长"
              >
                <el-option label="5" :value="5" />
                <el-option label="10" :value="10" />
                <el-option label="30" :value="30" />
                <el-option label="60" :value="60" />
                <el-option label="120" :value="120" />
                <el-option label="180" :value="180" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="box-content">
          <div class="left">
            <el-form
              ref="formBottomRef"
              :model="addForm"
              label-width="auto"
              :rules="rules"
            >
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-form-item label="预报降雨" prop="rainfallType">
                    <el-select
                      v-model="addForm.rainfallType"
                      placeholder="请选择降雨类型"
                      @change="changerainfallType"
                    >
                      <el-option label="实测降雨" :value="0" />
                      <el-option label="预报降雨" :value="1" />
                      <el-option label="人工分配" :value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="addForm.rainfallType !== 2">
                  <el-form-item
                    label="小流域"
                    prop="basinCode"
                    label-width="80"
                  >
                    <el-select
                      v-model="addForm.basinCode"
                      placeholder="请选择"
                      @change="changeSubBasin"
                    >
                      <el-option
                        v-for="item in subBasinList"
                        :key="item.basinCode"
                        :label="item.basinName"
                        :value="item.basinCode"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="addForm.rainfallType === 2">
                  <el-form-item label="分配降雨(mm)" prop="allocatedRainfall">
                    <el-input-number
                      v-model="addForm.allocatedRainfall"
                      placeholder="请输入"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24" v-if="addForm.rainfallType === 2">
                  <div class="flex w-full">
                    <el-form-item label="分配时段(时)" prop="allocationPeriod">
                      <el-input-number
                        v-model="addForm.allocationPeriod"
                        placeholder="请输入"
                        :controls="false"
                        :min="1"
                      />
                    </el-form-item>
                    <el-form-item
                      label="参考雨型"
                      prop="referenceRainfallPattern"
                      label-width="80"
                    >
                      <el-select
                        v-model="addForm.referenceRainfallPattern"
                        placeholder="请选择"
                        @change="changeReferenceRainfallPattern"
                      >
                        <el-option
                          v-for="item in typicalRainfallList"
                          :key="item.code"
                          :label="item.code"
                          :value="item.code"
                        />
                      </el-select>
                    </el-form-item>
                    <el-button
                      type="primary"
                      class="newRainButton"
                      @click="toShowNewRain"
                      >分配</el-button
                    >
                  </div>
                </el-col>
              </el-row>

              <!-- 降雨图表区域 -->
              <div
                v-show="shouldShowChart"
                class="chart-container"
                v-loading="chatLoading"
              >
                <div class="rain-chat" ref="rainChatRef" />
              </div>
              <!-- 无数据占位符 -->
              <div
                v-show="!shouldShowChart"
                class="chart-placeholder"
                v-loading="chatLoading"
              >
                <el-empty
                  description="请进行选择查看降雨数据"
                  :image-size="80"
                />
              </div>

              <el-form-item label="入流断面" prop="inflowSectionCode">
                <el-select
                  v-model="addForm.inflowSectionCode"
                  placeholder="请选择"
                  @change="fetchInFlowList"
                >
                  <el-option
                    v-for="item in riverSectionList"
                    :key="item.riverSectionCode"
                    :label="item.nodeName"
                    :value="item.riverSectionCode"
                  />
                </el-select>
              </el-form-item>

              <el-table
                :data="inFlowList"
                class="flow-table"
                v-loading="inflowDataLoading"
                element-loading-text="正在获取入流数据..."
              >
                <el-table-column
                  label="时间"
                  align="center"
                  prop="forecastTime"
                />
                <el-table-column label="入流量(m³/s)" align="center">
                  <template #default="scope">
                    <el-input-number
                      v-model="scope.row.inflowValue"
                      placeholder="请输入"
                      :controls="false"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </el-form>
          </div>
          <div class="right" v-loading="loading">
            <DragLegend
              :nodes="nodes"
              :edges="edges"
              :nodeValue="nodeValue"
              showType="readOnly"
              ref="vflow"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div style="display: flex; align-items: center; justify-content: start">
          <el-button
            type="primary"
            style="width: 140px"
            @click="saveConfig"
            v-if="!addForm.id || manageStatus === 'edit'"
            >保 存</el-button
          >
          <el-button style="width: 140px" @click="closeDialog">取 消</el-button>
          <el-button
            type="primary"
            style="width: 140px"
            @click="forecastResult"
            v-if="addForm.state != 2"
            >预 报</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 降雨分配模型对话框 -->
    <RainAllocationDialog
      :visible="showRainAllocationDialog"
      @update:visible="showRainAllocationDialog = $event"
      :typical-rainfall-data="rainChatList"
      :typical-rainfall-name="getTypicalRainfallName"
      :allocated-rainfall="addForm.allocatedRainfall"
      :allocation-period="addForm.allocationPeriod"
      :warmup-period="addForm.warmupPeriod"
      :forecast-horizon="addForm.forecastHorizon"
      :startTime="startTime"
      :endTime="endTime"
      @confirm="handleRainAllocationConfirm"
    />
  </div>
</template>

<script setup>
import {
  reactive,
  toRefs,
  onMounted,
  watch,
  getCurrentInstance,
  ref,
  nextTick,
  onUnmounted,
  computed,
} from "vue";
import DragLegend from "@/views/forecast/scheduling/reparation/dragLegend/index.vue";
import RainAllocationDialog from "./components/RainAllocationDialog.vue";
import {
  saveForecastResult,
  forecastResultForecast,
  getObservedRainfall,
  getPredictedRainfall,
  getInflowList,
  forecastResultEdit,
} from "@/api/scheduling";
import {
  getTypicalRainfallList,
  getTypicalRainfallInfo,
} from "@/api/watershed/forecast";
import moment from "moment";
import * as echarts from "echarts";
import { useForecastScheme } from "@/composables/useForecastScheme";
import { deepClone } from "@/utils";
import useUserStore from "@/store/modules/user";

defineOptions({
  name: "TaskResultManage",
});

const { proxy } = getCurrentInstance();
const { loading, getTaskDetail, getFlowDataBase } = useForecastScheme();
const userStore = useUserStore();

// ECharts 相关
const rainChatRef = ref(null);
let rainChart = null;

const showTaskResultManage = defineModel("showTaskResultManage", {
  default: false,
});

const props = defineProps({
  manageStatus: {
    type: String,
    default: "",
  },
  manageId: {
    type: String,
    default: "",
  },
  taskList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["getList"]);

// 获取当前时间最近的整点时间
const getNearestHourTime = () => {
  const now = moment();
  return now.startOf("hour").format("YYYY-MM-DD HH:mm");
};

// 禁用日期函数 - 只允许选择今天及之前的日期
const disabledDate = (time) => {
  const today = moment().startOf("day");
  return time.getTime() > today.valueOf();
};

// 处理预报时间变化，确保选择的是整点时间且不超过当前时间最近的整点
const handleForecastTimeChange = (value) => {
  if (value) {
    const selectedTime = moment(value);
    const currentNearestHour = moment().startOf("hour");

    // 如果选择的时间超过了当前时间最近的整点，调整为当前时间最近的整点
    if (selectedTime.isAfter(currentNearestHour)) {
      addForm.value.forecastTime =
        currentNearestHour.format("YYYY-MM-DD HH:mm");
      proxy.$message.warning("预报时间不能超过当前时间最近的整点，已自动调整");
      return;
    }

    // 如果选择的时间不是整点，自动调整为整点
    if (selectedTime.minute() !== 0 || selectedTime.second() !== 0) {
      const adjustedTime = selectedTime
        .startOf("hour")
        .format("YYYY-MM-DD HH:mm");
      addForm.value.forecastTime = adjustedTime;
      proxy.$message.warning("预报时间已自动调整为整点时间");
    }
  }
};

let state = reactive({
  addForm: {
    schemeId: "",
    inflowSectionCode: "",
    warmupPeriod: 24,
    forecastHorizon: 24,
    rainfallType: "",
    timeStep: 60,
    forecastType: 0,
    forecastTime: getNearestHourTime(),
  },
  rules: {
    schemeId: [{ required: true, message: "请选择方案", trigger: "change" }],
    forecastTime: [
      { required: true, message: "请选择预报时间", trigger: "change" },
    ],
    rainfallType: [
      { required: true, message: "请选择降雨类型", trigger: "change" },
    ],
    forecastHorizon: [
      { required: true, message: "请输入预见期时间(小时)", trigger: "blur" },
    ],
    timeStep: [
      { required: true, message: "请选择计算步长", trigger: "change" },
    ],
    basinCode: [{ required: true, message: "请选择小流域", trigger: "change" }],
    allocatedRainfall: [
      { required: true, message: "请输入分配降雨量(mm)", trigger: "blur" },
    ],
    allocationPeriod: [
      { required: true, message: "请输入分配时段(小时)", trigger: "blur" },
    ],
    referenceRainfallPattern: [
      { required: true, message: "请选择典型降雨模式", trigger: "change" },
    ],
  },
});
let { addForm, rules } = toRefs(state);

// 生成预报成果名称：预报方案-用户名称-预报时间
const generateForecastName = () => {
  const schemeName =
    props.taskList.find((item) => item.id === addForm.value.schemeId)
      ?.schemeName || "预报方案";
  const userName = userStore.name || "用户";
  const forecastTime = addForm.value.forecastTime
    ? moment(addForm.value.forecastTime).format("YYYY-MM-DD HH:mm")
    : moment().format("YYYY-MM-DD HH:mm");

  return `${schemeName}-${userName}-${forecastTime}`;
};

//保存任务
const saveConfig = () => {
  // 编辑模式下需要确认
  if (props.manageStatus === "edit" && addForm.value.id) {
    proxy.$modal
      .confirm("重新保存预报任务会取消之前的预报成果，确认继续保存吗？")
      .then(() => {
        performSave();
      })
      .catch(() => {});
  } else {
    performSave();
  }
};

// 执行保存操作
const performSave = () => {
  proxy.$refs.formTopRef.validate(async (valid) => {
    if (valid) {
      proxy.$refs.formBottomRef.validate(async (flag) => {
        if (flag) {
          const params = getSaveParams("save");
          const res = await saveForecastResult(params);

          if (res.code == 200) {
            proxy.$modal.msgSuccess("保存成功");
            emit("getList");
            closeDialog();
          }
        }
      });
    }
  });
};
// 预报任务
const forecastResult = () => {
  // 编辑模式下需要确认
  if (props.manageStatus === "edit" && addForm.value.id) {
    proxy.$modal
      .confirm("重新提交预报任务会覆盖之前的预报成果，确认继续预报吗？")
      .then(() => {
        performForecast();
      })
      .catch(() => {});
  } else {
    performForecast();
  }
};

// 执行预报操作
const performForecast = () => {
  proxy.$refs.formTopRef.validate(async (valid) => {
    if (valid) {
      proxy.$refs.formBottomRef.validate(async (flag) => {
        if (flag) {
          const params = getSaveParams("forecast");
          const res = await forecastResultForecast(params);

          if (res.code == 200) {
            proxy.$modal.msgSuccess("预报成功");
            emit("getList");
            closeDialog();
          }
        }
      });
    }
  });
};

const getSaveParams = (type = "save") => {
  const params = {
    forecastProductPO: {
      ...addForm.value,
      status: type === "save" ? 0 : 1,
      name: generateForecastName(), // 添加预报成果名称
    },
  };

  // 小流域降雨列表
  params.subBasinRainfallList = subBasinList.value.map((item) => ({
    basinCode: item.basinCode,
  }));
  if (addForm.value.rainfallType === 2) {
    params.typicalRainfallDataList = rainChatList.value;
  }

  // 入流断面列表 - 包含所有断面的入流数据
  params.inflowSectionList = [];
  allInflowData.value.forEach((data) => {
    params.inflowSectionList.push(...data);
  });
  return params;
};

const closeDialog = () => {
  proxy.$refs.formTopRef?.resetFields();
  proxy.$refs.formBottomRef?.resetFields();
  addForm.value = {
    warmupPeriod: 24,
    forecastHorizon: 24,
    timeStep: 60,
    forecastType: 0,
    forecastTime: getNearestHourTime(),
  };
  // 清空小流域数据
  subBasinList.value = [];
  // 清空雨量图表数据
  rainChatList.value = [];
  // 清空入流断面
  riverSectionList.value = [];
  // 清空入流数据
  inFlowList.value = [];
  // 清空入流数据缓存
  allInflowData.value.clear();
  proxy.$nextTick(() => {
    proxy.$refs.vflow?.resetNode(false);
  });
  showTaskResultManage.value = false;
};
// 降雨分配模型对话框显示状态
const showRainAllocationDialog = ref(false);

const toShowNewRain = () => {
  // 检查是否选择了典型降雨
  if (!state.addForm.referenceRainfallPattern) {
    proxy.$modal.msgWarning("请先选择典型降雨");
    return;
  }

  // 检查是否有分配降雨量
  if (!state.addForm.allocatedRainfall) {
    proxy.$modal.msgWarning("请输入分配降雨量");
    return;
  }

  // 检查是否有分配时段
  if (!state.addForm.allocationPeriod) {
    proxy.$modal.msgWarning("请输入分配时段");
    return;
  }

  if (!rainChatList.value.length) {
    proxy.$modal.msgWarning("没有降雨数据");
    return;
  }

  showRainAllocationDialog.value = true;
};

// 处理降雨分配确认
const handleRainAllocationConfirm = (allocationResult) => {
  // 更新图表数据
  rainChatList.value = allocationResult;
  initChat();

  // 关闭对话框
  showRainAllocationDialog.value = false;

  proxy.$modal.msgSuccess("降雨分配完成");
};

const rainChatList = ref([]);
const chatLoading = ref(false);

// 控制图表显示的计算属性
const shouldShowChart = computed(() => {
  return rainChatList.value && rainChatList.value.length > 0;
});

// 获取典型降雨名称
const getTypicalRainfallName = computed(() => {
  if (!state.addForm.referenceRainfallPattern || !typicalRainfallList.value) {
    return "";
  }
  const rainfall = typicalRainfallList.value.find(
    (item) => item.code === state.addForm.referenceRainfallPattern
  );
  return rainfall ? rainfall.code : "";
});
// 初始化降雨图表
const initChat = async () => {
  await nextTick();
  if (!rainChatRef.value) return;

  // 销毁已存在的图表实例
  if (rainChart) {
    rainChart.dispose();
  }

  // 创建新的图表实例
  rainChart = echarts.init(rainChatRef.value);

  // 处理真实降雨数据
  const chartData = {
    times: [],
    rainfall: [],
  };

  // 从rainChatList中提取数据并格式化
  if (rainChatList.value && rainChatList.value.length > 0) {
    const sortedData = deepClone(rainChatList.value).sort(
      (a, b) => new Date(a.time) - new Date(b.time)
    );

    sortedData.map((item) => {
      const timeStr = moment(item.time).format("YYYY-MM-DD HH:mm");
      chartData.times.push(timeStr);
      chartData.rainfall.push(item.rainfallValue || 0);
    });
  }

  // 根据降雨类型设置图表样式
  const rainfallType = state.addForm.rainfallType;
  const isObserved = rainfallType === 0; // 0表示实测降雨，1表示预报降雨
  const seriesName = isObserved ? "实测降雨" : "预报降雨";
  const seriesColor = isObserved ? "#52C41A" : "#87CEEB"; // 实测用绿色，预报用蓝色

  // 图表配置
  const option = {
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        const data = params[0];
        return `时间: ${data.axisValue}<br/>${seriesName}: ${data.value.toFixed(
          1
        )}mm`;
      },
    },
    grid: {
      left: "10%",
      right: "10%",
      bottom: "15%",
      top: "15%",
    },
    xAxis: {
      type: "category",
      data: chartData.times,
      axisLabel: {
        fontSize: 10,
        // rotate: 45,
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: "value",
      name: "降雨量(mm)",
      nameTextStyle: {
        fontSize: 10,
      },
      axisLabel: {
        fontSize: 10,
      },
      splitLine: {
        lineStyle: {
          type: "dashed",
          color: "#e0e0e0",
        },
      },
    },
    series: [
      {
        name: seriesName,
        type: "bar",
        data: chartData.rainfall,
        itemStyle: {
          color: seriesColor,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };

  // 设置图表配置
  rainChart.setOption(option);

  // 监听窗口大小变化，自适应图表大小
  window.addEventListener("resize", () => {
    if (rainChart) {
      rainChart.resize();
    }
  });
};

const nodes = ref([]);
const edges = ref([]);
const nodeValue = ref([]);
// 小流域列表
const subBasinList = ref([]);
// 获取河道断面列表
const riverSectionList = ref([]);
const changeTask = async (val) => {
  if (!val) {
    return;
  }
  const res = await getTaskDetail(val);
  if (res) {
    // 设置流程图数据
    nodes.value = res.nodes;
    edges.value = res.edges;
    nodeValue.value = res.nodeValue;
    subBasinList.value = res.subBasinList;
    if (subBasinList.value.length) {
      addForm.value.basinCode = subBasinList.value[0].basinCode;
      changeSubBasin(addForm.value.basinCode);
    }
    riverSectionList.value = res.riverSectionList.filter(
      (item) => item.isInflowSection === 0
    );

    // 等待DOM更新后显示流程图
    proxy.$nextTick(() => {
      proxy.$refs.vflow?.showView();
    });

    // 如果已设置时间参数，获取所有断面的入流数据
    if (
      state.addForm.forecastTime &&
      state.addForm.warmupPeriod &&
      state.addForm.forecastHorizon &&
      !detailLoading.value
    ) {
      await fetchAllInflowData();
    }
  }
};

// 预报降雨类型切换
const changerainfallType = (val) => {
  rainChatList.value = [];
  if (rainChart) {
    rainChart.dispose();
  }
  // 降雨类型为人工分配时，清空小流域
  if (val === 2) {
    state.addForm.basinCode = "";
  } else if (val === 1 || val === 0) {
    if (state.addForm.basinCode) {
      changeSubBasin(val);
    }
  }
};

const startTime = computed(() => {
  const forecastTime = moment(state.addForm.forecastTime);
  if (!forecastTime.isValid() || !state.addForm.warmupPeriod) {
    return "";
  }
  return forecastTime
    .clone()
    .subtract(state.addForm.warmupPeriod, "hours")
    .format("YYYY-MM-DD HH:mm");
});

const endTime = computed(() => {
  const forecastTime = moment(state.addForm.forecastTime);
  if (!forecastTime.isValid() || !state.addForm.forecastHorizon) {
    return "";
  }
  return forecastTime
    .clone()
    .add(state.addForm.forecastHorizon, "hours")
    .format("YYYY-MM-DD HH:mm");
});

// 小流域切换
const changeSubBasin = (value) => {
  console.log("🚀 ~ ", "---------------");
  const rainfallType = state.addForm.rainfallType;

  if (!state.addForm.forecastTime) {
    state.addForm.basinCode = "";
    proxy.$message.warning("请选择预报时间");
    return;
  }
  if (!state.addForm.warmupPeriod) {
    state.addForm.basinCode = "";
    proxy.$message.warning("请选择预热期");
    return;
  }
  if (!state.addForm.forecastHorizon) {
    state.addForm.basinCode = "";
    proxy.$message.warning("请选择预见期");
    return;
  }

  // 构建请求参数
  const params = {
    code: value,
    startTime: startTime.value,
    endTime: endTime.value,
  };

  if (rainfallType === 0) {
    chatLoading.value = true;
    getObservedRainfall(params)
      .then((res) => {
        rainChatList.value = res.rows;
        initChat();
      })
      .finally(() => {
        chatLoading.value = false;
      });
  } else if (rainfallType === 1) {
    chatLoading.value = true;
    getPredictedRainfall(params)
      .then((res) => {
        rainChatList.value = res.rows;
        initChat();
      })
      .finally(() => {
        chatLoading.value = false;
      });
  }
};

// 参考雨量切换/典型降雨切换
const changeReferenceRainfallPattern = (value) => {
  const code = typicalRainfallList.value.find(
    (item) => item.code === value
  ).code;
  chatLoading.value = true;
  getTypicalRainfallInfo({
    code,
  })
    .then((res) => {
      if (res.code === 200) {
        rainChatList.value = (res.data.rainfallData || []).sort(
          (a, b) => new Date(a.time) - new Date(b.time)
        );

        initChat();
      }
    })
    .finally(() => {
      chatLoading.value = false;
    });
};

// 获取典型降雨列表
const typicalRainfallList = ref([]);
const fetchTypicalRainfallList = async () => {
  const res = await getTypicalRainfallList();
  if (res.code === 200) {
    typicalRainfallList.value = res.rows;
  }
};

// 获取入流断面列表
const inFlowList = ref([]);
// 存储所有断面的入流数据
const allInflowData = ref(new Map());
// 入流数据加载状态
const inflowDataLoading = ref(false);

// 获取所有断面的入流数据
const fetchAllInflowData = async () => {
  if (!riverSectionList.value || riverSectionList.value.length === 0) {
    console.log("没有入流断面数据");
    return;
  }

  inflowDataLoading.value = true;

  try {
    // 并行获取所有断面的入流数据
    const promises = riverSectionList.value.map(async (section) => {
      const params = {
        code: section.riverSectionCode,
        startTime: startTime.value,
        endTime: endTime.value,
      };

      try {
        const res = await getInflowList(params);
        return {
          sectionCode: section.riverSectionCode,
          data: res.code === 200 ? res.rows : [],
        };
      } catch (error) {
        return {
          sectionCode: section.riverSectionCode,
          data: [],
        };
      }
    });

    const results = await Promise.all(promises);

    // 将结果存储到 Map 中
    const newInflowData = new Map();
    results.forEach((result) => {
      newInflowData.set(result.sectionCode, result.data);
    });

    allInflowData.value = newInflowData;

    // 如果当前已选择断面，更新显示数据
    if (state.addForm.inflowSectionCode) {
      updateInflowListDisplay(state.addForm.inflowSectionCode);
    }
  } catch (error) {
    console.error("获取入流数据失败:", error);
  } finally {
    inflowDataLoading.value = false;
  }
};

// 根据选择的断面更新入流数据显示
const updateInflowListDisplay = (sectionCode) => {
  if (allInflowData.value.has(sectionCode)) {
    const data = allInflowData.value.get(sectionCode);
    inFlowList.value = data;
  } else {
    inFlowList.value = [];
  }
};

// 断面切换
const fetchInFlowList = (value) => {
  if (!state.addForm.forecastTime) {
    state.addForm.inflowSectionCode = "";
    proxy.$message.warning("请选择预报时间");
    return;
  }
  if (!state.addForm.warmupPeriod) {
    state.addForm.inflowSectionCode = "";
    proxy.$message.warning("请选择预热期");
    return;
  }
  if (!state.addForm.forecastHorizon) {
    state.addForm.inflowSectionCode = "";
    proxy.$message.warning("请选择预见期");
    return;
  }

  // 直接从缓存中获取数据，不调用接口
  updateInflowListDisplay(value);
};

// 监听时间参数变化，重新获取入流数据
watch(
  [
    () => state.addForm.forecastTime,
    () => state.addForm.warmupPeriod,
    () => state.addForm.forecastHorizon,
  ],
  async ([newForecastTime, newWarmupPeriod, newForecastHorizon]) => {
    // 只有当所有时间参数都有值且有断面列表时才获取数据
    if (
      newForecastTime &&
      newWarmupPeriod &&
      newForecastHorizon &&
      riverSectionList.value.length > 0
    ) {
      await fetchAllInflowData();
    }
  },
  { deep: true }
);

const detailLoading = ref(false);
const getManageDetail = async () => {
  try {
    detailLoading.value = true;
    const res = await forecastResultEdit(props.manageId);
    if (res.code === 200) {
      const data = res.data;
      addForm.value = {
        ...addForm.value,
        ...data.forecastProductPO,
        rainfallType: Number(data.forecastProductPO.rainfallType),
      };

      await changeTask(addForm.value.schemeId);

      if (
        addForm.value.rainfallType === 1 ||
        addForm.value.rainfallType === 0
      ) {
        addForm.value.basinCode = subBasinList.value[0].basinCode;
        changerainfallType(addForm.value.rainfallType);
      } else if (addForm.value.rainfallType === 2) {
        rainChatList.value = data.typicalRainfallDataList;
        initChat();
      }

      nextTick(() => {
        if (!data.inflowSectionList.length) return;
        // 将结果存储到 Map 中
        const newInflowData = new Map();
        data.inflowSectionList.forEach((result) => {
          newInflowData.set(result.inflowSectionCode, result.sectionList);
        });

        allInflowData.value = newInflowData;

        addForm.value.inflowSectionCode =
          data.inflowSectionList[0].inflowSectionCode;
        updateInflowListDisplay(addForm.value.inflowSectionCode);
      });
    }
  } catch (error) {
    console.log(error);
  } finally {
    detailLoading.value = false;
  }
};

watch(
  () => showTaskResultManage.value,
  (val) => {
    if (val) {
      props.manageStatus === "edit" && getManageDetail();
    }
  }
);

onMounted(() => {
  fetchTypicalRainfallList();
});

onUnmounted(() => {
  if (rainChart) {
    rainChart.dispose();
  }

  window.removeEventListener("resize", () => {
    if (rainChart) {
      rainChart.resize();
    }
  });
});
</script>

<style lang="scss" scoped>
.box {
  display: flex;
  flex-direction: column;
  height: 100%;

  .box-header {
    margin-bottom: 10px;
  }

  .box-content {
    flex: 1;
    display: flex;
  }
}

.left {
  flex: 2;
  max-width: 500px;
  background-color: #f7f8fa;
  padding: 10px 20px;
  box-sizing: border-box;
  position: relative;
  z-index: 100;
}

.right {
  flex: 3;
  height: 100%;
  position: relative;
}
.newRainButton {
  margin-left: 10px;
}

:deep(.task-result-manage-dialog) {
  .el-dialog__body {
    height: 80vh;
  }
}

.chart-container {
  margin-bottom: 20px;
}

.rain-chat {
  height: 220px;
}

.chart-placeholder {
  height: 220px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.flow-table {
  max-height: 260px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}
</style>
