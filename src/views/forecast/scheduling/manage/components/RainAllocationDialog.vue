<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择降雨分配模型"
    width="1000px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    @close="handleClose"
  >
    <div class="rain-allocation-container">
      <!-- 典型降雨名称显示 -->
      <div class="rainfall-info">
        <h3>{{ typicalRainfallName }} 雨型</h3>
        <div class="allocation-params">
          <span>分配降雨量：{{ allocatedRainfall }}mm</span>
          <span style="margin-left: 20px"
            >分配时段：{{ allocationPeriod }}小时</span
          >
        </div>
      </div>

      <!-- 降雨柱形图 -->
      <div class="chart-container">
        <div
          ref="chartRef"
          class="rainfall-chart"
          v-loading="chartLoading"
        ></div>
      </div>

      <!-- 选择窗口信息 -->
      <div class="selection-info">
        <div class="info-item">
          <span
            >选择时段：第{{ selectedStartPeriod }}时段 - 第{{
              selectedEndPeriod
            }}时段</span
          >
        </div>
        <div class="info-item">
          <span>选择窗口长度：{{ windowLength }}小时</span>
        </div>
        <div class="info-item">
          <span
            >选择时段降雨总量：{{ selectedRainfallTotal.toFixed(2) }}mm</span
          >
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          type="primary"
          @click="handleCalculate"
          :disabled="!canCalculate"
        >
          计算
        </el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts";
import moment from "moment";
import { getDesignRainfall } from "@/api/scheduling";

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  // 典型降雨列表数据
  typicalRainfallData: {
    type: Array,
    default: () => [],
  },
  // 典型降雨名称
  typicalRainfallName: {
    type: String,
    default: "",
  },
  // 分配的雨量
  allocatedRainfall: {
    type: [Number, String],
    default: 0,
  },
  // 分配的时段
  allocationPeriod: {
    type: [Number, String],
    default: 0,
  },
  // 预热期
  warmupPeriod: {
    type: [Number, String],
    default: 0,
  },
  // 预见期
  forecastHorizon: {
    type: [Number, String],
    default: 0,
  },
  // 开始时间
  startTime: {
    type: String,
    default: "",
  },
  // 结束时间
  endTime: {
    type: String,
    default: "",
  },
});

// Emits
const emit = defineEmits(["update:visible", "confirm"]);

// 响应式数据
const dialogVisible = ref(false);
const chartRef = ref(null);
const chartLoading = ref(false);
let chartInstance = null;

// dataZoom 选择相关
const dataZoomStart = ref(0);
const dataZoomEnd = ref(0);
const allocationResult = ref([]);

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
      initializeData();
    }
  },
  { immediate: true }
);

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit("update:visible", newVal);
});

// 可选择窗口长度
const windowLength = computed(() => {
  const allocationPeriod = Number(props.allocationPeriod) || 0;
  const dateLength = Number(props.warmupPeriod) + Number(props.forecastHorizon);
  return Math.min(allocationPeriod, dateLength);
});

const selectedStartPeriod = computed(() => {
  if (props.typicalRainfallData.length === 0) return 1;
  const startIndex = Math.floor(
    (dataZoomStart.value / 100) * props.typicalRainfallData.length
  );
  return Math.max(1, startIndex + 1);
});

const selectedEndPeriod = computed(() => {
  if (props.typicalRainfallData.length === 0) return 1;

  // 计算选择的时段数量，应该等于窗口长度
  const startIndex = Math.floor(
    (dataZoomStart.value / 100) * props.typicalRainfallData.length
  );
  const selectedPeriods = windowLength.value;
  const endPeriod = startIndex + selectedPeriods;

  return Math.min(props.typicalRainfallData.length, endPeriod);
});

const selectedRainfallTotal = computed(() => {
  if (props.typicalRainfallData.length === 0) {
    return 0;
  }

  const startIndex = selectedStartPeriod.value - 1; // 转换为数组索引
  const endIndex = selectedEndPeriod.value; // 不包含结束索引

  return props.typicalRainfallData
    .slice(startIndex, endIndex)
    .reduce((sum, item) => sum + (item.rainfallValue || 0), 0);
});

const canCalculate = computed(() => {
  return props.typicalRainfallData.length > 0;
});

// 方法
const initializeData = () => {
  // 初始化 dataZoom 范围，默认选择前面的时段
  const defaultWindowPercent =
    (windowLength.value / props.typicalRainfallData.length) * 100;
  dataZoomStart.value = 0;
  dataZoomEnd.value = Math.min(defaultWindowPercent, 100);
  allocationResult.value = [];

  nextTick(() => {
    initChart();
  });
};

const initChart = () => {
  if (!chartRef.value || props.typicalRainfallData.length === 0) return;

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose();
  }

  // 创建新的图表实例
  chartInstance = echarts.init(chartRef.value);

  // 处理图表数据
  const chartData = {
    times: [],
    rainfall: [],
    colors: [],
  };

  props.typicalRainfallData.forEach((item, index) => {
    // x轴显示时段编号
    chartData.times.push(`第${index + 1}时段`);
    chartData.rainfall.push(item.rainfallValue || 0);
    chartData.colors.push("#409EFF"); // 默认颜色
  });

  const option = {
    title: {
      text: "典型降雨时序图",
      left: "center",
      textStyle: {
        color: "#333",
        fontSize: 16,
      },
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        const dataIndex = params[0].dataIndex;
        const item = props.typicalRainfallData[dataIndex];
        const time = moment(item.time).format("YYYY-MM-DD HH:mm");
        return `第${dataIndex + 1}时段<br/>时间: ${time}<br/>降雨量: ${
          item.rainfallValue || 0
        }mm`;
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      containLabel: true,
    },
    dataZoom: [
      {
        type: "slider",
        show: true,
        xAxisIndex: [0],
        start: dataZoomStart.value,
        end: dataZoomEnd.value,
        height: 30,
        bottom: 10,
        brushSelect: false,
        handleSize: "80%",
        handleStyle: {
          color: "#409EFF",
        },
        textStyle: {
          color: "#666",
        },
        borderColor: "#ddd",
        fillerColor: "rgba(64, 158, 255, 0.2)",
        selectedDataBackground: {
          lineStyle: {
            color: "#409EFF",
          },
          areaStyle: {
            color: "rgba(64, 158, 255, 0.3)",
          },
        },
        moveHandleSize: 10,
        zoomLock: true, // 锁定缩放，只允许平移
        minSpan: (windowLength.value / props.typicalRainfallData.length) * 100, // 最小选择范围
        maxSpan: (windowLength.value / props.typicalRainfallData.length) * 100, // 最大选择范围
      },
    ],
    xAxis: {
      type: "category",
      data: chartData.times,
      axisLabel: {
        interval: 0,
        rotate: 45,
      },
    },
    yAxis: {
      type: "value",
      name: "降雨量(mm)",
      axisLabel: {
        formatter: "{value}",
      },
    },
    series: [
      {
        name: "降雨量",
        type: "bar",
        data: chartData.rainfall.map((value, index) => ({
          value,
          itemStyle: {
            color: chartData.colors[index],
          },
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };

  chartInstance.setOption(option);

  // 监听 dataZoom 事件
  chartInstance.on("dataZoom", (params) => {
    if (params.batch && params.batch[0]) {
      dataZoomStart.value = params.batch[0].start;
      dataZoomEnd.value = params.batch[0].end;
    } else {
      dataZoomStart.value = params.start;
      dataZoomEnd.value = params.end;
    }
  });

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize);
};

const handleCalculate = async () => {
  if (!canCalculate.value || !props.startTime || !props.endTime) return;

  const startIndex = selectedStartPeriod.value - 1; // 转换为数组索引
  const endIndex = selectedEndPeriod.value; // 不包含结束索引
  const selectedData = props.typicalRainfallData.slice(startIndex, endIndex);

  // 计算分配结果
  const result = selectedData.map((item, index) => {
    const serialNumber = startIndex + index + 1;
    return {
      [serialNumber]: item.rainfallValue,
    };
  });

  const res = await getDesignRainfall({
    totalRainfall: props.allocatedRainfall,
    hourlyRainfall: result,
    startTime: props.startTime,
    endTime: props.endTime,
  });

  if (res.code === 200) {
    allocationResult.value = res.rows || [];

    // 发送确认事件
    emit("confirm", allocationResult.value);
  }
};

const handleCancel = () => {
  dialogVisible.value = false;
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 生命周期
onMounted(() => {
  if (props.visible) {
    initializeData();
  }
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style lang="scss" scoped>
.rain-allocation-container {
  .rainfall-info {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 18px;
    }

    .allocation-params {
      color: #666;
      font-size: 14px;
    }
  }

  .chart-container {
    margin-bottom: 20px;

    .rainfall-chart {
      width: 100%;
      height: 400px;
    }
  }

  .selection-info {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #409eff;

    .info-item {
      margin-bottom: 8px;
      color: #666;
      font-size: 14px;

      &:last-child {
        margin-bottom: 0;
      }

      span {
        font-weight: 500;
      }
    }
  }

  .allocation-result {
    .result-info {
      color: #666;
      font-size: 14px;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }
}

.dialog-footer {
  text-align: right;
}

// dataZoom 样式优化
:deep(.echarts-for-react) {
  .echarts-for-react__content {
    height: 100% !important;
  }
}
</style>
