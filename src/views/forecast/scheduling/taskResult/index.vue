<!--调度成果管理-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="成果名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="预报流域" prop="forecastBasinCode">
        <el-tree-select
          v-model="queryParams.forecastBasinCode"
          :data="waterAreaList"
          check-strictly
          clearable
          :render-after-expand="false"
          placeholder="请选择流域"
        />
      </el-form-item>
      <el-form-item label="预报方案" prop="schemeId">
        <el-select
          v-model="queryParams.schemeId"
          placeholder="请选择方案"
          clearable
        >
          <el-option
            :label="item.schemeName"
            :value="item.id"
            v-for="(item, index) in taskList"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预报时间" prop="date">
        <el-date-picker
          v-model="queryParams.date"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="预报类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择">
          <el-option label="作业预报" :value="0" />
          <el-option label="滚动预报" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="成果状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择">
          <el-option label="草稿" :value="0" />
          <el-option label="计算中" :value="1" />
          <el-option label="已完成" :value="2" />
          <el-option label="计算失败" :value="3" />
          <el-option label="已发布" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList()" icon="Search"
          >查询</el-button
        >
        <el-button @click="resetQuery" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row> -->
    <div class="content">
      <div class="right-table">
        <div class="table-header">
          <el-button type="success" icon="CirclePlus" @click="openForm"
            >作业预报</el-button
          >
        </div>
        <el-table v-loading="loading" :data="workList" stripe>
          <el-table-column type="selection" width="40" />
          <el-table-column
            type="index"
            label="序号"
            width="65"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="name"
            label="成果名称"
            align="center"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            prop="basinName"
            label="预报类型"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <div v-if="scope.row.forecastType == 0">作业预报</div>
              <div v-if="scope.row.forecastType == 1">滚动预报</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="basinName"
            label="预报时间"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              {{ scope.row.forecastTime || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="countEndTime"
            label="计算完成时间"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              {{ scope.row.completeTime || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="basinName"
            label="发布时间"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              {{ scope.row.publishTime || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="basinName"
            label="成果状态"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <el-tag
                :type="getStatusConfig(scope.row.status).type"
                effect="dark"
              >
                {{ getStatusConfig(scope.row.status).text }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="280">
            <template #default="scope">
              <!-- 查看按钮 - 所有状态都可以查看 -->
              <el-button
                link
                type="primary"
                icon="View"
                @click="seeDetail(scope.row)"
                >查看</el-button
              >
              <!-- 预报按钮 - 草稿(0)、计算中(1)、已完成(2)、计算失败(3)可以预报 -->
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="editTask(scope.row)"
                v-if="
                  scope.row.status == 0 ||
                  scope.row.status == 1 ||
                  scope.row.status == 2 ||
                  scope.row.status == 3
                "
                >预报</el-button
              >
              <!-- 发布按钮 - 只有已完成(2)可以发布 -->
              <el-button
                link
                type="primary"
                icon="Upload"
                @click="upWork(scope.row)"
                v-if="scope.row.status == 2"
                >发布</el-button
              >
              <!-- 下架按钮 - 只有已发布(4)可以下架 -->
              <el-button
                link
                type="primary"
                icon="Bottom"
                @click="downWork(scope.row)"
                v-if="scope.row.status == 4"
                >下架</el-button
              >
              <!-- 删除按钮 - 草稿(0)、计算中(1)、已完成(2)、计算失败(3)可以删除，已发布(4)不能删除 -->
              <el-button
                link
                type="danger"
                icon="Delete"
                v-if="
                  scope.row.status == 0 ||
                  scope.row.status == 1 ||
                  scope.row.status == 2 ||
                  scope.row.status == 3
                "
                @click="handleDelete(scope.row.id)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="pageNum"
          v-model:limit="pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <TaskResultManage
      v-model:showTaskResultManage="showTaskResultManage"
      :manageStatus="manageStatus"
      :manageId="manageId"
      :taskList="taskList"
      @getList="getList"
    />

    <!-- 预报成果详情弹窗 -->
    <ForecastResultDetailDialog
      v-model:visible="showDetailDialog"
      :detail-data="detailData"
    />
  </div>
</template>

<script setup>
import {
  forecastResultList,
  forecastResultDetail,
  deleteForecastResult,
  publishForecastResult,
  downForecastResult,
  getForecastSchemeList,
} from "@/api/scheduling/index";
import { selectStlyList } from "@/api/watershed/ads";
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import moment from "moment/moment";
import TaskResultManage from "@/views/forecast/scheduling/manage/index.vue";
import ForecastResultDetailDialog from "./components/ForecastResultDetailDialog.vue";

defineOptions({
  name: "TaskResult",
});

const { proxy } = getCurrentInstance();
const router = useRouter();

const loading = ref(false);
const showSearch = ref(true);
let pageSize = ref(20);
let pageNum = ref(1);
let total = ref(0);

const data = reactive({
  taskList: [],
  workList: [],
  queryParams: {
    name: "",
    forecastBasinCode: "",
    schemeId: "",
    type: "",
    date: [],
    status: "",
    date: [
      moment().subtract(7, "days").format("YYYY-MM-DD 08:00:00"),
      moment().format("YYYY-MM-DD 23:59:59"),
    ],
  },
  waterAreaList: [],
});

const { queryParams, waterAreaList, taskList, workList } = toRefs(data);

const upWork = async (row) => {
  const res = await publishForecastResult(row.id); // 发布状态改为4
  if (res.code === 200) {
    proxy.$modal.msgSuccess("任务发布成功");
    getList();
  }
};
const downWork = async (row) => {
  const res = await downForecastResult(row.id); // 下架后回到已完成状态2
  if (res.code === 200) {
    proxy.$modal.msgSuccess("任务下架成功");
    getList();
  }
};
const transformDataForTreeSelect = (data) => {
  // 递归地转换数据以匹配 el-tree-select 的需求
  return data.map((item) => ({
    label: item.data.name, // 使用 'name' 属性作为标签
    value: item.data.forecastBasinCode, // 使用 'forecastBasinCode' 属性作为值
    children: item.children ? transformDataForTreeSelect(item.children) : [], // 递归转换子节点
  }));
};
const resetQuery = () => {
  proxy.resetForm("queryRef");
  pageNum.value = 1;
  pageSize.value = 20;
  getList();
};

// 获取流域列表
const getAllWater = async () => {
  let res = await selectStlyList({ pageNum: 1, pageSize: 999 });
  if (res.code === 200) {
    data.waterAreaList = transformDataForTreeSelect(res.data);
  }
};
const getAllTask = async () => {
  let res = await getForecastSchemeList({
    pageNum: 1,
    pageSize: 99999,
  });
  if (res.code === 200) {
    data.taskList = res.rows;
  }
};
const getList = async () => {
  loading.value = true;
  let res = await forecastResultList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    name: data.queryParams.name,
    forecastBasinCode: data.queryParams.forecastBasinCode,
    schemeId: data.queryParams.schemeId,
    type: data.queryParams.type,
    startTime: data.queryParams.date[0],
    endTime: data.queryParams.date[1],
    status: data.queryParams.status,
  });
  if (res.code === 200) {
    data.workList = res.rows;
    total.value = res.total;
  } else {
    data.workList = [];
    total.value = 0;
  }
  loading.value = false;
};
const handleDelete = async (id) => {
  ElMessageBox.confirm("确认要删除预报任务及成果信息吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await deleteForecastResult(id);
    if (res.code === 200) {
      proxy.$modal.msgSuccess("删除成功");
      getList();
    }
  });
};

// 状态配置映射
const getStatusConfig = (status) => {
  const statusMap = {
    0: { type: "info", text: "草稿" },
    1: { type: "warning", text: "计算中" },
    2: { type: "", text: "已完成" },
    3: { type: "danger", text: "计算失败" },
    4: { type: "success", text: "已发布" },
  };
  return statusMap[status] || { type: "info", text: "未知" };
};

const showTaskResultManage = ref(false);
const manageStatus = ref("add");
const manageId = ref("");

const showDetailDialog = ref(false);
const detailData = ref({});

const seeDetail = async (data) => {
  const res = await forecastResultDetail(data.id);
  if (res.code === 200) {
    detailData.value = res.data;
    showDetailDialog.value = true;
  } else {
    proxy.$modal.msgError(res.msg || "获取详情失败");
  }
};
const editTask = (data) => {
  manageStatus.value = "edit";
  manageId.value = data.id;
  showTaskResultManage.value = true;
};

const openForm = () => {
  showTaskResultManage.value = true;
  manageStatus.value = "add";
};

onMounted(() => {
  getList();
  getAllTask();
  getAllWater();
});
</script>
<style scoped lang="scss">
.card-box {
  padding-right: 0;
  padding-left: 0;
  margin-bottom: 5px;
}

.exceed {
  width: 95%;
  margin: 2px auto;
  height: 150px;
  display: flex;
  color: #555;

  .items {
    flex: 1;
    height: 100%;
    text-align: center;

    .value {
      margin: 24px 0;
      font-size: 18px;
    }

    .name {
      margin: 10px 0;
      color: #555;
      font-size: 16px;
    }
  }
}
</style>
