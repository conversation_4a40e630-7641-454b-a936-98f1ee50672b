<template>
  <el-dialog
    :title="title"
    :model-value="visible"
    @update:model-value="handleClose"
    width="90vw"
    destroy-on-close
    class="forecast-detail-dialog"
  >
    <div class="detail-container" v-loading="loading">
      <!-- 基础信息 -->
      <div class="info-section">
        <div class="section-title">基础信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">预报流域:</span>
            <span class="value">{{ detailData.forecastBasinName || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">预报时间:</span>
            <span class="value">{{ detailData.forecastTime || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">预热期:</span>
            <span class="value">{{ detailData.warmupPeriod || "-" }}小时</span>
          </div>
          <div class="info-item">
            <span class="label">预见期:</span>
            <span class="value"
              >{{ detailData.forecastHorizon || "-" }}小时</span
            >
          </div>
          <div class="info-item">
            <span class="label">降雨总量:</span>
            <span class="value">{{ detailData.totalRainfall || "-" }}mm</span>
          </div>
        </div>
      </div>

      <!-- 概化图和图表切换区域 -->
      <div class="main-content">
        <!-- 左侧：概化图 -->
        <div class="left-panel">
          <DragLegend
            :nodes="nodes"
            :edges="edges"
            :nodeValue="nodeValue"
            showType="readOnly"
            ref="vflow"
          />
        </div>

        <!-- 右侧：图表和表格 -->
        <div class="right-panel">
          <!-- 预报节点选择 -->
          <div class="node-selector">
            <div class="selector-row">
              <span class="selector-label">预报节点:</span>
              <el-select
                v-model="selectedNodeType"
                placeholder="请选择节点类型"
                @change="handleNodeTypeChange"
                style="width: 150px; margin-right: 10px"
              >
                <el-option label="小流域" value="subBasin" />
                <el-option label="河道断面" value="section" />
                <el-option label="水库" value="reservoir" />
              </el-select>

              <el-select
                v-model="selectedNode"
                placeholder="请选择具体节点"
                @change="handleNodeChange"
                style="width: 200px; margin-right: 10px"
              >
                <el-option
                  v-for="node in currentNodeList"
                  :key="node.nodeOrder"
                  :label="node.nodeName || node.basinName || node.reservoirName"
                  :value="node.nodeOrder"
                />
              </el-select>

              <!-- 图表/表格切换 -->
              <el-button-group>
                <el-button
                  :type="viewMode === 'chart' ? 'primary' : ''"
                  @click="viewMode = 'chart'"
                  icon="TrendCharts"
                >
                  图
                </el-button>
                <el-button
                  :type="viewMode === 'table' ? 'primary' : ''"
                  @click="viewMode = 'table'"
                  icon="Grid"
                >
                  表
                </el-button>
              </el-button-group>
            </div>
          </div>

          <!-- 图表显示区域 -->
          <div class="chart-container" v-show="viewMode === 'chart'">
            <div v-if="!selectedNode" class="empty-data">
              <el-empty description="请选择预报节点查看图表" />
            </div>
            <div
              v-else-if="
                !currentNodeData ||
                !currentNodeData.list ||
                currentNodeData.list.length === 0
              "
              class="empty-data"
            >
              <el-empty description="暂无图表数据" />
            </div>
            <div
              v-else
              id="forecastChart"
              style="width: 100%; height: 400px"
            ></div>
          </div>

          <!-- 表格显示区域 -->
          <div class="table-container" v-show="viewMode === 'table'">
            <div v-if="!selectedNode" class="empty-data">
              <el-empty description="请选择预报节点查看表格" />
            </div>
            <div
              v-else-if="
                !currentNodeData ||
                !currentNodeData.list ||
                currentNodeData.list.length === 0
              "
              class="empty-data"
            >
              <el-empty description="暂无表格数据" />
            </div>
            <template v-else>
              <!-- 小流域表格 -->
              <el-table
                v-if="selectedNodeType === 'subBasin'"
                :data="currentTableData"
                border
                stripe
                size="small"
                max-height="400px"
                highlight-current-row
              >
                <el-table-column label="时间" width="180" align="center">
                  <template #default="scope">
                    {{ scope.row.recordTime || scope.row.time || "-" }}
                  </template>
                </el-table-column>
                <el-table-column label="面雨量(mm)" align="center">
                  <template #default="scope">
                    {{ scope.row.rainfall || 0 }}
                  </template>
                </el-table-column>
                <el-table-column label="出流量(m³/s)" align="center">
                  <template #default="scope">
                    {{ scope.row.outflow || scope.row.qo || 0 }}
                  </template>
                </el-table-column>
              </el-table>

              <!-- 河道断面表格 -->
              <el-table
                v-if="selectedNodeType === 'section'"
                :data="currentTableData"
                border
                stripe
                size="small"
                max-height="400px"
                highlight-current-row
              >
                <el-table-column label="时间" width="180" align="center">
                  <template #default="scope">
                    {{ scope.row.time || "-" }}
                  </template>
                </el-table-column>
                <el-table-column label="水位(m)" align="center">
                  <template #default="scope">
                    {{ scope.row.waterLevel || 0 }}
                  </template>
                </el-table-column>
                <el-table-column label="流量(m³/s)" align="center">
                  <template #default="scope">
                    {{ scope.row.discharge || 0 }}
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import {
  ref,
  computed,
  watch,
  nextTick,
  onUnmounted,
  getCurrentInstance,
} from "vue";
import * as echarts from "echarts";
import DragLegend from "./dragLegend/index.vue";
import moment from "moment";
import { useForecastScheme } from "@/composables/useForecastScheme";

defineOptions({
  name: "ForecastResultDetailDialog",
});

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  detailData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible"]);

const { getTaskFlowData } = useForecastScheme();

// 响应式数据
const loading = ref(false);
const selectedNodeType = ref("subBasin"); // 默认选择小流域
const selectedNode = ref("");
const viewMode = ref("chart"); // chart 或 table

// DragLegend 组件需要的数据
const nodes = ref([]);
const edges = ref([]);
const nodeValue = ref([]);

// 图表实例
let chartInstance = null;

// 计算当前节点列表
const currentNodeList = computed(() => {
  if (!props.detailData) return [];

  let list = [];
  switch (selectedNodeType.value) {
    case "subBasin":
      list = props.detailData.subBasinList || [];
      break;
    case "section":
      list = props.detailData.riverSectionList || [];
      break;
    case "reservoir":
      list = props.detailData.riverReservoirList || [];
      break;
    default:
      list = [];
  }

  // 过滤掉空值和没有 nodeOrder 的项
  return list.filter((item) => item && item.nodeOrder);
});

// 计算当前选中节点的数据
const currentNodeData = computed(() => {
  if (!selectedNode.value || !currentNodeList.value.length) return null;
  return (
    currentNodeList.value.find(
      (node) => node && node.nodeOrder === selectedNode.value
    ) || null
  );
});

// 计算表格数据
const currentTableData = computed(() => {
  if (!currentNodeData.value || !currentNodeData.value.list) return [];

  // 按时间倒序排序
  return [...currentNodeData.value.list].sort((a, b) => {
    const timeA = a.recordTime || a.time;
    const timeB = b.recordTime || b.time;
    if (!timeA || !timeB) return 0;
    return new Date(timeB) - new Date(timeA);
  });
});

const title = ref("");
// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      title.value = props.detailData.name;
      nextTick(() => {
        initializeDialog();
      });
    } else {
      // 弹窗关闭时清理
      cleanup();
    }
  }
);

// 监听节点类型变化
const handleNodeTypeChange = () => {
  selectedNode.value = "";
  // 自动选择第一个节点
  if (currentNodeList.value.length > 0) {
    const firstValidNode = currentNodeList.value.find(
      (node) => node && node.nodeOrder
    );
    if (firstValidNode) {
      selectedNode.value = firstValidNode.nodeOrder;

      nextTick(() => {
        initChart();
      });
    }
  }
};

// 监听节点变化
const handleNodeChange = () => {
  if (viewMode.value === "chart") {
    nextTick(() => {
      initChart();
    });
  }
};

// 监听视图模式变化
watch(
  () => viewMode.value,
  (newVal) => {
    if (newVal === "chart") {
      nextTick(() => {
        initChart();
      });
    }
  }
);
const { proxy } = getCurrentInstance();
// 构建 DragLegend 组件需要的数据
const buildDragLegendData = async () => {
  nodes.value = [];
  edges.value = [];
  nodeValue.value = [];
  if (!props.detailData) {
    return;
  }

  const data = await getTaskFlowData(props.detailData);
  if (data) {
    nodes.value = data.nodes;
    edges.value = data.edges;
    nodeValue.value = data.nodeValue;
  }

  // 等待DOM更新后显示流程图
  proxy.$nextTick(() => {
    if (proxy.$refs.vflow && proxy.$refs.vflow.showView) {
      proxy.$refs.vflow.showView();
    }
  });
};

// 初始化弹窗
const initializeDialog = () => {
  // 重置选择
  selectedNode.value = "";

  // 构建 DragLegend 需要的数据
  buildDragLegendData();

  // 默认选择第一个小流域
  if (
    props.detailData &&
    props.detailData.subBasinList &&
    props.detailData.subBasinList.length > 0
  ) {
    const validSubBasins = props.detailData.subBasinList.filter(
      (item) => item && item.nodeOrder
    );
    if (validSubBasins.length > 0) {
      selectedNodeType.value = "subBasin";
      selectedNode.value = validSubBasins[0].nodeOrder;
    }
  } else if (
    props.detailData &&
    props.detailData.riverSectionList &&
    props.detailData.riverSectionList.length > 0
  ) {
    const validSections = props.detailData.riverSectionList.filter(
      (item) => item && item.nodeOrder
    );
    if (validSections.length > 0) {
      selectedNodeType.value = "section";
      selectedNode.value = validSections[0].nodeOrder;
    }
  } else if (
    props.detailData &&
    props.detailData.riverReservoirList &&
    props.detailData.riverReservoirList.length > 0
  ) {
    const validReservoirs = props.detailData.riverReservoirList.filter(
      (item) => item && item.nodeOrder
    );
    if (validReservoirs.length > 0) {
      selectedNodeType.value = "reservoir";
      selectedNode.value = validReservoirs[0].nodeOrder;
    }
  }

  // 初始化图表
  if (viewMode.value === "chart" && selectedNode.value) {
    nextTick(() => {
      initChart();
    });
  }
};

// 初始化图表
const initChart = () => {
  const chartContainer = document.getElementById("forecastChart");
  if (!chartContainer || !currentNodeData.value) return;

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }

  // 创建新图表实例
  chartInstance = echarts.init(chartContainer);

  // 准备图表配置
  let option = {};

  if (selectedNodeType.value === "subBasin") {
    option = createSubBasinChartOption();
  } else if (selectedNodeType.value === "section") {
    option = createSectionChartOption();
  }

  if (option && Object.keys(option).length > 0) {
    nextTick(() => {
      chartInstance.setOption(option);
    });
  }
};

// 创建小流域图表配置
const createSubBasinChartOption = () => {
  if (!currentNodeData.value || !currentNodeData.value.list) return {};

  const data = currentNodeData.value.list;
  const times = data.map((item) => {
    const time = item.recordTime || item.time;
    if (!time) return "";
    // 格式化时间显示
    return moment(time).format("YYYY-MM-DD HH:mm");
  });
  const rainfall = data.map((item) => item.rainfall || 0);
  const outflow = data.map((item) => item.outflow || item.qo || 0);

  return {
    title: {
      text: `${
        currentNodeData.value.basinName || currentNodeData.value.nodeName
      }`,
      left: "center",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
      },
    },
    legend: {
      data: ["降雨量", "出流量"],
      top: 30,
    },
    grid: {
      top: 80,
      bottom: 60,
      left: 60,
      right: 60,
    },
    xAxis: {
      type: "category",
      data: times,
      axisLabel: {
        // rotate: 45,
      },
    },
    yAxis: [
      {
        type: "value",
        name: "降雨量(mm)",
        position: "left",
      },
      {
        type: "value",
        name: "出流量(m³/s)",
        position: "right",
      },
    ],
    series: [
      {
        name: "降雨量",
        type: "bar",
        yAxisIndex: 0,
        data: rainfall,
        itemStyle: {
          color: "#5470c6",
        },
      },
      {
        name: "出流量",
        type: "line",
        yAxisIndex: 1,
        data: outflow,
        itemStyle: {
          color: "#91cc75",
        },
        lineStyle: {
          width: 2,
        },
      },
    ],
  };
};

// 创建河道断面图表配置
const createSectionChartOption = () => {
  if (!currentNodeData.value || !currentNodeData.value.list) return {};

  const data = currentNodeData.value.list;
  const times = data.map((item) => {
    const time = item.recordTime || item.time;
    if (!time) return "";
    // 格式化时间显示
    return moment(time).format("YYYY-MM-DD HH:mm");
  });
  const waterLevel = data.map((item) => item.waterLevel || 0);
  const discharge = data.map((item) => item.discharge || 0);

  return {
    title: {
      text: `${
        currentNodeData.value.sectionIdentifier ||
        currentNodeData.value.nodeName
      }`,
      left: "center",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
      },
    },
    legend: {
      data: ["水位", "流量"],
      top: 30,
    },
    grid: {
      top: 80,
      bottom: 60,
      left: 60,
      right: 60,
    },
    xAxis: {
      type: "category",
      data: times,
      axisLabel: {
        // rotate: 45,
      },
    },
    yAxis: [
      {
        type: "value",
        name: "水位(m)",
        position: "left",
      },
      {
        type: "value",
        name: "流量(m³/s)",
        position: "right",
      },
    ],
    series: [
      {
        name: "水位",
        type: "line",
        yAxisIndex: 0,
        data: waterLevel,
        itemStyle: {
          color: "#5470c6",
        },
        lineStyle: {
          width: 2,
        },
      },
      {
        name: "流量",
        type: "line",
        yAxisIndex: 1,
        data: discharge,
        itemStyle: {
          color: "#ee6666",
        },
        lineStyle: {
          width: 2,
        },
      },
    ],
  };
};

// 清理资源
const cleanup = () => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
};

// 关闭弹窗
const handleClose = () => {
  emit("update:visible", false);
};

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>

<style scoped lang="scss">
.forecast-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
}

.detail-container {
  min-height: 600px;
}

.info-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 30px;
      height: 2px;
      background-color: #409eff;
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;

    .info-item {
      display: flex;
      align-items: center;

      .label {
        font-weight: 500;
        color: #666;
        min-width: 80px;
      }

      .value {
        color: #333;
        margin-left: 10px;
      }
    }
  }
}

.main-content {
  display: flex;
  gap: 20px;
  height: 500px;

  .left-panel {
    flex: 1;
    height: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
    overflow: hidden;
  }

  .right-panel {
    flex: 1;

    .node-selector {
      margin-bottom: 20px;

      .selector-row {
        display: flex;
        align-items: center;
        gap: 10px;

        .selector-label {
          font-weight: 500;
          color: #666;
          min-width: 80px;
        }
      }
    }

    .chart-container,
    .table-container {
      height: calc(100% - 80px);
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      background-color: #fff;

      .empty-data {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
