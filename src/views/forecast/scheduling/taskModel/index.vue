<!--调度成果管理-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="模型名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="类型" prop="isdo">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in modelType"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="isdo">
        <el-select
          v-model="queryParams.state"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="form-item-button">
        <el-button type="primary" icon="Search" @click="getList()"
          >查询</el-button
        >
        <el-button icon="Refresh" type="primary" plain @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="content content-table">
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="openForm">新增</el-button>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </div>
      <el-table
        v-loading="loading"
        :data="lyList"
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column type="selection" width="40" />
        <el-table-column
          type="index"
          label="序号"
          width="65"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="名称"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="type"
          label="类型"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <div>{{ modelType[scope.row.type + 1].label }}</div>
          </template>
        </el-table-column>
        <el-table-column label="提供方" align="center" prop="provider">
        </el-table-column>
        <el-table-column
          prop="state"
          label="状态"
          :show-overflow-tooltip="true"
          align="center"
        >
          <template #default="scope">
            <span v-if="scope.row.state === 0" style="color: #e6a23c"
              >停用中</span
            >
            <span v-if="scope.row.state == 1" style="color: #409eff"
              >启用中</span
            >
          </template>
        </el-table-column>
        <el-table-column label="描述" align="center" prop="remark">
        </el-table-column>
        <el-table-column
          label="操作"
          align="left"
          width="210"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Check"
              @click="changeTaskModelStatus(scope.row.id, 1)"
              v-if="scope.row.state === 0"
              >启用</el-button
            >
            <el-button
              link
              type="warning"
              icon="Close"
              @click="changeTaskModelStatus(scope.row.id, 0)"
              v-else
              >停用</el-button
            >
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleAccept(scope.row)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="pageNum"
        v-model:limit="pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 添加 -->
    <el-dialog
      :title="formStatus == 1 ? '新增模型' : '编辑模型'"
      v-model="dialogShow"
      width="30%"
      @close="resetForm()"
    >
      <el-form :model="addForm" :rules="rules" ref="addFormRef">
        <el-form-item label="模型类型" label-width="100" prop="type">
          <el-select
            v-model="addForm.type"
            placeholder="请选择模型类型"
            clearable
            style="width: 80%"
          >
            <template v-for="dict in modelType" :key="dict.value">
              <el-option
                :label="dict.label"
                v-if="dict.label != '全部'"
                :value="dict.value"
              />
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="模型名称" label-width="100" prop="name">
          <el-input
            v-model="addForm.name"
            placeholder="请输入模型名称"
            style="width: 80%"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="提供方" label-width="100" prop="provider">
          <el-input
            v-model="addForm.provider"
            placeholder="请输入提供方名称"
            clearable
            style="width: 80%"
          ></el-input>
        </el-form-item>
        <el-form-item label="模型描述" label-width="100" prop="remark">
          <el-input
            type="textarea"
            :rows="10"
            v-model="addForm.remark"
            placeholder="请填写模型描述"
            style="width: 80%"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="reset()">取消</el-button>
          <el-button type="primary" v-if="formStatus == 1" @click="submitForm">
            确定
          </el-button>
          <el-button type="primary" v-else @click="updateForm">
            修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  taskModelList,
  addTaskModel,
  changeTaskModel,
  delTaskModel,
  updateTaskModel,
} from "@/api/scheduling/index";
import { onMounted } from "vue";

defineOptions({
  name: "TaskModel",
});

const { proxy } = getCurrentInstance();
onMounted(() => {
  getList();
});
const lyList = ref([]);
// 详情里面的列表
//预警发布情况列表
const loading = ref(false);
const showSearch = ref(true);
let formStatus = ref(1);
let dialogShow = ref(false);
let pageSize = ref(20);
let pageNum = ref(1);
let total = ref(0);
let addForm = ref({
  id: "",
  type: "",
  name: "",
  remark: "",
  provider: "",
});
const data = reactive({
  status: [
    {
      label: "全部",
      value: "",
    },
    {
      label: "启用",
      value: 1,
    },
    {
      label: "停用",
      value: 0,
    },
  ],
  modelType: [
    {
      label: "全部",
      value: "",
    },
    {
      label: "水文模型",
      value: 0,
    },
    {
      label: "一维模型",
      value: 1,
    },
    {
      label: "二维模型",
      value: 2,
    },
    {
      label: "水质模型",
      value: 3,
    },
  ],
  queryParams: {
    name: "",
    type: "",
    state: "",
  },
  rules: {
    type: [{ required: true, message: "请选择模型类型", trigger: "change" }],
    name: [{ required: true, message: "请输入模型名称", trigger: "blur" }],
  },
});

const { queryParams, rules, status, modelType } = toRefs(data);
const resetQuery = () => {
  data.queryParams = {
    name: "",
    type: "",
    state: "",
  };
  pageNum.value = 1;
  pageSize.value = 20;
  getList();
};
const getList = async () => {
  loading.value = true;
  let res = await taskModelList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    name: queryParams.value.name,
    type: queryParams.value.type,
    state: queryParams.value.state,
  });
  if (res.data && res.data.length > 0) {
    lyList.value = res.data;
    total.value = res.total;
  } else {
    lyList.value = [];
    total.value = 0;
  }
  loading.value = false;
};
const handleAccept = async (data) => {
  formStatus.value = 2;
  addForm.value.id = data.id;
  addForm.value.type = data.type;
  addForm.value.name = data.name;
  addForm.value.remark = data.remark;
  addForm.value.provider = data.provider;
  dialogShow.value = true;
};
const handleDelete = async (id) => {
  proxy
    .$confirm("确定删除该模型吗?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(async () => {
      let res = await delTaskModel(id);
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
      } else {
        proxy.$modal.msgError(res.msg);
      }
      getList();
    });
};
const changeTaskModelStatus = async (id, state) => {
  let res = await changeTaskModel({ id: id, state: state });
  if (res.code == 200) {
    proxy.$modal.msgSuccess(res.msg);
  } else {
    proxy.$modal.msgError(res.msg);
  }
  getList();
};
const submitForm = async () => {
  proxy.$refs.addFormRef.validate(async (valid) => {
    if (valid) {
      delete addForm.value.id;
      let res = await addTaskModel({ ...addForm.value });
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
        dialogShow.value = false;
        reset();
        getList();
      } else {
        proxy.$modal.msgError(res.msg);
      }
    } else {
    }
  });
};
const updateForm = async () => {
  proxy.$refs.addFormRef.validate(async (valid) => {
    if (valid) {
      let res = await updateTaskModel(addForm.value.id, { ...addForm.value });
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
        dialogShow.value = false;
        reset();
        getList();
      } else {
        proxy.$modal.msgError(res.msg);
      }
    }
  });
};
const openForm = () => {
  dialogShow.value = true;
  formStatus.value = 1;
  proxy.$refs.addFormRef.resetFields();
};
/** 表单重置 */
function reset() {
  dialogShow.value = false;
  addForm.value = {
    id: "",
    type: "",
    name: "",
    remark: "",
    provider: [],
  };
}
</script>
<style scoped lang="scss">
.card-box {
  padding-right: 0;
  padding-left: 0;
  margin-bottom: 5px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exceed {
  width: 95%;
  margin: 2px auto;
  height: 150px;
  display: flex;
  color: #555;

  .items {
    flex: 1;
    height: 100%;
    text-align: center;

    .value {
      margin: 24px 0;
      font-size: 18px;
    }

    .name {
      margin: 10px 0;
      color: #555;
      font-size: 16px;
    }
  }
}
</style>
