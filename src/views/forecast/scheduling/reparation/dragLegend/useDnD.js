import { useVueFlow } from "@vue-flow/core";
import { ref, watch } from "vue";

let id = 0;

/**
 * @returns {string} - 返回一个唯一的ID
 */
const getId = () => {
  //随机生成16位随机字符串
  id = Math.random().toString(36).substring(2, 8);

  return id;
}

/**
 * 在实际应用中，应该避免在全局作用域中创建refs，因为它们可能无法正确清理。
 * @type {{draggedType: Ref<string|null>, isDragOver: Ref<boolean>, isDragging: Ref<boolean>}}
 */
const state = {
  /**
   * 正在拖拽的节点类型
   */
  draggedType: ref(null),
  isDragOver: ref(false),
  isDragging: ref(false),
  className: ref(null),
  label: ref(null),
  nodeType: ref(null),
};

export default function useDragAndDrop() {
  const { draggedType, isDragOver, isDragging, className, label, nodeType } =
    state;

  const { addNodes, screenToFlowCoordinate, onNodesInitialized, updateNode } =
    useVueFlow();

  watch(isDragging, (dragging) => {
    document.body.style.userSelect = dragging ? "none" : "";
  });

  function onDragStart(event, type) {
    className.value = event.target.className;
    label.value = event.target.getAttribute("label");
    nodeType.value = event.target.getAttribute("type");
    if (event.dataTransfer) {
      event.dataTransfer.setData("application/vueflow", type);
      event.dataTransfer.effectAllowed = "move";
    }

    draggedType.value = type;
    isDragging.value = true;

    document.addEventListener("drop", onDragEnd);
  }

  /**
   * 处理拖拽悬停事件
   *
   * @param {DragEvent} event
   */
  function onDragOver(event) {
    event.preventDefault();
    if (draggedType.value) {
      isDragOver.value = true;

      if (event.dataTransfer) {
        event.dataTransfer.dropEffect = "move";
      }
    }
  }

  function onDragLeave() {
    isDragOver.value = false;
  }

  function onDragEnd() {
    isDragging.value = false;
    isDragOver.value = false;
    draggedType.value = null;
    document.removeEventListener("drop", onDragEnd);
  }

  /**
   * 处理拖拽放置事件
   *
   * @param {DragEvent} event
   */
  function onDrop(event) {
    const position = screenToFlowCoordinate({
      x: event.clientX,
      y: event.clientY,
    });
    const nodeId = getId();

    const newNode = {
      id: nodeId,
      type: draggedType.value,
      position,
      class: className.value,
      data: { label: label.value, nodeId: nodeId, nodeType: nodeType.value },
    };

    /**
     * 拖拽放置后调整节点位置，使其以鼠标为中心
     *
     * 我们可以在回调中监听事件，并在调用后移除事件监听器。
     */
    const { off } = onNodesInitialized(() => {
      updateNode(nodeId, (node) => ({
        position: {
          x: node.position.x - node.dimensions.width / 2,
          y: node.position.y - node.dimensions.height / 2,
        },
      }));

      off();
    });

    addNodes(newNode);
  }

  return {
    draggedType,
    isDragOver,
    isDragging,
    onDragStart,
    onDragLeave,
    onDragOver,
    onDrop,
  };
}
