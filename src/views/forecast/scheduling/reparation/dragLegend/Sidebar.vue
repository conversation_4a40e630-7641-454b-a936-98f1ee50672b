<script setup>
import useDragAndDrop from "./useDnD";

const emits = defineEmits([ "resetNode", "deleteNode"]);
const { onDragStart } = useDragAndDrop();

const deleteNode = () => {
  emits("deleteNode");
};
const resetNode = () => {
  emits("resetNode");
};
</script>

<template>
  <div class="floating-sidebar">
    <!-- 节点工具栏 -->
    <div class="node-toolbar">
      <div class="node-items">
        <div
          class="node-item area1"
          label="小流域节点"
          :draggable="true"
          @dragstart="onDragStart($event, 'default')"
          type="subBasin"
          title="拖拽添加小流域节点"
        >
          <span class="node-text">小流域</span>
        </div>
        <div
          class="node-item area1"
          label="河道断面节点"
          :draggable="true"
          @dragstart="onDragStart($event, 'default')"
          type="section"
          title="拖拽添加河道断面节点"
        >
          <span class="node-text">河道断面</span>
        </div>
        <div
          class="node-item area1"
          label="水库节点"
          :draggable="true"
          @dragstart="onDragStart($event, 'default')"
          type="reservoir"
          title="拖拽添加水库节点"
        >
          <span class="node-text">水库</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <el-button
        type="warning"
        size="small"
        @click="deleteNode"
        class="action-btn"
      >
        删除
      </el-button>
      <el-button size="small" @click="resetNode" class="action-btn">
        重置
      </el-button>
    </div>
  </div>
</template>
<style scoped lang="scss">
.floating-sidebar {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 16px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

.node-toolbar {
  .node-items {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .node-item {
    position: relative;
    cursor: grab;
    transition: all 0.2s ease-in-out;
    user-select: none;
    width: 60px;
    height: 32px;
    background-color: #e1f3ff;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    border: 2px solid #5cadde;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    font-weight: 500;

    &:hover {
      transform: scale(1.05);
    }

    &:active {
      cursor: grabbing;
      transform: scale(0.95);
    }

    .node-text {
      font-size: 12px;
      font-weight: 500;
      color: #374151;
      white-space: nowrap;
    }

    // 圆形节点样式
    &.rounds {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  padding-top: 8px;
  border-top: 1px solid rgba(229, 231, 235, 0.8);

  .action-btn {
    font-size: 12px;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .floating-sidebar {
    left: 10px;
    right: 10px;
    transform: none;
    min-width: auto;
    bottom: 10px;
  }

  .node-toolbar .node-items {
    gap: 6px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 6px;
  }
}
</style>
