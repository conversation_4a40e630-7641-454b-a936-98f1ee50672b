<script setup>
import { ref, onMounted, getCurrentInstance, nextTick } from "vue";
import { VueFlow, useVueFlow, MarkerType } from "@vue-flow/core";
import { Controls } from "@vue-flow/controls";
import { selectRsList } from "@/api/watershed/ads";
import {
  getRiverSectionList,
  getSubBasinList,
  getSubBasinWeight,
} from "@/api/watershed";
import DropzoneBackground from "./DropzoneBackground.vue";
import Sidebar from "./Sidebar.vue";
import useDragAndDrop from "./useDnD";

const { proxy } = getCurrentInstance();
const {
  onConnect,
  onInit,
  addEdges,
  removeNodes,
  updateNode,
  fitView,
  setNodes,
  setEdges,
} = useVueFlow();
const emit = defineEmits(["getNodeValue"]);
const props = defineProps(["nodes", "edges", "nodeValue", "showType"]);
const { onDragOver, onDrop, onDragLeave, isDragOver } = useDragAndDrop();
const show = ref(false);
const defaultNodes = ref([]);
const defaultEdges = ref([]);
const activeIndex = ref(null);
const selectNode = ref(null);
// 小流域节点数据
const subBasinList = ref([]);
// 水库节点数据
const riverReservoirList = ref([]);
// 河道断面节点数据
const riverSectionList = ref([]);
const nodesChange = (e) => {
  console.log("nodesChange", e);
  let { type, selected } = e[0];

  if (type == "add") {
    let { item } = e[0];
    if (item.data.nodeType == "subBasin") {
      subBasinList.value.push({
        type: 1,
        nodeType: "subBasin",
        nodeOrder: item.data.nodeId,
        label: item.data.label,
        // 小流域代码
        basinCode: "",
        // 小流域标识
        basinIdentifier: "",
        // 小流域雨量站关联信息
        list: [],
      });
    }
    if (item.data.nodeType == "reservoir") {
      riverReservoirList.value.push({
        type: 2,
        nodeType: "reservoir",
        nodeOrder: item.data.nodeId,
        label: item.data.label,
        // 水库代码
        reservoirCode: "",
        // 水库标识
        reservoirIdentifier: "",
        // 水库出库方式
        releaseMethod: "",
        // 水库出库流量
        releaseFlow: "",
      });
    }
    if (item.data.nodeType == "section") {
      riverSectionList.value.push({
        type: 5,
        nodeType: "section",
        nodeOrder: item.data.nodeId,
        label: item.data.label,
        // 河道断面代码
        riverSectionCode: "",
        // 是否为入流断面
        isInflowSection: 1,
        // 断面标识
        sectionIdentifier: "",
      });
    }
  } else if (type == "remove") {
    let { id } = e[0];

    // 如果删除的是当前正在编辑的节点，重置相关状态
    if (dbclickNodeId.value === id) {
      show.value = false;
      dbclickNodeId.value = null;
      activeIndex.value = null;
    }

    // 如果删除的是当前选中的节点，清空选中状态
    if (selectNode.value === id) {
      selectNode.value = null;
    }

    // 从各个列表中删除对应的节点数据
    let index = subBasinList.value.findIndex((el) => el.nodeOrder == id);
    if (index != -1) {
      subBasinList.value.splice(index, 1);
      // 如果删除的节点影响了当前编辑的索引，需要重新计算
      if (
        activeIndex.value !== null &&
        activeIndex.value >= index &&
        flowType.value === "subBasin"
      ) {
        activeIndex.value =
          activeIndex.value > 0 ? activeIndex.value - 1 : null;
      }
    }

    let index2 = riverReservoirList.value.findIndex((el) => el.nodeOrder == id);
    if (index2 != -1) {
      riverReservoirList.value.splice(index2, 1);
      // 如果删除的节点影响了当前编辑的索引，需要重新计算
      if (
        activeIndex.value !== null &&
        activeIndex.value >= index2 &&
        flowType.value === "reservoir"
      ) {
        activeIndex.value =
          activeIndex.value > 0 ? activeIndex.value - 1 : null;
      }
    }

    let index3 = riverSectionList.value.findIndex((el) => el.nodeOrder == id);
    if (index3 != -1) {
      riverSectionList.value.splice(index3, 1);
      // 如果删除的节点影响了当前编辑的索引，需要重新计算
      if (
        activeIndex.value !== null &&
        activeIndex.value >= index3 &&
        flowType.value === "section"
      ) {
        activeIndex.value =
          activeIndex.value > 0 ? activeIndex.value - 1 : null;
      }
    }
  }
  if (type === "select") {
    if (selected) {
      const selectedNodes = e.filter((item) => item.selected);
      if (selectedNodes.length > 0) {
        const { id } = selectedNodes[0];
        selectNode.value = id;
      }
    } else {
      // 如果没有选中的节点，清空选中状态
      selectNode.value = null;
    }
  }
};

const rules1 = {
  label: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  basinCode: [{ required: true, message: "请选择小流域", trigger: "change" }],
  basinIdentifier: [
    { required: true, message: "请输入小流域标识", trigger: "blur" },
  ],
};
const rules2 = {
  label: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  reservoirCode: [{ required: true, message: "请选择水库", trigger: "change" }],
  reservoirIdentifier: [
    { required: true, message: "请输入水库标识", trigger: "blur" },
  ],
  releaseMethod: [
    { required: true, message: "请选择出库方式", trigger: "change" },
  ],
  releaseFlow: [{ required: true, message: "请输入出库流量", trigger: "blur" }],
};
const rules3 = {
  label: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  riverSectionCode: [
    { required: true, message: "请选择河道断面", trigger: "change" },
  ],
  sectionIdentifier: [
    { required: true, message: "请输入断面标识", trigger: "blur" },
  ],
  isInflowSection: [
    { required: true, message: "请选择是否为入流断面", trigger: "change" },
  ],
};

/**
 * 等待 Vue Flow 渲染完成并自动适应视图
 */
const waitForFlowRenderAndFitView = async () => {
  // 等待 Vue 响应式更新
  await nextTick();

  // 等待 DOM 渲染完成
  await new Promise((resolve) => {
    const checkRender = () => {
      const flowElement = proxy.$refs.dragFlow?.$el;
      const hasFlowDOM = flowElement?.querySelector(
        ".vue-flow__nodes, .vue-flow__edges"
      );

      if (hasFlowDOM) {
        // 使用 requestAnimationFrame 确保渲染完成
        requestAnimationFrame(() => requestAnimationFrame(resolve));
      } else {
        // 如果 DOM 还没准备好，继续等待
        setTimeout(checkRender, 50);
      }
    };
    checkRender();
  });

  // 执行视图适应
  try {
    fitView({
      duration: 600,
      padding: 0.1,
    });
  } catch (error) {
    console.warn("自动适应视图失败:", error);
  }
};

const showView = () => {
  // 重置数据
  subBasinList.value = [];
  riverReservoirList.value = [];
  riverSectionList.value = [];

  // 等待数据重置完成
  nextTick(() => {
    // 处理节点数据
    props.nodeValue.map((item) => {
      if (item.type == 1) {
        subBasinList.value.push(item);
      } else if (item.type == 2) {
        riverReservoirList.value.push(item);
      } else if (item.type == 5) {
        riverSectionList.value.push(item);
      }
    });

    // 设置节点
    defaultNodes.value = props.nodes;

    // 处理边数据
    const newEdges = props.edges.map((item) => ({
      id: item.id,
      source: item.source,
      target: item.target,
      markerEnd: MarkerType.ArrowClosed,
    }));
    defaultEdges.value = newEdges;

    waitForFlowRenderAndFitView();
  });
};

// 小流域列表
const waterAreaList = ref([]);
// 水库列表
const waterPointList = ref([]);
// 河道断面列表
const waterSectionList = ref([]);
const form1Check = ref(false);
const form2Check = ref(false);
const form3Check = ref(false);
const dbclickNodeId = ref(null);
const checkForm = () => {
  // 检查是否有有效的活动索引
  if (activeIndex.value === null) {
    proxy.$modal.msgWarning("没有选中的节点");
    return;
  }

  if (flowType.value == "subBasin") {
    // 检查数组索引是否有效
    if (!subBasinList.value[activeIndex.value]) {
      proxy.$modal.msgWarning("节点数据不存在");
      return;
    }
    proxy.$refs.form1.validate((valid) => {
      if (valid) {
        const node = proxy.$refs.dragFlow.getNodes.find(
          (item) => item.id == subBasinList.value[activeIndex.value].nodeOrder
        );

        // 检查是否有其他节点已经绑定了相同的小流域代码
        const basinCodes = subBasinList.value
          .map((item) => item.basinCode)
          .filter(Boolean);
        // basinCodes是否有重复的
        const hasDuplicates = new Set(basinCodes).size !== basinCodes.length;

        if (hasDuplicates) {
          subBasinList.value[activeIndex.value].basinCode = "";
          proxy.$modal.msgWarning("当前小流域已存在节点绑定 请重新选择");
          return;
        }

        node.data.label = subBasinList.value[activeIndex.value].label;
        updateNode(node);

        form1Check.value = true;
        subBasinList.value[activeIndex.value].list.map((item, index) => {
          if (!item.stationIdentifier) {
            form1Check.value = false;
          }
        });
        if (!form1Check.value) {
          proxy.$modal.msgWarning("请将雨量站标识补充完整");
          return;
        }
        show.value = false;
      }
    });
  } else if (flowType.value == "reservoir") {
    // 检查数组索引是否有效
    if (!riverReservoirList.value[activeIndex.value]) {
      proxy.$modal.msgWarning("节点数据不存在");
      return;
    }
    proxy.$refs.form2.validate((valid) => {
      if (valid) {
        let node = proxy.$refs.dragFlow.getNodes.find(
          (item) =>
            item.id == riverReservoirList.value[activeIndex.value].nodeOrder
        );
        node.data.label = riverReservoirList.value[activeIndex.value].label;
        updateNode(node);
        // 检查是否有其他节点已经绑定了相同的水库代码
        const duplicateIndex = riverReservoirList.value.findIndex(
          (item, index) =>
            index !== activeIndex.value &&
            item.reservoirCode ===
              riverReservoirList.value[activeIndex.value].reservoirCode
        );
        if (duplicateIndex !== -1) {
          riverReservoirList.value[activeIndex.value].reservoirCode = "";
          proxy.$modal.msgWarning("当前水库已存在节点绑定 请重新选择");
          return;
        }
        show.value = false;
        form2Check.value = true;
      }
    });
  } else if (flowType.value == "section") {
    // 检查数组索引是否有效
    if (!riverSectionList.value[activeIndex.value]) {
      proxy.$modal.msgWarning("节点数据不存在");
      return;
    }
    proxy.$refs.form3.validate((valid) => {
      if (valid) {
        const node = proxy.$refs.dragFlow.getNodes.find(
          (item) =>
            item.id == riverSectionList.value[activeIndex.value].nodeOrder
        );
        node.data.label = riverSectionList.value[activeIndex.value].label;
        updateNode(node);
        // 检查是否有其他节点已经绑定了相同的河道断面代码
        const duplicateIndex = riverSectionList.value.findIndex(
          (item, index) =>
            index !== activeIndex.value &&
            item.riverSectionCode ===
              riverSectionList.value[activeIndex.value].riverSectionCode
        );
        if (duplicateIndex !== -1) {
          riverSectionList.value[activeIndex.value].riverSectionCode = "";
          proxy.$modal.msgWarning("当前河道断面已存在节点绑定 请重新选择");
          return;
        }
        show.value = false;
        form3Check.value = true;
      }
    });
  }
};

// 获取河道断面
const getAllarea = async () => {
  const res = await getRiverSectionList({ pageNum: 1, pageSize: 999 });
  if (res.code === 200) {
    waterSectionList.value = res.rows || [];
  }
};

// 获取小流域
const getAllWater = async () => {
  let res = await getSubBasinList({ pageNum: 1, pageSize: 999 });
  if (res.code === 200) {
    waterAreaList.value = res.rows || [];
  }
};

// 获取水库
const getAllWaterPoint = async () => {
  let res = await selectRsList({ pageNum: 1, pageSize: 999 });
  waterPointList.value = res.data.records || [];
};

const changeBasin = async (code) => {
  const res = await getSubBasinWeight(code);
  if (res.code === 200) {
    subBasinList.value[activeIndex.value].list = res.data.weightPOS;
  }
};

// 重置节点
const resetNodeConfirm = () => {
  // 确认重置操作
  proxy.$modal
    .confirm(
      "确定要重置所有节点吗？此操作将清空画布上的所有节点和连接线，且无法撤销。"
    )
    .then(() => {
      resetNode();
    })
    .catch(() => {
      // 用户取消操作
      console.log("用户取消重置操作");
    });
};

const resetNode = (notify = true) => {
  // 清空画布上的所有节点和边
  setNodes([]);
  setEdges([]);

  // 重置默认节点和边数据
  defaultNodes.value = [];
  defaultEdges.value = [];

  // 重置所有相关的数据状态
  subBasinList.value = [];
  riverReservoirList.value = [];
  riverSectionList.value = [];

  // 重置选中状态
  selectNode.value = null;
  activeIndex.value = null;

  // 重置表单验证状态
  form1Check.value = false;
  form2Check.value = false;
  form3Check.value = false;

  // 重置其他状态变量
  title.value = "";
  flowType.value = "";
  dbclickNodeId.value = null;

  // 关闭弹窗
  show.value = false;

  nextTick(() => {
    fitView({ duration: 300, padding: 0.1 });
  });

  if (!notify) return;
  // 通知父组件数据已重置
  emit("getNodeValue", {
    subBasinList: [],
    riverReservoirList: [],
    riverSectionList: [],
  });

  proxy.$modal.msgSuccess("节点重置成功");
};

// 删除节点
const deleteNode = () => {
  if (selectNode.value) {
    removeNodes(selectNode.value);
  } else {
    proxy.$modal.msgWarning("请选择要删除节点");
  }
};

// 删除当前编辑的节点
const deleteCurrentNode = () => {
  if (!dbclickNodeId.value) {
    proxy.$modal.msgWarning("没有可删除的节点");
    return;
  }

  proxy.$modal
    .confirm("确定要删除当前节点吗？")
    .then(() => {
      const nodeIdToDelete = dbclickNodeId.value;

      // 先关闭弹窗和重置状态，避免组件访问已删除的数据
      show.value = false;
      dbclickNodeId.value = null;
      activeIndex.value = null;
      selectNode.value = null;

      // 使用 nextTick 确保 DOM 更新完成后再删除节点
      nextTick(() => {
        removeNodes(nodeIdToDelete);
      });
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 获取当前所有的节点信息
const getNode = () => {
  let nodes = proxy.$refs.dragFlow.nodes;
  let edges = proxy.$refs.dragFlow.edges;

  // 构建汇出点映射关系
  const outletMap = new Map();
  edges.forEach((edge) => {
    // edge.source 是当前节点，edge.target 是汇出点
    outletMap.set(edge.source, edge.target);
  });

  // 构建小流域节点列表
  const formattedSubBasinList = subBasinList.value.map((item) => {
    const outletOrder = outletMap.get(item.nodeOrder) || null;
    // 从 nodes 中找到对应的节点获取位置信息
    const nodeInfo = nodes.find((node) => node.id === item.nodeOrder);
    const position = nodeInfo?.position || { x: 0, y: 0 };

    return {
      nodeName: item.label || `subBasin${item.nodeOrder}`,
      nodeOrder: item.nodeOrder,
      nodeType: "subBasin",
      outletOrder: outletOrder,
      locationX: position.x,
      locationY: position.y,
      basinCode: item.basinCode || "",
      basinIdentifier: item.basinIdentifier || "",
      list:
        item.list?.map((station) => ({
          ...station,
          stationCode:
            station.stationCode || station.stcd || station.code || "",
          stationIdentifier: station.stationIdentifier || "",
          subBasinCode: item.basinCode || "",
        })) || [],
    };
  });

  // 构建水库节点列表
  const formattedRiverReservoirList = riverReservoirList.value.map((item) => {
    const outletOrder = outletMap.get(item.nodeOrder) || null;
    // 从 nodes 中找到对应的节点获取位置信息
    const nodeInfo = nodes.find((node) => node.id === item.nodeOrder);
    const position = nodeInfo?.position || { x: 0, y: 0 };

    return {
      nodeName: item.label || `reservoir{item.nodeOrder}`,
      nodeOrder: item.nodeOrder,
      nodeType: "reservoir",
      outletOrder: outletOrder,
      locationX: position.x,
      locationY: position.y,
      releaseFlow: parseFloat(item.releaseFlow) || 0,
      releaseMethod: parseInt(item.releaseMethod) || 0,
      reservoirCode: item.reservoirCode || "",
      reservoirIdentifier: item.reservoirIdentifier || "",
    };
  });

  // 构建河道断面节点列表
  const formattedRiverSectionList = riverSectionList.value.map((item) => {
    const outletOrder = outletMap.get(item.nodeOrder) || null;
    // 从 nodes 中找到对应的节点获取位置信息
    const nodeInfo = nodes.find((node) => node.id === item.nodeOrder);
    const position = nodeInfo?.position || { x: 0, y: 0 };

    return {
      nodeName: item.label || `section${item.nodeOrder}`,
      nodeOrder: item.nodeOrder,
      nodeType: "section",
      outletOrder: outletOrder,
      locationX: position.x,
      locationY: position.y,
      isInflowSection: parseInt(item.isInflowSection) || 0,
      riverSectionCode: item.riverSectionCode || "",
      sectionIdentifier: item.sectionIdentifier || "",
      schemeId: item.schemeId || null,
    };
  });

  // 返回格式化后的数据给后端
  const result = {
    subBasinList: formattedSubBasinList,
    riverReservoirList: formattedRiverReservoirList,
    riverSectionList: formattedRiverSectionList,
  };

  return result;
};
const title = ref("");
const flowType = ref("");

// 双击节点
const handleDoubleClick = (e) => {
  let data = e.node.data;
  let { nodeType, nodeId, label } = data;
  dbclickNodeId.value = "";
  dbclickNodeId.value = nodeId;
  if (nodeType == "subBasin") {
    let index = subBasinList.value.findIndex(
      (item) => item.nodeOrder == nodeId
    );
    activeIndex.value = index;
    subBasinList.value[activeIndex.value].label = label;
  }
  if (nodeType == "reservoir") {
    let index = riverReservoirList.value.findIndex(
      (item) => item.nodeOrder == nodeId
    );
    activeIndex.value = index;
    riverReservoirList.value[activeIndex.value].label = label;
  }
  if (nodeType == "section") {
    let index = riverSectionList.value.findIndex(
      (item) => item.nodeOrder == nodeId
    );
    activeIndex.value = index;
    riverSectionList.value[activeIndex.value].label = label;
  }

  show.value = true;
  title.value = label;
  flowType.value = nodeType;
};

onInit((vueFlowInstance) => {
  // instance is the same as the return of `useVueFlow`
  vueFlowInstance.fitView();
});

// 处理节点连接
onConnect((connection) => {
  // 创建新的连接线，添加箭头标记
  const newEdge = {
    ...connection,
    markerEnd: MarkerType.ArrowClosed,
  };
  addEdges(newEdge);
});

onMounted(() => {
  getAllarea(); //获取河道断面
  getAllWater(); //获取小流域
  getAllWaterPoint(); //获取水库
});

defineExpose({
  showView,
  resetNode,
  getNode,
});
</script>

<template>
  <div class="dnd-flow" @drop="onDrop">
    <VueFlow
      :nodes="defaultNodes"
      :edges="defaultEdges"
      @dragover="onDragOver"
      @dragleave="onDragLeave"
      ref="dragFlow"
      @nodeDoubleClick="handleDoubleClick"
      @nodesChange="nodesChange"
      :nodes-draggable="props.showType !== 'readOnly'"
      :edges-updatable="props.showType !== 'readOnly'"
      :nodes-connectable="props.showType !== 'readOnly'"
    >
      <Controls v-if="props.showType !== 'readOnly'" />
      <DropzoneBackground
        :style="{
          backgroundColor: isDragOver ? '#e7f3ff' : 'transparent',
          transition: 'background-color 0.2s ease',
        }"
      >
        <p v-if="isDragOver">拖拽节点到此处</p>
      </DropzoneBackground>
    </VueFlow>

    <Sidebar
      @resetNodeConfirm="resetNodeConfirm"
      v-if="props.showType != 'readOnly'"
      @deleteNode="deleteNode"
    />
  </div>
  <el-dialog
    v-model="show"
    :title="title"
    style="float: right; width: 600px"
    :modal="false"
    :show-close="false"
  >
    <template
      v-if="
        flowType == 'subBasin' &&
        activeIndex !== null &&
        subBasinList[activeIndex]
      "
    >
      <el-form
        label-width="auto"
        :model="subBasinList[activeIndex]"
        :rules="rules1"
        ref="form1"
        :disabled="props.showType == 'readOnly'"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="节点名称" prop="label">
              <el-input
                placeholder="请输入节点名称"
                v-model="subBasinList[activeIndex].label"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="小流域" prop="basinCode">
              <el-select
                v-model="subBasinList[activeIndex].basinCode"
                placeholder="请选择小流域"
                filterable
                @change="changeBasin"
              >
                <el-option
                  :label="item.name"
                  :value="item.code"
                  v-for="item in waterAreaList"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="小流域标识" prop="basinIdentifier">
              <el-input
                placeholder="请输入"
                v-model="subBasinList[activeIndex].basinIdentifier"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="3">序号</el-col>
          <el-col :span="7">雨量站</el-col>
          <el-col :span="3">权重</el-col>
          <el-col :span="10">雨量站标识</el-col>
        </el-row>
        <el-row
          style="margin-top: 10px; display: flex; align-items: center"
          v-for="(item, index) in subBasinList[activeIndex].list"
          :key="index"
        >
          <el-col :span="3">{{ index + 1 }}</el-col>
          <el-col :span="7">
            {{ item.stnm }}
          </el-col>
          <el-col :span="3">
            {{ item.serialNumber }}
          </el-col>
          <el-col :span="10">
            <el-input
              placeholder="请输入雨量站标识"
              v-model="subBasinList[activeIndex].list[index].stationIdentifier"
            />
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template
      v-if="
        flowType == 'reservoir' &&
        activeIndex !== null &&
        riverReservoirList[activeIndex]
      "
    >
      <el-form
        label-width="auto"
        :model="riverReservoirList[activeIndex]"
        :rules="rules2"
        ref="form2"
        :disabled="props.showType == 'readOnly'"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="节点名称" prop="label">
              <el-input
                placeholder="请输入节点名称"
                v-model="riverReservoirList[activeIndex].label"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="对应水库" prop="reservoirCode">
              <el-select
                placeholder="请选择"
                v-model="riverReservoirList[activeIndex].reservoirCode"
                filterable
                class="w-full"
              >
                <el-option
                  :label="item.resName"
                  :value="item.registerCode"
                  v-for="item in waterPointList"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="水库标识" prop="reservoirIdentifier">
              <el-input
                placeholder="请输入"
                v-model="riverReservoirList[activeIndex].reservoirIdentifier"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出库方式" prop="releaseMethod">
              <el-select
                placeholder="请选择"
                v-model="riverReservoirList[activeIndex].releaseMethod"
                class="w-full"
              >
                <el-option label="指定出流" :value="1" />
                <el-option label="自由出流" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出库流量(m3/s)" prop="releaseFlow">
              <el-input-number
                placeholder="请输入"
                v-model="riverReservoirList[activeIndex].releaseFlow"
                :precision="3"
                :controls="false"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <!-- 河道断面 -->
    <template
      v-if="
        flowType == 'section' &&
        activeIndex !== null &&
        riverSectionList[activeIndex]
      "
    >
      <el-form
        label-width="auto"
        :rules="rules3"
        ref="form3"
        :model="riverSectionList[activeIndex]"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="节点名称" prop="label">
              <el-input
                placeholder="请输入节点名称"
                v-model="riverSectionList[activeIndex].label"
                :disabled="props.showType == 'readOnly'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="河道断面" prop="riverSectionCode">
              <el-select
                placeholder="请选择"
                v-model="riverSectionList[activeIndex].riverSectionCode"
                :disabled="props.showType == 'readOnly'"
                filterable
                class="w-full"
              >
                <el-option
                  :label="item.sectionCode"
                  :value="item.sectionCode"
                  v-for="(item, index) in waterSectionList"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="断面标识" prop="sectionIdentifier">
              <el-input
                placeholder="请输入"
                v-model="riverSectionList[activeIndex].sectionIdentifier"
                :disabled="props.showType == 'readOnly'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入流断面" prop="isInflowSection">
              <el-select
                placeholder="请选择"
                v-model="riverSectionList[activeIndex].isInflowSection"
                :disabled="props.showType == 'readOnly'"
                class="w-full"
              >
                <el-option label="是" :value="0" />
                <el-option label="否" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          type="primary"
          @click="checkForm"
          v-if="props.showType != 'readOnly'"
        >
          保 存
        </el-button>
        <el-button @click="show = false">关 闭</el-button>
        <el-button
          type="warning"
          v-if="props.showType != 'readOnly'"
          @click="deleteCurrentNode"
        >
          删 除
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<style>
@import "@vue-flow/core/dist/style.css";
@import "@vue-flow/core/dist/theme-default.css";
@import "@vue-flow/controls/dist/style.css";

.vue-flow__minimap {
  transform: scale(75%);
  transform-origin: bottom right;
}

.dnd-flow {
  position: relative;
  display: flex;
  height: 100%;
  width: 100%;
}

.dnd-flow .vue-flow-wrapper {
  flex: 1;
  height: 100%;
  width: 100%;
}

.dropzone-background {
  position: relative;
  height: 100%;
  width: 100%;
}

.dropzone-background .overlay {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  pointer-events: none;
}

/* 确保箭头标记正确显示 */
.vue-flow__edge-path {
  stroke: #b1b1b7;
  stroke-width: 2;
}

.vue-flow__arrowhead {
  fill: #b1b1b7;
}

/* 确保连接线和箭头的层级正确 */
.vue-flow__edges {
  z-index: 1;
}

.vue-flow__nodes {
  z-index: 2;
}

/* Vue Flow 自定义节点样式 */
.rounds {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  background-color: #e1f3ff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 2px solid #5cadde;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

.area1 {
  width: 100px;
  height: 45px;
  background-color: #e1f3ff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 2px solid #5cadde;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}
</style>
