<template>
  <div class="reparation-container">
    <div class="reparation-sidebar" v-loading="loading">
      <el-form
        :model="addForm"
        label-width="90px"
        :rules="rules"
        ref="basicRef"
        :disabled="status == 'readOnly'"
        class="basic-form"
      >
        <el-form-item label="方案名称" prop="schemeName">
          <el-input
            v-model="addForm.schemeName"
            placeholder="请输入方案名称"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="预报流域" prop="forecastBasinCode">
          <el-tree-select
            v-model="addForm.forecastBasinCode"
            :data="transformDataForTreeSelect(waterAreaList)"
            check-strictly
            :render-after-expand="false"
            placeholder="请选择预报流域"
            class="w-full"
          />
        </el-form-item>
        <el-form-item
          label="应用密钥"
          prop="appKey"
          v-if="status !== 'readOnly'"
        >
          <el-input
            v-model="addForm.appKey"
            placeholder="请输入应用密钥"
            clearable
            maxlength="200"
          />
        </el-form-item>
        <el-form-item
          label="模型密钥"
          prop="modelKey"
          v-if="status !== 'readOnly'"
        >
          <el-input
            v-model="addForm.modelKey"
            placeholder="请输入模型密钥"
            clearable
            maxlength="200"
          />
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input
            v-model="addForm.description"
            placeholder="请输入备注"
            type="textarea"
            maxlength="250"
            :autosize="{ minRows: 3, maxRows: 8 }"
          />
        </el-form-item>
      </el-form>

      <div class="action-buttons" v-if="status != 'readOnly'">
        <el-button type="primary" @click="submitForm" v-if="status == 'add'"
          >保 存</el-button
        >
        <el-button type="primary" @click="editForm" v-if="status == 'edit'"
          >修 改</el-button
        >
        <el-button type="default">取 消</el-button>
      </div>
    </div>

    <div class="reparation-content" v-loading="loading">
      <dragLegend
        class="drag-legend-component"
        ref="flowRight"
        :nodes="nodes"
        :edges="edges"
        :nodeValue="nodeValue"
        :showType="status"
      />
    </div>
  </div>
</template>

<script setup>
import { reactive, toRefs, onMounted, getCurrentInstance } from "vue";
import { saveForecastScheme } from "@/api/scheduling/index";
import { selectStlyList } from "@/api/watershed/ads";
import dragLegend from "./dragLegend/index.vue";
import { useRoute, useRouter } from "vue-router";
import { useForecastScheme } from "@/composables/useForecastScheme";

defineOptions({
  name: "AddForecastScheme",
});

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const { loading, getTaskDetail } = useForecastScheme();
let state = reactive({
  addForm: {
    schemeName: "",
    forecastBasinCode: "",
    appKey: "",
    modelKey: "",
    description: "",
    subBasinList: [],
    riverSectionList: [],
    riverReservoirList: [],
  },
  status: "add",
  rules: {
    schemeName: [
      { required: true, message: "请输入方案名称", trigger: "blur" },
    ],
    forecastBasinCode: [
      { required: true, message: "请选择流域", trigger: "change" },
    ],
    appKey: [{ required: true, message: "请输入应用密钥", trigger: "blur" }],
    modelKey: [{ required: true, message: "请输入模型密钥", trigger: "blur" }],
  },
  nodes: [],
  edges: [],
  nodeValue: [],
  forecastDispatchPlanModelConfigEntities: [],
  waterAreaList: [],
});
let { addForm, rules, waterAreaList, nodeValue, nodes, edges, status } =
  toRefs(state);

// 处理获取任务详情的逻辑
const handleGetTaskDetail = async (id) => {
  const result = await getTaskDetail(id);
  if (result) {
    // 设置基本表单数据
    state.addForm = result.formData;
    state.addForm.forecastBasinCode = Number(state.addForm.forecastBasinCode);

    // 设置流程图数据
    state.nodes = result.nodes;
    state.edges = result.edges;
    state.nodeValue = result.nodeValue;

    // 等待DOM更新后显示流程图
    proxy.$nextTick(() => {
      if (proxy.$refs.flowRight && proxy.$refs.flowRight.showView) {
        proxy.$refs.flowRight.showView();
      }
    });
  }
};
const getNodeValue = () => {
  const result = proxy.$refs.flowRight.getNode();
  if (!result) return;
  // 更新表单数据，包含流程图的节点和边数据
  addForm.value.subBasinList = result.subBasinList || [];
  addForm.value.riverSectionList = result.riverSectionList || [];
  addForm.value.riverReservoirList = result.riverReservoirList || [];
};

const editForm = () => {
  proxy.$refs.basicRef.validate(async (valid) => {
    if (valid) {
      getNodeValue();
      const res = await saveForecastScheme(addForm.value);
      if (res.code == 200) {
        proxy.$modal.msgSuccess("方案修改成功");
        router.push("/forecast/taskList");
      } else {
        proxy.$modal.msgError("方案修改失败");
      }
    } else {
      return false;
    }
  });
};
const submitForm = () => {
  proxy.$refs.basicRef.validate(async (valid) => {
    if (valid) {
      getNodeValue();
      const res = await saveForecastScheme(addForm.value);
      if (res.code == 200) {
        proxy.$modal.msgSuccess("方案添加成功");
        router.push("/forecast/taskList");
      } else {
        proxy.$modal.msgError("方案添加失败");
      }
    }
  });
};
const transformDataForTreeSelect = (data) => {
  // 递归地转换数据以匹配 el-tree-select 的需求
  return data.map((item) => ({
    label: item.data.name, // 使用 'name' 属性作为标签
    value: item.data.basinId, // 使用 'basinId' 属性作为值
    children: item.children ? transformDataForTreeSelect(item.children) : [], // 递归转换子节点
  }));
};

// 获取流域列表
const getAllWater = async () => {
  let res = await selectStlyList({ pageNum: 1, pageSize: 999 });
  state.waterAreaList = res.data || [];
};

onMounted(() => {
  state.status = route.query.status || "add";

  getAllWater();
  if (route.query.id) {
    handleGetTaskDetail(route.query.id);
  }
});
</script>

<style lang="scss" scoped>
.reparation-container {
  display: flex;
  height: calc(100vh - 95px);
  padding: 20px;
  gap: 20px;
  background-color: #f5f7fa;
}

.reparation-sidebar {
  width: 360px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
  overflow: hidden;

  .basic-form {
    flex: 1;
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;

    .el-button {
      min-width: 100px;
    }
  }
}

.reparation-content {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.drag-legend-component {
  height: 100%;
}
</style>
