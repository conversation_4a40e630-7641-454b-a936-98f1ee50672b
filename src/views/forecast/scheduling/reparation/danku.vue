<!--单库调度-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="水库选择" prop="status">
        <el-select v-model="queryParams.stcd" placeholder="请选择" clearable style="width: 150px">
          <el-option v-for="dict in rsvrListData" :key="dict.STCD" :label="dict.STNM" :value="dict.STCD" />
        </el-select>
      </el-form-item>
      <el-form-item label="预报成果选择" prop="status">
        <el-select v-model="queryParams.fcid" placeholder="请选择" clearable style="width: 150px">
          <el-option v-for="dict in forecastPlanList" :key="dict.forecastStId" :label="dict.forecastName"
            :value="dict.forecastStId" />
        </el-select>
      </el-form-item>
      <el-form-item label="起调水位(m)" prop="name">
        <el-input v-model="queryParams.waterValue" placeholder="请输入" clearable style="width: 100px" />
      </el-form-item>
      <el-form-item label="调度方式" prop="warnType">
        <el-select v-model="queryParams.dispModule" placeholder="请选择" clearable style="width: 150px">
          <el-option v-for="dict in dispModuleSelects" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="success" style="width: 70px" @click="handleCountForecast">计算</el-button>
        <el-button plain style="width: 70px" @click="handleSaveForecast">保存</el-button>
        <el-button plain @click="resetQuery" style="width: 70px">重置</el-button>
        <el-button type="primary" @click="handleRenWuChe">任务车</el-button>
      </el-form-item>
    </el-form>
    <!--    <el-row :gutter="10" class="mb8">-->
    <!--      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
    <!--    </el-row>-->
    <el-table :data="dateList" class="productTable">
      <el-table-column type="index" label="序号" width="55"></el-table-column>
      <el-table-column prop="tm" label="时间" align="center"></el-table-column>
      <el-table-column prop="inq" label="入库流量 (m³/s)" align="center"> </el-table-column>
      <el-table-column prop="z" label="库水位(m)" align="center">
        <template v-slot="scope">
          <el-input v-show="dispModule == '2'" v-model="scope.row.z" class="borderColor" size="small"></el-input>

        </template>
      </el-table-column>
      <el-table-column prop="otq" label="总出库流量 (m³/s)" align="center">
        <template v-slot="scope">
          <span v-show="dispModule != '3'">{{ scope.row.otq }}</span>
          <el-input v-show="dispModule == '3'" v-model="scope.row.otq" class="borderColor" size="small"></el-input>
        </template>
      </el-table-column>
    </el-table>
    <!--    <pagination-->
    <!--        v-show="total > 0"-->
    <!--        :total="total"-->
    <!--        v-model:page="queryParams.pageNum"-->
    <!--        v-model:limit="queryParams.pageSize"-->
    <!--        @pagination="getList"-->
    <!--    />-->
    <!-- 添加或修改流域对话框 -->
    <el-dialog title="预警详情" v-model="open" width="850px" append-to-body>
      <el-row>

      </el-row>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import moment from "moment/moment";
import { getRainTimeByType } from "@/utils/common";
import { rsvrList } from "@/api/watershed/screenRight/overview";
import { execCountForecast, getForecastValueList, getRenWuCheList } from "@/api/watershed/forecast";

defineOptions({
  name: 'Danku'
})

const { proxy } = getCurrentInstance();

// 详情里面的列表
const cjzbList = ref([]); // 超警指标列表
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const rsvrListData = ref([]); // 水库列表
const forecastPlanList = ref([]); // 预报成果列表
const dateList = ref([
  {
    id: '1',
    tm: '2024-03-20 08:00',
    inq: '8.56',
    otq: '-',
    z: ''
  },
  {
    id: '2',
    tm: '2024-03-20 09:00',
    inq: '8.56',
    otq: '-',
    z: ''
  },
  {
    id: '3',
    tm: '2024-03-20 10:00',
    inq: '8.56',
    otq: '-',
    z: ''
  },
  {
    id: '4',
    tm: '2024-03-20 11:00',
    inq: '8.56',
    otq: '-',
    z: ''
  }
]);
const dispModule = ref('2')

// 调度模式
const dispModuleSelects = [
  {
    label: '规则调度',
    value: 1
  },
  {
    label: '库水位控制调度',
    value: 2
  },
  {
    label: '出库流量控制调度',
    value: 3
  },

]
// 预警等级
const warnLevelSelects = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '超警戒',
    value: 1
  },
  {
    label: '超保证',
    value: 2
  },
  {
    label: '超历史',
    value: 3
  },
  {
    label: '超汛限',
    value: 4
  },
  {
    label: '超设计',
    value: 5
  },
  {
    label: '超校核',
    value: 6
  },
  {
    label: '雨量转移',
    value: 7
  },
  {
    label: '水位转移',
    value: 8
  },
  {
    label: '流量转移',
    value: 9
  },
]
const data = reactive({
  form: {
  },
  queryParams: {
    time: ['', ''],
    name: undefined,
    // adcd: '130800000000',
    adcd: undefined,
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
  },
});

const { queryParams, form, rules } = toRefs(data);

function getList() {
  loading.value = true;
  queryParams.value.startTime = moment(queryParams.value.time[0]).format('YYYY-MM-DD HH:mm:ss')
  queryParams.value.endTime = moment(queryParams.value.time[1]).format('YYYY-MM-DD HH:mm:ss')
  // selectWarnPage(queryParams.value).then(res => {
  //   res.data.forEach(item=>{
  //     item.createTime = item.createTime?.substring(0, 16).replace('T', ' ')
  //   })
  //   lyList.value = res.data
  //   total.value = res.total
  //   loading.value = false;
  // });
}
const disabledDateFn = (time) => {
  return time.getTime() > Date.now();
};
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    pcode: '',
    addFlag: flag,
    geoType: 'people',
    lynm: undefined,
    icon: undefined,
    adcds: [],
  };
  cjzbList.value = []
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleRenWuChe() {
  open2.value = true
  loading.value = true
  getRenWuCheList(queryParams.value).then((res) => {
    renWuCheList.value = res.data
    loading.value = false
    total.value = res.total
  })
}

const getForecastPlanList = async () => {
  let params = {
    releaseStatus: 2,// 已发布
    pageNum: 1,
    pageSize: 10,
  }
  const res = await getForecastValueList(params)
  const obj = res.data
  let list = []
  for (let key in obj) {
    // obj[key].time = moment(obj[key].TM).format("MM-DD HH:mm")
    list.push(obj[key])
  }
  forecastPlanList.value = list
}

const getRSVRList = async () => {
  const res = await rsvrList({})
  const obj = res.data
  let list = []
  for (let key in obj) {
    obj[key].time = moment(obj[key].TM).format("MM-DD HH:mm")
    list.push(obj[key])
  }
  rsvrListData.value = list
}

// 向模型发起计算
function handleCountForecast() {
  let params = {
    ...form.value,
    rainStartTime: moment(form.value.time[0]).format('YYYY-MM-DD HH:mm:ss'),
    rainEndTime: moment(form.value.time[1]).format('YYYY-MM-DD HH:mm:ss'),
    rainArray: "",
    forecastTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    ealTimeCorrection: form.value.jiaoZheng ? 1 : 0
  }
  execCountForecast(params).then((res) => {
    proxy.$modal.msgSuccess("保存成功");
    console.log(res)
  })
}
function handleSaveForecast() {
  open.value = true
}

onMounted(() => {
  getRSVRList()
  getForecastPlanList()

  // 默认查询预警时间近72小时
  queryParams.value.time = getRainTimeByType('72')
  // 结束时间改成23:59
  let endTimeStr = moment(queryParams.value.time[1]).format('YYYY-MM-DD HH:mm:ss')
  endTimeStr = endTimeStr.substring(0, 11) + '23:59:59'
  queryParams.value.time[1] = new Date(endTimeStr)
  getList();
})
</script>
<style scoped>
.card-box {
  padding-right: 0;
  padding-left: 0;
  margin-bottom: 5px;
}
</style>
