export const initialNodes = [
    {
        id: '1',
        type: 'input',
        data: {},
        position: { x: 150, y: 10 },
        class: 'rounds',
    },
    {
        id: '2',
        type: 'input',
        data: { label: '江河' },
        position: { x: 120, y: 70 },
        class: 'area1',
    },
    {
        id: '3',
        type: 'input',
        data: { label: '水库' },
        position: { x: 140, y: 140 },
        class: 'area2',
    },
    {
        id: '4',
        type: 'input',
        data: { label: '溃口' },
        position: { x: 120, y: 210 },
        class: 'area1',
    },
    {
        id: '5',
        type: 'input',
        data: { label: '城镇' },
        position: { x: 120, y: 280 },
        class: 'area1',
    },
    {
        id: '6',
        type: 'input',
        data: { label: '水文站' },
        position: { x: 240, y: 10 },
        class: 'area1',
    },
    {
        id: '7',
        type: 'input',
        data: { label: '河流' },
        position: { x: 270, y: 80 },
        class: 'line',
    },
    {
        id: '8',
        type: 'input',
        data: {},
        position: { x: 270, y: 280 },
        class: 'rounds',
    },

]

export const initialEdges = [
    // {
    //     id: 'e1-2',
    //     source: '1',
    //     target: '2',
    //     animated: true,
    // },
    // {
    //     id: 'e1-3',
    //     source: '1',
    //     target: '3',
    //     label: 'edge with arrowhead',
    //     markerEnd: MarkerType.ArrowClosed,
    // },
    // {
    //     id: 'e4-5',
    //     type: 'step',
    //     source: '4',
    //     target: '5',
    //     label: 'Node 2',
    //     style: { stroke: 'orange' },
    //     labelBgStyle: { fill: 'orange' },
    // },
    // {
    //     id: 'e3-4',
    //     type: 'smoothstep',
    //     source: '3',
    //     target: '4',
    //     label: 'smoothstep-edge',
    // },
]
