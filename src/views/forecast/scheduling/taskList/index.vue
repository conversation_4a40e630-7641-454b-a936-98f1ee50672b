<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="预报方案名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="预报流域" prop="isdo">
        <el-tree-select
          v-model="queryParams.basinId"
          :data="transformDataForTreeSelect(waterAreaList)"
          check-strictly
          clearable
          :render-after-expand="false"
          placeholder="请选择预报流域"
        />
      </el-form-item>
      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList()" icon="Search"
          >查询</el-button
        >
        <el-button @click="resetQuery" type="primary" plain icon="Refresh"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <!-- <el-row :gutter="10" class="mb8">
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row> -->
    <div class="content content-table">
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="openForm"
          >新增</el-button
        >
      </div>
      <el-table
        v-loading="loading"
        :data="lyList"
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column
          type="index"
          label="序号"
          width="65"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="schemeName"
          label="预报方案名称"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="forecastBasinName"
          label="预报流域"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column prop="description" label="方案描述" align="center">
        </el-table-column>
        <el-table-column prop="updateTime" label="最近更新时间" align="center">
          <template #default="{ row }">
            {{ moment(row.updateTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="210"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="View"
              @click="seeDetail(scope.row)"
              >查看</el-button
            >
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="editTask(scope.row)"
              >编辑</el-button
            >
            <!-- <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row.id)"
              >删除</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="total"
        v-model:page="pageNum"
        v-model:limit="pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script setup>
import { getForecastSchemeList, delTaskPlan } from "@/api/scheduling/index";
import { selectStlyList } from "@/api/watershed/ads";
import { onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import moment from "moment";

defineOptions({
  name: "TaskList",
});

const { proxy } = getCurrentInstance();
const router = useRouter();

let lyList = ref([]);
const loading = ref(false);
const showSearch = ref(true);
let pageSize = ref(20);
let pageNum = ref(1);
let total = ref(0);

const data = reactive({
  queryParams: {
    name: "",
    basinId: "",
  },
  waterAreaList: [],
});

const { queryParams, waterAreaList } = toRefs(data);

const transformDataForTreeSelect = (data) => {
  // 递归地转换数据以匹配 el-tree-select 的需求
  return data.map((item) => ({
    label: item.data.name, // 使用 'name' 属性作为标签
    value: item.data.basinId, // 使用 'basinId' 属性作为值
    children: item.children ? transformDataForTreeSelect(item.children) : [], // 递归转换子节点
  }));
};
const resetQuery = () => {
  data.queryParams = {
    name: "",
    basinId: "",
  };
  pageNum.value = 1;
  pageSize.value = 20;
  getList();
};
const getAllWater = async () => {
  let res = await selectStlyList({ pageNum: 1, pageSize: 999 });
  data.waterAreaList = res.data || [];
};
const getList = async () => {
  try {
    loading.value = true;
    let res = await getForecastSchemeList({
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      name: queryParams.value.name,
      basinId: queryParams.value.basinId,
    });
    if (res.code === 200) {
      lyList.value = res.rows;
      total.value = res.total;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const handleDelete = async (id) => {
  ElMessageBox.confirm("确定删除该方案吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    let res = await delTaskPlan(id);
    if (res.code == 200) {
      proxy.$modal.msgSuccess(res.msg);
    } else {
      proxy.$modal.msgError(res.msg);
    }
    getList();
  });
};
const changeTaskModelStatus = async (id, state) => {
  let res = await changeTaskModel({ id: id, state: state });
  if (res.code == 200) {
    proxy.$modal.msgSuccess(res.msg);
  } else {
    proxy.$modal.msgError(res.msg);
  }
  getList();
};

const seeDetail = (data) => {
  router.push({
    path: `/forecast/reparation`,
    query: { id: data.id, status: "readOnly" },
  });
};
const editTask = (data) => {
  router.push({
    path: `/forecast/reparation`,
    query: { id: data.id, status: "edit" },
  });
};
const openForm = () => {
  router.push({ path: `/forecast/reparation`, query: { status: "add" } });
};

onMounted(() => {
  getList();
  getAllWater();
});
</script>
<style scoped lang="scss">
.card-box {
  padding-right: 0;
  padding-left: 0;
  margin-bottom: 5px;
}

.exceed {
  width: 95%;
  margin: 2px auto;
  height: 150px;
  display: flex;
  color: #555;

  .items {
    flex: 1;
    height: 100%;
    text-align: center;

    .value {
      margin: 24px 0;
      font-size: 18px;
    }

    .name {
      margin: 10px 0;
      color: #555;
      font-size: 16px;
    }
  }
}
</style>
