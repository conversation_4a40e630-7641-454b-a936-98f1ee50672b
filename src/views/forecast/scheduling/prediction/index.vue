<!--调度成果管理-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      class="form-container"
    >
      <el-form-item label="流域名称" prop="basinId">
        <el-tree-select
          v-model="queryParams.basinId"
          :data="transformDataForTreeSelect(waterAreaList)"
          check-strictly
          clearable
          :render-after-expand="false"
          placeholder="请选择流域"
        />
      </el-form-item>
      <!-- <el-form-item label="降雨类型" prop="isdo">
                <el-select v-model="queryParams.rainfallType" placeholder="请选择" clearable style="width: 200px">
                    <el-option v-for="dict in type" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
            </el-form-item> -->
      <el-form-item label="方案名称" prop="planId">
        <el-select
          v-model="queryParams.planId"
          placeholder="请选择方案"
          clearable
        >
          <el-option
            :label="item.planName"
            :value="item.planId"
            v-for="(item, index) in taskList"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="businessState">
        <el-select
          v-model="queryParams.businessState"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="form-item-button">
        <el-button type="primary" @click="getList()" icon="Search">查询</el-button>
        <el-button @click="resetQuery" type="primary" plain icon="Refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="content">
      <div class="right-table">
        <div class="table-header">
          <el-button type="success" icon="CirclePlus" @click="openForm">新增</el-button>
        </div>
        <el-table
          v-loading="loading"
          :data="autoList"
          @selection-change="handleSelectionChange"
          stripe
        >
          <el-table-column
            type="index"
            label="序号"
            width="65"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="basinName"
            label="流域名称"
            align="center"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            prop="planName"
            label="方案名称"
            align="center"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            prop="startTime"
            label="计划启动时间"
            align="center"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            prop="endTime"
            label="计划结束时间"
            align="center"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            prop="rainfallType"
            label="降雨类型"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <div v-if="scope.row.rainfallType === 0">预报降雨</div>
              <div v-if="scope.row.rainfallType == 1">实时降雨</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="updateTime"
            label="最近计算完成时间"
            align="center"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            prop="dispcType"
            label="状态"
            :show-overflow-tooltip="true"
            align="center"
          >
            <template #default="scope">
              <span v-if="scope.row.businessState === 0">未使用</span>
              <span v-if="scope.row.businessState === 1">使用中</span>
              <span v-if="scope.row.businessState === 2">已过期</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="left"
            width="210"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button link type="primary" icon="View" @click="seeTask(scope.row)"
                >查看</el-button
              >
              <el-button link type="primary" icon="Edit" @click="editTask(scope.row)"
                >编辑</el-button
              >
              <el-button link type="primary" icon="Delete" @click="deleteTask(scope.row)"
                >删除</el-button
              >
              <template v-if="scope.row.isdo === '0'">
                <el-button link type="primary" icon="Setting" @click="handleIsdo(scope.row)"
                  >设置为执行方案</el-button
                >
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <pagination
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加 -->
    <el-dialog
      :title="formStatus == 1 ? '新增预报调度任务' : '预报调度任务详情'"
      v-model="dialogShow"
      width="30%"
      @close="resetForm()"
    >
      <el-form ref="form" :model="addForm" label-width="120px" :rules="rules">
        <el-form-item label="选择流域" prop="basinId">
          <el-tree-select
            v-model="addForm.basinId"
            :data="transformDataForTreeSelect(waterAreaList)"
            :render-after-expand="false"
            placeholder="请选择流域"
            :disabled="formStatus == 2"
          />
        </el-form-item>
        <el-form-item label="选择方案" prop="planId">
          <el-select
            v-model="addForm.planId"
            placeholder="请选择方案"
            :disabled="formStatus == 2"
          >
            <el-option
              :label="item.planName"
              :value="item.planId"
              v-for="(item, index) in taskChoose"
              :key="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="降雨类型" prop="rainfallType">
          <el-select
            v-model="addForm.rainfallType"
            placeholder="请选择降雨类型"
          >
            <el-option label="预报降雨" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="降雨时段(时)" prop="durationRaining">
          <el-select
            v-model="addForm.durationRaining"
            placeholder="请选择降雨类型"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in set_hour"
              :key="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预热期(时)">
          <el-input
            v-model="addForm.warmUpPeriod"
            style="width: 200px"
            placeholder="请输入小时数"
            @input="handleInput"
          ></el-input>
        </el-form-item>
        <el-form-item label="预见期(时)">
          <el-input
            v-model="addForm.forecastPeriod"
            style="width: 200px"
            placeholder="请输入小时数"
            @input="handleInput2"
          ></el-input>
        </el-form-item>
        <el-form-item label="启动时间(时)" prop="startTime">
          <el-date-picker
            v-model="addForm.startTime"
            type="datetime"
            placeholder="请选择启动日期时间"
            format="YYYY-MM-DD HH"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="结束时间(时)" prop="endTime">
          <el-date-picker
            v-model="addForm.endTime"
            type="datetime"
            placeholder="请选择结束日期时间"
            time-format="HH:00:00"
            format="YYYY-MM-DD HH"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="计算间隔(时)" prop="calculateInterval">
          <el-select
            v-model="addForm.calculateInterval"
            placeholder="请选择计算间隔"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in set_hour"
              :key="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="计算步长(分)" prop="timeStep">
          <el-select v-model="addForm.timeStep" placeholder="请选择计算步长">
            <el-option label="10" :value="10" />
            <el-option label="30" :value="30" />
            <el-option label="60" :value="60" />
            <el-option label="120" :value="120" />
            <el-option label="180" :value="180" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" v-if="formStatus == 1" @click="submitForm">
            保 存
          </el-button>
          <el-button type="primary" v-else @click="updateForm">
            保 存
          </el-button>
          <el-button @click="resetForm">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  reactive,
  ref,
  toRefs,
  onMounted,
  watch,
  getCurrentInstance,
} from "vue";
import {
  taskPlanList,
  autoTaskList,
  addAutoTask,
  updateAutoTask,
  deleteAutoTask,
} from "@/api/scheduling/index";
import { selectStlyList } from "@/api/watershed/ads";
import { selectRsList } from "@/api/watershed/ads";
import moment from "moment";

defineOptions({
  name: 'PredictionIndex'
})

const { proxy } = getCurrentInstance();
// 详情里面的列表
//预警发布情况列表
const open = ref(false);
const loading = ref(false);
const showSearch = ref(true);
let formStatus = ref(1);
let dialogShow = ref(false);
const state = reactive({
  form: {},
  addForm: {
    basinId: "",
    // rainfallType: "",
    planId: "",
    durationRaining: 24,
    startTime: moment().format("YYYY-MM-DD 08:00:00"),
    endTime: "",
    forecastPeriod: "",
    calculateInterval: 24,
    timeStep: 60,
    warmUpPeriod: "",
  },
  status: [
    {
      label: "全部",
      value: "",
    },
    {
      label: "使用中",
      value: 1,
    },
    {
      label: "未使用",
      value: 0,
    },
    {
      label: "已过期",
      value: 2,
    },
  ],
  type: [
    {
      label: "预报降雨",
      value: 1,
    },
    {
      label: "实测降雨",
      value: 2,
    },
  ],
  set_hour: [
    {
      label: "12",
      value: 12,
    },
    {
      label: "24",
      value: 24,
    },
    {
      label: "48",
      value: 48,
    },
    {
      label: "72",
      value: 72,
    },
  ],

  taskList: [],
  autoList: [],
  waterPointAll: [],
  waterAreaList: [],
  queryParams: {
    // rainfallType: '',
    businessState: "",
    basinId: "",
    planId: "",
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
    planId: [{ required: true, message: "请选择方案", trigger: "change" }],
    basinId: [{ required: true, message: "请选择流域", trigger: "change" }],
    // rainfallType: [
    //     { required: true, message: '请选择降雨类型', trigger: 'change' }
    // ],
    durationRaining: [
      { required: true, message: "请选择降雨时长", trigger: "change" },
    ],
    forecastPeriod: [
      { required: true, message: "请输入预见期时间", trigger: "blur" },
    ],
    startTime: [
      { required: true, message: "请选择启动时间", trigger: "change" },
      //不得大于结束时间
      {
        validator: (rule, value, callback) => {
          if (
            moment(value).valueOf() > moment(state.addForm.endTime).valueOf()
          ) {
            return callback(new Error("启动时间不得大于结束时间"));
          } else {
            return callback();
          }
        },
      },
    ],
    endTime: [
      { required: true, message: "请选择结束时间", trigger: "change" },
      //不得小于启动时间
      //不得大于当前时间
      {
        validator: (rule, value, callback) => {
          if (
            moment(value).valueOf() < moment(state.addForm.startTime).valueOf()
          ) {
            return callback(new Error("结束时间不得小于启动时间"));
          } else if (moment(value).valueOf() < moment().valueOf()) {
            return callback(new Error("结束时间不得大于当前时间"));
          } else {
            return callback();
          }
        },
      },
    ],
    timeStep: [
      { required: true, message: "请选择计算步长", trigger: "change" },
    ],
    calculateInterval: [
      { required: true, message: "请选择计算间隔", trigger: "change" },
    ],
    warmUpPeriod: [
      { required: true, message: "请输入预热期时间", trigger: "blur" },
    ],
  },
  total: 0,
  taskChoose: [],
  detailDate: null,
});

const {
  queryParams,
  form,
  rules,
  status,
  type,
  set_hour,
  set_step,
  taskChoose,
  addForm,
  taskList,
  waterPointAll,
  waterAreaList,
  total,
  autoList,
} = toRefs(state);
const seeTask = (data) => {
  state.addForm = data;
  dialogShow.value = true;

  proxy.$nextTick(() => {
    state.taskChoose = state.taskList.filter(
      (item) => item.basinId == data.basinId
    );
    state.addForm.planId = data.planId;

    formStatus.value = 2; //查看
  });
};
const handleInput = (event) => {
  let value = event;
  value = value.replace(/[^0-9.]/g, "").slice(0, 3);
  if (value && value > 360) {
    value = 360;
  }
  state.addForm.warmUpPeriod = Number(value) + "";
};
const handleInput2 = (event) => {
  let value = event;
  value = value.replace(/[^0-9.]/g, "").slice(0, 3);
  if (value && value > 360) {
    value = 360;
  }
  state.addForm.forecastPeriod = Number(value) + "";
};
const editTask = (data) => {
  state.addForm = data;
  formStatus.value = 2; //编辑
  proxy.$nextTick(() => {
    state.taskChoose = state.taskList.filter(
      (item) => item.basinId == data.basinId
    );
    state.addForm.planId = data.planId;
  });
  dialogShow.value = true;
};
const deleteTask = (data) => {
  proxy
    .$confirm("是否删除该自动预报任务?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(async () => {
      let res = await deleteAutoTask({ id: data.id });
      if (res.code == 200) {
        proxy.$modal.msgSuccess("删除成功");
      } else {
        proxy.$modal.msgError(res.msg);
      }
      getList();
    });
};
const transformDataForTreeSelect = (data) => {
  // 递归地转换数据以匹配 el-tree-select 的需求
  return data.map((item) => ({
    label: item.data.name, // 使用 'name' 属性作为标签
    value: item.data.basinId, // 使用 'basinId' 属性作为值
    children: item.children ? transformDataForTreeSelect(item.children) : [], // 递归转换子节点
  }));
};
const submitForm = async () => {
  proxy.$refs.form.validate(async (valid) => {
    if (valid) {
      let res = await addAutoTask(state.addForm);
      if (res.code == 200) {
        proxy.$modal.msgSuccess("新增自动预报任务成功");
      } else {
        proxy.$modal.msgError(res.msg);
      }
      dialogShow.value = false;
      getList();
    }
  });
};
const updateForm = async () => {
  proxy.$refs.form.validate(async (valid) => {
    if (valid) {
      let res = await updateAutoTask(state.addForm);
      if (res.code == 200) {
        proxy.$modal.msgSuccess("更新自动预报任务成功");
      } else {
        proxy.$modal.msgError(res.msg);
      }
      dialogShow.value = false;
      getList();
    }
  });
};
const resetQuery = () => {
  proxy.resetForm("queryRef");
  getList();
};
watch(
  () => state.addForm.basinId,
  (newValue, oldValue) => {
    if (newValue) {
      state.taskChoose = state.taskList.filter(
        (item) => item.basinId == newValue
      );
      if (state.taskChoose.length == 0) {
        state.addForm.planId = "";
      }
    }
  }
);

const openForm = () => {
  dialogShow.value = true;
  formStatus.value = 1;
};
/** 表单重置 */
const resetForm = () => {
  proxy.$refs.form.resetFields();
  dialogShow.value = false;
};
onMounted(() => {
  getAllTask();
  getAllWater();
  getAllWaterPoint();
  getList();
});
const getList = async () => {
  loading.value = true;
  let res = await autoTaskList(state.queryParams);
  state.autoList = res.data || [];
  state.total = res.total || 0;
  loading.value = false;
};
const getAllTask = async () => {
  let res = await taskPlanList({
    pageNum: 1,
    pageSize: 99999,
  });
  if (res.data.records && res.data.records.length > 0) {
    state.taskList = res.data.records;
  } else {
    state.taskList = [];
  }
};
const getAllWater = async () => {
  let res = await selectStlyList({ pageNum: 1, pageSize: 999 });
  state.waterAreaList = res.data || [];
};
const getAllWaterPoint = async () => {
  let res = await selectRsList({ pageNum: 1, pageSize: 999 });
  state.waterPointAll = res.data.records || [];
  if (state.waterPointAll.length > 0) {
    state.waterPointAll = state.waterPointAll.reduce((accumulator, item) => {
      accumulator[item.registerCode] = item.resName;
      return accumulator;
    }, {});
  }
};
onUnmounted(() => {});
</script>
<style scoped lang="scss">
.card-box {
  padding-right: 0;
  padding-left: 0;
  margin-bottom: 5px;
}

.exceed {
  width: 95%;
  margin: 2px auto;
  height: 150px;
  display: flex;
  color: #555;

  .items {
    flex: 1;
    height: 100%;
    text-align: center;

    .value {
      margin: 24px 0;
      font-size: 18px;
    }

    .name {
      margin: 10px 0;
      color: #555;
      font-size: 16px;
    }
  }
}
</style>