<!--滚动预报配置-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="预报节点" prop="forecastStId">
        <el-select v-model="queryParams.forecastStId" placeholder="请选择" clearable style="width: 200px">
          <el-option v-for="dict in stList" :key="dict.stcd" :label="dict.stnm" :value="dict.stcd" />
        </el-select>
      </el-form-item>
      <el-form-item label="预报模型" prop="forecastModuleId">
        <el-select v-model="queryParams.forecastModuleId" placeholder="请选择" clearable style="width: 200px">
          <el-option v-for="dict in moduleList" :key="dict.id" :label="dict.moduleName" :value="dict.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="运行状态" prop="runStatus">
        <el-select v-model="queryParams.runStatus" placeholder="请选择" clearable style="width: 100px">
          <el-option v-for="dict in runStatusSelects" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="自动预报是否启用" prop="usedStatus">
        <el-select v-model="queryParams.usedStatus" placeholder="请选择" clearable style="width: 100px">
          <el-option v-for="dict in usedStatusSelects" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增配置</el-button>
      </el-col>
      <!--         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" :data="lyList">
      <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
      <el-table-column prop="forecastModuleName" label="预报方案名称" align="center"
        :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="forecastStNm" label="预报节点" :show-overflow-tooltip="true" align="center">
      </el-table-column>
      <el-table-column prop="preheatTime" label="预热期(天)" :show-overflow-tooltip="true" align="center"></el-table-column>
      <el-table-column prop="intervalHours" label="间隔(小时)" :show-overflow-tooltip="true"
        align="center"></el-table-column>
      <el-table-column prop="runTime" label="启动时间" :show-overflow-tooltip="true" align="center"></el-table-column>

      <el-table-column prop="usedStatus" label="启用状态" :show-overflow-tooltip="true" align="center">
        <template #default="scope">
          <span v-if="scope.row.usedStatus === 1" style="color: rgb(9,154,18)">启用</span>
          <span v-else style="color: rgba(255,85,85,0.82)">停用</span>
        </template>
      </el-table-column>
      <el-table-column prop="runStatus" label="运行状态" :show-overflow-tooltip="true" align="center">
        <template #default="scope">
          <span v-if="scope.row.runStatus === 0" style="color: rgba(21,19,19,0.82)">未启用</span>
          <span v-else-if="scope.row.runStatus === 1" style="color: rgb(12,175,22)">正常</span>
          <span v-else-if="scope.row.runStatus === 2" style="color: rgba(255,85,85,0.82)">失败</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left" width="210" class-name="small-padding fixed-width">
        <template #default="scope">
          <template v-if="scope.row.usedStatus === 1">
            <el-button link type="primary" icon="VideoPause" @click="handleStop(scope.row)">停用</el-button>
          </template>
          <template v-else>
            <el-button link type="primary" icon="VideoPlay" @click="handleStart(scope.row)">启用</el-button>
            <el-button link type="primary" icon="Edit" @click="handleInfo(scope.row)">编辑</el-button>
            <el-button link type="primary" icon="Delete" @click="handleClose(scope.row)">删除</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
    <!-- 自动预报配置编辑 -->
    <el-dialog title="自动预报配置编辑" v-model="open" width="450px" append-to-body>
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="预报方案名称" prop="forecastModuleId">
          <el-select v-model="form.forecastModuleId" placeholder="请选择" clearable style="width: 200px">
            <el-option v-for="dict in moduleList" :key="dict.id" :label="dict.moduleName" :value="dict.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="预报节点" prop="forecastStId">
          <el-select v-model="form.forecastStId" placeholder="请选择" clearable style="width: 200px">
            <el-option v-for="dict in stList" :key="dict.stcd" :label="dict.stnm" :value="dict.stcd" />
          </el-select>
        </el-form-item>
        <el-form-item label="预热期（天）">
          <el-input v-model="form.preheatTime" style="width: 200px" />
        </el-form-item>
        <el-form-item label="间隔（小时）">
          <el-input v-model="form.intervalHours" style="width: 200px" />
          <!--            <p>注：最大值72小时</p>-->
        </el-form-item>
        <el-form-item label="预报时间">
          <el-checkbox v-model="form.jiaoZheng" name="jiaoZheng">
            实时校正
          </el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import {
  deleteAutoForecast,
  getAutoForecastPage,
  getJieDianList,
  saveAutoRollingForecast,
  updateAutoForecast, updateUsedStatus
} from "@/api/watershed/forecast";

defineOptions({
  name: 'RollingConfig'
})

const { proxy } = getCurrentInstance();

const lyList = ref([]);
// 详情里面的列表
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const refreshTable = ref(true);
const uploadRef = ref('')
const stList = ref([]);
const stMap = ref({});
const moduleList = ref([]);

// 运行状态
const runStatusSelects = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '未启用',
    value: 0
  },
  {
    label: '正常',
    value: 1
  },
  {
    label: '失败',
    value: 2
  },
]
// 自动预报是否启用
const usedStatusSelects = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '启动',
    value: 1
  },
  {
    label: '停用',
    value: 0
  }
]

const data = reactive({
  form: {
    forecastStId: null, // 预报节点
    forecastModuleId: null, // 预报方案
    preheatTime: 15, // 预热期（单位天）
    intervalHours: 72, // 间隔（单位小时）
    ealTimeCorrection: 1, // 实时校正
  },
  queryParams: {
    runStatus: '',
    usedStatus: '',
    forecastStId: '',
    forecastModuleId: '',
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
  },
});

const { queryParams, form, rules } = toRefs(data);

function getList() {
  loading.value = true;
  getAutoForecastPage(queryParams.value).then(res => {
    res.data.forEach(item => {
      // item.createTime = item.createTime?.substring(0, 16).replace('T', ' ')
      item.forecastStNm = stMap.value[item.forecastStId]
    })
    lyList.value = res.data
    total.value = res.total
    loading.value = false;
  });
}
const disabledDateFn = (time) => {
  return time.getTime() > Date.now();
};
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
  }
  proxy.resetForm("menuRef");
}
// 新增配置
function handleAdd() {
  form.value.addFlag = true
  open.value = true
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 收到按钮操作 */
function handleAccept(row) {
  // reset(true);
  // if (row != null && row.lycode) {
  //   form.value.pcode = row.lycode;
  // } else {
  //   form.value.pcode = '';
  // }
  // open.value = true;
  // title.value = "添加流域";
  proxy.$modal.confirm('确定收到预警对象为【"' + row.name + '"】的预警?').then(function () {
    proxy.$modal.msgSuccess("收到了");
  })
}

function handleStop(row) {
  proxy.$modal.confirm('确定停用?').then(function () {
    let params = {
      id: row.id,
      usedStatus: 0
    }
    updateUsedStatus(params).then(res => {
      proxy.$modal.msgSuccess("已停用");
      getList()
    })
  })
}

function handleStart(row) {
  proxy.$modal.confirm('确定启用?').then(function () {
    let params = {
      id: row.id,
      usedStatus: 1
    }
    updateUsedStatus(params).then(res => {
      proxy.$modal.msgSuccess("已启用");
      getList()
    })
  })
}
/** 修改按钮操作 */
async function handleInfo(row) {
  reset();
  let obj = Object.assign(row, {});
  form.value = obj
  open.value = true;
  form.value.addFlag = false
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate(valid => {
    if (valid) {

      if (form.value.addFlag) {
        saveAutoRollingForecast(form.value).then(res => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        })
      } else {
        let params = {
          ...form.value
        }
        delete params.createTime
        updateAutoForecast(params).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

function handleClose(row) {
  proxy.$modal.confirm('确定删除模型名称为【"' + row.forecastModuleName + '"】的配置记录?').then(function () {
    return deleteAutoForecast(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

onMounted(async () => {
  const res = await getJieDianList()
  stList.value = res.data.stList
  moduleList.value = res.data.moduleList
  stList.value.forEach(st => {
    stMap.value[st.stcd] = st.stnm
  })
  getList();
})
</script>
<style scoped>
.card-box {
  padding-right: 0;
  padding-left: 0;
  margin-bottom: 5px;
}
</style>
