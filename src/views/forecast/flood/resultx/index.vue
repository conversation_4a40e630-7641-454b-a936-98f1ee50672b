<!--预报成果管理-->
<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="预报成果名称" prop="forecastName">
            <el-input
               v-model="queryParams.forecastName"
               placeholder="请输入名称"
               clearable
               style="width: 200px"
               @keyup.enter.native="handleQuery"
            />
         </el-form-item>
        <el-form-item label="预报时间" prop="warnTime">
          <el-date-picker class="borderColor" v-model="queryParams.time" type="datetimerange" format="YYYY-MM-DD HH:mm"
                          :clearable="false" date-format="YYYY/MM/DD ddd" time-format="hh:mm" range-separator="至" :disabled-date="disabledDateFn"
                          start-placeholder="开始时间" end-placeholder="结束时间" size="small" style="width: 266px;" />
        </el-form-item>
        <el-form-item label="节点" prop="forecastStId">
          <el-select v-model="queryParams.forecastStId" placeholder="请选择" clearable style="width: 200px">
            <el-option
                v-for="dict in stList"
                :key="dict.stcd"
                :label="dict.stnm"
                :value="dict.stcd"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="forecastType">
          <el-select v-model="queryParams.forecastType" placeholder="请选择" clearable style="width: 200px">
            <el-option
                v-for="dict in forecastTypeSelects"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item> <el-form-item label="状态" prop="releaseStatus">
          <el-select v-model="queryParams.releaseStatus" placeholder="请选择" clearable style="width: 200px">
            <el-option
                v-for="dict in releaseStatusSelects"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

     <el-row :gutter="10" class="mb8">
       <el-col :span="1.5">
         <el-button
             type="primary"
             plain
             icon="Plus"
             @click="handleAdd"
         >创建人工预报</el-button>
       </el-col>
     </el-row>

      <el-table
         v-if="refreshTable"
         v-loading="loading"
         :data="lyList"
         row-key="lycode"
         :default-expand-all="isExpandAll"
         :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
         <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
         <el-table-column prop="forecastName" label="预报成果名称"  width="165"  align="center" :show-overflow-tooltip="true" ></el-table-column>
         <el-table-column prop="forecastTime" label="预报时间" width="185" :show-overflow-tooltip="true" align="center"></el-table-column>
         <el-table-column prop="forecastCaseName" label="预报方案"  align="center"></el-table-column>
         <el-table-column prop="forecastStId" label="节点ID" width="165" :show-overflow-tooltip="true" align="center"></el-table-column>
         <el-table-column prop="forecastStName" label="节点名称" width="165" :show-overflow-tooltip="true" align="center"></el-table-column>
         <el-table-column prop="forecastPeoName" label="预报员" :show-overflow-tooltip="true" align="center"></el-table-column>
         <el-table-column prop="releaseStatus" label="状态" :show-overflow-tooltip="true"  align="center">
           <template #default="scope">
             <span v-if="scope.row.releaseStatus === 1" style="color: rgb(75,72,72)">未发布</span>
             <span v-else-if="scope.row.releaseStatus === 2" style="color: rgb(17,161,25)"  >已发布</span>
           </template>
         </el-table-column>

         <el-table-column label="操作" align="left" width="250" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="View" @click="handleInfo(scope.row)"  >查看</el-button>
               <el-button link type="primary" icon="Delete" @click="handleClose(scope.row)"  >删除</el-button>
               <template v-if="scope.row.releaseStatus === 1">
                 <el-button link type="primary" icon="Plus" @click="handleAccept(scope.row)"  >发布</el-button>
               </template>
              <template v-else-if="scope.row.releaseStatus === 2">
                <el-button link type="primary" icon="Plus" @click="handleCancelAccept(scope.row)"  >取消发布</el-button>
              </template>
            </template>
         </el-table-column>
      </el-table>
     <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
     />
      <!-- 添加或修改流域对话框 -->
      <el-dialog title="预警详情" v-model="open" width="850px" append-to-body>
        <el-row>
          <el-col :span="24" class="card-box">
            <el-card>
              <div class=" ">
                <el-row style="padding: 5px 0;">
                  <el-col :span="6"><div style="font-size: 18px;font-weight: bold">{{form.name}}</div></el-col>
                  <el-col :span="8">{{form.adnm || form.area}}</el-col>
                </el-row>
                <el-row  style="padding: 5px 0;">
                  <el-col :span="5">时间：{{form.createTime}}</el-col>
                  <el-col :span="4">预警类型：{{parseWarnType(form.warnType)}}</el-col>
                  <el-col :span="5">预警时效：{{parseWarnTimeType(form.timing)}}</el-col>
                  <el-col :span="5">预警等级：{{parseWarnLevel(form)}}</el-col>
                  <el-col :span="5">预警状态：  <span v-if="form.status === 1" style="color: rgba(255,85,85,0.82)">预警中</span>
                    <span v-else >预警关闭</span></el-col>
                </el-row>
                <el-row  style="padding: 5px 0;">
                  <el-col :span="24">预警描述：{{form.remark}}</el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>

          <el-col :span="24" class="card-box">
            <el-card>
              <template #header><span>超预警指标情况</span></template>
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <el-table :data="cjzbList" >
                  <el-table-column prop="warnIndicators" label="预警指标" width="160"></el-table-column>
                  <el-table-column prop="indicatorValue" label="指标值" width="160" align="center" ></el-table-column>
                  <el-table-column prop="detectionValue" label="监测/预报值" align="center"></el-table-column>
                  <el-table-column prop="differenceValue" label="超预警指标值"  align="center"></el-table-column>
                </el-table>
              </div>
            </el-card>
          </el-col>

          <el-col :span="24" class="card-box">
            <el-card>
              <template #header> <span>预警发布情况</span> </template>
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <el-table :data="yjfbList" >
                  <el-table-column type="index" label="接收人" width="65" ></el-table-column>
                  <el-table-column prop="name" label="电话号码" align="center" ></el-table-column>
                  <el-table-column prop="warnType" label="发送状态" align="center"></el-table-column>
                  <el-table-column prop="area" label="响应状态"  align="center"></el-table-column>
                  <el-table-column prop="area" label="失败原因"  align="center"></el-table-column>
                  <el-table-column prop="area" label="发送时间"  align="center"></el-table-column>
                </el-table>
              </div>
            </el-card>
          </el-col>
        </el-row>
         <template #footer>
            <div class="dialog-footer">
               <el-button @click="cancel">关闭</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup>
import {onUnmounted, onMounted } from "vue";
import {useRouter} from "vue-router";
import moment from "moment/moment";
import {getRainTimeByType} from "@/utils/common";
import {addLy, updateLy} from "@/api/watershed/ads";
import {selectWarnInfo} from "@/api/watershed/warning";
import {
  deleteForecastById,
  getForecastValueList,
  getJieDianList,
  releaseForecast
} from "@/api/watershed/forecast";

defineOptions({
  name: 'floodResult'
})

const { proxy } = getCurrentInstance();

const lyList = ref([]);
// 详情里面的列表
const cjzbList = ref([]); // 超警指标列表
const yjfbList = ref([]); // 预警发布情况列表
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const total = ref(0);
const isExpandAll = ref(false);
const refreshTable = ref(true);
const stList = ref([]);
const stMap = ref({});
const moduleList = ref([]);
const uploadRef = ref('')

// 预报状态
const forecastTypeSelects = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '人工预报',
    value: 1
  },
  {
    label: '自动预报',
    value:2
  },
]
// 发布状态
const releaseStatusSelects = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '未发布',
    value: 1
  },
  {
    label: '已发布',
    value: 2
  }
]
// 预警等级
const warnLevelSelects = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '超警戒',
    value:1
  },
  {
    label: '超保证',
    value:2
  },
  {
    label: '超历史',
    value:3
  },
  {
    label: '超汛限',
    value:4
  },
  {
    label: '超设计',
    value:5
  },
  {
    label: '超校核',
    value:6
  },
  {
    label: '雨量转移',
    value:7
  },
  {
    label: '水位转移',
    value:8
  },
  {
    label: '流量转移',
    value:9
  },
]
const data = reactive({
  form: {
  },
  queryParams: {
    time: ['',''],
    name: undefined,
    // adcd: '130800000000',
    adcd: undefined,
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
  },
});

const { queryParams, form, rules } = toRefs(data);

function getList() {
  loading.value = true;
  queryParams.value.startTime = moment(queryParams.value.time[0]).format('YYYY-MM-DD HH:mm:ss')
  queryParams.value.endTime =  moment(queryParams.value.time[1]).format('YYYY-MM-DD HH:mm:ss')
  getForecastValueList(queryParams.value).then(res => {
    res.data.forEach(item=>{
      // item.createTime = item.createTime?.substring(0, 16).replace('T', ' ')
      item.forecastStNm = stMap.value[item.forecastStId]
    })
    lyList.value = res.data
    total.value = res.total
    loading.value = false;
  });
}
const disabledDateFn = (time) => {
  return time.getTime() > Date.now();
};
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    pcode: '',
    addFlag: flag,
    geoType: 'people',
    lynm: undefined,
    icon: undefined,
    adcds: [],
  };
  cjzbList.value = []
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 发布 */
function handleAccept(row) {
  proxy.$modal.confirm('确定发布?').then(function() {
    let params = {
      id: row.id,
      releaseStatus:2
    }
    releaseForecast(params).then(res=>{
      proxy.$modal.msgSuccess("完成")
      getList()
    })
  })
}
/** 取消发布 */
function handleCancelAccept(row) {
  proxy.$modal.confirm('确定取消发布?').then(function() {
    let params = {
      id: row.id,
      releaseStatus: 1
    }
    releaseForecast(params).then(res=>{
      proxy.$modal.msgSuccess("完成")
      getList()
    })
  })
}

function parseWarnType (value) {
  for(let i =0 ;i<warnTypeSelects.length;i++){
    if (Number(warnTypeSelects[i].value) === value) {
      return warnTypeSelects[i].label
    }
  }
}
function parseWarnTimeType (value) {
  for(let i =0 ;i<warnTimeTypeSelects.length;i++){
    if (Number(warnTimeTypeSelects[i].value) === value) {
      return warnTimeTypeSelects[i].label
    }
  }
}
function parseWarnLevel (row) {
  if(row.warnType === 1) {
    return row.warnDesc
  } else {
    for(let i =0 ;i<warnLevelSelects.length;i++){
      if (Number(warnLevelSelects[i].value) === row.warnLevel) {
        return warnLevelSelects[i].label
      }
    }
  }
}
/** 修改按钮操作 */
async function handleInfo(row) {
  reset();
  let res = await selectWarnInfo(row.id)
  let obj = Object.assign(row,res.data);
  form.value = obj
  open.value = true;
  cjzbList.value = res.data
  title.value = "预警详情";
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate(valid => {
    if (valid) {
      // 处理关联行政区
      form.value.lyAndAdcds = []
      if(form.value.adcds?.length > 0) {
        let ll = []
        form.value.adcds.forEach(item=>{
          if(item.length > 1) {
            ll.push({
              adnm:'',
              adcd:item[item.length-1]
            })
          } else {
            ll.push({
              adnm:'',
              adcd:item[0]
            })
          }

        })
        form.value.lyAndAdcds = ll
      }
      if (form.value.addFlag) {
        addLy(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      } else {
        updateLy(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

function handleClose(row) {
  proxy.$modal.confirm('确定删除当前数据吗?').then(function() {
    return deleteForecastById(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function updateBound(geojson) {
  // 提交geojson数据到后台
  console.log(geojson)
  form.value.geom = geojson
}
const router = useRouter();

function handleAdd() {
  router.push('/forecast/flood/operational')
}

function handleSuccess(
    response
)  {
  if (response.code == 200) {
    form.value.geom = response.data
  } else {
    uploadRef.value?.clearFiles()
    proxy.$modal.msgSuccess(response.msg);
  }
}

function updloadFile() {
  // 上传文件解析

}

onMounted(async () => {
  const res = await getJieDianList()
  stList.value = res.data.stList
  moduleList.value = res.data.moduleList
  stList.value.forEach(st => {
    stMap.value[st.stcd] = st.stnm
  })
  // 默认查询预警时间近72小时
  queryParams.value.time = getRainTimeByType('72')
  // 结束时间改成23:59
  let endTimeStr = moment(queryParams.value.time[1]).format('YYYY-MM-DD HH:mm:ss')
  endTimeStr = endTimeStr.substring(0, 11) + '23:59:59'
  queryParams.value.time[1] = new Date(endTimeStr)
  getList();
})
onUnmounted(()=>{

})


</script>
<style scoped>
.card-box {
  padding-right: 0;
  padding-left: 0;
  margin-bottom: 5px;
}
</style>
