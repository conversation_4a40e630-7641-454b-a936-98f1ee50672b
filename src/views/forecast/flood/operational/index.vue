<!-- 洪水作业预报-->
<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="5" :xs="24">
        <el-form  label-position="left"  label-width="70px">
          <el-form-item label="预报节点" prop="forecastStId">
            <el-select v-model="form.forecastStId" placeholder="请选择" clearable style="width: 200px">
              <el-option
                  v-for="dict in stList"
                  :key="dict.stcd"
                  :label="dict.stnm"
                  :value="dict.stcd"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="降雨设置" prop="rainType">
            <el-radio-group v-model="form.rainType" @change="changeRainType">
              <el-radio :label="1">气象预报</el-radio>
              <el-radio :label="2">典型降雨</el-radio>
              <el-radio :label="3">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="降雨添加" prop="rainType" v-if="form.rainType===3">
            <el-table
                :data="form.rainArray"
                border
            >
              <el-table-column type="index" label="序号" width="55" align="center"></el-table-column>
              <el-table-column prop="drp" label="降雨量(mm)"  align="center" >
                <template #default="scope">
                  <el-input v-model="scope.row.drp" />
                </template>
              </el-table-column>
            </el-table>
            <el-button type="success" style="width: 80px;margin: 3px 0" size="small" icon="Delete" @click="handleRainArray">添加一行</el-button>
          </el-form-item>
          <el-form-item label="时间选择" prop="warnTime" v-if="form.rainType===2">
            <el-date-picker class="borderColor" v-model="form.time" type="datetimerange" format="YYYY-MM-DD HH:mm"
                            :clearable="false" date-format="YYYY/MM/DD ddd" time-format="hh:mm" range-separator="至" :disabled-date="disabledDateFn"
                            start-placeholder="开始时间" end-placeholder="结束时间" size="small" style="width: 266px;" />
          </el-form-item>
          <el-form-item label="放大系数" v-if="form.rainType===2">
            <el-input v-model="form.rainFactor" />
          </el-form-item>
          <el-form-item label="预报方案" prop="forecastModuleId">
            <el-select v-model="form.forecastModuleId" placeholder="请选择" clearable style="width: 200px">
              <el-option
                  v-for="dict in moduleList"
                  :key="dict.id"
                  :label="dict.moduleName"
                  :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="预热期">
            <el-input v-model="form.preheatTime" style="width: 100px" />&nbsp&nbsp天
          </el-form-item>
          <el-form-item label="预见期">
            <el-input v-model="form.foreseeTime" style="width: 100px" />&nbsp&nbsp小时
            <p>注：最大值72小时</p>
          </el-form-item>
          <el-form-item label="预报时间">
            <el-date-picker
                v-model="form.forecastTime"
                placeholder="预报时间"
                type="datetime" format="YYYY-MM-DD HH"
                :clearable="false" date-format="YYYY/MM/DD ddd" time-format="hh时"
                style="width: 100%"
            />
            <el-checkbox v-model="form.jiaoZheng" name="jiaoZheng">
              实时校正
            </el-checkbox>
          </el-form-item>

        </el-form>
        <div style="width: 100%;text-align: center;padding: 5px">
          <el-button type="success" style="width: 80px" @click="handleCountForecast">计算</el-button>
          <el-button plain @click="resetQuery" style="width: 80px">重置</el-button>
        </div>
        <div style="width: 100%;text-align: center;padding: 5px">
          <el-button plain style="width: 80px" @click="handleQuery">人工校正</el-button>
          <el-button plain style="width: 80px" @click="handleSaveForecast">保存方案</el-button>
        </div>
        <div style="width: 100%;text-align: center;padding: 5px">
          <el-button type="primary" style="width: 180px" @click="handleRenWuChe">任务车</el-button>
        </div>
      </el-col>
      <el-col :span="19" :xs="24">
        <div style="height: 40px;width: 100%">
          <el-button type="primary" @click="changeTabPanel('svg')">概化图</el-button>
          <el-button type="primary" v-show="dataList.length>0" @click="changeTabPanel('rvsr')">水库断面明细信息</el-button>
        </div>
        <div class="tabContainer" v-show="curentPanel === 'svg'">
          <svg-panel></svg-panel>
        </div>
        <div class="tabContainer" v-show="curentPanel === 'rvsr'">
          <div style="width: 100%;height:100%">
            <div style="width: 100%;height: 35px">
              <el-radio-group v-model="curStStcd" style="margin-bottom: 30px">
                <el-radio-button :value="itemX?.stcd" @click="changeSt(index)"  v-for="(itemX,index) in dataList">{{itemX?.stnm}}{{curStType}}</el-radio-button>
              </el-radio-group>
            </div>
            <div style="width: 100%;height: 35px;background: rgba(134,133,133,0.3);padding: 7px 10px; font-size: 14px;font-weight: normal">
              <span>预报洪峰 </span>
              <span style="font-weight: bold;padding-right: 10px;">{{curStItem?.forecastFlood?.toFixed(3)}}m³/s</span>
              <span>峰现时间 </span>
              <span style="font-weight: bold;padding-right: 10px;">{{curStItem?.dataTime}}</span>
              <span>预报洪量 </span>
              <span style="font-weight: bold;padding-right: 10px;">{{curStItem?.inboundTraffic?.toFixed(3)}}万m³</span>
            </div>
            <div class="detailCharts" id="detailCharts"></div>
            <el-table class="productTable" :data="curStItem.infoDataList" :header-cell-style="{ textAlign: 'center', color: '#363636' }"
                      :cell-style="{ color: '#424242', textAlign: 'center', fontSize: 13 + 'px' }">
              <el-table-column label="序号" type="index" width="50"></el-table-column>
              <el-table-column prop="dataTime" label="时间">
                <template v-slot="scope">
                  <span>{{ moment(scope.row.dataTime).format("YYYY-MM-DD HH") }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="curStType == 'RR'" prop="rainValue" label="雨量(mm)"></el-table-column>
              <el-table-column v-if="curStType == 'RR'" prop="inboundTraffic" label="入库流量 (m³/s)"></el-table-column>
              <el-table-column v-if="curStType == 'RR'" prop="actualWaterLevel" label="库水位(m)"></el-table-column>
              <el-table-column v-if="curStType == 'RR'" prop="outFlow" label="总出库流量 (m³/s)"></el-table-column>
              <el-table-column v-if="curStType != 'RR'" prop="rainValue" label="降雨量(mm)"></el-table-column>
              <el-table-column v-if="curStType != 'RR'" prop="outFlow" label="流量 (m³/s)"></el-table-column>
              <el-table-column v-if="curStType != 'RR'" prop="forecastWaterLevel" label="水位(m)"></el-table-column>
            </el-table>
          </div>
        </div>
        <div class="tabContainer" style="display: none">
        </div>
      </el-col>
    </el-row>
    <!-- 添加或修改流域对话框 -->
    <el-dialog title="方案保存" v-model="open" width="420px" append-to-body>
      <el-form ref="menuRef" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="预报结果名称" prop="forecastCaseName">
              <el-input v-model="form.forecastCaseName" placeholder="请填写"   />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预报方案名称"  >
              <el-input v-model="form.forecastName" placeholder="" disabled   />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预报时间" prop="forecastTime">
              <el-date-picker
                  v-model="form.forecastTime"
                  type="date"
                  placeholder="预报时间"
                  style="width: 100%"
                  disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预报员" prop="forecastPeoName">
              <el-input v-model="form.forecastPeoName" placeholder="请输入人员"   />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="方案描述" prop="forecastRemark">
              <el-input v-model="form.forecastRemark" placeholder="请输入描述信息" type="textarea"   />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSaveForecastSubmit">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog title="任务车"  v-model="open2" width="820px" height="550px" append-to-body>
      <div style="height: 500px;width: 780px;padding: 10px 10px">
        <div style="padding: 5px 0">
          <el-button type="danger" @click="handleCheSelectionDelete">批量删除</el-button>
        </div>
        <el-table
            v-loading="loading"
            :data="renWuCheList"
            border
            @selection-change="handleCheSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
          <el-table-column prop="forecastName" width="165" label="预报方案"  align="center" ></el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="195"  align="center" ></el-table-column>
          <el-table-column prop="status" label="状态" align="center">
            <template #default="scope">
              <template v-if="scope.row.status===1">
                <div style="color: rgba(20,121,215,0.92)">计算中</div>
              </template>
              <template v-else-if="scope.row.status===2">
                <div style="color: rgba(218,218,218,0.51)">计算失败</div>
              </template>
              <template v-else-if="scope.row.status===3">
                <div style="color: #097e10">计算完成</div>
              </template>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
            <template #default="scope">
              <span>
                <el-button link type="primary" icon="Delete" @click="handleRenWuCheDelete(scope.row)"  >删除</el-button>
                <el-button link type="primary" icon="Edit" @click="handleForecastInfo(scope.row)"  >查看</el-button>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="handleRenWuChe"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open2 = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  addLy,
  delLy,
  getAdcdTreeByAccount,
  getAllStations,
  selectStlyInfo,
  selectStlyList,
  updateLy
} from "@/api/watershed/ads";

import {nextTick, onUnmounted} from "vue";
import { useRouter } from 'vue-router'
import {
  deleteRenWuQuest,
  execCountForecast,
  getForecastInfo,
  getJieDianList,
  getRenWuCheList, saveForecastInfo
} from "@/api/watershed/forecast";
import SvgPanel from "@/components/Map/plugins/svgPanel";
import moment from "moment/moment";
import * as echarts from "echarts";

defineOptions({
  name: 'operational'
})

const { proxy } = getCurrentInstance();
const router = useRouter();
const stList = ref([]);
const renWuCheList = ref([]); // 任务车列表
const renWuCheSelectList = ref([]); // 任务车选中列表
const moduleList = ref([]);
const open = ref(false);
const open2 = ref(false);
const loading = ref(true);
const title = ref("");
const curGeom = ref("");
const curTitle = ref("");
const uploadRef = ref('')
const dataList = ref([])
const allStations = ref([])
const curStItem = ref({})
const curStStcd = ref(null)
const curStType = ref('river')
const curentPanel = ref("svg")
const total = ref(0);
const adcdOptions = ref([]); // 行政区树 默认获取一下


const disabledDateFn = (time) => {
  return time.getTime() > Date.now();
};

// 流域级别
const lyjbSelects = [
  {
    label: '一级',
    value: 1
  },
  {
    label: '二级',
    value:2
  },
  {
    label: '三级',
    value:3
  },
  {
    label: '四级',
    value:4
  },
  {
    label: '五级',
    value:5
  },
  {
    label: '六级',
    value:6
  },
  {
    label: '七级',
    value:7
  },
]
const data = reactive({
  form: {
    rainType: 1,
    rainArray: [],
    rainFactor: 1,
    forecastStId: null, // 预报节点
    forecastModuleId: null, // 预报方案
    preheatTime: 15, // 预热期（单位天）
    foreseeTime: 72, // 预见期（单位小时）
    forecastTime: null, // 预报时间
    ealTimeCorrection: 1, // 实时校正
  },
  queryParams: {
    pageNum: 1,
    pageSize: 5,
  },
  rules: {

  },
});

const { queryParams, form, rules } = toRefs(data);

/** 取消按钮 */
function cancel() {
  open.value = false;
  // reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    addFlag: flag,
    stnm: undefined,
    adcd: undefined,
  };
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
}
// 查询任务车
function handleRenWuChe(){
  open2.value = true
  loading.value = true
  getRenWuCheList(queryParams.value).then((res)=>{
    renWuCheList.value = res.data
    loading.value = false
    total.value = res.total
  })
}
function changeTabPanel(val) {
  curentPanel.value = val
}
async function changeSt(index) {
  curStItem.value = dataList.value[index] // 默认选中第一个
  curStStcd.value = curStItem.value.stcd
  curStType.value = allStations.data[curStStcd.value].STTP
}
function handleCheSelectionChange(data) {
  renWuCheSelectList.value = data
}
function handleCheSelectionDelete(data) {
  console.log(data)
}
// 向模型发起计算
function handleCountForecast(){
  let params = {
    ...form.value,
    rainStartTime: moment(form.value.time[0]).format('YYYY-MM-DD HH:mm:ss'),
    rainEndTime:  moment(form.value.time[1]).format('YYYY-MM-DD HH:mm:ss'),
    rainArray: "",
    forecastTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    ealTimeCorrection: form.value.jiaoZheng? 1 : 0
  }
  execCountForecast(params).then((res)=>{
    proxy.$modal.msgSuccess("保存成功");
    console.log(res)
  })
}
function handleSaveForecast() {
  open.value = true
}
function handleSaveForecastSubmit() {
  proxy.$modal.confirm('是否保存当前方案?').then(()=> {
    let params = {
      id: form.value.id,
      forecastCaseName: form.value.forecastCaseName,
      forecastRemark: form.value.forecastRemark,
      forecastPeoName: form.value.forecastPeoName,
    }

    return saveForecastInfo(params);
  }).then(() => {
    proxy.$modal.msgSuccess("保存成功");
    open.value = false
  }).catch(() => {});
}
// 删除任务车
function handleDeleteRenWuQuest(){
  let ids = ""
  deleteRenWuQuest(ids).then((res)=>{
    console.log(res)
  })
}

/** 重置按钮操作 */
function resetQuery() {
  form.value = {
    rainType: 1,
    rainArray: [],
    rainFactor: 1,
    forecastStId: null, // 预报节点
    forecastModuleId: null, // 预报方案
    preheatTime: 15, // 预热期（单位天）
    foreseeTime: 72, // 预见期（单位小时）
    forecastTime: null, // 预报时间
    ealTimeCorrection: 1, // 实时校正
  }
}
/** 新增按钮操作 */
function handleAdd(row) {
  reset(true);
  if (row != null && row.lycode) {
    form.value.pcode = row.lycode;
  } else {
    form.value.pcode = '';
  }
  open.value = true;
  title.value = "添加流域";
}

function gotoDangSt(row) {
  router.push({ path: '/warning/dangerAndStcd' });
  // 去危险区页面关联
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  let obj = Object.assign({},row);
  delete obj.children
  delete obj.createBy
  delete obj.createTime
  delete obj.updateBy
  delete obj.updateTime
  delete obj.remark
  form.value = obj

  open.value = true;
  title.value = "编辑信息";
}
function parseLyjb (data) {
  for(let i =0 ;i<lyjbSelects.length;i++){
    if (Number(lyjbSelects[i].value) === data.lyjb) {
      return lyjbSelects[i].label
    }
  }
}
/** 删除按钮操作 */
function handleRenWuCheDelete(row) {
  proxy.$modal.confirm('是否确认删除当前数据项?').then(function() {
    return deleteRenWuQuest(row.id);
  }).then(() => {
    handleRenWuChe();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function handleForecastInfo(row) {
  getForecastInfo(row.id).then(async res => {
    console.log(res)
    open2.value = false
    res.data.forecastStId = res.data.forecastStId + ''
    res.data.time = [new Date(res.data.rainStartTime), new Date(res.data.rainEndTime)]
    form.value = res.data
    dataList.value = res.data?.dataList || []
    curStItem.value = dataList.value[0] // 默认选中第一个
    curStStcd.value = curStItem.value.stcd
    allStations.value = await getAllStations({})
    curStType.value = allStations.value.data[curStStcd.value].STTP
    curentPanel.value = 'rvsr'
    createChart(curStItem.value.infoDataList, curStType.value)
  })
}
const option = ref( {
  grid: [
    {
      left: '60px',  //距左边距 留够name的宽度
      right: '60px',
      height: '25%',
      top: '50px',
      // containLabel: true
    },
    {
      left: '60px',
      right: '60px',
      top: '60%',
      height: '25%',
      bottom: '50px',
      // containLabel: true
    }
  ],  //两个图表
  // tooltip: {
  //   trigger: 'axis',
  //   axisPointer: {
  //     type: 'cross',
  //     crossStyle: {
  //       color: '#999'
  //     }
  //   }
  // },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross', crossStyle: { color: '#555' }, label: {
        backgroundColor: '#555'
      }
    }
  },
  legend: {
    width: "100%",
    // align: "auto",
    data: [
      '雨量',
      '预报流量',
      '实测流量',
      '预报水位',
      '实测水位'
    ],
    textStyle: {
      color: "rgba(66,65,65,0.51)",
      fontSize: 12,
      fontFamily: 'PingFang SC'
    }
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      boundaryGap: true,
      axisPointer: {
        type: 'shadow'
      },
      axisLabel: {
        // interval: "0",
        color: "rgba(66,65,65,0.51)",
        show: false
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#fff"
        },
        onZero: true
      },
      axisTick: {
        show: false
      },
      position: 'top'   //x坐标轴的位置
    },
    {
      type: 'category',
      data: [],
      boundaryGap: true,
      axisPointer: {
        type: 'shadow'
      },
      axisLabel: {
        // interval: "0",
        color: "rgba(66,65,65,0.51)",
      },
      axisLine: {
        lineStyle: {
          color: "#fff"
        },
        onZero: true
      },
      gridIndex: 1,
      position: 'bottom'   //x坐标轴的位置
    },
  ],
  axisPointer: {
    link: [
      {
        xAxisIndex: 'all'  //两个图表联动且tooltip合一
      }
    ]
  },
  dataZoom: [
    {
      type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
      show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
      start: 0,//数据窗口范围的起始百分比0-100
      end: 100,//数据窗口范围的结束百分比0-100
      xAxisIndex: [0, 1],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
      bottom: 0 //距离底部的距离
    },
    {
      type: 'inside',
      zoomOnMouseWheel: false, // 滚轮是否触发缩放
      moveOnMouseMove: true, // 鼠标滚轮触发滚动
      moveOnMouseWheel: true
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '雨量(mm)',
      inverse: true,  //进行翻转
      scale: true,
      alignTicks: true,
      nameLocation: 'end', //坐标轴名称显示位置
      nameTextStyle: {
        color: "rgba(66,65,65,0.51)",
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#fff"
        },
      },
      min: 0,
      axisLabel: {
        color: "rgba(66,65,65,0.51)",
      },
      splitLine: {
        lineStyle: {
          color: "#005b99"
        }
      }
    },
    {
      gridIndex: 1,   //第几个图标的y轴 根据grid的坐标
      alignTicks: true,
      type: 'value',
      name: '流量m³/s',
      nameLocation: 'end', //坐标轴名称显示位置
      scale: true,
      nameTextStyle: {
        color: "rgba(66,65,65,0.51)",
      },
      axisLabel: {
        color: "rgba(66,65,65,0.51)",
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#fff"
        },
      },
      splitLine: {
        lineStyle: {
          color: "#005b99"
        }
      }
    },
    {
      gridIndex: 1,
      alignTicks: true,
      type: 'value',
      name: '水位(m)',
      nameLocation: 'end', //坐标轴名称显示位置
      // scale: true,
      nameTextStyle: {
        color: "rgba(66,65,65,0.51)",
      },
      axisLabel: {
        color: "rgba(66,65,65,0.51)",
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#fff"
        },
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: "#fff"
        }
      }
    }
  ],
  series: [
    {
      name: '雨量',
      type: 'bar',
      // smooth: true,
      symbol: "none",
      color: "#00B7FF",
      data: []
    },
    {
      name: '预报流量',
      type: 'line',
      symbol: "none",
      lineStyle: {
        type: "dashed"
      },
      color: "#FFCE2C",
      xAxisIndex: 1,
      yAxisIndex: 1,
      data: []
    },
    {
      name: '实测流量',
      symbol: "none",
      type: 'line',
      color: "#00FF95",
      xAxisIndex: 1,
      yAxisIndex: 1,
      data: []
    },
    {
      name: '预报水位',
      symbol: "none",
      type: 'line',
      color: "#9E54FF",
      xAxisIndex: 1,
      yAxisIndex: 2,
      lineStyle: {
        type: "dashed"
      },
      data: []
    },
    {
      name: '实测水位',
      symbol: "none",
      type: 'line',
      color: "#9E54FF",
      data: [],
      xAxisIndex: 1,
      yAxisIndex: 2,

    },
    {
      type: 'line',
      xAxisIndex: 1,
      yAxisIndex: 2,
      markLine: {
        symbol: 'none',               // 去掉警戒线最后面的箭头
        label: {
          position: 'start',  // 将警示值放在哪个位置，三个值“start”,'middle','end'  开始  中点 结束
          formatter: '',
          color: '#F56C6C',
        },
        data: [{
          silent: true,             // 鼠标悬停事件  true没有，false有
          lineStyle: {               // 警戒线的样式  ，虚实  颜色
            // type: 'dash',
            type: 'solid',
            color: '#F56C6C',
            width: 1
          },
          xAxis: "10-05"
        }]
      },
      markArea: {
        label: {
          position: 'inside'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: 'rgba(233,110,110,0.07)' },
            { offset: 0.5, color: 'rgba(233,110,110,0.3)' },
            { offset: 1, color: 'rgba(233,110,110,0.5)' }
          ]),
          zIndex: -1
        },
        emphasis: {
          label: {
            show: true,
            position: 'inside'
          }
        },
        data: [
          [{
            xAxis: '00:00:00'
          },
            { xAxis: '01:00:00' }]
        ]
      }
    },
    {
      type: 'line',
      markLine: {
        symbol: 'none',               // 去掉警戒线最后面的箭头
        label: {
          position: 'start',  // 将警示值放在哪个位置，三个值“start”,'middle','end'  开始  中点 结束
          formatter: '',
          color: '#F56C6C',
        },
        data: [{
          silent: true,             // 鼠标悬停事件  true没有，false有
          lineStyle: {               // 警戒线的样式  ，虚实  颜色
            // type: 'dash',
            type: 'solid',
            color: '#F56C6C',
            width: 1
          },
          xAxis: ""
        }]
      },
      markArea: {
        label: {
          position: 'inside'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: 'rgba(233,110,110,0.07)' },
            { offset: 0.5, color: 'rgba(233,110,110,0.3)' },
            { offset: 1, color: 'rgba(233,110,110,0.5)' }
          ]),
          zIndex: -1
        },
        emphasis: {
          label: {
            show: true,
            position: 'inside'
          }
        },
        data: [
          [{
            xAxis: ''
          },
            { xAxis: '' }]
        ]
      }
    }
  ]

})
const option1 = ref({
  grid: [
    {
      left: '60px',  //距左边距 留够name的宽度
      right: '60px',
      height: '25%',
      top: '50px',
      // containLabel: true
    },
    {
      left: '60px',
      right: '60px',
      top: '60%',
      height: '25%',
      bottom: '50px',
      // containLabel: true
    }
  ],  //两个图表
  // tooltip: {
  //   trigger: 'axis',
  //   axisPointer: {
  //     type: 'cross',
  //     crossStyle: {
  //       color: '#999'
  //     }
  //   }
  // },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross', crossStyle: { color: '#555' }, label: {
        backgroundColor: '#555'
      }
    }
  },
  legend: {
    width: "80%",
    data: [
      '雨量',
      '实测出库流量',
      '拟定出库流量',
      '预报入库流量',
      '实测库水位',
      '拟定库水位',
    ],
    textStyle: {
      color: "rgba(66,65,65,0.51)",
      fontSize: 12,
      fontFamily: 'PingFang SC'
    }
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      boundaryGap: true,
      axisPointer: {
        type: 'shadow'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#fff"
        },
        onZero: true
      },
      axisLabel: {
        color: "rgba(66,65,65,0.51)",
        show: false
      },
      axisTick: {
        show: false
      },
      position: 'top'   //x坐标轴的位置
    },
    {
      type: 'category',
      data: [],
      boundaryGap: true,
      axisPointer: {
        type: 'shadow'
      },
      axisLabel: {
        // interval: "0",
        color: "rgba(66,65,65,0.51)"
      },
      axisLine: {
        lineStyle: {
          color: "rgba(66,65,65,0.51)"
        },
        onZero: true
      },
      gridIndex: 1,
      position: 'bottom'   //x坐标轴的位置
    },
  ],
  axisPointer: {
    link: [
      {
        xAxisIndex: 'all'  //两个图表联动且tooltip合一
      }
    ]
  },
  dataZoom: [
    {
      type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
      show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
      start: 0,//数据窗口范围的起始百分比0-100
      end: 100,//数据窗口范围的结束百分比0-100
      xAxisIndex: [0, 1],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
      bottom: 0 //距离底部的距离
    },
    {
      type: 'inside',
      zoomOnMouseWheel: false, // 滚轮是否触发缩放
      moveOnMouseMove: true, // 鼠标滚轮触发滚动
      moveOnMouseWheel: true
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '雨量(mm)',
      inverse: true,  //进行翻转
      scale: true,
      alignTicks: true,
      nameLocation: 'end', //坐标轴名称显示位置
      nameTextStyle: {
        color: "rgba(66,65,65,0.51)"
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(66,65,65,0.51)"
        },
      },
      min: 0,
      axisLabel: {
        color: "rgba(66,65,65,0.51)"
      },
      splitLine: {
        lineStyle: {
          color: "#005b99"
        }
      }
    },
    {
      gridIndex: 1,   //第几个图标的y轴 根据grid的坐标
      alignTicks: true,
      type: 'value',
      name: '流量m³/s',
      nameLocation: 'end', //坐标轴名称显示位置
      scale: true,
      nameTextStyle: {
        color: "rgba(66,65,65,0.51)"
      },
      axisLabel: {
        color: "rgba(66,65,65,0.51)"
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#fff"
        },
      },
      splitLine: {
        lineStyle: {
          color: "#005b99"
        }
      }
    },
    {
      gridIndex: 1,
      alignTicks: true,
      type: 'value',
      name: '水位(m)',
      nameLocation: 'end', //坐标轴名称显示位置
      // scale: true,
      nameTextStyle: {
        color: "rgba(66,65,65,0.51)"
      },
      axisLabel: {
        color: "rgba(66,65,65,0.51)"
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#fff"
        },
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: "rgba(66,65,65,0.51)"
        }
      }
    }
  ],
  series: [
    {
      name: '雨量',
      type: 'bar',
      // smooth: true,
      symbol: "none",
      color: "#00B7FF",
      data: []
    },
    {
      name: '实测出库流量',
      type: 'line',
      symbol: "none",
      color: "#FFCE2C",
      xAxisIndex: 1,
      yAxisIndex: 1,
      data: []
    },
    {
      name: '拟定出库流量',
      type: 'line',
      symbol: "none",
      color: "#FFCE2C",
      lineStyle: {
        type: "dashed"
      },
      xAxisIndex: 1,
      yAxisIndex: 1,
      data: []
    },
    {
      name: '预报入库流量',
      symbol: "none",
      type: 'line',
      color: "#29F8F8",
      lineStyle: {
        type: "dashed"
      },
      xAxisIndex: 1,
      yAxisIndex: 1,
      data: []
    },
    {
      name: '实测库水位',
      symbol: "none",
      type: 'line',
      color: "#FF8F1F",
      data: [],
      xAxisIndex: 1,
      yAxisIndex: 2,
    },
    {
      name: '拟定库水位',
      symbol: "none",
      type: 'line',
      color: "#FF8F1F",
      lineStyle: {
        type: "dashed"
      },
      data: [],
      xAxisIndex: 1,
      yAxisIndex: 2,
    },
  ]
})
function createChart(resList,type){
  if (type == 'rsvr') {
    option1.value.xAxis[0].data = []
    option1.value.xAxis[1].data = []
    option1.value.series[0].data = []
    option1.value.series[1].data = []
    option1.value.series[2].data = []
    option1.value.series[3].data = []
    option1.value.series[4].data = []
    option1.value.series[5].data = []
    resList.forEach(el => {
      option1.value.xAxis[0].data.push(moment(el.dataTime).format("MM-DD HH"))
      option1.value.xAxis[1].data.push(moment(el.dataTime).format("MM-DD HH"))
      option1.value.series[0].data.push(el.rainValue)
      option1.value.series[1].data.push(el.outFlow)
      option1.value.series[2].data.push(el.OTQ)
      option1.value.series[3].data.push(el.INQ)
      option1.value.series[4].data.push(el.SC_Z)
      option1.value.series[5].data.push(el.Z)
    })
    nextTick(() => {
      echarts
          .init(document.getElementById('detailCharts'))
          .dispose()
      let myEchart = echarts.init(
          document.getElementById('detailCharts')
      )
      myEchart.setOption(option1.value)
    })
  } else {
    option.value.xAxis[0].data = []
    option.value.xAxis[1].data = []
    option.value.series[0].data = []
    option.value.series[1].data = []
    option.value.series[2].data = []
    option.value.series[3].data = []
    option.value.series[4].data = []
    resList.forEach(el => {
      option.value.xAxis[0].data.push(moment(el.dataTime).format("MM-DD HH"))
      option.value.xAxis[1].data.push(moment(el.dataTime).format("MM-DD HH"))
      option.value.series[1].data.push(el.rainValue)
      option.value.series[0].data.push(el.outFlow)
      option.value.series[2].data.push(el.SC_Q)
      option.value.series[3].data.push(el.Z)
      option.value.series[4].data.push(el.SC_Z)
    })
    option.value.series[5].markLine.data[0].xAxis = moment(resList[0].IYMDH).format("MM-DD HH")
    option.value.series[5].markArea.data[0][0].xAxis = moment(resList[0].IYMDH).format("MM-DD HH")
    option.value.series[5].markArea.data[0][1].xAxis = moment(resList[0].IYMDH).format("MM-DD HH")
    option.value.series[6].markLine.data[0].xAxis = moment(resList[0].IYMDH).format("MM-DD HH")
    option.value.series[6].markArea.data[0][0].xAxis = moment(resList[0].IYMDH).format("MM-DD HH")
    option.value.series[6].markArea.data[0][1].xAxis = moment(resList[0].IYMDH).format("MM-DD HH")
    nextTick(() => {
      echarts
          .init(document.getElementById('detailCharts'))
          .dispose()
      let myEchart = echarts.init(
          document.getElementById('detailCharts')
      )
      myEchart.setOption(option.value)
    })
  }
}

function updateBound(geojson) {
  // 提交geojson数据到后台
  console.log(geojson)
  form.value.geom = geojson
}

function getForecastModuleName(){
  moduleList.value.forEach(obj=>{
    if(obj.id === form.value.forecastModuleId){
       return obj.moduleName
    }
  })
}
function handleSuccess(
    response
)  {
  if (response.code == 200) {
    form.value.geom = response.data
  } else {
    uploadRef.value?.clearFiles()
    proxy.$modal.msgSuccess(response.msg);
  }
}

function updloadFile() {
  // 上传文件解析

}

async function showMap(row) {
  let res = await selectStlyInfo(row.lycode)
  if (res.data) {
    open2.value = true
    await nextTick()
    curGeom.value = res.data
    curTitle.value = row.lynm
  } else {
    proxy.$modal.msgSuccess("没找到空间数据");
  }
}
function changeRainType(e) {
  if(e===1){

  } else if(e===2){

  } else if(e===3){

  }
}

function handleRainArray() {
  if(form.value.rainArray.length < Number(form.value.foreseeTime) ){
    form.value.rainArray.push({
      drp: 0,
      id: new Date().getTime()
    })
  } else {
    proxy.$modal.msgWarning("自动义雨量行数不能超过预见期大小");
  }

}

onMounted(()=>{
  // getAdcdTree({ adcd: ''}).then((res) => {
  getAdcdTreeByAccount({ adcd: ''}).then((res) => {
    adcdOptions.value = res.data
  })
  getJieDianList().then((res)=>{
    stList.value = res.data.stList
    moduleList.value = res.data.moduleList
  })
})
onUnmounted(()=>{

})

</script>
<style scoped>
.tabContainer{
  margin-top: 5px;
  height: 100%;
  min-height: 600px;
  width: 100%;
}
.el-form-item {
  display: flex;
  --font-size: 14px;
  margin-bottom: 8px;
}
:deep(.pagination-container .el-pagination) {
  right: 26px;
  position: absolute;
}
.detailCharts {
  width: 100%;
  height: 300px;
  margin: 20px 0;
}
</style>
