<!--预报方案管理- 与模型对接暂缓-->
<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="预报节点" prop="timing">
          <el-select v-model="queryParams.timing" placeholder="请选择" clearable style="width: 200px">
            <el-option
                v-for="dict in warnTimeTypeSelects"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table
         v-if="refreshTable"
         v-loading="loading"
         :data="lyList"
         row-key="lycode"
         :default-expand-all="isExpandAll"
         :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
         <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
         <el-table-column prop="name" label="预报方案名称"  align="center" :show-overflow-tooltip="true" ></el-table-column>
         <el-table-column prop="warnLevel" label="预报节点" :show-overflow-tooltip="true" align="center">
           <template #default="scope">
             <span>{{ parseWarnLevel(scope.row)}}</span>
           </template>
         </el-table-column>
        <el-table-column prop="yname" label="最大预见期（小时）" :show-overflow-tooltip="true" align="center"></el-table-column>
        <el-table-column prop="yname" label="模型" :show-overflow-tooltip="true" align="center"></el-table-column>
        <el-table-column prop="yname" label="步长(小时)" :show-overflow-tooltip="true" align="center"></el-table-column>
        <!--<el-table-column prop="startTime" label="启动时间" :show-overflow-tooltip="true" align="center"></el-table-column>-->
         <el-table-column label="操作" align="left" width="210" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Plus" @click="handleAccept(scope.row)"  >参数设置</el-button>
            </template>
         </el-table-column>
      </el-table>
     <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
     />
      <!-- 自动预报配置编辑 -->
      <el-dialog title="自动预报配置编辑" v-model="open" width="850px" append-to-body>
        <el-row>
          <el-col :span="24" class="card-box">
            <el-card>
              <div class=" ">
                <el-row style="padding: 5px 0;">
                  <el-col :span="6"><div style="font-size: 18px;font-weight: bold">{{form.name}}</div></el-col>
                  <el-col :span="8">{{form.adnm || form.area}}</el-col>
                </el-row>
                <el-row  style="padding: 5px 0;">
                  <el-col :span="5">时间：{{form.createTime}}</el-col>
                  <el-col :span="4">预警类型：{{parseWarnType(form.warnType)}}</el-col>
                  <el-col :span="5">预警时效：{{parseWarnTimeType(form.timing)}}</el-col>
                  <el-col :span="5">预警等级：{{parseWarnLevel(form)}}</el-col>
                  <el-col :span="5">预警状态：  <span v-if="form.status === 1" style="color: rgba(255,85,85,0.82)">预警中</span>
                    <span v-else >预警关闭</span></el-col>
                </el-row>
                <el-row  style="padding: 5px 0;">
                  <el-col :span="24">预警描述：{{form.remark}}</el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>

          <el-col :span="24" class="card-box">
            <el-card>
              <template #header><span>超预警指标情况</span></template>
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <el-table :data="cjzbList" >
                  <el-table-column prop="warnIndicators" label="预警指标" width="160"></el-table-column>
                  <el-table-column prop="indicatorValue" label="指标值" width="160" align="center" ></el-table-column>
                  <el-table-column prop="detectionValue" label="监测/预报值" align="center"></el-table-column>
                  <el-table-column prop="differenceValue" label="超预警指标值"  align="center"></el-table-column>
                </el-table>
              </div>
            </el-card>
          </el-col>

          <el-col :span="24" class="card-box">
            <el-card>
              <template #header> <span>预警发布情况</span> </template>
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <el-table :data="yjfbList" >
                  <el-table-column type="index" label="接收人" width="65" ></el-table-column>
                  <el-table-column prop="name" label="电话号码" align="center" ></el-table-column>
                  <el-table-column prop="warnType" label="发送状态" align="center"></el-table-column>
                  <el-table-column prop="area" label="响应状态"  align="center"></el-table-column>
                  <el-table-column prop="area" label="失败原因"  align="center"></el-table-column>
                  <el-table-column prop="area" label="发送时间"  align="center"></el-table-column>
                </el-table>
              </div>
            </el-card>
          </el-col>
        </el-row>
         <template #footer>
            <div class="dialog-footer">
               <el-button @click="cancel">关闭</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup>
import {onUnmounted, onMounted } from "vue";
import moment from "moment/moment";
import {addLy, updateLy} from "@/api/watershed/ads";
import {selectWarnInfo, turnOffWarn} from "@/api/watershed/warning";
import {getRainTimeByType} from "@/utils/common";

defineOptions({
  name: 'PlanManagement'
})

const { proxy } = getCurrentInstance();

const lyList = ref([]);
// 详情里面的列表
const cjzbList = ref([]); // 超警指标列表
const yjfbList = ref([]); // 预警发布情况列表
const open = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const title = ref("");
const total = ref(0);
const isExpandAll = ref(false);
const refreshTable = ref(true);
const uploadRef = ref('')

// 预警时效
const warnTimeTypeSelects = [
  {
    label: '全部',
    value: ''
  },
  // {
  //   label: '监测预警',
  //   value: 1
  // },
  // {
  //   label: '预报预警',
  //   value:2
  // },
]
// 预警类型
const warnTypeSelects = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '山洪预警',
    value:1
  },
  {
    label: '河道预警',
    value:2
  },
  {
    label: '水库预警',
    value:3
  },

]
// 预警等级
const warnLevelSelects = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '超警戒',
    value:1
  },
  {
    label: '超保证',
    value:2
  },
  {
    label: '超历史',
    value:3
  },
  {
    label: '超汛限',
    value:4
  },
  {
    label: '超设计',
    value:5
  },
  {
    label: '超校核',
    value:6
  },
  {
    label: '雨量转移',
    value:7
  },
  {
    label: '水位转移',
    value:8
  },
  {
    label: '流量转移',
    value:9
  },
]
const data = reactive({
  form: {
  },
  queryParams: {
    time: ['',''],
    name: undefined,
    // adcd: '130800000000',
    adcd: undefined,
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
  },
});

const { queryParams, form, rules } = toRefs(data);

function getList() {
  // loading.value = true;
  // queryParams.value.startTime = moment(queryParams.value.time[0]).format('YYYY-MM-DD HH:mm:ss')
  // queryParams.value.endTime =  moment(queryParams.value.time[1]).format('YYYY-MM-DD HH:mm:ss')
  // selectWarnPage(queryParams.value).then(res => {
  //   res.data.forEach(item=>{
  //     item.createTime = item.createTime?.substring(0, 16).replace('T', ' ')
  //   })
  //   lyList.value = res.data
  //   total.value = res.total
  //   loading.value = false;
  // });
}
const disabledDateFn = (time) => {
  return time.getTime() > Date.now();
};
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    pcode: '',
    addFlag: flag,
    geoType: 'people',
    lynm: undefined,
    icon: undefined,
    adcds: [],
  };
  cjzbList.value = []
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 收到按钮操作 */
function handleAccept(row) {
  // reset(true);
  // if (row != null && row.lycode) {
  //   form.value.pcode = row.lycode;
  // } else {
  //   form.value.pcode = '';
  // }
  // open.value = true;
  // title.value = "添加流域";
  proxy.$modal.confirm('确定收到预警对象为【"' + row.name + '"】的预警?').then(function() {
    proxy.$modal.msgSuccess("收到了");
  })
}

function parseWarnType (value) {
  for(let i =0 ;i<warnTypeSelects.length;i++){
    if (Number(warnTypeSelects[i].value) === value) {
      return warnTypeSelects[i].label
    }
  }
}
function parseWarnTimeType (value) {
  for(let i =0 ;i<warnTimeTypeSelects.length;i++){
    if (Number(warnTimeTypeSelects[i].value) === value) {
      return warnTimeTypeSelects[i].label
    }
  }
}
function parseWarnLevel (row) {
  if(row.warnType === 1) {
    return row.warnDesc
  } else {
    for(let i =0 ;i<warnLevelSelects.length;i++){
      if (Number(warnLevelSelects[i].value) === row.warnLevel) {
        return warnLevelSelects[i].label
      }
    }
  }
}
/** 修改按钮操作 */
async function handleInfo(row) {
  reset();
  let res = await selectWarnInfo(row.id)
  let obj = Object.assign(row,res.data);
  form.value = obj
  open.value = true;
  cjzbList.value = res.data
  title.value = "预警详情";
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate(valid => {
    if (valid) {
      // 处理关联行政区
      form.value.lyAndAdcds = []
      if(form.value.adcds?.length > 0) {
        let ll = []
        form.value.adcds.forEach(item=>{
          if(item.length > 1) {
            ll.push({
              adnm:'',
              adcd:item[item.length-1]
            })
          } else {
            ll.push({
              adnm:'',
              adcd:item[0]
            })
          }

        })
        form.value.lyAndAdcds = ll
      }
      if (form.value.addFlag) {
        addLy(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      } else {
        updateLy(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

function handleClose(row) {
  proxy.$modal.confirm('确定关闭预警对象为【"' + row.name + '"】的预警?').then(function() {
    return turnOffWarn(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("关闭预警成功");
  }).catch(() => {});
}

function updateBound(geojson) {
  // 提交geojson数据到后台
  console.log(geojson)
  form.value.geom = geojson
}

function handleSuccess(
    response
)  {
  if (response.code == 200) {
    form.value.geom = response.data
  } else {
    uploadRef.value?.clearFiles()
    proxy.$modal.msgSuccess(response.msg);
  }
}

function updloadFile() {
  // 上传文件解析

}

onMounted(()=>{
  // 默认查询预警时间近72小时
  queryParams.value.time = getRainTimeByType('72')
  // 结束时间改成23:59
  let endTimeStr = moment(queryParams.value.time[1]).format('YYYY-MM-DD HH:mm:ss')
  endTimeStr = endTimeStr.substring(0,11) + '23:59:59'
  queryParams.value.time[1] = new Date(endTimeStr)
  getList();
})
onUnmounted(()=>{

})


</script>
<style scoped>
.card-box {
  padding-right: 0;
  padding-left: 0;
  margin-bottom: 5px;
}
</style>
