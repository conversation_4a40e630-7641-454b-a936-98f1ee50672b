<!-- 调度指令管理（管理单位）-->
<template>
   <div class="app-container">
       <el-row :gutter="10" class="mb8">
         <el-tabs
             v-model="activeName"
             type="card"
             class="demo-tabs"
             @tab-click="handleClick" >
           <el-tab-pane label="已下发调令" name="distributeIPage">
             <distributeIPage style="width: 100%;height:100%"></distributeIPage>
           </el-tab-pane>
           <el-tab-pane label="接收调令" name="receiveIPage">
             <receiveIPage style="width: 100%;height:100%"></receiveIPage>
           </el-tab-pane>
           <el-tab-pane label="草稿箱" name="draftPage">
             <draftPage style="width: 100%;height:100%"></draftPage>
           </el-tab-pane>
         </el-tabs>
      </el-row>
   </div>
</template>

<script setup>
import distributeIPage from './distributeIPage.vue'
import receiveIPage from './receiveIPage.vue'
import draftPage from './draftPage.vue'
import { onUnmounted } from "vue";

defineOptions({
  name: 'instruction-mgt-unit-index'
})

const { proxy } = getCurrentInstance();

const activeName = ref('distributeIPage');
const data = reactive({
  form: {
  },
  queryParams: {
  },
  rules: {
  },
});

const { queryParams, form, rules } = toRefs(data);

function handleClick() {

}

onMounted(()=>{
})
onUnmounted(()=>{

})

</script>
<style scoped>
.demo-tabs{
  width: 100%;
  height: 100%;
}
</style>
