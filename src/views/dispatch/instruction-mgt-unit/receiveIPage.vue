<!--调度指令管理（管理单位）- 接受调令-->
<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="下发单位" prop="issuingUnit">
          <el-select v-model="queryParams.authorDeptList" placeholder="请选择" clearable style="width: 200px">
            <el-option
                v-for="dict in issuingUnitSelects"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发往单位" prop="issuingUnit">
          <el-select v-model="queryParams.deptIdList" placeholder="请选择" clearable style="width: 200px">
            <el-option
                v-for="dict in issuingUnitSelects"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发出时间" prop="publishTime">
          <el-date-picker class="borderColor" v-model="queryParams.time" type="datetimerange" format="YYYY-MM-DD HH:mm"
                          :clearable="false" date-format="YYYY/MM/DD ddd" time-format="hh:mm" range-separator="至"
                          start-placeholder="开始时间" end-placeholder="结束时间" size="small" style="width: 100%;" />
        </el-form-item>

         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <el-table
         v-if="refreshTable"
         v-loading="loading"
         :data="lyList"
         row-key="lycode"
         :default-expand-all="isExpandAll"
         :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
         <el-table-column type="index" label="序号" width="65" align="center"></el-table-column>
         <el-table-column prop="lynm" label="下发单位" :show-overflow-tooltip="true" ></el-table-column>
         <el-table-column prop="deptIdList" label="发往单位" :show-overflow-tooltip="true" ></el-table-column>
         <el-table-column prop="createTime" label="发出时间" width="165" align="center"></el-table-column>
         <el-table-column prop="instructionContent" label="调度内容" :show-overflow-tooltip="true" align="center"></el-table-column>
         <el-table-column prop="instructionTarget" label="调度目标" :show-overflow-tooltip="true" align="center"></el-table-column>
         <el-table-column prop="instructionReason" label="调度原因" :show-overflow-tooltip="true" align="center"></el-table-column>
         <el-table-column prop="backContent" label="执行反馈" :show-overflow-tooltip="true" align="center"></el-table-column>
         <el-table-column label="操作" align="center" width="210" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"  >编辑</el-button>
               <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)"  >附件</el-button>
            </template>
         </el-table-column>
      </el-table>
      <!-- 添加或修改流域对话框 -->
      <el-dialog title="预警详情" v-model="open" width="720px" append-to-body>
         <el-form ref="menuRef" :model="form" :rules="rules" label-width="130px">
             详情待开发
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup>
import {onUnmounted, nextTick } from "vue";
import {addLy, delLy, selectStlyInfo, selectStlyList, updateLy} from "@/api/watershed/ads";
import {getCopyPage, getDeptsByTenantId} from "@/api/watershed/forecast";

defineOptions({
  name: 'receiveIPage'
})

const { proxy } = getCurrentInstance();

const lyList = ref([]);
const open = ref(false);
const open2 = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const curGeom = ref("");
const curTitle = ref("");
const lyOptions = ref([]);
const isExpandAll = ref(false);
const refreshTable = ref(true);
const uploadRef = ref('')

// 调度执行状态
const dispatchExecStatusSelects = [
  {
    label: '全部',
    value:''
  },
  {
    label: '正在执行',
    value:1
  },
  {
    label: '调度异常',
    value:2
  },
  {
    label: '调度结束',
    value:3
  },
  {
    label: '未执行',
    value:4
  }
]
// 下发单位
const issuingUnitSelects = [
  {
    label: '单位1',
    value: 1
  },
  {
    label: '单位2',
    value:2
  },
]
// 预警类型
const warnTypeSelects = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '河道',
    value:1
  },
  {
    label: '水库',
    value:2
  },
  {
    label: '山洪',
    value:3
  },

]
const data = reactive({
  form: {
    geoType: 'people'
  },
  queryParams: {
    authorDeptList: [], // 下发单位
    deptIdList: [], // 发往单位
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
    lynm: [{ required: true, message: "流域名称不能为空", trigger: "blur" }],
    lycode: [{ required: true, message: "流域代码不能为空", trigger: "blur" }],
    orderNum: [{ required: true, message: "流域顺序不能为空", trigger: "blur" }],
    path: [{ required: true, message: "路由地址不能为空", trigger: "blur" }]
  },
});

const { queryParams, form, rules } = toRefs(data);

function getList() {
  loading.value = true
  let params = {
    pageNum: 1,
    pageSize: 10,
  }
  getCopyPage(params).then(res => {
    console.log(res, 'getCopyPage')
    lyList.value = res.data
    loading.value = false
  })
}
/** 查询流域下拉树结构 */
function getTreeselect() {
  lyOptions.value = [];
  selectStlyList({}).then(res => {
    lyOptions.value= res.data
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset(flag) {
  form.value = {
    pcode: '',
    addFlag: flag,
    geoType: 'people',
    lynm: undefined,
    icon: undefined,
    adcds: [],
  };
  proxy.resetForm("menuRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
  reset(true);
  getTreeselect();
  if (row != null && row.lycode) {
    form.value.pcode = row.lycode;
  } else {
    form.value.pcode = '';
  }
  open.value = true;
  title.value = "添加流域";
}

function handerCasadcds (list) {
  let allList = []
  if(list){
    list.forEach(item=>{
      let ls = getParentAdcds(item.adcd)
      allList.push(ls)
    })
  }
  return allList
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  await getTreeselect();
  let obj = Object.assign({geoType: 'people'},row);
  delete obj.children
  delete obj.createBy
  delete obj.createTime
  delete obj.updateBy
  delete obj.updateTime
  delete obj.remark
  delete obj.stAdCdBCode
  form.value = obj
  // 处理级联行政区
  let adcds = handerCasadcds(obj.lyAndAdcds)
  form.value.adcds = adcds || []
  // 处理几何数据
  let res = await selectStlyInfo(row.lycode)
  open.value = true;
  title.value = "修改流域";
  await nextTick()
  form.value.geom = res.data ||''
}
function parseLyjb (data) {
  for(let i =0 ;i<publishTimeTypeSelects.length;i++){
    if (Number(publishTimeTypeSelects[i].value) === data.lyjb) {
      return publishTimeTypeSelects[i].label
    }
  }
}
function parseKjlx (data) {
  for(let i =0 ;i<dispatchExecStatusSelects.length;i++){
    if (Number(dispatchExecStatusSelects[i].value) === data.lyjb) {
      return dispatchExecStatusSelects[i].label
    }
  }
}
function parseLyAndAdcds(data) {
  let ls = data.lyAndAdcds || []
  let str = ''
  ls.forEach(l=>{
    str += l.adnm + ','
  })
  return str.substring(0,str.length - 1)
}
function hideMap() {
  open2.value = false
}
async function showMap(row) {
  let res = await selectStlyInfo(row.lycode)
  if (res.data) {
    open2.value = true
    await nextTick()
    curGeom.value = res.data
    curTitle.value = row.lynm
  } else {
    proxy.$modal.msgSuccess("没找到空间数据");
  }
}
function getParentAdcds (adcd) {
  if (adcd) {
    // 把5个市辖区排除
    // 根据adcd 获取父 组，为了给级联用 2位 2位的 2 位的
    if(adcd.indexOf('0000')> -1) {
      // 肯定是省  直接返回
      return [adcd]
    } else if(adcd.substring(4,6).indexOf('00')> -1) {
      return [adcd.substring(0,2) +'0000',adcd.substring(0,4) + '00']
    } else {
      // 那就是县
      return [adcd.substring(0,2) +'0000',adcd.substring(0,4) + '00',adcd]
    }
  } else {
    return []
  }

}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["menuRef"].validate(valid => {
    if (valid) {
      // 处理关联行政区
      form.value.lyAndAdcds = []
      if(form.value.adcds?.length > 0) {
        let ll = []
        form.value.adcds.forEach(item=>{
          if(item.length > 1) {
            ll.push({
              adnm:'',
              adcd:item[item.length-1]
            })
          } else {
            ll.push({
              adnm:'',
              adcd:item[0]
            })
          }

        })
        form.value.lyAndAdcds = ll
      }
      if (form.value.addFlag) {
        addLy(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      } else {
        updateLy(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.lynm + '"的数据项?').then(function() {
    return delLy(row.lycode);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function updateBound(geojson) {
  // 提交geojson数据到后台
  console.log(geojson)
  form.value.geom = geojson
}

function handleSuccess(
    response
)  {
  if (response.code == 200) {
    form.value.geom = response.data
  } else {
    uploadRef.value?.clearFiles()
    proxy.$modal.msgSuccess(response.msg);
  }
}

function updloadFile() {
  // 上传文件解析

}

onMounted(()=>{
  getDeptsByTenantId().then(res=>{
    issuingUnitSelects.value = res.data
  })
  getList()
})
onUnmounted(()=>{

})

getList();
</script>
