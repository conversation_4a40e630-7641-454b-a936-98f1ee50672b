<template>
  <div class="box1">
    <div class="headBg">避险转移数据统计</div>
    <div class="cont" v-if="show == 'region'">
      <el-scrollbar>
        <div class="regionTop top">
          <div class="items" v-for="(item, index) in regionList" :key="index">
            <div class="value">
              {{ item.people }} <span>{{ item.unit }}</span>
            </div>
            <div class="name">{{ item.text }}</div>
          </div>
        </div>
        <div class="line">
          <div class="left"></div>
          <div class="center"></div>
          <div class="right"></div>
        </div>
        <div class="form">
          行政区划:
          <el-tree-select
            class="hedgTree"
            v-model="queryForm.adcd"
            size="small"
            :data="adcdList"
            :props="{ value: 'adcd', label: 'adnm', children: 'children' }"
            value-key="id"
            placeholder="请选择行政区"
            check-strictly
          />
          风险等级:
          <el-select
            v-model="queryForm.riskLevel"
            size="small"
            class="borderColor"
            style="width: 120px; margin-right: 10px"
          >
            <el-option label="红色" :value="1"></el-option>
            <el-option label="橙色" :value="2"></el-option>
            <el-option label="黄色" :value="3"></el-option>
            <el-option label="蓝色" :value="4"></el-option>
          </el-select>
          <el-button type="primary" size="small" @click="getList"
            >查询</el-button
          >
          <el-button
            size="small"
            @click="
              queryForm = {};
              getList();
            "
            >重置</el-button
          >
        </div>
        <el-table
          class="productTable"
          :data="list1"
          :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
          :cell-style="{
            color: '#ffffff',
            textAlign: 'center',
            fontSize: 13 + 'px',
          }"
          border
        >
          <el-table-column prop="risk" label="风险对象类型" width="90px">
            <template #default="scope">
              {{ scope.row.riskType == 1 ? "行政区" : "危险区" }}
            </template>
          </el-table-column>
          <el-table-column prop="riskId" label="风险对象代码"></el-table-column>
          <el-table-column
            prop="riskName"
            label="风险对象名称"
          ></el-table-column>
          <el-table-column
            prop="transferName"
            label="转移负责人"
          ></el-table-column>
          <el-table-column
            prop="transferTel"
            label="联系方式"
          ></el-table-column>
          <el-table-column prop="placementList" label="安置点">
            <template #default="scope">
              <div
                v-if="
                  scope.row.placementList && scope.row.placementList.length > 0
                "
              >
                <span v-for="item in scope.row.placementList" :key="item.id"
                  >{{ item.placeName }}.</span
                >
                <!-- 、 -->
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="riskReason" label="致险原因"></el-table-column>
          <el-table-column
            prop="occurrenceTime"
            label="发生时间"
          ></el-table-column>
          <el-table-column label="风险等级">
            <template #default="scope">
              <span v-if="scope.row.riskLevel == 1" style="color: #ff4040"
                >红色风险</span
              >
              <span v-if="scope.row.riskLevel == 2" style="color: #ff8f1f"
                >橙色风险</span
              >
              <span v-if="scope.row.riskLevel == 3" style="color: #ffc300"
                >黄色风险</span
              >
              <span v-if="scope.row.riskLevel == 4" style="color: #1890ff"
                >蓝色风险</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="riskLevel" label="处理措施"></el-table-column>
        </el-table>
      </el-scrollbar>
    </div>
    <div class="cont" v-if="show == 'person'">
      <div class="head">
        <div class="title">{{ riskName }}</div>
        <div class="btn" @click="show = 'region'">返回</div>
      </div>
      <div class="line">
        <div class="left"></div>
        <div class="center"></div>
        <div class="right"></div>
      </div>
      <div class="box2">
        <el-scrollbar>
          <div class="top">
            <div
              class="items"
              v-for="(item, index) in transferList"
              :key="index"
            >
              <div class="value">{{ item.people }} <span>人</span></div>
              <div class="name">{{ item.text }}</div>
            </div>
          </div>
          <div class="form">
            转移状态:
            <el-select
              v-model="transferType"
              size="small"
              class="borderColor"
              style="width: 200px; margin-right: 10px"
            >
              <el-option label="应转移" :value="1"></el-option>
              <el-option label="已转移" :value="2"></el-option>
            </el-select>
            <el-button type="primary" size="small" @click="getList2"
              >查询</el-button
            >
            <el-button
              size="small"
              @click="
                transferType = '';
                getList2();
              "
              >重置</el-button
            >
          </div>
          <el-table
            class="productTable"
            :data="list2"
            :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
            :cell-style="{
              color: '#ffffff',
              textAlign: 'center',
              fontSize: 13 + 'px',
            }"
            border
          >
            <el-table-column prop="transferType" label="转移状态">
              <template #default="scope">
                <span>{{
                  scope.row.transferType == 1 ? "应转移" : "已转移"
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="peoCode"
              label="居民户编码"
            ></el-table-column>
            <el-table-column
              prop="peoHolderName"
              label="户主名称"
            ></el-table-column>
            <el-table-column prop="peoName" label="人员姓名"></el-table-column>
            <el-table-column
              prop="transferName"
              label="转移负责人"
            ></el-table-column>
            <el-table-column label="安置方式">
              <template #default="scope">
                <span>{{
                  scope.row.resettleType == 1 ? "集中安置" : "自行安置"
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="placementName"
              label="安置点"
            ></el-table-column>
          </el-table>
          <div style="color: #fff; font-size: 16px; padding-left: 10px">
            避险转移通知
          </div>
          <div
            style="
              display: flex;
              justify-content: space-between;
              padding-left: 10px;
              margin-top: 10px;
            "
          >
            <span>{{ fileName }}</span>
            <el-button type="primary" link @click="handleDown">下载</el-button>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <div class="cont" v-if="show == 'resettle'">
      <div class="head">
        <div class="title">{{ placeData.placeName }}(安置点)</div>
        <div class="btn" @click="show = 'region'">返回</div>
      </div>
      <div class="line">
        <div class="left"></div>
        <div class="center"></div>
        <div class="right"></div>
      </div>
      <div class="box2">
        <el-scrollbar>
          <div class="top placeTop">
            <div class="items" v-for="(item, index) in placeList3" :key="index">
              <div class="value">{{ item.people }} <span>人</span></div>
              <div class="name">{{ item.text }}</div>
            </div>
          </div>
          <div style="display: flex">
            <div style="width: 50%; text-align: center">
              负责人: {{ placeData.peoName }}
            </div>
            <div style="width: 50%; text-align: center">
              地址: {{ placeData.address }}
            </div>
          </div>
          <div class="form">
            姓名:
            <el-input
              v-model="peoName"
              size="small"
              class="borderColor"
              style="width: 200px; margin-right: 10px"
            ></el-input>
            <el-button type="primary" size="small" @click="getList3"
              >查询</el-button
            >
            <el-button
              size="small"
              @click="
                peoName = '';
                getList3();
              "
              >重置</el-button
            >
          </div>
          <el-table
            class="productTable"
            :data="list3"
            :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
            :cell-style="{
              color: '#ffffff',
              textAlign: 'center',
              fontSize: 13 + 'px',
            }"
            border
          >
            <el-table-column prop="adnm" label="行政区划"></el-table-column>
            <el-table-column
              prop="peoCode"
              label="居民户编码"
            ></el-table-column>
            <el-table-column
              prop="peoHolderName"
              label="户主名称"
            ></el-table-column>
            <el-table-column prop="peoName" label="人员姓名"></el-table-column>
            <el-table-column
              prop="transferName"
              label="转移负责人"
            ></el-table-column>
            <el-table-column prop="transferJob" label="职务"></el-table-column>
            <el-table-column
              prop="transferTel"
              label="联系方式"
            ></el-table-column>
          </el-table>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import { getAdcdTree } from "@/api/watershed/ads";
import {
  indexPage,
  riskTransferResettleIPage,
  placeIndexPage,
} from "@/api/watershed/transfer";
export default defineComponent({
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      regionList: [
        {
          text: "风险对象",
          people: "0",
          unit: "个",
        },
        {
          text: "准备转移",
          people: "0",
          unit: "个",
        },
        {
          text: "立即转移",
          people: "0",
          unit: "个",
        },
        {
          text: "应转移",
          people: "0",
          unit: "人",
        },
        {
          text: "已转移",
          people: "0",
          unit: "人",
        },
        {
          text: "未转移",
          people: "0",
          unit: "人",
        },
      ],

      transferList: [
        {
          text: "应转移",
          people: "0",
        },
        {
          text: "已转移",
          people: "300",
          township: "0",
        },
        {
          text: "未转移",
          people: "0",
        },
      ],
      placeList3: [
        {
          text: "已安置",
          people: "0",
        },
        {
          text: "可安置",
          people: "0",
        },
      ],
      queryForm: {},
      adcdList: [],
      list1: [],
      show: "region",
      transferType: "",
      list2: [],
      riskName: "", //危险区需要赋值
      riskId: "",
      fileName: "",
      fileNamePath: "",
      list3: [],
      placeId: "",
      peoName: "",
      placeData: {},
    });
    const fakeData = {
      code: 200,
      data: {
        pageNum: null,
        pageSize: null,
        riskCount: 2,
        preTransferCount: 2,
        soonTransferCount: 0,
        shouldTransferCount: 0,
        beforeTransferCount: 0,
        alreadyTransferCount: 2,
        placeList: [
          {
            placeId: 1,
            placeName: "安置点1",
            peoNum: 500,
            alreadyPeoNum: 2,
            peoName: "张三",
            peoTel: "13012341234",
            cenLat: 37.182628,
            cenLon: 116.035812,
          },
          {
            placeId: 2,
            placeName: "安置点2",
            peoNum: 500,
            alreadyPeoNum: null,
            peoName: "李四",
            peoTel: "13012341234",
            cenLat: 37.198878,
            cenLon: 115.871548,
          },
          // {
          //   placeId: 3,
          //   placeName: "安置点3",
          //   peoNum: 500,
          //   alreadyPeoNum: null,
          //   peoName: "王五",
          //   peoTel: "13012341234",
          //   cenLat: 37.159529,
          //   cenLon: 115.985351,
          // },
          {
            placeId: 4,
            placeName: "安置点4",
            peoNum: 500,
            alreadyPeoNum: null,
            peoName: "赵六",
            peoTel: "13012341234",
            cenLat: 37.284348,
            cenLon: 115.990362,
          },
          // {
          //   placeId: 5,
          //   placeName: "安置点5",
          //   peoNum: 500,
          //   alreadyPeoNum: null,
          //   peoName: "钱七",
          //   peoTel: "13012341234",
          //   cenLat: 41.044778,
          //   cenLon: 117.983389,
          // },
          {
            placeId: 6,
            placeName: "安置点6",
            peoNum: 500,
            alreadyPeoNum: null,
            peoName: "孙八",
            peoTel: "13012341234",
            cenLat: 37.199733,
            cenLon: 116.109892,
          },
        ],
        ipage: {
          records: [
            {
              riskTransferId: 1,
              riskType: 1,
              riskId: "110101001000",
              riskName: "黑沟",
              riskReason: "水位超预警1.1mm",
              riskLevel: 1,
              transferName: "张三",
              transferTel: "19012301230",
              occurrenceTime: "2024-02-01 00:00:01",
              transferStatus: 2,
              adcdCenLon: 115.911629,
              adcdCenLat: 37.175499,
              danCenLon: null,
              danCenLat: null,
              danAdcd: null,
              transferStartTime: "2024-03-15 01:01:01",
              adcd: "110101001000",
              placementList: [
                {
                  adcd: "110101001000",
                  id: 1,
                  placeName: "安置点1",
                },
                {
                  adcd: "110101001000",
                  id: 2,
                  placeName: "安置点2",
                },
                {
                  adcd: "110101001000",
                  id: 3,
                  placeName: "安置点3",
                },
              ],
              peoTotal: 5,
              households: 2,
              shouldTransferCount: 1,
              alreadyTransferCount: 0,
              noTransferCount: 1,
            },
            {
              riskTransferId: 5,
              riskType: 1,
              riskId: "110101002000",
              riskName: "老虎沟",
              riskReason: "水位超汛限1.1mm",
              riskLevel: 1,
              transferName: "张三",
              transferTel: "19012301230",
              occurrenceTime: "2024-02-01 00:00:01",
              transferStatus: 2,
              adcdCenLon: 115.986783,
              adcdCenLat: 37.323347,
              danCenLon: null,
              danCenLat: null,
              danAdcd: null,
              transferStartTime: "2024-03-01 00:00:01",
              adcd: "110101002000",
              placementList: [
                {
                  adcd: "110101002000",
                  id: 4,
                  placeName: "安置点4",
                },
                {
                  adcd: "110101002000",
                  id: 5,
                  placeName: "安置点5",
                },
                {
                  adcd: "110101002000",
                  id: 6,
                  placeName: "安置点6",
                },
              ],
              peoTotal: null,
              households: null,
              shouldTransferCount: 1,
              alreadyTransferCount: 0,
              noTransferCount: 1,
            },
          ],
          total: 2,
          size: 100,
          current: 1,
          orders: [],
          optimizeCountSql: true,
          searchCount: true,
          countId: null,
          maxLimit: null,
          pages: 1,
        },
      },
    };
    onMounted(() => {
      getAdcdTree({
        adcd: "",
      }).then((res) => {
        state.adcdList = res.data[0].children;
      });
      getList();
      window.EventBus.$on("transfer/showRisk", (data) => {
        state.transferType = "";
        state.riskId = data.id;
        state.riskName = data.riskName;
        state.show = "person";
        // riskTransferResettleIPage({
        //   id: data.id,
        // }).then((res) => {
        let res = {
          msg: "操作成功",
          code: 200,
          data: {
            shouldTransferCount: 1,
            alreadyTransferCount: 1,
            noTransferCount: 0,
            fileName: "a15ec917-55d6-43eb-be3f-e50555ff4a0e-避险转移通知.pdf",
            ipage: {
              records: [
                {
                  pageNum: null,
                  pageSize: null,
                  id: null,
                  tenantId: null,
                  riskName: "黑沟村",
                  transferStatus: 2,
                  transferStartTime: "2024-03-15 01:01:01",
                  transferEndTime: "2024-03-15 01:01:01",
                  transferType: 2,
                  peoCode: "A1",
                  peoName: "张三",
                  peoHolderName: "张三",
                  transferName: "张三",
                  transferTel: "19012301230",
                  resettleType: 1,
                  placementId: 1,
                  placementName: null,
                },
              ],
              total: 1,
              size: 10,
              current: 1,
              orders: [],
              optimizeCountSql: true,
              searchCount: true,
              countId: null,
              maxLimit: null,
              pages: 1,
            },
          },
        };
        state.transferList[0].people = res.data.shouldTransferCount;
        state.transferList[1].people = res.data.alreadyTransferCount;
        state.transferList[2].people = res.data.noTransferCount;
        state.list2 = res.data.ipage.records;
        state.fileNamePath = res.data.fileName;
        state.fileName = res.data.fileName.slice(37);
        // });
      });
      window.EventBus.$on("transfer/showPlace", (data) => {
        state.peoName = "";
        state.placeId = data.id;
        state.show = "resettle";
        // placeIndexPage({
        //   placeId: data.id,
        // }).then((res) => {
        let res = {
          msg: "操作成功",
          code: 200,
          data: {
            pageNum: null,
            pageSize: null,
            placeId: 1,
            placeName: "安置点1",
            alreadyNum: 498,
            allNum: 500,
            peoName: "张三",
            peoTel: "13012341234",
            address: "地址信息",
            ipage: {
              records: [
                {
                  placementId: 1,
                  adcd: "110101001000",
                  peoCode: "A1",
                  peoHolderName: "张三",
                  peoName: "张三",
                  transferName: "张三",
                  transferTel: "19012301230",
                  transferJob: "村长",
                  adnm: "黑沟村",
                  padnm: "黑沟村",
                },
                {
                  placementId: 2,
                  adcd: "110101001000",
                  peoCode: "A2",
                  peoHolderName: "张三",
                  peoName: "李四",
                  transferName: "张三",
                  transferTel: "19012301230",
                  transferJob: "村长",
                  adnm: "老虎沟",
                  padnm: "老虎沟",
                },
              ],
              total: 2,
              size: 10,
              current: 1,
              orders: [],
              optimizeCountSql: true,
              searchCount: true,
              countId: null,
              maxLimit: null,
              pages: 1,
            },
          },
        };
        state.list3 = res.data.ipage.records;
        state.placeData.placeName = res.data.placeName;
        state.placeData.peoName = res.data.peoName + " " + res.data.peoTel;
        state.placeData.address = res.data.address;
        state.placeList3[0].value = res.data.alreadyNum
          ? res.data.alreadyNum
          : 0;
        state.placeList3[1].value = res.data.allNum ? res.data.allNum : 0;
        // });
      });
    });

    const getList = () => {
      // indexPage({
      //   pageNum: 1,
      //   pageSize: 100,
      //   adcd: state.queryForm.adcd,
      //   riskLevel: state.queryForm.riskLevel
      // }).then(res => {
      let res = fakeData;
      state.list1 = res.data.ipage.records;
      state.regionList[0].people = res.data.riskCount;
      state.regionList[1].people = res.data.preTransferCount;
      state.regionList[2].people = res.data.soonTransferCount;
      state.regionList[3].people = res.data.shouldTransferCount;
      state.regionList[4].people = res.data.alreadyTransferCount;
      state.regionList[5].people = res.data.beforeTransferCount;
      window.EventBus.$emit("transfer/risk", state.list1);
      window.EventBus.$emit("transfer/place", res.data.placeList);
      // })
    };
    const getList2 = () => {
      // riskTransferResettleIPage({
      //   transferType: state.transferType,
      //   id: state.riskId,
      // }).then((res) => {
      let res = {
        msg: "操作成功",
        code: 200,
        data: {
          shouldTransferCount: 1,
          alreadyTransferCount: 1,
          noTransferCount: 0,
          fileName: "a15ec917-55d6-43eb-be3f-e50555ff4a0e-避险转移通知.pdf",
          ipage: {
            records: [
              {
                pageNum: null,
                pageSize: null,
                id: null,
                tenantId: null,
                riskName: "东华门街道",
                transferStatus: 2,
                transferStartTime: "2024-03-15 01:01:01",
                transferEndTime: "2024-03-15 01:01:01",
                transferType: 2,
                peoCode: "A1",
                peoName: "张三",
                peoHolderName: "张三",
                transferName: "张三",
                transferTel: "19012301230",
                resettleType: 1,
                placementId: 1,
                placementName: null,
              },
            ],
            total: 1,
            size: 10,
            current: 1,
            orders: [],
            optimizeCountSql: true,
            searchCount: true,
            countId: null,
            maxLimit: null,
            pages: 1,
          },
        },
      };

      state.transferList[0].people = res.data.shouldTransferCount;
      state.transferList[1].people = res.data.alreadyTransferCount;
      state.transferList[2].people = res.data.noTransferCount;
      state.list2 = res.data.ipage.records;
      state.fileNamePath = res.data.fileName;
      state.fileName = res.data.fileName.slice(37);
      // });
    };
    const getList3 = () => {
      // placeIndexPage({
      //   peoName: state.peoName,
      //   placeId: state.placeId,
      // }).then((res) => {
      let res = {
        msg: "操作成功",
        code: 200,
        data: {
          pageNum: null,
          pageSize: null,
          placeId: 1,
          placeName: "安置点1",
          alreadyNum: 498,
          allNum: 500,
          peoName: "张三",
          peoTel: "13012341234",
          address: "地址信息",
          ipage: {
            records: [
              {
                placementId: 1,
                adcd: "110101001000",
                peoCode: "A1",
                peoHolderName: "张三",
                peoName: "张三",
                transferName: "张三",
                transferTel: "19012301230",
                transferJob: "镇长",
                adnm: "东华门街道",
                padnm: "东城区",
              },
              {
                placementId: 2,
                adcd: "110101001000",
                peoCode: "A2",
                peoHolderName: "张三",
                peoName: "李四",
                transferName: "张三",
                transferTel: "19012301230",
                transferJob: "镇长",
                adnm: "东华门街道",
                padnm: "东城区",
              },
            ],
            total: 2,
            size: 10,
            current: 1,
            orders: [],
            optimizeCountSql: true,
            searchCount: true,
            countId: null,
            maxLimit: null,
            pages: 1,
          },
        },
      };
      state.list3 = res.data.ipage.records;
      state.placeData.placeName = res.data.placeName;
      state.placeData.peoName = res.data.peoName + " " + res.data.peoTel;
      state.placeData.address = res.data.address;
      state.placeData.alreadyNum = res.data.alreadyNum;
      state.placeData.allNum = res.data.allNum;
      // });
    };
    const handleDown = () => {
      proxy.download(
        "/risks/risks/file/downloadFile",
        {
          fileName: state.fileNamePath,
        },
        `${state.fileName}通知书_${new Date().getTime()}.pdf`
      );
    };
    return {
      ...toRefs(state),
      getList,
      getList2,
      getList3,
      handleDown,
    };
  },
});
</script>
<style scoped lang="scss">
.box1 {
  width: 100%;
  height: 100%;

  .headBg {
    width: 319px;
    height: 32px;
    background-image: url(../image/hedgTop.png);
    line-height: 32px;
    padding-left: 10px;
    color: #fff;
    font-size: 16px;
    letter-spacing: 1px;
  }
}

.cont {
  width: 100%;
  color: #fff;
  padding: 10px;
  background-color: rgba(1, 28, 70, 0.7);
  height: calc(100% - 34px);
  border: 1px solid #1c8bda;
  background-color: #022757;
}

.cont {
  .head {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;

    .title {
      color: #ccebff;
      font-size: 18px;
    }

    .btn {
      width: 48px;
      height: 24px;
      border: 1px solid #0090ff;
      color: #a3deff;
      text-align: center;
      font-size: 14px;
      line-height: 20px;
      border-radius: 2px;
      cursor: pointer;
    }
  }

  .line {
    margin-bottom: 8px;

    .right {
      background-color: #ffd154;
    }

    .left {
      background-color: #ffd154;
    }
  }

  .box2 {
    width: 100%;
    height: calc(100% - 60px);
  }
}

.top {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;

  .items {
    width: 149px;
    height: 121px;
    text-align: center;
    background-image: url(../../screen/image/blueBack.png);
    color: #fff;
    font-size: 14px;
    overflow: hidden;

    &:nth-child(2) {
      background-image: url(../../screen/image/greenBack.png);
    }

    &:nth-child(3) {
      background-image: url(../../screen/image/yellowBack.png);
    }

    .value {
      margin: 30px 0 0;
      font-size: 22px;

      span {
        font-size: 12px;
      }
    }

    .name {
      margin-bottom: 20px;
    }
  }
}

.regionTop {
  flex-wrap: wrap;

  .items {
    background-image: url(../../screen/image/redBack.png);

    &:nth-child(2) {
      background-image: url(../../screen/image/yellowBack.png);
    }

    &:nth-child(3) {
      background-image: url(../../screen/image/orangeBack.png);
    }

    &:nth-child(4) {
      background-image: url(../../screen/image/blueBack.png);
    }

    &:nth-child(5) {
      background-image: url(../../screen/image/greenBack.png);
    }
  }
}

.placeTop {
  .items {
    &:nth-child(2) {
      background-image: url(../../screen/image/yellowBack.png);
    }
  }
}

.form {
  font-size: 14px;
  margin-top: 20px;

  display: flex;
  align-items: center;
  color: #b7daff;
}

:deep(.productTable) {
  background-color: #022757 !important;
  margin: 10px 0 20px;

  tr {
    background-color: #022757 !important;

    th {
      background-color: #083f86 !important;
      color: #fff !important;
      border-bottom: 1px solid #17365a;
      border-color: #005e9d !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #005e9d;
      border-color: #005e9d !important;
    }
  }

  --el-table-row-hover-bg-color: #022757;
  --el-table-border-color: #005e9d;
  --el-bg-color: #022757;

  .el-table__inner-wrapper {
    &::before {
      background-color: #005e9d;
    }
  }
}

.borderColor {
  :deep(.el-input__wrapper) {
    border-color: #409eff;
    // box-shadow: none;
    background-color: transparent;
    --el-input-border-color: #409eff;

    .el-input__inner {
      color: #fff;
    }
  }
}

.hedgTree {
  width: 120px;
  margin-right: 10px;

  :deep(.el-input__wrapper) {
    border-color: #409eff;
    // box-shadow: none;
    background-color: transparent;
    --el-input-border-color: #409eff;

    .el-input__inner {
      color: #fff;
    }
  }
}
</style>