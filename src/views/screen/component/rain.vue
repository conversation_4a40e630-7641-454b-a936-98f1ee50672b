<template>
  <div class="box">
    <div class="title">雨情信息列表</div>
    <div class="line">
      <div class="left"></div>
      <div class="center"></div>
      <div class="right"></div>
    </div>
    <div class="cont table-container">
      <div class="form">
        <el-form :model="form" label-width="80px" size="small">
          <el-form-item label="降雨量级">
            <div class="list">
              <div class="items three">
                <el-checkbox v-model="form.checked1" size="large" />
                <div class="round level0"></div>
                <div>未降雨</div>
              </div>
              <div class="items three">
                <el-checkbox v-model="form.checked2" size="large" />
                <div class="round level1"></div>
                <div>0~10.0mm</div>
              </div>
              <div class="items three">
                <el-checkbox v-model="form.checked3" size="large" />
                <div class="round level2"></div>
                <div>10.0~25.0mm</div>
              </div>
              <div class="items">
                <el-checkbox v-model="form.checked4" size="large" />
                <div class="round level3"></div>
                <div>25.0~50.0mm</div>
              </div>
              <div class="items">
                <el-checkbox v-model="form.checked5" size="large" />
                <div class="round level4"></div>
                <div>50.0~100.0mm</div>
              </div>
              <div class="items">
                <el-checkbox v-model="form.checked6" size="large" />
                <div class="round level5"></div>
                <div>100.0~250.0mm</div>
              </div>
              <div class="items">
                <el-checkbox v-model="form.checked7" size="large" />
                <div class="round level6"></div>
                <div>250mm以上</div>
              </div>
            </div>

          </el-form-item>
          <el-form-item label="时间选择">
            <el-select v-model="form.value" class="borderColor" style="width: 114px;" @change="changeTimeSelect">
              <el-option label="今日8时到现在" value="today"></el-option>
              <el-option label="昨日8时到现在" value="yesterday"></el-option>
              <el-option label="最近12小时" value="12"></el-option>
              <el-option label="最近48小时" value="48"></el-option>

              <el-option label="自定义" value="1000"></el-option>
            </el-select>
            <el-date-picker class="borderColor" v-model="time" type="datetimerange" format="YYYY-MM-DD HH:mm"
              :clearable="false" date-format="YYYY/MM/DD ddd" time-format="hh:mm" range-separator="至"
              :readonly="form.value != 1000" start-placeholder="开始时间" end-placeholder="结束时间" size="small"
              style="width: 100px;margin-left: 10px;" />
          </el-form-item>
          <el-form-item label="专题图">
            <el-checkbox v-model="form.checked" @change="openContour" label="等值面" size="large" />
          </el-form-item>
        </el-form>
      </div>
      <div class="title">
        <div>雨量信息列表</div>
        <div>
          <el-button type="primary" size="small" @click="handleQuery">查询</el-button>
          <!-- <el-button type="primary" size="small">导出</el-button> -->
        </div>
      </div>
      <div class="tablesBox">
        <div class="tablesContent">
          <!-- 注意关键点：el-table 要设置 height="0" -->
          <el-table v-loading="loading" element-loading-background="rgba(122, 122, 122, 0.1)" class="productTable"
            :data="peakList" :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
            :cell-style="{ color: '#ffffff', textAlign: 'center', fontSize: 13 + 'px' }" style="margin-bottom: 20px;"
            @row-click="handleClick"
            max-height="550"
            >
            <el-table-column type="index" label="序号" width="55"></el-table-column>
            <el-table-column prop="stnm" label="站名" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column prop="totalRainValue" label="雨量(mm)" width="110"></el-table-column>
            <!--            <el-table-column prop="RANK" label="等级" width="90"></el-table-column>-->
            <el-table-column prop="county" label="政区" :show-overflow-tooltip="true"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import { getRainTimeByType, getHourTime } from "@/utils/common";
import moment from "moment";
import { measuringStationList } from "@/api/watershed/query/index"

export default defineComponent({
  setup() {
    const state = reactive({
      form: {
        value: 'today', // 一般默认是今日累计雨量
        checked: false,
        checked1: false,
        checked2: true,
        checked3: true,
        checked4: true,
        checked5: true,
        checked6: true,
        checked7: true,
      },
      loading: false,
      flag: true, // 标识第一次
      time: [],
      adcd: '', // 131000
      lycode: '', // 131000
      peakList: [],
      bgtm: '',
      endtm: ''
    })
    onMounted(() => {
      // 增加区域切换监听
      window.EventBus.$on('changeAreaSelect', (data) => {
        if (data.type === 'adcd') {
          state.adcd = data.code
          state.lycode = null
        } else if (data.type === 'watershed') {
          state.adcd = null
          state.lycode = data.code
        }
        handleQuery()
      })
      window.EventBus.$on('rainPanel/visible', (data) => {
        let contourLayer = window.viewer.getLayerGroup('contour')
        if (contourLayer) {
          contourLayer.show = true
        }
        // 处理如果从概览过来的话，时间需要和雨量分级或者雨量极值时间统一，然后刷新
        if (data.timeType) {
          state.form.value = '1000'
          state.time = getHourTime(data.timeType)
        }
        if (data.time && data.time.length > 0) {
          state.time = data.time
        }
        if (data.hasOwnProperty('rank')) {
          if (data.rank) {
            state.form.checked1 = false
            state.form.checked2 = false
            state.form.checked3 = false
            state.form.checked4 = false
            state.form.checked5 = false
            state.form.checked6 = false
            state.form.checked7 = false
            state.form['checked' + data.rank] = true
          } else {
            state.form.checked1 = false
            state.form.checked2 = true
            state.form.checked3 = true
            state.form.checked4 = true
            state.form.checked5 = true
            state.form.checked6 = true
            state.form.checked7 = true
          }

        }
        handleQuery()
      })
      handleTime(state.form.value)
      // handleQuery() // 默认查询数据， 地图上会自己查询
    })
    // 处理时间
    const handleTime = (value) => {
      let time = getRainTimeByType(value)
      state.time = time
    }
    const handleQuery = () => {
      getPeakValue()
    }
    const getPeakValue = async () => {
      let obj2
      let ranks = []
      if (state.form.checked1) {
        ranks.push('0')
      }
      if (state.form.checked2) {
        ranks.push('1')
      }
      if (state.form.checked3) {
        ranks.push('2')
      }
      if (state.form.checked4) {
        ranks.push('3')
      }
      if (state.form.checked5) {
        ranks.push('4')
      }
      if (state.form.checked6) {
        ranks.push('5')
      }
      if (state.form.checked7) {
        ranks.push('6')
      }
      state.loading = true
      //
      //
      const res2 = await measuringStationList({

        startTime: moment(state.time[0]).format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(state.time[1]).format('YYYY-MM-DD HH:mm:ss'),
        // keyword: '',
        rainLevel: ranks.length > 0 ? ranks.join(',') : null,
        pageSize: 99999
      })
      state.loading = false
      obj2 = res2.data.ipage.records || []
      let rainList = obj2

      // 最大降雨量在上边 ，理论后端需要排好序
      rainList.sort(function (a, b) {
        return b.totalRainValue - a.totalRainValue; // 降序排序
      });
      rainList.forEach((item) => {
        item['nowTime'] = moment(state.time[0]).format('YYYY-MM-DD HH:mm:ss')
        item['beforeTime'] = moment(state.time[1]).format('YYYY-MM-DD HH:mm:ss')
        item['adnm'] = item.county
      })
      state.peakList = rainList
      // 刷新地图数据
      window.EventBus.$emit('rain/update', rainList)
      // 如果等值面勾选，那么也刷新等值面
      openContour()
    }
    const handleClick = (e) => {
      // 雨情弹框
      // 定位飞行
      let item = {
        lng: e.lgtd,
        lat: e.lttd
      }
      window.EventBus.$emit('flyToPostion', item)
    }
    const openContour = (e) => {
      if (state.form.checked) {
        let points = ""
        state.peakList.forEach(item => {
          points += item.lgtd + ',' + item.lttd + ',' + (item.totalRainValue || 0) + ','
        })
        points = points.substring(0, points.length - 1)
        window.EventBus.$emit('rainContour/visible', points)
      } else {
        window.EventBus.$emit('rainContour/clear')
      }
    }
    const changeTimeSelect = (type) => {
      if (type == '1000') {
        // 自定义就是不管了，自己想怎么设置怎么设置
      } else if (type == 'yesterday') {
        state.time = [moment().subtract(1, 'days').format('YYYY-MM-DD 08:mm:ss'), moment().format('YYYY-MM-DD HH:mm:ss')]
      } else {
        handleTime(type)
      }
    }
    return {
      ...toRefs(state),
      handleClick,
      openContour,
      changeTimeSelect,
      handleQuery
    };
  },
});
</script>
<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  color: #fff;
  // padding: 15px;
  // background-color: rgba(1, 28, 70, 0.7);

  .title {
    color: #8CD2FF;
    font-size: 16px;
    font-weight: 700;
  }

  .cont {
    background-color: #022757;
    padding: 10px;
  }

  .form {
    margin-top: 10px;

    .list {
      display: flex;
      flex-wrap: wrap;
      width: 100%;

      .items {
        width: 50%;
        display: flex;
        align-items: center;
        font-size: 14px;
        margin-bottom: 5px;

        &.three {
          width: 30%;
        }

        .round {
          width: 10px;
          height: 10px;
          border-radius: 5px;
          margin: 0 4px;
        }

        .level0 {
          background-color: #fff;
        }

        .level1 {
          background-color: #BAEF9F;
        }

        .level2 {
          background-color: #55DD33;
        }

        .level3 {
          background-color: #7FBDFF;
        }

        .level4 {
          background-color: #000DFF;
        }

        .level5 {
          background-color: #E434EF;
        }

        .level6 {
          background-color: #FF2C2C;
        }
      }
    }
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }
}

:deep(.el-form) {
  .el-form-item__label {
    color: #fff;
    font-size: 14px;
  }

  .el-form-item__content {
    .el-checkbox {
      height: 24px !important;

      .el-checkbox__label {
        color: #fff;
        font-size: 14px;
      }
    }
  }
}

:deep(.productTable) {
  background-color: transparent !important;

  tr {
    background-color: transparent !important;

    th {
      background-color: #083f86 !important;
      color: #fff !important;
      border-bottom: 0 solid #00ACFF !important;
      font-size: 14px !important;
      font-weight: normal !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #00ACFF;

    }
  }

  --el-table-row-hover-bg-color: transparent;

  .el-table__inner-wrapper {
    &::before {
      background-color: transparent;
    }
  }
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell) {
  background-color: #033c68 !important;
  cursor: pointer;
}

.borderColor {
  :deep(.el-input__wrapper) {
    border-color: #409eff !important;
    background-color: transparent !important;
    --el-input-border-color: #409eff !important;

    .el-input__inner {
      color: #fff;
    }
  }
}

:deep(.el-date-editor) {
  border: 1px solid #409eff;
  background-color: transparent !important;
  box-shadow: 0 0 0 1px #409eff;
  color: #fff;
}

:deep(.el-date-editor .el-range__icon) {
  height: inherit;
  font-size: 14px;
  color: #a3deff;
  float: left;
}

:deep(.el-date-editor .el-range-input) {
  color: #a3deff;
}

:deep(.el-date-editor .el-range-separator) {
  color: rgba(255, 255, 255, 0.9);
}
</style>
