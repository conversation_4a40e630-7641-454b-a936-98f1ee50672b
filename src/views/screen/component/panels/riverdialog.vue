<template>
  <div class="box">
    <div class="tabs">
      <div
        class="items"
        :class="item.id == current ? 'act' : ''"
        @click="handleTab(item.id)"
        v-for="item in tabList"
        :key="item.id"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="monitor item" v-show="current == 0">
      <el-scrollbar>
        <!-- <div class="warn">双林 超警戒 涨 超保证(10-21 12:10)</div> -->
        <div class="form">
          <el-date-picker
            class="borderColor"
            v-model="time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            format="YYYY-MM-DD HH:mm"
            date-format="YYYY/MM/DD ddd"
            time-format="hh:mm"
            end-placeholder="结束时间"
            :clearable="false"
            size="small"
            style="width: 100px; margin-right: 10px"
          />
          <el-button type="primary" size="small" @click="query">查询</el-button>
          <el-button type="primary" plain size="small" @click="resatQuery"
            >重置</el-button
          >
          <el-button
            type="primary"
            plain
            size="small"
            @click="handleChangeData"
            >{{ dataShow ? "数据" : "图" }}</el-button
          >
          <!-- <el-button type="primary" plain size="small">导出</el-button> -->
        </div>
        <div
          v-show="dataShow"
          class="monitorCharts"
          id="monitorCharts"
          style="width: 508px"
        ></div>
        <div v-show="!dataShow" class="table">
          <el-table
            class="productTable"
            :data="list1"
            :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
            style="height: 320px"
            :cell-style="{
              color: '#ffffff',
              textAlign: 'center',
              fontSize: 13 + 'px',
            }"
            border
          >
            <el-table-column
              prop="time"
              label="时间"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="waterLevel"
              label="水位(m)"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="waterFlow"
              label="流量(m³/s)"
              align="center"
            ></el-table-column>
          </el-table>
        </div>
      </el-scrollbar>
    </div>
    <div class="picture item" v-show="current == 1">
      <el-select v-model="site" size="small" @change="getVedioUrl">
        <!-- <el-option label="1号摄像头站点" value="1"></el-option>
        <el-option label="2号摄像头站点" value="2"></el-option>
        <el-option label="3号摄像头站点" value="3"></el-option> -->
        <el-option
          :label="item.name"
          :value="item.camId"
          v-for="(item, index) in vedioList"
          :key="index"
        ></el-option>
      </el-select>
      <div class="video-container" id="full-box">
        <div class="video-box">
          <p v-if="!vedioData.videoUrl" class="video-msg">
            {{ vedioData.msg }}
          </p>
          <video-player
            v-else
            :videoInfo="vedioData"
            class="video-player"
          ></video-player>
        </div>
      </div>
    </div>
    <div class="desc item" v-show="current == 2">
      <el-scrollbar>
        <el-descriptions class="margin-top" :column="2" size="small" border>
          <el-descriptions-item
            v-for="(item, index) in descList"
            :key="index"
            :label="item.name"
            >{{ item.value }}</el-descriptions-item
          >
        </el-descriptions>
      </el-scrollbar>
    </div>
    <div class="item" v-show="current == 3">
      <div class="sectionCharts" id="sectionCharts" style="width: 508px"></div>
    </div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import moment from "moment";
import { ElMessage } from "element-plus";
import {
  riverWaterLevel,
  riverSectionDetail,
} from "@/api/watershed/query/index";
import VideoPlayer from "@/views/queryStatistics/video/components/VideoPlayer.vue";
import { cameraList, previewUrl } from "@/api/watershed/query/index";
import { deepClone } from "@/utils";

const props = defineProps({
  data1: {
    type: Object,
    default: () => ({}),
  },
});

const state = reactive({
  current: 0,
  dataShow: true,
  tabList: [
    {
      name: "实时监控",
      id: 0,
    },
    // {
    //   name: '视频监控',
    //   id: 1
    // },
    // {
    //   name: '防洪指标信息',
    //   id: 2
    // },
    {
      name: "河道大断面",
      id: 3,
    },
  ],
  time: [new Date(new Date().setHours(0, 0, 0, 0) - 7 * 86400000), new Date()],
  option: {
    grid: {
      left: "60px", //距左边距 留够name的宽度
      right: "60px",
      bottom: "60px",
      top: "50px",
      // containLabel: true
    },
    // tooltip: {
    //   trigger: 'axis',
    //   axisPointer: {
    //     type: 'cross',
    //     crossStyle: {
    //       color: '#999'
    //     }
    //   }
    // },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: { color: "#555" },
        label: {
          backgroundColor: "#555",
        },
      },
    },
    legend: {
      data: ["水位", "保证水位", "警戒水位", "历史最高水位", "流量"],
      selected: {
        保证水位: false,
        警戒水位: false,
        历史最高水位: false,
      },
      textStyle: {
        color: "#fff",
        fontSize: 12,
        fontFamily: "PingFang SC",
      },
    },
    xAxis: {
      type: "category",
      data: [],
      boundaryGap: false,
      axisPointer: {
        type: "shadow",
      },
      axisLabel: {
        // interval: "0",
        color: "#fff",
      },
      axisLine: {
        lineStyle: {
          color: "#fff",
        },
      },
      axisTick: {
        show: false,
      },
    },
    dataZoom: [
      {
        type: "slider", //有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
        show: true, //是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
        start: 0, //数据窗口范围的起始百分比0-100
        end: 100, //数据窗口范围的结束百分比0-100
        xAxisIndex: [0], // 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
        bottom: 0, //距离底部的距离
      },
      {
        type: "inside",
        zoomOnMouseWheel: false, // 滚轮是否触发缩放
        moveOnMouseMove: true, // 鼠标滚轮触发滚动
        moveOnMouseWheel: true,
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "水位(m)",
        boundaryGap: ["0", "12.5%"],
        scale: true,
        nameTextStyle: {
          color: "#fff",
        },
        axisLabel: {
          color: "#fff",
        },
        // min: function (value) {
        //   return value.min - 2;
        // },
        // max: function (value) {
        //   return value.max + 2;
        // },
        splitLine: {
          lineStyle: {
            color: "#005b99",
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#fff",
          },
        },
      },
      {
        type: "value",
        name: "流量(m³/s)",
        boundaryGap: ["0", "30.5%"],
        // scale: true,
        nameTextStyle: {
          color: "#fff",
        },
        axisLabel: {
          color: "#fff",
        },
        splitLine: {
          show: false,
          lineStyle: {
            color: "#fff",
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#fff",
          },
        },
      },
    ],
    series: [
      {
        name: "水位",
        type: "line",
        symbol: "none",
        color: "#059DFE",
        data: [],
      },

      {
        name: "保证水位",
        symbol: "none",
        type: "line",
        color: "#FFB912",
        data: [],
      },
      {
        name: "警戒水位",
        symbol: "none",
        type: "line",
        color: "#FE9601",
        data: [],
      },
      {
        name: "历史最高水位",
        symbol: "none",
        type: "line",
        color: "#F53838",
        data: [],
      },
      {
        name: "流量",
        type: "line",
        symbol: "none",
        yAxisIndex: 1,
        // stack: '大型',
        color: "#1AD2A4",
        data: [],
      },
      // {
      //   type: 'line',
      //   markLine: {
      //     symbol: 'none',               // 去掉警戒线最后面的箭头
      //     label: {
      //       position: 'start',  // 将警示值放在哪个位置，三个值"start",'middle','end'  开始  中点 结束
      //       formatter: '',
      //       color: '#F56C6C',
      //     },
      //     data: [{
      //       silent: true,             // 鼠标悬停事件  true没有，false有
      //       lineStyle: {               // 警戒线的样式  ，虚实  颜色
      //         // type: 'dash',
      //         type: 'solid',
      //         color: '#F56C6C',
      //         width: 1
      //       },
      //       xAxis: "10-05"
      //     }]
      //   },
      //   markArea: {
      //     label: {
      //       position: 'inside'
      //     },
      //     itemStyle: {
      //       color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      //         { offset: 0, color: 'rgba(233,110,110,0.07)' },
      //         { offset: 0.5, color: 'rgba(233,110,110,0.3)' },
      //         { offset: 1, color: 'rgba(233,110,110,0.5)' }
      //       ]),
      //       zIndex: -1
      //     },
      //     emphasis: {
      //       label: {
      //         show: true,
      //         position: 'inside'
      //       }
      //     },
      //     data: [
      //       [{
      //         xAxis: '00:00:00'
      //       },
      //       { xAxis: '01:00:00' }]
      //     ]
      //   }
      // }
    ],
  },
  site: "",
  list1: [],
  descList: [
    {
      name: "左堤高程(m)",
      value: "",
    },
    {
      name: "右堤高程(m)",
      value: "",
    },
    {
      name: "警戒水位(m)",
      value: "",
    },
    {
      name: "警戒流量(m³/s)",
      value: "",
    },
    {
      name: "保证水位(m)",
      value: "",
    },
    {
      name: "保证流量(m³/s)",
      value: "",
    },
    // {
    //   name: '平滩流量(m³/s)',
    //   value: '',
    // },
    {
      name: "实测最高水位(m)",
      value: "",
    },
    {
      name: "实测最高水位出现时间",
      value: "",
    },
    {
      name: "调查最高水位(m)",
      value: "",
    },
    {
      name: "调查最高水位出现时间",
      value: "",
    },
    {
      name: "实测最大流量(m³/s)",
      value: "",
    },
    {
      name: "实测最大流量出现时间",
      value: "",
    },
    {
      name: "调查最大流量(m³/s)",
      value: "",
    },
    {
      name: "调查最大流量出现时间",
      value: "",
    },
    {
      name: "历史最大含沙量(kg/m³)",
      value: "",
    },
    {
      name: "历史最大含沙量出现时间",
      value: "",
    },
    {
      name: "历史最大断面平均流速(m/s)",
      value: "",
    },
    {
      name: "历史最大断面平均流速出现时间",
      value: "",
    },
    {
      name: "历史最低水位(m)",
      value: "",
    },
    {
      name: "历史最低水位出现时间",
      value: "",
    },
    {
      name: "历史最小流量(m³/s)",
      value: "",
    },
    {
      name: "历史最小流量出现时间",
      value: "",
    },
    {
      name: "高水位告警值(m)",
      value: "",
    },
    {
      name: "大流量告警值(m³/s)",
      value: "",
    },
    {
      name: "低水位告警值(m)",
      value: "",
    },
    {
      name: "小流量告警值(m³/s)",
      value: "",
    },
    {
      name: "启动预报水位标准(m)",
      value: "",
    },
    {
      name: "平滩流量(m³/s)",
      value: "",
    },
  ],
  option3: {
    grid: {
      left: "50px", //距左边距 留够name的宽度
      right: "70px",
      bottom: "60px",
      top: "50px",
      // containLabel: true
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: { color: "#555" },
        label: {
          backgroundColor: "#555",
        },
      },
    },
    xAxis: {
      type: "category",
      name: "起点距(m)",
      data: [],
      boundaryGap: false,
      axisPointer: {
        type: "shadow",
      },
      axisLabel: {
        // interval: "0",
        color: "#fff",
      },
      axisLine: {
        lineStyle: {
          color: "#fff",
        },
      },
      axisTick: {
        show: false,
      },
    },
    dataZoom: [
      {
        type: "slider", //有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
        show: true, //是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
        start: 0, //数据窗口范围的起始百分比0-100
        end: 100, //数据窗口范围的结束百分比0-100
        xAxisIndex: [0], // 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
        bottom: 0, //距离底部的距离
      },
      {
        type: "inside",
        zoomOnMouseWheel: false, // 滚轮是否触发缩放
        moveOnMouseMove: true, // 鼠标滚轮触发滚动
        moveOnMouseWheel: true,
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "河底高程(m)",
        min: 0,
        scale: true,
        nameTextStyle: {
          color: "#fff",
        },
        axisLabel: {
          color: "#fff",
        },
        splitLine: {
          lineStyle: {
            // color: "#005b99"
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#fff",
          },
        },
      },
    ],
    series: [
      {
        name: "河底高程",
        type: "line",
        symbol: "none",
        color: "#E1C08B",
        areaStyle: {
          color: "#E1C08B",
          shadowBlur: 10,
          opacity: 1,
        },
        data: [],
      },
    ],
  },
  data: JSON.parse(JSON.stringify(props.data1 || {})),
  sttp: "",
  vedioList: [],
  vedioData: {
    id: "",
    msg: "无信号",
    videoUrl: "",
  },
});

const {
  current,
  dataShow,
  tabList,
  time,
  option,
  site,
  list1,
  descList,
  option3,
  data,
  sttp,
  vedioList,
  vedioData,
} = toRefs(state);
const handleTab = async (id) => {
  current.value = id;
  if (id == 3) {
    nextTick(() => {
      getRvsectInfo();
    });
  } else if (id == 1) {
    // const res = await cameraList({ stationCode: '611K2607' })
    const res = await cameraList({ stationCode: data.value.STCD });
    vedioList.value = res.data;
    if (res.data.length > 0) {
      site.value = res.data[0].camId;
      vedioData.value.videoUrl = true;
      getVedioUrl(site.value);
    }
  } else if (id === 0) {
    query();
  }
};
//获取河道实时水情数据
const query = async () => {
  const res = await riverWaterLevel({
    startTime: moment(time.value[0]).format("YYYY-MM-DD HH:mm:ss"),
    endTime: moment(time.value[1]).format("YYYY-MM-DD HH:mm:ss"),
    stcd: data.value.STCD,
  });

  sttp.value = data.value.STTP;
  if (res.data.length == 0) {
    ElMessage({ message: "当前时间暂无数据", type: "warning" });
    // return;
  }
  option.value.legend.data = ["水位", "流量"];
  option.value.yAxis = [
    {
      type: "value",
      name: "水位(m)",
      scale: true,
      nameTextStyle: {
        color: "#fff",
      },
      axisLabel: {
        color: "#fff",
      },
      splitLine: {
        lineStyle: {
          color: "#005b99",
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#fff",
        },
      },
    },
    {
      type: "value",
      name: "流量(m³/s)",
      nameTextStyle: {
        color: "#fff",
      },
      axisLabel: {
        color: "#fff",
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: "#fff",
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#fff",
        },
      },
    },
  ];
  option.value.series = [
    {
      name: "水位",
      type: "line",
      symbol: "none",
      color: "#059DFE",

      data: [],
    },

    {
      name: "流量",
      type: "line",
      symbol: "none",
      yAxisIndex: 1,
      color: "#1AD2A4",
      data: [],
    },
  ];
  option.value.xAxis.data = [];
  option.value.series[0].data = [];
  option.value.series[1].data = [];

  // 对数据按时间倒序排序
  const cloneData = deepClone(res.data);
  const sortedData = cloneData.sort(
    (a, b) => new Date(b.time) - new Date(a.time)
  );
  list1.value = sortedData.map((el) => {
    el.time = moment(el.time).format("MM-DD HH:mm");
    return el;
  });

  res.data.forEach((el) => {
    option.value.xAxis.data.push(moment(el.time).format("MM-DD HH:mm"));
    option.value.series[0].data.push(el.waterLevel); //Z
    option.value.series[1].data.push(el.waterFlow);
    el.time = moment(el.time).format("MM-DD HH:mm");
  });
  echarts.init(document.getElementById("monitorCharts")).dispose();
  let myEchart = echarts.init(document.getElementById("monitorCharts"));
  myEchart.setOption(option.value);
};

const getRvsectInfo = () => {
  let stcd = data.value.STCD;
  riverSectionDetail(stcd).then((res) => {
    let editTable = [];
    if (res.data.length > 0) {
      editTable = res.data.map((item) => {
        return {
          di: item.di,
          zb: item.zb,
        };
      });
    }
    option3.value.xAxis.data = [];
    option3.value.series[0].data = [];
    let max = editTable ? Math.max(...editTable.map((item) => item.zb)) : 0;
    option3.value.yAxis[0].max = max * 1.2;
    option3.value.xAxis.data = editTable.map((item) => item.di);
    option3.value.series[0].data = editTable.map((item) => item.zb);

    echarts.init(document.getElementById("sectionCharts")).dispose();
    let myEchart = echarts.init(document.getElementById("sectionCharts"));
    myEchart.setOption(option3.value);
  });
};
onMounted(() => {
  // getrvfcchInfo()
  query();
});

// 完善：监听props.data1变化，只有新值有效时才赋值和执行query
watch(
  () => props.data1,
  (newVal) => {
    if (
      !newVal ||
      (typeof newVal === "object" && Object.keys(newVal).length === 0)
    ) {
      // 清空数据
      data.value = {};
      list1.value = [];
      option.value.xAxis.data = [];
      option.value.series.forEach((s) => (s.data = []));
      option3.value.xAxis.data = [];
      option3.value.series.forEach((s) => (s.data = []));
      // 清空主图表
      const chartDom = document.getElementById("monitorCharts");
      if (chartDom) {
        const chart = echarts.getInstanceByDom(chartDom);
        if (chart) chart.clear();
      }
      // 清空断面图表
      const sectionDom = document.getElementById("sectionCharts");
      if (sectionDom) {
        const sectionChart = echarts.getInstanceByDom(sectionDom);
        if (sectionChart) sectionChart.clear();
      }
      return;
    }
    data.value = JSON.parse(JSON.stringify(newVal));
    query();
  }
);
const getVedioUrl = (id) => {
  previewUrl({ channelId: id }).then((res) => {
    if (res.code == 200) {
      vedioData.value = { videoUrl: res.msg, id: id, msg: "" };
      // vedioData.value = { videoUrl: "http://jvmp94.hnsvideo.com:20378/live/43010002031329005763.flv?stream=8B5A22C9539F401A889B9D8F00281C02", id: id, msg: "" }
      console.log(vedioData.value);
    } else {
      vedioData.value = {
        id: "1",
        msg: "无信号",
        videoUrl: "",
      };
    }
  });
};
const handleChangeData = () => {
  // if (data.valueShow) {
  data.valueShow = !data.valueShow;
  // }
};
const resatQuery = () => {
  time.value = [
    new Date(new Date().setHours(0, 0, 0, 0) - 7 * 86400000),
    new Date(),
  ];
  query();
};
</script>
<style scoped lang="scss">
.box {
  width: 100%;
  //background-color: #00356c;
  padding: 15px;

  .tabs {
    display: flex;
    height: 40px;
    align-items: center;
    margin-bottom: 10px;

    .items {
      margin-right: 15px;
      color: #b1d1f2;
      cursor: pointer;
      position: relative;
      font-size: 14px;

      &.act {
        color: #fff;
        font-size: 16px;

        &:after {
          content: "";
          width: 50%;
          height: 2px;
          border-radius: 2px;
          background-color: #fff;
          position: absolute;
          bottom: -7px;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }

  .item {
    width: 100%;
    height: 400px;
  }

  .monitor {
    .warn {
      width: 100%;
      height: 28px;
      font-size: 14px;
      line-height: 28px;
      color: #fff;
      padding-left: 10px;
      background-color: #ff9601;
    }

    .form {
      display: flex;
      margin: 10px 0;
    }

    .monitorCharts {
      width: 100%;
      height: 300px;
      // background-color: #B1D1F2;
    }
  }

  .picture {
    .video-container {
      margin-top: 10px;
      width: 100%;
      height: calc(100% - 40px);
      display: flex;
      flex-wrap: wrap;

      .video-box {
        width: 100%;
        height: 100%;
        background: #000;
        border: 1px solid #fff;
        position: relative;

        .video-player {
          width: 100%;
          height: 100%;
        }

        .iconClose {
          position: absolute;
          cursor: pointer;
          top: 10px;
          right: 10px;
          z-index: 999;
          padding: 5px;
          color: #fff;
          visibility: hidden;
          border-radius: 50%;
          background: rgba(255, 204, 183, 0.4);
        }

        .video-msg {
          color: #3e8ef7;
          position: absolute;
          top: 50%;
          left: calc(50% - 20px);
        }

        &:hover > .show-close {
          visibility: visible;
        }

        .show-flow {
          position: absolute;
          bottom: 30px;
          left: 20px;
        }

        .show-flow-item {
          display: block;
          color: #ffffff;
        }
      }
    }
  }

  .desc {
    :deep(.el-descriptions) {
      --el-border-color-lighter: #0067ba !important;
    }

    :deep(.el-descriptions__body) {
      background-color: transparent;
      // color: #fff;

      .el-descriptions__cell {
        color: #fff;

        background-color: transparent;
      }

      .el-descriptions__label {
        color: #a5d1ff;
        width: 35% !important;
      }

      .el-descriptions__content {
        width: 14% !important;
      }
    }
  }

  .sectionCharts {
    width: 100%;
    height: 100%;
  }
}

:deep(.productTable) {
  background-color: transparent !important;
  margin: 10px 0 20px;

  tr {
    background-color: transparent !important;

    th {
      background-color: transparent !important;
      color: #fff !important;
      border-bottom: 1px solid #17365a;
      border-color: #005e9d !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #005e9d;
      border-color: #005e9d !important;
    }
  }

  --el-table-row-hover-bg-color: transparent;
  --el-table-border-color: #005e9d;
  --el-bg-color: transparent;

  .el-table__inner-wrapper {
    &::before {
      background-color: #005e9d;
    }
  }
}

.borderColor {
  :deep(.el-input__wrapper) {
    border-color: #409eff !important;
    background-color: transparent !important;
    --el-input-border-color: #409eff !important;

    .el-input__inner {
      color: #fff;
    }
  }
}

:deep(.el-date-editor) {
  border: 1px solid #409eff;
  background-color: transparent !important;
  box-shadow: 0 0 0 1px #409eff;
  color: #fff;
}

:deep(.el-date-editor .el-range__icon) {
  height: inherit;
  font-size: 14px;
  color: #a3deff;
  float: left;
}

:deep(.el-date-editor .el-range-input) {
  color: #a3deff;
}

:deep(.el-date-editor .el-range-separator) {
  color: rgba(255, 255, 255, 0.9);
}
</style>
