<template>
  <div class="dialog-container">
    <div class="box">
      <div class="tabs">
        <div class="super">
          <div class="items" :class="item.id == current ? 'act' : ''" @click="handleClick(item.id)"
            v-for="item in tabList" :key="item.id">{{ item.name
            }}
          </div>

        </div>
      </div>
      <div class="monitor item" v-show="current == 8">
        <el-button type="primary" plain size="small" @click="handleChangeData">{{ dataShow ? '数据' : '图'
          }}</el-button>
        <div v-show="dataShow" class="monitorCharts" id="monitorCharts2" style="width: 508px;"></div>

        <div v-show="!dataShow" class="table">
          <el-table class="productTable" :data="list1" :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
            :cell-style="{ color: '#ffffff', textAlign: 'center', fontSize: 13 + 'px' }" border>
            <el-table-column prop="time" label="时间" v-if="current != 8"></el-table-column>

            <el-table-column prop="rz" label="水位(mm)"></el-table-column>
            <el-table-column prop="w" label="库容(百万m³)"></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="monitor item" v-show="current == 0">
        <el-scrollbar>
          <!-- <div class="warn">双林 超警戒 涨 超保证(10-21 12:10)</div> -->
          <div class="form">
            <el-date-picker class="borderColor" v-model="time" type="datetimerange" range-separator="至"
              format="YYYY-MM-DD HH:mm" date-format="YYYY/MM/DD ddd" time-format="hh:mm" start-placeholder="开始时间"
              end-placeholder="结束时间" size="small" style="width: 100px;margin-right: 10px;" />
            <el-button type="primary" size="small" @click="query">查询</el-button>
            <el-button type="primary" plain size="small" @click="resatQuery">重置</el-button>
            <el-button type="primary" plain size="small" @click="handleChangeData">{{ dataShow ? '数据' : '图'
              }}</el-button>
            <!-- <el-button type="primary" plain size="small">导出</el-button> -->
          </div>
          <div v-show="dataShow" class="monitorCharts" id="monitorCharts" style="width: 508px;"></div>
          <div v-show="!dataShow" class="table">
            <el-table class="productTable" :data="list1" :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
              :cell-style="{ color: '#ffffff', textAlign: 'center', fontSize: 13 + 'px' }" border>
              <el-table-column prop="time" label="时间"></el-table-column>

              <el-table-column prop="waterLevel" label="实测水位(mm)"></el-table-column>
              <el-table-column prop="inboundFlow" label="入库流量(m³/s)"></el-table-column>
              <el-table-column prop="outboundFlow" label="出库流量(m³/s)"></el-table-column>
            </el-table>
          </div>
        </el-scrollbar>
      </div>
      <div class="picture item" v-show="current == 1">
        <el-select v-model="site" size="small" @change="getVedioUrl">
          <el-option :label="item.name" :value="item.camId" v-for="(item, index) in vedioList" :key="index"></el-option>
        </el-select>
        <!-- <div class="vedio">
          <video-player :videoInfo="vedioData" class="video-player"></video-player>
        </div> -->
        <div class="video-container" id="full-box">
          <div class="video-box">
            <p v-if="!vedioData.videoUrl" class="video-msg">{{ vedioData.msg }}</p>
            <video-player v-else :videoInfo="vedioData" class="video-player"></video-player>
          </div>
        </div>
      </div>
      <div class="item" v-show="current == 2">
        <!-- <el-scrollbar> -->
        <div class="descBox">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="水库名称：">
              <div>{{ dataForm.STNM }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="注册登记码：">
              <span v-if="dataForm.proType == 1">码头工程（含渡口）</span>
              <span v-if="dataForm.proType == 2">桥梁工程</span>
              <span v-if="dataForm.proType == 3">道路工程（含铁路）</span>
            </el-descriptions-item>
            <el-descriptions-item label="工程规模：">
              <span v-if="dataForm.PROJ_SCAL == '1'">大（1）型</span>
              <span v-if="dataForm.PROJ_SCAL == '2'">大（2）型 </span>
              <span v-if="dataForm.PROJ_SCAL == '3'">中型</span>
              <span v-if="dataForm.PROJ_SCAL == '4'">小（1）型</span>
              <span v-if="dataForm.PROJ_SCAL == '5'">小（2）型</span>
            </el-descriptions-item>
            <el-descriptions-item label="行政区：">
              <span>{{ dataForm.ADDVNM }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="所在流域：">
              <div>{{ dataForm.BSNM }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="所在水系：">
              <div>{{ dataForm.HNNM }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="所在河流：">
              <div>{{ dataForm.RVNM }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="高程基准：">
              <div>{{ dataForm.proUnit }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="水库功能：">
              <div>{{ dataForm.contacts }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="坝址以上控制流域面积(k㎡)：">
              <div>{{ dataForm.CAT_A }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="坝顶高程(m)：">
              <div>{{ dataForm.DAMEL }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="校核洪水位(m)：">
              <div>{{ dataForm.CKFLZ }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="设计洪水位(m)：">
              <div>{{ dataForm.DSFLZ }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="正常高水位(m)：">
              <div>{{ dataForm.NORMZ }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="汛限水位(m)：">
              <div>{{ dataForm.FSLTDZ }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="死水位(m)：">
              <div>{{ dataForm.DDZ }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="历史最高库水位(m)：">
              <div>{{ dataForm.HHRZ }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="历史最高库水位出现时间：">
              <div>{{ dataForm.HHRZTM }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="低水位告警值(m)：">
              <div>{{ dataForm.LAZ }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="总库容(10^6m³)：">
              <div>{{ dataForm.TTCP }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- </el-scrollbar> -->
      </div>
      <div class="item" v-show="current == 3">
        <el-scrollbar>
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item v-for="(item, index) in descList" :key="index" :label="item.name">{{ item.value
              }}</el-descriptions-item>
          </el-descriptions>
        </el-scrollbar>
      </div>
      <div class="item" v-show="current == 4">
        <el-scrollbar>
          <el-descriptions class="margin-top" :column="2" size="small" border>
            <el-descriptions-item v-for="(item, index) in descList1" :key="index" :label="item.name">{{ item.value
              }}</el-descriptions-item>
          </el-descriptions>
        </el-scrollbar>
      </div>
      <div class="person item" v-show="current == 5">
        <!-- <el-scrollbar> -->
        <div class="items" v-for="item in personList" :key="item.id">
          <div class="name">{{ item.name }} {{ item.telphone }}</div>
          <div class="responsibility">{{ item.responsibility }}</div>
        </div>
        <!-- </el-scrollbar> -->
      </div>
      <div class="plan item" v-show="current == 6">
        <div class="items" v-for="item in planList" :key="item.id" @click="openLink(item)">
          <div class="name">{{ item.name }}</div>
        </div>
      </div>
      <div class="timeLine item" v-show="current == 7">
        <el-scrollbar>
          <el-timeline>
            <el-timeline-item v-for="(item, index) in timeList" :key="index" :timestamp="item.timestamp"
              placement="top">
              <span style="color: #fff;">
                {{ item.content }}
              </span>
            </el-timeline-item>
          </el-timeline>
        </el-scrollbar>
      </div>
    </div>

  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, watch } from "vue";
import * as echarts from "echarts";
import { wrresbInfo, rsvrfcchInfo, rsvrfsrInfo, } from '@/api/watershed/screenRight/overview'
import { previewUrl } from "@/api/watershed/query/index"
import { reservoirStationInfo, reservoirStationLine } from "@/api/watershed/query/index";
import moment from "moment";
import { ElMessage } from "element-plus";
import VideoPlayer from "../../../queryStatistics/video/components/VideoPlayer.vue";

export default defineComponent({
  props: ["data1"],
  components: {
    VideoPlayer
  },
  setup(props, context) {
    const state = reactive({
      current: 0,
      dataForm: {},
      tabList: [
        {
          name: '水库水情',
          id: 0
        },
        // {
        //   name: '视频监控',
        //   id: 1
        // },
        // {
        //   name: '基本信息',
        //   id: 2
        // },
        // // {
        // //   name: '水库特征',
        // //   id: 3
        // // },
        // // {
        // //   name: '水文特征',
        // //   id: 4
        // // },
        // {
        //   name: '责任人',
        //   id: 5
        // },
        // // {
        // //   name: '调度规程及应急预案',
        // //   id: 6
        // // },
        // {
        //   name: '调度实况跟踪',
        //   id: 7
        // },
        {
          name: '水库库容曲线',
          id: 8
        }

      ],
      time: [new Date().getTime() - 86400000, new Date()],
      dataShow: true,
      option: {
        grid: [
          {
            left: '60px',  //距左边距 留够name的宽度
            right: '60px',
            height: '25%',
            top: '50px',
            // containLabel: true
          },
          {
            left: '30px',
            right: '30px',
            top: '60px',

            bottom: '50px',
            // containLabel: true
          }
        ],  //两个图表
        // tooltip: {
        //   trigger: 'axis',
        //   axisPointer: {
        //     type: 'cross',
        //     crossStyle: {
        //       color: '#999'
        //     }
        //   }
        // },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        legend: {
          width: "60%",
          // align: "auto",
          data: [
            '出库流量',
            '入库流量',
            '实测水位'
          ],
          textStyle: {
            color: '#fff',
            fontSize: 12,
            fontFamily: 'PingFang SC'
          }
        },
        xAxis: [
          {
            gridIndex: 1,
            type: 'category',
            data: [],
            boundaryGap: true,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              // interval: "0",
              color: "#fff"
            },
            axisLine: {
              lineStyle: {
                color: "#fff"
              },
              onZero: true
            },

            //x坐标轴的位置
          },
        ],
        axisPointer: {
          link: [
            {
              xAxisIndex: 'all'  //两个图表联动且tooltip合一
            }
          ]
        },
        dataZoom: [
          {
            type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0,//数据窗口范围的起始百分比0-100
            end: 100,//数据窗口范围的结束百分比0-100
            xAxisIndex: [0, 1],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: 0 //距离底部的距离
          },
          {
            type: 'inside',
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        yAxis: [

          {
            gridIndex: 1,   //第几个图标的y轴 根据grid的坐标
            alignTicks: true,
            boundaryGap: ['0', '12.5%'],
            type: 'value',
            name: '流量m³/s',
            nameLocation: 'end', //坐标轴名称显示位置
            scale: true,
            nameTextStyle: {
              color: "#fff"
            },
            axisLabel: {
              color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            splitLine: {
              lineStyle: {
                color: "#005b99"
              }
            }
          },
          {
            gridIndex: 1,
            alignTicks: true,
            boundaryGap: ['0', '12.5%'],
            type: 'value',
            name: '水位(m)',
            nameLocation: 'end', //坐标轴名称显示位置
            // scale: true,
            nameTextStyle: {
              color: "#fff"
            },
            axisLabel: {
              color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: "#fff"
              }
            }
          }
        ],
        series: [

          {
            name: '出库流量',
            type: 'line',
            symbol: "none",
            color: "#FFCE2C",
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: []
          },

          {
            name: '入库流量',
            symbol: "none",
            type: 'line',
            color: "#00FF95",
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: []
          },

          {
            name: '实测水位',
            symbol: "none",
            type: 'line',
            color: "#9E54FF",
            data: [],
            xAxisIndex: 0,
            yAxisIndex: 1,

          },
        ]

      },
      option1: {
        grid: [
          {
            left: '60px',  //距左边距 留够name的宽度
            right: '60px',
            height: '25%',
            top: '50px',
            // containLabel: true
          },
          {
            left: '60px',
            right: '60px',
            top: '60%',
            height: '25%',
            bottom: '50px',
            // containLabel: true
          }
        ],  //两个图表
        // tooltip: {
        //   trigger: 'axis',
        //   axisPointer: {
        //     type: 'cross',
        //     crossStyle: {
        //       color: '#999'
        //     }
        //   }
        // },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        legend: {
          width: "100%",
          data: [
            '雨量',
            '出库流量',
            '入库流量',
            '实测水位'
          ],
          textStyle: {
            color: '#fff',
            fontSize: 12,
            fontFamily: 'PingFang SC'
          }
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            boundaryGap: true,
            axisPointer: {
              type: 'shadow'
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
              onZero: true
            },
            axisLabel: {
              color: "#fff",
              show: false
            },
            axisTick: {
              show: false
            },
            position: 'top'   //x坐标轴的位置
          },
          {
            type: 'category',
            data: [],
            boundaryGap: true,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              // interval: "0",
              color: "#fff"
            },
            axisLine: {
              lineStyle: {
                color: "#fff"
              },
              onZero: true
            },
            gridIndex: 1,
            position: 'bottom'   //x坐标轴的位置
          },
        ],
        axisPointer: {
          link: [
            {
              xAxisIndex: 'all'  //两个图表联动且tooltip合一
            }
          ]
        },
        dataZoom: [
          {
            type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0,//数据窗口范围的起始百分比0-100
            end: 100,//数据窗口范围的结束百分比0-100
            xAxisIndex: [0, 1],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: 0 //距离底部的距离
          },
          {
            type: 'inside',
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '雨量(mm)',
            inverse: true,  //进行翻转
            scale: true,
            alignTicks: true,
            nameLocation: 'end', //坐标轴名称显示位置
            nameTextStyle: {
              color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            min: 0,
            axisLabel: {
              color: "#fff"
            },
            splitLine: {
              lineStyle: {
                color: "#005b99"
              }
            }
          },
          {
            gridIndex: 1,   //第几个图标的y轴 根据grid的坐标
            alignTicks: true,
            type: 'value',
            name: '流量m³/s',
            nameLocation: 'end', //坐标轴名称显示位置
            scale: true,
            nameTextStyle: {
              color: "#fff"
            },
            axisLabel: {
              color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            splitLine: {
              lineStyle: {
                color: "#005b99"
              }
            }
          },
          {
            gridIndex: 1,
            alignTicks: true,
            type: 'value',
            name: '水位(m)',
            nameLocation: 'end', //坐标轴名称显示位置
            // scale: true,
            nameTextStyle: {
              color: "#fff"
            },
            axisLabel: {
              color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: "#fff"
              }
            }
          }
        ],
        series: [
          {
            name: '雨量',
            type: 'bar',
            // smooth: true,
            symbol: "none",
            color: "#00B7FF",
            data: []
          },
          {
            name: '出库流量',
            type: 'line',
            symbol: "none",
            color: "#FFCE2C",
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: []
          },
          {
            name: '入库流量',
            symbol: "none",
            type: 'line',
            color: "#29F8F8",
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: []
          },
          {
            name: '实测水位',
            symbol: "none",
            type: 'line',
            color: "#FF8F1F",
            data: [],
            xAxisIndex: 1,
            yAxisIndex: 2,

          },

        ]

      },
      site: '',
      option2: {
        grid: [

          {
            left: '30px',
            right: '90px',
            top: '60px',

            bottom: '50px',
            // containLabel: true
          }
        ],

        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        xAxis: {
          type: 'category',
          name: '库容(百万m³)',
          axisLine: {
            show: true,
            lineStyle: {
              color: "#fff"
            },
          },
          position: 'bottom',
          data: []
        },
        yAxis: [
          {
            name: '水位(m)',
            type: 'value',
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            splitLine: {
              show: true,
            }
          },

        ],
        series: [
          {
            data: [],

            name: '水位',
            type: 'line'
          },

        ]
      },
      personList: [
        {
          id: 0,
          name: '陈瑞',
          telphone: '18762815525',
          responsibility: '政府责任人(兼行政责任人)'
        },
        {
          id: 1,
          name: '陈瑞',
          telphone: '18762815525',
          responsibility: '政府责任人(兼行政责任人)'
        },
        {
          id: 2,
          name: '陈瑞',
          telphone: '18762815525',
          responsibility: '政府责任人(兼行政责任人)'
        },
        {
          id: 3,
          name: '陈瑞',
          telphone: '18762815525',
          responsibility: '政府责任人(兼行政责任人)'
        },
      ],
      timeList: [
        {
          timestamp: '10-20',
          content: '预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s'
        },
        {
          timestamp: '10-21',
          content: '预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s'
        },
        {
          timestamp: '10-22',
          content: '预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s'
        },
        {
          timestamp: '10-23',
          content: '预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s'
        },
        {
          timestamp: '10-23',
          content: '预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s'
        }, {
          timestamp: '10-23',
          content: '预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s'
        }, {
          timestamp: '10-23',
          content: '预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s'
        },
      ],
      planList: [
        {
          id: 0,
          name: '调度规程',
          link: 'http://39.99.246.88:10007/apidoc/ddgc.doc'
        },
        {
          id: 1,
          name: '应急预案',
          link: 'http://39.99.246.88:10007/apidoc/yjya.doc'
        },
      ],
      list1: [],
      descList: [
        {
          name: '水库类型',
          value: '',
        },
        {
          name: '坝顶高程',
          value: '',
        },
        {
          name: '校核洪水位',
          value: '',
        },
        {
          name: '设计洪水位',
          value: '',
        },
        {
          name: '正常高水位',
          value: '',
        },
        {
          name: '死水位',
          value: '',
        },
        {
          name: '兴利水位',
          value: '',
        },
        {
          name: '总库容',
          value: '',
        },
        {
          name: '防洪库容',
          value: '',
        },
        {
          name: '兴利库容',
          value: '',
        },
        {
          name: '死库容',
          value: '',
        },
        {
          name: '历史最高库水位',
          value: '',
        },
        {
          name: '历史最大蓄水量',
          value: '',
        },
        {
          name: '历史最高库水位（蓄水量）出现时间',
          value: '',
        },
        {
          name: '历史最大入流',
          value: '',
        },
        {
          name: '历史最大入流时段长',
          value: '',
        },
        {
          name: '历史最大入流出现时间',
          value: '',
        },
        {
          name: '历史最大出流',
          value: '',
        },
        {
          name: '历史最大出流出现时间',
          value: '',
        },
        {
          name: '历史最低库水位',
          value: '',
        },
        {
          name: '历史最低库水位出现时间',
          value: '',
        },
        {
          name: '历史最小日均入流',
          value: '',
        },
        {
          name: '历史最小日均入流出现时间',
          value: '',
        },
        {
          name: '低水位告警值',
          value: '',
        },
        {
          name: '启动预报流量标准',
          value: '',
        },
      ],
      descList1: [
        {
          name: '开始月日',
          value: '',
        },
        {
          name: '结束月日',
          value: '',
        },
        {
          name: '汛限水位',
          value: '',
        },
        {
          name: '汛限库容',
          value: '',
        },
        {
          name: '汛期类别',
          value: '',
        },
      ],
      data1: JSON.parse(JSON.stringify(props.data1)),
      vedioList: [],
      vedioData: {
        id: '',
        msg: "无信号",
        videoUrl: ""
      }
    });
    const query = () => {
      getrsvrShort()
    }
    const handleClick = async (id) => {
      state.current = id
      state.dataShow = true
      if (id === 0) {
        getrsvrShort()

      } else if (id == 8) {
        getChart2()
      }
    }
    const getChart2 = async () => {
      reservoirStationLine({
        startTime: moment(state.time[0]).format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(state.time[1]).format('YYYY-MM-DD HH:mm:ss'),
        stcd: state.data1.STCD
      }).then((res) => {
        state.option2.xAxis.data = []
        state.option2.series[0].data = []
        // state.option.series[1].data = []
        if (res.data.length > 0) {
          res.data.forEach(el => {
            state.option2.xAxis.data.push(el.w)
            state.option2.series[0].data.push(el.rz)
            // state.option.series[1].data.push(el.w)
          })
          echarts
            .init(document.getElementById('monitorCharts2'))
            .dispose()
          let myEchart = echarts.init(
            document.getElementById('monitorCharts2')
          )
          myEchart.setOption(state.option2)
        } else {
          ElMessage({ message: '当前时间暂无数据', type: 'warning' })
          echarts
            .init(document.getElementById('monitorCharts2'))
            .dispose()
        }
        state.list1 = res.data
      })
    }
    const getVedioUrl = (id) => {
      previewUrl({ channelId: id }).then(res => {
        if (res.code == 200) {
          state.vedioData = { videoUrl: res.msg, id: id, msg: "" }
          // state.vedioData = { videoUrl: "http://jvmp94.hnsvideo.com:20378/live/43010002031329005763.flv?stream=8B5A22C9539F401A889B9D8F00281C02", id: id, msg: "" }
        } else {
          state.vedioData = {
            id: '1',
            msg: "无信号",
            videoUrl: ""
          };
        }
      })
    }
    const getrsvrShort = async () => {
      const res = await reservoirStationInfo({
        startTime: moment(state.time[0]).format("YYYY-MM-DD HH:mm:ss"),
        endTime: moment(state.time[1]).format("YYYY-MM-DD HH:mm:ss"),
        stcd: state.data1.STCD
      })
      if (res.data.length == 0) {
        ElMessage({ message: '当前时间暂无数据', type: 'warning' })
      }

      if (state.current === 0) {
        state.option.xAxis[0].data = []
        state.option.series[0].data = []
        state.option.series[1].data = []
        state.option.series[2].data = []
        res.data.forEach(el => {
          el.time = moment(el.time).format("MM-DD HH:mm")
          state.option.xAxis[0].data.push(moment(el.time).format("MM-DD HH:mm"))
          state.option.series[0].data.push(el.outboundFlow)
          state.option.series[1].data.push(el.inboundFlow)
          state.option.series[2].data.push(el.waterLevel)
        })

        echarts
          .init(document.getElementById('monitorCharts'))
          .dispose()
        let myEchart = echarts.init(
          document.getElementById('monitorCharts')
        )
        myEchart.setOption(state.option)

      } else {
        state.option1.xAxis[0].data = []
        state.option1.xAxis[1].data = []
        state.option1.series[0].data = []
        state.option1.series[1].data = []
        state.option1.series[2].data = []
        state.option1.series[3].data = []
        if (res.data.SCDATE && res.data.SCDATE.length > 0) {
          res.data.SCDATE.forEach(el => {
            state.option1.xAxis[0].data.push(moment(el.TM).format("MM-DD HH:mm"))
            state.option1.xAxis[1].data.push(moment(el.TM).format("MM-DD HH:mm"))
            state.option1.series[1].data.push(el.OTQ)
            state.option1.series[0].data.push(el.DRP)
            state.option1.series[2].data.push(el.INQ)
            state.option1.series[3].data.push(el.RZ)
            el.TM = moment(el.TM).format("MM-DD HH:mm")
          })
          echarts
            .init(document.getElementById('monitorCharts'))
            .dispose()
          let myEchart = echarts.init(
            document.getElementById('monitorCharts')
          )
          myEchart.setOption(state.option1)
        }
      }
      state.list1 = res.data
    }
    
    const openLink = (item) => {
      window.open(item.link)
    }
    onMounted(() => {
      getrsvrShort()
    })

    // 新增：监听props.data1变化
    watch(
      () => props.data1,
      (newVal) => {
        // 判断有效性：有STCD字段即为有效
        if (newVal && typeof newVal === 'object' && Object.keys(newVal).length > 0 && newVal.STCD) {
          state.data1 = JSON.parse(JSON.stringify(newVal));
          query();
        } else {
          // 清空所有相关数据
          state.data1 = {};
          // option
          if (state.option && state.option.xAxis && state.option.xAxis[0]) {
            state.option.xAxis[0].data = [];
          }
          if (state.option && state.option.series) {
            state.option.series.forEach(s => s.data = []);
          }
          // option1
          if (state.option1 && state.option1.xAxis) {
            state.option1.xAxis.forEach(x => x.data = []);
          }
          if (state.option1 && state.option1.series) {
            state.option1.series.forEach(s => s.data = []);
          }
          // option2
          if (state.option2 && state.option2.xAxis) {
            state.option2.xAxis.data = [];
          }
          if (state.option2 && state.option2.series) {
            state.option2.series.forEach(s => s.data = []);
          }
          // 清空表格数据
          state.list1 = [];
        }
      },
      { immediate: true, deep: true }
    );
    const handleChangeData = () => {
      state.dataShow = !state.dataShow
    }
    const resatQuery = () => {
      state.time = [new Date().getTime() - 86400000, new Date()]
      getrsvrShort()
    }
    return {
      ...toRefs(state),
      query,
      handleChangeData,
      openLink,
      resatQuery,
      handleClick,
      getVedioUrl
    };
  },
});
</script>
<style scoped lang="scss">
.box {
  width: 100%;



  .tabs::-webkit-scrollbar {
    height: 8px;
    background-color: #03417d;
  }

  .tabs::-webkit-scrollbar-thumb {
    background-color: #2d5a86;
    border-radius: 5px;
  }

  .tabs {
    height: 50px;
    width: 100%;
    overflow-x: scroll;
    margin-bottom: 10px;

    .super {
      display: flex;
      justify-content: flex-start;
      height: 40px;
      align-items: center;
      // width: 670px;
    }

    .items {
      margin-right: 10px;
      color: #B1D1F2;
      cursor: pointer;
      position: relative;
      font-size: 14px;

      &.act {
        color: #fff;
        font-size: 16px;

        &:after {
          content: "";
          width: 50%;
          height: 2px;
          border-radius: 2px;
          background-color: #fff;
          position: absolute;
          bottom: -7px;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }

  .item {
    width: 100%;
    height: 400px;

    :deep(.el-descriptions__body) {
      background-color: transparent;
      // color: #fff;

      .el-descriptions__cell {
        color: #fff;

        background-color: transparent;
      }

      .el-descriptions__label {
        width: 35% !important;
      }

      .el-descriptions__content {
        width: 14% !important;
      }
    }

    .margin-top {
      :deep(.el-descriptions__body) {
        background-color: transparent;
        // color: #fff;

        .el-descriptions__cell {
          color: #fff;

          background-color: transparent;
        }

        .el-descriptions__label {
          width: 20% !important;
        }

        .el-descriptions__content {
          width: 30% !important;
        }
      }
    }

  }

  .monitor {

    .warn {
      width: 100%;
      height: 28px;
      font-size: 14px;
      line-height: 28px;
      color: #fff;
      padding-left: 10px;
      background-color: #FF9601;
    }

    .form {
      display: flex;
      margin: 10px 0;
    }

    .monitorCharts {
      width: 100%;
      height: 330px;
      // background-color: #B1D1F2;
    }
  }

  .picture {

    .video-container {
      margin-top: 10px;
      width: 100%;
      height: calc(100% - 40px);
      display: flex;
      flex-wrap: wrap;

      .video-box {
        width: 100%;
        height: 100%;
        background: #000;
        border: 1px solid #fff;
        position: relative;

        .video-player {
          width: 100%;
          height: 100%;
        }

        .iconClose {
          position: absolute;
          cursor: pointer;
          top: 10px;
          right: 10px;
          z-index: 999;
          padding: 5px;
          color: #fff;
          visibility: hidden;
          border-radius: 50%;
          background: rgba(255, 204, 183, 0.4);
        }

        .video-msg {
          color: #3e8ef7;
          position: absolute;
          top: 50%;
          left: calc(50% - 20px);
        }

        &:hover>.show-close {
          visibility: visible;
        }

        .show-flow {
          position: absolute;
          bottom: 30px;
          left: 20px;
        }

        .show-flow-item {
          display: block;
          color: #ffffff;
        }
      }



    }
  }

  .person {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    align-content: flex-start;

    .items {
      width: 45%;
      height: 70px;
      color: #fff;
      text-align: center;
      background-image: url(../../image/personBack.png);
      background-size: 100% 100%;
      margin-top: 10px;

      .name {
        margin-bottom: 10px;
      }

      .responsibility {
        font-size: 14px;
        color: #B1D1F2;
      }
    }
  }

  .plan {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .items {
      width: 33%;
      height: 110px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .name {
        width: 70%;
        height: 70%;
        background-image: url(../../image/planBack.png);
        background-size: 100% 100%;
        text-align: center;
        color: #CCEBFF;
        font-size: 20px;
        font-family: SourceHanSansCN-Medium;
      }

    }

  }

  .descBox {

    // width: 680px;
    // height: 100%;
    :deep(.el-descriptions) {
      --el-border-color-lighter: #0067ba !important;

    }

    :deep(.el-descriptions__body) {
      background-color: transparent;
      // color: #fff;

      .el-descriptions__cell {
        color: #fff;

        background-color: transparent;
      }

      .el-descriptions__label {
        color: #A5D1FF;

        &:nth-child(1) {
          width: 25% !important;
        }

        &:nth-child(2) {
          width: 32% !important;
        }
      }

      .el-descriptions__content {

        // width: 20% !important;
        &:nth-child(1) {
          width: 22% !important;
        }

        &:nth-child(2) {
          width: 20% !important;
        }
      }
    }
  }

}

:deep(.productTable) {
  background-color: transparent !important;
  margin: 10px 0 20px;

  tr {
    background-color: transparent !important;

    th {
      background-color: transparent !important;
      color: #fff !important;
      border-bottom: 1px solid #17365a;
      border-color: #005e9d !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #005e9d;
      border-color: #005e9d !important;

    }
  }

  --el-table-row-hover-bg-color: transparent;
  --el-table-border-color:#005e9d;
  --el-bg-color:transparent;

  .el-table__inner-wrapper {
    &::before {
      background-color: #005e9d;
    }
  }
}

.borderColor {
  :deep(.el-input__wrapper) {
    border-color: #409eff !important;
    background-color: transparent !important;
    --el-input-border-color: #409eff !important;

    .el-input__inner {
      color: #fff;
    }
  }
}

:deep(.el-date-editor) {
  border: 1px solid #409eff;
  background-color: transparent !important;
  box-shadow: 0 0 0 1px #409eff;
  color: #fff;
}

:deep(.el-date-editor .el-range__icon) {
  height: inherit;
  font-size: 14px;
  color: #a3deff;
  float: left;
}

:deep(.el-date-editor .el-range-input) {
  color: #a3deff;
}

:deep(.el-date-editor .el-range-separator) {
  color: rgba(255, 255, 255, 0.9);
}
</style>
