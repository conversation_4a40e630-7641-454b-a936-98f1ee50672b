<template>
  <div class="box">
    <!-- <div class="head">
      <div class="items" v-for="item in list1" :key="item.id">
        {{ item.name }}
        <span v-if="item.value == 0 || item.value == null">无雨</span>
        <span v-else>{{ item.value }}mm</span>
      </div>
    </div> -->
    <el-radio-group v-model="rainType" @change="chooseChange">
      <el-radio-button label="时段降雨" value="时段降雨" />
      <el-radio-button label="日降雨" value="日降雨" />
    </el-radio-group>
    <div class="form">
      <el-date-picker
        v-if="rainType === '时段降雨'"
        class="borderColor"
        v-model="time"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        format="YYYY-MM-DD HH"
        end-placeholder="结束时间"
        :clearable="false"
        size="small"
        style="width: 100px; margin-right: 10px"
        :disabled-minutes="
          () => {
            // 返回从1到59的数组，表示禁用的分钟
            return rainType == '时段降雨'
              ? Array.from({ length: 59 }, (_, i) => i + 1)
              : [];
          }
        "
        :disabled-seconds="
          () => {
            // 返回从1到59的数组，表示禁用的秒
            return rainType == '时段降雨'
              ? Array.from({ length: 59 }, (_, i) => i + 1)
              : [];
          }
        "
      />
      <el-date-picker
        v-if="rainType === '日降雨'"
        class="borderColor"
        v-model="time"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        format="YYYY-MM-DD"
        end-placeholder="结束时间"
        :clearable="false"
        size="small"
        style="width: 100px; margin-right: 10px"
      />
      <el-button type="primary" size="small" @click="query">查询</el-button>
      <el-button type="primary" plain size="small" @click="resatQuery"
        >重置</el-button
      >
      <el-button type="primary" @click="handleChangeData" plain size="small">{{
        dataShow ? "数据" : "图"
      }}</el-button>
      <!-- <el-button type="primary" plain size="small">导出</el-button> -->
    </div>
    <div
      v-show="dataShow && hasData"
      class="rainfallChart"
      style="width: 530px; height: 300px"
      id="rainfallChart"
    ></div>
    <div v-show="dataShow && !hasData" class="no-data-chart">
      <el-empty description="所选时段无降雨" :image-size="100">
        <template #image>
          <div class="no-rain-icon">
            <el-icon :size="60"><Cloudy /></el-icon>
          </div>
        </template>
      </el-empty>
    </div>
    <div v-show="!dataShow" class="table">
      <el-table
        v-if="hasData"
        class="productTable"
        :data="list"
        :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
        :cell-style="{
          color: '#ffffff',
          textAlign: 'center',
          fontSize: 13 + 'px',
        }"
        height="300px"
        border
      >
        <el-table-column prop="tm" label="时间"></el-table-column>
        <el-table-column prop="rainValue" label="降雨量"></el-table-column>
        <el-table-column
          prop="totalRainValue"
          label="累计降雨量"
        ></el-table-column>
      </el-table>
      <div v-else class="no-data-table">
        <el-empty description="所选时段无降雨" :image-size="100">
          <template #image>
            <div class="no-rain-icon">
              <el-icon :size="60"><Cloudy /></el-icon>
            </div>
          </template>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, watch } from "vue";
import * as echarts from "echarts";
import { measuringStationInfo } from "@/api/watershed/query/index";
import { ElMessage } from "element-plus";
import moment from "moment";
export default defineComponent({
  props: ["data1"],
  setup(props, context) {
    const state = reactive({
      rainType: "时段降雨",
      data: JSON.parse(JSON.stringify(props.data1)),
      dataShow: true,
      hasData: true,
      time: [
        moment().subtract(1, "days").format("YYYY-MM-DD HH:00:00").valueOf(),
        moment().format("YYYY-MM-DD HH:00:00").valueOf(),
      ],
      option: {
        grid: {
          left: "60px", //距左边距 留够name的宽度
          right: "60px",
          bottom: "50px",
          top: "30px",
          // containLabel: true
        },
        // tooltip: {
        //   trigger: 'axis',
        //   axisPointer: {
        //     type: 'cross',
        //     crossStyle: {
        //       color: '#999'
        //     }
        //   }
        // },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: { color: "#555" },
            label: {
              backgroundColor: "#555",
            },
          },
        },
        xAxis: {
          type: "category",
          data: [],
          boundaryGap: true,
          axisPointer: {
            type: "shadow",
          },
          axisLabel: {
            // interval: "0",
            color: "#fff",
            overflow: "truncate",
            // width: '100'
          },
          axisLine: {
            lineStyle: {
              color: "#fff",
            },
          },
          axisTick: {
            show: false,
          },
        },

        dataZoom: [
          {
            type: "slider", //有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true, //是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0, //数据窗口范围的起始百分比0-100
            end: 100, //数据窗口范围的结束百分比0-100
            xAxisIndex: [0], // 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: 0, //距离底部的距离
          },
          {
            type: "inside",
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true,
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "降雨量(mm)",
            boundaryGap: ['0', '12.5%'],
            // scale: true,
            nameTextStyle: {
              color: "#fff",
            },
            axisLabel: {
              color: "#fff",
            },
            splitLine: {
              lineStyle: {
                color: "#005b99",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff",
              },
            },
          },
          {
            type: "value",
            name: "累计降雨量(mm)",
            boundaryGap: ['0', '12.5%'],
            scale: true,
            nameTextStyle: {
              color: "#fff",
            },
            axisLabel: {
              color: "#fff",
            },
            splitLine: {
              lineStyle: {
                color: "#005b99",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff",
              },
            },
          },
        ],
        series: [
          {
            name: "降雨量",
            type: "bar",
            // smooth: true,
            symbol: "none",
            color: "#059DFE",
            data: [],
            // barWidth: 20
          },
          {
            name: "累计降雨量",
            type: "line",
            // smooth: true,
            symbol: "none",
            yAxisIndex: 1,
            color: "#fac858",
            data: [],
          },
        ],
      },
      list: [],
    });
    // 将getChartsList提前
    const getChartsList = async () => {
      const res = await measuringStationInfo({
        startTime: moment(state.time[0]).format("YYYY-MM-DD HH:mm:ss"),
        endTime: moment(state.time[1]).format("YYYY-MM-DD HH:mm:ss"),
        stcd: state.data.STCD,
        rainType: state.rainType == "时段降雨" ? 0 : 1,
      });
      // res.data = [];
      // console.log(res)

      //数据异常情况处理
      res.data.forEach(el => {
        if(el.rainValue == null || el.totalRainValue == null) {
          el.rainValue = 0;
          el.totalRainValue = 0;
        }
      })
      
      if (res.data.length === 0) {
        state.list = [];
        echarts.init(document.getElementById("rainfallChart")).dispose();
        ElMessage({ message: "当前时间暂无数据", type: "warning" });
        state.hasData = false;
        return;
      }
      state.option.xAxis.data = [];
      state.option.series[0].data = [];
      state.option.series[1].data = [];
      res.data.forEach((el) => {
        el.realTime = el.tm
        if (state.rainType == "时段降雨") {
          state.option.xAxis.data.push(moment(el.tm).format("YYYY-MM-DD HH"));
          el.tm = moment(el.tm).format("YYYY-MM-DD HH");
        } else {
          state.option.xAxis.data.push(moment(el.tm).format("YYYY-MM-DD"));
          el.tm = moment(el.tm).format("YYYY-MM-DD");
        }
        state.option.series[0].data.push(el.rainValue);
        state.option.series[1].data.push(el.totalRainValue);
      });
      //数据做时间倒序排列
      state.list = res.data.sort((a, b) => {
        return new Date(b.realTime) - new Date(a.realTime);
      });
      
      echarts.init(document.getElementById("rainfallChart")).dispose();
      let myEchart = echarts.init(document.getElementById("rainfallChart"));
      myEchart.setOption(state.option);
      state.hasData = true;
    };
    const query = () => {
      getChartsList();
    };
    onMounted(() => {
      getChartsList();
    });

    // 新增：监听props.data1变化
    watch(
      () => props.data1,
      (newVal) => {
        if (newVal && typeof newVal === 'object' && Object.keys(newVal).length > 0 && newVal.STCD) {
          state.data = JSON.parse(JSON.stringify(newVal));
          query();
        } else {
          // 清空所有相关数据
          state.data = {};
          if (state.option && state.option.xAxis) {
            state.option.xAxis.data = [];
          }
          if (state.option && state.option.series) {
            state.option.series.forEach(s => s.data = []);
          }
          state.list = [];
          state.hasData = false;
        }
      },
      { immediate: true, deep: true }
    );
    // watch(state.rainType, (newValue, oldValue) => {
    //   if(newValue == '日降雨') {
    //     console.log(newValue)
    //     state.time = [moment().subtract(7, 'days').format('YYYY-MM-DD 00:00:00').valueOf(), moment().format('YYYY-MM-DD 23:59:59').valueOf()]
    //   } else {
    //     state.time = [moment().subtract(1, 'days').format('YYYY-MM-DD HH:00:00').valueOf(), moment().format('YYYY-MM-DD HH:00:00').valueOf()]
    //   }
    // }, {
    //   immediate: true
    // })
    const chooseChange = (e) => {
      state.rainType = e;
      if (e == "日降雨") {
        // console.log(e)
        state.time = [
          moment().subtract(7, "days").format("YYYY-MM-DD 00:00:00").valueOf(),
          moment().format("YYYY-MM-DD 23:59:59").valueOf(),
        ];
      } else {
        state.time = [
          moment().subtract(1, "days").format("YYYY-MM-DD HH:00:00").valueOf(),
          moment().format("YYYY-MM-DD HH:00:00").valueOf(),
        ];
      }
      getChartsList();
    };
    // const getDetail = async () => {
    //   const res = await rainDetail({
    //     keyword: state.data.STCD,
    //     datetm: moment().format("YYYY-MM-DD"),
    //     // datetm: '2022-10-01',
    //   })
    //   if (res.data[state.data.STCD]) {
    //     state.list1[0].value = res.data[state.data.STCD].ACCP8LT
    //     state.list1[1].value = res.data[state.data.STCD].ACCP1
    //     state.list1[2].value = res.data[state.data.STCD].ACCP3
    //     state.list1[3].value = res.data[state.data.STCD].ACCP6
    //     state.list1[4].value = res.data[state.data.STCD].ACCP12
    //     state.list1[5].value = res.data[state.data.STCD].ACCP24
    //   }

    // }
    const handleChangeData = () => {
      // if (state.dataShow) {
      state.dataShow = !state.dataShow;
      // }
    };
    const resatQuery = () => {
      state.time = [new Date().getTime() - 86400000, new Date()];
      getChartsList();
    };
    return {
      ...toRefs(state),
      query,
      handleChangeData,
      resatQuery,
      chooseChange,
    };
  },
});
</script>
<style scoped lang="scss">
.box {
  width: 100%;
  //background-color: #00356c;
  padding: 15px;
  color: #ffffff;

  .head {
    display: flex;
    flex-wrap: wrap;

    .items {
      width: 33%;
      font-size: 14px;
    }
  }

  .form {
    display: flex;
    margin: 20px 0;
  }

  .rainfallChart {
    width: 100%;
    height: 300px;
  }

  .table {
    width: 100%;
    height: 300px;
  }

  .no-data-chart {
    width: 100%;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 53, 108, 0.3);
    border-radius: 4px;
  }

  .no-data-table {
    width: 100%;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 53, 108, 0.3);
    border-radius: 4px;
  }

  .no-rain-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #a3deff;

    i {
      font-size: 60px;
      margin-bottom: 10px;
    }
  }
}

:deep(.productTable) {
  background-color: transparent !important;
  margin: 10px 0 20px;

  tr {
    background-color: transparent !important;

    th {
      background-color: transparent !important;
      color: #fff !important;
      border-bottom: 1px solid #17365a;
      border-color: #005e9d !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #005e9d;
      border-color: #005e9d !important;
    }
  }

  --el-table-row-hover-bg-color: transparent;
  --el-table-border-color: #005e9d;
  --el-bg-color: transparent;

  .el-table__inner-wrapper {
    &::before {
      background-color: #005e9d;
    }
  }
}

.borderColor {
  :deep(.el-input__wrapper) {
    border-color: #409eff !important;
    background-color: transparent !important;
    --el-input-border-color: #409eff !important;

    .el-input__inner {
      color: #fff;
    }
  }
}

:deep(.el-date-editor) {
  border: 1px solid #409eff;
  background-color: transparent !important;
  box-shadow: 0 0 0 1px #409eff;
  color: #fff;
}

:deep(.el-date-editor .el-range__icon) {
  height: inherit;
  font-size: 14px;
  color: #a3deff;
  float: left;
}

:deep(.el-date-editor .el-range-input) {
  color: #a3deff;
}

:deep(.el-date-editor .el-range-separator) {
  color: rgba(255, 255, 255, 0.9);
}
</style>
