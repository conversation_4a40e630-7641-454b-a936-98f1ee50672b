<template>
  <div class="box">
    <!-- <div class="head">
      <div class="items" v-for="item in list1" :key="item.id">
        {{ item.name }}
        <span v-if="item.value == 0 || item.value == null">无雨</span>
        <span v-else>{{ item.value }}mm</span>
      </div>
    </div> -->
    <!-- <el-radio-group v-model="rainType" @change="chooseChange">
      <el-radio-button label="时段降雨" value="时段降雨" />
      <el-radio-button label="日降雨" value="日降雨" />
    </el-radio-group> -->
    <div class="form">
      <el-date-picker
        v-model="time"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        format="YYYY-MM-DD HH:mm"
        value-format="YYYY-MM-DD HH:mm"
        :clearable="false"
        size="small"
        style="width: 260px; margin-right: 10px"
      />
      <el-button type="primary" size="small" @click="query">查询</el-button>
      <el-button type="primary" plain size="small" @click="resetQuery">重置</el-button>
      <el-button type="primary" @click="handleChangeData" plain size="small">{{
        dataShow ? "数据" : "图"
      }}</el-button>
    </div>
    <div
      v-show="dataShow && hasData"
      class="rainfallChart"
      style="width: 530px; height: 300px"
      id="rainfallChart"
    ></div>
    <div v-show="dataShow && !hasData" class="no-data-chart">
      <el-empty description="所选时段无数据" :image-size="100" />
    </div>
    <div v-show="!dataShow" class="table">
      <el-table
        v-if="hasData"
        class="productTable"
        :data="list"
        :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
        :cell-style="{
          color: '#ffffff',
          textAlign: 'center',
          fontSize: 13 + 'px',
        }"
        height="300px"
        border
      >
        <el-table-column prop="recordTime" label="日期"></el-table-column>
        <el-table-column prop="upstreamLevel" label="闸上水位(m)"></el-table-column>
        <el-table-column prop="downstreamLevel" label="闸下水位(m)"></el-table-column>
        <el-table-column prop="dischargeFlow" label="下泄流量(m³/s)"></el-table-column>
      </el-table>
      <div v-else class="no-data-table">
        <el-empty description="所选时段无数据" :image-size="100" />
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, watch } from "vue";
import * as echarts from "echarts";
import moment from 'moment';
import { getWaterGateDetail } from '@/api/watershed/ads/index';

export default defineComponent({
  props: ["data1"],
  setup(props, context) {
    // 1. 初始化时间范围为最近7天
    const getDefaultTime = () => {
      const start = moment().subtract(7, 'days').startOf('day').format('YYYY-MM-DD HH:mm');
      const end = moment().endOf('day').format('YYYY-MM-DD HH:mm');
      return [start, end];
    };
    const state = reactive({
      dataShow: true,
      hasData: true,
      time: getDefaultTime(),
      option: {
        color: ['#FFD600', '#00C853', '#9E54FF'],
        tooltip: { trigger: 'axis' },
        legend: {
          data: ['闸上水位', '闸下水位', '下泄流量'],
          textStyle: { color: '#fff' }
        },
        grid: { left: '60px', right: '60px', bottom: '50px', top: '30px' },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: { color: '#fff' },
          axisLine: { lineStyle: { color: '#fff' } }
        },
        yAxis: [
          {
            type: 'value',
            name: '水位(m)',
            position: 'left',
            axisLabel: { color: '#fff' },
            nameTextStyle: { color: '#fff' },
            splitLine: { lineStyle: { color: '#005b99' } }
          },
          {
            type: 'value',
            name: '流量(m³/s)',
            position: 'right',
            axisLabel: { color: '#fff' },
            nameTextStyle: { color: '#fff' },
            splitLine: { show: false }
          }
        ],
        dataZoom: [
          { type: 'slider', show: true, xAxisIndex: 0, bottom: 0 },
          { type: 'inside' }
        ],
        series: [
          {
            name: '闸上水位',
            type: 'line',
            data: [],
            yAxisIndex: 0,
            symbol: 'none',
            color: '#FFD600'
          },
          {
            name: '闸下水位',
            type: 'line',
            data: [],
            yAxisIndex: 0,
            symbol: 'none',
            color: '#00C853'
          },
          {
            name: '下泄流量',
            type: 'line',
            data: [],
            yAxisIndex: 1,
            symbol: 'none',
            color: '#9E54FF'
          }
        ]
      },
      list: [],
    });

    // 图表渲染方法
    function renderChart() {
      const res = props.data1 || {};
      let waterLevelDataList = res.data && res.data.waterLevelDataList ? res.data.waterLevelDataList : [];

      // 统一处理为最近7天数据，时间格式为YYYY-MM-DD HH:mm
      const startTime = state.time[0];
      const endTime = state.time[1];
      const startDate = moment(startTime, 'YYYY-MM-DD HH:mm');
      const endDate = moment(endTime, 'YYYY-MM-DD HH:mm');

      // 只用接口数据，无mock
      if (!waterLevelDataList.length) {
        state.hasData = false;
        if (document.getElementById("rainfallChart")) {
          echarts.init(document.getElementById("rainfallChart")).dispose();
        }
        state.list = [];
        return;
      }
      state.hasData = true;
      // 图表x轴为MM-DD HH:mm
      state.option.xAxis.data = waterLevelDataList.map(item => moment(item.recordTime, 'YYYY-MM-DD HH:mm').format('MM-DD HH:mm'));
      state.option.series[0].data = waterLevelDataList.map(item => item.upstreamLevel);
      state.option.series[1].data = waterLevelDataList.map(item => item.downstreamLevel);
      state.option.series[2].data = waterLevelDataList.map(item => item.dischargeFlow);
      // 表格数据倒序
      state.list = [...waterLevelDataList].sort((a, b) => new Date(b.recordTime) - new Date(a.recordTime));

      // 动态设置y轴min/max，留10%空余量
      const upArr = waterLevelDataList.map(item => Number(item.upstreamLevel));
      const downArr = waterLevelDataList.map(item => Number(item.downstreamLevel));
      const flowArr = waterLevelDataList.map(item => Number(item.dischargeFlow));
      // 水位y轴
      const allLevel = upArr.concat(downArr);
      let minLevel = Math.min(...allLevel);
      let maxLevel = Math.max(...allLevel);
      const gapLevel = (maxLevel - minLevel) * 0.1 || 1;
      minLevel = Math.floor(minLevel - gapLevel);
      maxLevel = Math.ceil(maxLevel + gapLevel);
      // 流量y轴
      let minFlow = Math.min(...flowArr);
      let maxFlow = Math.max(...flowArr);
      const gapFlow = (maxFlow - minFlow) * 0.1 || 1;
      minFlow = Math.floor(minFlow - gapFlow);
      maxFlow = Math.ceil(maxFlow + gapFlow);
      state.option.yAxis[0].min = minLevel;
      state.option.yAxis[0].max = maxLevel;
      state.option.yAxis[1].min = minFlow;
      state.option.yAxis[1].max = maxFlow;

      if (document.getElementById("rainfallChart")) {
        echarts.init(document.getElementById("rainfallChart")).dispose();
        let myEchart = echarts.init(document.getElementById("rainfallChart"));
        myEchart.setOption(state.option);
      }
    }

    // 查询按钮
    async function query() {
      // 获取参数
      const code = props.data1?.data?.gateCode || props.data1?.data?.id || '';
      const [startTime, endTime] = state.time;
      let waterLevelDataList = [];
      try {
        const res = await getWaterGateDetail({ code, startTime, endTime });
        if (res && res.data && Array.isArray(res.data.rows) && res.data.rows.length > 0) {
          waterLevelDataList = res.data.rows[0].waterLevelDataList || [];
        }
      } catch (e) {
        // ignore, fallback to mock
      }
      // 赋值到 data1 的 data 字段，触发 watch
      if (!props.data1.data) props.data1.data = {};
      props.data1.data.waterLevelDataList = waterLevelDataList;
      renderChart();
    }
    // 重置按钮
    function resetQuery() {
      state.time = getDefaultTime();
      renderChart();
    }

    // 监听 props.data1 或时间变化，自动刷新图表
    watch([() => props.data1, () => state.time], () => {
      renderChart();
    }, { immediate: true });

    onMounted(() => {
      renderChart();
    });

    const handleChangeData = () => {
      state.dataShow = !state.dataShow;
    };

    return {
      ...toRefs(state),
      handleChangeData,
      query,
      resetQuery
    };
  }
});
</script>
<style scoped lang="scss">
.box {
  width: 100%;
  //background-color: #00356c;
  padding: 15px;
  color: #ffffff;

  .head {
    display: flex;
    flex-wrap: wrap;

    .items {
      width: 33%;
      font-size: 14px;
    }
  }

  .form {
    display: flex;
    margin: 20px 0;
  }

  .rainfallChart {
    width: 100%;
    height: 300px;
  }

  .table {
    width: 100%;
    height: 300px;
  }

  .no-data-chart {
    width: 100%;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 53, 108, 0.3);
    border-radius: 4px;
  }

  .no-data-table {
    width: 100%;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 53, 108, 0.3);
    border-radius: 4px;
  }

  .no-rain-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #a3deff;

    i {
      font-size: 60px;
      margin-bottom: 10px;
    }
  }
}

:deep(.productTable) {
  background-color: transparent !important;
  margin: 10px 0 20px;

  tr {
    background-color: transparent !important;

    th {
      background-color: transparent !important;
      color: #fff !important;
      border-bottom: 1px solid #17365a;
      border-color: #005e9d !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #005e9d;
      border-color: #005e9d !important;
    }
  }

  --el-table-row-hover-bg-color: transparent;
  --el-table-border-color: #005e9d;
  --el-bg-color: transparent;

  .el-table__inner-wrapper {
    &::before {
      background-color: #005e9d;
    }
  }
}

.borderColor {
  :deep(.el-input__wrapper) {
    border-color: #409eff !important;
    background-color: transparent !important;
    --el-input-border-color: #409eff !important;

    .el-input__inner {
      color: #fff;
    }
  }
}

:deep(.el-date-editor) {
  border: 1px solid #409eff;
  background-color: transparent !important;
  box-shadow: 0 0 0 1px #409eff;
  color: #fff;
}

:deep(.el-date-editor .el-range__icon) {
  height: inherit;
  font-size: 14px;
  color: #a3deff;
  float: left;
}

:deep(.el-date-editor .el-range-input) {
  color: #a3deff;
}

:deep(.el-date-editor .el-range-separator) {
  color: rgba(255, 255, 255, 0.9);
}
</style>
