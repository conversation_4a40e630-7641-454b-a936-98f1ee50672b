export function option1(name1, name2, a, b, time) {
    return {
        grid: [
            {
                top: '40%',
                bottom: '20px',
                // containLabel: true
            },

        ],
        xAxis: {
            type: 'category',
            data: time,
            boundaryGap: true,
            axisPointer: {
                type: 'shadow'
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#fff"
                },
                onZero: true
            },
            axisLabel: {
                show: false
            },
            axisTick: {
                show: false
            },
        },
        yAxis: {
            type: 'value',
            name: '库水位对比(m)',
            nameLocation: 'end', //坐标轴名称显示位置
            scale: true,
            nameTextStyle: {
                color: "#fff"
            },
            axisLabel: {
                color: "#fff"
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#fff"
                },
            },
            splitLine: {
                lineStyle: {
                    color: "#005b99",
                    type: 'dashed'
                }
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross', crossStyle: { color: '#555' }, label: {
                    backgroundColor: '#555'
                }
            }
        },
        legend: {
            textStyle: {
                color: '#fff',
                fontSize: 12,
                fontFamily: 'PingFang SC'
            }
        },
        series: [
            {
                name: `${name1}-预报水位`,
                data: a,
                type: 'line',
                smooth: true
            },
            // {
            //     name: `${name1}-实测水位`,
            //     data: b,
            //     type: 'line',
            //     smooth: true
            // },
            {
                name: `${name2}-预报水位`,
                data: b,
                type: 'line',
                smooth: true
            },
            // {
            //     name: `${name2}-实测水位`,
            //     data: b1,
            //     type: 'line',
            //     smooth: true
            // }
        ]
    }
}
export function option2(name1, name2, a, b, a1, b1, time) {
    return {
        grid: [
            {
                top: '40%',
                bottom: '20px',
                // containLabel: true
            },

        ],
        xAxis: {
            type: 'category',
            data: time,
            boundaryGap: true,
            axisPointer: {
                type: 'shadow'
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#fff"
                },
                onZero: true
            },
            axisLabel: {
                show: false
            },
            axisTick: {
                show: false
            },
        },
        legend: {
            textStyle: {
                color: '#fff',
                fontSize: 12,
                fontFamily: 'PingFang SC'
            }
        },
        yAxis: {
            type: 'value',
            name: '出入库流量对比(m³/s)',
            nameLocation: 'end', //坐标轴名称显示位置
            scale: true,
            nameTextStyle: {
                color: "#fff",
                padding: [0, 0, 0, 50]
            },
            axisLabel: {
                color: "#fff",

            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#fff"
                },
            },
            splitLine: {
                lineStyle: {
                    color: "#005b99",
                    type: 'dashed'
                }
            }
        },

        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross', crossStyle: { color: '#555' }, label: {
                    backgroundColor: '#555'
                }
            }
        },
        series: [
            {
                name: `${name1}-预报入库流量`,
                data: a,
                type: 'line',
                smooth: true
            },
            {
                name: `${name1}-预报出库流量`,
                data: b,
                type: 'line',
                smooth: true
            },
            {
                name: `${name2}-预报入库流量`,
                data: a1,
                type: 'line',
                smooth: true
            },
            {
                name: `${name2}-预报出库流量`,
                data: b1,
                type: 'line',
                smooth: true
            }
        ]
    }
}