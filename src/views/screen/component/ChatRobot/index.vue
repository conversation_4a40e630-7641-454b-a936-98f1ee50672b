<!--
 * @file RobotAssistant.vue
 * @description 机器人助手组件，提供右下角悬浮按钮和弹窗功能
 * @features
 * - 悬浮机器人按钮
 * - 点击展示取用水指南弹窗
 * - 提供快速查看取用水相关信息
 * - 支持与Dify AI聊天交互功能
 * - 支持地图、数据、报告三个选项卡功能
 -->

<template>
  <!-- 机器人助手按钮 -->
  <div class="robot-button" @click="toggleRobotPopup">
    <img
      src="@/assets/images/robot-assistant.svg"
      class="robot-icon"
      alt="AI助手"
    />
  </div>

  <!-- 机器人助手弹窗 -->
  <div class="robot-popup" v-show="showRobotPopup">
    <div class="popup-header">
      <div class="popup-title">
        <img
          src="@/assets/images/robot-assistant.svg"
          class="popup-robot-icon"
          alt="AI助手"
        />
        <span>防洪四预助手</span>
      </div>
      <button class="close-button" @click="toggleRobotPopup">
        <Icon icon="mdi:close" />
      </button>
    </div>

    <div class="tab-container">
      <div class="tab-nav">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          class="tab-item"
          :class="{ active: activeTab === tab.key }"
          @click="switchTab(tab.key)"
        >
          <Icon :icon="tab.icon" class="tab-icon" />
          {{ tab.name }}
        </div>
      </div>
    </div>

    <div class="popup-content">
      <DifyChatContainer ref="difyChatRef" />
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
import { Icon } from "@iconify/vue";
import DifyChatContainer from "./components/DifyChatContainer.vue";

defineOptions({
  name: "RobotAssistant",
});

// 弹窗显示状态
const showRobotPopup = ref(false);
// 切换弹窗显示状态
const toggleRobotPopup = () => {
  showRobotPopup.value = !showRobotPopup.value;
};

// 选项卡配置
const tabs = [
  {
    key: "map",
    name: "地图",
    icon: "mdi:map",
    api: "app-dhKH5HPDS84tywtHsaCWUfRT",
  },
  {
    key: "data",
    name: "数据",
    icon: "mdi:chart-box",
    api: "app-TLhzXgVobGiVISnCIjHsA10O",
  },
  {
    key: "report",
    name: "方案",
    icon: "mdi:play",
    api: "app-94x1rpq88xInA82fv816UQtv",
  },
];

// 当前活动选项卡
const activeTab = ref("map");

// DifyChatContainer组件引用
const difyChatRef = ref(null);

// 获取当前选中标签的API
const getCurrentTabApi = computed(() => {
  const currentTab = tabs.find((tab) => tab.key === activeTab.value);
  return currentTab ? currentTab.api : tabs[0].api;
});

// 切换选项卡
const switchTab = (tabKey) => {
  activeTab.value = tabKey;
  // 调用DifyChatContainer组件的方法重新初始化
  if (difyChatRef.value) {
    difyChatRef.value.initDifyClient(getCurrentTabApi.value);
    difyChatRef.value.startNewChat();
    difyChatRef.value.getAppParameters();
  }
};

onMounted(() => {
  if (difyChatRef.value) {
    difyChatRef.value.initDifyClient(getCurrentTabApi.value);
    difyChatRef.value.getAppParameters();
  }
});
</script>

<style lang="scss" scoped>
/* 机器人按钮样式 */
.robot-button {
  position: fixed;
  bottom: 25px;
  right: 25px;
  width: 54px;
  height: 54px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px) scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  .robot-icon {
    width: 100%;
    height: 100%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &:hover .robot-icon {
    transform: scale(1.02);
  }
}

/* 机器人弹窗样式 */
.robot-popup {
  position: fixed;
  bottom: 20px;
  right: 15px;
  width: 500px;
  height: calc(100vh - 100px);
  max-height: 770px;
  border-radius: 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  z-index: 1001;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: popup-slide-in 0.3s ease-out;
  transform-origin: bottom right;
  background-color: #082648;
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;

  @keyframes popup-slide-in {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 简化标签动画 */
  .tab-item {
    animation: tab-item-fade-in 0.3s ease-out backwards;
  }

  .tab-item:nth-child(1) {
    animation-delay: 0.1s;
  }

  .tab-item:nth-child(2) {
    animation-delay: 0.15s;
  }

  .tab-item:nth-child(3) {
    animation-delay: 0.2s;
  }

  @keyframes tab-item-fade-in {
    0% {
      opacity: 0;
      transform: translateY(-4px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .popup-header {
    padding: 10px 16px;
    background: linear-gradient(90deg, #001a38 0%, #003366 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0, 121, 255, 0.3);
    position: relative;
    overflow: hidden;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(
        90deg,
        rgba(17, 149, 255, 0.2),
        rgba(17, 149, 255, 0.8),
        rgba(17, 149, 255, 0.2)
      );
    }

    .popup-title {
      display: flex;
      align-items: center;
      gap: 10px;
      font-weight: 500;
      color: white;
      font-size: 16px;
      letter-spacing: 1px;
      text-shadow: 0 0 5px rgba(0, 174, 255, 0.3);

      .popup-robot-icon {
        width: 24px;
        height: 24px;
        padding: 0;
      }
    }

    .close-button {
      background: none;
      border: none;
      cursor: pointer;
      color: rgba(255, 255, 255, 0.9);
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        color: white;
        background-color: rgba(0, 121, 255, 0.3);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .tab-container {
    background-color: #ffffff;
    padding: 6px 10px;
    position: relative;

    .tab-nav {
      display: flex;
      justify-content: space-around;
      padding: 0;
      border-radius: 8px;
      background-color: #f5f7fa;
      margin: 0;
      position: relative;
      z-index: 1;

      .tab-item {
        flex: 1;
        text-align: center;
        padding: 10px 12px;
        font-size: 14px;
        cursor: pointer;
        color: #606266;
        transition: all 0.2s ease;
        position: relative;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        z-index: 2;

        .tab-icon {
          font-size: 16px;
          color: inherit;
        }

        &:hover {
          color: #2196f3;
        }

        &.active {
          color: #2196f3;
          font-weight: 600;

          &:after {
            content: "";
            position: absolute;
            bottom: -1px;
            left: 20%;
            width: 60%;
            height: 2px;
            background-color: #2196f3;
            border-radius: 1px;
          }
        }
      }
    }
  }

  .popup-content {
    overflow-y: auto;
    flex-grow: 1;
    padding: 0;
  }
}
</style>
