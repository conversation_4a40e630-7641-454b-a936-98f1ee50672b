<!--
 * @file ReferenceResources.vue
 * @description 引用资源展示组件，负责展示和管理引用资源的展示状态
 * @features
 * - 支持水平排列的文件名列表
 * - 支持展开/收起文件内容
 * - 支持复制文件内容
 * - 支持相关度显示
 -->

<template>
  <div v-if="resources && resources.length > 0" class="references-container">
    <div class="references-header">
      <Icon icon="mdi:book-open-variant" />
      <span>引用 ({{ resources.length }})</span>
    </div>
    <!-- 水平排列的文件引用列表 -->
    <div class="references-files-row">
      <div
        v-for="(resource, index) in resources"
        :key="index"
        class="reference-file"
        :class="{ 'reference-file-active': isReferenceExpanded(index) }"
        @click="handleReferenceClick(index, resource)"
      >
        <Icon icon="mdi:file-document-outline" class="file-icon" />
        <span class="file-name">{{ resource.document_name || "文档" }}</span>
      </div>
    </div>

    <!-- 展开的引用内容区域 -->
    <div
      v-for="(resource, index) in resources"
      :key="`content-${index}`"
      v-show="isReferenceExpanded(index)"
      class="reference-detail"
    >
      <div class="reference-detail-header">
        <span class="reference-name">
          {{ resource.document_name || "文档" }}
        </span>
        <div class="reference-actions">
          <span class="reference-score">
            相关度: {{ (resource.score * 100).toFixed(1) }}%
          </span>
          <button
            class="copy-reference-btn"
            @click.stop="copyReferenceContent(resource.content)"
            title="复制引用内容"
          >
            <Icon icon="mdi:content-copy" />
          </button>
        </div>
      </div>
      <div class="reference-content">
        <pre>{{ resource.content }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { Icon } from "@iconify/vue";
import { ElMessage } from "element-plus";

const props = defineProps({
  resources: {
    type: Array,
    required: true,
  },
  conversationId: {
    type: String,
    default: "",
  },
});

// 展开的引用列表
const expandedReferences = ref([]);

// 切换引用展开状态
const toggleReferenceExpand = (index) => {
  const i = expandedReferences.value.indexOf(index);
  if (i > -1) {
    expandedReferences.value.splice(i, 1);
  } else {
    expandedReferences.value.push(index);
  }
};

// 检查引用是否展开
const isReferenceExpanded = (index) => {
  return expandedReferences.value.includes(index);
};

// 复制引用内容
const copyReferenceContent = async (content) => {
  try {
    await navigator.clipboard.writeText(content);
    ElMessage.success("复制成功");
  } catch (err) {
    ElMessage.error("复制失败");
  }
};

// 记录引用资源的使用情况
const logReferenceUsage = async (resourceId) => {
  if (props.conversationId && resourceId) {
    try {
      // 这里可以调用API记录引用资源的使用情况
      await dify.logReferenceUsage({
        conversation_id: props.conversationId,
        resource_id: resourceId,
      });
    } catch (error) {
      console.error("记录引用资源使用情况失败:", error);
    }
  }
};

// 处理引用资源点击
const handleReferenceClick = async (index, resource) => {
  toggleReferenceExpand(index);
  if (isReferenceExpanded(index)) {
    await logReferenceUsage(resource.id);
  }
};
</script>

<style lang="scss" scoped>
.references-container {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f7f7f9;
  border-radius: 6px;
  border: 1px solid #ebebf0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;

  .references-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid #e8e8ed;

    .iconify {
      font-size: 16px;
      margin-right: 6px;
      color: #0052d9;
    }

    span {
      font-size: 13px;
      font-weight: 500;
      color: #444;
    }
  }

  .references-files-row {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 8px;

    .reference-file {
      display: flex;
      align-items: center;
      gap: 3px;
      padding: 4px 8px;
      background: #fff;
      border-radius: 4px;
      border: 1px solid #e0e0e0;
      cursor: pointer;
      transition: all 0.2s ease;
      user-select: none;
      max-width: calc(50% - 3px);
      min-width: 80px;

      &:hover {
        border-color: #0052d9;
        background-color: #f5f8ff;
      }

      &.reference-file-active {
        background: #e8f0ff;
        border-color: #0052d9;
        font-weight: 500;
      }

      .file-icon {
        font-size: 14px;
        color: #666;
        flex-shrink: 0;
      }

      .file-name {
        font-size: 12px;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .reference-detail {
    margin-bottom: 8px;
    padding: 8px;
    background: #fff;
    border-radius: 6px;
    border: 1px solid #ebebf0;
    transition: all 0.2s ease;
    animation: fadeIn 0.2s ease;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      border-color: #d0d0d8;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    }

    .reference-detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
      padding-bottom: 4px;
      border-bottom: 1px dashed #ebebf0;
      gap: 8px;

      .reference-name {
        font-size: 12px;
        font-weight: 500;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }

      .reference-actions {
        display: flex;
        align-items: center;
        gap: 6px;
        flex-shrink: 0;

        .reference-score {
          font-size: 11px;
          color: #666;
          background: #f0f7ff;
          padding: 1px 4px;
          border-radius: 3px;
          white-space: nowrap;
        }

        .copy-reference-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          border: none;
          background: transparent;
          cursor: pointer;
          border-radius: 3px;
          transition: all 0.2s;
          padding: 0;

          &:hover {
            background: #f0f0f5;
          }

          .iconify {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }

    .reference-content {
      max-height: 120px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 1.5px;

        &:hover {
          background-color: rgba(0, 0, 0, 0.2);
        }
      }

      pre {
        margin: 0;
        padding: 6px;
        font-size: 11px;
        line-height: 1.4;
        color: #444;
        font-family: "Menlo", "Monaco", "Consolas", monospace;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-3px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
</style>
