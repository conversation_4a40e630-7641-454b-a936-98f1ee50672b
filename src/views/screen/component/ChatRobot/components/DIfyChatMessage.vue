<!--
 * @file DIfyChatMessage.vue
 * @description 聊天消息组件，负责渲染单条消息的展示，包括用户消息和AI响应
 * @features
 * - 支持多种消息类型（文本、图片、文件）
 * - 支持Markdown渲染
 * - 支持代码高亮
 * - 支持ECharts图表渲染
 * - 支持地图路线规划
 * - 支持消息操作（复制、重新生成等）
 * - 支持思考过程展示
 -->

<template>
  <!-- 消息容器，根据消息角色应用不同样式 -->
  <div :class="['message-item', message.role]">
    <!-- 消息头部：头像、名称和时间 -->
    <div class="message-header">
      <template v-if="message.avatar.startsWith('mdi:')">
        <Icon :icon="message.avatar" class="avatar-icon" />
      </template>
      <img v-else :src="message.avatar" :alt="message.name" class="avatar" />
      <span class="name">{{ message.name }}</span>
      <span class="time">{{ formatTime(message.datetime) }}</span>
    </div>

    <!-- AI助手思考状态显示 -->
    <div
      v-if="loading && isLastMessage && message.role === 'assistant'"
      class="thinking thinking-ai"
    >
      <div class="loading-indicator">
        <div class="loading-dots"><span></span><span></span><span></span></div>
      </div>
      <span class="thinking-text"
        >思考中<span class="dot-animation"
          ><span>.</span><span>.</span><span>.</span></span
        ></span
      >
    </div>

    <!-- AI助手工作流loading -->
    <div
      v-if="message.role === 'assistant' && workflowLoading && isLastMessage"
      class="thinking thinking-workflow"
    >
      <div class="loading-indicator">
        <div class="loading-circle"></div>
      </div>
      <span class="thinking-text"
        >
        <!-- 工作流运行中 -->
        正在深度思考中
        <span class="dot-animation"
          ><span>.</span><span>.</span><span>.</span></span
        ></span
      >
    </div>

    <!-- 消息内容区域 -->
    <div class="message-content">
      <!-- 图片展示区域 -->
      <div
        v-if="message.files && message.files.length > 0"
        class="files-content"
      >
        <div
          v-for="(file, fileIndex) in message.files"
          :key="fileIndex"
          class="file-item"
          @click="handleFileClick(file)"
        >
          <!-- 图片预览 -->
          <div class="image-preview" v-if="file.localUrl">
            <img :src="file.localUrl" :alt="file.name" />
          </div>
          <div class="file-info">
            <div class="file-icon">
              <Icon :icon="getFileIcon(file.type)" />
            </div>
            <div class="file-details">
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 文本内容展示区域 -->
      <div class="text-content">
        <!-- AI思考过程展示区域 -->
        <ChatReasoning :message="message" />

        <!-- 主要消息内容区域 -->
        <div
          class="markdown-content markdown-body"
          v-html="renderedContent"
          ref="markdownContent"
        ></div>
      </div>
    </div>
    <!-- 调配方案预览组件 -->
    <ForecastDispatchPlan
      :list="forecastDispatchPlanList"
      v-if="hasForecastDispatchPlan"
    />
    <!-- 引用资源展示区域 -->
    <ReferenceResources
      v-if="
        message.retriever_resources &&
        message.retriever_resources.length > 0 &&
        message.role === 'assistant'
      "
      :resources="message.retriever_resources"
      :conversation-id="conversationId"
    />

    <!-- 消息操作按钮区域 -->
    <div
      v-if="
        message.role === 'user' ||
        (!isStreamLoad && message.role === 'assistant')
      "
      :class="['message-actions', { 'last-message': isLastMessage }]"
    >
      <!-- 悬浮操作按钮组 -->
      <div class="hover-buttons">
        <button
          @click="$emit('operation', 'copy', { index })"
          class="action-btn"
          title="复制"
        >
          <Icon icon="mdi:content-copy" />
        </button>
        <button
          @click="saveDomAsImage(markdownContent)"
          class="action-btn"
          title="保存为图片"
          v-if="message.role === 'assistant'"
        >
          <Icon icon="mdi:image-outline" />
        </button>
        <template v-if="message.role === 'assistant'">
          <button
            @click="$emit('operation', 'replay', { index })"
            class="action-btn"
            title="重新生成"
          >
            <Icon icon="mdi:refresh" />
          </button>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from "vue";
import MarkdownIt from "markdown-it"; // Markdown渲染器
import hljs from "highlight.js"; // 代码高亮
import "highlight.js/styles/github.css"; // 代码高亮样式
import "github-markdown-css/github-markdown-light.css";
import { Icon } from "@iconify/vue"; // 图标组件
import ClipboardJS from "clipboard"; // 剪贴板操作
import { useECharts } from "@/composables/useECharts"; // ECharts图表渲染工具
import { useCesium } from "@/composables/useCesium"; // 导入Cesium地图渲染工具
import ChatReasoning from "./DIfyChatReasoning/index.vue";
import ReferenceResources from "./ReferenceResources/index.vue";
import ForecastDispatchPlan from "./ForecastDispatchPlan/index.vue";
import { saveDomAsImage } from "@/utils/saveAsImage";
import { ElMessage } from "element-plus";

/**
 * @component 消息组件Props定义
 * @property {Object} message - 消息对象，包含角色、内容、时间等信息
 * @property {Number} index - 消息在列表中的索引
 * @property {Boolean} isLastMessage - 是否为最后一条消息
 * @property {Boolean} loading - 是否正在加载
 * @property {Boolean} isStreamLoad - 是否为流式加载
 * @property {String} conversationId - 对话ID
 * @property {Boolean} showReferences - 是否显示引用
 * @property {Boolean} showAnnotations - 是否显示注释
 */
const props = defineProps({
  message: {
    type: Object,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
  isLastMessage: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  workflowLoading: {
    type: Boolean,
    default: false,
  },
  isStreamLoad: {
    type: Boolean,
    default: false,
  },
  conversationId: {
    type: String,
    default: "",
  },
  showReferences: {
    type: Boolean,
    default: false,
  },
  showAnnotations: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(["operation"]);

// 引入ECharts相关功能
const { echartsCounter, parseChartData, renderCharts } = useECharts();

/**
 * Markdown渲染器配置
 * 支持HTML、换行、链接自动识别、排版优化
 * 自定义代码高亮，支持ECharts图表渲染
 */
const md = new MarkdownIt({
  html: true, // 允许HTML标签
  breaks: true, // 转换换行符为 <br>
  linkify: true, // 自动识别链接
  typographer: true, // 启用排版优化
  highlight: function (str, lang) {
    // ECharts图表代码块处理
    if (lang === "echarts") {
      const index = echartsCounter.value++;
      return `<div class="echarts-placeholder" data-index="${index}">
        <div class="chart-wrapper">
          <div class="chart-loading">
            <div class="loading-dots">
              <span></span><span></span><span></span>
            </div>
            <span class="loading-text">图表生成中...</span>
          </div>
        </div>
      </div>`;
    }

    // cesium代码块处理
    if (lang === "cesium") {
      // 添加提示信息的代码块
      return `<div class='cesium-placeholder'>
        <svg class="cesium-icon" viewBox="0 0 24 24" width="20" height="20">
          <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="cesium-text">正在加载地图操作，请稍候<span class="loading-dots"><span>.</span><span>.</span><span>.</span></span></span>
      </div>`;
    }

    // 普通代码块处理
    const codeId = generateUniqueId();
    try {
      // 构建代码块头部（包含语言标识和复制按钮）
      let html = [
        '<div class="code-header">',
        `<span class="code-lang">${lang || "text"}</span>`,
        `<button class="copy-btn" type="button" data-clipboard-action="copy" data-clipboard-target="#${codeId}">`,
        '<span class="copy-text">复制</span>',
        "</button>",
        "</div>",
      ].join("");

      // 代码高亮处理
      let highlightedCode = str;
      if (lang && hljs.getLanguage(lang)) {
        highlightedCode = hljs.highlight(str, { language: lang }).value;
      } else {
        highlightedCode = md.utils.escapeHtml(str);
      }

      // 构建完整的代码块HTML
      html += `<pre class="hljs"><code>${highlightedCode}</code></pre>`;
      // 添加隐藏的文本区域用于复制
      html += `<textarea
        id="${codeId}"
        style="position: absolute; top: -9999px; left: -9999px; z-index: -9999;"
      >${str}</textarea>`;

      return html;
    } catch (error) {
      console.error("代码高亮处理错误:", error);
      return `<pre class="hljs"><code>${md.utils.escapeHtml(str)}</code></pre>`;
    }
  },
});

// 添加自定义渲染规则，处理连续的 > 开头的段落
let isInsideThinkingBlock = false;
// 标记是否已经处理过第一个思考过程
let firstThinkingProcessed = false;

/**
 * 修改渲染规则，使用Vue的响应式属性处理展开/收起状态
 */
md.renderer.rules.blockquote_open = (tokens, idx) => {
  // 只有第一次出现的连续引用块被视为思考过程
  if (!isInsideThinkingBlock && !firstThinkingProcessed) {
    isInsideThinkingBlock = true;
    return `<div class="pseudo-thinking-container">
      <div class="thinking-header">
        <div class="header-left">
          <div class="icon-wrapper">
            <span class="thinking-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20">
                <path fill="currentColor" d="M13 3c3.9 0 7 3.1 7 7 0 2.8-1.6 5.2-4 6.3V21H9v-4.7c-2.4-1.1-4-3.5-4-6.3 0-3.9 3.1-7 7-7h1Zm1 2h-2.9c-2.8 0-5.1 2.2-5.1 5 0 2.2 1.4 4.1 3.4 4.8l.6.2V19h2v-3.9l.6-.2c2-.7 3.4-2.6 3.4-4.9 0-2.8-2.3-5-5.1-5h.1Z"/>
              </svg>
            </span>
          </div>
          <div class="text-wrapper">
            <span class="thinking-title">思考过程</span>
          </div>
        </div>
        <div class="header-right">
          <button class="action-btn toggle-thinking" onclick="this.parentNode.parentNode.parentNode.classList.toggle('collapsed'); this.querySelector('.toggle-icon').classList.toggle('collapsed');">
            <span class="toggle-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" class="arrow">
                <path fill="currentColor" d="m12 13.171 4.95-4.95 1.414 1.415L12 16 5.636 9.636 7.05 8.222l4.95 4.95Z"/>
              </svg>
            </span>
          </button>
        </div>
      </div>
      <div class="thinking-content">`;
  } else if (!isInsideThinkingBlock && firstThinkingProcessed) {
    // 非第一个引用块，使用普通的blockquote渲染
    return "<blockquote>";
  }
  return "";
};

md.renderer.rules.blockquote_close = (tokens, idx, options, env, slf) => {
  // 判断是否是连续引用块的最后一个
  const nextToken = tokens[idx + 1];

  if (isInsideThinkingBlock) {
    if (!nextToken || nextToken.type !== "blockquote_open") {
      isInsideThinkingBlock = false;
      firstThinkingProcessed = true;
      return "</div></div>";
    }
    return "";
  } else {
    // 普通引用块的关闭标签
    return "</blockquote>";
  }
};
// 修改引用内容的渲染处理
md.renderer.rules.paragraph_open = (tokens, idx, options, env, slf) => {
  const token = tokens[idx];
  if (token.level > 0 && isInsideThinkingBlock) {
    return '<div class="pseudo-thinking">';
  }
  return slf.renderToken(tokens, idx, options);
};

md.renderer.rules.paragraph_close = (tokens, idx, options, env, slf) => {
  const token = tokens[idx];
  if (token.level > 0 && isInsideThinkingBlock) {
    return "</div>";
  }
  return slf.renderToken(tokens, idx, options);
};

/**
 * 工具函数：生成唯一ID
 * 用于代码块复制功能
 */
const generateUniqueId = () => {
  return `code-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * @function formatTime
 * @description 时间格式化函数，根据消息时间与当前时间的差异显示不同格式
 * - 今天的消息：显示具体时间（时:分）
 * - 昨天的消息：显示"昨天"和具体时间
 * - 更早的消息：显示完整日期和时间
 * @param {number|string|Date} date - 时间戳或日期对象
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = (date) => {
  const now = new Date();
  const messageDate = new Date(date);

  // 今天的消息只显示时间
  if (now.toDateString() === messageDate.toDateString()) {
    return messageDate.toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  // 昨天的消息显示"昨天"和时间
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  if (yesterday.toDateString() === messageDate.toDateString()) {
    return (
      "昨天 " +
      messageDate.toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
      })
    );
  }

  // 更早的消息显示完整日期和时间
  return messageDate.toLocaleString("zh-CN", {
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

/**
 * @function getFileIcon
 * @description 根据文件类型返回对应的图标名称
 * @param {String} fileType - 文件MIME类型
 * @returns {String} 图标名称
 */
const getFileIcon = (fileType) => {
  if (fileType.startsWith("image/") || fileType === "image") return "mdi:image";
  if (fileType.startsWith("video/")) return "mdi:video";
  if (fileType.startsWith("audio/")) return "mdi:music";
  if (fileType.includes("pdf")) return "mdi:file-pdf";
  if (fileType.includes("word")) return "mdi:file-word";
  if (fileType.includes("excel") || fileType.includes("sheet"))
    return "mdi:file-excel";
  if (fileType.includes("powerpoint") || fileType.includes("presentation"))
    return "mdi:file-powerpoint";
  return "mdi:file-document";
};

/**
 * @function formatFileSize
 * @description 格式化文件大小，自动转换单位（B/KB/MB/GB）
 * @param {Number} bytes - 文件大小（字节）
 * @returns {String} 格式化后的文件大小
 */
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * @computed chartData
 * @description 解析消息内容中的图表数据
 * @returns {Array|null} 解析后的图表数据数组
 */
const chartData = computed(() => {
  if (!props.message.content) return null;
  return parseChartData(props.message.content);
});

/**
 * @computed renderedContent
 * @description 渲染消息内容，处理思考标签和Markdown格式
 * @returns {String} 渲染后的HTML内容
 */
const renderedContent = computed(() => {
  echartsCounter.value = 0;
  if (!props.message.content) return "";

  // 重置思考过程状态，确保每条消息都能正确处理
  isInsideThinkingBlock = false;
  firstThinkingProcessed = false;

  let content = props.message.content || "";

  // 处理完整的思考标签
  const thinkMatch = content.match(/<think>([\s\S]*?)<\/think>/);
  if (thinkMatch) {
    content = content.replace(/<think>[\s\S]*?<\/think>/, "").trim();
  }
  // 处理未结束的思考标签
  else if (content.includes("<think>")) {
    content = "";
  }

  const rendered = content ? md.render(content) : "";
  return rendered;
});

const markdownContent = ref(null);

/**
 * @watch message.content
 * @description 监听消息内容和图表数据变化，渲染ECharts图表
 */
watch(
  () => props.message.content,
  (newContent, oldContent) => {
    nextTick(() => {
      if (!markdownContent.value || !newContent || newContent === oldContent) {
        return;
      }

      const chartData = parseChartData(newContent);
      if (chartData && Array.isArray(chartData)) {
        // 确保所有图表都被渲染
        renderCharts(markdownContent.value, chartData);
      }
    });
  },
  { immediate: true }
);

const { parseCesiumData, executeOperation } = useCesium();

// 是否包含调配方案预览
const hasForecastDispatchPlan = ref(false);
// 调配方案预览列表
const forecastDispatchPlanList = ref([]);

// 添加cesiumDataExecuted状态变量
const cesiumDataExecuted = ref(false);

watch(
  () => props.isStreamLoad,
  async (val) => {
    // 流式加载结束，默认折叠思考块
    // 延迟执行折叠操作，确保所有内容都已渲染
    nextTick(() => {
      setTimeout(() => {
        const thinkingContainers = document.querySelectorAll(
          ".pseudo-thinking-container"
        );
        thinkingContainers.forEach((container) => {
          if (!container.classList.contains("collapsed")) {
            container.classList.add("collapsed");
            const arrow = container.querySelector(".arrow");
            if (arrow) {
              arrow.classList.add("collapsed");
            }
          }
        });
      }, 500);
    });

    // 处理Cesium操作
    if (!val && props.isLastMessage) {
      const cesiumData = parseCesiumData(props.message.content);
      console.log(cesiumData, "cesiumData");

      if (cesiumData && cesiumData.length > 0) {
        // 遍历外层数组
        for (const operationGroup of cesiumData) {
          // 判断内层是否为数组
          const operations = Array.isArray(operationGroup)
            ? operationGroup
            : [operationGroup];
          // 串行执行每个操作
          for (const operation of operations) {
            try {
              // 执行当前操作并等待完成
              const result = await executeOperation(operation);
              console.log("Cesium operation result:", result);

              // 添加安全检查，确保result不是undefined
              if (!result) {
                console.error("Operation returned undefined result");
                continue; // 跳过当前操作，继续下一个
              }

              // 在这里添加错误处理UI逻辑
              if (!result.success) {
                console.error("Operation failed:", result.error);
                ElMessage.warning(result.error);
                continue;
              }

              // 添加成功UI逻辑，例如显示成功消息或图标
              if (result.success) {
                if (result.message) {
                  ElMessage.success(result.message);
                }

                // 方案预览
                if (result.type === "forecastDispatchPlan") {
                  hasForecastDispatchPlan.value = true;
                  forecastDispatchPlanList.value = result.data;
                }
              }

              // 添加延迟以确保操作完全执行完成
              await new Promise((resolve) => setTimeout(resolve, 500));
            } catch (error) {
              console.error("Error executing Cesium operation:", error);
            }
          }
        }

        // cesiumData全部执行完毕，标记执行状态为完成
        cesiumDataExecuted.value = true;

        // 更新cesium代码块状态为已完成
        nextTick(() => {
          const cesiumPlaceholders = document.querySelectorAll(
            ".cesium-placeholder"
          );
          cesiumPlaceholders.forEach((placeholder) => {
            placeholder.classList.add("cesium-completed");
            const textElement = placeholder.querySelector(".cesium-text");
            if (textElement) {
              textElement.textContent = "地图场景加载完成";
            }

            // 更新图标为完成状态
            const iconElement = placeholder.querySelector(".cesium-icon");
            if (iconElement) {
              // 替换为勾选图标
              iconElement.innerHTML = `
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20 7l-8 4-8-4" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              `;
            }
          });
        });
      }
    }
  }
);

// 图片预览相关状态
const isPreviewVisible = ref(false);
const previewImage = ref(null);

/**
 * 判断文件是否为图片
 * @param {Object} file - 文件对象
 * @returns {Boolean} 是否为图片
 */
const isImageFile = (file) => {
  return file.type === "image" || (file.type && file.type.startsWith("image/"));
};

/**
 * 处理文件点击事件
 * @param {Object} file - 被点击的文件
 */
const handleFileClick = (file) => {
  if (isImageFile(file)) {
    previewImage.value = {
      localUrl: file.localUrl,
      name: file.name,
    };
    isPreviewVisible.value = true;
  }
};

// 初始化 clipboard
const clipboard = new ClipboardJS(".copy-btn");
onMounted(() => {
  clipboard.on("success", (e) => {
    const button = e.trigger;
    button.textContent = "已复制";
    setTimeout(() => {
      button.textContent = "复制";
    }, 2000);
    e.clearSelection();
  });

  clipboard.on("error", () => {
    console.error("复制失败");
  });
});

onUnmounted(() => {
  clipboard.destroy();
});
</script>

<style lang="scss" scoped>
@use "@/assets/styles/markdown.scss";
$transition-quick: all 0.2s ease;

.message-item {
  animation: message-fade-in 0.4s ease-out;

  @keyframes message-fade-in {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 用户消息样式 - 显示在右侧 */
  &.user {
    .message-header {
      flex-direction: row-reverse;

      .name {
        margin-right: 8px;
      }

      .time {
        margin-left: 0;
      }

      .avatar-icon {
        background-color: #e3f2fd;
        color: #1a73e8;
      }
    }

    .message-content {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .text-content {
        background: #e8f4ff;
        border-radius: 12px 2px 12px 12px;
        padding: 12px 16px;
        min-width: 60px;
        margin-left: auto;
        word-break: break-word;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      }
    }

    .files-content {
      justify-content: flex-end;
      max-width: 80%;
      margin-left: auto;
    }

    .message-actions {
      justify-content: flex-end;
    }
  }

  /* AI消息样式 - 显示在左侧 */
  &.assistant {
    .message-header {
      .avatar-icon {
        background-color: rgba(26, 115, 232, 0.1);
        color: #1a73e8;
      }
    }
  }

  /* 消息头部样式 */
  .message-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .avatar-icon {
      padding: 2px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 26px;
      height: 26px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    .avatar {
      width: 46px;
      height: 46px;
      border-radius: 50%;
      object-fit: cover;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    .name {
      margin-left: 12px;
      font-weight: 600;
      color: #333;
      font-size: 15px;
    }

    .time {
      margin-left: 12px;
      font-size: 12px;
      color: #888;
    }
  }

  /* AI思考状态样式 */
  .thinking {
    display: flex;
    align-items: center;
    background-color: rgba(250, 250, 250, 0.8);
    border-radius: 12px;
    padding: 8px 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;
    max-width: fit-content;
    animation: thinking-appear 0.5s ease-out;

    @keyframes thinking-appear {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .loading-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .loading-dots {
      display: flex;
      gap: 4px;

      span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        animation: dot-pulse 1.5s infinite ease-in-out;
      }
    }

    .loading-circle {
      width: 18px;
      height: 18px;
      border: 2px solid transparent;
      border-radius: 50%;
      animation: circle-spin 1.2s linear infinite;
    }

    .thinking-text {
      margin-left: 12px;
      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0.2px;
      position: relative;

      .dot-animation {
        display: inline-flex;
        margin-left: 1px;

        span {
          opacity: 0;
          animation: dotFade 1.4s infinite;

          &:nth-child(1) {
            animation-delay: 0s;
          }

          &:nth-child(2) {
            animation-delay: 0.2s;
          }

          &:nth-child(3) {
            animation-delay: 0.4s;
          }
        }
      }
    }
  }

  /* AI思考状态特定样式 */
  .thinking-ai {
    border-left: 3px solid rgba(26, 115, 232, 0.8);

    .loading-dots span {
      background-color: rgba(26, 115, 232, 0.6);

      &:nth-child(1) {
        animation-delay: 0s;
      }

      &:nth-child(2) {
        animation-delay: 0.3s;
      }

      &:nth-child(3) {
        animation-delay: 0.6s;
      }
    }

    .thinking-text {
      color: rgba(26, 115, 232, 0.9);

      &::after {
        content: "";
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 100%;
        height: 1px;
        background: linear-gradient(
          to right,
          rgba(26, 115, 232, 0.5),
          transparent
        );
      }
    }
  }

  /* 工作流状态特定样式 */
  .thinking-workflow {
    border-left: 3px solid rgba(76, 175, 80, 0.8);

    .loading-circle {
      border-top-color: rgba(76, 175, 80, 0.8);
      border-right-color: rgba(76, 175, 80, 0.4);
      border-bottom-color: rgba(76, 175, 80, 0.2);
    }

    .thinking-text {
      color: rgba(76, 175, 80, 0.9);

      &::after {
        content: "";
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 100%;
        height: 1px;
        background: linear-gradient(
          to right,
          rgba(76, 175, 80, 0.5),
          transparent
        );
      }
    }
  }

  @keyframes dot-pulse {
    0%,
    100% {
      transform: scale(0.8);
      opacity: 0.6;
    }
    50% {
      transform: scale(1.2);
      opacity: 1;
    }
  }

  @keyframes circle-spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .message-actions {
    padding: 0 8px;
    margin-top: 4px;
    display: flex;
    gap: 8px;
    align-items: center;
    opacity: 0;
    transition: opacity 0.2s;

    &.last-message {
      opacity: 1;
    }

    .hover-buttons {
      display: flex;
      gap: 8px;

      .action-btn {
        padding: 10px;
        width: 42px;
        height: 42px;
        border: none;
        background: transparent;
        color: #5d5d5d;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        .iconify {
          font-size: 18px;
        }

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }

  &:hover .message-actions {
    opacity: 1;
  }

  :deep(.echarts-placeholder) {
    display: flex;
    width: 100%;
    flex-wrap: wrap;

    .chart-wrapper {
      width: 100%;
      // min-width: 600px;
      border-radius: 8px;
      overflow: hidden;
      overflow-x: auto;
      margin: 16px;
      height: 400px;
      position: relative;

      .echarts-chart {
        width: 100%;
        height: 400px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        opacity: 0;
        transition: opacity 0.3s ease-in;
        position: absolute;
        top: 0;
        left: 0;

        &[style*="display: block"] {
          opacity: 1;
        }
      }
    }
  }

  /* 修改地图容器相关样式 */
  :deep(.map-placeholder) {
    display: flex;
    width: 100%;
    flex-wrap: wrap;

    .map-wrapper {
      width: 100%;
      border-radius: 8px;
      overflow: hidden;
      height: 500px;
      position: relative;
      border: 1px solid #eee;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .map-container {
        width: 100%;
        height: 100%;
        position: absolute;
        display: flex;
        top: 0;
        left: 0;

        .map {
          flex: 1;
          height: 100%;
          position: relative;

          /* 高德地图控件样式优化 */
          .amap-logo,
          .amap-copyright {
            opacity: 0.3;
          }

          .amap-toolbar,
          .amap-scale {
            border-radius: 4px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    .route-panel {
      width: 280px;
      max-height: 100%;
      overflow-y: auto;
      font-size: 13px;
      box-sizing: border-box;
      border-radius: 8px;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #e0e0e0;
      }

      .amap-call {
        display: none;
      }
    }
  }

  :deep(.map-error) {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: #f53f3f;
    background: linear-gradient(to right, #fff9f9, #fff);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;

    /* 添加错误图标和动画 */
    &::before {
      content: "!";
      width: 48px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      font-size: 24px;
      font-weight: bold;
      background: rgba(245, 63, 63, 0.1);
      color: #f53f3f;
      border-radius: 50%;
      margin-bottom: 16px;
      animation: iconPulse 2s ease-in-out infinite;
      box-shadow: 0 0 0 0 rgba(245, 63, 63, 0.4);
      transform-origin: center;
      transition: all 0.3s ease;
    }

    /* 主要错误信息 */
    font-size: 16px;
    font-weight: 500;

    .error-detail {
      margin-top: 12px;
      font-size: 14px;
      color: #86909c;
      font-weight: normal;
      max-width: 80%;
      line-height: 1.6;
    }
  }

  @keyframes iconPulse {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(245, 63, 63, 0.4);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 0 10px rgba(245, 63, 63, 0);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(245, 63, 63, 0);
    }
  }
}

/* 解决地图控件样式被markdown-it 样式覆盖 */
:deep(.route-panel) {
  all: initial;
}

.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);

  .modal-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;

    img {
      max-width: 100%;
      max-height: 90vh;
      object-fit: contain;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .close-button {
      position: absolute;
      top: -40px;
      right: -40px;
      background: transparent;
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: scale(1.1);
      }

      .iconify {
        font-size: 28px;
      }
    }
  }
}

.image-preview {
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

@keyframes dotFade {
  0%,
  100% {
    opacity: 0;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-1px);
  }
}

/* 伪深度思考样式容器 */
:deep(.pseudo-thinking-container) {
  position: relative;
  margin: 16px 0;
  border-radius: 14px;
  border: 1px solid rgba(26, 115, 232, 0.1);
  background: #ffffff;
  box-shadow: 0 2px 12px rgba(26, 115, 232, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;

  &.collapsed {
    .thinking-content {
      display: none;
    }

    .toggle-icon {
      transform: rotate(180deg);
    }
  }

  .thinking-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: rgba(26, 115, 232, 0.02);
    border-bottom: 1px solid rgba(26, 115, 232, 0.1);
    transition: $transition-quick;

    &:hover {
      background: rgba(26, 115, 232, 0.05);
      cursor: pointer;
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .icon-wrapper {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: rgba(26, 115, 232, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;

        .thinking-icon {
          color: #1a73e8;
          height: 20px;
        }
      }

      .text-wrapper {
        display: flex;
        flex-direction: column;

        .thinking-title {
          font-size: 14px;
          font-weight: 600;
          color: #1a73e8;
        }

        .thinking-status {
          font-size: 12px;
          color: #6b7280;
          margin-top: 2px;
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 12px;

      .action-btn {
        background: transparent;
        border: none;
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #6b7280;
        transition: all 0.2s ease;
        outline: none;
        position: relative;

        &:hover {
          background: rgba(26, 115, 232, 0.1);
          color: #1a73e8;
        }
      }
    }
  }

  .thinking-content {
    padding: 16px;
    background: #ffffff;
  }
}

/* 伪深度思考样式 */
:deep(.pseudo-thinking) {
  position: relative;
  padding: 12px;
  margin-bottom: 12px;
  background: #f8f9fc;
  font-size: 14px;
  line-height: 1.6;
  color: #1f2937;
  border-radius: 8px;
  border-left: 3px solid rgba(26, 115, 232, 0.3);
  transition: all 0.25s ease;

  p {
    margin: 0;
    padding: 2px 0;
  }

  code {
    background: rgba(26, 115, 232, 0.1);
    padding: 2px 5px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 13px;
    color: #1a73e8;
  }

  /* 悬停效果 */
  &:hover {
    background: #f3f4ff;
    transform: translateY(-1px);
  }

  /* 分隔线 - 除了最后一个 pseudo-thinking 之外都添加底部分隔线 */
  &:last-child {
    margin-bottom: 0;
  }
}

/* Cesium 提示框样式 */
:deep(.cesium-placeholder) {
  display: flex;
  align-items: center;
  background-color: rgba(240, 247, 255, 0.8);
  border: 1px solid rgba(77, 139, 249, 0.2);
  border-left: 3px solid #4d8bf9;
  border-radius: 8px;
  padding: 12px 16px;
  margin: 0;
  gap: 12px;
  min-height: 48px;
  line-height: 1.4;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(77, 139, 249, 0.1);
  backdrop-filter: blur(4px);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(77, 139, 249, 0.15);
    background-color: rgba(240, 247, 255, 0.9);
  }

  .cesium-icon {
    fill: none;
    stroke: #4d8bf9;
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    animation: iconPulse 2s ease-in-out infinite;
    transform-origin: center;
  }

  .cesium-text {
    font-size: 14px;
    color: #3c4043;
    font-weight: 500;
    letter-spacing: 0.2px;
    display: flex;
    align-items: center;
    gap: 2px;

    .loading-dots {
      display: inline-flex;
      align-items: center;
      height: 14px;
      margin-left: 2px;

      span {
        opacity: 0;
        animation: dotFade 1.4s infinite;
        display: inline-block;
        width: 4px;
        text-align: center;
        background-color: transparent;

        &:nth-child(1) {
          animation-delay: 0s;
        }

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }
  }

  /* 已完成状态样式 */
  &.cesium-completed {
    background-color: rgba(240, 255, 244, 0.8);
    border: 1px solid rgba(72, 187, 120, 0.2);
    border-left: 3px solid #48bb78;
    box-shadow: 0 2px 8px rgba(72, 187, 120, 0.1);

    &:hover {
      box-shadow: 0 4px 12px rgba(72, 187, 120, 0.15);
      background-color: rgba(240, 255, 244, 0.9);
    }

    .cesium-icon {
      stroke: #48bb78;
      animation: none;
    }

    .cesium-text {
      color: #2d3748;
    }
  }
}

@keyframes iconPulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}
</style>
