<!--
 * @file ForecastDispatchPlan/index.vue
 * @description 调配方案预览组件，负责展示调配方案列表
 * @features
 * - 展示调配方案列表
 * - 支持查看方案详情
 * - 显示方案状态
 * - 支持展开和折叠
 * - 列表为空时显示空状态
 -->

<template>
  <div class="forecast-dispatch-plan" :class="{ 'is-collapsed': isCollapsed }">
    <div class="plan-header" @click="toggleCollapse">
      <div class="header-icon">
        <Icon icon="mdi:file-document-outline" />
      </div>
      <div class="header-title">调配方案预览</div>
      <div class="header-toggle">
        <Icon :icon="isCollapsed ? 'mdi:chevron-down' : 'mdi:chevron-up'" />
      </div>
    </div>
    <div class="plan-list" v-show="!isCollapsed">
      <!-- 列表为空时显示的空状态 -->
      <div v-if="!list || list.length === 0" class="empty-state">
        <div class="empty-icon">
          <Icon icon="mdi:file-outline" />
        </div>
        <div class="empty-text">暂无调配方案</div>
      </div>

      <!-- 列表有数据时显示的内容 -->
      <div v-else v-for="item in list" :key="item.id" class="plan-item">
        <div class="plan-item-header">
          <div class="plan-name">{{ item.name }}</div>
        </div>
        <div class="plan-item-info">
          <div class="info-row">
            <span class="info-label">计算时间：</span>
            <span class="info-value">{{
              formatTimeRange(item.countStartTime, item.countEndTime)
            }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">预测开始：</span>
            <span class="info-value">{{
              formatDate(item.forecastBeginTime)
            }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">创建时间：</span>
            <span class="info-value">{{ formatDate(item.createTime) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">生成方式：</span>
            <span class="info-value">{{
              item.isAuto ? "自动生成" : "手动生成"
            }}</span>
          </div>
        </div>
        <div class="plan-item-actions">
          <button class="action-btn" @click.stop="viewPlanDetail(item)">
            <Icon icon="mdi:eye-outline" />
            <span>预览</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from "@iconify/vue";
import { ref } from "vue";

/**
 * @component 调配方案预览组件Props定义
 * @property {Array} list - 调配方案列表
 */
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});

// 折叠状态
const isCollapsed = ref(false);

/**
 * @function toggleCollapse
 * @description 切换折叠/展开状态
 */
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

/**
 * @function formatDate
 * @description 格式化日期
 * @param {String} dateString - 日期字符串
 * @returns {String} 格式化后的日期字符串
 */
const formatDate = (dateString) => {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

/**
 * @function formatTimeRange
 * @description 格式化时间范围，计算耗时
 * @param {String} startTime - 开始时间
 * @param {String} endTime - 结束时间
 * @returns {String} 格式化后的时间范围
 */
const formatTimeRange = (startTime, endTime) => {
  if (!startTime || !endTime) return "-";

  const start = new Date(startTime);
  const end = new Date(endTime);
  const duration = end - start;

  // 计算耗时（分钟和秒）
  const minutes = Math.floor(duration / 60000);
  const seconds = Math.floor((duration % 60000) / 1000);

  // 时间范围字符串
  const timeRange = `${start.toLocaleString("zh-CN", {
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  })} - ${end.toLocaleString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
  })}`;

  return `${timeRange} (耗时: ${minutes}分${seconds}秒)`;
};

/**
 * @function viewPlanDetail
 * @description 查看方案详情
 * @param {Object} item - 方案对象
 */
const viewPlanDetail = (item) => {
  console.log("查看方案详情:", item);
  window.EventBus.$emit("forecastDispatchPlan", item);
  window.EventBus.$emit("change/agent-scheme/show");
};
</script>

<style lang="scss" scoped>
.forecast-dispatch-plan {
  margin: 16px 0;
  border-radius: 10px;
  border: 1px solid #e8e8e8;
  background: #ffffff;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.25s ease;

  &:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  }

  &.is-collapsed {
    .plan-header {
      border-bottom: none;
    }

    .header-toggle .iconify {
      transform: rotate(0deg);
    }
  }

  .plan-header {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    background: #f8f8f8;
    border-bottom: 1px solid #eaeaea;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;

    &:hover {
      background: #f2f2f2;
    }

    .header-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      background: rgba(26, 115, 232, 0.08);
      color: #1a73e8;
      border-radius: 6px;
      margin-right: 12px;

      .iconify {
        font-size: 18px;
      }
    }

    .header-title {
      font-size: 15px;
      font-weight: 600;
      color: #333;
      flex: 1;
    }

    .header-toggle {
      color: #777;

      .iconify {
        font-size: 20px;
        transform: rotate(180deg);
        transition: transform 0.25s ease;
      }
    }
  }

  .plan-list {
    padding: 12px;
    max-height: 500px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ddd;
      border-radius: 4px;
    }

    // 空状态样式
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 30px 0;
      color: #8c8c8c;

      .empty-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: rgba(140, 140, 140, 0.08);
        border-radius: 50%;
        margin-bottom: 12px;

        .iconify {
          font-size: 24px;
        }
      }

      .empty-text {
        font-size: 14px;
      }
    }

    .plan-item {
      background: #f9fafc;
      border-radius: 8px;
      padding: 14px;
      margin-bottom: 10px;
      border: 1px solid #edf0f7;
      cursor: pointer;
      transition: all 0.2s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        background: #f4f7fc;
        transform: translateY(-2px);
      }

      .plan-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .plan-name {
          font-size: 14px;
          font-weight: 600;
          color: #1a73e8;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
        }
      }

      .plan-item-info {
        .info-row {
          display: flex;
          align-items: flex-start;
          margin-bottom: 6px;
          font-size: 13px;

          &:last-child {
            margin-bottom: 0;
          }

          .info-label {
            color: #666;
            min-width: 70px;
            flex-shrink: 0;
          }

          .info-value {
            color: #333;
            word-break: break-word;
          }
        }
      }

      .plan-item-actions {
        display: flex;
        justify-content: flex-end;

        .action-btn {
          display: flex;
          align-items: center;
          background: rgba(26, 115, 232, 0.08);
          color: #1a73e8;
          border: none;
          padding: 6px 12px;
          border-radius: 6px;
          font-size: 13px;
          cursor: pointer;
          transition: all 0.2s ease;

          .iconify {
            margin-right: 4px;
            font-size: 16px;
          }

          &:hover {
            background: rgba(26, 115, 232, 0.12);
          }

          &:active {
            transform: scale(0.98);
          }
        }
      }
    }
  }
}
</style>
