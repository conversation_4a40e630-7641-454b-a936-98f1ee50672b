<!--
 * @file ChatInput.vue
 * @description 聊天输入组件，支持文本输入和文件上传
 * @features
 * - 支持拖拽上传文件
 * - 支持粘贴上传图片
 * - 自适应文本框高度
 * - 支持快捷键发送消息（Enter）和换行（Shift + Enter）
 -->

<template>
  <!-- 输入区域容器 -->
  <div
    class="input-wrapper"
    @dragover.prevent="handleDragOver"
    @dragleave.prevent="handleDragLeave"
    @drop.prevent="handleDrop"
    :class="{ 'drag-over': isDragging && fileUploadConfig.enabled }"
  >
    <!-- 主要输入区域 -->
    <div class="chat-input">
      <!-- 文本输入框，支持自适应高度 -->
      <textarea
        v-model="inputValue"
        @keydown.enter="handleEnter"
        @paste="handlePaste"
        :placeholder="placeholder"
        rows="1"
        ref="textareaRef"
        class="chat-input-textarea"
      ></textarea>
      <!-- 操作按钮区域 -->
      <div class="input-actions">
        <!-- 文件上传按钮和输入框 -->
        <div class="upload-wrapper" v-if="fileUploadConfig.enabled">
          <input
            type="file"
            ref="fileInput"
            @change="handleFileChange"
            accept="image/*"
            multiple
            class="file-input"
          />
          <button
            @click="triggerFileUpload"
            class="action-btn upload-btn"
            :class="{ 'has-files': selectedFiles.length > 0 }"
            :title="
              '上传图片' +
              (selectedFiles.length
                ? ` (${selectedFiles.length}/${fileUploadConfig.number_limits})`
                : '')
            "
          >
            <Icon icon="mdi:image" />
            <span v-if="selectedFiles.length" class="file-count">{{
              selectedFiles.length
            }}</span>
          </button>
        </div>
        <!-- 停止生成按钮（仅在流式加载时显示） -->
        <button
          v-if="isStreamLoad"
          @click="$emit('stop')"
          class="action-btn stop-btn"
          title="停止生成"
        >
          <Icon icon="mdi:stop-circle-outline" />
        </button>
        <!-- 发送消息按钮 -->
        <button
          @click="handleSend"
          :disabled="
            isStreamLoad || (!inputValue.trim() && !selectedFiles.length)
          "
          class="action-btn send-btn"
          title="发送消息"
        >
          <Icon icon="mdi:send" />
        </button>
      </div>
    </div>

    <!-- 已选文件预览区域 -->
    <div v-if="selectedFiles.length > 0" class="selected-files">
      <div
        v-for="(file, index) in selectedFiles"
        :key="index"
        class="file-item"
      >
        <!-- 图片预览 -->
        <div
          class="file-preview"
          @click="showPreview(file)"
          :class="{ clickable: file.localUrl }"
        >
          <img :src="file.localUrl" :alt="file.name" v-if="file.localUrl" />
          <div class="file-icon" v-else>
            <Icon icon="mdi:image" />
          </div>
        </div>
        <div class="file-info">
          <p class="file-name">{{ file.name }}</p>
          <p class="file-details">
            <span>{{ formatFileSize(file.size) }}</span>
          </p>
        </div>
        <!-- 移除文件按钮 -->
        <button @click="removeFile(index)" class="remove-file">
          <Icon icon="mdi:close" />
        </button>
      </div>
    </div>

    <!-- 拖拽提示遮罩层 -->
    <div v-if="isDragging && fileUploadConfig.enabled" class="drag-overlay">
      <div class="drag-content">
        <Icon icon="mdi:cloud-upload" />
        <span>释放鼠标上传文件</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from "vue";
import { Icon } from "@iconify/vue";
import * as dify from "@/api/dify";
import { ElMessage } from "element-plus";
// 组件属性定义
const props = defineProps({
  isStreamLoad: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: "给 Deepseek 发送消息 (Shift + Enter 换行)",
  },
  fileUploadConfig: {
    type: Object,
    default: () => ({
      enabled: false,
      number_limits: 3,
      transfer_methods: [],
    }),
  },
  userId: {
    type: String,
    required: true,
  },
});

// 定义组件事件
const emit = defineEmits(["send", "stop"]);

const inputValue = ref(""); // 输入框的值
const textareaRef = ref(null); // 文本框引用
const fileInput = ref(null); // 文件输入框引用
const selectedFiles = ref([]); // 已选择的文件列表
const isDragging = ref(false); // 是否正在拖拽文件

// 添加本地预览URL的映射
const localPreviewUrls = ref(new Map());

// 图片预览状态
const isPreviewVisible = ref(false);
const previewImage = ref(null);

/**
 * 为上传的文件创建本地预览URL
 */
const createLocalPreviewUrl = (file) => {
  if (file instanceof File && file.type.startsWith("image/")) {
    const url = URL.createObjectURL(file);
    localPreviewUrls.value.set(file.name, url);
    return url;
  }
  return null;
};

/**
 * 清理本地预览URL
 */
const cleanupLocalPreviewUrl = (fileName) => {
  const url = localPreviewUrls.value.get(fileName);
  if (url) {
    URL.revokeObjectURL(url);
    localPreviewUrls.value.delete(fileName);
  }
};

/**
 * 自动调整文本框高度
 * 根据内容自动调整高度，最小高度47px，最大高度150px
 */
const adjustTextareaHeight = () => {
  const textarea = textareaRef.value;
  if (!textarea) return;

  textarea.style.height = "auto";
  let contentHeight = Math.max(textarea.scrollHeight, 47);
  textarea.style.height = contentHeight <= 150 ? `${contentHeight}px` : "150px";
};

// 监听输入值变化，调整文本框高度
watch(inputValue, () => {
  nextTick(adjustTextareaHeight);
});

/**
 * 处理回车键事件
 * Shift + Enter: 换行
 * Enter: 发送消息
 */
const handleEnter = (e) => {
  if (!e.shiftKey) {
    e.preventDefault();
    handleSend();
  }
};

// 拖拽相关事件处理
const handleDragOver = (e) => {
  e.preventDefault();
  isDragging.value = true;
  e.dataTransfer.dropEffect = "copy";
};

const handleDragLeave = (e) => {
  // 确保只在真正离开容器时触发
  if (
    e.target === e.currentTarget ||
    !e.currentTarget.contains(e.relatedTarget)
  ) {
    isDragging.value = false;
  }
};

/**
 * 处理文件拖放
 * 支持图片、视频、音频、PDF、Office文档等格式
 */
const handleDrop = async (e) => {
  e.preventDefault();
  isDragging.value = false;

  if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
    const files = Array.from(e.dataTransfer.files);
    const validFiles = files.filter(validateFile);

    if (validFiles.length === 0) {
      ElMessage.error(
        "请选择有效的图片文件（支持png、jpg、jpeg、webp、gif格式，大小不超过10MB）"
      );
      return;
    }

    // 检查文件数量限制
    if (
      selectedFiles.value.length + validFiles.length >
      props.fileUploadConfig.number_limits
    ) {
      ElMessage.error(
        `最多只能上传${props.fileUploadConfig.number_limits}个文件`
      );
      return;
    }

    // 上传文件到服务器
    try {
      for (const file of validFiles) {
        const uploadedFile = await uploadFile(file);
        selectedFiles.value.push(uploadedFile);
      }
    } catch (error) {
      console.error("文件上传失败:", error);
      ElMessage.error("文件上传失败，请重试");
    }
  }
};

/**
 * 处理粘贴事件
 * 支持粘贴图片，自动生成文件名
 */
const handlePaste = async (e) => {
  if (!props.fileUploadConfig.enabled) return;
  const items = e.clipboardData.items;
  const files = [];

  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    if (item.kind === "file") {
      const file = item.getAsFile();
      if (file && validateFile(file)) {
        // 为图片生成唯一文件名
        const timestamp = new Date().getTime();
        const newFile = new File([file], `pasted_image_${timestamp}.png`, {
          type: file.type,
        });
        files.push(newFile);
      }
    }
  }

  if (files.length > 0) {
    e.preventDefault();

    // 检查文件数量限制
    if (
      selectedFiles.value.length + files.length >
      props.fileUploadConfig.number_limits
    ) {
      ElMessage.error(
        `最多只能上传${props.fileUploadConfig.number_limits}个文件`
      );
      return;
    }

    // 上传文件到服务器
    try {
      for (const file of files) {
        const uploadedFile = await uploadFile(file);

        selectedFiles.value.push(uploadedFile);
      }
    } catch (error) {
      console.error("文件上传失败:", error);
      ElMessage.error("文件上传失败，请重试");
    }
  }
};

// 文件上传相关方法
const triggerFileUpload = () => {
  fileInput.value.click();
};

// 添加文件验证和处理的工具函数
const validateFile = (file) => {
  // 检查文件类型
  const supportedTypes = [
    "image/png",
    "image/jpeg",
    "image/jpg",
    "image/webp",
    "image/gif",
  ];
  if (!supportedTypes.includes(file.type.toLowerCase())) {
    console.error("不支持的文件类型:", file.type);
    return false;
  }

  // 检查文件大小 (10MB)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    console.error("文件大小超过限制");
    return false;
  }

  return true;
};

const formatFileSize = (size) => {
  if (size < 1024) {
    return `${size}B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)}KB`;
  }
};

/**
 * 处理文件上传
 * @param {File} file 要上传的文件
 * @returns {Promise<Object>} 上传后的文件信息
 */
const uploadFile = async (file) => {
  try {
    const response = await dify.uploadFile(file, props.userId);
    if (!response.ok) {
      throw new Error(`上传失败: ${response.status} ${response.statusText}`);
    }
    const fileData = await response.json();

    // 创建本地预览URL
    const previewUrl = createLocalPreviewUrl(file);

    return {
      ...fileData,
      localUrl: previewUrl, // 添加本地预览URL
    };
  } catch (error) {
    console.error("文件上传请求失败:", error);
    throw error;
  }
};

/**
 * 处理文件变更
 */
const handleFileChange = async (event) => {
  const files = Array.from(event.target.files);
  const validFiles = files.filter(validateFile);

  if (validFiles.length === 0) {
    ElMessage.error(
      "请选择有效的图片文件（支持png、jpg、jpeg、webp、gif格式，大小不超过10MB）"
    );
    return;
  }

  if (
    selectedFiles.value.length + validFiles.length >
    props.fileUploadConfig.number_limits
  ) {
    ElMessage.error(
      `最多只能上传${props.fileUploadConfig.number_limits}个文件`
    );
    return;
  }

  try {
    for (const file of validFiles) {
      const uploadedFile = await uploadFile(file);
      selectedFiles.value.push(uploadedFile);
    }
  } catch (error) {
    console.error("文件上传失败:", error);
    ElMessage.error("文件上传失败，请重试");
  }

  // 清空input，允许重复选择相同文件
  event.target.value = null;
};

/**
 * 移除文件
 */
const removeFile = (index) => {
  const file = selectedFiles.value[index];
  if (file.localUrl) {
    cleanupLocalPreviewUrl(file.name);
  }
  selectedFiles.value.splice(index, 1);
};

/**
 * 获取文件类型对应的图标
 * 根据文件MIME类型返回对应的Material Design图标
 */
const getFileIcon = (fileType) => {
  // 如果没有文件类型，默认返回文档图标
  if (!fileType) return "mdi:file-document";

  if (fileType.startsWith("image/")) return "mdi:image";
  if (fileType.startsWith("video/")) return "mdi:video";
  if (fileType.startsWith("audio/")) return "mdi:music";
  if (fileType.includes("pdf")) return "mdi:file-pdf";
  if (fileType.includes("word")) return "mdi:file-word";
  if (fileType.includes("excel") || fileType.includes("sheet"))
    return "mdi:file-excel";
  if (fileType.includes("powerpoint") || fileType.includes("presentation"))
    return "mdi:file-powerpoint";
  return "mdi:file-document";
};

/**
 * 处理消息发送
 */
const handleSend = async () => {
  if (
    props.isStreamLoad ||
    (!inputValue.value.trim() && !selectedFiles.value.length)
  )
    return;

  const messageText = inputValue.value;
  const files = selectedFiles.value.map((file) => ({
    type: "image",
    transfer_method: "local_file",
    upload_file_id: file.id,
    ...file,
  }));

  emit("send", {
    prompt: messageText,
    files: files,
  });

  // 清空输入框和文件
  inputValue.value = "";
  selectedFiles.value = [];

  // 重置文本框的高度
  const textarea = textareaRef.value;
  if (textarea) {
    textarea.style.height = "80px";
  }
};

// 显示预览
const showPreview = (file) => {
  if (file.localUrl) {
    previewImage.value = file;
    isPreviewVisible.value = true;
  }
};

// 暴露方法给父组件
defineExpose({
  setInputValue: (value) => {
    inputValue.value = value;
  },
});
</script>

<style lang="scss" scoped>
$primary-color: #0052d9;
$error-color: #f44336;
$border-color: #e0e0e0;
$text-primary: #333;
$text-secondary: #666;
$text-placeholder: #aab0b7;
$bg-primary: #f8f9fa;
$bg-white: #fff;
$shadow-light: rgba(0, 0, 0, 0.05);
$shadow-medium: rgba(0, 0, 0, 0.1);

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin button-reset {
  border: none;
  background: transparent;
  cursor: pointer;
}

.input-wrapper {
  position: relative;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 拖拽状态样式 */
  &.drag-over {
    background-color: rgba(26, 115, 232, 0.03);
    border: 2px dashed rgba(26, 115, 232, 0.3);
    border-radius: 16px;
    padding: 19px;

    &::after {
      content: "拖放文件到此处上传";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #1a73e8;
      font-weight: 500;
      background-color: rgba(255, 255, 255, 0.95);
      padding: 12px 24px;
      border-radius: 24px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      z-index: 10;
      backdrop-filter: blur(4px);
    }

    .chat-input {
      opacity: 0.5;
      transform: scale(0.99);
    }
  }

  /* 聊天输入框容器样式 */
  .chat-input {
    position: relative;
    display: flex;
    align-items: flex-end;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 20px;
    background-color: rgba(245, 248, 255, 0.6);
    padding: 14px 18px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
    backdrop-filter: blur(8px);

    &:focus-within {
      border-color: rgba(26, 115, 232, 0.2);
      background-color: rgba(255, 255, 255, 0.95);
      box-shadow: 0 4px 16px rgba(26, 115, 232, 0.08);
      transform: translateY(-1px);
    }

    /* 文本输入框样式 */
    textarea {
      flex: 1;
      border: none;
      background: transparent;
      outline: none;
      resize: none;
      padding: 0;
      margin-bottom: 0;
      line-height: 1.6;
      font-size: 15px;
      max-height: 150px;
      min-height: 47px;
      color: #2c3e50;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
        Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;

      &::placeholder {
        color: #94a3b8;
        opacity: 0.8;
      }

      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 2px;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.2);
        }
      }

      /* Firefox 滚动条样式 */
      scrollbar-width: thin;
      scrollbar-color: rgba(0, 0, 0, 0.1) transparent;

      &:hover {
        scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
      }
    }

    /* 操作按钮区域样式 */
    .input-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-left: 8px;
      height: 36px;

      /* 文件上传按钮样式 */
      .upload-wrapper {
        position: relative;

        .file-input {
          display: none;
        }

        .upload-btn {
          color: #64748b;
          background-color: rgba(241, 245, 249, 0.8);
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

          &:hover {
            color: #1a73e8;
            background-color: rgba(26, 115, 232, 0.1);
            transform: translateY(-1px);
          }

          &.has-files {
            color: #1a73e8;
            background-color: rgba(26, 115, 232, 0.1);
          }

          .file-count {
            position: absolute;
            top: -2px;
            right: -2px;
            min-width: 16px;
            height: 16px;
            padding: 0 4px;
            border-radius: 8px;
            background-color: #1a73e8;
            color: #fff;
            font-size: 10px;
            line-height: 16px;
            text-align: center;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(26, 115, 232, 0.2);
          }
        }
      }

      /* 停止生成按钮样式 */
      .stop-btn {
        color: #fff;
        background-color: #ef4444;
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);

        &:hover {
          background-color: #dc2626;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(239, 68, 68, 0.25);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
        }
      }

      /* 发送按钮样式 */
      .send-btn {
        color: #fff;
        background-color: #1a73e8;
        box-shadow: 0 2px 4px rgba(26, 115, 232, 0.2);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          background-color: #1557b0;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(26, 115, 232, 0.25);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 4px rgba(26, 115, 232, 0.2);
        }

        &:disabled {
          background-color: #e2e8f0;
          color: #94a3b8;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }
      }

      /* 操作按钮通用样式 */
      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 12px;
        border: none;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

        /* 图标样式 */
        .iconify {
          font-size: 20px;
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }

  /* 已选文件预览区域样式 */
  .selected-files {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 16px;
    padding: 0 4px;

    .file-item {
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      background-color: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(8px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        .file-preview img {
          transform: scale(1.05);
        }
      }

      .file-preview {
        width: 80px;
        height: 80px;
        overflow: hidden;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }

      .remove-file {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 22px;
        height: 22px;
        border-radius: 6px;
        background-color: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(4px);
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        opacity: 0;
        transform: scale(0.8);

        .iconify {
          color: #fff;
          font-size: 14px;
        }

        &:hover {
          background-color: rgba(239, 68, 68, 0.8);
          transform: scale(1.1);
        }
      }

      &:hover .remove-file {
        opacity: 1;
        transform: scale(1);
      }
    }
  }

  .drag-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    border-radius: 16px;
    border: 2px dashed rgba(26, 115, 232, 0.3);

    .drag-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      color: #1a73e8;
      transform: translateY(-8px);

      .iconify {
        font-size: 48px;
        animation: float 2s ease-in-out infinite;
      }

      span {
        font-size: 16px;
        font-weight: 500;
        background: linear-gradient(120deg, #1a73e8, #1557b0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-12px);
    }
  }
}
</style>
