<template>
  <!-- AI思考过程展示区域 -->
  <div v-if="isThinking || reasoningContent" class="reasoning-content">
    <div class="reasoning-header" @click="toggleReasoning">
      <Icon icon="mdi:brain" class="reasoning-icon" />
      <span class="reasoning-status">{{ getThinkingStatus }}</span>
      <Icon
        :icon="
          isReasoningCollapsed
            ? 'mdi:chevron-double-down'
            : 'mdi:chevron-double-up'
        "
        class="collapse-icon"
        :class="{ 'is-collapsed': isReasoningCollapsed }"
      />
    </div>
    <!-- 思考过程Markdown内容 -->
    <div
      v-show="!isReasoningCollapsed"
      ref="markdownContent"
      class="markdown-content markdown-body"
      v-html="md.render(reasoningContent)"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { Icon } from "@iconify/vue";
import MarkdownIt from "markdown-it"; // Markdown渲染器
import hljs from "highlight.js"; // 代码高亮
import "highlight.js/styles/github.css"; // 代码高亮样式

defineOptions({
  name: "ChatReasoning",
});

const props = defineProps({
  message: {
    type: Object,
    required: () => {},
  },
});

/**
 * Markdown渲染器配置
 * 支持HTML、换行、链接自动识别、排版优化
 * 自定义代码高亮，支持ECharts图表渲染
 */
const md = new MarkdownIt({
  html: true, // 允许HTML标签
  breaks: true, // 转换换行符为 <br>
  linkify: true, // 自动识别链接
  typographer: true, // 启用排版优化
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value;
      } catch (__) {}
    }
    return ""; // use external default escaping
  },
});

/**
 * 思考过程展示控制
 */
const isReasoningCollapsed = ref(false); // 思考过程折叠状态

// 切换思考过程显示/隐藏
const toggleReasoning = () => {
  isReasoningCollapsed.value = !isReasoningCollapsed.value;
};

const isThinking = computed(() => {
  if (!props.message.content) return false;
  return (
    props.message.content.includes("<think>") &&
    !props.message.content.includes("</think>")
  );
});

// 添加新的计算属性来处理思考内容
const reasoningContent = computed(() => {
  if (props.message.reasoning_content) {
    return props.message.reasoning_content;
  }

  if (!props.message.content) {
    return "";
  }

  // 处理完整的思考标签
  const thinkMatch = props.message.content.match(/<think>([\s\S]*?)<\/think>/);
  if (thinkMatch) {
    return thinkMatch[1].trim();
  }

  // 处理未结束的思考标签
  if (props.message.content.includes("<think>")) {
    return props.message.content.split("<think>")[1].trim();
  }

  return "";
});

const getThinkingStatus = computed(() => {
  // 如果是通过 think 标签生成的
  if (props.message.content?.includes("<think>")) {
    return isThinking.value ? "正在深度思考" : "已深度思考";
  }
  // 如果是直接设置的 reasoning_content
  return props.message.content ? "已深度思考" : "正在深度思考";
});

const markdownContent = ref(null);

// 滚动到底部方法
const scrollToBottom = async () => {
  await nextTick();
  if (markdownContent.value) {
    const element = markdownContent.value;
    // 只有当内容高度超过容器高度，且不在底部时才需要滚动
    const isNotAtBottom =
      element.scrollHeight - element.scrollTop - element.clientHeight > 10;
    if (isNotAtBottom) {
      element.scrollTop = element.scrollHeight;
    }
  }
};

// 使用防抖优化滚动操作
let scrollDebounceTimer = null;
const debouncedScrollToBottom = () => {
  if (scrollDebounceTimer) clearTimeout(scrollDebounceTimer);
  scrollDebounceTimer = setTimeout(() => {
    scrollToBottom();
  }, 100);
};

// 监听内容变化，使用防抖优化
watch(reasoningContent, (newVal, oldVal) => {
  // 只在有实际内容变化时触发滚动
  if (newVal !== oldVal && newVal && !isReasoningCollapsed.value) {
    debouncedScrollToBottom();
  }
});

// 监听折叠状态变化
watch(isReasoningCollapsed, (newVal) => {
  // 展开时，如果有内容则滚动到底部
  if (!newVal && reasoningContent.value) {
    debouncedScrollToBottom();
  }
});
</script>

<style lang="scss" scoped>
$primary-color: rgba(0, 82, 217, 1);
$primary-light: rgba($primary-color, 0.03);
$primary-border: rgba($primary-color, 0.5);
$primary-text: rgba($primary-color, 0.75);
$primary-icon: rgba($primary-color, 0.85);
$transition-default: all 0.3s ease;
$transition-quick: all 0.2s ease;

.reasoning-content {
  margin-bottom: 16px;
  padding: 16px;
  background: $primary-light;
  border-radius: 12px;
  border-left: 3px solid $primary-border;
  transition: $transition-default;
  box-shadow: 0 2px 8px rgba($primary-color, 0.05);

  .markdown-content {
    font-size: 13px;
    color: #454545;
    background-color: transparent;
    line-height: 1.6;
    padding-top: 8px;
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.05) transparent;
  }
  .reasoning-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: $primary-text;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    user-select: none;
    padding: 4px;
    border-radius: 6px;
    transition: $transition-quick;

    &:hover {
      background: rgba($primary-color, 0.08);

      .collapse-icon {
        color: $primary-icon;
      }
    }

    .reasoning-icon {
      font-size: 18px;
      color: $primary-icon;
    }

    .reasoning-status {
      font-size: 13px;
      font-weight: 500;
      color: $primary-text;
      margin-left: 4px;
    }

    .collapse-icon {
      margin-left: auto;
      font-size: 18px;
      color: rgba($primary-color, 0.5);
      transition: $transition-default;

      &.is-collapsed {
        transform: translateY(-1px);
      }
    }
  }
}
</style>
