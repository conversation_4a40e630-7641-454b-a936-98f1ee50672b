<template>
  <!-- AI思考过程展示区域 -->
  <div v-if="isThinking || reasoningContent" class="reasoning-content">
    <div class="reasoning-header" @click="toggleReasoning">
      <Icon icon="mdi:brain" class="reasoning-icon" />
      <span class="reasoning-status">{{ getThinkingStatus }}</span>
      <Icon
        :icon="isReasoningCollapsed ? 'mdi:chevron-down' : 'mdi:chevron-up'"
        class="collapse-icon"
        :class="{ 'is-collapsed': isReasoningCollapsed }"
      />
    </div>
    <!-- 收起状态显示最新内容 -->
    <div
      v-if="isReasoningCollapsed && !isThinkingEnd"
      class="latest-content-wrapper"
      ref="scrollContainerRef"
    >
      <div class="latest-content">
        <div
          v-for="(line, index) in recentLines"
          :key="index"
          class="content-line"
          :class="{
            'is-entering': isLineEntering(index),
          }"
        >
          {{ line }}
        </div>
      </div>
      <!-- 渐变遮罩 -->
      <div class="fade-mask fade-mask-top"></div>
      <div class="fade-mask fade-mask-bottom"></div>
    </div>
    <!-- 展开状态显示完整Markdown内容 -->
    <div
      v-show="!isReasoningCollapsed"
      class="markdown-content markdown-body"
      v-html="md.render(reasoningContent)"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { Icon } from "@iconify/vue";
import MarkdownIt from "markdown-it";
import hljs from "highlight.js";
import "highlight.js/styles/github.css";

defineOptions({
  name: "ChatReasoningGrok",
});

const props = defineProps({
  message: {
    type: Object,
    required: () => {},
  },
});

const md = new MarkdownIt({
  html: true,
  breaks: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value;
      } catch (__) {}
    }
    return "";
  },
});

const isReasoningCollapsed = ref(true);
const scrollContainerRef = ref(null);
const lastContentLength = ref(0);
const enteringLines = ref(new Set());

const toggleReasoning = () => {
  isReasoningCollapsed.value = !isReasoningCollapsed.value;
};

const isThinking = computed(() => {
  if (!props.message.content) return false;
  return (
    props.message.content.includes("<think>") &&
    !props.message.content.includes("</think>")
  );
});

const reasoningContent = computed(() => {
  if (props.message.reasoning_content) {
    return props.message.reasoning_content;
  }

  if (!props.message.content) {
    return "";
  }

  const thinkMatch = props.message.content.match(/<think>([\s\S]*?)<\/think>/);
  if (thinkMatch) {
    return thinkMatch[1].trim();
  }

  if (props.message.content.includes("<think>")) {
    return props.message.content.split("<think>")[1].trim();
  }

  return "";
});

// 获取最近的几行内容
const recentLines = computed(() => {
  const content = reasoningContent.value;
  if (!content) return [];

  const lines = content.split("\n").filter((line) => line.trim());
  return lines.slice(-4);
});

const isLineEntering = (index) => {
  return enteringLines.value.has(index);
};

const getThinkingStatus = computed(() => {
  if (props.message.content?.includes("<think>")) {
    return isThinking.value ? "正在深度思考" : "已深度思考";
  }
  return props.message.content ? "已深度思考" : "正在深度思考";
});

// 是否思考结束 返回true 或者 false
const isThinkingEnd = computed(() => {
  return getThinkingStatus.value === "已深度思考";
});

// 监听内容变化，处理动画和滚动
watch(
  () => reasoningContent.value,
  async (newContent) => {
    if (!isReasoningCollapsed.value || !scrollContainerRef.value) return;

    const newLines = newContent.split("\n").filter((line) => line.trim());
    const newLength = newLines.length;

    // 标记新行为正在进入
    if (newLength > lastContentLength.value) {
      const newLineIndices = new Set();
      for (let i = lastContentLength.value; i < newLength; i++) {
        const recentIndex = i % 4; // 因为我们只显示最后4行
        newLineIndices.add(recentIndex);
      }
      enteringLines.value = newLineIndices;

      // 300ms后清除entering标记
      setTimeout(() => {
        enteringLines.value = new Set();
      }, 300);
    }

    lastContentLength.value = newLength;

    // 平滑滚动到底部
    await nextTick();
    const container = scrollContainerRef.value;
    const scrollHeight = container.scrollHeight;
    const startPosition = container.scrollTop;
    const distance = scrollHeight - startPosition - container.clientHeight;

    if (distance <= 0) return;

    const duration = 300;
    const startTime = performance.now();

    function animate(currentTime) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // easeOutQuart 缓动函数
      const easing = 1 - Math.pow(1 - progress, 4);

      container.scrollTop = startPosition + distance * easing;

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    }

    requestAnimationFrame(animate);
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
// 颜色变量
$color-base: rgb(28, 28, 35);
$color-bg: rgba($color-base, 0.01);
$color-border: rgba($color-base, 0.04);
$color-text-primary: rgba($color-base, 0.85);
$color-text-secondary: rgba($color-base, 0.65);
$color-text-tertiary: rgba($color-base, 0.4);

// 动画变量
$transition-default: all 0.3s ease;
$transition-quick: all 0.2s ease;
$bezier-smooth: cubic-bezier(0.4, 0, 0.2, 1);

// Mixins
@mixin fade-gradient($direction) {
  background: linear-gradient(
    to #{$direction},
    rgba($color-base, 0.02) 0%,
    rgba($color-base, 0.01) 40%,
    transparent 100%
  );
}

.reasoning-content {
  margin-bottom: 16px;
  padding: 16px;
  background: $color-bg;
  border-radius: 12px;
  border: 1px solid $color-border;
  transition: $transition-default;
}

.reasoning-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: $color-text-secondary;
  font-weight: 500;
  font-size: 13px;
  cursor: pointer;
  user-select: none;
  padding: 6px 10px;
  border-radius: 8px;
  transition: $transition-quick;

  &:hover {
    background: rgba($color-base, 0.02);

    .collapse-icon {
      color: $color-text-secondary;
    }
  }
}

.reasoning-icon {
  font-size: 16px;
  color: rgba($color-base, 0.75);
  animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.9;
  }
  100% {
    opacity: 0.6;
  }
}

.reasoning-status {
  font-size: 13px;
  font-weight: 500;
  color: $color-text-secondary;
  letter-spacing: 0.2px;
}

.collapse-icon {
  margin-left: auto;
  font-size: 16px;
  color: $color-text-tertiary;
  transition: $transition-default;
}

.latest-content-wrapper {
  max-height: 140px;
  overflow: hidden;
  position: relative;
  padding: 12px 0;
  border-radius: 8px;
}

.latest-content {
  position: relative;
  z-index: 1;
}

.content-line {
  padding: 6px 12px;
  color: $color-text-secondary;
  font-size: 13px;
  line-height: 1.6;
  letter-spacing: 0.2px;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.4s $bezier-smooth;
  position: relative;

  &:not(:last-child) {
    filter: blur(0.8px);
    opacity: 0.5;
    transform: scale(0.99) translateY(0);
    transition: all 0.4s $bezier-smooth;
  }

  &:last-child {
    opacity: 1;
    filter: blur(0);
    transform: scale(1);
    color: $color-text-primary;
    font-weight: 500;
  }

  &.is-entering {
    animation: slideIn 0.4s $bezier-smooth forwards;
  }
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(16px) scale(0.98);
    filter: blur(4px);
  }
  60% {
    opacity: 0.6;
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* 渐变遮罩 */
.fade-mask {
  position: absolute;
  left: 0;
  right: 0;
  height: 32px;
  pointer-events: none;
  z-index: 2;

  &-top {
    top: 0;
    @include fade-gradient(bottom);
  }

  &-bottom {
    bottom: 0;
    @include fade-gradient(top);
  }
}

.markdown-content {
  font-size: 13px;
  color: rgba($color-base, 0.75);
  line-height: 1.6;
  padding-top: 8px;
  letter-spacing: 0.2px;
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.05) transparent;
  position: relative;
}
</style>
