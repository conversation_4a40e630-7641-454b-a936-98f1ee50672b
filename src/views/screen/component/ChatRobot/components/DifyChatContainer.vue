<!--
 * @file DifyChatContainer.vue
 * @description Dify 聊天容器组件，负责展示聊天界面和处理消息交互
 * @features
 * - 支持消息流式输出
 * - 支持聊天上下文管理
 * - 支持示例问题快速提问
 * - 支持文件上传和图片显示
 * - 支持语音转文本
 * - 支持引用和标注功能
 * - 支持自定义用户输入表单
 -->

<template>
  <div class="dify-chat-container">
    <!-- 消息列表区域 - 包含聊天记录和欢迎界面 -->
    <div class="messages-wrapper">
      <div class="chat-messages" ref="messagesRef">
        <!-- 开场白 -->
        <div
          v-if="chatList.length === 0 && openingStatement"
          class="opening-statement"
        >
          <div class="opening-content">
            <div v-if="appLoading" class="loading-container">
              <el-skeleton :rows="3" animated />
            </div>
            <div v-else class="opening-message">{{ openingStatement }}</div>
          </div>
          <div
            v-if="
              suggestedQuestions && suggestedQuestions.length > 0 && !appLoading
            "
            class="suggested-questions"
          >
            <div class="suggestions-label">您可以尝试以下示例问题：</div>
            <div class="question-list">
              <button
                v-for="(question, idx) in suggestedQuestions"
                :key="idx"
                class="question-item"
                @click="handleExampleClick(question)"
              >
                {{ question }}
              </button>
            </div>
          </div>
        </div>

        <!-- 消息列表 - 展示用户和AI的对话内容 -->
        <DIfyChatMessage
          v-for="(message, index) in chatList"
          :key="index"
          :message="message"
          :index="index"
          :isLastMessage="index === chatList.length - 1"
          :loading="loading"
          :workflowLoading="workflowLoading"
          :isStreamLoad="isStreamLoad"
          :showReferences="retrieverResource.enabled"
          :showAnnotations="annotationReply.enabled"
          @operation="handleOperation"
        />
        <!-- 开始新对话按钮 - 清空当前对话记录 -->
        <div class="new-chat-wrapper" v-if="chatList.length > 0">
          <button @click="startNewChat" class="start-new-chat">
            <Icon icon="mdi:plus-circle" />
            <span>开始新对话</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 输入区域 - 包含文本输入、语音输入和文件上传功能 -->
    <ChatInput
      @send="handleData"
      @stop="onStop"
      :isStreamLoad="isStreamLoad"
      :fileUploadConfig="fileUpload"
      :userId="userId"
      placeholder="发送消息进行聊天 (Shift + Enter 换行)"
    />
  </div>
</template>

<script setup>
import { ref, nextTick } from "vue";
import DIfyChatMessage from "./DIfyChatMessage.vue";
import ChatInput from "./DIfyChatInput.vue";
import { Icon } from "@iconify/vue";
import { v4 as uuidv4 } from "uuid";
import { DifyClient } from "@/api/dify";
import { ElMessage } from "element-plus";

const difyClient = new DifyClient();

const initDifyClient = (apiKey) => {
  difyClient.init(apiKey);
};

// 状态管理 - 核心状态变量
const chatList = ref([]); // 聊天消息列表
const messagesRef = ref(null); // 消息容器DOM引用
const loading = ref(false); // 加载状态标识
const workflowLoading = ref(false); // 工作流加载状态标识
const isStreamLoad = ref(false); // 流式加载状态标识
const fetchCancel = ref(null); // 当前请求的取消控制器
const appLoading = ref(false); // 应用参数加载状态标识

// 会话管理 - 会话相关状态
const currentConversationId = ref(""); // 当前会话ID
const userId = ref(uuidv4()); // 用户唯一标识
const currentTaskId = ref(""); // 当前任务ID
const appParameters = ref(null); // 应用配置参数

// 功能配置 - 各项功能的开关和配置
const openingStatement = ref(""); // 开场白文本
const suggestedQuestions = ref([]); // 推荐问题列表
const suggestedQuestionsAfterAnswer = ref({ enabled: false }); // 回答后的推荐问题配置
const speechToText = ref({ enabled: false }); // 语音转文本功能配置
const retrieverResource = ref({ enabled: false }); // 引用资源功能配置
const annotationReply = ref({ enabled: false }); // 标注回复功能配置
const userInputForm = ref([]); // 用户输入表单配置
const fileUpload = ref(); // 文件上传功能配置

/**
 * 滚动到消息列表底部
 * @description 在新消息添加后自动滚动到最新消息位置
 */
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesRef.value) {
      messagesRef.value.scrollTop = messagesRef.value.scrollHeight;
    }
  });
};

/**
 * 处理用户发送的数据
 * @description 处理用户输入的文本和文件，发送到Dify API并处理响应
 * @param {Object} data - 用户发送的数据对象
 * @param {string} data.prompt - 用户输入的文本内容
 * @param {Array} data.files - 用户上传的文件列表
 */
const handleData = async ({ prompt, files = [] }) => {
  loading.value = true;
  isStreamLoad.value = true;

  // 添加用户消息到聊天列表
  chatList.value.push({
    role: "user",
    content: prompt,
    files: files,
    name: "我",
    avatar: "mdi:account",
    datetime: Date.now(),
  });

  // 添加AI助手的初始响应
  chatList.value.push({
    role: "assistant",
    content: "",
    reasoning_content: "",
    name: "防洪四预助手",
    avatar: "mdi:robot",
    datetime: Date.now() + 1,
  });

  const lastIndex = chatList.value.length - 1;
  let lastItem = chatList.value[lastIndex];

  try {
    // 创建用于取消请求的控制器
    const controller = new AbortController();
    fetchCancel.value = controller;

    // 调用Dify API发送消息并处理流式响应
    await difyClient.sendChatMessage(
      {
        query: prompt,
        user: userId.value,
        response_mode: "streaming",
        files: files, // 文件上传功能将在后续实现
        inputs: {},
        conversation_id: currentConversationId.value,
      },
      {
        stream: true,
        controller,
        onMessage: (chunk) => {
          loading.value = false;

          // 处理非JSON格式的消息
          if (typeof chunk === "string") {
            try {
              chunk = JSON.parse(chunk);
            } catch (e) {
              console.warn("消息块不是有效的JSON:", chunk);
              // 过滤系统消息
              if (
                chunk.trim().startsWith("event:") ||
                chunk.trim().startsWith("data:") ||
                chunk.includes("[DONE]") ||
                chunk.includes("ping")
              ) {
                console.log("过滤掉非内容消息:", chunk);
                return;
              }
            }
          }

          // 更新会话和任务ID
          if (chunk.conversation_id && !currentConversationId.value) {
            currentConversationId.value = chunk.conversation_id;
          }
          if (chunk.task_id) {
            currentTaskId.value = chunk.task_id;
          }

          // 处理不同类型的事件消息
          if (chunk.event) {
            switch (chunk.event) {
              case "message":
                workflowLoading.value = false;
                // 处理普通文本消息
                if (chunk.answer !== undefined) {
                  lastItem.content += chunk.answer;
                  scrollToBottom();
                } else if (chunk.data && chunk.data.answer) {
                  lastItem.content += chunk.data.answer;
                  scrollToBottom();
                }
                break;
              case "agent_message":
                // 处理Agent模式的文本消息
                if (chunk.answer !== undefined) {
                  lastItem.content += chunk.answer;
                  scrollToBottom();
                } else if (chunk.data && chunk.data.answer) {
                  lastItem.content += chunk.data.answer;
                  scrollToBottom();
                }
                break;
              case "message_end":
                // 处理消息结束事件
                console.log("消息传输完成", chunk.metadata);
                isStreamLoad.value = false;
                if (chunk.metadata && chunk.metadata.retriever_resources) {
                  console.log("引用资源:", chunk.metadata.retriever_resources);
                  // 保存引用资源到最后一条消息
                  if (lastItem) {
                    lastItem.retriever_resources =
                      chunk.metadata.retriever_resources;
                  }
                }
                currentTaskId.value = "";
                break;
              case "node_started":
                // 处理工作流开始事件
                console.log("工作流开始:", chunk);
                workflowLoading.value = true;
                break;
              case "node_finished":
                // 处理工作流结束事件
                console.log("工作流结束:", chunk);
                workflowLoading.value = false;
                break;
              case "message_file":
                // 处理文件消息，主要是图片
                console.log("收到文件消息:", chunk);
                if (
                  chunk.url &&
                  chunk.type === "image" &&
                  chunk.belongs_to === "assistant"
                ) {
                  if (!lastItem.files) {
                    lastItem.files = [];
                  }
                  lastItem.files.push({
                    id: chunk.id,
                    type: chunk.type,
                    url: chunk.url,
                  });
                }
                break;
              case "agent_thought":
                // 处理Agent思考过程
                console.log("Agent思考过程:", chunk);
                if (chunk.thought) {
                  if (!lastItem.reasoning_content) {
                    lastItem.reasoning_content = "";
                  }
                  lastItem.reasoning_content += chunk.thought + "\n";
                }
                break;
              case "message_replace":
                // 处理内容审查替换
                console.log("内容被替换:", chunk);
                if (chunk.answer !== undefined) {
                  lastItem.content = chunk.answer;
                  scrollToBottom();
                }
                break;
              case "error":
                // 处理错误事件
                console.error("接收到错误:", chunk);
                lastItem.content = chunk.message || "请求失败，请稍后重试";
                isStreamLoad.value = false;
                currentTaskId.value = "";
                break;
              case "ping":
                // 处理保活ping
                console.log("收到ping");
                break;
              default:
                // 处理未知类型的事件
                console.log("未处理的事件类型:", chunk.event, chunk);
                if (chunk.data) {
                  const textContent =
                    typeof chunk.data === "string"
                      ? chunk.data
                      : chunk.data.answer ||
                        chunk.data.content ||
                        chunk.data.text ||
                        "";
                  if (textContent) {
                    lastItem.content += textContent;
                    scrollToBottom();
                  }
                }
            }
          } else {
            // 处理非标准格式的消息
            if (chunk.content) {
              lastItem.content += chunk.content;
              scrollToBottom();
            } else if (chunk.text) {
              lastItem.content += chunk.text;
              scrollToBottom();
            } else if (typeof chunk === "object") {
              const textContent = JSON.stringify(chunk);
              console.log("未识别的消息格式:", textContent);
              if (textContent !== "{}" && textContent !== "null") {
                lastItem.content += `[收到未识别格式数据]`;
              }
            }
          }
        },
      }
    );
  } catch (error) {
    console.error(error);
    if (error.name === "AbortError") {
      // 处理用户主动取消的请求
    } else {
      // 处理其他类型的错误
      let errorMessage = "请求失败，请稍后重试";

      if (error.message && typeof error.message === "string") {
        if (error.message.includes("服务器繁忙")) {
          errorMessage = "服务器繁忙，请稍后再试";
        } else if (error.message.includes("provider_quota_exceeded")) {
          errorMessage = "API调用额度不足，请联系管理员";
        } else if (error.message.includes("model_currently_not_support")) {
          errorMessage = "当前模型不可用，请稍后再试";
        } else {
          errorMessage = error.message;
        }
      }

      lastItem.content = errorMessage;
    }
  } finally {
    isStreamLoad.value = false;
    loading.value = false;
    fetchCancel.value = null;
  }
};

/**
 * 停止生成响应
 * @description 中断当前正在进行的API请求，可以通过任务ID或取消控制器实现
 */
const onStop = async () => {
  try {
    if (currentTaskId.value) {
      // 通过任务ID停止响应生成
      console.log("停止任务:", currentTaskId.value);
      await difyClient.stopResponseGeneration(
        currentTaskId.value,
        userId.value
      );
      isStreamLoad.value = false;
      loading.value = false;
      workflowLoading.value = false;
    } else if (fetchCancel.value) {
      // 通过AbortController停止请求
      await fetchCancel.value.abort();
      isStreamLoad.value = false;
      loading.value = false;
    } else {
      console.warn("没有可停止的任务");
    }
  } catch (error) {
    console.error("停止生成失败:", error);
    isStreamLoad.value = false;
    loading.value = false;
  } finally {
    fetchCancel.value = null;
    currentTaskId.value = "";
  }
};

/**
 * 处理消息操作
 * @description 处理消息的重发和复制操作
 * @param {string} type - 操作类型，支持 'replay' 和 'copy'
 * @param {Object} options - 操作参数
 * @param {number} options.index - 消息在列表中的索引
 */
const handleOperation = function (type, { index }) {
  if (type === "replay") {
    // 重发消息操作
    const currentMessage = chatList.value[index];
    const userMessage = chatList.value.find(
      (item, i) =>
        item.role === "user" &&
        item.datetime < currentMessage.datetime &&
        i === index - 1
    );

    if (userMessage) {
      chatList.value.splice(index - 1);
      handleData({
        prompt: userMessage.content,
        files: userMessage.files,
      });
    }
  } else if (type === "copy") {
    // 复制消息内容
    navigator.clipboard
      .writeText(chatList.value[index].content)
      .then(() => {
        ElMessage.success("复制成功");
      })
      .catch(() => {
        ElMessage.error("复制失败");
      });
  }
};

/**
 * 处理示例问题点击
 * @description 当用户点击推荐问题时，自动发送该问题
 * @param {string} question - 被点击的示例问题文本
 */
const handleExampleClick = (question) => {
  handleData({
    prompt: question,
    files: [],
  });
};

/**
 * 开始新对话
 * @description 清空当前对话记录并重置会话状态
 */
const startNewChat = () => {
  chatList.value = [];
  currentConversationId.value = "";
  currentTaskId.value = "";
};

/**
 * 获取应用参数
 * @description 从服务器获取应用配置参数并更新本地状态
 * @returns {Promise<void>}
 */
const getAppParameters = async () => {
  appLoading.value = true;
  try {
    const res = await difyClient.getAppParameters();
    if (!res.ok) {
      throw new Error(`获取应用参数失败: ${res.status} ${res.statusText}`);
    }
    const data = await res.json();

    // 保存应用参数
    appParameters.value = data;

    // 解构并保存各个功能参数
    const {
      opening_statement,
      suggested_questions,
      suggested_questions_after_answer,
      speech_to_text,
      retriever_resource,
      annotation_reply,
      user_input_form,
      file_upload,
    } = data;

    // 更新各功能配置
    openingStatement.value = opening_statement || "";
    suggestedQuestions.value = suggested_questions || [];
    suggestedQuestionsAfterAnswer.value = suggested_questions_after_answer || {
      enabled: false,
    };
    speechToText.value = speech_to_text || { enabled: false };
    retrieverResource.value = retriever_resource || { enabled: false };
    annotationReply.value = annotation_reply || { enabled: false };
    userInputForm.value = user_input_form || [];
    fileUpload.value = file_upload;

    return data;
  } catch (error) {
    console.error("获取应用参数出错:", error);
    ElMessage.error("获取应用参数失败");
  } finally {
    appLoading.value = false;
  }
};

// 导出方法供父组件调用
defineExpose({
  initDifyClient,
  startNewChat,
  getAppParameters,
});
</script>

<style lang="scss" scoped>
/* 聊天容器样式 */
.dify-chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  position: relative;

  /* 消息列表容器样式 */
  .messages-wrapper {
    flex: 1;
    min-height: 0;
    position: relative;

    /* 消息列表滚动区域样式 */
    .chat-messages {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      overflow-y: auto;
      padding: 20px;
      scrollbar-width: thin; // Firefox滚动条样式
      scrollbar-color: rgba(0, 0, 0, 0.1) transparent; // Firefox滚动条颜色
      padding-top: 20px;
      scroll-behavior: smooth;

      /* Webkit浏览器滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px; // 滚动条宽度
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(26, 115, 232, 0.2); // 滚动条滑块颜色
        border-radius: 3px;
        transition: background-color 0.3s;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: rgba(26, 115, 232, 0.4); // 滚动条滑块悬停颜色
      }

      &::-webkit-scrollbar-track {
        background-color: transparent; // 滚动条轨道颜色
      }
    }

    /* 新对话按钮容器样式 */
    .new-chat-wrapper {
      display: flex;
      justify-content: center;
      padding: 20px 0;
      margin-top: 20px;

      /* 新对话按钮样式 */
      .start-new-chat {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px 24px;
        border: none;
        border-radius: 24px;
        background-color: rgba(26, 115, 232, 0.1);
        color: rgba(26, 115, 232, 0.8);
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.25s ease;

        /* 按钮中的图标样式 */
        .iconify {
          font-size: 18px;
          color: rgba(26, 115, 232, 0.8);
          transition: transform 0.25s ease;
        }

        /* 按钮悬停效果 */
        &:hover {
          background-color: rgba(26, 115, 232, 0.15);
          color: rgba(26, 115, 232, 0.9);
          transform: translateY(-1px);
          box-shadow: 0 3px 10px rgba(26, 115, 232, 0.15);
        }

        /* 按钮点击效果 */
        &:active {
          transform: translateY(0);
          background-color: rgba(26, 115, 232, 0.2);
        }
      }
    }
  }
}

/* 开场白样式 */
.opening-statement {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 30px;
  animation: fade-in 0.4s ease-out;

  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .opening-content {
    display: flex;
    gap: 16px;
    padding: 18px 20px;
    background: linear-gradient(to right, #f0f5ff, #edf3ff);
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.08);
    border-left: 3px solid rgba(26, 115, 232, 0.5);

    .opening-message {
      flex: 1;
      font-size: 15px;
      line-height: 1.5;
      color: #333;
      white-space: pre-line;
    }
  }

  .suggested-questions {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .suggestions-label {
      font-size: 14px;
      font-weight: 500;
      color: #555;
      margin-left: 4px;
    }

    .question-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .question-item {
        padding: 10px 16px;
        background-color: #f7f9ff;
        border: 1px solid #e0e7ff;
        border-radius: 20px;
        font-size: 14px;
        color: #1a73e8;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);

        &:hover {
          background-color: #e6eeff;
          transform: translateY(-2px);
          box-shadow: 0 3px 8px rgba(26, 115, 232, 0.1);
        }

        &:active {
          transform: translateY(0);
          background-color: #dde7ff;
        }
      }
    }
  }
}

.loading-container {
  width: 100%;
  padding: 10px 0;

  :deep(.el-skeleton) {
    --el-skeleton-color: rgba(26, 115, 232, 0.1);
    --el-skeleton-to-color: rgba(26, 115, 232, 0.2);
  }
}
</style>
