<template>
  <div class="hedgingBox">
    <Results
      v-if="detailShow"
      :taskId="taskId"
      :planId="planId"
      @back="backAdjust"
    ></Results>
    <Programme
      v-if="createShow"
      :info="infoData1"
      @closeCreate="createFalse"
    ></Programme>
    <div class="lenged-fix" v-if="sliderShow">
      <img
        src="@/assets/images/play_start.png"
        v-if="!viewPlay"
        alt=""
        style="width: 40px; height: 40px; cursor: pointer"
        @click="playView"
      />
      <img
        src="@/assets/images/play_pause.png"
        v-else
        alt=""
        style="width: 40px; height: 40px; cursor: pointer"
        @click="pauseView"
      />
      <el-slider
        v-model="slider"
        :step="2"
        :marks="marks"
        :show-tooltip="false"
        :min="0"
        :max="max"
        style="margin-left: 10px"
        @change="sliderChange"
      />
    </div>
    <div class="lenged-fix3" v-if="sliderShow2">
      <img
        src="@/assets/images/play_start.png"
        v-if="!viewPlay2"
        alt=""
        style="width: 40px; height: 40px; cursor: pointer"
        @click="playDoubleView"
      />
      <img
        src="@/assets/images/play_pause.png"
        v-else
        alt=""
        style="width: 40px; height: 40px; cursor: pointer"
        @click="pauseDoubleView"
      />
      <el-slider
        v-model="slider2"
        :step="1"
        :marks="marks2"
        :show-tooltip="true"
        :min="0"
        :max="max2"
        style="margin-left: 10px"
        @change="sliderChange2"
      />
    </div>
    <!-- <div class="fix4-box" v-if="txtShow">
      <div>淹没影响人口：5098人</div>
      <div>淹没影响房屋：1345座</div>
      <div>淹没耕地面积：345亩</div>
      <div>淹没损失统计：589万元</div>
    </div> -->
    <!-- <div class="NavigatorButton">
      <el-button type="primary" @click="showUE()">溃坝分析</el-button>
    </div> -->
    <el-dialog
      v-model="centerUEDialogVisible"
      width="1200"
      destroy-on-close
      center
      custom-class="dialogClass"
    >

    <iframe
      id="sonSystem"
      width="100%"
      height="100%"
      title="Inline Frame"
      src="http://**********:3000/"
    ></iframe>
    </el-dialog>
    <div class="colorBand" v-if="colorBandShow">
      <div class="colorBand-title">水深</div>
      <div class="colorBand-content">
        <div
          class="colorBand-item"
          v-for="(item, index) in colorLegendOfRiver"
          :key="index"
        >
          <div
            class="colorBand-item-color"
            :style="{ background: item.color }"
          ></div>
          <div class="colorBand-item-text">{{ Math.floor(item.value) }}</div>
          <!-- 可能需要保留几位小数 -->
        </div>
      </div>
    </div>

    <div class="colorBandByCatchment" v-if="colorBandShow">
      <div class="colorBand-title">出流量</div>
      <div class="colorBand-content">
        <div
          class="colorBand-item"
          v-for="(item, index) in colorLegendOfCatchment"
          :key="index"
        >
          <div
            class="colorBand-item-color"
            :style="{ background: item.color }"
          ></div>
          <div class="colorBand-item-text">{{ Math.floor(item.value) }}</div>
          <!-- 可能需要保留几位小数 -->
        </div>
      </div>
    </div>

    <div class="box-border" v-if="tableRange">
      <span></span>
      <span></span>
      <span></span>
      <span></span>
      <div class="box1 map-panel">
        <div class="map-panel-content">
          <div class="head">
            <div class="title">调度成果对比</div>
            <div class="close" @click="closeMask"></div>
          </div>
          <div class="line">
            <div class="left"></div>
            <div class="center"></div>
            <div class="right"></div>
          </div>
          <div class="achievement" v-if="rsShow">
            <div class="top"></div>
            <el-table
              class="productTable"
              :data="rangeTableData"
              :header-cell-style="{ textAlign: 'center', color: '#ffffff' }"
              :cell-style="{
                color: '#ffffff',
                textAlign: 'center',
                fontSize: 13 + 'px',
              }"
            >
              <!-- <el-table-column label="序号" type="index" width="50"></el-table-column> -->
              <el-table-column prop="name" label="任务成果" width="180" />
              <el-table-column prop="topInq" label="入库洪峰(m³/s)" width="180">
                <template v-slot="scope">
                  {{ scope.row.topInq ? scope.row.topInq.toFixed(3) : "0.00" }}
                </template>
              </el-table-column>
              <el-table-column prop="topZ" label="最高库水位(m)">
                <template v-slot="scope">
                  {{ scope.row.topZ ? scope.row.topZ.toFixed(3) : "0.00" }}
                </template>
              </el-table-column>
              <el-table-column prop="topOtq" label="最大出库流量(m³/s)">
                <template v-slot="scope">
                  {{ scope.row.topOtq ? scope.row.topOtq.toFixed(3) : "0.00" }}
                </template>
              </el-table-column>
              <el-table-column prop="peakRate" label="削峰率(%)">
                <template v-slot="scope">
                  {{
                    scope.row.peakRate ? scope.row.peakRate.toFixed(3) : "0.00"
                  }}
                </template>
              </el-table-column>
              <el-table-column prop="peakTime" label="错峰时长(h)">
              </el-table-column>
              <el-table-column prop="floodCapacity" label="拦洪量(万m³)">
                <template v-slot="scope">
                  {{
                    scope.row.floodCapacity
                      ? scope.row.floodCapacity.toFixed(3)
                      : "0.00"
                  }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="charts">
            <div class="item">
              <div class="top">
                {{ rsShow ? "库水位过程对比" : "水位过程对比" }}
              </div>
              <div id="rangeData1" class="leftEcharts"></div>
            </div>
            <div class="item">
              <div class="top">
                {{ rsShow ? "出入库流量过程对比" : "流量过程对比" }}
              </div>
              <div id="rangeData2" class="rightEcharts"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="table-range">
      <div style="width: 80%;margin: 5% auto;">
        <el-table :data="rangeTableData" :style="{ width: '100%' }">

        </el-table>
      </div>

    </div> -->
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  nextTick,
} from "vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { selectStlyList, selectStlyInfo } from "@/api/watershed/ads";
import moment from "moment";
import Results from "./results.vue";
import Programme from "./programme.vue";
import {
  postDisFn,
  dispFaEnable,


} from "../../../api/watershed/screenRight/dispatch";
import {
  taskWorkList,
  getWorkDetail,
  taskPlanList,
  queryOneDimensionalWaterInfo,
} from "@/api/scheduling";
import useAppStore from "@/store/modules/app";
import { option1, option2 } from "./schemeConfig";
import * as echarts from "echarts";
import { zwhRiver } from "@/assets/zwhRiver.js";
import { exw } from "@/assets/exw.js";
import { dmfq } from "@/assets/dmfq.js";
import useUserStore from "@/store/modules/user";
import { sortColor } from "@/utils/sortColor.js";
import * as turf from "@turf/turf";
const userStore = useUserStore();
const viewer = null;
let Cesium = null;
let zwhLayer = [];
let zwhSplitScreenLayer_second = [];
let zwhSplitScreenLayer = [];
let riverFeaturesColorByOne = [];
let riverFeaturesColorByTwo_first = [];
let riverFeaturesColorByTwo_second = [];
const userId = userStore.deptId;
export default defineComponent({
  components: {
    Results,
    Programme,
  },
  setup() {
    const proxy = getCurrentInstance();
    const state = reactive({
      achList: [],
      txtShow: false,
      taskList: [],
      rangeTableData: [],
      rangeData: [],
      rangeIndex: null,
      tableRange: false,
      sliderShow: false,
      sliderShow2: false,
      taskId: null,
      taskId2: null,
      planId2: null,
      marks: {},
      marks2: {},
      planId: null,
      waterAreaList: [],
      allTask: [],
      form: {
        dispcNm: "",
        fcid: "",
        author: "",
        time: [],
      },
      detailShow: false,
      createShow: false,
      multiple: true,
      list: [
        // {
        //   name: '2023-10-12人工固定下泄调度方案',
        //   result: '预报成果',
        //   time: '2022-12-05',
        //   operator: '李倩'
        // },
        // {
        //   name: '2023-10-12人工固定下泄调度方案',
        //   result: '预报成果',
        //   time: '2022-12-05',
        //   operator: '李倩'
        // }
      ],
      st: null,
      st2: null,
      selectedList: [],
      infoData: {},
      viewPlay: false,
      viewPlay2: false,
      stationList: [],
      stationList2: [],
      rainList: [],
      slider: 0,
      slider2: 0,
      max: 999,
      max2: 999,
      queryParams: {
        name: "",
        basinId: "",
        planId: "",
        isAuto: "",
        date: [],
        state: "",
      },
      rsShow: true,
      rainList2: [],
      imageListAcc: [],
      imageListAcc2: [],
      cloudLayers: [],
      cloudLayers2: [],
      infoData1: {},
      result: [],
      result2: [],
      RiverLoading: null,
      colorBandShow: false,
      colorLegendOfRiver: [], // 河流颜色图例
      colorLegendOfCatchment: [], //流域颜色图例
      NavigatorShow: false,
      centerUEDialogVisible:false, //中心UE场景对话框
    });
    const appStore = useAppStore();
    const setRangeChart = () => {
      if (state.rangeData.length == 0) return false;
      // console.log(state.rangeData, "rangeData");
      let time = state.rangeData[0].vos.map((item) => {
        return moment(item.time).format("YYYY-MM-DD HH:mm:ss");
      });
      //预测库水位 实测库水位
      let a = state.rangeData[0].vos.map((item) => {
        return item.z;
      });

      let b = state.rangeData[1].vos.map((item) => {
        return item.z;
      });

      //预测入库 预测出库
      let z = state.rangeData[0].vos.map((item) => {
        return item.qi;
      });
      let x = state.rangeData[0].vos.map((item) => {
        return item.qo;
      });
      let z1 = state.rangeData[1].vos.map((item) => {
        return item.qi;
      });
      let x1 = state.rangeData[1].vos.map((item) => {
        return item.qo;
      });
      echarts.init(document.getElementById("rangeData1")).dispose();
      let myEchart = echarts.init(document.getElementById("rangeData1"));
      myEchart.setOption(
        option1(
          state.result.data.name + "-" + state.rangeData[0].stnm,
          state.result2.data.name + "-" + state.rangeData[1].stnm,
          a,
          b,
          time
        )
      );
      echarts.init(document.getElementById("rangeData2")).dispose();
      let myEchart2 = echarts.init(document.getElementById("rangeData2"));
      myEchart2.setOption(
        option2(
          state.result.data.name + "-" + state.rangeData[0].stnm,
          state.result2.data.name + "-" + state.rangeData[1].stnm,
          z,
          x,
          z1,
          x1,
          time
        )
      );
    };
    onMounted(() => {
      window.EventBus.$on("backList", backList);
      window.EventBus.$on("colorBandShow/update", (newValue) => {
        state.colorBandShow = newValue;
      });
      window.EventBus.$on("Navigation/update", (newValue) => {
        if (newValue === false) {
          state.NavigatorShow = false;
          let layer = window.viewer.getLayer("NativeLine1");
          if (layer) {
            window.viewer.removeLayer(layer);
          }
          window.viewer.entities.values.forEach((entitie) => {
            if (
              entitie.id === "startPoi_1" ||
              entitie.id === "startPoi_2" ||
              entitie.id === "startPoi_3" ||
              entitie.id === "endPoi"
            ) {
              entitie.show = false;
            }
          });
        }
      });
      window.EventBus.$on("forecastDispatchPlan", handleClick);
      let time = [
        moment().subtract(2, "days").format("YYYY-MM-DD 00:00:00"),
        moment().format("YYYY-MM-DD 23:59:59"),
      ];
      state.form.time = time;

      getAllTask();
      getAllWater();
      getList();

    });

    const getList = async () => {

      taskWorkList({
        state: 5,
        name: state.queryParams.name,
        basinId: state.queryParams.basinId,
        planId: state.queryParams.planId,
        isAuto: state.queryParams.isAuto,
        startTime: state.form.time[0],
        endTime: state.form.time[1],
      }).then((res) => {
        // console.log(res, "预报调度列表");
        state.list = res.data;
      });
    };
    const transformDataForTreeSelect = (data) => {
      // 递归地转换数据以匹配 el-tree-select 的需求
      return data.map((item) => ({
        label: item.data.name, // 使用 'name' 属性作为标签
        value: item.data.basinId, // 使用 'basinId' 属性作为值
        children: item.children
          ? transformDataForTreeSelect(item.children)
          : [], // 递归转换子节点
      }));
    };
    const getAllWater = async () => {
      let res = await selectStlyList({ pageNum: 1, pageSize: 999 });
      state.waterAreaList = res.data || [];
    };
    const getAllTask = async () => {
      let res = await taskPlanList({
        pageNum: 1,
        pageSize: 99999,
      });
      if (res.data.records && res.data.records.length > 0) {
        state.taskList = res.data.records;
        // console.log(state.taskList, "所有的方案");
      } else {
        state.taskList = [];
      }
    };
    const sliderChange = (e) => {
      state.cloudLayers.forEach((item) => {
        item.alpha = 0;
      });
    };
    const sliderChange2 = (e) => {
      state.cloudLayers.forEach((item) => {
        item.alpha = 0;
      });
      state.cloudLayers2.forEach((item) => {
        item.alpha = 0;
      });
    };
    const queryData = async () => {
      const res = await postDisFn({
        dispcNm: state.form.dispcNm,
        fcid: state.form.fcid,
        dispcSTTm: moment(state.form.time[0]).format("YYYY-MM-DD HH:mm:ss"),
        dispcEDTm: moment(state.form.time[1]).format("YYYY-MM-DD HH:mm:ss"),
        // dispcSTTm: state.form.time[0] ? state.form.time[0] : '',
        // dispcEDTm: state.form.time[1] ? state.form.time[1] : '',
        author: state.form.author,
      });

      state.list = res.data;
    };
    const resetQuery = () => {
      state.queryParams = {
        name: "",
        basinId: "",
        planId: "",
        isAuto: "",
        date: [],
        state: "",
      };
      let time = [
        moment().subtract(2, "days").format("YYYY-MM-DD 00:00:00"),
        moment().format("YYYY-MM-DD 23:59:59"),
      ];
      state.form.time = time;
      pageNum.value = 1;
      pageSize.value = 20;
      getList();
    };
    const handDoubleClick = async (item, item2) => {
      state.taskId = item.id;
      state.planId = item.planId;
      state.taskId2 = item2.id;
      state.planId2 = item2.planId;
      state.RiverLoading = ElLoading.service({
        lock: true,
        text: "数据加载中",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let result = await getWorkDetail(item.id);
      let hour = result.data.warmUpPeriod || 0;
      let lineTime = moment(result.data.resultVos[0].vos[0].time)
        .add(hour, "hours")
        .valueOf();
      for (let i = 0; i < result.data.resultVos.length; i++) {
        result.data.resultVos[i].vos = result.data.resultVos[i].vos.filter(
          (item) => {
            return moment(item.time).valueOf() >= lineTime;
          }
        );
      }
      for (let i = 0; i < result.data.rainResultVos.length; i++) {
        result.data.rainResultVos[i].rainListVos = result.data.rainResultVos[
          i
        ].rainListVos.filter((item) => {
          return moment(item.tm).valueOf() >= lineTime;
        });
      }

      let result2 = await getWorkDetail(item2.id);
      for (let i = 0; i < result2.data.resultVos.length; i++) {
        result2.data.resultVos[i].vos = result2.data.resultVos[i].vos.filter(
          (item) => {
            return moment(item.time).valueOf() >= lineTime;
          }
        );
      }
      for (let i = 0; i < result2.data.rainResultVos.length; i++) {
        result2.data.rainResultVos[i].rainListVos = result2.data.rainResultVos[
          i
        ].rainListVos.filter((item) => {
          return moment(item.tm).valueOf() >= lineTime;
        });
      }
      //方案对比 遇见期不同处理
      let num1 = result.data.rainResultVos[0]?.rainListVos.length;
      let num2 = result2.data.rainResultVos[0]?.rainListVos.length;
      let outNum = num1 - num2;
      if (outNum > 0) {
        addSliderTime(result);
        for (let i = 0; i < num1; i++) {
          if (!result2.data.rainResultVos[0].rainListVos[i]) {
            result2.data.rainResultVos[0].rainListVos.push({
              tm: result.data.rainResultVos[0].rainListVos[i].tm,
              rainValue: 0,
            });
          }
          if (!result2.data.resultVos[0].vos[i]) {
            if (result2.data.resultVos[0].sttp == "RR") {
              result2.data.resultVos[0].vos.push({
                stcd: "",
                time: result.data.resultVos[0].vos[i].time,
                qo: 0,
                qi: 0,
                z: 0,
                realQ: 0,
                realZ: 0,
                inq: 0,
                otq: 0,
              });
            } else {
              result2.data.resultVos[0].vos.push({
                stcd: "",
                time: result.data.resultVos[0].vos[i].time,
                qo: 0,
                qi: 0,
                z: 0,
                realQ: 0,
              });
            }
          }
        }
      } else {
        addSliderTime(result2);
        for (let i = 0; i < num2; i++) {
          if (!result.data.rainResultVos[0].rainListVos[i]) {
            result.data.rainResultVos[0].rainListVos.push({
              tm: result2.data.rainResultVos[0].rainListVos[i].tm,
              rainValue: 0,
            });
          }
          if (!result.data.resultVos[0].vos[i]) {
            if (result.data.resultVos[0].sttp == "RR") {
              result.data.resultVos[0].vos.push({
                stcd: "",
                time: result2.data.resultVos[0].vos[i].time,
                qo: 0,
                qi: 0,
                z: 0,
                realQ: 0,
                realZ: 0,
                inq: 0,
                otq: 0,
              });
            } else {
              result.data.resultVos[0].vos.push({
                stcd: "",
                time: result2.data.resultVos[0].vos[i].time,
                qo: 0,
                qi: 0,
                z: 0,
                realQ: 0,
              });
            }
          }
        }
      }
      //
      state.result = result;
      state.result2 = result2;
      let layer = window.viewer.getLayer("xiongjiachang-layer ");
      let layer2 = window.viewer2.getLayer("xiongjiachang-layer ");
      if (layer) {
        layer.clear();
        window.viewer.removeLayer(layer);
      }
      if (layer2) {
        layer2.clear();
        window.viewer2.removeLayer(layer2);
      }
      let res = await selectStlyInfo(state.result.data.basinId);
      // console.log(res, "熊家场流域详情");
      // console.log(typeof res.data.geom);
      let geom =
        typeof res.data.geom == "string"
          ? JSON.parse(res.data.geom)
          : res.data.geom;
      layer = new DC.GeoJsonLayer("xiongjiachang-layer ", geom);
      layer2 = new DC.GeoJsonLayer("xiongjiachang-layer ", geom);
      layer.eachOverlay((item) => {
        // item 为一个entity,

        if (item.polygon) {
          //todo
          item.polygon.allowDrillPicking = true; //样式穿透
          item.polygon.zIndex = 0;
          item.polygon.material =
            DC.Color.fromCssColorString("rgba(24,144,255,0)");
          let polygon = DC.Polygon.fromEntity(item);
        }
      });
      layer2.eachOverlay((item) => {
        // item 为一个entity,

        if (item.polygon) {
          //todo
          item.polygon.allowDrillPicking = true; //样式穿透
          item.polygon.zIndex = 0;
          item.polygon.material =
            DC.Color.fromCssColorString("rgba(24,144,255,0)");
          let polygon = DC.Polygon.fromEntity(item);
        }
      });
      //漳渭河流域个性化地图初始渲染
      userId == "237" ? zwhbMapInit(result, result2) : null;
      window.viewer.addLayer(layer);
      window.viewer2.addLayer(layer2);
      window.viewer.flyTo(layer);
      window.viewer2.flyTo(layer2);
      addMapChange(result);
      addMapChange2(result2);
      startLoadDoublePointImages(result, result2);
    };

    const addMapChange = (result) => {
      let data = result.data;
      state.stationList = data.resultVos;
      state.rainList = data.rainResultVos[0].rainListVos;
      let rainViewer = window.viewer.getLayer("rainViewer");
      if (rainViewer) {
        rainViewer.clear();
      } else {
        rainViewer = new DC.HtmlLayer(`rainViewer`);
      }
      window.viewer.addLayer(rainViewer);
      state.stationList.forEach((item, index) => {
        let html;
        if (item.sttp == "RR") {
          html = `<div style=' display: flex;flex-direction: column;justify-content: center; margin-bottom: 170px;margin-left:75px;'>
          <div  class="area-item">
            <div>${item.stnm}</div>
            <hr class="popHr">
            <div>面雨量 ${
              state.rainList[state.slider2]
                ? state.rainList[state.slider2].rainValue
                : "--"
            }mm</div>
            <div>预报入库流量 ${
              state.stationList[index].vos[state.slider2].qi
            } m³/s</div>
            <div>实测入库流量  ${
              state.stationList[index].vos[state.slider2].inq
            } m³/s</div>
            <div>预报库水位 ${
              state.stationList[index].vos[state.slider2].z
            } m</div>
            <div>实测库水位 ${
              state.stationList[index].vos[state.slider2].realZ
            } m</div>
            <div>预报出库流量 ${
              state.stationList[index].vos[state.slider2].qo
            } m³/s/s</div>
            <div>实测出库流量 ${
              state.stationList[index].vos[state.slider2].otq
            } m³/s/s</div>
            <div>数据时间 ${moment(
              state.stationList[index].vos[state.slider2].time
            ).format("YYYY-MM-DD HH:mm:ss")}</div>
            </div>  <img src="/images/bottom.png" style="width:96px;height:62px;"/>
          </div>`;
        } else {
          html = `<div style=' display: flex;flex-direction: column;justify-content: center;margin-bottom: 170px;margin-left:75px;'>
          <div  class="area-item">
            <div>${item.stnm}</div>
            <hr class="popHr">
            <div>面雨量 ${
              state.rainList[state.slider2]
                ? state.rainList[state.slider2].rainValue
                : "--"
            }mm</div>
            <div>预报流量 ${
              state.stationList[index].vos[state.slider2].qi
            } m³/s</div>
            <div>实际流量  ${
              state.stationList[index].vos[state.slider2].realQ
            } m³/s</div>
            <div>预报水位 ${
              state.stationList[index].vos[state.slider2].z
            } m</div>
            <div>实测水位 ${
              state.stationList[index].vos[state.slider2].realZ
            } m</div>
            <div>数据时间 ${moment(
              state.stationList[index].vos[state.slider2].time
            ).format("YYYY-MM-DD HH:mm:ss")}</div>
            </div>  <img src="/images/bottom.png" style="width:96px;height:62px;"/>
          </div>`;
        }
        let position2 = new DC.Position(Number(item.lgtd), Number(item.lttd));
        let divIcon2 = new DC.DivIcon(position2, html);
        divIcon2.attr = item;
        divIcon2.attr[`rangeIndex`] = index;
        divIcon2.on(DC.MouseEventType.CLICK, (e) => {
          //获取表格数据后再获取echarts数据
          let c_index = e.overlay.attr.rangeIndex;
          state.rangeData = [];
          let t_item1 = state.stationList[c_index];
          let t_item2 = state.stationList2[c_index];
          t_item1["name"] = state.result.data.name;
          t_item2["name"] = state.result2.data.name;
          state.rangeData.push(t_item1, t_item2);
          // console.log(state.rangeData);
          state.rangeTableData = state.rangeData.map((el) => {
            return { name: el.name, ...el.forecastResStationInfoDto };
          });
          // console.log("调度成果数据", state.rangeTableData);
          state.tableRange = true;
          state.sliderShow = false;
          nextTick(() => {
            setRangeChart();
          });
        });
        rainViewer.addOverlay(divIcon2);
      });
    };
    const closeMask = () => {
      state.tableRange = false;
      state.sliderShow2 = true;
    };
    const addMapChange2 = (result) => {
      let data = result.data;
      state.stationList2 = data.resultVos;
      state.rainList2 = data.rainResultVos[0].rainListVos;
      let rainViewer = window.viewer2.getLayer("rainViewer");
      if (rainViewer) {
        rainViewer.clear();
      } else {
        rainViewer = new DC.HtmlLayer(`rainViewer`);
      }
      window.viewer2.addLayer(rainViewer);
      state.stationList2.forEach((item, index) => {
        let html;
        if (item.sttp == "RR") {
          html = `<div style=' display: flex;flex-direction: column;justify-content: center;margin-bottom: 170px;margin-left:75px;'>
          <div  class="area-item">
            <div>${item.stnm}</div>
            <hr class="popHr">
            <div>面雨量 ${
              state.rainList2[state.slider2]
                ? state.rainList2[state.slider2].rainValue
                : "--"
            }mm</div>
            <div>预报入库流量 ${
              state.stationList2[index].vos[state.slider2].qi
            } m³/s</div>
            <div>实测入库流量  ${
              state.stationList2[index].vos[state.slider2].inq
            } m³/s</div>
            <div>预报库水位 ${
              state.stationList2[index].vos[state.slider2].z
            } m</div>
            <div>实测库水位 ${
              state.stationList2[index].vos[state.slider2].realZ
            } m</div>
            <div>预报出库流量 ${
              state.stationList2[index].vos[state.slider2].qo
            } m³/s/s</div>
            <div>实测出库流量 ${
              state.stationList2[index].vos[state.slider2].otq
            } m³/s/s</div>
            <div>数据时间 ${moment(
              state.stationList2[index].vos[state.slider2].time
            ).format("YYYY-MM-DD HH:mm:ss")}</div>
            </div>  <img src="/images/bottom.png" style="width:96px;height:62px;"/>
          </div>`;
        } else {
          html = `<div style=' display: flex;flex-direction: column;justify-content: center;margin-bottom: 170px;margin-left:75px;'>
          <div  class="area-item">
            <div>${item.stnm}</div>
            <hr class="popHr">
            <div>面雨量 ${
              state.rainList2[state.slider]
                ? state.rainList2[state.slider].rainValue
                : "--"
            }mm</div>
            <div>预报流量 ${
              state.stationList2[index].vos[state.slider2].qi
            } m³/s</div>
            <div>实际流量  ${
              state.stationList2[index].vos[state.slider2].realQ
            } m³/s</div>
            <div>预报水位 ${
              state.stationList2[index].vos[state.slider2].z
            } m</div>
            <div>实测水位 ${
              state.stationList2[index].vos[state.slider2].realZ
            } m</div>
            <div>数据时间 ${moment(
              state.stationList2[index].vos[state.slider2].time
            ).format("YYYY-MM-DD HH:mm:ss")}</div>
            </div>  <img src="/images/bottom.png" style="width:96px;height:62px;"/>
          </div>`;
        }
        let position2 = new DC.Position(Number(item.lgtd), Number(item.lttd));
        let divIcon2 = new DC.DivIcon(position2, html);
        divIcon2.attr = item;
        divIcon2.attr[`rangeIndex`] = index;
        divIcon2.on(DC.MouseEventType.CLICK, (e) => {
          //获取表格数据后再获取echarts数据
          let c_index = e.overlay.attr.rangeIndex;
          state.rangeData = [];
          let t_item1 = state.stationList[c_index];
          let t_item2 = state.stationList2[c_index];
          t_item1["name"] = state.result.data.name;
          t_item2["name"] = state.result2.data.name;
          state.rangeData.push(t_item1, t_item2);
          // console.log(state.rangeData);
          state.rangeTableData = state.rangeData.map((el) => {
            return { name: el.name, ...el.forecastStationInfoDto };
          });
          state.tableRange = true;
          state.sliderShow = false;
          nextTick(() => {
            setRangeChart();
          });
        });
        rainViewer.addOverlay(divIcon2);
      });
    };
    const handleClick = async (item) => {
      state.RiverLoading = ElLoading.service({
        lock: true,
        text: "数据加载中",
        background: "rgba(0, 0, 0, 0.7)",
      });
      state.taskId = item.id;
      state.planId = item.planId;
      state.detailShow = true;
      //同步地图联动
      // window.EventBus.$emit('floodPreRehearsal/dispatch/select', item)
      let result = await getWorkDetail(item.id);
      // if(result) {
      //   console.log('前置请求',result)
      // }
      //漳渭河流域个性化地图初始渲染
      userId == "237" ? zwhbMapInit(result) : null;
      state.result = result;
      let layer = window.viewer.getLayer("xiongjiachang-layer");
      if (layer) {
        layer.clear();
        window.viewer.removeLayer(layer);
      }
      let res = await selectStlyInfo(state.result.data.basinId);
      // console.log(res, "熊家场流域详情");
      // console.log(typeof res.data.geom);
      let geom =
        typeof res.data.geom == "string"
          ? JSON.parse(res.data.geom)
          : res.data.geom;
      layer = new DC.GeoJsonLayer("xiongjiachang-layer", geom);
      layer.eachOverlay((item) => {
        // item 为一个entity,

        if (item.polygon) {
          //todo
          item.polygon.allowDrillPicking = true; //样式穿透
          item.polygon.zIndex = 0;
          item.polygon.material =
            DC.Color.fromCssColorString("rgba(24,144,255,0)");
          let polygon = DC.Polygon.fromEntity(item);
        }
      });
      window.viewer.addLayer(layer);
      window.viewer.flyTo(layer);
      let hour = result.data.warmUpPeriod || 0;
      let lineTime = moment(result.data.resultVos[0].vos[0].time)
        .add(hour, "hours")
        .valueOf();
      for (let i = 0; i < result.data.resultVos.length; i++) {
        result.data.resultVos[i].vos = result.data.resultVos[i].vos.filter(
          (item) => {
            return moment(item.time).valueOf() >= lineTime;
          }
        );
      }
      for (let i = 0; i < result.data.rainResultVos.length; i++) {
        result.data.rainResultVos[i].rainListVos = result.data.rainResultVos[
          i
        ].rainListVos.filter((item) => {
          return moment(item.tm).valueOf() >= lineTime;
        });
      }
      mapUpdate(result);
      sliderTime(result);

      startLoadGridPointImages(result);
      state.txtShow = true;
    };
    const createFloodDetentionArea = (viewer) => {
      Cesium = DC.getLib("Cesium");
      let FloodDetentionArea = viewer.getLayerGroup("FloodDetentionArea");
      if (FloodDetentionArea) {
        FloodDetentionArea.show = true;
        if (viewer.entities.values.length > 0) {
          viewer.entities.values.forEach((item) => {
            if (item.id == "exw" || item.id == "dmfq" || item.id == "snsz") {
              item.show = true;
            }
          });
        }
      } else {
        FloodDetentionArea = new DC.LayerGroup("FloodDetentionArea");
        viewer.addLayerGroup(FloodDetentionArea);
        // window.viewer2.addLayerGroup(FloodDetentionArea);
        let layer_exw = new DC.GeoJsonLayer("layer_exw", exw);
        let layer_dmfq = new DC.GeoJsonLayer("layer_dmfq", dmfq);
        //添加两个溃面，一个四女闸

        let position_dmfq = new DC.Position(115.11994, 36.30395);
        let Label_dmfq = new DC.Label(position_dmfq, "大名泛区");

        let position_exw = new DC.Position(116.09481, 37.28066);
        let Label_exw = new DC.Label(position_exw, "恩县洼");

        let style = {
          font: "28px sans-serif",
          fillColor: Cesium.Color.WHITE,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(40, 0),
        };
        Label_dmfq.setStyle(style);
        Label_exw.setStyle(style);

        FloodDetentionArea.addLayer(layer_exw);
        FloodDetentionArea.addLayer(layer_dmfq);
        FloodDetentionArea.addLayer(Label_dmfq);
        FloodDetentionArea.addLayer(Label_exw);

        const billboardEntity_exw = new Cesium.Entity({
          id: "exw",
          position: Cesium.Cartesian3.fromDegrees(115.898358, 37.164071),
          billboard: {
            image: "/icons/marker/kuikou.png",
            scale: 0.75,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
          label: {
            text: "分洪口",
            font: "20px sans-serif",
            fillColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -50),
          },
        });
        viewer.entities.add(billboardEntity_exw);
        const billboardEntity_dmfq = new Cesium.Entity({
          id: "dmfq",
          position: Cesium.Cartesian3.fromDegrees(114.98337, 36.274523),
          billboard: {
            image: "/icons/marker/kuikou.png",
            scale: 0.75,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
          label: {
            text: "分洪口",
            font: "20px sans-serif",
            fillColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -50),
          },
        });
        viewer.entities.add(billboardEntity_dmfq);
        const billboardEntity_snsz = new Cesium.Entity({
          id: "snsz",
          position: Cesium.Cartesian3.fromDegrees(116.2319935, 37.3605421),
          billboard: {
            image: "/icons/marker/zhamen.png",
            scale: 0.5,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
          label: {
            text: "四女寺闸",
            font: "20px sans-serif",
            fillColor: Cesium.Color.WHITE,
            // outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -50),
          },
        });
        viewer.entities.add(billboardEntity_snsz);
      }
    };

    const addSliderTime = async (result) => {
      let data = result.data;
      data.resultVos.forEach((data) => {
        state.marks2 = [];
        state.max2 = data.vos.length;
        if (data.vos.length > 120) {
          data.vos.forEach((pl, sint) => {
            if (sint % 12 == 0 || sint === 0) {
              state.marks2.push(moment(pl.time).format("HH:mm"));
            } else {
              state.marks2.push("");
            }
          });
        } else if (data.vos.length > 40) {
          data.vos.forEach((pl, sint) => {
            if (sint % 6 == 0 || sint === 0) {
              state.marks2.push(moment(pl.time).format("HH:mm"));
            } else {
              state.marks2.push("");
            }
          });
        } else if (data.vos.length > 20) {
          data.vos.forEach((pl, sint) => {
            if (sint % 4 == 0 || sint === 0) {
              state.marks2.push(moment(pl.time).format("HH:mm"));
            } else {
              state.marks2.push("");
            }
          });
        } else {
          data.vos.forEach((pl) => {
            state.marks2.push(moment(pl.time).format("HH:mm"));
          });
        }
      });
      state.sliderShow2 = true;
    };
    const sliderTime = async (result) => {
      let data = result.data;
      data.resultVos.forEach((data) => {
        state.marks = [];

        state.max = data.vos.length;
        // debugger
        if (data.vos.length > 72) {
          data.vos.forEach((pl, sint) => {
            if (sint % 12 == 0 || sint === 0) {
              state.marks.push(moment(pl.time).format("HH:mm"));
            } else {
              state.marks.push("");
            }
          });
        } else if (data.vos.length > 40) {
          data.vos.forEach((pl, sint) => {
            if (sint % 6 == 0 || sint === 0) {
              state.marks.push(moment(pl.time).format("HH:mm"));
            } else {
              state.marks.push("");
            }
          });
        } else if (data.vos.length > 20) {
          data.vos.forEach((pl, sint) => {
            if (sint % 4 == 0 || sint === 0) {
              state.marks.push(moment(pl.time).format("HH:mm"));
            } else {
              state.marks.push("");
            }
          });
        } else {
          data.vos.forEach((pl) => {
            state.marks.push(moment(pl.time).format("HH"));
          });
        }
      });
      state.sliderShow = true;
    };
    const mapUpdate = async (result) => {
      let data = result.data;
      state.stationList = data.resultVos;
      state.rainList = data.rainResultVos[0].rainListVos;
      let rainViewer = window.viewer.getLayer("rainViewer");
      if (rainViewer) {
        rainViewer.clear();
      } else {
        rainViewer = new DC.HtmlLayer(`rainViewer`);
      }
      window.viewer.addLayer(rainViewer);

      state.stationList.forEach((item, index) => {
        let position2 = new DC.Position(Number(item.lgtd), Number(item.lttd));
        let html;
        if (item.sttp == "RR") {
          html = `<div style=' display: flex;flex-direction: column;justify-content: center;margin-bottom: 170px;margin-left:75px;'>
          <div  class="area-item">
            <div>${item.stnm}</div>
            <hr class="popHr">
            <div>面雨量 ${
              state.rainList[state.slider]
                ? state.rainList[state.slider].rainValue
                : "--"
            }mm</div>
            <div>预报入库流量 ${
              state.stationList[index].vos[state.slider].qi
            } m³/s</div>
            <div>实测入库流量  ${
              state.stationList[index].vos[state.slider].inq
            } m³/s</div>
            <div>预报库水位 ${
              state.stationList[index].vos[state.slider].z
            } m</div>
            <div>实测库水位 ${
              state.stationList[index].vos[state.slider].realZ
            } m</div>
            <div>预报出库流量 ${
              state.stationList[index].vos[state.slider].qo
            } m³/s</div>
            <div>实测出库流量 ${
              state.stationList[index].vos[state.slider].otq
            } m³/s</div>
            <div>数据时间 ${moment(
              state.stationList[index].vos[state.slider].time
            ).format("YYYY-MM-DD HH:mm:ss")}</div>
            </div>  <img src="/images/bottom.png" style="width:96px;height:62px;"/>
          </div>`;
        } else {
          html = `<div style=' display: flex;flex-direction: column;justify-content: center; margin-bottom: 170px;margin-left:75px;'>
          <div  class="area-item">
            <div>${item.stnm}</div>
            <hr class="popHr">
            <div>面雨量 ${
              state.rainList[state.slider]
                ? state.rainList[state.slider].rainValue
                : "--"
            }mm</div>
            <div>预报流量 ${
              state.stationList[index].vos[state.slider].qi
            } m³/s</div>
            <div>实际流量  ${
              state.stationList[index].vos[state.slider].realQ
            } m³/s</div>
            <div>预报水位 ${
              state.stationList[index].vos[state.slider].z
            } m</div>
            <div>实测水位 ${
              state.stationList[index].vos[state.slider].realZ
            } m</div>
            <div>数据时间 ${moment(
              state.stationList[index].vos[state.slider].time
            ).format("YYYY-MM-DD HH:mm:ss")}</div>
            </div>  <img src="/images/bottom.png" style="width:96px;height:62px;"/>
          </div>`;
        }
        let divIcon2 = new DC.DivIcon(position2, html);

        divIcon2.attr = item;
        rainViewer.addOverlay(divIcon2);
      });
    };
    //
    const startLoadGridPointImages = async (result) => {
      let data = JSON.parse(result.data.picUrls);

      data = data.replaceAll("[", "");
      data = data.replaceAll("]", "");
      data = data.replaceAll('"', "");
      data = data.replaceAll(
        "https://mc-sh1-prod.obs.cn-east-3.myhuaweicloud.com",
        ""
      );
      let imgTestArray = data.split(",");
      imgTestArray = imgTestArray.slice(0, imgTestArray.length - 1);
      // console.log(imgTestArray)
      let point = result.data.latitude.split(",");
      // const imgTestArray = data.slice(0, data.length - 10)
      state.imageListAcc = [];
      // http://typhoon.nmc.cn/weatherservice/imgs/radar/202311161954_1_0.png
      // 从当前时间往前推 10分钟一个间隔
      // 获取当前时间
      // 初始化数组

      let list = [];
      // 循环往前推 10 分钟
      let times = state.marks;

      let index = 0;
      times.forEach((time, count) => {
        // 将时间添加到数组
        // let tm = moment(time).format('YYYYMMDDHH00')
        // this.marks[index] = moment(time).format('HH:00')
        let item = {
          name: time,
          tm: time,
          baseUrl: imgTestArray[count],
        };
        index++;
        list.push(item);
      });
      getData(list, point);
    };
    const startLoadDoublePointImages = async (result, result2) => {
      let data = JSON.parse(result.data.picUrls);
      let data2 = JSON.parse(result2.data.picUrls);
      data = data.replaceAll("[", "");
      data = data.replaceAll("]", "");
      data = data.replaceAll('"', "");
      data = data.replaceAll(
        "https://mc-sh1-prod.obs.cn-east-3.myhuaweicloud.com",
        ""
      );
      data2 = data2.replaceAll("[", "");
      data2 = data2.replaceAll("]", "");
      data2 = data2.replaceAll('"', "");
      data2 = data2.replaceAll(
        "https://mc-sh1-prod.obs.cn-east-3.myhuaweicloud.com",
        ""
      );
      let imgTestArray = data.split(",");
      let imgTestArray2 = data2.split(",");
      imgTestArray = imgTestArray.slice(0, imgTestArray.length - 1);
      imgTestArray2 = imgTestArray2.slice(0, imgTestArray2.length - 1);

      let point = result.data.latitude.split(",");
      let point2 = result2.data.latitude.split(",");
      state.imageListAcc2 = [];
      state.imageListAcc = [];
      let list = [];
      let list2 = [];
      let times = state.marks2;
      let index = 0;
      times.forEach((time, count) => {
        // 将时间添加到数组
        // let tm = moment(time).format('YYYYMMDDHH00')
        // this.marks[index] = moment(time).format('HH:00')
        let item = {
          name: time,
          tm: time,
          baseUrl: imgTestArray[count]
            ? imgTestArray[count]
            : imgTestArray[imgTestArray.length - 1],
        };
        let item2 = {
          name: time,
          tm: time,
          baseUrl: imgTestArray2[count]
            ? imgTestArray2[count]
            : imgTestArray2[imgTestArray2.length - 1],
        };
        index++;
        list.push(item);
        list2.push(item2);
      });
      // console.log(list, list2, "二维图片数组");
      // getData2(list, point)
      //getData
      state.imageListAcc = list;
      state.imageListAcc2 = list2;
      // 加载第一张
      if (state.imageListAcc.length > 0) {
        //this.loading = true
        //加载淹没数据
        const layers = window.viewer.imageryLayers;
        state.cloudLayers = [];
        const cloudsPromise = [];
        let Cesium = DC.getLib("Cesium");
        const datas = state.imageListAcc;
        // 四角不一样
        let ss = Cesium.Rectangle.fromDegrees(
          Number(point[0]),
          Number(point[1]),
          Number(point[2]),
          Number(point[3])
        );
        for (let i = 0; i < datas.length; i++) {
          const lyr = Cesium.ImageryLayer.fromProviderAsync(
            Cesium.SingleTileImageryProvider.fromUrl(datas[i].baseUrl, {
              rectangle: ss,
            })
          );

          layers.add(lyr);
          lyr.show = true;
          lyr.alpha = 0.0;
          state.cloudLayers.push(lyr);
          // 最后一个可见因为展示最新的默认
          // if (i === datas.length - 1) {
          //   lyr.alpha = 0.75;
          //   this.currentValue = 0
          // }
          cloudsPromise.push(lyr.readyPromise);
        }
        // image加载完了之后执行
        Promise.all(cloudsPromise).then(() => {
          // this.loading = false
          // console.log("对比1演进图片加载完成!!!");
        });
      }
      if (state.imageListAcc2.length > 0) {
        //this.loading = true
        //加载淹没数据
        const layers = window.viewer2.imageryLayers;
        state.cloudLayers2 = [];
        const cloudsPromise = [];
        let Cesium = DC.getLib("Cesium");
        const datas = state.imageListAcc2;
        // 四角不一样
        let ss = Cesium.Rectangle.fromDegrees(
          Number(point2[0]),
          Number(point2[1]),
          Number(point2[2]),
          Number(point2[3])
        );
        for (let i = 0; i < datas.length; i++) {
          const lyr = Cesium.ImageryLayer.fromProviderAsync(
            Cesium.SingleTileImageryProvider.fromUrl(datas[i].baseUrl, {
              rectangle: ss,
            })
          );

          layers.add(lyr);
          lyr.show = true;
          lyr.alpha = 0.0;
          state.cloudLayers2.push(lyr);
          // 最后一个可见因为展示最新的默认
          // if (i === datas.length - 1) {
          //   lyr.alpha = 0.75;
          //   this.currentValue = 0
          // }
          cloudsPromise.push(lyr.readyPromise);
        }
        // image加载完了之后执行
        Promise.all(cloudsPromise).then(() => {
          // this.loading = false
          // console.log("对比2演进图片加载完成!!!");
        });
      }
    };
    const getData = (data, point) => {
      state.imageListAcc = data;
      // 加载第一张
      if (state.imageListAcc.length > 0) {
        loadImages(point);
      }
    };

    const loadImages = (point) => {
      //this.loading = true
      //加载淹没数据
      const layers = window.viewer.imageryLayers;
      state.cloudLayers = [];
      const cloudsPromise = [];
      let Cesium = DC.getLib("Cesium");
      const datas = state.imageListAcc;
      // 四角不一样
      let ss = Cesium.Rectangle.fromDegrees(
        Number(point[0]),
        Number(point[1]),
        Number(point[2]),
        Number(point[3])
      );
      for (let i = 0; i < datas.length; i++) {
        const lyr = Cesium.ImageryLayer.fromProviderAsync(
          Cesium.SingleTileImageryProvider.fromUrl(datas[i].baseUrl, {
            rectangle: ss,
          })
        );

        layers.add(lyr);
        lyr.show = true;
        lyr.alpha = 0.0;
        state.cloudLayers.push(lyr);
        // 最后一个可见因为展示最新的默认
        // if (i === datas.length - 1) {
        //   lyr.alpha = 0.75;
        //   this.currentValue = 0
        // }
        cloudsPromise.push(lyr.readyPromise);
      }
      // image加载完了之后执行
      Promise.all(cloudsPromise).then(() => {
        // this.loading = false
        // console.log("演进图片加载完成!!!");
      });
    };

    const playView = async () => {
      if (state.NavigatorShow === true) {
        state.NavigatorShow = false;
        let layer = window.viewer.getLayer("NativeLine1");
        if (layer) {
          window.viewer.removeLayer(layer);
        }
      }

      state.slider === 0 &&
        state.cloudLayers.forEach((item) => {
          item.alpha = 0;
        });
      let max = 0;
      const showAlpha = 0.7;
      let Cesium = DC.getLib("Cesium");
      max = state.max;

      // 判断当前是第几个，如果是最后的那么还原
      if (state.slider === max) {
        state.slider = 0;
        state.cloudLayers[max - 1].alpha = 0;
        state.cloudLayers[state.slider].alpha = showAlpha;
      } else {
      }
      state.st = window.setInterval(() => {
        // console.log("state.slider", state.slider);

        state.viewPlay = true;
        mapUpdate(state.result);
        if (true) {
          if (state.slider === 0) {
            state.cloudLayers[state.slider].alpha = showAlpha;
            //TODO: 通过state.slider来匹配当前水位值颜色,循环查找
            if (riverFeaturesColorByOne.length > 0) {
              riverFeaturesColorByOne.forEach((color, index) => {
                color.alpha = 0;
              });
            }
            zwhLayer.forEach((dataSource) => {
              // 更换geojson的样式
              const ind = dataSource.id;

              dataSource._delegate.then((source) => {
                source.entities.values.forEach((item, index, arr) => {
                  // 修改多边形边框颜色（需要修改其他的，通过item拿到的就是entity了）
                  if (item.properties.dataDtos) {
                    // item.polyline.material = Cesium.Color.fromCssColorString(item.properties.dataDtos._value[state.slider].color);
                    riverFeaturesColorByOne[ind] =
                      new Cesium.Color.fromCssColorString(
                        item.properties.dataDtos._value[state.slider].color
                      );
                  }
                });
              });
            });
          } else {
            state.cloudLayers[state.slider - 1].alpha = 0.0;
            state.cloudLayers[state.slider].alpha = showAlpha;
            zwhLayer.forEach((dataSource) => {
              const ind = dataSource.id;
              dataSource._delegate.then((source) => {
                source.entities.values.forEach((item, index, arr) => {
                  if (
                    item.properties.dataDtos
                    // &&
                    // item.properties.dataDtos._value[state.slider].color !==
                    //   item.properties.dataDtos._value[state.slider - 1].color
                  ) {
                    riverFeaturesColorByOne[ind] =
                      new Cesium.Color.fromCssColorString(
                        item.properties.dataDtos._value[state.slider].color
                      );
                    // item.polyline.material.color._value.alpha = 0.7
                    // item.polyline.material = Cesium.Color.fromCssColorString(item.properties.dataDtos._value[state.slider].color);
                  }
                });
              });
            });
          }

          WatershedColorChange(); //流域根据预报入库流量变色事件订阅
        } else {
          this.curentStations(this.times[state.slider]);
          if (this.dispId2) {
            this.curentStations2(this.times[state.slider]);
          }
        }

        state.slider++;
        if (state.slider === max) {
          clearInterval(state.st);
          state.slider = 0;
          state.viewPlay = false;
        }
      }, 1000); //演进速度
    };
    /**
     * @author: lgy
     * @Date:
     * @note: 漳卫河流域的所有业务逻辑的初始化函数
     * @description:
     * @param {*} result  //河流信息
     * @param {*} result_second  //两个viewer的两条河流信息
     */
    const zwhbMapInit = async (result, result_second) => {
      Cesium = DC.getLib("Cesium");
      if (riverFeaturesColorByOne.length > 0) {
        riverFeaturesColorByOne = []; //清空控制图像的颜色数组
      }
      if (riverFeaturesColorByTwo_first.length > 0) {
        riverFeaturesColorByTwo_first = [];
      }
      if (riverFeaturesColorByTwo_second.length > 0) {
        riverFeaturesColorByTwo_second = [];
      }
      if (result && !result_second) {
        const id = result.data.id;
        const MapResult = await queryOneDimensionalWaterInfo(id);

        state.colorLegendOfRiver = MapResult.data.colorBands; //河流颜色图例
        state.colorLegendOfRiver.length > 0 ? (state.colorBandShow = true) : ""; //河流颜色图例可见

        state.colorLegendOfCatchment = state.stationList[0].colorBands;
        state.colorLegendOfCatchment.forEach((item) => {
          item.color = item.color.replace("0.3", "0.7");
        });
        //把河流geojson分段来
        let Line = [];
        let riverByFloodControl = zwhRiver;
        //渲染其余空间数据
        createFloodDetentionArea(window.viewer);
        //分段河流遍历
        riverByFloodControl.features.forEach((item) => {
          //每一段河流挂载上每段时间的颜色值
          MapResult.data.dtos.forEach((res) => {
            // const colorBandByBlue = [
            //   "rgba(3,255,213,0.7)",
            //   "rgba(2,255,234, 0.7)",
            //   "rgba(4,251,255, 0.7)",
            //   "rgba(0,229,255, 0.7)",
            //   "rgba(0,204,255, 0.7)",
            //   "rgba(1,183,255, 0.7)",
            //   "rgba(1,157,255, 0.7)",
            //   "rgba(3,136,255, 0.7)",
            //   "rgba(1,111,255, 0.7)",
            //   "rgba(0,88,254, 0.7)",
            // ];
            // const sort = sortColor(
            //   res.dataDtos,
            //   "waterLevel",
            //   MapResult.data.maxWaterValue,
            //   MapResult.data.minWaterValue,
            //   colorBandByBlue
            // );
            // res.dataDtos = sort.waterData;

            // averageCatchment();

            //TODO:接口精度必须和geojson保持一致
            res.lttd.split(",")[0] == item.geometry.coordinates[0][0] //多线段要素的三个数组拆分
              ? (item.properties = {
                  ...item.properties,
                  dataDtos: [...res.dataDtos],
                })
              : null;
          });
          riverFeaturesColorByOne.push(
            new DC.Color.fromCssColorString("rgba(24,144,255,0)")
          ); //9.25 初始化颜色变成全透明
          Line.push(item);
        });
        let box = [];

        //现在可用river1GroupLayer来管理地图上的所有河流，方便模块转换后控制河流显隐
        let river1GroupLayer = window.viewer.getLayerGroup("river1");
        if (river1GroupLayer) {
          river1GroupLayer.show = false;
        }
        let river1GroupLayer_zwh = window.viewer.getLayerGroup("river_zwh");
        if (river1GroupLayer_zwh) {
          river1GroupLayer_zwh.remove();
        }
        river1GroupLayer_zwh = new DC.LayerGroup("river_zwh");
        window.viewer.addLayerGroup(river1GroupLayer_zwh);

        Line.forEach((element, ind) => {
          let river1layer = new DC.GeoJsonLayer(ind, element, {
            stroke: new Cesium.CallbackProperty(() => {
              return riverFeaturesColorByOne[ind];
            }, false),

            strokeWidth: 15,
          });
          river1GroupLayer_zwh.addLayer(river1layer);
          // river1layer.eachOverlay((item) => {
          //   //item 为一个entirey对象

          //   // 创建一个水面材质
          //   const waterMaterial = new Cesium.MaterialProperty({
          //     fabric: {
          //       type: "Water",
          //       uniforms: {
          //         normalMap: Cesium.buildModuleUrl(
          //           "/Assets/Textures/waterNormals.jpg"
          //         ),
          //         frequency: 10000.0,
          //         animationSpeed: 0.01,
          //         amplitude: 1.0,
          //       },
          //     },
          //   });
          //   item.ellipse = {
          //     semiMinorAxis: 400000.0,
          //     semiMajorAxis: 400000.0,
          //     material: waterMaterial,
          //   };
          // });
          box = [...box, river1layer];
        });
        zwhLayer = box; //将zwh提取到全局以便进行颜色管理

        addNavatiePoi(
          Cesium,
          window.viewer,
          [115.09364, 36.23543],
          [114.93195, 36.39257]
        );
        if (zwhLayer) {
          state.RiverLoading.close();
          state.RiverLoading = null;
        }
      } else {
        /*
          对比分析 业务 初始化逻辑
          result_second是对比的河流，result是当前河流
        */
        const MapResult = await queryOneDimensionalWaterInfo(result.data.id);
        const MapResult_second = await queryOneDimensionalWaterInfo(
          result_second.data.id
        );
        //渲染其余空间数据
        createFloodDetentionArea(window.viewer);
        window.viewer2 && createFloodDetentionArea(window.viewer2);

        //把河流geojson分段
        let Line = [];
        let Line_second = [];
        let firstRiverBySplitScreen = zwhRiver;
        let secondRiverBySplitScreen = zwhRiver;
        const colorBandByBlue = [
          "rgba(3,255,213,0.7)",
          "rgba(2,255,234, 0.7)",
          "rgba(4,251,255, 0.7)",
          "rgba(0,229,255, 0.7)",
          "rgba(0,204,255, 0.7)",
          "rgba(1,183,255, 0.7)",
          "rgba(1,157,255, 0.7)",
          "rgba(3,136,255, 0.7)",
          "rgba(1,111,255, 0.7)",
          "rgba(0,88,254, 0.7)",
        ];
        firstRiverBySplitScreen.features.forEach((item) => {
          MapResult.data.dtos.forEach((res) => {
            //TODO:先将颜色rgb匹配水文高度

            // res.dataDtos = sortColor(
            //   res.dataDtos,
            //   "waterLevel",
            //   MapResult.data.maxWaterValue,
            //   MapResult.data.minWaterValue,
            //   colorBandByBlue
            // ).waterData;
            //TODO:接口精度必须和geojson保持一致
            res.lttd.split(",")[0] == item.geometry.coordinates[0][0] //多线段要素的三个数组拆分
              ? (item.properties = {
                  ...item.properties,
                  dataDtos: [...res.dataDtos],
                })
              : null;
          });
          riverFeaturesColorByTwo_first.push(
            new DC.Color.fromCssColorString("rgba(24,144,255,0)")
          ); //9.25 初始化颜色变成全透明
          Line.push(item);
        });
        secondRiverBySplitScreen.features.forEach((item) => {
          MapResult_second.data.dtos.forEach((res) => {
            //TODO:先将颜色rgb匹配水文高度
            // res.dataDtos = sortColor(
            //   res.dataDtos,
            //   "waterLevel",
            //   MapResult_second.data.maxWaterValue,
            //   MapResult_second.data.minWaterValue,
            //   colorBandByBlue
            // ).waterData;
            //TODO:接口精度必须和geojson保持一致
            res.lttd.split(",")[0] == item.geometry.coordinates[0][0] //多线段要素的三个数组拆分
              ? (item.properties = {
                  ...item.properties,
                  dataDtos: [...res.dataDtos],
                })
              : null;
          });
          riverFeaturesColorByTwo_second.push(
            new DC.Color.fromCssColorString("rgba(24,144,255,0)")
          ); //9.25 初始化颜色变成全透明
          Line_second.push(item);
        });
        // averageCatchmentByViewer2();

        let box = [];
        let box_second = [];
        //将分屏对比的两屏的初始化river1图层隐藏
        let river1GroupLayer = window.viewer.getLayerGroup("river1");
        if (river1GroupLayer) {
          river1GroupLayer.show = false;
        }
        let river2GroupLayer = window.viewer2.getLayerGroup("river1");
        if (river2GroupLayer) {
          river2GroupLayer.show = false;
        }
        //将两屏的上次渲染的业务河流图层清除
        let riverGroupLayer_river_zwh_1 =
          window.viewer.getLayerGroup("river_zwh");
        if (riverGroupLayer_river_zwh_1) {
          riverGroupLayer_river_zwh_1.remove();
        }

        let riverGroupLayer_river_zwh_2 =
          window.viewer2.getLayerGroup("river_zwh_2");
        if (riverGroupLayer_river_zwh_2) {
          riverGroupLayer_river_zwh_2.remove();
        }

        let SplitScreenLayer = new DC.LayerGroup("river_zwh");
        let SplitScreenLayer_second = new DC.LayerGroup("river_zwh_2");
        window.viewer.addLayerGroup(SplitScreenLayer);
        window.viewer2.addLayerGroup(SplitScreenLayer_second);

        Line.forEach((element, ind) => {
          let river1layer = new DC.GeoJsonLayer(`${ind}`, element, {
            stroke: new Cesium.CallbackProperty(() => {
              return riverFeaturesColorByTwo_first[ind];
            }, false),
            fill: new Cesium.CallbackProperty(() => {
              return riverFeaturesColorByTwo_first[ind];
            }, false),
            strokeWidth: 15,
          });
          SplitScreenLayer.addLayer(river1layer);
          box = [...box, river1layer];
        });
        Line_second.forEach((element, ind) => {
          let river1layer = new DC.GeoJsonLayer(`${ind}`, element, {
            stroke: new Cesium.CallbackProperty(() => {
              return riverFeaturesColorByTwo_second[ind];
            }, false),
            fill: new Cesium.CallbackProperty(() => {
              return riverFeaturesColorByTwo_second[ind];
            }, false),
            strokeWidth: 15,
          });
          SplitScreenLayer_second.addLayer(river1layer);
          box_second = [...box_second, river1layer];
        });
        zwhSplitScreenLayer = box; //将zwh提取到全局以便进行颜色管理
        zwhSplitScreenLayer_second = box_second; //将zwh提取到全局以便进行颜色管理
        if (zwhSplitScreenLayer && zwhSplitScreenLayer_second) {
          state.RiverLoading.close();
          state.RiverLoading = null;
        }
      }
    };

    /**
     * @author: lgy 👽
     * @Date: 16点51分
     * @note: 验证蓄滞洪区的驾车避让技术可行性
     * @description: 读取每个小时的蓄滞洪区geojson，调用高德驾车规划及传入避让参数，获取驾车路线，并绘制到地图上
     * @param {*} slider  //当前的slider值
     * @param {*} insideCoordinates  //危险区坐标
     * @param {*} outsideCoordinates  //安置点坐标
     */
    const driveAround = async (
      slider,
      insideCoordinates = "115.09364,36.23543",
      outsideCoordinates = "114.93195,36.39257"
    ) => {
      //使用fetch读取本地geojson文件
      if (slider >= 24 && slider <= 144) {
        let thisHourGeojson = [];
        let thisHourRodeLine = [];
        let layer = window.viewer.getLayer("NativeLine1");

        if (layer) {
          window.viewer.removeLayer(layer);
        }
        await fetch(`/datas/outjson2/R2DC${slider}.json`)
          .then((response) => response.json())
          .then((geojson) => {
            let deleteHighth = [];
            let area = turf.area(geojson);
            console.log(area);
            if (area < 81000000) {
              geojson.features[0].geometry.type == "Polygon" &&
                geojson.features[0].geometry.coordinates.forEach((shp) => {
                  shp.forEach((coord, ind) => {
                    ind !== shp.length - 1 &&
                      deleteHighth.push([coord[0], coord[1]]);
                  });
                });
              geojson.features[0].geometry.type == "MultiPolygon" &&
                geojson.features[0].geometry.coordinates[0].forEach((shp) => {
                  shp.forEach((coord, ind) => {
                    ind !== shp.length - 1 &&
                      deleteHighth.push([coord[0], coord[1]]);
                  });
                });
              thisHourGeojson = deleteHighth.join(";");
            } else {
              alert("蓄滞洪区面积超过81平方公里，无法进行避让");
            }
          });
        if (thisHourGeojson.length == 0) {
          return;
        }
        thisHourGeojson.length > 0 &&
          (await fetch(
            `https://restapi.amap.com/v5/direction/driving?origin=${insideCoordinates}&destination=${outsideCoordinates}&key=13ad85a2a3e380b64eaca45e74b6d541&show_fields=polyline&avoidpolygons=${thisHourGeojson}`
          )
            .then((res) => res.json())
            .then((road) => {
              console.log(Math.floor(Math.random() * road.route.paths.length));
              if (road.status === "1") {
                road.route.paths[
                  Math.floor(Math.random() * road.route.paths.length)
                ].steps.forEach((step) => {
                  let variable = step.polyline
                    .split(";")
                    .map((item) =>
                      item.split(",").map((coordinate) => Number(coordinate))
                    );
                  // console.log(variable)
                  thisHourRodeLine = [...thisHourRodeLine, ...variable];
                });
              } else {
                alert("高德api调用失败");
              }
            }));
        //  console.log(thisHourRodeLine);
        if (thisHourRodeLine.length > 0) {
          let addToMapRoad = turf.lineString(thisHourRodeLine, {
            name: "line1",
          });
          layer = new DC.GeoJsonLayer("NativeLine1", addToMapRoad, {
            stroke: new DC.Color.fromCssColorString("rgba(14,219,36,0.7)"), //填充颜色
            strokeWidth: 5, //线宽
          });
          window.viewer.addLayer(layer);
          let heading = Cesium.Math.toRadians(100);
          let pitch = 0;
          let roll = 0;
          console.log(heading);
          window.viewer.flyTo(layer);
        } else {
          alert("当前时间调用高德服务暂无路线");
        }
      } else {
        alert("当前时间暂无蓄滞洪区");
      }
    };

    const addNavatiePoi = (Cesium, viewer, startPoi, endPoi) => {
      let flag = false;
      window.viewer.entities.values.forEach((entitie) => {
        if (
          entitie.id === "startPoi_1" ||
          entitie.id === "startPoi_2" ||
          entitie.id === "startPoi_3" ||
          entitie.id === "endPoi"
        ) {
          entitie.show = true;
          flag = true;
        }
      });
      if (!flag) {
        const billboardEntity_startPoi_1 = new Cesium.Entity({
          id: "startPoi_1",
          position: Cesium.Cartesian3.fromDegrees(startPoi[0], startPoi[1]),
          billboard: {
            image: "/icons/marker/startPoi.png",
            scale: 0.75,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
          label: {
            text: "危险区1",
            font: "20px sans-serif",
            fillColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -50),
          },
          show: true,
        });
        viewer.entities.add(billboardEntity_startPoi_1);

        const billboardEntity_startPoi_2 = new Cesium.Entity({
          id: "startPoi_2",
          position: Cesium.Cartesian3.fromDegrees(115.147985, 36.286406),
          billboard: {
            image: "/icons/marker/startPoi.png",
            scale: 0.75,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
          label: {
            text: "危险区2",
            font: "20px sans-serif",
            fillColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -50),
          },
          show: true,
        });
        viewer.entities.add(billboardEntity_startPoi_2);

        const billboardEntity_startPoi_3 = new Cesium.Entity({
          id: "startPoi_3",
          position: Cesium.Cartesian3.fromDegrees(115.185455, 36.269926),
          billboard: {
            image: "/icons/marker/startPoi.png",
            scale: 0.75,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
          label: {
            text: "危险区3",
            font: "20px sans-serif",
            fillColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -50),
          },
          show: true,
        });
        viewer.entities.add(billboardEntity_startPoi_3);

        const billboardEntity_endPoi = new Cesium.Entity({
          id: "endPoi",
          position: Cesium.Cartesian3.fromDegrees(endPoi[0], endPoi[1]),
          billboard: {
            image: "/icons/marker/endPoi.png",
            scale: 0.75,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
          label: {
            text: "安置点",
            font: "20px sans-serif",
            fillColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -50),
          },
          show: true,
        });
        viewer.entities.add(billboardEntity_endPoi);

        let scene = viewer.scene;
        let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
        handler.setInputAction(function (movement) {
          let pick = viewer.scene.pick(movement.position);
          if (Cesium.defined(pick)) {
            switch (pick.id.id) {
              case "startPoi_1":
                driveAround(state.slider, "115.09364,36.23543");

                break;

              case "startPoi_2":
                driveAround(state.slider, "115.147985,36.286406");

                break;

              case "startPoi_3":
                driveAround(state.slider, "115.185455,36.269926");

                break;

              default:
                break;
            }
          }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      }
    };

    const WatershedColorChange = () => {
      window.EventBus.$emit(
        "catchmentFillColor_viewer/update",
        new DC.Color.fromCssColorString(
          state.stationList[0].vos[state.slider].color
        )
      );
    };
    const WatershedColorChangeByViewer2 = () => {
      window.EventBus.$emit(
        "catchmentFillColor_viewer/update",
        new DC.Color.fromCssColorString(
          state.stationList2[0].vos[state.slider2].color
        )
      );
      window.EventBus.$emit(
        "catchmentFillColor_viewer2/update",
        new DC.Color.fromCssColorString(
          state.stationList2[0].vos[state.slider2].color
        )
      );
    };
    const averageCatchment = () => {
      let min = null;
      let max = null;
      const colorBandByRed = [
        "rgba(2, 255, 0, 0.3)",
        "rgba(54, 255, 3, 0.3)",
        "rgba(112, 255, 2, 0.3)",
        "rgba(170, 255, 1, 0.3)",
        "rgba(224, 255, 3, 0.3)",
        "rgba(255, 225, 0, 0.3)",
        "rgba(254, 170, 1, 0.3)",
        "rgba(254, 111, 0, 0.3)",
        "rgba(255, 55, 1, 0.3)",
        "rgba(255, 1, 0, 0.3)",
      ];
      state.stationList.forEach((item, index) => {
        //计算出所有vos的qi值中的最大和最小值
        state.stationList[index].vos.forEach((vos) => {
          if (!vos.qi) {
            return;
          } else {
            if (min == null || vos.qi < min) {
              min = vos.qi;
            }
            if (max == null || vos.qi > max) {
              max = vos.qi;
            }
          }
        });
        if (min && max) {
          const sort = sortColor(item.vos, "qi", max, min, colorBandByRed);
          item.vos = sort.waterData;

          state.colorLegendOfCatchment = sort.allData;
          state.colorLegendOfCatchment.forEach((item) => {
            item.color = item.color.replace("0.3", "0.7");
          });
        }
      });
    };
    const averageCatchmentByViewer2 = () => {
      let min = null;
      let max = null;
      const colorBandByRed = [
        "rgba(2, 255, 0, 0.3)",
        "rgba(54, 255, 3, 0.3)",
        "rgba(112, 255, 2, 0.3)",
        "rgba(170, 255, 1, 0.3)",
        "rgba(224, 255, 3, 0.3)",
        "rgba(255, 225, 0, 0.3)",
        "rgba(254, 170, 1, 0.3)",
        "rgba(254, 111, 0, 0.3)",
        "rgba(255, 55, 1, 0.3)",
        "rgba(255, 1, 0, 0.3)",
      ];
      state.stationList2.forEach((item, index) => {
        //计算出所有vos的qi值中的最大和最小值
        state.stationList2[index].vos.forEach((vos) => {
          if (!vos.qi) {
            return;
          } else {
            if (min == null || vos.qi < min) {
              min = vos.qi;
            }
            if (max == null || vos.qi > max) {
              max = vos.qi;
            }
          }
        });
        if (min && max) {
          const sort = sortColor(item.vos, "qi", max, min, colorBandByRed);
          item.vos = sort.waterData;
        }
      });
    };
    const showUE = () => {
      state.centerUEDialogVisible = true;
    };
    const pauseView = () => {
      state.viewPlay = false;
      if (state.st) {
        clearInterval(state.st);
      }
      if (state.slider >= 24 && state.slider <= 144) {
        state.NavigatorShow = true;
      }
    };

    const playDoubleView = () => {
      //执行俩个对比任务进行
      state.cloudLayers.forEach((item) => {
        item.alpha = 0;
      });
      state.cloudLayers2.forEach((item) => {
        item.alpha = 0;
      });
      let max;
      let max2 = 0;
      const showAlpha = 0.7;
      let Cesium = DC.getLib("Cesium");
      max2 = state.max2;
      // 判断当前是第几个，如果是最后的那么还原
      if (state.slider2 === max2) {
        state.slider2 = 0;
        state.cloudLayers[max2 - 1].alpha = 0;
        state.cloudLayers[state.slider2].alpha = showAlpha;
        state.cloudLayers2[max2 - 1].alpha = 0;
        state.cloudLayers2[state.slider2].alpha = showAlpha;
      }
      state.st = window.setInterval(() => {
        state.viewPlay2 = true;
        addMapChange(state.result);
        addMapChange2(state.result2);
        if (true) {
          if (state.slider2 === 0) {
            state.cloudLayers[state.slider2].alpha = showAlpha;
            state.cloudLayers2[state.slider2].alpha = showAlpha;
            zwhSplitScreenLayer.forEach((dataSource) => {
              const ind = dataSource.id;
              dataSource._delegate.then((source) => {
                source.entities.values.forEach((item, index, arr) => {
                  // 修改多边形边框颜色（需要修改其他的，通过item拿到的就是entity了）

                  if (item.properties.dataDtos) {
                    // item.polyline.material = Cesium.Color.fromCssColorString(
                    //   item.properties.dataDtos._value[state.slider2].color
                    // );
                    // item.polyline.show = true;
                    // item.polyline.allowDrillPicking = true;
                    // item.polyline.material.color._value.alpha = 0.7;
                    riverFeaturesColorByTwo_first[ind] =
                      new Cesium.Color.fromCssColorString(
                        item.properties.dataDtos._value[state.slider2].color
                      );
                  }
                });
              });
            });
            zwhSplitScreenLayer_second.forEach((dataSource) => {
              const ind = dataSource.id;
              dataSource._delegate.then((source) => {
                source.entities.values.forEach((item, index, arr) => {
                  // 修改多边形边框颜色（需要修改其他的，通过item拿到的就是entity了）

                  if (item.properties.dataDtos) {
                    // item.polyline.material = Cesium.Color.fromCssColorString(
                    //   item.properties.dataDtos._value[state.slider2].color
                    // );
                    // item.polyline.show = true;
                    // item.polyline.allowDrillPicking = true;
                    // item.polyline.material.color._value.alpha = 0.7;
                    riverFeaturesColorByTwo_second[ind] =
                      new Cesium.Color.fromCssColorString(
                        item.properties.dataDtos._value[state.slider2].color
                      );
                  }
                });
              });
            });
          } else {
            if (!state.cloudLayers[state.slider2]) {
            } else {
              state.cloudLayers[state.slider2 - 1].alpha = 0.0;
              state.cloudLayers[state.slider2].alpha = showAlpha;
            }
            if (!state.cloudLayers2[state.slider2]) {
            } else {
              state.cloudLayers2[state.slider2 - 1].alpha = 0.0;
              state.cloudLayers2[state.slider2].alpha = showAlpha;
            }

            zwhSplitScreenLayer.forEach((dataSource) => {
              const ind = dataSource.id;
              dataSource._delegate.then((source) => {
                source.entities.values.forEach((item, index, arr) => {
                  // 修改多边形边框颜色（需要修改其他的，通过item拿到的就是entity了）

                  if (
                    item.properties.dataDtos &&
                    !item.properties.dataDtos._value[state.slider2]
                  ) {
                    return false;
                  } else {
                    if (
                      item.properties.dataDtos &&
                      item.properties.dataDtos._value[state.slider2].color !==
                        item.properties.dataDtos._value[state.slider2 - 1].color
                    ) {
                      // item.polyline.material = Cesium.Color.fromCssColorString(
                      //   item.properties.dataDtos._value[state.slider2].color
                      // );
                      // item.polyline.show = true;
                      // item.polyline.allowDrillPicking = true;
                      // item.polyline.material.color._value.alpha = 0.7;
                      riverFeaturesColorByTwo_first[ind] =
                        new Cesium.Color.fromCssColorString(
                          item.properties.dataDtos._value[state.slider2].color
                        );
                    }
                  }
                });
              });
            });
            zwhSplitScreenLayer_second.forEach((dataSource) => {
              const ind = dataSource.id;
              dataSource._delegate.then((source) => {
                source.entities.values.forEach((item, index, arr) => {
                  // 修改多边形边框颜色（需要修改其他的，通过item拿到的就是entity了）

                  if (
                    item.properties.dataDtos &&
                    !item.properties.dataDtos._value[state.slider2]
                  ) {
                    return false;
                  } else {
                    if (
                      item.properties.dataDtos &&
                      item.properties.dataDtos._value[state.slider2].color !==
                        item.properties.dataDtos._value[state.slider2 - 1].color
                    ) {
                      // item.polyline.material = Cesium.Color.fromCssColorString(
                      //   item.properties.dataDtos._value[state.slider2].color
                      // );
                      // item.polyline.show = true;
                      // item.polyline.allowDrillPicking = true;
                      // item.polyline.material.color._value.alpha = 0.7;
                      riverFeaturesColorByTwo_second[ind] =
                        new Cesium.Color.fromCssColorString(
                          item.properties.dataDtos._value[state.slider2].color
                        );
                    }
                  }
                });
              });
            });
            WatershedColorChangeByViewer2();
          }
        } else {
          this.curentStations(this.times[state.slider]);
          if (this.dispId2) {
            this.curentStations2(this.times[state.slider]);
          }
        }

        state.slider2++;
        if (state.slider2 === max2) {
          clearInterval(state.st);
          state.slider2 = 0;
          state.viewPlay2 = false;
        }
      }, 1000); //演进速度
    };
    const pauseDoubleView = () => {
      state.viewPlay2 = false;
      if (state.st) {
        clearInterval(state.st);
      }
    };
    const createPlan = () => {
      state.createShow = true;
    };
    const handleSelectionChange = (e) => {
      state.selectedList = e;
      state.multiple = e.length != 2;
    };
    const handleSet = async (row) => {
      try {
        const res = await dispFaEnable({
          dispId: row.ID,
          enable: "1",
          istrue: "0",
        });
        if (res.code == 200) {
          ElMessage.success("设置成功");
          queryData();
        }
        throw res;
      } catch (res) {
        ElMessageBox.confirm(
          "当前已有执行方案,是否将此方案代替老方案设置为新的执行方案?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            dispFaEnable({
              dispId: row.ID,
              enable: "1",
              istrue: "1",
            }).then((res) => {
              if (res.code == 200) {
                ElMessage.success("设置成功");
                queryData();
              }
            });
          })
          .catch(() => {});
      }
    };
    const detailFalse = () => {
      state.detailShow = false;
      window.EventBus.$emit("bubble_vanish");
    };
    const createFalse = () => {
      state.infoData1 = {};
      state.createShow = false;
      queryData();
    };
    // 对比分析
    const contrast = () => {
      appStore.setCtstList(state.selectedList);
      window.EventBus.$emit("sliderHide");
      window.EventBus.$emit("module/type/change", { name: "对比分析" });
      handDoubleClick(state.selectedList[0], state.selectedList[1]);
      // 同步地图联动
      // window.EventBus.$emit('floodPreRehearsal/dispatch/select', state.selectedList[0])
      // window.EventBus.$emit('floodPreRehearsal/dispatch/select2', state.selectedList[1])
    };

    const backList = () => {
      state.txtShow = false;
      pauseDoubleView();
      state.cloudLayers.forEach((item) => {
        item.alpha = 0;
      });
      state.cloudLayers2.forEach((item) => {
        item.alpha = 0;
      });
      let layer2 = window.viewer2.getLayer("rainViewer");
      if (layer2) {
        layer2.clear();
      }
      let layer = window.viewer.getLayer("rainViewer");
      if (layer) {
        layer.clear();
      }
      state.sliderShow2 = false;
      state.slider2 = 0;

      window.EventBus.$emit("sliderShow");
      window.viewer.flyToPosition(
        new DC.Position(
          118.64482064959539,
          36.1759707289059,
          575497.5787557325,
          0,
          -90
        ),
        null,
        3
      );
      window.viewer2.flyToPosition(
        new DC.Position(
          118.64482064959539,
          36.1759707289059,
          575497.5787557325,
          0,
          -90
        ),
        null,
        3
      );
    };
    const backAdjust = () => {
      state.txtShow = false;
      pauseView();
      state.cloudLayers.forEach((item) => {
        item.alpha = 0;
      });
      let rainViewer = window.viewer.getLayer("rainViewer");
      if (rainViewer) {
        rainViewer.clear();
      }
      state.detailShow = false;
      state.sliderShow = false;
      state.slider = 0;

      let layer2 = window.viewer.getLayer("xiongjiachang-layer");
      layer2.clear();
      // window.viewer.flyToPosition(
      //   new DC.Position(
      //     118.64482064959539,
      //     36.1759707289059,
      //     575497.5787557325,
      //     0,
      //     -90
      //   ),
      //   null,
      //   3
      // );
    };
    return {
      ...toRefs(state),
      queryData,
      handleClick,
      createPlan,
      handleSelectionChange,
      moment,
      handleSet,
      contrast,
      backAdjust,
      detailFalse,
      createFalse,
      transformDataForTreeSelect,
      resetQuery,
      getList,
      playView,
      pauseView,
      playDoubleView,
      pauseDoubleView,
      closeMask,
      driveAround,
      sliderChange,
      sliderChange2,
      showUE,
    };
  },
});
</script>
<style scoped lang="scss">
.table-range {
  position: fixed;
  top: 73px;
  left: 0;
  width: 100%;
  bottom: 0;
  z-index: 99;
  background-color: #fff;
}

.hedgingBox {
  width: 100%;
  color: #fff;
  padding: 10px;
  height: 100%;
  // border: 1px solid #1c8bda;
  // background-color: rgba(1, 28, 70, 0.7);
  position: relative;

  .title {
    color: #8cd2ff;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .cont {
    width: 100%;
    height: calc(100% - 30px);
    background: #022757;
    padding: 10px;

    .form {
      :deep(.el-form) {
        display: flex;
        flex-wrap: wrap;

        .el-form-item {
          width: 50%;

          .el-form-item__label {
            font-size: 14px;
            color: #fff;
          }
        }
      }
    }

    .button {
      padding: 0 0 10px;
      display: flex;
      justify-content: flex-end;
      border-bottom: 1px solid #00acff;
      margin-bottom: 10px;
    }
  }

  .tableHeight {
    width: 100%;
    height: calc(100vh - 370px);
    // height: 500px;
  }

  .rightoperate {
    color: #fff;
    cursor: pointer;

    &:hover {
      color: #00acff;
      text-decoration: underline;
    }
  }

  .ywoperate {
    color: #ffce2c;
  }
}

.box-border {
  background: rgba(0, 57, 115, 0);
  position: fixed;

  top: 73px;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid #1c8bda;
  width: auto;
  height: auto;
  padding: 6px;
  z-index: 1000 !important;
}

.box-border span:nth-child(1) {
  position: absolute;
  left: 0;
  top: 0;
  padding: 10px;
  border-style: solid;
  border-color: #00ccff;
  border-width: 2px 0 0 2px;
}

.box-border span:nth-child(2) {
  position: absolute;
  right: -1px;
  top: -1px;
  padding: 10px;
  border-style: solid;
  border-color: #00ccff;
  border-width: 2px 2px 0 0;
}

.box-border span:nth-child(3) {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 10px;
  border-style: solid;
  border-color: #00ccff;
  border-width: 0 2px 2px 0;
}

.box-border span:nth-child(4) {
  position: absolute;
  left: 0;
  bottom: -1px;
  padding: 10px;
  border-style: solid;
  border-color: #00ccff;
  border-width: 0 0 2px 2px;
}

.box1 {
  position: absolute;
  width: 1000px;
  left: 50%;
  margin-left: -500px;
  margin-top: 4%;
  color: #ccebff;
  //background-color: #00356c;
  //border: 1px solid #0091fd;

  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    padding: 5px 10px 0;

    .close {
      width: 14px;
      height: 14px;
      background-image: url(../image/close.png);
      background-size: 100% 100%;
      cursor: pointer;
    }
  }

  .line {
    margin: 10px 0 0;
    padding: 0 10px;

    .left,
    .right {
      /* flex:0 0 100px; */
      width: 10px;
      height: 2px;
      background-color: #ffd154;
    }
  }

  .achievement {
    padding: 10px;
  }

  .charts {
    display: flex;

    .item {
      width: 50%;
      padding: 0 10px 10px;

      .leftEcharts {
        width: 100%;
        height: 300px;
      }

      .rightEcharts {
        width: 100%;
        height: 300px;
      }
    }
  }
}

:deep(.productTable) {
  background-color: transparent !important;
  margin: 10px 0 20px;

  tr {
    background-color: transparent !important;

    th {
      background-color: #074999 !important;
      color: #bde4ff !important;
      border-bottom: 1px solid #17365a;
      border-color: #005e9d !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #005e9d;
      border-color: #005e9d !important;
    }
  }

  --el-table-row-hover-bg-color: transparent;
  --el-table-border-color: #005e9d;
  --el-bg-color: transparent;

  .el-table__inner-wrapper {
    &::before {
      background-color: #005e9d;
    }
  }
}

:deep(.productTable) {
  background-color: transparent !important;
  margin: 10px 0 20px;

  tr {
    background-color: transparent !important;

    th {
      background-color: #083f86 !important;
      color: #fff !important;
      border-bottom: 1px solid #17365a;
      border-color: #005e9d !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #005e9d;
      border-color: #005e9d !important;

      &.el-table-fixed-column--right {
        background-color: #022757 !important;
      }
    }
  }

  --el-table-border-color: #005e9d;
  //--el-bg-color:transparent;

  .el-table__inner-wrapper {
    &::before {
      background-color: #005e9d;
    }
  }
}

.double {
  width: 100vw;
  height: 100vh;
  position: relative;
  background: #000c2f;
  z-index: 998;
}

.map-main {
  width: 100%;
  height: 100%;
  display: inline-block;
  position: relative;
}

.width51 {
  position: fixed;
  left: 50%;
  width: 4px;
  background: #fff;
  top: 64px;
  bottom: 0;
  height: 100%;
  z-index: 10;
}

.duibiBtn {
  right: 50px;
  top: 30px;
  position: absolute;
}

.width50 {
  width: 50%;
  /* border-bottom: 275px solid #000c2f; */
}

.borderColor {
  :deep(.el-input__wrapper) {
    border-color: #409eff;
    // box-shadow: none;
    background-color: transparent;
    --el-input-border-color: #409eff;

    .el-input__inner {
      color: #fff;
    }
  }
}

:deep(.el-date-editor) {
  border: 1px solid #409eff;
  background-color: transparent !important;
  box-shadow: 0 0 0 1px #409eff;
  color: #fff;

  .el-input__wrapper {
    background-color: transparent;
    box-shadow: 0 0 0 1px #409eff;

    .el-input__inner {
      color: #a3deff;
    }
  }
}

:deep(.el-date-editor .el-range__icon) {
  height: inherit;
  font-size: 14px;
  color: #a3deff;
  float: left;
}

:deep(.el-date-editor .el-range-input) {
  color: #a3deff;
}

:deep(.el-date-editor .el-range-separator) {
  color: rgba(255, 255, 255, 0.9);
}

:deep(.el-table__body tr:hover > td.el-table__cell) {
  background-color: #033c68 !important;
  cursor: pointer;
}

:deep(.el-slider__marks-text) {
  color: #a3deff;
}

:deep(.el-slider__bar) {
  background: linear-gradient(90deg, #1b83f1, #0cc3f8);
}

:deep(.el-slider__stop) {
  color: #a3deff;
  background-color: #409eff;
}

:deep(.el-slider__button) {
  width: 10px;
  height: 10px;
}

.lenged-fix3 {
  position: fixed;
  display: flex;
  align-items: center;
  left: 50%;
  top: 80%;
  bottom: 50px;
  padding: 10px 30px 10px 12px;
  width: 760px;
  margin-left: -400px;
  margin-top: -40px;
  height: 63px;
  background: rgba(0, 78, 196, 0.3);
  border: 1px solid #2ab3fc;
  border-radius: 4px;
  z-index: 999;
}

.fix4-box {
  position: fixed;

  right: 27.5%;
  bottom: 10%;
  line-height: 40px;
  font-size: 24px;
  padding: 10px 30px 10px 12px;
  z-index: 999;
}
.NavigatorButton {
  position: fixed;
  left: 55%;
  top: 15%;
}
.colorBand {
  position: fixed;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  left: 88%;
  top: calc(100vh - 350px);
  // bottom: 50px;
  padding: 10px 30px 10px 12px;
  width: 120px;
  margin-left: -400px;
  margin-top: -40px;
  // height: 200px;
  z-index: 999;
  // background: rgba(0, 78, 196, 0.3);
  // border: 1px solid #2ab3fc;

  .colorBand-title {
    margin-bottom: 15px;
  }
  .colorBand-item-text {
    text-align: center;
  }
  .colorBand-item-color {
    width: 30px;
    height: 10px;
    // border-radius: 4px;
    margin: 2px 20px;
  }
}
.colorBandByCatchment {
  position: fixed;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  left: 85%;
  top: calc(100vh - 350px);
  // bottom: 50px;
  padding: 10px 30px 10px 12px;
  width: 120px;
  margin-left: -400px;
  margin-top: -40px;
  // height: 400px;
  z-index: 999;
  // background: rgba(0, 78, 196, 0.3);
  // border: 1px solid #2ab3fc;

  .colorBand-title {
    margin-bottom: 15px;
  }
  .colorBand-item-text {
    text-align: center;
  }
  .colorBand-item-color {
    width: 30px;
    height: 10px;
    // border-radius: 4px;
    margin: 2px 20px;
  }
}
.lenged-fix {
  position: fixed;
  display: flex;
  align-items: center;
  left: 590px;
  bottom: 34px;
  padding: 10px 30px 10px 12px;
  width: 34%;
  height: 63px;
  z-index: 20;
  background: rgba(0, 78, 196, 0.3);
  border: 1px solid #2ab3fc;
  border-radius: 4px;
}
</style>
<style>
.area-item {
  margin-left: -60px;
  margin-bottom: -15px;
  line-height: 22px;
  padding: 10px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
  z-index: 1;
}

.popHr {
  height: 2px;
  border: 0;
  margin: 2px;
  width: 100%;
  background: linear-gradient(to right, #0ec2fe 0%, rgb(255 255 255 / 0%) 100%);
}
.dialogClass {

  header {
    padding: 0;
  }
  .el-dialog__body {
    height: 90vh;
    padding: 0;
  }

}
</style>