<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import useUserStore from "@/store/modules/user";
import usePanelStore from "@/store/modules/panel";
import moment from "moment";

defineOptions({
  name: "AgentHead",
});

// 定义props
const props = defineProps({
  flag: {
    type: String,
    default: ""
  }
});

const router = useRouter();
const userStore = useUserStore();
const panelStore = usePanelStore();

const userName = ref("");
const currentTime = ref("");
const timer = ref(null);

const handleSystem = () => {
  router.push("/warning/warningIssue");
};

// 处理工程安全按钮点击
const handleSafePanel = () => {
  panelStore.switchToSafe();
};

// 处理全景按钮点击
const handleDefaultPanel = () => {
  panelStore.switchToDefault();
};

// 处理防洪调度按钮点击
const handleFloodPanel = () => {
  // 暂时切换到默认面板，后续可以创建专门的防洪调度面板
  panelStore.switchToDefault();
};

const updateTime = () => {
  currentTime.value = moment().format("YYYY年MM月DD日 HH:mm:ss");
};

onMounted(() => {
  userName.value = userStore.name;
  updateTime();
  timer.value = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  timer.value && clearInterval(timer.value);
});
</script>

<template>
  <div class="head">
    <div class="right-menu">
      <!-- MXScene场景下的三个按钮 -->
      <div v-if="props.flag === 'MXScene'" class="mx-buttons">
        <div class="mx-btn" @click="handleDefaultPanel">全景</div>
        <div class="mx-btn" @click="handleFloodPanel">防洪调度</div>
        <div class="mx-btn" @click="handleSafePanel">工程安全</div>
      </div>
      <div class="center" @click="handleSystem">应用中心</div>
      <div class="name">{{ userName }}</div>
      <div class="time">{{ currentTime }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: flex-end;
  color: #fff;
  background: url(../image/screen-header.png) no-repeat center center;
  background-size: 100% 100%;
  // font-family: SourceHanSansCN-Regular Source Han Sans CN;
  font-family: Microsoft YaHei, Arial, Helvetica, sans-serif;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;

  .title {
    width: 340px;
    // height: 34px;
    //字体间隔
    letter-spacing: 4px;
    font-size: 35px;
    font-weight: 600;
    background-image: -webkit-linear-gradient(bottom, white, #05adef);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 60px;
    margin-left: 40px;
  }

  .tabs {
    width: 30%;
    height: 70%;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .items {
      font-size: 20px;
      line-height: 50px;
      // width: 33%;
      height: 100%;
      color: #7dbfec;
      cursor: pointer;
      text-align: center;
      width: 100px;
      border-bottom: 3px solid #1095ff;

      &.act {
        color: #fff;
        border-bottom: 3px solid #f9ce4d;
      }
    }
  }

  .right-menu {
    height: 56px;
    display: flex;
    align-items: center;
    text-align: center;
    color: #fff;
    font-size: 16px;
    margin-right: 20px;

    .mx-buttons {
      display: flex;
      align-items: center;
      margin-right: 20px;

      .mx-btn {
        cursor: pointer;
        width: 80px;
        height: 30px;
        background: linear-gradient(0deg, #1642ff 0%, #59b7ff 100%);
        border-radius: 15px;
        line-height: 30px;
        text-align: center;
        font-weight: 400;
        font-size: 14px;
        margin-right: 10px;
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(0deg, #0d2bb3 0%, #4a9be6 100%);
          transform: translateY(-1px);
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .center {
      cursor: pointer;
      width: 88px;
      height: 30px;
      background: linear-gradient(0deg, #1642ff 0%, #59b7ff 100%);
      border-radius: 15px;
      line-height: 30px;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
    }

    .name {
      border-right: 1px solid #fff;
      border-left: 1px solid #fff;
      padding: 0 20px;
      margin: 0 20px;
    }
  }
}
</style>
