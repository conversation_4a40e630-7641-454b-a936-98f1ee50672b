<template>
  <div class="box">
    <el-scrollbar>
      <div class="top">
        <div class="tabs">
          <div
            class="items"
            :class="item.id == current ? 'act' : ''"
            @click="current = item.id"
            v-for="item in tabList"
            :key="item.id"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="line">
        <div class="left"></div>
        <div class="center"></div>
        <div class="right"></div>
      </div>
      <div v-show="current == 0" style="margin-top: 10px">
        <div class="form">
          <el-select
            v-model="queryParams.warnType"
            class="no_border"
            placeholder="请选择"
            @change="warnTypeChange"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="dict in warnTypeSelects"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </div>
        <div  style="overflow: auto; height: 600px">
          <div class="warn-content" v-for="item in warnList" :key="item.id">
            <div class="warn-content-info">
              <img
                style="width: 50px; height: 50px"
                src="@/assets/images/warn_info.png"
              />
              <span style="padding-left: 10px">{{ item.remark }}</span>
            </div>
            <div class="bottom-btns">
              <el-button
                type="warning"
                size="small"
                plain
                @click="handleClose(item)"
                style="
                  border-radius: 0;
                  background: rgba(253, 254, 190, 0);
                  border: 1px solid #fdfebe;
                  font-size: 14px;
                  font-weight: 400;
                  color: #e0dda7;
                  line-height: 22px;
                "
                >关闭</el-button
              >
              <el-button
                type="warning"
                size="small"
                style="
                  border-radius: 0;
                  background: #fdfebe;
                  font-size: 14px;
                  font-weight: 400;
                  color: #bb3900;
                  line-height: 22px;
                "
                >收到</el-button
              >
            </div>
          </div>
        </div>
      </div>
      <div v-show="current == 1" style="padding-right: 20px; margin-top: 10px">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in timeList"
            :key="index"
            :timestamp="item.timestamp"
            placement="top"
          >
            <span style="color: #fff">
              {{ item.content }}
            </span>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import { queryWarningRelease, closeWarningRelease } from "@/api/warning/index";
import {
  ElMessageBox,
  ElMessage,

} from "element-plus";

export default defineComponent({
  setup() {
    const state = reactive({
      value: "1",
      tabList: [
        {
          name: "预警信息",
          id: 0,
        },
        {
          name: "信息发布动态",
          id: 1,
        },
      ],
      current: 0,
      form: {
        value1: "day",
        value2: "day",
      },
      text: "【新吴区防汛办】【河道超警戒预警】09曰21日06时13分太湖新安站实测水位345.21吗，超警戒0.32吗，请加强监测巡查，做好安全防汛工作.",
      timeList: [
        {
          timestamp: "10-20",
          content:
            "预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s",
        },
        {
          timestamp: "10-21",
          content:
            "预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s",
        },
        {
          timestamp: "10-22",
          content:
            "预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s",
        },
        {
          timestamp: "10-23",
          content:
            "预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s",
        },
        {
          timestamp: "10-23",
          content:
            "预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s",
        },
        {
          timestamp: "10-23",
          content:
            "预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s",
        },
        {
          timestamp: "10-23",
          content:
            "预报未来12小时(即09月21日18时)入库洪水达到20年一遇标准， 洪峰流量为5000m³/s",
        },
      ],
      warnList: [],
      queryParams: {
        // time: ['',''],
        // name: undefined,
        adcd: "",
        lycode: "",
        warnType: "",
        status: 1, // 查预警中的
        pageNum: 1,
        pageSize: 10,
        count: 1,
      },
      warnTypeSelects: [
        {
          label: "所有预警类型",
          value: "",
        },
        {
          label: "山洪预警",
          value: 1,
        },
        {
          label: "河道预警",
          value: 2,
        },
        {
          label: "水库预警",
          value: 3,
        },
      ],
    });
    const getWarnList = () => {
      queryWarningRelease({
        pageNum: 1,
        pageSize: 999999,
        warnStatus: 1, // 默认选中是
      }).then((res) => {
        state.warnList = res.data.records;
      });
    };
    const handleClose = (row) => {
      ElMessageBox.confirm('确定关闭预警对象为【"' + row.name + '"】的预警?', {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          closeWarningRelease(row.id).then(res => {
            if (res.code === 200) {
              ElMessage.info("关闭预警成功！");
                getWarnList()
            } else {
                proxy.$modal.msgError(res.msg)
            }
        })
        })
        .then(() => {
          getWarnList();
          // ElMessage.info("关闭预警成功！");
        })
        .catch(() => {});
    };
    const loadWarn = () => {
      state.queryParams.count++;
      state.queryParams.pageSize = 10 * state.queryParams.count;
      // console.log("刷新了：" + state.queryParams.pageSize);
      getWarnList();
    };
    const warnTypeChange = (e) => {
      state.queryParams.count = 1;
      state.queryParams.pageSize = 10;
      getWarnList();
    };
    onMounted(() => {
      // 增加区域切换监听
      window.EventBus.$on("changeAreaSelect", (data) => {
        if (data.type === "adcd") {
          state.queryParams.adcd = data.code;
          state.queryParams.lycode = null;
        } else if (data.type === "watershed") {
          state.queryParams.adcd = null;
          state.queryParams.lycode = data.code;
        }
        // 刷新
        getWarnList();
      });

      getWarnList();

    });
    return {
      ...toRefs(state),
      getWarnList,
      handleClose,
      loadWarn,
      warnTypeChange,
    };
  },
});
</script>
<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: rgba(1, 28, 70, 0.7);

  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .tabs {
      display: flex;
      height: 40px;
      align-items: center;
      padding-left: 20px;

      .items {
        margin-right: 15px;
        color: #b1d1f2;
        cursor: pointer;
        position: relative;
        font-size: 16px;

        &.act {
          color: #fff;
          font-size: 16px;

          &:after {
            content: "";
            width: 60%;
            height: 2px;
            border-radius: 2px;
            background-color: #08aeff;
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
          }
        }
      }
    }
  }

  .form {
    display: flex;
    padding: 10px 0;
  }

  .warn-content {
    width: 100%;
    background: linear-gradient(
      to right,
      rgba(154, 151, 151, 0.1) 5%,
      rgb(204, 71, 15) 50%,
      rgba(154, 151, 151, 0.1) 95%
    );
    border: 1px solid rgba(255, 80, 3, 0.98);
    padding: 5px;
    margin-bottom: 20px;
  }

  .warn-content-info {
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #fdfec0;
    line-height: 22px;
    display: flex;
  }

  .bottom-btns {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    padding-right: 20px;
  }
}

.no_border {
  :deep(.el-input__wrapper) {
    border: none;
    box-shadow: none;
    background-color: transparent;

    .el-input__inner {
      color: #fff;
    }
  }
}

.borderColor {
  :deep(.el-input__wrapper) {
    border-color: #409eff !important;
    // box-shadow: none;
    background-color: transparent !important;
    --el-input-border-color: #409eff !important;

    .el-input__inner {
      color: #fff;
    }
  }
}

div::-webkit-scrollbar {
  width: 5px; //滚动条宽度
}

div::-webkit-scrollbar-thumb {
  border-radius: 10px; //滚动条圆角
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); //滚动区域底色
  opacity: 0.2; //透明度
  background: rgba(0, 143, 217, 1); //滚动条背景色
}

div::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); //滚动区域底色
  border-radius: 0;
}
</style>
