<template>
  <div class="box">
    <div class="top">
      <div class="left">{{ creat ? '创建' : '调整' }}方案</div>
      <div class="close" @click="close"></div>
    </div>
    <div class="line">
      <div class="left"></div>
      <div class="center"></div>
      <div class="right"></div>
    </div>
    <div class="summary">
      <el-scrollbar>
        <div class="one">
          <div>1、预报来水选择</div>
          <div class="p10">预报成果：
            <el-select class="borderColor" size="small" v-model="value" @change="selectYBFNID">
              <el-option v-for="item in achList" :label="item.NAME" :value="item.YBFNID"></el-option>
            </el-select>
          </div>
        </div>
        <div class="two">
          <div>2、控制工程设置</div>
          <div class="round">
            <div class="graphical"></div> 水库 ( 闸门 ) 调度设置
          </div>
          <div class="setBox" v-for="item in list">
            <div>{{ item.stnm }} 调度设置</div>
            <div class="item"><el-checkbox v-model="item.isfailure" label="是否溃坝" /></div>

            <div class="form">
              <div class="item">起调水位：<el-input class="borderColor" :disabled="item.isfailure" size="small"
                  style="width: 130px;margin-right: 5px;" v-model="item.sDispWaterLeave"></el-input>m</div>
              <div class="item">调度方式：<el-select class="borderColor" :disabled="item.isfailure" size="small"
                  style="width: 130px;" v-model="item.dispModule">
                  <el-option v-for="(value, key) in options" :key="key" :label="value" :value="key">
                  </el-option>
                </el-select>
              </div>
            </div>
            <el-table v-show="item.dispModule == '2' || item.dispModule == '3'" :data="item.dateList"
              element-loading-background="rgba(122, 122, 122, 0.1)" class="productTable"
              :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
              :cell-style="{ color: '#ffffff', textAlign: 'center', fontSize: 13 + 'px' }">
              <el-table-column type="index" label="序号" width="55"></el-table-column>
              <el-table-column prop="tm" label="时间"></el-table-column>
              <el-table-column prop="inq" label="入库流量 (m³/s)">
              </el-table-column>
              <el-table-column prop="z" label="库水位(m)">
                <template v-slot="scope">
                  <span v-show="item.dispModule != '2'">{{ scope.row.z }}</span>
                  <el-input v-show="item.dispModule == '2'" v-model="scope.row.z" class="borderColor"
                    size="small"></el-input>

                </template>
              </el-table-column>
              <el-table-column prop="otq" label="总出库流量 (m³/s)">
                <template v-slot="scope">
                  <span v-show="item.dispModule != '3'">{{ scope.row.otq }}</span>
                  <el-input v-show="item.dispModule == '3'" v-model="scope.row.otq" class="borderColor"
                    size="small"></el-input>
                </template>
              </el-table-column>
            </el-table>

          </div>
          <div class="round">
            <div class="graphical"></div>蓄滞洪区启用设置
          </div>
          <div v-for="item in basinList" class="enableBox">
            {{ item.dtbrsnm }}蓄滞洪区设置
            <el-radio-group style="margin-left: 10px;" v-model="item.enable">
              <el-radio label="1">启用</el-radio>
              <el-radio label="0">不启用</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="btn">
          <el-button type="primary" @click="count">计算</el-button>
          <el-button type="primary" @click="saveShow = true">保存</el-button>
          <el-button @click="resat">重置</el-button>
        </div>
      </el-scrollbar>
    </div>
    <div class="save" v-if="saveShow">
      <div class="saveBox">
        <div class="top">方案保存
          <div class="close" @click="saveShow = false"></div>
        </div>
        <div class="line">
          <div class="left"></div>
          <div class="center"></div>
          <div class="right"></div>
        </div>
        <el-form class="saveForm" ref="saveRef" :rules="rules" :model="saveForm" label-width="122px">
          <el-form-item label="调度成果名称:" prop="dispcNm">
            <el-input v-model="saveForm.dispcNm" class="borderColor"></el-input>
          </el-form-item>
          <el-form-item label="调度开始时间:" prop="dispcSttm">
            <el-date-picker class="borderColor" v-model="saveForm.dispcSttm" style="width: 100%;" type="datetime" />
          </el-form-item>
          <el-form-item label="调度结束时间:" prop="dispcEdtm">
            <el-date-picker class="borderColor" v-model="saveForm.dispcEdtm" style="width: 100%;" type="datetime" />
          </el-form-item>
          <el-form-item label="创建人:" prop="author">
            <el-input class="borderColor" disabled v-model="saveForm.author"></el-input>
          </el-form-item>
          <el-form-item label="方案描述:">
            <el-input class="borderColor" v-model="saveForm.cm" :rows="3" type="textarea"></el-input>
          </el-form-item>
          <div class="btn">
            <el-button size="small" @click="saveShow = false"> 取消</el-button>
            <el-button type="primary" @click="savePanl" size="small"> 确定</el-button>
          </div>
        </el-form>
      </div>
    </div>
    <div class="save count" v-if="countShow">
      <el-progress type="dashboard" :percentage="percentage" :color="colors" />
      <div>模型计算中,请稍等...</div>
    </div>

  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, onMounted, nextTick, getCurrentInstance } from "vue";
import * as echarts from "echarts";
import { getDdms, getForecast, putDisSettingCont, getForecastResById, getXzhqByYbid } from '../../../api/watershed/screenRight/dispatch'
import { ElMessage } from 'element-plus'
import moment from "moment";
import useUserStore from '@/store/modules/user'
export default defineComponent({
  emits: ["closeCreate"],
  props: ["info"],

  setup(props, context) {
    const userStore = useUserStore()
    const state = reactive({
      value: "",
      basinSet: '',
      achList: [],
      options: [
      ],
      list: [
      ],
      saveForm: {

      },
      rules: {
        dispcNm: [{ required: true, message: "调度成果名称不能为空", trigger: "blur" }],
        dispcSttm: [{ required: true, message: "调度开始时间不能为空", trigger: "blur" }],
        dispcEdtm: [{ required: true, message: "调度结束时间不能为空", trigger: "blur" }],
        author: [{ required: true, message: "创建人不能为空", trigger: "blur" }],
      },
      saveShow: false,
      countShow: false,
      basinList: [],
      creat: true,
      colors: [
        { color: '#f56c6c', percentage: 20 },
        { color: '#e6a23c', percentage: 40 },
        { color: '#5cb87a', percentage: 60 },
        { color: '#1989fa', percentage: 80 },
        { color: '#6f7ad3', percentage: 100 },
      ],
      percentage: 0

    })
    const { proxy } = getCurrentInstance();
    const handleClick = (id) => {
      state.current = id
      if (id == 1) {
        nextTick(() => {
          echarts
            .init(document.getElementById('detailCharts'))
            .dispose()
          let myEchart = echarts.init(
            document.getElementById('detailCharts')
          )
          myEchart.setOption(state.option)
        })
      }
    }
    onMounted(() => {
      // console.log(props.info);
      getDdms().then(res => {
        state.options = res.data
      })
      getForecast().then(res => {
        state.achList = res.data
        if (!props.info.dispcNm) {
          state.value = state.achList[state.achList.length - 1].YBFNID
          selectYBFNID(state.value)
        }
      })
      state.saveForm.author = userStore.name
      if (props.info.dispcNm) {
        state.value = props.info.fcid
        state.list = JSON.parse(JSON.stringify(props.info.statResEntity))
        state.basinList = JSON.parse(JSON.stringify(props.info.btbrsEntity))
        state.saveForm.dispcNm = props.info.dispcNm
        state.saveForm.dispcSttm = props.info.dispcSttm
        state.saveForm.dispcEdtm = props.info.dispcEdtm
        state.saveForm.author = userStore.name
        state.saveForm.cm = props.info.cm
        state.creat = false
      }
    })
    const close = () => {
      if (props.info.dispcNm) {
        window.EventBus.$emit('bubble_vanish')
      }
      context.emit('closeCreate');

    }
    const selectYBFNID = (e) => {
      getForecastResById({
        id: e
      }).then(res => {
        state.list = res.data
        state.list.forEach(e => {
          e.data.forEach(el => {
            el.tm = moment(el.IYMDH).format("YYYY-MM-DD HH:mm:ss")
            el.inq = el.INQ
            el.z = el.Z
            el.otq = el.OTQ
          })
          e.dateList = e.data
          e.stnm = e.name
          e.isfailure = false
          e.sDispWaterLeave = 100
          e.dispModule = '1'
          e.stcd = e.data[0].STCD
        })
      })
      getXzhqByYbid({
        ybid: e
      }).then(res => {
        res.data.forEach(el => {
          el.dtbrsnm = el.DTBRS_NM
          el.enable = '0'
        })
        state.basinList = res.data
      })
    }
    const resat = () => {
      if (props.info.dispcNm) {
        // console.log(props.info.statResEntity);
        state.value = props.info.fcid
        state.list = JSON.parse(JSON.stringify(props.info.statResEntity))
        state.basinList = JSON.parse(JSON.stringify(props.info.btbrsEntity))
        state.creat = false
      } else {
        state.value = state.achList[state.achList.length - 1].YBFNID
        selectYBFNID(state.value)

      }
    }
    const count = () => {
      // state.list.forEach(el => {
      //   console.log(el)
      // })
      state.countShow = true
      const setInte = setInterval(() => {
        if (state.percentage == 100) {
          state.countShow = false
          state.percentage = 0
          clearInterval(setInte)
          return
        }
        state.percentage = (state.percentage % 100) + 10
        // console.log(state.percentage);
      }, 300);
    }
    // 保存
    const savePanl = () => {

      proxy.$refs["saveRef"].validate(valid => {
        if (valid) {
          if (props.info.dispcNm) {
            putDisSettingCont({
              fcid: state.value,
              dispcNm: state.saveForm.dispcNm,
              dispcSttm: moment(state.saveForm.dispcSttm).format("YYYY-MM-DD HH:mm:ss"),
              dispcEdtm: moment(state.saveForm.dispcEdtm).format("YYYY-MM-DD HH:mm:ss"),
              author: state.saveForm.author,
              cm: state.saveForm.cm,
              btbrsEntity: state.basinList,
              statResEntity: state.list,
              dispcType: props.info.dispcType,
              id: props.info.id,
              isdo: props.info.isdo,
              lycode: props.info.lycode,
              rsvrs: props.info.rsvrs
            }).then(res => {
              if (res.code == 200) {
                ElMessage.success('调整成功')
                state.saveShow = false
                close()
              }
            })
          } else {
            let rsvrs = []
            state.list.forEach(el => {
              rsvrs.push(el.stcd)
            })
            putDisSettingCont({
              fcid: state.value,
              dispcNm: state.saveForm.dispcNm,
              dispcSttm: moment(state.saveForm.dispcSttm).format("YYYY-MM-DD HH:mm:ss"),
              dispcEdtm: moment(state.saveForm.dispcEdtm).format("YYYY-MM-DD HH:mm:ss"),
              author: state.saveForm.author,
              cm: state.saveForm.cm,
              btbrsEntity: state.basinList,
              statResEntity: state.list,
              dispcType: '2',
              isdo: '0',
              rsvrs: rsvrs.join(',')
            }).then(res => {
              if (res.code == 200) {
                ElMessage.success('创建成功')
                state.saveShow = false
                close()
              }
            })
          }
        }
      })
    }
    return {
      ...toRefs(state),
      handleClick,
      close,
      savePanl,
      selectYBFNID,
      resat,
      count,
      userStore
    };
  },
});
</script>
<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  // background-color: #00356c;
  background-color: rgba(1, 28, 70, 1);
  color: #fff;
  padding: 10px;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 999;


  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    font-size: 18px;
    margin-bottom: 5px;

    .close {
      width: 14px;
      height: 14px;
      background-image: url(../../screen/image/close.png);
      background-size: 100% 100%;
      cursor: pointer;
    }

  }

  .line {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 2px;

    .left,
    .right {
      /* flex:0 0 100px; */
      width: 10px;
      height: 2px;
      background-color: #ffd154;
    }

    .center {
      flex: 1;
      height: 2px;
      background-color: #19477a;
    }
  }

  .summary {
    width: 100%;
    height: calc(100% - 45px);

    .one {
      margin-top: 10px;
      font-size: 16px;

      .p10 {
        font-size: 14px;
        padding: 15px 23px;
        border-bottom: 1px solid #4494D4;
      }
    }

    .two {
      margin-top: 10px;

      .round {
        display: flex;
        align-items: center;
        margin: 10px 0;

        .graphical {
          width: 8px;
          height: 8px;
          background: #05DEFE;
          border-radius: 50%;
          margin: 0 10px;
        }
      }

      .setBox {
        font-size: 14px;
        background: rgba(0, 68, 138, 0.7);
        border-radius: 2px;
        padding: 10px;

        .form {
          display: flex;
          flex-wrap: wrap;
          margin: 10px 0;

          .item {
            width: 50%;
            display: flex;
            align-items: center;
            margin-bottom: 5px;
          }
        }
      }

      .enableBox {
        font-size: 14px;
        background: rgba(0, 68, 138, 0.7);
        border-radius: 2px;
        padding: 10px 20px;
        display: flex;
        align-items: center;
      }
    }

    .btn {
      margin: 10px;
      display: flex;
      justify-content: center;
    }
  }

  .save {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(2, 12, 29, 0.9);
    padding: 20px;
    z-index: 999;
    overflow: hidden;

    &.count {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .saveBox {
      margin-top: 100px;
      background-color: rgba(1, 28, 70, 1);
      padding: 10px;

      .btn {
        display: flex;
        justify-content: center;
      }
    }
  }


}




:deep(.el-checkbox) {

  height: 24px !important;

  .el-checkbox__label {
    color: #fff;
    font-size: 14px;
  }

}

:deep(.saveForm) {
  margin-top: 20px;

  .el-form-item {
    .el-form-item__label {
      color: #fff;
    }
  }
}

:deep(.productTable) {
  background-color: transparent !important;

  tr {
    background-color: transparent !important;

    th {
      background-color: rgba(15, 85, 183, 0.1) !important;
      color: #fff !important;
      border-bottom: 0 solid #00ACFF !important;
      font-size: 14px !important;
      font-weight: normal !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #00ACFF;

    }
  }

  --el-table-row-hover-bg-color: transparent;

  .el-table__inner-wrapper {
    &::before {
      background-color: transparent;
    }
  }
}

:deep(.el-radio) {
  color: #a3deff;
}

:deep(.el-radio__inner) {
  background: none;
}

:deep(.el-radio__inner::after) {
  width: 7px;
  height: 7px;
  border-radius: var(--el-radio-input-border-radius);
  background-color: #00CCFF;
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform .15s ease-in;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  border-color: #96CDEF;
  background: none;
}

:deep(.el-radio__input.is-checked+.el-radio__label) {
  color: #a3deff;
}


.borderColor {
  :deep(.el-input__wrapper) {
    border-color: #409eff !important;
    background-color: transparent !important;
    --el-input-border-color: #409eff !important;
    --el-input-bg-color: transparent !important;
    --el-fill-color-blank: transparent !important;

    .el-input__inner {
      color: #fff;
    }
  }

  :deep(.el-textarea__inner) {
    background-color: transparent !important;
    --el-border-color: #409eff !important;
    --el-input-border-color: #409eff !important;
  }
}

:deep(.el-date-editor) {
  border: 1px solid #409eff;
  background-color: transparent !important;
  box-shadow: 0 0 0 1px #409eff;
  color: #fff;
}

:deep(.el-date-editor .el-range__icon) {
  height: inherit;
  font-size: 14px;
  color: #a3deff;
  float: left;
}

:deep(.el-date-editor .el-range-input) {
  color: #a3deff;
}

:deep(.el-date-editor .el-range-separator) {
  color: rgba(255, 255, 255, 0.9);
}
</style>