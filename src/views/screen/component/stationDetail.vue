<template>
    <div>
        <div class="box">

            <template v-if="current == 0">
                <div style="height: 500px; padding: 10px;">
                    <div class="monitorCharts" id="monitorCharts" style="width: 100%;height: 100%;">
                    </div>
                </div>
                <div style="width:100%;padding: 0 20px;box-sizing: border-box;" v-if="data.sttp == 'RR'">
                    <el-table class="productTable" :data="data?.vos" style="margin-top: 20px;width: 100%;" border>
                        <el-table-column type="index" label="序号"></el-table-column>
                        <el-table-column label="时间">
                            <template #default="scope">
                                {{ formatTime(scope.row.time) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="DRP" label="面雨量(mm)">
                            <template #default="scope">
                                {{ rainData[0].rainListVos[scope.$index] ?
                                    rainData[0].rainListVos[scope.$index].rainValue : '--' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="qi" label="预报入库流量(m³/s)"></el-table-column>
                        <el-table-column prop="inq" label="实测入库流量(m³/s)"></el-table-column>
                        <el-table-column prop="z" label="预报库水位(m)"></el-table-column>
                        <el-table-column prop="realZ" label="实测库水位(m)"></el-table-column>
                        <el-table-column prop="qo" label="预报出库流量(m³/s)"></el-table-column>
                        <el-table-column prop="otq" label="实测出库流量(m³/s)"></el-table-column>
                    </el-table>
                </div>
                <div style="width:100%;padding: 0 20px;box-sizing: border-box;height: 700px;"
                    v-else-if="data.sttp == 'ZQ'">
                    <el-table class="productTable" :data="data?.vos" style="margin-top: 20px;width: 100%;height: 100%;"
                        border>
                        <el-table-column prop="TM" type="index" label="序号" width="80"></el-table-column>
                        <el-table-column label="时间">
                            <template #default="scope">
                                {{ formatTime(scope.row.time) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="DRP" label="面雨量(mm)">
                            <template #default="scope">
                                {{ rainData[0].rainListVos[scope.$index] ?
                                    rainData[0].rainListVos[scope.$index].rainValue : '--' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="qi" label="预报流量(m³/s)"></el-table-column>
                        <el-table-column prop="realQ" label="实测流量(m³/s)"></el-table-column>
                        <el-table-column prop="z" label="预报水位(m)"></el-table-column>
                        <el-table-column prop="realZ" label="实测水位(m)"></el-table-column>
                    </el-table>
                </div>

            </template>
            <!-- <template v-if="current == 1">
                <div style="width:100%;padding: 0 20px;box-sizing: border-box;" v-if="data.sttp == 'ZQ'">
                    <el-table class="productTable" :data="data?.vos" style="margin-top: 20px;width: 100%;height: 100%;"
                        border>
                        <el-table-column prop="TM" type="index" label="序号"></el-table-column>
                        <el-table-column label="时间">
                            <template #default="scope">
                                {{ formatTime(scope.row.time) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="DRP" label="面雨量(mm)">
                            <template #default="scope">
                                {{ rainData[0].rainListVos[scope.$index] ?
                                    rainData[0].rainListVos[scope.$index].rainValue : '--' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="qi" label="预报流量(m³/s)"></el-table-column>
                        <el-table-column prop="realQ" label="实测流量(m³/s)"></el-table-column>
                        <el-table-column prop="z" label="预报水位(m)"></el-table-column>
                        <el-table-column prop="realZ" label="实测水位(m)"></el-table-column>
                    </el-table>
                </div>
                <div style="width:100%;padding: 0 20px;box-sizing: border-box;height: 700px;"
                    v-else-if="stationType == 3">
                    <el-table class="productTable" :data="data?.vos" style="margin-top: 20px;width: 100%;height: 100%;"
                        border>
                        <el-table-column prop="TM" type="index" label="序号" width="80"></el-table-column>
                        <el-table-column label="时间">
                            <template #default="scope">
                                {{ formatTime(scope.row.time) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="DRP" label="面雨量(mm)">
                            <template #default="scope">
                                {{ rainData[0].rainListVos[scope.$index] ?
                                    rainData[0].rainListVos[scope.$index].rainValue : '--' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="qi" label="预报流量(m³/s)"></el-table-column>
                        <el-table-column prop="realQ" label="实测流量(m³/s)"></el-table-column>
                        <el-table-column prop="z" label="预报水位(m)"></el-table-column>
                        <el-table-column prop="realZ" label="实测水位(m)"></el-table-column>
                    </el-table>
                </div>
            </template>
            <template v-if="current == 2">
                <div style="width:100%;padding: 0 20px;box-sizing: border-box;" v-if="stationType == 2">
                    <el-table class="productTable" :data="list2" style="margin-top: 20px;" border>
                        <el-table-column prop="TM" label="统计指标">
                            <template #default="scope">
                                {{ getCustomLabel(scope.$index) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="id" label="实测值"></el-table-column>
                        <el-table-column prop="DRP" label="预报值"></el-table-column>
                        <el-table-column prop="RZ" label="差值"></el-table-column>
                    </el-table>
                </div>
                <div style="width:100%;padding: 0 20px;box-sizing: border-box;" v-if="stationType == 3">
                    <el-table class="productTable" :data="list2" style="margin-top: 20px;" border>
                        <el-table-column prop="TM" label="统计指标">
                            <template #default="scope">
                                {{ getCustomLabel2(scope.$index) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="id" label="实测值"></el-table-column>
                        <el-table-column prop="DRP" label="预报值"></el-table-column>
                        <el-table-column prop="RZ" label="差值"></el-table-column>
                    </el-table>
                </div>
            </template> -->
        </div>

    </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch, onMounted } from "vue";
import * as echarts from "echarts";
import moment from "moment";

export default defineComponent({
    props: {
        data: {
            type: Object,
            default: () => {
                return {}
            }
        },
        beforeHour: {
            type: Number,
            default: 0
        },
        rainData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },

    setup(props, context) {
        const { proxy } = getCurrentInstance()
        const state = reactive({
            stationType: -1,
            current: 0,
            list2: [{ id: 1 }, { id: 1 }, { id: 1 }, { id: 1 }, { id: 1 }, { id: 1 },],
            dataForm: {},
            time: [new Date().getTime() - 86400000, new Date()],
            dataShow: true,
            option: {
                grid: [
                    {
                        left: '30px',  //距左边距 留够name的宽度
                        right: '30px',
                        height: '25%',
                        top: '10px',
                        // containLabel: true
                    },
                    {
                        left: '30px',
                        right: '30px',
                        top: '50%',
                        height: '30%',
                        bottom: '0px',
                        // containLabel: true
                    }
                ],  //两个图表
                // tooltip: {
                //   trigger: 'axis',
                //   axisPointer: {
                //     type: 'cross',
                //     crossStyle: {
                //       color: '#999'
                //     }
                //   }
                // },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross', crossStyle: { color: '#555' }, label: {
                            backgroundColor: '#555'
                        }
                    }
                },
                legend: {

                    // align: "auto",
                    top: '35%',

                    textStyle: {
                        color: '#fff',
                        fontSize: 12,
                        fontFamily: 'PingFang SC'
                    }
                },
                xAxis: [
                    {
                        type: 'category',
                        data: [],
                        boundaryGap: true,
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: "#fff"
                            },
                            onZero: true
                        },
                        axisLabel: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        position: 'top'   //x坐标轴的位置
                    },
                    {
                        type: 'category',
                        data: [],
                        boundaryGap: true,
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            // interval: "0",
                            color: "#fff"
                        },
                        axisLine: {
                            lineStyle: {
                                color: "#fff"
                            },
                            onZero: true
                        },
                        gridIndex: 1,
                        position: 'bottom'   //x坐标轴的位置
                    },
                ],
                axisPointer: {
                    link: [
                        {
                            xAxisIndex: 'all'  //两个图表联动且tooltip合一
                        }
                    ]
                },
                dataZoom: [
                    {
                        type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
                        show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
                        start: 0,//数据窗口范围的起始百分比0-100
                        end: 100,//数据窗口范围的结束百分比0-100
                        xAxisIndex: [0, 1],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
                        bottom: '7%' //距离底部的距离
                    },
                    {
                        type: 'inside',
                        zoomOnMouseWheel: false, // 滚轮是否触发缩放
                        moveOnMouseMove: true, // 鼠标滚轮触发滚动
                        moveOnMouseWheel: true
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '面雨量(mm)',
                        inverse: true,  //进行翻转
                        scale: true,
                        alignTicks: true,
                        nameLocation: 'end', //坐标轴名称显示位置
                        nameTextStyle: {
                            color: "#fff"
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: "#fff"
                            },
                        },
                        min: 0,
                        axisLabel: {
                            color: "#fff"
                        },
                        splitLine: {
                            lineStyle: {
                                color: "#005b99",
                                type: 'dashed'
                            }
                        }
                    },
                    {
                        gridIndex: 1,   //第几个图标的y轴 根据grid的坐标
                        alignTicks: true,
                        type: 'value',
                        name: '流量m³/s',
                        nameLocation: 'end', //坐标轴名称显示位置
                        scale: true,
                        nameTextStyle: {
                            color: "#fff"
                        },
                        axisLabel: {
                            color: "#fff"
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: "#fff"
                            },
                        },
                        splitLine: {
                            lineStyle: {
                                color: "#005b99",
                                type: 'dashed'
                            }
                        }
                    },
                    {
                        gridIndex: 1,
                        alignTicks: true,
                        type: 'value',
                        name: '水位(m)',
                        nameLocation: 'end', //坐标轴名称显示位置
                        // scale: true,
                        nameTextStyle: {
                            color: "#fff"
                        },
                        axisLabel: {
                            color: "#fff"
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: "#fff"
                            },
                        },
                        splitLine: {
                            show: false,
                            lineStyle: {
                                color: "#fff",
                                type: 'dashed'
                            }
                        }
                    }
                ],
                series: [
                    {
                        name: '面雨量',
                        type: 'bar',
                        // smooth: true,
                        //barCategoryGap: '0%',
                        symbol: "none",

                        color: "#00B7FF",
                        data: []
                    },
                    {
                        name: '预报流量',
                        symbol: 'circle',
                        type: 'line',
                        //symbol: "none",
                        color: "#FFCE2C",
                        xAxisIndex: 1,
                        yAxisIndex: 1,
                        data: [],
                        lineStyle: {
                            type: "dashed"
                        }

                    },
                    {
                        name: '实测流量',
                        symbol: 'circle',
                        //symbol: "none",
                        type: 'line',
                        color: "#40d9ff",
                        lineStyle: {
                            type: "dashed"
                        },
                        xAxisIndex: 1,
                        yAxisIndex: 1,
                        data: []
                    },
                    {
                        name: '预报水位',
                        //symbol: "none",
                        symbol: 'circle',
                        type: 'line',
                        color: "#5a9e47",
                        xAxisIndex: 1,
                        yAxisIndex: 2,
                        data: [],
                        lineStyle: {
                            type: "dashed"
                        }

                    },
                    {
                        name: '实测水位',
                        //symbol: "none",
                        symbol: 'circle',
                        type: 'line',
                        color: "#b77070",
                        xAxisIndex: 1,
                        yAxisIndex: 2,
                        data: []
                    },

                    {
                        type: 'line',

                        xAxisIndex: 1,
                        yAxisIndex: 2,
                        markLine: {    //警戒线
                            // symbol: 'none',               // 去掉警戒线最后面的箭头
                            symbol: ['circle', 'arrow'], //箭头
                            label: {
                                position: 'middle',  // 将警示值放在哪个位置，三个值“start”,'middle','end'  开始  中点 结束
                                show: false,
                                formatter: function (params) {
                                    return `汛限水位：${params.data.value}.00m`;
                                },
                                color: '#F56C6C',
                            },
                            data: [{
                                silent: true,             // 鼠标悬停事件  true没有，false有
                                lineStyle: {               // 警戒线的样式  ，虚实  颜色
                                    // type: 'dash',
                                    type: 'dashed',
                                    color: 'rgba(255,255,255,0)',
                                    width: 1.65
                                },
                                yAxis: 150
                            }]
                        },
                        markArea: {
                            label: {
                                position: 'inside'
                            },
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                    { offset: 0, color: 'rgba(0,183,255,0.07)' },
                                    { offset: 0.5, color: 'rgba(0,183,255,0.3)' },
                                    { offset: 1, color: 'rgba(0,183,255,0.5)' }
                                ]),
                                zIndex: -1
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    position: 'inside'
                                }
                            },
                            data: [
                                [{
                                    xAxis: '00:00:00'
                                },
                                { xAxis: '01:00:00' }]
                            ]
                        }
                    },
                    {
                        type: 'line',
                        markLine: {
                            symbol: 'none',               // 去掉警戒线最后面的箭头
                            label: {
                                position: 'start',  // 将警示值放在哪个位置，三个值“start”,'middle','end'  开始  中点 结束
                                formatter: '',
                                color: '#F56C6C',
                            },
                            data: [{
                                silent: true,             // 鼠标悬停事件  true没有，false有
                                lineStyle: {               // 警戒线的样式  ，虚实  颜色
                                    // type: 'dash',
                                    type: 'solid',
                                    color: '#00B7FF',
                                    width: 1.65
                                },
                                xAxis: ""
                            }]
                        },
                        markArea: {
                            label: {
                                position: 'inside'
                            },
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                    { offset: 0, color: 'rgba(0,183,255,0.07)' },
                                    { offset: 0.5, color: 'rgba(0,183,255,0.3)' },
                                    { offset: 1, color: 'rgba(0,183,255,0.5)' }
                                ]),
                                zIndex: -1
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    position: 'inside'
                                }
                            },
                            data: [
                                [{
                                    xAxis: ''
                                },
                                { xAxis: '' }]
                            ]
                        }
                    },
                    {
                        type: 'line',
                        xAxisIndex: 1,
                        yAxisIndex: 2,
                        markLine: {
                            symbol: 'none',               // 去掉警戒线最后面的箭头
                            label: {
                                position: 'start',  // 将警示值放在哪个位置，三个值“start”,'middle','end'  开始  中点 结束
                                formatter: '',
                                color: '#F56C6C',
                            },
                            data: [{
                                silent: true,             // 鼠标悬停事件  true没有，false有
                                lineStyle: {               // 警戒线的样式  ，虚实  颜色
                                    // type: 'dash',
                                    type: 'solid',
                                    color: '#00B7FF',
                                    width: 1.65
                                },
                                xAxis: ""
                            },

                            ]
                        },
                    },


                ]

            },
            option2: {
                grid: [
                    {
                        left: '30px',  //距左边距 留够name的宽度
                        right: '30px',
                        height: '25%',
                        top: '10px',
                        // containLabel: true
                    },
                    {
                        left: '30px',
                        right: '30px',
                        top: '49%',
                        height: '30%',
                        bottom: '0',
                        // containLabel: true
                    }
                ],  //两个图表
                // tooltip: {
                //   trigger: 'axis',
                //   axisPointer: {
                //     type: 'cross',
                //     crossStyle: {
                //       color: '#999'
                //     }
                //   }
                // },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross', crossStyle: { color: '#555' }, label: {
                            backgroundColor: '#555'
                        }
                    }
                },
                legend: {

                    // align: "auto",
                    top: '33%',

                    textStyle: {
                        color: '#fff',
                        fontSize: 12,
                        fontFamily: 'PingFang SC'
                    }
                },
                xAxis: [
                    {
                        type: 'category',
                        data: [],
                        boundaryGap: true,
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: "#fff"
                            },
                            onZero: true
                        },
                        axisLabel: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        position: 'top'   //x坐标轴的位置
                    },
                    {
                        type: 'category',
                        data: [],
                        boundaryGap: true,
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            // interval: "0",
                            color: "#fff"
                        },
                        axisLine: {
                            lineStyle: {
                                color: "#fff"
                            },
                            onZero: true
                        },
                        gridIndex: 1,
                        position: 'bottom'   //x坐标轴的位置
                    },
                ],
                axisPointer: {
                    link: [
                        {
                            xAxisIndex: 'all'  //两个图表联动且tooltip合一
                        }
                    ]
                },
                dataZoom: [
                    {
                        type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
                        show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
                        start: 0,//数据窗口范围的起始百分比0-100
                        end: 100,//数据窗口范围的结束百分比0-100
                        xAxisIndex: [0, 1],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
                        bottom: '7%', //距离底部的距离

                    },
                    {
                        type: 'inside',
                        zoomOnMouseWheel: false, // 滚轮是否触发缩放
                        moveOnMouseMove: true, // 鼠标滚轮触发滚动
                        moveOnMouseWheel: true
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '面雨量(mm)',
                        inverse: true,  //进行翻转
                        scale: true,
                        alignTicks: true,
                        nameLocation: 'end', //坐标轴名称显示位置
                        nameTextStyle: {
                            color: "#fff"
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: "#fff"
                            },
                        },
                        min: 0,
                        axisLabel: {
                            color: "#fff"
                        },
                        splitLine: {
                            lineStyle: {
                                color: "#005b99",
                                type: 'dashed'
                            }
                        }
                    },
                    {
                        gridIndex: 1,   //第几个图标的y轴 根据grid的坐标
                        alignTicks: true,
                        type: 'value',
                        name: '流量m³/s',
                        nameLocation: 'end', //坐标轴名称显示位置
                        scale: true,
                        nameTextStyle: {
                            color: "#fff"
                        },
                        axisLabel: {
                            color: "#fff"
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: "#fff"
                            },
                        },
                        splitLine: {
                            lineStyle: {
                                color: "#005b99",
                                type: 'dashed'
                            }
                        }
                    },
                    {
                        gridIndex: 1,
                        alignTicks: true,
                        type: 'value',
                        name: '水位(m)',
                        nameLocation: 'end', //坐标轴名称显示位置
                        // scale: true,
                        nameTextStyle: {
                            color: "#fff"
                        },
                        axisLabel: {
                            color: "#fff"
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: "#fff"
                            },
                        },
                        splitLine: {
                            show: false,
                            lineStyle: {
                                color: "#fff",
                                type: 'dashed'
                            }
                        }
                    }
                ],
                series: [
                    {
                        name: '面雨量',
                        type: 'bar',
                        // smooth: true,
                        //barCategoryGap: '0%',
                        symbol: "none",

                        color: "#00B7FF",
                        data: []
                    },
                    {
                        name: '预报入库流量',
                        symbol: 'circle',
                        type: 'line',
                        //symbol: "none",
                        color: "#FFCE2C",
                        xAxisIndex: 1,
                        yAxisIndex: 1,
                        data: [],
                        lineStyle: {
                            type: "dashed"
                        }

                    },
                    {
                        name: '实测入库流量',
                        symbol: 'circle',
                        //symbol: "none",
                        type: 'line',
                        color: "#40d9ff",
                        lineStyle: {
                            type: "dashed"
                        },
                        xAxisIndex: 1,
                        yAxisIndex: 1,
                        data: []
                    },
                    {
                        name: '预报库水位',
                        //symbol: "none",
                        symbol: 'circle',
                        type: 'line',
                        color: "#5a9e47",
                        xAxisIndex: 1,
                        yAxisIndex: 2,
                        data: [],
                        lineStyle: {
                            type: "dashed"
                        }

                    },
                    {
                        name: '实测库水位',
                        //symbol: "none",
                        symbol: 'circle',
                        type: 'line',
                        color: "#b77070",
                        xAxisIndex: 1,
                        yAxisIndex: 2,
                        data: []
                    },
                    {
                        name: '预报出库流量',
                        //symbol: "none",
                        symbol: 'circle',
                        type: 'line',
                        // color: "#b77070",
                        xAxisIndex: 1,
                        yAxisIndex: 1,
                        data: [],
                        lineStyle: {
                            type: "dashed"
                        }

                    },
                    {
                        name: '实测出库流量',
                        //symbol: "none",
                        symbol: 'circle',
                        type: 'line',
                        // color: "#b77070",
                        xAxisIndex: 1,
                        yAxisIndex: 1,
                        data: []
                    },

                    {
                        type: 'line',

                        xAxisIndex: 1,
                        yAxisIndex: 2,
                        markLine: {    //警戒线
                            // symbol: 'none',               // 去掉警戒线最后面的箭头
                            symbol: ['circle', 'arrow'], //箭头
                            label: {
                                position: 'middle',  // 将警示值放在哪个位置，三个值“start”,'middle','end'  开始  中点 结束
                                show: false,
                                formatter: function (params) {
                                    return `汛限水位：${params.data.value}.00m`;
                                },
                                color: '#F56C6C',
                            },
                            data: [{
                                silent: true,             // 鼠标悬停事件  true没有，false有
                                lineStyle: {               // 警戒线的样式  ，虚实  颜色
                                    // type: 'dash',
                                    type: 'dashed',
                                    color: 'rgba(255,255,255,0)',
                                    width: 1.65
                                },
                                yAxis: 150
                            }]
                        },
                        markArea: {
                            label: {
                                position: 'inside'
                            },
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                    { offset: 0, color: 'rgba(0,183,255,0.07)' },
                                    { offset: 0.5, color: 'rgba(0,183,255,0.3)' },
                                    { offset: 1, color: 'rgba(0,183,255,0.5)' }
                                ]),
                                zIndex: -1
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    position: 'inside'
                                }
                            },
                            data: [
                                [{
                                    xAxis: '00:00:00'
                                },
                                { xAxis: '01:00:00' }]
                            ]
                        }
                    },
                    {
                        type: 'line',
                        markLine: {
                            symbol: 'none',               // 去掉警戒线最后面的箭头
                            label: {
                                position: 'start',  // 将警示值放在哪个位置，三个值“start”,'middle','end'  开始  中点 结束
                                formatter: '',
                                color: '#F56C6C',
                            },
                            data: [{
                                silent: true,             // 鼠标悬停事件  true没有，false有
                                lineStyle: {               // 警戒线的样式  ，虚实  颜色
                                    // type: 'dash',
                                    type: 'solid',
                                    color: '#00B7FF',
                                    width: 1.65
                                },
                                xAxis: ""
                            }]
                        },
                        markArea: {
                            label: {
                                position: 'inside'
                            },
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                    { offset: 0, color: 'rgba(0,183,255,0.07)' },
                                    { offset: 0.5, color: 'rgba(0,183,255,0.3)' },
                                    { offset: 1, color: 'rgba(0,183,255,0.5)' }
                                ]),
                                zIndex: -1
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    position: 'inside'
                                }
                            },
                            data: [
                                [{
                                    xAxis: ''
                                },
                                { xAxis: '' }]
                            ]
                        }
                    },
                    {
                        type: 'line',
                        xAxisIndex: 1,
                        yAxisIndex: 2,
                        markLine: {
                            symbol: 'none',               // 去掉警戒线最后面的箭头
                            label: {
                                position: 'start',  // 将警示值放在哪个位置，三个值“start”,'middle','end'  开始  中点 结束
                                formatter: '',
                                color: '#F56C6C',
                            },
                            data: [{
                                silent: true,             // 鼠标悬停事件  true没有，false有
                                lineStyle: {               // 警戒线的样式  ，虚实  颜色
                                    // type: 'dash',
                                    type: 'solid',
                                    color: '#00B7FF',
                                    width: 1.65
                                },
                                xAxis: ""
                            },

                            ]
                        },
                    },


                ]

            },
            site: '',



            list1: [],
            data1: [],
            vedioData: {
                id: '',
                msg: "无信号",
                videoUrl: ""
            }
        });
        const query = () => {
            getrsvrShort()
        }
        const formatTime = (time) => {
            return moment(time).format('YYYY-MM-DD HH:mm:ss')
        }
        watch(() => props.data, (newVal, oldVal) => {
            if (newVal) {
                // console.log('data改变了', newVal);
                proxy.$nextTick(() => {
                    getrsvrShort()
                })
            }
        })
        watch(() => state.current, (newVal, oldVal) => {
            if (newVal === 0) {
                proxy.$nextTick(() => {
                    getrsvrShort()
                })
            }
        })
        const getrsvrShort = async () => {

            let data = props.data
            let hour = props.beforeHour
            // console.log(hour, 'hour')
            state.stationType = data.sttp == 'RR' ? 2 : 3

            if (state.stationType == 3) {
                state.option.xAxis[0].data = []
                state.option.xAxis[1].data = []
                state.option.series[0].data = []
                state.option.series[1].data = []
                state.option.series[2].data = []
                state.option.series[3].data = []
                state.option.series[4].data = []
                state.option.series[5].data = []
                if (data.vos && data.vos.length > 0) {
                    let lineTime = moment(data.vos[0].time).add(hour, 'hours').format('MM-DD HH:mm')
                    data.vos.forEach(el => {
                        state.option.xAxis[0].data.push(moment(el.time).format("MM-DD HH:mm"))
                        state.option.xAxis[1].data.push(moment(el.time).format("MM-DD HH:mm"))
                        state.option.series[1].data.push(el.qi) //预报流量
                        state.option.series[2].data.push(el.realQ) //实测
                        state.option.series[3].data.push(el.z)//预测水位
                        state.option.series[4].data.push(el.realZ) //实测流量

                        //
                        state.option.series[7].markLine.data[0].xAxis = lineTime
                        // state.option.series[7].markLine.data[0].xAxis = moment(data.vos[data.vos.length - 1].time).format("MM-DD HH:mm")
                        state.option.series[5].markArea.data[0][0].xAxis = moment(data.vos[0].time).format("MM-DD HH:mm")
                        state.option.series[5].markArea.data[0][1].xAxis = lineTime
                        state.option.series[6].markLine.data[0].xAxis = lineTime
                        state.option.series[6].markArea.data[0][0].xAxis = moment(data.vos[0].time).format("MM-DD HH:mm")
                        state.option.series[6].markArea.data[0][1].xAxis = lineTime

                    })
                }

                //如果有雨量的数据
                // console.log(props.rainData)
                if (props.rainData && props.rainData.length > 0) {

                    let data = props.rainData[0].rainListVos
                    data.forEach((item) => {

                        state.option.series[0].data.push(item.rainValue) //雨量

                    })
                }

            } else {
                state.option2.xAxis[0].data = []
                state.option2.xAxis[1].data = []
                state.option2.series[0].data = []
                state.option2.series[1].data = []
                state.option2.series[2].data = []
                state.option2.series[3].data = []
                state.option2.series[4].data = []
                state.option2.series[5].data = []
                if (data.vos && data.vos.length > 0) {
                    let lineTime = moment(data.vos[0].time).add(hour, 'hours').format('MM-DD HH:mm')
                    data.vos.forEach(el => {
                        state.option2.xAxis[0].data.push(moment(el.time).format("MM-DD HH:mm"))
                        state.option2.xAxis[1].data.push(moment(el.time).format("MM-DD HH:mm"))
                        state.option2.series[1].data.push(el.qi) //预报入库流量
                        state.option2.series[2].data.push(el.inq) //实测入库流量
                        state.option2.series[3].data.push(el.z)//预测库水位
                        state.option2.series[4].data.push(el.realZ) //实测库水位
                        state.option2.series[5].data.push(el.qo) //预报出库流量
                        state.option2.series[6].data.push(el.otq) //实测出库流量

                        //
                        state.option2.series[9].markLine.data[0].xAxis = lineTime
                        // state.option.series[7].markLine.data[0].xAxis = moment(data.vos[data.vos.length - 1].time).format("MM-DD HH:mm")
                        state.option2.series[7].markArea.data[0][0].xAxis = moment(data.vos[0].time).format("MM-DD HH:mm")
                        state.option2.series[7].markArea.data[0][1].xAxis = lineTime
                        state.option2.series[8].markLine.data[0].xAxis = lineTime
                        state.option2.series[8].markArea.data[0][0].xAxis = moment(data.vos[0].time).format("MM-DD HH:mm")
                        state.option2.series[8].markArea.data[0][1].xAxis = lineTime

                    })
                }
                if (props.rainData && props.rainData.length > 0) {

                    let data = props.rainData[0].rainListVos
                    data.forEach((item) => {

                        state.option2.series[0].data.push(item.rainValue) //雨量

                    })
                }
            }

            echarts
                .init(document.getElementById('monitorCharts'))
                .dispose()
            let myEchart = echarts.init(
                document.getElementById('monitorCharts')
            )
            myEchart.setOption(state.stationType == 2 ? state.option2 : state.option)

            // state.list1 = res.data.SCDATE
            // state.list1 = [...res.data.SCDATE, ...res.data.YBDATA]

        }
        const openLink = (item) => {
            window.open(item.link)
        }
        onMounted(() => {
            getrsvrShort()

        })
        const handleChangeData = () => {
            // if (state.dataShow) {
            state.dataShow = !state.dataShow
            // }
        }
        const resatQuery = () => {
            state.time = [new Date().getTime() - 86400000, new Date()]
            getrsvrShort()
        }
        const getCustomLabel = (index) => {
            // 根据索引返回不同的标签
            const labels = ['最大入库流量(m³/s)', '最大出库流量(m³/s)', '最高库水位(m)', '最大1天洪量(亿m³)', '最大3天洪量(亿m³)', '最大5天洪量(亿m³)', '最大7天洪量(亿m³)', '最大15天洪量(亿m³)'];
            return labels[index];
        }
        const getCustomLabel2 = (index) => {
            // 根据索引返回不同的标签
            const labels = ['最大洪库流量(m³/s)', '最大1天洪量(亿m³)', '最大3天洪量(亿m³)', '最大5天洪量(亿m³)', '最大7天洪量(亿m³)', '最大15天洪量(亿m³)'];
            return labels[index];
        }
        return {
            ...toRefs(state),
            query,
            handleChangeData,
            openLink,
            resatQuery,
            getCustomLabel,
            formatTime,
            getCustomLabel2
        };
    },
});
</script>
<style scoped lang="scss">
.box {
    width: 100%;
    height: 100%;


    .tabs {
        text-align: right;
    }
}
</style>