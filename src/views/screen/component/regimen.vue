<template>
  <div class="box">
    <div class="boxTop">
      <div class="title">河道水情</div>
    </div>
    <div class="line">
      <div class="left"></div>
      <div class="center"></div>
      <div class="right"></div>
    </div>
    <div class="boxBom table-container">
      <div class="exceed">
        <div class="items" :class="riverWarnType === item.name ? 'active' : ''" v-for="item in exceedList"
          @click="handlerFilter(item)">
          <div class="value" :style="{ color: item.color }">{{ item.value }}</div>
          <div class="name">{{ item.name }}</div>
        </div>
      </div>
      <div class="tablesBox">
        <div class="tablesContent">
          <el-table v-loading="loading" element-loading-background="rgba(122, 122, 122, 0.1)" class="productTable"
            :data="regimenList" :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
            :cell-style="{ color: '#ffffff', textAlign: 'center', fontSize: 13 + 'px' }" @row-click="handleClick">
            <el-table-column type="index" label="序号" width="55"></el-table-column>
            <el-table-column prop="riverStationName" label="站名" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column prop="Z" width="100px" label="水位(m)">
              <template v-slot="scope">
                <span>{{ formatRiverValue(scope.row.waterLevel) }}</span>
                <!--                <span v-if="scope.row.WPTN == 4">落</span>-->
                <!--                <span v-if="scope.row.WPTN == 5">涨</span>-->
                <!--                <span v-if="scope.row.WPTN == 6">平</span>-->
                <span v-if="scope.row.wptn == '落'"><img :src="trend_down_img" style="width: 18px;padding: 2px"></span>
                <span v-if="scope.row.wptn == '涨'"><img :src="trend_up_img" style="width: 18px;padding: 2px"></span>
                <span v-if="scope.row.wptn == '平'">
                  <div :src="trend_img" style="width: 18px;display: inline-block;padding: 2px"></div>
                </span>

              </template>
            </el-table-column>
            <el-table-column prop="WARNTP" label="超警状态" width="110">
              <template v-slot="scope">
                <span v-if="scope.row.warnTypeVal?.indexOf('历史') > -1">{{ scope.row.warnTypeVal.replace('水位', '')
                  }}</span>
                <span v-else>{{ scope.row.WARNTP || '' }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="TM" label="时间" :show-overflow-tooltip="true"></el-table-column> -->
            <el-table-column prop="time" label="时间" width="95px"></el-table-column>
            <el-table-column prop="wptn" label="水势">

            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import { riverStationList } from "@/api/watershed/query/index"
import moment from "moment";
import trend_up_img from '@/assets/icon/trend_up.png'
import trend_down_img from '@/assets/icon/trend_down.png'
import { formatRiverValue, riverDataSort2 } from "@/components/Map/utils/water-common"
import trend_img from "@/assets/icon/trend.png";
export default defineComponent({
  setup() {
    const state = reactive({
      adcd: '', // 131000
      lycode: '', // 131000
      exceedList: [
        {
          name: '全部',
          value: '0',
          color: '#87EAFF'
        },
        {
          name: '超警戒',
          value: '0',
          color: '#FFEA35'
        },
        {
          name: '超保证',
          value: '0',
          color: '#FFA92F'
        },
        {
          name: '超历史',
          value: '0',
          color: '#FF7E01'
        },
      ],
      riverWarnType: '全部',
      regimenList: [],
      backList: [], // 备份
      loading: false
    })
    onMounted(() => {
      // 增加区域切换监听
      window.EventBus.$on('changeAreaSelect', (data) => {
        if (data.type === 'adcd') {
          state.adcd = data.code
          state.lycode = null
        } else if (data.type === 'watershed') {
          state.adcd = null
          state.lycode = data.code
        }
        handleQuery()
      })
      window.EventBus.$on('riverPanel/visible', (data) => {
        // 处理如果从概览过来的话，时间需要和雨量分级或者雨量极值时间统一，然后刷新
        // handleTime(state.form.value)
        handleQuery()
      })
      window.EventBus.$on('riverPanel/handlerFilter', (data) => {
        // 处理如果从概览 水情列表过滤，联动
        // handleTime(state.form.value)
        state.riverWarnType = data.name
      })
      // handleQuery()
    })
    const handleQuery = async () => {
      state.loading = true
      riverStationList({
        startTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        pageNum: 1,
        pageSize: 99999
      }).then(res => {
        state.loading = false
        state.exceedList[0].value = res.data.length || 0
        state.exceedList[1].value = res.data.filter((item) => item.warnType == 1).length || 0
        state.exceedList[2].value = res.data.filter(item => item.warnType == 2).length || 0
        state.exceedList[3].value = res.data.filter(item => item.warnType == 3).length || 0
        const obj = res.data
        let list = []
        obj.forEach(el => {
          el.time = moment(el.TM).format("MM-DD HH:mm")
          list.push(el)
        });
        list = riverDataSort2(list)
        state.backList = list
        console.log(list)
        if (state.riverWarnType == '全部') {
          state.regimenList = list
          window.EventBus.$emit('river/update', list)
        } else {
          const newList = list.filter(item => item.warnType == state.riverWarnType)
          state.regimenList = newList
          window.EventBus.$emit('river/update', newList)
        }
      })

    }
    const handleClick = (e) => {
      // 河道站 ZZ河道水位站，ZQ河道水文站
      // 获取属性里面的经纬度
      let item = {
        lng: e.lgtd,
        lat: e.lttd
      }
      window.EventBus.$emit('flyToPostion', item)
    }
    const handlerFilter = (item) => {
      state.riverWarnType = item.name
      if (item.name == '全部') {
        handleQuery()
      } else {
        const list = state.backList.filter((el) => {
          return el.warnType == item.value
        })
        // console.log(item.name)
        // debugger
        state.regimenList = list
        window.EventBus.$emit('river/update', list)
      }
    }
    return {
      ...toRefs(state),
      handleClick,
      handleQuery,
      formatRiverValue,
      trend_up_img,
      trend_img,
      trend_down_img,
      handlerFilter
    };
  },
});
</script>
<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  color: #fff;

  .title {
    color: #8CD2FF;
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
  }

  .boxBom {
    background-color: #022757;
    overflow: hidden;
    padding: 10px;
  }

  .exceed {
    width: 90%;
    margin: 10px auto;
    height: 80px;
    display: flex;
    color: #fff;


    .items {
      width: 25%;
      height: 100%;
      text-align: center;
      cursor: pointer;
      margin-bottom: 10px;

      .value {
        margin: 10px 0;
        font-size: 20px;
      }
    }

    .items:hover {
      background: linear-gradient(0deg, #07529F 0%, rgba(8, 61, 132, 0) 100%);
      box-sizing: border-box;
      border: 0 solid #000000;
      box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
      cursor: pointer;
    }

    .active {
      background: linear-gradient(0deg, #07529F 0%, rgba(8, 61, 132, 0) 100%);
      box-sizing: border-box;
      border: 0 solid #000000;
      box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
      cursor: pointer;
    }
  }
}

:deep(.productTable) {
  background-color: transparent !important;

  tr {
    background-color: transparent !important;

    th {
      background-color: #083f86 !important;
      color: #fff !important;
      border-bottom: 0 solid #00ACFF !important;
      font-size: 14px !important;
      font-weight: normal !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #00ACFF;
    }
  }

  --el-table-row-hover-bg-color: transparent;

  .el-table__inner-wrapper {
    &::before {
      background-color: transparent;
    }
  }
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell) {
  background-color: #033c68 !important;
  cursor: pointer;
}
</style>
