<template>
  <div class="box" v-loading="loading" element-loading-text="数据加载中..."
    element-loading-background="rgba(122, 122, 122, 0.4)">
    <div class="head">方案详情及指标
      <el-button type="primary" size="small" @click="back">返回</el-button>
    </div>
    <div class="top">
      <div class="left">{{ title }}</div>

      <div class="tabs">
        <div class="items" :class="item.id == current ? 'act' : ''" @click="handleClick(item.id)"
          v-for="item in tabList" :key="item.id">{{ item.name
          }}
        </div>
      </div>
    </div>

    <div class="detail" v-show="current == 1">
      <el-scrollbar>
        <el-form>
          <el-form-item label="模拟流域">{{ workData.basinName }}</el-form-item>
          <el-form-item label="降雨时段">{{ workData.forecastBeginTime + ` ` }} —— {{ ` ` + workData.forecastEndTime
            }}</el-form-item>
          <template style="display: flex;justify-content: space-between;">
            <el-form-item label="预热期(时)">{{ workData.warmUpPeriod }}h</el-form-item>
            <el-form-item label="预见期(时)" style="margin-right: 100px;">{{ workData.forecastPeriod }}h</el-form-item>
          </template>
        </el-form>
        <el-select v-model="chooseId" placeholder="请选择站点" class="sci-fi-select">
          <el-option :label="item.stnm" :value="item.configCode" v-for="(item, index) in stationList" :key="index" />
        </el-select>
        <div v-show="rsShow">
          <div class="title">调度结果</div>
          <div class="desc">
            <div class="items" v-for="item in list5" :key="item.id">
              {{ item.name }} : {{ item.value }}
            </div>
          </div>
        </div>
        <station-detail :data="data" :rainData="rainData" :stationType="stationType" :beforeHour="beforeHour"
          style="margin-top: 20px;"></station-detail>
      </el-scrollbar>
    </div>

  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, onMounted, nextTick, watch, onUnmounted } from "vue";
import * as echarts from "echarts";
import { getWorkDetail, taskPlanDetail } from '@/api/scheduling'
import moment from "moment";
import stationDetail from './stationDetail'
export default defineComponent({
  emits: ["back", "backAdjust"],
  props: ["taskId", 'planId'],
  components: { stationDetail },
  setup(props, context) {
    const state = reactive({
      showDetail: false,
      beforeHour: 0,
      tabList: [

        {
          name: '调度节点详情',
          id: 1
        },
      ],
      loading: false,
      list5: [
        {
          id: 0,
          name: '入库洪峰(m³/s)',
          value: ''
        },
        {
          id: 1,
          name: '峰现时间',
          value: ''
        },
        {
          id: 2,
          name: '最高库水位(m)',
          value: ''
        },
        {
          id: 3,
          name: '出现时间',
          value: ''
        },
        {
          id: 4,
          name: '最大出库流量 (m³/s)',
          value: ''
        },
        {
          id: 5,
          name: '出现时间',
          value: ''
        },
        {
          id: 6,
          name: '削峰率(%)',
          value: ''
        },
        {
          id: 7,
          name: '错峰时长(h)',
          value: ''
        },
        {
          id: 8,
          name: '拦洪量(万m³)"',
          value: ''
        },
        // {
        //   id: 9,
        //   name: '调度模式',
        //   value: ''
        // },
      ],
      data: [],
      rainData: [],
      stationType: -1,
      current: 1,
      chooseId: null,
      workData: [],
      value: "",
      chooseStation: [],
      stationAll: [],
      stationList: [],
      detailData: [],
      option: {
        grid: [
          {
            left: '60px',  //距左边距 留够name的宽度
            right: '60px',
            height: '25%',
            top: '50px',
            // containLabel: true
          },
          {
            left: '60px',
            right: '60px',
            top: '60%',
            height: '25%',
            bottom: '50px',
            // containLabel: true
          }
        ],  //两个图表
        // tooltip: {
        //   trigger: 'axis',
        //   axisPointer: {
        //     type: 'cross',
        //     crossStyle: {
        //       color: '#999'
        //     }
        //   }
        // },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        legend: {
          width: "100%",
          // align: "auto",
          data: [
            '雨量',
            '预报流量',
            '实测流量',
            '预报水位',
            '实测水位'
          ],
          textStyle: {
            color: '#fff',
            fontSize: 12,
            fontFamily: 'PingFang SC'
          }
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            boundaryGap: true,
            axisPointer: {
              type: 'shadow'
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
              onZero: true
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            position: 'top'   //x坐标轴的位置
          },
          {
            type: 'category',
            data: [],
            boundaryGap: true,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              // interval: "0",
              color: "#fff"
            },
            axisLine: {
              lineStyle: {
                color: "#fff"
              },
              onZero: true
            },
            gridIndex: 1,
            position: 'bottom'   //x坐标轴的位置
          },
        ],
        axisPointer: {
          link: [
            {
              xAxisIndex: 'all'  //两个图表联动且tooltip合一
            }
          ]
        },
        dataZoom: [
          {
            type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0,//数据窗口范围的起始百分比0-100
            end: 100,//数据窗口范围的结束百分比0-100
            xAxisIndex: [0, 1],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: 0 //距离底部的距离
          },
          {
            type: 'inside',
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '雨量(mm)',
            inverse: true,  //进行翻转
            scale: true,
            alignTicks: true,
            nameLocation: 'end', //坐标轴名称显示位置
            nameTextStyle: {
              color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            min: 0,
            axisLabel: {
              color: "#fff"
            },
            splitLine: {
              lineStyle: {
                color: "#005b99"
              }
            }
          },
          {
            gridIndex: 1,   //第几个图标的y轴 根据grid的坐标
            alignTicks: true,
            type: 'value',
            name: '流量m³/s',
            nameLocation: 'end', //坐标轴名称显示位置
            scale: true,
            nameTextStyle: {
              color: "#fff"
            },
            axisLabel: {
              color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            splitLine: {
              lineStyle: {
                color: "#005b99"
              }
            }
          },
          {
            gridIndex: 1,
            alignTicks: true,
            type: 'value',
            name: '水位(m)',
            nameLocation: 'end', //坐标轴名称显示位置
            // scale: true,
            nameTextStyle: {
              color: "#fff"
            },
            axisLabel: {
              color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: "#fff"
              }
            }
          }
        ],
        series: [
          {
            name: '雨量',
            type: 'bar',
            // smooth: true,
            symbol: "none",
            color: "#00B7FF",
            data: []
          },
          {
            name: '预报流量',
            type: 'line',
            symbol: "none",
            lineStyle: {
              type: "dashed"
            },
            color: "#FFCE2C",
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: []
          },
          {
            name: '实测流量',
            symbol: "none",
            type: 'line',
            color: "#00FF95",
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: []
          },
          {
            name: '预报水位',
            symbol: "none",
            type: 'line',
            color: "#9E54FF",
            xAxisIndex: 1,
            yAxisIndex: 2,
            lineStyle: {
              type: "dashed"
            },
            data: []
          },
          {
            name: '实测水位',
            symbol: "none",
            type: 'line',
            color: "#9E54FF",
            data: [],
            xAxisIndex: 1,
            yAxisIndex: 2,

          },
          {
            type: 'line',
            xAxisIndex: 1,
            yAxisIndex: 2,
            markLine: {
              symbol: 'none',               // 去掉警戒线最后面的箭头
              label: {
                position: 'start',  // 将警示值放在哪个位置，三个值"start",'middle','end'  开始  中点 结束
                formatter: '',
                color: '#F56C6C',
              },
              data: [{
                silent: true,             // 鼠标悬停事件  true没有，false有
                lineStyle: {               // 警戒线的样式  ，虚实  颜色
                  // type: 'dash',
                  type: 'solid',
                  color: '#F56C6C',
                  width: 1
                },
                xAxis: "10-05"
              }]
            },
            markArea: {
              label: {
                position: 'inside'
              },
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: 'rgba(233,110,110,0.07)' },
                  { offset: 0.5, color: 'rgba(233,110,110,0.3)' },
                  { offset: 1, color: 'rgba(233,110,110,0.5)' }
                ]),
                zIndex: -1
              },
              emphasis: {
                label: {
                  show: true,
                  position: 'inside'
                }
              },
              data: [
                [{
                  xAxis: '00:00:00'
                },
                { xAxis: '01:00:00' }]
              ]
            }
          },
          {
            type: 'line',
            markLine: {
              symbol: 'none',               // 去掉警戒线最后面的箭头
              label: {
                position: 'start',  // 将警示值放在哪个位置，三个值"start",'middle','end'  开始  中点 结束
                formatter: '',
                color: '#F56C6C',
              },
              data: [{
                silent: true,             // 鼠标悬停事件  true没有，false有
                lineStyle: {               // 警戒线的样式  ，虚实  颜色
                  // type: 'dash',
                  type: 'solid',
                  color: '#F56C6C',
                  width: 1
                },
                xAxis: ""
              }]
            },
            markArea: {
              label: {
                position: 'inside'
              },
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: 'rgba(233,110,110,0.07)' },
                  { offset: 0.5, color: 'rgba(233,110,110,0.3)' },
                  { offset: 1, color: 'rgba(233,110,110,0.5)' }
                ]),
                zIndex: -1
              },
              emphasis: {
                label: {
                  show: true,
                  position: 'inside'
                }
              },
              data: [
                [{
                  xAxis: ''
                },
                { xAxis: '' }]
              ]
            }
          }
        ]

      },
      option1: {
        grid: [
          {
            left: '60px',  //距左边距 留够name的宽度
            right: '60px',
            height: '25%',
            top: '50px',
            // containLabel: true
          },
          {
            left: '60px',
            right: '60px',
            top: '10%',
            height: '25%',
            bottom: '50px',
            // containLabel: true
          }
        ],  //两个图表
        // tooltip: {
        //   trigger: 'axis',
        //   axisPointer: {
        //     type: 'cross',
        //     crossStyle: {
        //       color: '#999'
        //     }
        //   }
        // },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        legend: {
          width: "80%",
          data: [
            '雨量',
            '实测出库流量',
            '拟定出库流量',
            '预报入库流量',
            '实测库水位',
            '拟定库水位',
          ],
          textStyle: {
            color: '#fff',
            fontSize: 12,
            fontFamily: 'PingFang SC'
          }
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            boundaryGap: true,
            axisPointer: {
              type: 'shadow'
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
              onZero: true
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            position: 'top'   //x坐标轴的位置
          },
          {
            type: 'category',
            data: [],
            boundaryGap: true,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              // interval: "0",
              color: "#fff"
            },
            axisLine: {
              lineStyle: {
                color: "#fff"
              },
              onZero: true
            },
            gridIndex: 1,
            position: 'bottom'   //x坐标轴的位置
          },
        ],
        axisPointer: {
          link: [
            {
              xAxisIndex: 'all'  //两个图表联动且tooltip合一
            }
          ]
        },
        dataZoom: [
          {
            type: 'slider',//有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true,//是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0,//数据窗口范围的起始百分比0-100
            end: 100,//数据窗口范围的结束百分比0-100
            xAxisIndex: [0, 1],// 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: 0 //距离底部的距离
          },
          {
            type: 'inside',
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '雨量(mm)',
            inverse: true,  //进行翻转
            scale: true,
            alignTicks: true,
            nameLocation: 'end', //坐标轴名称显示位置
            nameTextStyle: {
              color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            min: 0,
            axisLabel: {
              color: "#fff"
            },
            splitLine: {
              lineStyle: {
                color: "#005b99"
              }
            }
          },
          {
            gridIndex: 1,   //第几个图标的y轴 根据grid的坐标
            alignTicks: true,
            type: 'value',
            name: '流量m³/s',
            nameLocation: 'end', //坐标轴名称显示位置
            scale: true,
            nameTextStyle: {
              color: "#fff"
            },
            axisLabel: {
              color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            splitLine: {
              lineStyle: {
                color: "#005b99"
              }
            }
          },
          {
            gridIndex: 1,
            alignTicks: true,
            type: 'value',
            name: '水位(m)',
            nameLocation: 'end', //坐标轴名称显示位置
            // scale: true,
            nameTextStyle: {
              color: "#fff"
            },
            axisLabel: {
              color: "#fff"
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: "#fff"
              }
            }
          }
        ],
        series: [
          {
            name: '雨量',
            type: 'bar',
            // smooth: true,
            symbol: "none",
            color: "#00B7FF",
            data: []
          },
          {
            name: '实测出库流量',
            type: 'line',
            symbol: "none",
            color: "#FFCE2C",
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: []
          },
          {
            name: '拟定出库流量',
            type: 'line',
            symbol: "none",
            color: "#FFCE2C",
            lineStyle: {
              type: "dashed"
            },
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: []
          },
          {
            name: '预报入库流量',
            symbol: "none",
            type: 'line',
            color: "#29F8F8",
            lineStyle: {
              type: "dashed"
            },
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: []
          },
          {
            name: '实测库水位',
            symbol: "none",
            type: 'line',
            color: "#FF8F1F",
            data: [],
            xAxisIndex: 1,
            yAxisIndex: 2,
          },
          {
            name: '拟定库水位',
            symbol: "none",
            type: 'line',
            color: "#FF8F1F",
            lineStyle: {
              type: "dashed"
            },
            data: [],
            xAxisIndex: 1,
            yAxisIndex: 2,
          },
        ]
      },
      infoData: {},
      sltList: {},
      resList: [],
      resObj: {},
      type: '',
      title: '',
      rsShow: true
    })
    const handleClick = (id) => {
      state.current = id
      if (id == 1) {
        nextTick(() => {
          changeSlt(state.value)
        })
      }
    }
    const back = () => {
      context.emit('back');
      window.viewer.getLayerGroup('river_zwh').show = false
      window.viewer.getLayerGroup('river1').show = true
      window.EventBus.$emit("colorBandShow/update", false);
      window.EventBus.$emit("change/agent-scheme/hide");
      window.EventBus.$emit("Navigation/update", false);
      window.EventBus.$emit( //流域面颜色初始化
        "catchmentFillColor_viewer/update",
        new DC.Color.fromCssColorString(
          'rgba(0, 0, 0, 0)'
        )
      );
      closeZwhPoi(window.viewer)
    }
    const closeZwhPoi = (viewer) => {
      let FloodDetentionArea =
      viewer.getLayerGroup("FloodDetentionArea");
      if (FloodDetentionArea) {
        FloodDetentionArea.show = false
      }
      if(viewer.entities.values.length > 0){
          viewer.entities.values.forEach(item => {
            if(item.id == "exw" || item.id == "dmfq" || item.id == "snsz"){
              item.show = false
            }
          })
        }
    }
    onMounted(async () => {
      // 添加自定义类名到body，用于定位特定页面的下拉菜单样式
      document.body.classList.add('results-page-active');

      state.loading = true
      try {
        let plan = await taskPlanDetail(props.planId)
        state.detailData = plan.data || {}
        state.stationList = plan.data.forecastDispatchPlanModelConfigEntities.filter((item => item.type == 3)) //item.type == 2
        state.stationAll = plan.data.forecastDispatchPlanModelConfigEntities.filter((item => item.type == 3)) //item.type == 2
        let task = await getWorkDetail(props.taskId)
        state.workData = task.data || []
        state.chooseId = state.stationList[0].configCode
        state.beforeHour = task.data.warmUpPeriod

        // state.showDetail = state.stationList[0].sttp == 'RR' ? true : false
        // if (state.showDetail) {
        //   state.list5[0].value = state.stationList[0].forecastResStationInfoDto.topInq ? state.stationList[0].forecastResStationInfoDto.topInq.toFixed(3) : '0.00'
        //   state.list5[1].value = moment(state.stationList[0].forecastResStationInfoDto.topInqTime).format('YYYY-MM-DD HH:mm:ss')
        //   state.list5[2].value = state.stationList[0].forecastResStationInfoDto.topZ ? state.stationList[0].forecastResStationInfoDto.topZ.toFixed(3) : '0.00'
        //   state.list5[3].value = moment(state.stationList[0].forecastResStationInfoDto.topZTime).format('YYYY-MM-DD HH:mm:ss')
        //   state.list5[4].value = state.stationList[0].forecastResStationInfoDto.topOtq ? state.stationList[0].forecastResStationInfoDto.topOtq.toFixed(3) : '0.00'
        //   state.list5[5].value = moment(state.stationList[0].forecastResStationInfoDto.topOtqTime).format('YYYY-MM-DD HH:mm:ss')
        //   state.list5[6].value = state.stationList[0].forecastResStationInfoDto.peakRate ? state.stationList[0].forecastResStationInfoDto.peakRate.toFixed(3) : '0.00'
        //   state.list5[7].value = state.stationList[0].forecastResStationInfoDto.peakTime
        //   state.list5[8].value = state.stationList[0].forecastResStationInfoDto.floodCapacity ? state.stationList[0].forecastResStationInfoDto.floodCapacity.toFixed(3) : '0.00'
        // }
        state.data = task.data.resultVos.find((item) => item.stcd == state.chooseId)
        state.rainData = task.data.rainResultVos
        window.EventBus.$on('panel_show123', (data) => {
          state.current = 1
          state.value = data.STNM
          changeSlt(state.value)
        })
      } catch (error) {
        console.log(error)
      }
      state.loading = false
    })

    // 在组件卸载时移除自定义类名
    onUnmounted(() => {
      document.body.classList.remove('results-page-active');
    });

    //概化图
    const generalization = () => {
      window.EventBus.$emit('module/type/change', { name: '概化图' })
    }
    //调整方案
    const adjust = () => {
      context.emit('backAdjust');

    }
    watch(() => state.chooseId, (v) => {
      if (v) {
        state.data = state.workData.resultVos.find((item) => item.stcd == v)
        state.showDetail = state.data.sttp === 'RR'
        if (state.showDetail && state.workData.resultVos.length > 0) {
          state.list5[0].value = state.data.forecastResStationInfoDto.topInq ? state.data.forecastResStationInfoDto.topInq.toFixed(3) : '0.00'
          state.list5[1].value = state.data.forecastResStationInfoDto.topInqTime ? moment(state.data.forecastResStationInfoDto.topInqTime).format('YYYY-MM-DD HH:mm:ss') : ''
          state.list5[2].value = state.data.forecastResStationInfoDto.topZ ? state.data.forecastResStationInfoDto.topZ.toFixed(3) : '0.00'
          state.list5[3].value = state.data.forecastResStationInfoDto.topZTime ? moment(state.data.forecastResStationInfoDto.topZTime).format('YYYY-MM-DD HH:mm:ss') : ''
          state.list5[4].value = state.data.forecastResStationInfoDto.topOtq ? state.data.forecastResStationInfoDto.topOtq.toFixed(3) : '0.00'
          state.list5[5].value = state.data.forecastResStationInfoDto.topOtqTime ? moment(state.data.forecastResStationInfoDto.topOtqTime).format('YYYY-MM-DD HH:mm:ss') : ''
          state.list5[6].value = state.data.forecastResStationInfoDto.peakRate ? state.data.forecastResStationInfoDto.peakRate.toFixed(3) : '0.00'
          state.list5[7].value = state.data.forecastResStationInfoDto.peakTime
          state.list5[8].value = state.data.forecastResStationInfoDto.floodCapacity ? state.data.forecastResStationInfoDto.floodCapacity.toFixed(3) : '0.00'
          // let typeValue = state.workData.reservoirmodeList.filter((item) => item.registerCode == state.chooseId)
          // console.log(typeValue, 22222)
          // state.list5[9].value = typeValue.dispatchModelType == 1 ? '优化调度' : '自由出流'
        }
      }
    })
    const changeSlt = (v) => {
      state.resList = state.sltList[v]
      // console.log(state.resList);
      if (state.resList.length > 0) {
        state.type = state.resList[0].TYPE
        if (state.resList[0].TYPE == 'rsvr') {
          state.option1.xAxis[0].data = []
          state.option1.xAxis[1].data = []
          state.option1.series[0].data = []
          state.option1.series[1].data = []
          state.option1.series[2].data = []
          state.option1.series[3].data = []
          state.option1.series[4].data = []
          state.option1.series[5].data = []
          state.resList.forEach(el => {
            state.option1.xAxis[0].data.push(moment(el.YMDH).format("MM-DD HH"))
            state.option1.xAxis[1].data.push(moment(el.YMDH).format("MM-DD HH"))
            state.option1.series[0].data.push(el.DRP)
            state.option1.series[1].data.push(el.SC_Q)
            state.option1.series[2].data.push(el.OTQ)
            state.option1.series[3].data.push(el.INQ)
            state.option1.series[4].data.push(el.SC_Z)
            state.option1.series[5].data.push(el.Z)
          })
          nextTick(() => {
            echarts
              .init(document.getElementById('detailCharts'))
              .dispose()
            let myEchart = echarts.init(
              document.getElementById('detailCharts')
            )
            myEchart.setOption(state.option1)
          })
        } else {
          state.option.xAxis[0].data = []
          state.option.xAxis[1].data = []
          state.option.series[0].data = []
          state.option.series[1].data = []
          state.option.series[2].data = []
          state.option.series[3].data = []
          state.option.series[4].data = []
          state.resList.forEach(el => {
            state.option.xAxis[0].data.push(moment(el.YMDH).format("MM-DD HH"))
            state.option.xAxis[1].data.push(moment(el.YMDH).format("MM-DD HH"))
            state.option.series[1].data.push(el.DRP)
            state.option.series[0].data.push(el.Q)
            state.option.series[2].data.push(el.SC_Q)
            state.option.series[3].data.push(el.Z)
            state.option.series[4].data.push(el.SC_Z)
          })
          state.option.series[5].markLine.data[0].xAxis = moment(state.resList[0].IYMDH).format("MM-DD HH")
          state.option.series[5].markArea.data[0][0].xAxis = moment(state.resList[0].IYMDH).format("MM-DD HH")
          state.option.series[5].markArea.data[0][1].xAxis = moment(state.resList[0].IYMDH).format("MM-DD HH")
          state.option.series[6].markLine.data[0].xAxis = moment(state.resList[0].IYMDH).format("MM-DD HH")
          state.option.series[6].markArea.data[0][0].xAxis = moment(state.resList[0].IYMDH).format("MM-DD HH")
          state.option.series[6].markArea.data[0][1].xAxis = moment(state.resList[0].IYMDH).format("MM-DD HH")
          nextTick(() => {
            echarts
              .init(document.getElementById('detailCharts'))
              .dispose()
            let myEchart = echarts.init(
              document.getElementById('detailCharts')
            )
            myEchart.setOption(state.option)
          })
        }
      }
      // console.log(state.resObj[state.resList[0].STCD]);
      if (state.resObj[state.resList[0].STCD]) {
        state.rsShow = true
        state.list5[0].value = state.resObj[state.resList[0].STCD].MAXINQ
        state.list5[1].value = moment(state.resObj[state.resList[0].STCD].MAXINTTM).format("YYYY-MM-DD HH")
        state.list5[2].value = state.resObj[state.resList[0].STCD].MAXZ
        state.list5[3].value = moment(state.resObj[state.resList[0].STCD].MAXZTM).format("YYYY-MM-DD HH")
        state.list5[4].value = state.resObj[state.resList[0].STCD].MAXOTQ
        state.list5[5].value = moment(state.resObj[state.resList[0].STCD].MAXOTQTM).format("YYYY-MM-DD HH")
        state.list5[6].value = state.resObj[state.resList[0].STCD].XFL_
        state.list5[7].value = state.resObj[state.resList[0].STCD].XFSC_
        state.list5[8].value = state.resObj[state.resList[0].STCD].LHL_
      } else {
        state.rsShow = false

      }
    }
    return {
      ...toRefs(state),
      handleClick,
      back,
      generalization,
      adjust,
      changeSlt,
      moment
    };
  },
});
</script>

<!-- 全局样式，只在results页面激活时影响下拉菜单的样式 -->
<style>
.results-page-active .el-select__popper {
  background-color: rgba(0, 30, 80, 0.95) !important;
  border: 1px solid #0085c3 !important;
}

.results-page-active .el-select-dropdown__item {
  color: #00eaff !important;
}

.results-page-active .el-select-dropdown__item.hover,
.results-page-active .el-select-dropdown__item:hover {
  background-color: rgba(0, 50, 120, 0.8) !important;
}

.results-page-active .el-select-dropdown__item.selected {
  background-color: rgba(0, 70, 150, 0.9) !important;
  color: #fff !important;
}

.results-page-active .el-popper__arrow::before {
  background-color: rgba(0, 30, 80, 0.95) !important;
  border-color: #0085c3 !important;
}
</style>

<style scoped lang="scss">
:deep(.el-form-item__label) {
  color: #fff;
  line-height: auto;
}

:deep(.el-form-item--default) {
  margin-bottom: 10px;
}

// 自定义科幻蓝风格的select组件样式
:deep(.sci-fi-select) {
  margin: 10px 0;

  .el-input__wrapper {
    background-color: rgba(0, 40, 100, 0.8) !important;
    box-shadow: 0 0 0 1px #0085c3 inset !important;
    border-radius: 4px;

    &.is-focus {
      box-shadow: 0 0 0 1px #00acff inset !important;
    }

    .el-input__inner {
      color: #00eaff !important;
    }
  }

  .el-select__caret {
    color: #00acff !important;
  }
}

// 为下拉菜单设置样式
:deep(.el-popper.is-light) {
  background-color: rgba(0, 30, 80, 0.95) !important;
  border: 1px solid #0085c3 !important;

  .el-select-dropdown__item {
    color: #00eaff !important;

    &.hover, &:hover {
      background-color: rgba(0, 50, 120, 0.8) !important;
    }

    &.selected {
      background-color: rgba(0, 70, 150, 0.9) !important;
      color: #fff !important;
    }
  }

  .el-popper__arrow::before {
    background-color: rgba(0, 30, 80, 0.95) !important;
    border-color: #0085c3 !important;
  }
}

.box {
  width: 100%;
  // background-color: #00356c;
  background-color: rgba(1, 28, 70, 1);
  color: #fff;
  padding: 10px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1002;
  border: 1px solid #1c8bda;

  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;

  }

  .tabs {
    display: flex;
    height: 40px;
    align-items: center;
    margin-bottom: 10px;

    .items {
      margin-right: 15px;
      color: #B1D1F2;
      cursor: pointer;
      position: relative;
      font-size: 14px;

      &.act {
        color: #fff;
        font-size: 16px;

        &:after {
          content: "";
          width: 50%;
          height: 2px;
          border-radius: 2px;
          background-color: #fff;
          position: absolute;
          bottom: -7px;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }

  .summary {
    height: 700px;

    .item {
      width: 100%;
      height: 76px;
      padding: 5px;
      display: flex;
      align-items: center;
      text-align: center;
      background-color: #004081;
      margin-bottom: 10px;

      .img {
        margin: 0 10px;
        width: 78px;
        height: 66px;
        background-image: url(../../screen/image/blockBack.png);
        background-size: 100% 100%;
        padding-top: 10px;
      }

      .list {
        display: flex;

        .items {
          height: 66px;
          width: 78px;

          .value {
            color: red;
            margin: 10px 0 5px;
          }
        }

      }
    }

    .analysis {
      background-color: #004081;
      padding: 5px;
      margin-bottom: 10px;

      .top {
        display: flex;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #00ACFF;

        .title {
          font-size: 16px;
        }
      }

      .list {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 5px;
        flex-wrap: wrap;

        .items {
          text-align: center;
          width: 24%;
          overflow: hidden;
          font-size: 14px;
          color: #BDE4FF;

          .value {
            margin: 10px 0 5px;
            color: #FBC508;
            font-size: 18px;
          }
        }
      }
    }

    .statistics {

      .list {
        .items {
          width: 20%;
          height: 70px;
        }
      }
    }

    .button {
      display: flex;
      justify-content: center;
      margin: 20px 0 30px;
    }
  }

  .detail {
    height: 78vh;

    .title {
      margin: 20px 0 10px;
    }

    .desc {
      display: flex;
      flex-wrap: wrap;
      padding: 5px;
      background-color: #004081;


      .items {
        // &:nth-child(even) {
        //   width: 60%;
        // }

        // &:nth-child(odd) {
        //   width: 40%;
        // }
        width: 50%;
        font-size: 14px;
        height: 28px;
        line-height: 28px;
      }

    }

    .detailCharts {
      width: 100%;
      height: 300px;
      margin: 20px 0;
    }
  }
}

:deep(.productTable) {
  background-color: transparent !important;
  margin: 10px 0 20px;

  tr {
    background-color: transparent !important;

    th {
      background-color: #074999 !important;
      color: #BDE4FF !important;
      border-bottom: 1px solid #17365a;
      border-color: #005e9d !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #005e9d;
      border-color: #005e9d !important;

    }
  }

  --el-table-row-hover-bg-color: transparent;
  --el-table-border-color:#005e9d;
  --el-bg-color:transparent;

  .el-table__inner-wrapper {
    &::before {
      background-color: #005e9d;
    }
  }
}
</style>