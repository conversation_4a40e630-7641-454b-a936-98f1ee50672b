<template>
  <div class="head">
    <div class="title">
      拉萨市智慧水利
    </div>
    <div class="tabs">
      <div class="items" :class="current == item.id ? 'act' : ''" v-for="item in topList" :key="item.id"
        @click="handleClick(item)">{{
          item.name }}</div>
    </div>
    <div class="right-menu">
      <div class="center" @click="handleSystem">应用中心</div>
      <div class="name">{{ userName }}</div>
      <div class="time" id="time"></div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import { useRouter } from 'vue-router'
import useUserStore from "@/store/modules/user";
const userStore = useUserStore()
export default defineComponent({
  emits: ["headCurrent"],
  setup(props, context) {
    const state = reactive({
      topList: [
        {
          id: 4,
          name: '风险评估'
        },
        {
          id: 0,
          name: '态势分析'
        },
        {
          id: 1,
          name: '防洪预演'
        },
        {
          id: 2,
          name: '避险转移'
        },
        {
          id: 3,
          name: '淹没分析'
        }
      ],
      current: 4,
      userName: '',
    })
    const router = useRouter();
    const handleSystem = () => {
      router.push('/warning/warningIssue')
    }

    function updateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      const hours = now.getHours();
      const minutes = now.getMinutes();
      const seconds = now.getSeconds();
      const timeString = year + '年' + month + '月' + day + '日' + hours.toString().padStart(2, '0') + ":" +
        minutes.toString().padStart(2, '0') + ":" +
        seconds.toString().padStart(2, '0');
      document.getElementById("time").innerHTML = timeString;
    }

    // 每秒钟更新一次时间
    setInterval(updateTime, 1000);
    const handleClick = (item) => {

      state.current = item.id
      context.emit('headCurrent', state.current);
      window.EventBus.$emit('module/type/change', item)

    }
    onMounted(() => {
      state.userName = userStore.name
    })
    return {
      ...toRefs(state),
      handleSystem,
      handleClick
    };
  },
});
</script>
<style scoped lang="scss">
.head {
  width: 100%;
  height: 73px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
  background-image: url(../image/headImg.png);
  background-size: 100% 100%;
  font-family: SourceHanSansCN-Regular Source Han Sans CN;

  .title {
    width: 340px;
    // height: 34px;
    //字体间隔
    letter-spacing: 4px;
    font-size: 35px;
    font-weight: 600;
    background-image: -webkit-linear-gradient(bottom, white, #05adef);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 60px;
    margin-left: 40px;
  }

  .tabs {
    width: 30%;
    height: 70%;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .items {
      font-size: 20px;
      line-height: 50px;
      // width: 33%;
      height: 100%;
      color: #7dbfec;
      cursor: pointer;
      text-align: center;
      width: 100px;
      border-bottom: 3px solid #1095ff;

      &.act {
        color: #fff;
        border-bottom: 3px solid #f9ce4d;
      }
    }
  }

  .right-menu {
    width: 29%;
    height: 40px;
    display: flex;
    align-items: center;
    text-align: center;
    color: #A3DEFF;
    font-size: 18px;

    .center {
      width: 30%;
      border-right: 2px solid #025ba1;
      cursor: pointer;
    }

    .name {
      width: 30%;
      border-right: 2px solid #025ba1;
    }

    .time {
      width: 40%;
      font-size: 16px;
    }
  }
}
</style>
