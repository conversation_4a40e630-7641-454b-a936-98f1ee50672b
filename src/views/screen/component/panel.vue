<template>
  <div class="box-border">
    <span></span>
    <span></span>
    <span></span>
    <span></span>
    <div class="box1 map-panel">
      <div class="map-panel-content">
        <div class="head">
          <div class="title">{{ mainPanel.title }}</div>
          <div class="close" @click="closeMask"></div>
        </div>
        <div class="line">
          <div class="left"></div>
          <div class="center"></div>
          <div class="right"></div>
        </div>
        <!-- 雨量站 -->
        <RainDialog
          v-if="mainPanel.type == 'PP'"
          :data1="mainPanel.data"
        ></RainDialog>
        <!-- 河道站 -->
        <RiverDialog
          v-if="mainPanel.type == 'ZZ' || mainPanel.type == 'ZQ'"
          :data1="mainPanel.data"
        ></RiverDialog>
        <!-- 水库站 -->
        <ReservoirDialog
          v-if="mainPanel.type == 'RR'"
          :data1="mainPanel.data"
        ></ReservoirDialog>
        <!-- 水闸 -->
        <GateDialog
          v-if="mainPanel.type == 'Gate'"
          :data1="mainPanel.data"
        ></GateDialog>
        <!-- 视频监控 -->
        <div
          class="video-player"
          v-if="mainPanel.type == 'VIDEO'"
          v-loading="loading"
        >
          <HikvisionVideoPlayer
            v-if="videoInfo.videoUrl"
            :videoInfo="videoInfo"
            :showControls="true"
            :autoplay="true"
            :muted="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import RainDialog from "./panels/raindialog.vue";
import RiverDialog from "./panels/riverdialog.vue";
import ReservoirDialog from "./panels/reservoirdialog.vue";
import GateDialog from "./panels/gatedialog.vue";
import HikvisionVideoPlayer from "@/views/queryStatistics/video/components/HikvisionVideoPlayer.vue";
import { useMapPanelStore } from "@/store/modules/map";
import { getOnlineVideoPushUrl } from "@/api/video/online";

const panelStore = useMapPanelStore();

const { mainPanel } = storeToRefs(panelStore);

const closeMask = () => {
  mainPanel.value.visible = false;
  mainPanel.value.data = {};
};

const videoInfo = ref({});
const loading = ref(false);

const getVideoUrl = async (data) => {
  try {
    loading.value = true;
    const res = await getOnlineVideoPushUrl({
      cameraIndexCode: data.STCD,
      protocol: "ws",
      streamType: 0,
      transmode: 1,
    });

    if (res.code == 200) {
      videoInfo.value = {
        videoUrl: res.data,
        id: data.STCD,
        name: data.STNM,
        msg: "",
        useHikvision: true,
      };
    }
  } catch (error) {
    console.log(error, "error");
  } finally {
    loading.value = false;
  }
};

watch(
  mainPanel.value,
  (newVal) => {
    if (newVal.type == "VIDEO") {
      getVideoUrl(newVal.data);
    }
  },
  { immediate: true }
);
</script>
<style scoped lang="scss">
.box-border {
  background: rgba(0, 57, 115, 0);
  border: 1px solid #1c8bda;
  width: auto;
  height: auto;
  padding: 6px;
}

.box-border span:nth-child(1) {
  position: absolute;
  left: 0;
  top: 0;
  padding: 10px;
  border-style: solid;
  border-color: #00ccff;
  border-width: 2px 0 0 2px;
}

.box-border span:nth-child(2) {
  position: absolute;
  right: -1px;
  top: -1px;
  padding: 10px;
  border-style: solid;
  border-color: #00ccff;
  border-width: 2px 2px 0 0;
}

.box-border span:nth-child(3) {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 10px;
  border-style: solid;
  border-color: #00ccff;
  border-width: 0 2px 2px 0;
}

.box-border span:nth-child(4) {
  position: absolute;
  left: 0;
  bottom: -1px;
  padding: 10px;
  border-style: solid;
  border-color: #00ccff;
  border-width: 0 0 2px 2px;
}

.box1 {
  width: 560px;
  color: #ccebff;
  //background-color: #00356c;
  //border: 1px solid #0091fd;

  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    padding: 5px 10px 0;

    .close {
      width: 14px;
      height: 14px;
      background-image: url(../image/close.png);
      background-size: 100% 100%;
      cursor: pointer;
    }
  }

  .line {
    margin: 10px 0 0;
    padding: 0 10px;

    .left,
    .right {
      /* flex:0 0 100px; */
      width: 10px;
      height: 2px;
      background-color: #ffd154;
    }
  }
}

.video-player {
  width: 100%;
  height: 400px;
}
</style>
