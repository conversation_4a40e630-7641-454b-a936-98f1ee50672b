<template>
  <div class="box-border">
    <span></span>
    <span></span>
    <span></span>
    <span></span>
    <div class="box1 map-panel">
      <div class="map-panel-content">
        <div class="head">
          <div class="title">{{ title }}</div>
          <div class="close" @click="closeMask"></div>
        </div>
        <div class="line">
          <div class="left"></div>
          <div class="center"></div>
          <div class="right"></div>
        </div>
        <div class="achievement" v-if="rsShow">
          <div class="top">调度成果对比</div>
          <el-table class="productTable" :data="list" :header-cell-style="{ textAlign: 'center', color: '#ffffff' }"
            :cell-style="{ color: '#ffffff', textAlign: 'center', fontSize: 13 + 'px' }">
            <!-- <el-table-column label="序号" type="index" width="50"></el-table-column> -->
            <el-table-column prop="DISPNM" label="方案">
              <!-- <template v-slot="scope">
                <span>{{ scope.row.DISPNM }}</span>
              </template> -->
            </el-table-column>
            <el-table-column prop="DISPID" label="ID"></el-table-column>
            <el-table-column prop="MAXINQ" label="入库洪峰(m³/s)"></el-table-column>
            <el-table-column prop="MAXINTTM" label="峰现时间"></el-table-column>
            <el-table-column prop="MAXZ" label="最高库水位(m)"></el-table-column>
            <el-table-column prop="MAXZTM" label="最高库水位出现时间"></el-table-column>
            <el-table-column prop="MAXOTQ" label="最大出库流量 (m³/s)"></el-table-column>
            <el-table-column prop="MAXOTQTM" label="最大出库流量出现时间"></el-table-column>
            <el-table-column prop="XFL_" label="削峰率(%)"></el-table-column>
            <el-table-column prop="XFSC_" label="错峰时长(h)"></el-table-column>
            <el-table-column prop="LHL_" label="拦洪量(万m³)"></el-table-column>
          </el-table>
        </div>
        <div class="charts">
          <div class="item">
            <div class="top"> {{ rsShow ? '库水位过程对比' : '水位过程对比' }} </div>
            <div class="leftEcharts" id="leftEcharts"></div>
          </div>
          <div class="item">
            <div class="top">{{ rsShow ? '出入库流量过程对比' : '流量过程对比' }}</div>
            <div class="rightEcharts" id="rightEcharts"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, nextTick } from "vue";
import { getDisStatResList, getDisYmResTj_Ddcg } from "../../../api/watershed/screenRight/dispatch"
import * as echarts from "echarts";
import moment from "moment";
import useAppStore from '@/store/modules/app'
import { useMapPanelStore } from '@/store/modules/map'

export default defineComponent({
  setup(props, context) {
    const panelStore = useMapPanelStore();
    const { comparisonPanel } = storeToRefs(panelStore);

    const state = reactive({
      title: '某某水库调度对比',
      arr: [],
      list: [],
      leftOption: {
        grid: {
          left: '60px',  //距左边距 留够name的宽度
          right: '60px',
          bottom: '50px',
          top: '30px',
          // containLabel: true
        },
        legend: {
          show: true,
          textStyle: {
            color: '#fff'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        xAxis: {
          type: 'category',
          data: [],
          boundaryGap: true,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            // interval: "0",
            color: "#fff",
            overflow: 'truncate',
            // width: '100'
          },
          axisLine: {
            lineStyle: {
              color: "#fff"
            }
          },
          axisTick: {
            show: false
          },
        },
        yAxis: [
          {
            type: 'value',
            name: '水位(m)',
            // scale: true,
            nameTextStyle: {
              color: "#fff"
            },
            axisLabel: {
              color: "#fff"
            },
            splitLine: {
              lineStyle: {
                color: "#005b99"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
          },

        ],
        series: [
          {
            name: '库水位',
            type: 'line',
            // smooth: true,
            symbol: "none",
            color: "#059DFE",
            data: [],
            // barWidth: 20
          },
          {
            name: '库水位',
            type: 'line',
            // smooth: true,
            symbol: "none",
            color: "#fac858",
            data: []
          },
        ]
      },
      leftOption1: {
        grid: {
          left: '60px',  //距左边距 留够name的宽度
          right: '60px',
          bottom: '50px',
          top: '30px',
          // containLabel: true
        },
        legend: {
          show: true,
          textStyle: {
            color: '#fff'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        xAxis: {
          type: 'category',
          data: [],
          boundaryGap: true,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            // interval: "0",
            color: "#fff",
            overflow: 'truncate',
            // width: '100'
          },
          axisLine: {
            lineStyle: {
              color: "#fff"
            }
          },
          axisTick: {
            show: false
          },
        },
        yAxis: [
          {
            type: 'value',
            name: '流量(m³/s)',
            // scale: true,
            nameTextStyle: {
              color: "#fff"
            },
            axisLabel: {
              color: "#fff"
            },
            splitLine: {
              lineStyle: {
                color: "#005b99"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
          },

        ],
        series: [
          {
            name: '流量',
            type: 'line',
            // smooth: true,
            symbol: "none",
            color: "#059DFE",
            data: [],
            // barWidth: 20
          },
          {
            name: '流量',
            type: 'line',
            // smooth: true,
            symbol: "none",
            color: "#fac858",
            data: []
          },
        ]
      },
      rightOption: {
        grid: {
          left: '60px',  //距左边距 留够name的宽度
          right: '60px',
          bottom: '50px',
          top: '50px',
          // containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross', crossStyle: { color: '#555' }, label: {
              backgroundColor: '#555'
            }
          }
        },
        legend: {
          width: '200px',
          show: true,
          textStyle: {
            color: '#fff'
          }
        },
        xAxis: {
          type: 'category',
          data: [],
          boundaryGap: true,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            // interval: "0",
            color: "#fff",
            overflow: 'truncate',
            // width: '100'
          },
          axisLine: {
            lineStyle: {
              color: "#fff"
            }
          },
          axisTick: {
            show: false
          },
        },
        yAxis: [
          {
            type: 'value',
            name: '流量(m³/s)',
            // scale: true,
            nameTextStyle: {
              color: "#fff"
            },
            axisLabel: {
              color: "#fff"
            },
            splitLine: {
              lineStyle: {
                color: "#005b99"
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff"
              },
            },
          },

        ],
        series: [
          {
            name: '出库流量',
            type: 'line',
            // smooth: true,
            symbol: "none",
            color: "#059DFE",
            data: [],
            // barWidth: 20
          },
          {
            name: '出库流量',
            type: 'line',
            // smooth: true,
            symbol: "none",
            color: "#fac858",
            data: []
          },
          {
            name: '入库流量',
            type: 'line',
            // smooth: true,
            symbol: "none",
            color: "#059DFE",
            data: [],
            // barWidth: 20
          },
          {
            name: '入库流量',
            type: 'line',
            // smooth: true,
            symbol: "none",
            color: "#fac858",
            data: []
          },
        ]
      },
      sltList: [],
      sltList1: [],
      obj: {},
      rsShow: true
    })
    const closeMask = () => {
      comparisonPanel.value.visible = false;
    }

    watch(() => comparisonPanel.value.visible, (newVal) => {
      if (newVal) {
        state.obj = comparisonPanel.value.data
        show(state.obj)
      } else {
        state.arr = []
        state.list = []
        state.leftOption.series[0].data = []
        state.leftOption.series[1].data = []
        state.leftOption.xAxis.data = []
        state.rightOption.series[0].data = []
        state.rightOption.series[1].data = []
        state.rightOption.series[2].data = []
        state.rightOption.series[3].data = []
        state.rightOption.xAxis.data = []
      }
    }, { immediate: true })

    const appStore = useAppStore()

    const show = (data) => {
      state.arr = appStore.ctstList
      executeApi()
      state.list = []
      getDisYmResTj_Ddcg({
        dispId: state.arr[0].ID
      }).then(res => {
        if (res.data[state.obj.STCD]) {
          state.rsShow = true
          state.list.push(res.data[state.obj.STCD])
          getDisYmResTj_Ddcg({
            dispId: state.arr[1].ID
          }).then(re => {
            state.list.push(re.data[state.obj.STCD])
            // console.log(state.list);
          })
        } else {
          state.rsShow = false

        }
      })
    }
    const executeApi = async () => {
      const res = await getDisStatResList({
        dispId: state.arr[0].ID
      })
      state.sltList = res.data[state.obj.STNM]
      state.title = state.obj.STNM + '调度对比'
      getDisStatResList({
        dispId: state.arr[1].ID
      }).then(res => {
        state.sltList1 = res.data[state.obj.STNM]
        state.leftOption.series[0].data = []
        state.leftOption.series[1].data = []
        state.leftOption.xAxis.data = []
        state.rightOption.series[0].data = []
        state.rightOption.series[1].data = []
        state.rightOption.series[2].data = []
        state.rightOption.series[3].data = []
        state.rightOption.xAxis.data = []
        if (state.sltList1[0].TYPE == 'rsvr') {
          state.sltList1.forEach((el, idx) => {
            // console.log(el);
            // console.log(state.sltList);
            state.leftOption.series[0].data.push(el.Z)
            state.leftOption.series[1].data.push(state.sltList[idx].Z)
            state.leftOption.xAxis.data.push(moment(el.YMDH).format("YYYY-MM-DD HH:mm:ss"))
            state.rightOption.xAxis.data.push(moment(el.YMDH).format("YYYY-MM-DD HH:mm:ss"))
            state.rightOption.series[0].data.push(el.OTQ)
            state.rightOption.series[1].data.push(state.sltList[idx].OTQ)
            state.rightOption.series[2].data.push(el.INQ)
            state.rightOption.series[3].data.push(state.sltList[idx].INQ)
          })
          state.leftOption.series[0].name = state.sltList1[0].DISPID + '库水位'
          state.leftOption.series[1].name = state.sltList[0].DISPID + '库水位'
          state.rightOption.series[0].name = state.sltList1[0].DISPID + '出库流量'
          state.rightOption.series[1].name = state.sltList[0].DISPID + '出库流量'
          state.rightOption.series[2].name = state.sltList1[0].DISPID + '入库流量'
          state.rightOption.series[3].name = state.sltList[0].DISPID + '入库流量'

          nextTick(() => {
            echarts
              .init(document.getElementById('leftEcharts'))
              .dispose()
            let myEchart = echarts.init(
              document.getElementById('leftEcharts')
            )
            myEchart.setOption(state.leftOption)
            echarts
              .init(document.getElementById('rightEcharts'))
              .dispose()
            let myEchart1 = echarts.init(
              document.getElementById('rightEcharts')
            )
            myEchart1.setOption(state.rightOption)
          })
        } else if (state.sltList1[0].TYPE == 'river') {
          state.sltList1.forEach((el, idx) => {
            // console.log(el);
            // console.log(state.sltList);
            state.leftOption.series[0].data.push(el.Z)
            state.leftOption.series[1].data.push(state.sltList[idx].Z)
            state.leftOption.xAxis.data.push(moment(el.YMDH).format("YYYY-MM-DD HH:mm:ss"))
            state.leftOption1.xAxis.data.push(moment(el.YMDH).format("YYYY-MM-DD HH:mm:ss"))
            state.leftOption1.series[0].data.push(el.Q)
            state.leftOption1.series[1].data.push(state.sltList[idx].Q)
          })
          state.leftOption.series[0].name = state.sltList1[0].DISPID + '水位'
          state.leftOption.series[1].name = state.sltList[0].DISPID + '水位'
          state.leftOption1.series[0].name = state.sltList1[0].DISPID + '流量'
          state.leftOption1.series[1].name = state.sltList[0].DISPID + '流量'

          nextTick(() => {
            echarts
              .init(document.getElementById('leftEcharts'))
              .dispose()
            let myEchart = echarts.init(
              document.getElementById('leftEcharts')
            )
            myEchart.setOption(state.leftOption)
            echarts
              .init(document.getElementById('rightEcharts'))
              .dispose()
            let myEchart1 = echarts.init(
              document.getElementById('rightEcharts')
            )
            myEchart1.setOption(state.leftOption1)
          })
        }
      })
    }

    return {
      ...toRefs(state),
      closeMask
    };
  },
});
</script>
<style scoped lang="scss">
.box-border {
  background: rgba(0, 57, 115, 0);
  border: 1px solid #1C8BDA;
  width: auto;
  height: auto;
  padding: 6px;
  z-index: 1000 !important;
}

.box-border span:nth-child(1) {
  position: absolute;
  left: 0;
  top: 0;
  padding: 10px;
  border-style: solid;
  border-color: #00CCFF;
  border-width: 2px 0 0 2px;
}

.box-border span:nth-child(2) {
  position: absolute;
  right: -1px;
  top: -1px;
  padding: 10px;
  border-style: solid;
  border-color: #00CCFF;
  border-width: 2px 2px 0 0;
}

.box-border span:nth-child(3) {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 10px;
  border-style: solid;
  border-color: #00CCFF;
  border-width: 0 2px 2px 0;
}

.box-border span:nth-child(4) {
  position: absolute;
  left: 0;
  bottom: -1px;
  padding: 10px;
  border-style: solid;
  border-color: #00CCFF;
  border-width: 0 0 2px 2px;
}

.box1 {
  width: 1000px;
  color: #CCEBFF;
  //background-color: #00356c;
  //border: 1px solid #0091fd;

  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    padding: 5px 10px 0;

    .close {
      width: 14px;
      height: 14px;
      background-image: url(../image/close.png);
      background-size: 100% 100%;
      cursor: pointer;
    }
  }

  .line {
    margin: 10px 0 0;
    padding: 0 10px;

    .left,
    .right {
      /* flex:0 0 100px; */
      width: 10px;
      height: 2px;
      background-color: #ffd154;
    }
  }

  .achievement {
    padding: 10px;
  }

  .charts {
    display: flex;

    .item {
      width: 50%;
      padding: 0 10px 10px;

      .leftEcharts {
        width: 100%;
        height: 300px;
      }

      .rightEcharts {
        width: 100%;
        height: 300px;
      }
    }
  }
}

:deep(.productTable) {
  background-color: transparent !important;
  margin: 10px 0 20px;

  tr {
    background-color: transparent !important;

    th {
      background-color: #074999 !important;
      color: #BDE4FF !important;
      border-bottom: 1px solid #17365a;
      border-color: #005e9d !important;
    }

    td {
      color: #92b7e9 !important;
      border-bottom: 1px solid #005e9d;
      border-color: #005e9d !important;

    }
  }

  --el-table-row-hover-bg-color: transparent;
  --el-table-border-color:#005e9d;
  --el-bg-color:transparent;

  .el-table__inner-wrapper {
    &::before {
      background-color: #005e9d;
    }
  }
}
</style>
