<template>
  <div class="box">
    <div class="tab">
      <div class="items" v-for="item in rightList" :key="item.id" :class="current == item.id ? 'act' : ''"
        @click="changeTab(item)"> {{ item.text }}</div>
    </div>
    <div class="overview itemCont" v-show="current == 0">
      <el-scrollbar>
        <Overview></Overview>
      </el-scrollbar>
    </div>
    <div class="meteorological itemCont" v-show="current == 4">
      <el-scrollbar>
        <Meteorological></Meteorological>
      </el-scrollbar>
    </div>
    <div class="warn itemCont" v-show="current == 3">
      <WarnInfo></WarnInfo>
    </div>
    <div class="itemCont pd15" v-show="current == 2">
      <Regimen></Regimen>
    </div>
    <div class="itemCont  pd15" v-show="current == 1">
      <Rain></Rain>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import Overview from './overview.vue'
import Meteorological from './meteorological.vue'
import WarnInfo from './warnInfo.vue'
import Rain from './rain.vue'
import Regimen from './regimen.vue'
export default defineComponent({
  components: {
    Overview,
    Meteorological,
    WarnInfo,
    Rain,
    Regimen
  },
  setup() {
    const state = reactive({
      rightList: [
        {
          id: 0,
          text: '概览'
        },
        {
          id: 3,
          text: '预警'
        },
        {
          id: 1,
          text: '雨情'
        },
        {
          id: 2,
          text: '水情'
        },

        {
          id: 4,
          text: '气象'
        },
      ],
      current: 0
    })
    const changeTab = (item)=>{
      state.current = item.id
      // 点击气象显示面板
      if (state.current === 4) {
        window.EventBus.$emit('layerCloud/visible', true)
      } else {
        window.EventBus.$emit('layerCloud/visible', false)
      }
      // 点击雨情显示面板， 默认雨情把查询去掉了，切换再去查询列表数据
      if (state.current === 1) {
        window.EventBus.$emit('rainPanel/visible', item)
      }
      // 点击水情显示面板， 默认水情把查询去掉了，切换再去查询列表数据
      if (state.current === 2) {
        window.EventBus.$emit('riverPanel/visible', item)
      }

      if(state.current === 4) {
        window.EventBus.$emit('change/right/height/min', {} )
      } else{
        window.EventBus.$emit('change/right/height/max', {} )
      }
      // 点击概览，雨情的需要还原
      if (state.current === 0) {
        window.EventBus.$emit('overview/rain/update', {})
      }
    }
    onMounted(()=>{
      // 增加个外部change tab
      window.EventBus.$on('gotoTab', (data) => {
        changeTab(data)
      })

    })
    return {
      ...toRefs(state),
      changeTab
    };
  },
});
</script>
<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;

  .tab {
    display: flex;
    width: 100%;
    height: 29px;
    padding-left: 30px;

    .items {
      width: 130px;
      height: 100%;
      line-height: 30px;
      font-size: 16px;
      color: #86C9F7;
      text-align: left;
      padding-left: 20px;
      cursor: pointer;
      background-image: url(../image/tabImg.png);
      // background-size: auto 100%;
      transform: translateX(-30px);



      &.act {
        color: #fff;
        background-image: url(../image/tabImgAct.png);
      }
      //
      //&:nth-child(4) {
      //  background-image: url(../image/orangeImg.png);
      //  color: #DADCAA;
      //
      //}
    }
  }

  .itemCont {
    width: 100%;
    height: calc(100% - 30px);
    // background-color: #032756;
    border: 1px solid #1C8BDA;
  }

  .pd15 {
    padding: 15px;
    background-color: rgba(1, 28, 70, 0.7);

  }
}
</style>
<style  lang="scss">
.line {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 2px;

  .left,
  .right {
    /* flex:0 0 100px; */
    width: 10px;
    height: 2px;
    background-color: #3276b1;
  }

  .center {
    flex: 1;
    height: 2px;
    background-color: #19477a;
  }
}
</style>
