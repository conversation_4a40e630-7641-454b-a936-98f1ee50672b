<template>
  <div class="meteorological">
    <div class="meteorological-header">
      <el-radio-group v-model="curType" @change="changeType">
        <el-radio :label="1">气象云图</el-radio>
        <el-radio :label="2">雷达回波图</el-radio>
        <el-radio :label="3">格网降雨预报</el-radio>
        <el-radio :label="4">山洪气象预警</el-radio>
      </el-radio-group>
    </div>
    <div v-show="curType == 5" class="meteorological-content">
      <div class="title">风险影响分析</div>
      <div class="line">
        <div class="left"></div>
        <div class="center"></div>
        <div class="right"></div>
      </div>
      <div class="meteorBox">
        <div class="form"></div>
        <div class="number">
          <div class="township">2个镇</div>
          <div class="village">12个村</div>
          <div class="resident">700户居民</div>
          <div class="people">3000人</div>
          <el-popover
            placement="left"
            :width="170"
            trigger="hover"
            content="高风险指 降雨量达到 250mm 以上区域"
          >
            <template #reference>
              <div class="askIcon"></div>
            </template>
          </el-popover>
        </div>
        <el-table
          class="productTable"
          :data="list1"
          :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
          :cell-style="{
            color: '#ffffff',
            textAlign: 'center',
            fontSize: 13 + 'px',
          }"
        >
          <el-table-column prop="risk" label="风险对象"></el-table-column>
          <el-table-column prop="village" label="影响村庄"></el-table-column>
          <el-table-column prop="resident" label="居民户数"></el-table-column>
          <el-table-column prop="people" label="人口数"></el-table-column>
        </el-table>
        <div class="number" style="background-color: #bf4c00">
          <div class="township">2个镇</div>
          <div class="village">12个村</div>
          <div class="resident">700户居民</div>
          <div class="people">3000人</div>
          <el-popover
            placement="left"
            :width="170"
            trigger="hover"
            content="较高风险指 降雨量达到 100~250mm 以上区域"
          >
            <template #reference>
              <div class="askIcon"></div>
            </template>
          </el-popover>
          <!-- <div class="askIcon"></div> -->
        </div>
        <el-table
          class="productTable"
          :data="list1"
          :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
          :cell-style="{
            color: '#ffffff',
            textAlign: 'center',
            fontSize: 13 + 'px',
          }"
        >
          <el-table-column prop="risk" label="风险对象"></el-table-column>
          <el-table-column prop="village" label="影响村庄"></el-table-column>
          <el-table-column prop="resident" label="居民户数"></el-table-column>
          <el-table-column prop="people" label="人口数"></el-table-column>
        </el-table>
        <div class="number" style="background-color: #dd8f13">
          <div class="township">2个镇</div>
          <div class="village">12个村</div>
          <div class="resident">700户居民</div>
          <div class="people">3000人</div>
          <el-popover
            placement="left"
            :width="170"
            trigger="hover"
            content="中风险指 降雨量达到 50~100mm 以上区域"
          >
            <template #reference>
              <div class="askIcon"></div>
            </template>
          </el-popover>
        </div>
        <el-table
          class="productTable"
          :data="list1"
          :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
          :cell-style="{
            color: '#ffffff',
            textAlign: 'center',
            fontSize: 13 + 'px',
          }"
        >
          <el-table-column prop="risk" label="风险对象"></el-table-column>
          <el-table-column prop="village" label="影响村庄"></el-table-column>
          <el-table-column prop="resident" label="居民户数"></el-table-column>
          <el-table-column prop="people" label="人口数"></el-table-column>
        </el-table>
        <div class="number" style="background-color: #0595d4">
          <div class="township">2个镇</div>
          <div class="village">12个村</div>
          <div class="resident">700户居民</div>
          <div class="people">3000人</div>
          <el-popover
            placement="left"
            :width="170"
            trigger="hover"
            content="低风险指降雨量达到 25~50mm 以上区域"
          >
            <template #reference>
              <div class="askIcon"></div>
            </template>
          </el-popover>
        </div>
        <el-table
          class="productTable"
          :data="list1"
          :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
          :cell-style="{
            color: '#ffffff',
            textAlign: 'center',
            fontSize: 13 + 'px',
          }"
        >
          <el-table-column prop="risk" label="风险对象"></el-table-column>
          <el-table-column prop="village" label="影响村庄"></el-table-column>
          <el-table-column prop="resident" label="居民户数"></el-table-column>
          <el-table-column prop="people" label="人口数"></el-table-column>
        </el-table>
      </div>
    </div>
    <div v-show="curType == 4" class="meteorological-content">
      <div style="text-align: center">
        <el-radio-group v-model="hourType" @change="changeTimeType">
          <el-radio :label="3">未来24小时</el-radio>
          <el-radio :label="2">未来12小时</el-radio>
          <el-radio :label="1">未来6小时</el-radio>
        </el-radio-group>
      </div>
      <div class="exceed">
        <div style="width: 100%; height: 40px; display: flex">
          <div
            class="items"
            :class="waterLevel === item.name ? 'active' : ''"
            v-for="(item, index) in colors"
            :key="index"
          >
            <!--             @click="handlerHourTypeFilter(item)"-->
            <div class="image"><img :src="item.img" style="width: 50px" /></div>
            <div class="value" :style="{ color: item.color }">
              {{ item.value }}
            </div>
          </div>
        </div>
        <div style="width: 100%; height: 30px; display: flex">
          <div
            class="items"
            :class="waterLevel === item.name ? 'active' : ''"
            v-for="(item, index) in colors"
            :key="index"
          >
            <div class="label" :style="{ color: item.color }">
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>

      <div class="meteorBox">
        <el-table
          class="productTable"
          :data="warnList"
          :span-method="objectSpanMethod"
          border
          :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
          :cell-style="{
            color: '#ffffff',
            textAlign: 'center',
            fontSize: 13 + 'px',
          }"
        >
          <el-table-column prop="padnm" label="地市"></el-table-column>
          <el-table-column prop="adnm" label="区县"></el-table-column>
          <el-table-column prop="warnLevel" label="预警等级" align="center">
            <template #default="scope">
              <div>{{ getWarnLevelName(scope.row) }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="rn" label="区域雨量"></el-table-column>
          <el-table-column prop="warnValue" label="预警指标"></el-table-column>
          <!--          <template v-if="curTimeType === 6">-->
          <!--            <el-table-column  prop="sixHourValue" label="预警指标"></el-table-column>-->
          <!--          </template>-->
          <!--          <template v-if="curTimeType === 12">-->
          <!--            <el-table-column v-show="curTimeType === 12" prop="twelveHourValue" label="预警指标"></el-table-column>-->
          <!--          </template>-->
          <!--          <template v-if="curTimeType === 24">-->
          <!--            <el-table-column v-show="curTimeType === 24" prop="twentyFourHourValue" label="预警指标"></el-table-column>-->
          <!--          </template>-->
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import redImg from "@/assets/icon/warn_red.png";
import orangeImg from "@/assets/icon/warn_orange.png";
import yellowImg from "@/assets/icon/warn_yellow.png";
import blueImg from "@/assets/icon/warn_blue.png";
export default defineComponent({
  setup() {
    const state = reactive({
      value: false, // 默认不可见，勾选完显示地图上面板
      list1: [
        {
          risk: "某某风险区",
          village: "王庄小李村",
          resident: "100",
          people: "4000",
        },
        {
          risk: "地质灾害风险点",
          village: "王庄小李村",
          resident: "100",
          people: "4000",
        },
      ],
      warnList: [],
      curType: 1, //
      curTimeType: 24, //
      adcd: "130000000000", //
      lycode: "", //
      waterLevel: "",
      hourType: 3, // 1未来六小时，2未来十二小时，3未来二十四小时
      colors: [
        {
          waterLevel: 4,
          name: "红色",
          value: "-",
          img: redImg,
          color: "rgba(255,0,0,0.8)",
        },
        {
          waterLevel: 3,
          name: "橙色",
          value: "-",
          img: orangeImg,
          color: "rgba(222,130,38,0.8)",
        },
        {
          waterLevel: 2,
          name: "黄色",
          value: "-",
          img: yellowImg,
          color: "rgba(242,255,0,0.8)",
        },
        {
          waterLevel: 1,
          name: "蓝色",
          value: "-",
          img: blueImg,
          color: "rgba(0,165,255,0.8)",
        },
      ],
    });
    const openCloudPanel = (isCheck) => {
      window.EventBus.$emit("layerCloud/visible", state.value);
    };
    const changeType = (e) => {
      window.EventBus.$emit("cloudPlay/change", e);
      // if (e === 3 || e === 4) {
      //   window.EventBus.$emit('change/right/height/max', {})
      // } else {
      //   window.EventBus.$emit('change/right/height/min', {})
      // }
      window.EventBus.$emit("change/right/height/min", {});

      if (e === 4) {
        getFloodWarnList();
      }
    };
    // 拼凑数组出来
    const getTempWarnList = (item, list) => {
      let allLevels = [1, 2, 3, 4];
      list.forEach((ii) => {
        let index = allLevels.indexOf(ii.warnLevel);
        if (index > -1) {
          //大于0 代表存在，
          allLevels.splice(index, 1); //存在就删除
        }
      });
      allLevels.forEach((level) => {
        list.push({
          padnm: item.padnm,
          adnm: item.adnm,
          adcd: item.adcd,
          warnLevel: level,
        });
      });
      return list;
    };

    // 需要判断的属性组
    const spanProps = ["padnm", "adnm", "warnLevel", "rn"];

    let rowSpansMap = new Map(); //存需要开始合并的行号，向下合并多少行

    /**
     * 根据列表数据得出需要合并的行
     * @param data 列表数据
     */
    const spanPropGroup = (data) => {
      let oldRow; //需要合并的行
      rowSpansMap = new Map(); //重置Map

      oldRow = data[0]; //默认第0行为需要合并的行
      rowSpansMap.set(0, 1); //第0行，向下合并一行(其实就是自己单独一行)
      let spanRow = 0; //记录需要开始合并的行号
      for (let i = 1; i < data.length; i++) {
        const item = data[i];
        let isSame = true;
        //遍历需要判断的属性判断对应值是否全部相等
        for (let j = 0; j < spanProps.length; j++) {
          const prop = spanProps[j];
          //只要有一个属性值不相等则记录新的需要合并的行号
          if (item[prop] != oldRow[prop]) {
            oldRow = item;
            rowSpansMap.set(i, 1);
            spanRow = i;
            isSame = false;
            break;
          }
        }
        //如果所有属性值相同则所需要合并的行数+1
        if (isSame) {
          let span = rowSpansMap.get(spanRow);
          rowSpansMap.set(spanRow, span + 1);
        }
      }
    };

    const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      // 只有前两列需要合并
      if (columnIndex < 3) {
        //根据当前行号从map中获取开始合并的行根据当前行号从map中获取开始合并的行号，向下合并多少行
        const span = rowSpansMap.get(rowIndex);
        if (span != null) {
          return {
            rowspan: span, //向下合并span行
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    };
    const getWarnLevelName = (data) => {
      let name = "--";
      if (data.warnLevel === 1) {
        name = "可能发生";
      } else if (data.warnLevel === 2) {
        name = "可能性较大";
      } else if (data.warnLevel === 3) {
        name = "可能性大";
      } else if (data.warnLevel === 4) {
        name = "可能性很大";
      }
      return name;
    };
    const getFloodWarnList = () => {
      let params = {
        adcd: state.adcd,
        hourType: state.hourType,
        waterLevel: state.waterLevel,
      };
      // selectFloodWarn1(state.adcd).then(res => {
      let ll = {
        red: 2,
        orange: 0,
        blue: 1,
        yellow: 2,
        list: [
          {
            adcd: "130227000000",
            adnm: "涉县",
            padcd: "130200000000",
            padnm: "邯郸市",
            warnLevel: 4,
            rn: 107.58,
            warnValue: 2,
            ymdh: null,
          },
          {
            adcd: "130225000000",
            adnm: "平顺县",
            padcd: "130200000000",
            padnm: "长治市",
            warnLevel: 4,
            rn: 23.04,
            warnValue: 1,
            ymdh: null,
          },
          {
            adcd: "130223000000",
            adnm: "林州市",
            padcd: "130200000000",
            padnm: "安阳市",
            warnLevel: 2,
            rn: 84.6,
            warnValue: 3.3,
            ymdh: null,
          },
          {
            adcd: "130283000000",
            adnm: "潞城区",
            padcd: "130200000000",
            padnm: "长治市",
            warnLevel: 2,
            rn: 11.16,
            warnValue: 2,
            ymdh: null,
          },
          {
            adcd: "130281000000",
            adnm: "潞州区",
            padcd: "130200000000",
            padnm: "长治市",
            warnLevel: 1,
            rn: 1.92,
            warnValue: 0.1,
            ymdh: null,
          },
        ],
      };
      let ls = [];
      // 预警等级（1立即转移2准备转移）
      // ll.forEach((item) => {
      //   let rainWarnList = item.torrentialFloodWarns || [];
        // if(rainWarnList.length === 4) { // 那么就是全的
        // }
        // else {
        //   rainWarnList = getTempWarnList(item, rainWarnList)
        // }
        ll.list.forEach((rain) => {
          ls.push({
            id: rain.id,
            padnm: rain.padnm,
            rn: rain.rn,
            adnm: rain.adnm,
            adcd: rain.adcd,
            warnLevel: rain.warnLevel,
            warnValue: rain.warnValue,
            sixHourValue: rain.sixHourValue,
            twelveHourValue: rain.twelveHourValue,
            twentyFourHourValue: rain.twentyFourHourValue,
          });
        // });
      });

      // total.value = res.total
      state.warnList = ls;
      //进行传递数据
      spanPropGroup(state.warnList);
      // window.EventBus.$emit("update/flood/data", state.warnList);
      // });

      // selectFloodWarn1(params).then((res) => {
      //   let ls = res.data.list;
        state.colors = [
          {
            waterLevel: 4,
            // name: '红色',
            name: "可能性很大",
            img: redImg,
            value: ll.red || "2",
            color: "rgba(255,0,0,0.8)",
          },
          {
            waterLevel: 3,
            // name: '橙色',
            name: "可能性大",
            img: orangeImg,
            value: ll.orange || "",
            color: "rgba(222,130,38,0.8)",
          },
          {
            waterLevel: 2,
            // name: '黄色',
            name: "可能性较大",
            img: yellowImg,
            value: ll.yellow || "2",
            color: "rgba(242,255,0,0.8)",
          },
          {
            waterLevel: 1,
            // name: '蓝色',
            name: "可能发生",
            img: blueImg,
            value: ll.blue || "1",
            color: "rgba(0,165,255,0.8)",
          },
        ];

        // total.value = res.total
        // state.warnList = ls;
        //进行传递数据
        // spanPropGroup(state.warnList);
        window.EventBus.$emit("update/flood/data", state.warnList);
      // });
    };
    const changeTimeType = (e) => {
      getFloodWarnList();
    };
    const handlerHourTypeFilter = (e) => {};
    onMounted(() => {
      window.EventBus.$emit("change/right/height/min", {});

      // 增加区域切换监听
      window.EventBus.$on("changeAreaSelect", (data) => {
        if (data.type === "adcd") {
          state.adcd = data.code;
          state.lycode = null;
        } else if (data.type === "watershed") {
          state.adcd = null;
          state.lycode = data.code;
        }
        // 如果是山洪气象预警
        if (state.curType === 4) {
          getFloodWarnList();
        }
      });

      // 获取面板数据 - 不会直接看他，延迟请求下
      // setTimeout(() => {
      //   getRiskEffect().then(res => {
      //     console.log(res)
      //   })
      // }, 3000)
    });
    return {
      ...toRefs(state),
      openCloudPanel,
      changeType,
      changeTimeType,
      handlerHourTypeFilter,
      objectSpanMethod,
      getWarnLevelName,
    };
  },
});
</script>
<style scoped lang="scss">
.meteorological {
  background-color: rgba(1, 28, 70, 0.7);
}

.meteorological-header {
  width: 100%;
  color: #fff;
  padding: 10px 25px;
  background-color: rgba(1, 28, 70, 0.7);
  height: 50px;
  border-bottom: 1px #1c84c6 solid;
  text-align: center;
}

.meteorological-content {
  width: 100%;
  color: #fff;
  padding: 10px;
  background-color: rgba(1, 28, 70, 0.7);

  .title {
    color: #8cd2ff;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .exceed {
    width: 80%;
    margin: 10px auto;
    height: 70px;
    color: #fff;

    .items {
      width: 25%;
      height: 100%;
      display: flex;

      .value {
        margin: 15px 0;
        flex: 1;
        font-size: 22px;
      }

      .image {
        flex: 1;
        width: 40px;
      }

      .label {
        width: 80px;
        text-align: center;
        font-size: 14px;
      }
    }

    //
    //.items:hover {
    //  background: linear-gradient(0deg, #07529F 0%, rgba(8, 61, 132, 0) 100%);
    //  box-sizing: border-box;
    //  border: 0px solid #000000;
    //  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    //  cursor: pointer;
    //}

    .active {
      background: linear-gradient(0deg, #07529f 0%, rgba(8, 61, 132, 0) 100%);
      box-sizing: border-box;
      border: 0 solid #000000;
      box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
      cursor: pointer;
    }
  }

  .meteorBox {
    background-color: #022757;
    padding: 10px;

    .form {
      display: flex;
      align-items: center;

      span {
        font-size: 16px;
        line-height: 30px;
      }
    }

    .number {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      font-size: 18px;
      text-align: center;
      line-height: 20px;
      color: #fff;
      background-color: #8f1a1a;
      margin: 20px 0;

      .township {
        width: 18%;
        height: 50%;
        border-right: 1px solid #fff;
      }

      .village {
        width: 18%;
        height: 50%;
        border-right: 1px solid #fff;
      }

      .resident {
        width: 22%;
        height: 50%;
        border-right: 1px solid #fff;
      }

      .people {
        width: 22%;
        height: 50%;
      }

      .askIcon {
        width: 30px;
        height: 30px;
        margin-left: 50px;
        background-image: url(../image/mark.png);
        background-size: 100% 100%;
      }
    }
  }

  :deep(.productTable) {
    background-color: transparent !important;

    tr {
      background-color: transparent !important;

      th {
        background-color: #083f86 !important;
        color: #fff !important;
        border-bottom: 1px solid #17365a;
      }

      td {
        color: #92b7e9 !important;
        border-bottom: 1px solid #024a84;
      }
    }

    --el-table-row-hover-bg-color: transparent;

    .el-table__inner-wrapper {
      &::before {
        background-color: transparent;
      }
    }
  }
}

.borderColor {
  :deep(.el-input__wrapper) {
    border-color: #409eff;
    // box-shadow: none;
    background-color: transparent;
    --el-input-border-color: #409eff;

    .el-input__inner {
      color: #fff;
    }
  }
}

:deep(.el-radio) {
  margin-right: 10px;
}

:deep(.el-radio) {
  color: #a3deff;
}

:deep(.el-radio__inner) {
  background: none;
}

:deep(.el-radio__inner::after) {
  width: 7px;
  height: 7px;
  border-radius: var(--el-radio-input-border-radius);
  background-color: #00ccff;
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.15s ease-in;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  border-color: #96cdef;
  background: none;
}

:deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #a3deff;
}

.el-table {
  --el-table-border-color: none;
}
</style>
