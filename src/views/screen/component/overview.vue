<template>
  <div class="box">
    <div class="screenBox">
      <div class="boxTop">
        <div class="title">雨量分级统计</div>
        <div class="right">
          <el-select v-model="grade" class="no_border" style="width: 130px;" @change="changeGradeTimeSelect">
            <el-option label="今日雨量" value="today"></el-option>
            <el-option label="昨日雨量" value="yesterday"></el-option>
            <el-option label="最近24小时" value="24"></el-option>
            <el-option label="最近48小时" value="48"></el-option>
          </el-select>

        </div>
      </div>
      <div class="line">
        <div class="left"></div>
        <div class="center"></div>
        <div class="right"></div>
      </div>
      <div class="boxBom">
        <div class="grade">
          <div class="title" @click="gotoMaxRainStation"> 降雨量最大站点：<span class="maxRainSt">{{ maxname }} {{ maxvalue
              }}mm</span> </div>
          <div class="gradeBtm">
            <div class="leftText">
              <div class="list">
                <div class="items" v-for="item in gradeList">
                  <div class="left" :style="{ backgroundColor: item.color }"></div>
                  <div class="right1" @click="gotoRainTabByRank(item)">
                    <div class="center">{{ item.text }}</div>
                    <div class="right">{{ item.value }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="gradeCharts" id="gradeCharts">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="screenBox">
      <div class="boxTop">
        <div class="title">雨量极值统计<span @click="showRainPanel"></span></div>
        <div class="right">
          <el-select v-model="peak" class="no_border" style="width: 130px;" @change="changePeakTimeSelect">
            <el-option label="最近1小时" value="1"></el-option>
            <el-option label="最近3小时" value="3"></el-option>
            <el-option label="最近6小时" value="6"></el-option>
            <el-option label="最近12小时" value="12"></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="left"></div>
        <div class="center"></div>
        <div class="right"></div>
      </div>
      <div class="boxBom">
        <div class="peak ">
          <el-table class="productTable" height="250" :data="peakList"
            :header-cell-style="{ color: '#ffffff', textAlign: 'center' }" @row-click="handleClick"
            :cell-style="{ color: '#ffffff', textAlign: 'center', fontSize: 13 + 'px' }">
            <el-table-column type="index" label="序号" width="60">
              <template v-slot="scope">
                <span v-if="scope.row.index === 1"> <el-tag class="ml-2 rain-index-1" type="danger">0{{ scope.row.index
                    }}</el-tag></span>
                <span v-else-if="scope.row.index === 2"> <el-tag class="ml-2 rain-index-2" type="warning">0{{
                  scope.row.index }}</el-tag></span>
                <span v-else> <el-tag class="ml-2 rain-index-3" type="success">0{{ scope.row.index }}</el-tag></span>
              </template>
            </el-table-column>
            <el-table-column prop="stnm" label="雨量站" width="95" show-overflow-tooltip></el-table-column>
            <el-table-column prop="adnm" label="行政区" width="95" show-overflow-tooltip></el-table-column>
            <el-table-column prop="rainfall" label="时段雨量(mm)"></el-table-column>
            <el-table-column prop="todayRainfall" label="今日雨量(mm)"></el-table-column>
          </el-table>
          <div class="gotoBtn" @click="gotoRainTab">
            <img :src="gotoImg" style="width:15px;">点击查看更多
          </div>
        </div>
      </div>
    </div>
    <div class="screenBox">
      <div class="boxTop">
        <div class="title">河道水情</div>
      </div>
      <div class="line">
        <div class="left"></div>
        <div class="center"></div>
        <div class="right"></div>
      </div>
      <div class="boxBom">
        <div class="exceed">
          <div class="items" :class="riverWarnType === item.name ? 'active' : ''" v-for="item in exceedList"
            @click="handlerFilter(item)">
            <div class="value" :style="{ color: item.color }">{{ item.value }}</div>
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
        <div class="peak ">
          <el-table class="productTable" :data="regimenList" height="250" @row-click="gotoRiverInfo"
            :header-cell-style="{ color: '#ffffff', textAlign: 'center' }"
            :cell-style="{ color: '#ffffff', textAlign: 'center', fontSize: 13 + 'px' }">

            <el-table-column prop="riverStationName" label="站名" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column prop="Z" width="100px" label="水位(m)">
              <template v-slot="scope">
                <span>{{ formatRiverValue(scope.row.waterLevel) }}</span>
                <!--                <span v-if="scope.row.WPTN == 4">落</span>-->
                <!--                <span v-if="scope.row.WPTN == 5">涨</span>-->
                <!--                <span v-if="scope.row.WPTN == 6">平</span>-->
                <span v-if="scope.row.wptn == '落'"><img :src="trend_down_img" style="width: 18px;padding: 2px"></span>
                <span v-if="scope.row.wptn == '涨'"><img :src="trend_up_img" style="width: 18px;padding: 2px"></span>
                <span v-if="scope.row.wptn == '平'">
                  <div :src="trend_img" style="width: 18px;display: inline-block;padding: 2px"></div>
                </span>

              </template>
            </el-table-column>
            <el-table-column prop="WARNTP" label="超警状态" width="110">
              <template v-slot="scope">
                <span v-if="scope.row.warnTypeVal?.indexOf('历史') > -1">{{ scope.row.warnTypeVal.replace('水位', '')
                  }}</span>
                <span v-else>{{ scope.row.WARNTP || '' }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="TM" label="时间" :show-overflow-tooltip="true"></el-table-column> -->
            <el-table-column label="时间" width="95px">
              <template v-slot="scope">
                <span>{{ scope.row.time ?  scope.row.time : '-'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="wptn" label="水势">

            </el-table-column>
          </el-table>
          <div class="gotoBtn" @click="gotoRiverTab">
            <img :src="gotoImg" style="width:15px;">
            <div style="line-height: 20px;display: inline-block">点击查看更多</div>
          </div>
        </div>
      </div>
    </div>
    <div class="screenBox">
      <div class="boxTop">
        <div class="title" @click="getRSVRList">水库水情</div>
      </div>
      <div class="line">
        <div class="left"></div>
        <div class="center"></div>
        <div class="right"></div>
      </div>
      <div class="regimen">
        <div class="items" v-for="(item, index) in rsvrList">
          <div class="item_top">
            <div class="title maxRainSt" @click="gotoRvsrInfo(item)"> {{ index + 1 }}、{{ item.stnm || item.STNM }}</div>
            <div class="time">{{ item.time }}</div>
          </div>
          <div class="item_cont">
            <div>库水位: {{ item.waterLevel || '-- ' }}m</div>
            <div>预警状态: <span class="yellow">{{ item.warnTypeVal || '--' }}</span></div>
            <div>出/入库流量: {{ item.outboundFlow || '- ' }}/ {{ item.inboundFlow || ' -' }}m³/s</div>
            <div>水势:
              <span v-if="item.wptn == '涨'"><img :src="trend_down_img" style="width: 18px;padding: 2px"></span>
              <span v-if="item.wptn == '落'"><img :src="trend_up_img" style="width: 18px;padding: 2px"></span>
              <span v-if="item.wptn == '平'">
                <div :src="trend_img" style="width: 18px;display: inline-block;padding: 2px"></div>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import * as echarts from "echarts";
import {
  rainDetail
} from '@/api/watershed/screenRight/overview'
import { measuringStationList, riverStationList, reservoirStationList } from "@/api/watershed/query/index"
import moment from "moment";
import trend_up_img from '@/assets/icon/trend_up.png'
import trend_down_img from '@/assets/icon/trend_down.png'
import trend_img from '@/assets/icon/trend.png'
import gotoImg from '@/assets/icon/goto.png'
import { formatRiverValue, riverDataFilter, riverDataSort2 } from "@/components/Map/utils/water-common"
import { getRainTimeByType } from "@/utils/common";
import { useMapPanelStore } from "@/store/modules/map";

export default defineComponent({
  setup() {
    const panelStore = useMapPanelStore();

    const state = reactive({
      adcd: '',
      lycode: '',
      type: '',
      grade: 'today',
      gradeList: [
        {
          id: 0,
          text: '0~10.0mm',
          value: 10,
          color: '#BAEF9F'
        },
        {
          id: 1,
          text: '10.0~25.0mm',
          value: 10,
          color: '#55DD33'
        },
        {
          id: 2,
          text: '25.0~50.0mm',
          value: 10,
          color: '#7FBDFF'
        },
        {
          id: 3,
          text: '50.0~100.0mm',
          value: 10,
          color: '#000DFF'
        },
        {
          id: 4,
          text: '100.0~250.0mm',
          value: 10,
          color: '#E434EF'
        },
        {
          id: 5,
          text: '250mm以上',
          value: '0',
          color: '#FF2C2C'
        },
      ],
      maxname: '',
      maxvalue: '',
      maxDrp: {

      },
      rtOption: {
        color: ['#BAEF9F', '#55DD33', '#7FBDFF', '#000DFF', '#E434EF', '#FF2C2C'],
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: 'pie',
            radius: ['70%', '90%'],
            center: ['50%', '50%'],
            label: {
              show: false
            },
            data: [
              { name: '0~10.0mm', value: 0 },
              { name: '10.0~25.0mm', value: 0 },
              { name: '25.0~50.0mm', value: 0 },
              { name: '50.0~100.0mm', value: 0 },
              { name: '100.0~250.0mm', value: 0 },
              { name: '250以上mm', value: 0 },
            ]
          }
        ]
      },
      peak: '1',
      peakHeight: '300px',
      peakList: [],
      regimenList: [],
      backList: [],
      exceedList: [
        {
          name: '全部',
          value: '62',
          color: '#87EAFF'
        },
        {
          name: '超警戒',
          value: '0',
          color: '#FFEA35'
        },
        {
          name: '超保证',
          value: '0',
          color: '#FFA92F'
        },
        {
          name: '超历史',
          value: '0',
          color: '#FF7E01'
        },
      ],
      timeList: [
        {
          name: '潘家口水库',
          time: '2023-10-18 22:50',
          value1: '113.56',
          value2: '超正常0.11',
          value3: '234.12/-m³/s',
          value4: '超设计0.05'
        },
      ],
      riverWarnType: '全部', // 水情选择状态
      rsvrList: [], // 水库列表
      peakTime: [], // 雨量极值时间
      gradeTime: [], // 雨量分类时间
    });
    onMounted(() => {
      // echarts
      //   .init(document.getElementById('gradeCharts'))
      //   .dispose()
      // let myEchart = echarts.init(
      //   document.getElementById('gradeCharts')
      // )
      // myEchart.setOption(state.rtOption)
      // 增加区域切换监听
      window.EventBus.$on('changeAreaSelect', (data) => {
        if (data.type === 'adcd') {
          state.adcd = data.code
          state.lycode = null
        } else if (data.type === 'watershed') {
          state.adcd = null
          state.lycode = data.code
        }
        // 刷新
        getOverViewDatas()
      })
      // 切回到概览，有些需要刷新回来，雨情分级复原查询
      window.EventBus.$on('overview/rain/update', (data) => {
        changeGradeTimeSelect(state.grade)
        // 水库列表也还原回来
        getRiverList()
        getRSVRList();
      })
      // 先初始化时间 ,两个雨情的直接请求了，不然调用 getOverViewDatas 会重复请求
      changeGradeTimeSelect(state.grade)
      changePeakTimeSelect(state.peak)

      // 查询河道水情
      getRiverList()
      // 查询水库水情
      getRSVRList();
    })
    const getOverViewDatas = () => {
      // 查询雨情分级
      getLevelValue()
      // 查询雨情极值
      getPeakValue()
      // 查询河道水情
      getRiverList()
      // 查询水库水情
      getRSVRList();
    }
    const getRSVRList = async () => {
      let time = moment().format('YYYY-MM-DD HH:mm:ss')
      const res = await reservoirStationList({
        pageNum: 1,
        pageSize: 99999,
        time: time
      })
      const obj = res.data.records || []
      let list = []
      obj.forEach((item) => {
        item['time'] = moment(time).format("MM-DD HH:mm")
        list.push(item)
      })

      state.rsvrList = list
      // 刷新地图数据
      window.EventBus.$emit('reservoir/update', list)
    }
    const getLevelValue = () => {
      let params = {
        startTime: moment(state.gradeTime[0]).format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(state.gradeTime[1]).format('YYYY-MM-DD HH:mm:ss')
      }
      measuringStationList(params).then(res => {
        state.gradeList[0].value = res.data.zeroToTenCount || 0
        state.gradeList[1].value = res.data.tenToTwentyFiveCount || 0
        state.gradeList[2].value = res.data.twentyFiveToFiftyCount || 0
        state.gradeList[3].value = res.data.fiftyToOneHundredCount || 0
        state.gradeList[4].value = res.data.hundredToTwoHundredFiftyCount || 0
        state.gradeList[5].value = res.data.moreThanTwoHundredFiftyCount || 0
        state.maxDrp = res.data.ipage.records.find(item => item.stnm == res.data.topRainStName)
        state.maxname = res.data.topRainStName
        state.maxvalue = res.data.topRainValue || 0
        state.rtOption.series[0].data[0].value = res.data.zeroToTenCount || 0
        state.rtOption.series[0].data[1].value = res.data.tenToTwentyFiveCount || 0
        state.rtOption.series[0].data[2].value = res.data.twentyFiveToFiftyCount || 0
        state.rtOption.series[0].data[3].value = res.data.fiftyToOneHundredCount || 0
        state.rtOption.series[0].data[4].value = res.data.hundredToTwoHundredFiftyCount || 0
        state.rtOption.series[0].data[5].value = res.data.moreThanTwoHundredFiftyCount || 0
        echarts
          .init(document.getElementById('gradeCharts'))
          .dispose()
        let myEchart = echarts.init(
          document.getElementById('gradeCharts')
        )
        myEchart.setOption(state.rtOption)
      })
    }
    const getPeakValue = async () => {
      let index = state.peak
      let obj1
      let obj2 = {}
      // const res1 = await rainPeak({
      //   // date: getCurrent8Time(),
      //   date: moment(state.peakTime[0]).format('YYYY-MM-DD HH:mm:ss')
      //   // stcds: '30155322'
      // })
      const res1 = await rainDetail(index)
      obj1 = res1.data
      //  今日雨量
      // let time = getRainTimeByType('today')
      // let ad_ly = getAdcdOrLycode(state)
      // const res2 = await rainSumlist({
      //   ...ad_ly,
      //   bgtm: moment(time[0]).format('YYYY-MM-DD HH:mm:ss'),
      //   endtm: moment(time[1]).format('YYYY-MM-DD HH:mm:ss'),
      //   keyword: '',
      // })
      // obj2 = res2.data
      // let list1 = []
      // for (let key in obj2) {
      //   if (obj1[key]) {
      //     list1.push({
      //       LGTD: obj2[key].LGTD,
      //       LTTD: obj2[key].LTTD,
      //       ADNM: obj2[key].ADNM,
      //       STNM: obj2[key].STNM,
      //       accp: obj2[key].accp,
      //       maxDrp: Number(obj1[key]['ACCP' + index])
      //     })
      //   }
      // }
      // // 最大时段降雨在上边 ，理论后端需要排好序
      // list1.sort(function (a, b) {
      //   return Number(b.accp) - Number(a.accp); // 降序排序
      // });
      let index2 = 1
      obj1.forEach(item => {
        item['index'] = index2
        index2++
      })
      state.peakList = obj1.splice(0, 5)

    }
    const getRiverList = async () => {
      riverStationList({
        startTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        pageNum: 1,
        pageSize: 99999
      }).then(res => {
        state.exceedList[0].value = res.data.length || 0
        state.exceedList[1].value = res.data.filter((item) => item.warnType == 1).length || 0
        state.exceedList[2].value = res.data.filter(item => item.warnType == 2).length || 0
        state.exceedList[3].value = res.data.filter(item => item.warnType == 3).length || 0
        const obj = res.data
        let list = []
        obj.forEach(el => {
          el.time = moment(el.time).format("MM-DD HH:mm")
          list.push(el)
        });
        let list2 = riverDataSort2(list)
        // console.log(list2)
        state.backList = list2.concat([])
        if (state.riverWarnType == '全部') {
          let newArr = list2.slice();
          state.regimenList = newArr.splice(0, 5)
          window.EventBus.$emit('river/update', list2)
        } else {
          const list = riverDataFilter(state.riverWarnType, state.backList)
          window.EventBus.$emit('river/update', list)
          state.regimenList = list.splice(0, 5)
        }
      })
    }
    const handleClick = (e) => {
      let item = {
        lng: e.lgtd,
        lat: e.lttd
      }
      window.EventBus.$emit('flyToPostion', item)
    }
    const showRainPanel = () => {
      // 雨量极值的更多的跳转，时间统一后刷新地图
      window.EventBus.$emit('rainPanel/visible', { time: [] })
    }
    const gotoRainTab = () => {
      // 雨量极值的更多的跳转，时间统一后刷新地图
      window.EventBus.$emit('gotoTab', { id: 1, time: state.peakTime, timeType: state.peak })
    }
    const gotoRainTabByRank = (item) => {
      let rank = item.id + 2
      // 雨量极值的更多的跳转，时间统一后刷新地图
      window.EventBus.$emit('gotoTab', { id: 1, time: state.gradeTime, timeType: state.grade, rank: rank })
    }
    const gotoMaxRainStation = () => {
      let item = state.maxDrp
      // console.log(item)
      if (state.maxname == '无') return false
      // 最大降雨站点的信息的修改
      panelStore.showMainPanel({
        STTP: 'PP',
        STNM: item.stnm,
        STCD: item.stcd
      })
      // 获取属性里面的经纬度
      let position = {
        lng: item.lgtd,
        lat: item.lttd
      }
      window.EventBus.$emit('flyToPostion', position)
    }
    const gotoRiverTab = () => {
      // 河道水情的更多的跳转
      window.EventBus.$emit('gotoTab', { id: 2, time: [] })
    }
    const getRainIndex = (data) => {
      return `<div class="rain-index-${data.index}">0${data.index}</div>`
    }
    // 雨量分级时间选择
    const changeGradeTimeSelect = (value) => {
      let time = getRainTimeByType(value)
      state.gradeTime = time
      getLevelValue()
      // 更新雨情列表时间 时间统一后刷新地图
      // window.EventBus.$emit('rainPanel/visible', { id: 1, time: time, timeType: state.grade, rank: null })
    }
    // 雨量极值时间选择
    const changePeakTimeSelect = (value) => {
      state.peak = value
      window.EventBus.$emit('rainMap/update', value)
      getPeakValue(value)
    }
    // 水库弹框
    const gotoRvsrInfo = (item) => {
      // 获取属性里面的经纬度
      let position = {
        lng: item.lgtd,
        lat: item.lttd
      }
      window.EventBus.$emit('flyToPostion', position)
    }
    const gotoRiverInfo = (item) => {
      // 获取属性里面的经纬度
      let position = {
        lng: item.lgtd,
        lat: item.lttd
      }
      window.EventBus.$emit('flyToPostion', position)
    }
    const gotoRainInfo = (item) => {
      // 获取属性里面的经纬度
      let position = {
        lng: item.lgtd,
        lat: item.lttd
      }
      window.EventBus.$emit('flyToPostion', position)
    }
    const handlerFilter = (item) => {
      state.riverWarnType = item.name
      window.EventBus.$emit('riverPanel/handlerFilter', { name: item.name })
      if (item.name == '全部') {
        getRiverList()
      } else {
        // console.log(state.backList)
        const list = riverDataFilter(item.name, state.backList)
        // console.log(list.length + '条数据')
        window.EventBus.$emit('river/update', list)
        // window.EventBus.$emit('river/update', list)
        state.regimenList = list.slice(0, 5)
      }

    }
    return {
      ...toRefs(state),
      handleClick,
      showRainPanel,
      gotoRainTab,
      gotoRiverTab,
      getRainIndex,
      changeGradeTimeSelect,
      changePeakTimeSelect,
      formatRiverValue,
      trend_img,
      trend_up_img,
      gotoImg,
      trend_down_img,
      gotoRvsrInfo,
      gotoRiverInfo,
      gotoRainInfo,
      gotoRainTabByRank,
      gotoMaxRainStation,
      handlerFilter,
      getRSVRList,
    };
  },
});
</script>
<style scoped lang="scss">
.box {
  padding: 10px;
  background-color: rgba(1, 28, 70, 0.7);
}

.screenBox {
  width: 100%;
  margin-top: 10px;

  .boxTop {
    display: flex;
    width: 100%;
    height: 32px;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0 5px;

    .title {
      font-size: 16px;
      font-family: AlimamaShuHeiTi;
      font-weight: bold;
      color: #8CD2FF;
      line-height: 30px;
    }
  }

  .boxBom {
    width: 100%;
    background: rgba(17, 149, 255, 0.1);
    // opacity: 0.1;
    overflow: hidden;
    padding: 10px;
  }

  .gotoBtn {
    width: 100%;
    //height: 30px;
    text-align: center;
    line-height: 20px;
    color: #FFC300;
    opacity: 0.9;
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
  }

  .rain-index-1 {
    color: #fff;
    transform: skew(-15deg);
    border: none;
    background-color: rgba(214, 88, 75, 0.8);
  }

  .rain-index-2 {
    color: #fff;
    transform: skew(-15deg);
    border: none;
    background-color: rgba(223, 163, 66, 0.8);
  }

  .rain-index-3 {
    color: #fff;
    transform: skew(-15deg);
    border: none;
    background-color: rgba(59, 117, 189, 0.8);
  }

  .regimen {
    width: 100%;
    font-size: 14px;
    font-weight: 400;

    .items {
      width: 100%;
      color: #fff;
      margin: 20px 0;


      .item_top {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .item_cont {
        width: 100%;
        padding: 5px 15px;
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        background-color: #043166;

        div {
          width: calc(50% - 15px);
          height: 40px;
          line-height: 40px;
        }

        .yellow {
          color: #FFC947;
          font-weight: 700;
        }

        .red {
          color: #FF4B4B;
          font-weight: 700;
        }
      }
    }
  }


  .grade {
    width: 100%;
    height: 169px;

    .title {
      font-size: 14px;
      color: #fff;
      margin-bottom: 10px;

      //font-weight: bold;
      span {
        color: #FFCE0B;
      }
    }

    .gradeBtm {
      display: flex;
      width: 100%;
      height: 120px;

      .leftText {
        width: 320px;

        .list {
          width: 100%;
          height: 120px;
          display: flex;
          flex-wrap: wrap;
          flex-direction: column;
          justify-content: flex-start;


          .items {
            width: 50%;
            height: 28px;
            display: flex;
            align-items: center;


            .left {
              width: 8px;
              height: 8px;
              border-radius: 4px;
              background-color: #FFCE0B;
              margin-right: 5px;
              border-bottom: 1px rgba(255, 255, 255, 0) solid;
            }

            .right1 {
              width: 160px;
              border-bottom: 1px rgba(255, 255, 255, 0) solid;
            }

            .right1:hover {
              color: #fff;
              border-bottom: 1px #fff solid;
            }

            .center {
              color: rgba(255, 255, 255, 0.7);
              float: left;
              display: inline-block;
            }

            .center:hover {
              color: #fff;
            }

            ;

            .right {
              color: #fff;
              margin-right: 5px;
              float: right;
            }

          }

          .items:hover {
            cursor: pointer;
          }
        }
      }

      .gradeCharts {
        width: calc(100% - 320px);
        height: 100%;
      }
    }
  }

  .peak {
    width: 100%;
  }

  .exceed {
    width: 80%;
    margin: 10px auto;
    height: 80px;
    display: flex;
    color: #fff;


    .items {
      width: 25%;
      height: 100%;
      text-align: center;

      .value {
        margin: 10px 0;
        font-size: 20px;
      }
    }

    .items:hover {
      background: linear-gradient(0deg, #07529F 0%, rgba(8, 61, 132, 0) 100%);
      box-sizing: border-box;
      border: 0 solid #000000;
      box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
      cursor: pointer;
    }

    .active {
      background: linear-gradient(0deg, #07529F 0%, rgba(8, 61, 132, 0) 100%);
      box-sizing: border-box;
      border: 0 solid #000000;
      box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
      cursor: pointer;
    }
  }
}

.no_border {
  :deep(.el-input__wrapper) {
    border: none;
    box-shadow: none;
    background-color: transparent;

    .el-input__inner {
      color: #8CD2FF;
      text-align: right
    }
  }

  :deep(.el-select__caret) {
    color: #8cd2ff;
  }
}

:deep(.productTable) {
  background-color: transparent !important;

  tr {
    background-color: transparent;

    th {
      background-color: #083f86 !important;
      color: #fff !important;
      border-bottom: 0 solid #17365a !important;
      font-size: 14px !important;
      font-weight: normal !important;
    }

    td {
      color: #fff !important;
      border-bottom: 1px solid rgba(0, 172, 255, 0.5);

    }

    //:hover {
    //  background-color: #033c68 !important;
    //}
  }

  .el-table__inner-wrapper {
    &::before {
      background-color: transparent;
    }
  }
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell) {
  background-color: #033c68 !important;
  cursor: pointer;
}

.maxRainSt {
  cursor: pointer;
  padding: 5px 4px;
}

.maxRainSt:hover {
  background: #033c68;
  cursor: pointer;
}
</style>
