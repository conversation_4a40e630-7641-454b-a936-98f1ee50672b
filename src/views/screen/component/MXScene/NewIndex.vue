<!--
 * @Description:
 * @Author: liguiyuan
 * @LastEditors: liguiyuan
-->
<script setup>
import { onMounted, ref, onBeforeUnmount } from "vue";
import { LarkSR } from "larksr_websdk";
import { CommandManager, GameAPI } from "scene-communication-plugin";
import VideoDialog from './VideoDialog.vue';
import LeftPanel from "./Panel/ViewPanel/LeftPanel.vue";
import SafePanel from "./Panel/SafePanel/LeftPanel.vue";
import usePanelStore from "@/store/modules/panel";

window.larksr = null;
// var commandManager;
window.gameAPI = null;
window.larksr = null;
window.commandManager = null;
const appContainer = ref(null);
const videoDialogVisible = ref(false);
const videoDialogInfo = ref({});
const dataChannelConnected = ref(false); // 数据通道连通状态

// 使用Pinia store管理面板状态
const panelStore = usePanelStore();

// mock视频信息（后续可用接口替换）
const mockVideoInfo = {
  id: 'monitor1',
  name: '监测点1实时监控',
  videoUrl: 'ws://example.com/stream/monitor1', // 替换为真实流地址
};

// 监听点点击或弹窗详情事件
function handlePoiClick(e) {
  if (e?.Id == 1) {
    videoDialogInfo.value = mockVideoInfo;
    videoDialogVisible.value = true;
  }
}

onMounted(async () => {
  await getInit();
});
const getInit = async () => {
  window.larksr = new LarkSR({
    rootElement: appContainer.value,
    serverAddress: "http://***********:8181", //服务器地址
    authCode: "2c51256e61e641708a038f8b3033fbe0", //服务器SDK
    fullScreenMode: 0,
    loadingBgUrl: "./images/MXBackground.webp", //场景背景图
    handelRootElementSize: false,
    logLevel: "warn",
  });
  window.larksr
    .connect({
      // appliId: "1382404129690746880", //服务器appID
      appliId: "1400422102707732480", //服务器appID
    })
    .then(() => {
      window.commandManager = new CommandManager(window.larksr);
      window.gameAPI = new GameAPI(window.commandManager);
      // 关闭视频流音效
      window.larksr.audioElement.volume = 0;
    })
    .catch((e) => {
      console.log(e);
      alert(JSON.stringify(e));
    });
  // 初始化数据通道
  window.larksr.on("datachannelopen", (e) => {
    console.log("数据通道已连通", e);
    dataChannelConnected.value = true; // 设置数据通道连通状态为true

    // 场景初始化之后 需保证数据通道已连通 才能进行业务逻辑
    if (window.gameAPI) {
      createMonitoringPoi();
    }
  });
  // 监听接收信息
  window.larksr.on("datachanneltext", (e) => {
    const response = JSON.parse(e.data);
    console.log(response, "response");
    handlePoiClick(response);
  });
  // 关闭监听接收信息
  // larksr.off('datachanneltext', ()=>{});
  window.larksr.on("error", (e) => {
    alert(JSON.stringify(e.message));
  });
  window.larksr.on("info", (e) => {
    console.log("LarkSRClientEvent info", e);
  });
};
// 监测点 添加
const createMonitoringPoi = () => {
  // 从md文件提取的点位数据
  const points = [
    { lon: 91.095981, lat: 29.646593, height: 3655.274835 - 3 },
    { lon: 91.096062, lat: 29.646246, height: 3655.274287 - 3 },
    { lon: 91.096135, lat: 29.6459, height: 3655.273991 - 3 },
    { lon: 91.096214, lat: 29.645553, height: 3655.273931 - 3 },
    { lon: 91.096293, lat: 29.645212, height: 3655.274108 - 3 },
    { lon: 91.096368, lat: 29.644869, height: 3655.274524 - 3 },
    { lon: 91.096443, lat: 29.644524, height: 3655.27518 - 3 },
    { lon: 91.096517, lat: 29.644179, height: 3655.276074 - 3 },
    { lon: 91.096595, lat: 29.643829, height: 3655.277226 - 3 },
    { lon: 91.096673, lat: 29.643491, height: 3655.278573 - 3 },
    { lon: 91.09675, lat: 29.643137, height: 3655.280224 - 3 },
    { lon: 91.096826, lat: 29.642801, height: 3655.282029 - 3 },
    { lon: 91.096907, lat: 29.642446, height: 3655.284185 - 3 },
    { lon: 91.096982, lat: 29.642101, height: 3655.286516 - 3 },
    { lon: 91.097058, lat: 29.641755, height: 3655.289094 - 3 },
    { lon: 91.095678, lat: 29.643992, height: 3642.516628 - 3 }, // 16 闸下水位点
    { lon: 91.097167, lat: 29.64458, height: 3641.88071 - 3 },  // 17 闸上水位点
  ];

  // mock属性数据
  const mockMsg = [
    ["温度", "26°C"],
    ["水压", "145kpa"],
    ["水位", "32m"],
    ["流量", "123m³/s"],
    ["流速", "0.5m/s"],
  ];

  // 闸下水位点 mock
  const gateDownMsg = [
    ["水位", "3642.52m"],
    ["流速", "0.45m/s"],
    ["流量", "120m³/s"],
    ["温度", "15°C"],
    ["监测时间", "2024-05-01 12:00"]
  ];
  // 闸上水位点 mock
  const gateUpMsg = [
    ["水位", "3641.88m"],
    ["流速", "0.42m/s"],
    ["流量", "118m³/s"],
    ["温度", "14°C"],
    ["监测时间", "2024-05-01 12:00"]
  ];

  // 弹窗样式
  const defaultPopupStyle = {
    CullingDistance: 15000000000,
    ShowDist: 15000000000,
    ColorLabel: "#FFFFFF",
    ColorLabelBg: "#0E4A83F0",
    ColorMsgBg: "#1565C0E6",
    ColorMsgKey: "#B3E5FC",
    ColorMsgValue: "#FFFFFF",
  };
  const gatePopupStyle = {
    CullingDistance: 5000000,
    ShowDist: 5000000,
    ColorLabel: "#FFFFFF",
    ColorLabelBg: "#0D47A1F0",
    ColorMsgBg: "#1976D2E6",
    ColorMsgKey: "#E1F5FE",
    ColorMsgValue: "#FFFFFF",
  };

  const Features = points.map((pt, idx) => {
    // 16: 闸下水位点，17: 闸上水位点
    if (idx === 15) {
      return {
        type: "Feature",
        properties: {
          id: idx + 1,
          name: "闸下水位点",
          msg: gateDownMsg,
        },
        geometry: {
          type: "Point",
          coordinates: [pt.lon, pt.lat, pt.height],
        },
        popupStyle: gatePopupStyle,
      };
    } else if (idx === 16) {
      return {
        type: "Feature",
        properties: {
          id: idx + 1,
          name: "闸上水位点",
          msg: gateUpMsg,
        },
        geometry: {
          type: "Point",
          coordinates: [pt.lon, pt.lat, pt.height],
        },
        popupStyle: gatePopupStyle,
      };
    } else {
      return {
        type: "Feature",
        properties: {
          id: idx + 1,
          name: `监测点${idx + 1}`,
          msg: mockMsg,
        },
        geometry: {
          type: "Point",
          coordinates: [pt.lon, pt.lat, pt.height],
        },
        popupStyle: defaultPopupStyle,
      };
    }
  });

  window.gameAPI.camera("addPOI", {
    Key: "MonitoringPois_3",
    Features,
    // 下面这些样式参数只作为默认值，单个Feature可覆盖
    ...defaultPopupStyle,
    Type: 9,
    MsgType: 1,
  });
};
onBeforeUnmount(() => {
  window.larksr.close();
});
</script>
<template>
  <div class="appBox">
    <div ref="appContainer" class="map" />
    <!-- 根据当前面板状态显示不同的面板 -->
    <LeftPanel v-if="dataChannelConnected && panelStore.currentPanel === 'default'" />
    <SafePanel v-if="dataChannelConnected && panelStore.currentPanel === 'safe'" />
    <!-- 科幻蓝视频弹窗 -->
    <!-- <VideoDialog
      v-model:visible="videoDialogVisible"
      :videoInfo="videoDialogInfo"
      title="监测点1实时监控"
    /> -->
  </div>
</template>
<style scoped>
.appBox {
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  z-index: 0;
}
.appBox .map {
  width: 100vw;
  height: 100vh;
}
</style>