<!--
 * @Description: 
 * @Author: liguiyuan
 * @LastEditors: liguiyuan
-->
<template>
  <el-dialog
    :visible="visible"
    :title="title"
    width="800px"
    class="scifi-blue-dialog force-center-dialog"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    append-to-body
    @close="handleClose"
    @update:visible="handleUpdateVisible"
    top="calc(50vh - 250px)"
    style="margin: 0 auto;"
  >
    <div class="video-dialog-content">
      <HikvisionVideoPlayer
        v-if="videoInfo && videoInfo.videoUrl"
        :videoInfo="videoInfo"
        :showControls="true"
        :autoplay="true"
        :muted="true"
        style="width:100%;height:400px;"
      />
      <div v-else class="video-placeholder">暂无视频信号</div>
    </div>
    <template #footer>
      <el-button type="primary" @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import HikvisionVideoPlayer from '@/views/queryStatistics/video/components/HikvisionVideoPlayer.vue';

const props = defineProps({
  visible: Boolean,
  videoInfo: Object,
  title: {
    type: String,
    default: '监控视频',
  },
});
const emit = defineEmits(['update:visible', 'close']);

const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};
const handleUpdateVisible = (val) => {
  emit('update:visible', val);
};
</script>

<style scoped>
.scifi-blue-dialog >>> .el-dialog__header {
  background: linear-gradient(90deg, #1a237e 0%, #1976d2 100%);
  color: #fff;
  border-bottom: 2px solid #42a5f5;
}
.scifi-blue-dialog >>> .el-dialog__body {
  background: #0d1b2a;
  color: #90caf9;
  padding: 24px 32px;
}
.scifi-blue-dialog >>> .el-dialog__footer {
  background: #102040;
  border-top: 1px solid #1976d2;
}
.force-center-dialog >>> .el-dialog {
  margin: 0 auto !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 800px !important;
  min-height: 500px !important;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.video-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}
.video-placeholder {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #42a5f5;
  background: rgba(25, 118, 210, 0.1);
  border: 1px dashed #42a5f5;
  border-radius: 8px;
}
</style> 