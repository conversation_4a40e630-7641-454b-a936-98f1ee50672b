<!--
 * @Description:
 * @Author: liguiyuan
 * @LastEditors: liguiyuan
-->
<script setup>
import { onMounted, onBeforeUnmount, ref } from "vue";
import TechPanel from "@/components/Map/panels/TechPanel/TechPanel.vue";
import WarnPanel from "@/components/Map/components/warnPanel/index.vue";
// import * as StarAPI from "@metastar/star-api";
import { rainSumlist } from '@/api/watershed/screenRight/overview';
import { getRainTypeByDrpOnUE } from '@/components/Map/utils/water-common'
import {
  riverStationList,
  reservoirStationList,
} from "@/api/watershed/query/index";
import moment from "moment";
const appDomRef = ref(null);
const TechpanelIsShow = ref(false);
const showRoamingButton = ref(false);
const showPauseEndButtons = ref(false);
const isRoamingPaused = ref(false);
const option = {
  path: "http://***********:19310",
  pack: "Mx_Core_5-1_2-2-39_Windows",
  scenePaks: ["D:\\RuanJian\\CloudRendering\\pak\\LaSaHe\\LaSaHe.pak"],
  version: 426,
  startView: {
    "coordinates": [
        91.064345,
        29.555215,
        10236.055358
    ],
    "pitch": 36.7313802737,
    "bearing": -72.1169658667,
    "rotation": {
        "x": 3.968509302147432e-15,
        "y": -36.73138027368596,
        "z": -72.116965866681
    }
  },
  sceneCenter: [91.096442, 29.645588, 3645.499719],
  // sceneCenter: [91.070037, 29.545079, 9967.657957],
  streaming: {
    version: 2,
    // 开启H265
    // compress: true,
    // codecType: "h265"
  },
  maxSize: { height: 1080, width: 1920 },
  frameRate: 60, // 帧率
  // 加载页面配置
  loading: {
    // 关闭原始logo
    originLogo: false,
    // 更改背景图，图片需要放在public下
    backgroundList: ["./images/MXBackground.webp"],
    // duration: 60 * 1000, // 60秒
    tips: [
      '正在渲染河流中...',
      '正在渲染山体中...',
      '正在渲染植被中...',
      '正在渲染建筑中...',
      '正在渲染道路中...',
      '正在渲染水体中...',
      '正在渲染天空中...',
    ]
  },
  linux: false,
  share: false,
  localTransform: false,
  coordinateSystem: "EPSG4326",
};
onMounted(() => {
  getModel();
});
// 初始化
function getModel() {
  if (appDomRef.value === null) return;
  window.app = new StarAPI.App(appDomRef.value, option);
  // 准备就绪
  window.app.on("ready", () => {
    console.log('云渲染准备就绪回调');
    TechpanelIsShow.value = true;
    showRoamingButton.value = true;
    //1.加载雨量站及功能
    loadRainFallStations()
    //2.加载重要断面实时水位
    // loadImportantSection()
    //3.加载河道水文站、水位站
    loadRiverStation()
  });
}
const loadRainFallStations = async () => {
    try{
      const hours = 6;
      const res = await rainSumlist(hours);
      const stations = res.data;
      if (stations && Array.isArray(stations)) {
        stations.forEach(station => {
          if(station.lgtd && station.lttd){
          const rainType = getRainTypeByDrpOnUE(station.todayRainfall);
          const marker = new StarAPI.ComponentMarker({
            name: 'rainfallStation',
            coordinates: [Number(station.lgtd), Number(station.lttd), 3645.499719],
            markerType: 'popup1',
            // visibleDistance:1000, // 设置可见距离，单位为米
            config: {
              type:  rainType.color,
              title: station.stnm + ' - ' + station.todayRainfall + 'mm',
              icon: 'water',
              popupSize: [250, 200],
              showTitle: true,
              // distanceLength: 1000, //标题框的可视距离 单位米
              hoverShowTitle: true,
              popupType: 'textList',
              list: [
                  {
                      key: '降雨量',
                      value: station.todayRainfall + 'mm',
                  },
                  {
                      key: '所在区县',
                      value: station.adnm,
                  },
                  // {
                  //     key: '经度',
                  //     value: station.lgtd,
                  // },
                  // {
                  //     key: '纬度',
                  //     value: station.lttd,
                  // },
                  {
                      key: '时间',
                      value: moment().subtract(hours, 'hours').format('HH:mm') + '-' + moment().format('HH:mm'),
                  },

              ],
          },

          });
          marker.scale = {
              x: 0.9,
              y: 0.9,
              z: 0.9,
            }
          window.app.add(marker);
        }
      });

      }
  } catch (error) {
    console.error("Error loading rain stations:", error);
  }

}

const loadRiverStation = async () => {
  try {
    riverStationList({
        startTime: moment().subtract(1, 'day').format("YYYY-MM-DD HH:mm:ss"),
        pageNum: 1,
        pageSize: 99999,
      }).then((res) => {
        const stations = res.data;
        if (stations && Array.isArray(stations)) {
          stations.forEach(station => {
            // 根据实际数据字段调整坐标和信息
            if(station.lgtd && station.lttd){
            const marker = new StarAPI.ComponentMarker({
              name: 'riverStation',
              coordinates: [Number(station.lgtd), Number(station.lttd), 3645.499719], // 假设z坐标与雨量站相同，请根据实际需要调整
              markerType: 'popup3', // 使用popup3类型
              // visibleDistance: 1000, // 设置可见距离
              config: {
                type: 'blue', // 根据实时水位或警戒水位判断设置不同的类型，例如 'warn', 'normal'
                title: (station.riverStationName || '未知站点') , // 站名和实时水位
                icon: 'reservoir2', // 使用通用图标，可根据需要更换
                popupSize: [300, 200],
                showTitle: true,
                // distanceLength: 1000,
                hoverShowTitle: true,
                popupType: 'textList',
                list: [

                    {
                        key: '实时水位',
                        value: station.waterLevel !== undefined ? station.waterLevel + ' m' : '无数据',
                    },
                    {
                        key: '实时流量',
                        value: station.waterFlow !== undefined ? station.waterFlow + ' m³/s' : '无数据',
                    },
                    // {
                    //     key: '警戒水位', // 假设有警戒水位字段warningLevel
                    //     value: station.warningLevel !== undefined ? station.warningLevel + 'm' : '无数据',
                    // },
                    {
                        key: '时间',
                        value: moment().format('YYYY-MM-DD HH:mm:ss') , // 假设有时间字段tm
                    },
                    // 根据实际需求添加更多字段
                ],
            },
            });
            marker.scale = {
              x: 0.9,
              y: 0.9,
              z: 0.9,
            }
            window.app.add(marker);
          }
        });
        }
      })
  } catch (error) {
    console.error("Error loading river station:", error);
  }
}

// 定义漫游路径的占位符
const roamingPath = [
  // 在这里填充拉萨河段的坐标点
  // 例如: [经度, 纬度, 高度], // 示例起始点
  [91.18934, 29.637791, 4075.427574], // 示例中途点
  [91.173481, 29.638109, 4000.823311],
  [91.159062, 29.641283, 3900.504939], //调整视角

  [91.138225, 29.637691, 3800.280249],
  [91.111872, 29.643081, 3755.901629], // 水面材质
  [91.102381, 29.642973, 3760.809665], //

];

// 启动河道漫游的方法
const startRiverRoaming = () => {
  if (window.app) {
    console.log('开始河道漫游');
    setVisibleDistanceAndDistanceLength('riverStation', 1000, 1000);
    setVisibleDistanceAndDistanceLength('rainfallStation', 1000, 1000);
    showRoamingButton.value = false; // 隐藏开始按钮
    showPauseEndButtons.value = true; // 显示暂停和结束按钮
    isRoamingPaused.value = false; // 重置暂停状态
    window.app.camera.roamingCustom({
      coordinates: roamingPath,
      followPath: true, // 镜头沿路径方向
      speeds: 100, // 漫游速度，单位 m/s
      loop: false, // 不循环漫游
      break: true, // 允许打断漫游
      borderHelper: true, // 开启边界辅助线
      around: false, // 禁用环绕
      // 其他漫游配置可以根据需求添加
    });
  } else {
    console.error('StarAPI App 未初始化');
  }
};

// 切换暂停/继续漫游的方法
const togglePauseContinue = () => {
  if (window.app) {
    if (isRoamingPaused.value) {
      // 当前是暂停状态，点击后继续
      console.log('继续河道漫游');
      window.app.camera.roamingCustom('play');
      isRoamingPaused.value = false;
    } else {
      // 当前是播放状态，点击后暂停
      console.log('暂停河道漫游');
      window.app.camera.roamingCustom('pause');
      isRoamingPaused.value = true;
    }
  } else {
    console.error('StarAPI App 未初始化');
  }
};

// 结束河道漫游的方法
const endRiverRoaming = () => {
  if (window.app) {
    console.log('结束河道漫游');
    window.app.camera.roamingCustom(false); // 结束漫游
    showPauseEndButtons.value = false; // 隐藏暂停和结束按钮
    showRoamingButton.value = true; // 显示开始按钮
    isRoamingPaused.value = false; // 重置暂停状态
  } else {
    console.error('StarAPI App 未初始化');
  }
};
// 通过name获取组件，并设置组件的可见距离和距离长度
const setVisibleDistanceAndDistanceLength =  (name, visibleDistance = 1000, distanceLength = 1000) => {
  let stations =  window.app.getByName(name);
  stations.then(res => {
    for (let i = 0; i < res.length; i++) {
      res[i].visibleDistance = visibleDistance;
      res[i].config.distanceLength = distanceLength;
    }
  })
}
onBeforeUnmount(() => {
  window.app.dispose();
});
</script>
<template>
  <div>
    <div ref="appDomRef" class="cloud_render_app" />
    <tech-panel v-if="TechpanelIsShow"/>
    <warn-panel v-if="TechpanelIsShow"/>
    <!-- 添加河道漫游按钮 -->
    <button class="river-roaming-button" @click="startRiverRoaming" v-if="showRoamingButton">
      河道漫游
    </button>

    <!-- 添加暂停和结束按钮 -->
    <div class="roaming-controls" v-if="showPauseEndButtons">
      <button class="roaming-button" @click="togglePauseContinue">
        {{ isRoamingPaused ? '继续漫游' : '暂停漫游' }}
      </button>
      <button class="roaming-button end-button" @click="endRiverRoaming">
        结束漫游
      </button>
    </div>
  </div>
</template>
<style scoped lang="scss">
.cloud_render_app {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
}

/* 科幻风按钮基础样式 */
.river-roaming-button {
  position: absolute;
  bottom: 40px; /* 调整位置 */
  right: 40px; /* 调整位置 */
  padding: 12px 25px; /* 调整内边距 */
  background: linear-gradient(145deg, rgba(0, 128, 255, 0.9) 0%, rgba(0, 180, 255, 0.7) 100%); /* 渐变背景 */
  color: #e0f7ff; /* 调整文字颜色 */
  border: none; /* 移除边框 */
  border-radius: 25px; /* 更大的圆角 */
  cursor: pointer;
  font-size: 18px; /* 调整字体大小 */
  font-weight: 500; /* 字体粗细 */
  backdrop-filter: blur(10px); /* 更强的毛玻璃效果 */
  box-shadow: 0 4px 20px rgba(0, 180, 255, 0.4); /* 添加阴影 */
  transition: all 0.3s ease; /* 平滑过渡效果 */
  outline: none; /* 移除focus时的轮廓 */

  &:hover {
    background: linear-gradient(145deg, rgba(0, 180, 255, 1) 0%, rgba(0, 200, 255, 0.8) 100%);
    box-shadow: 0 6px 25px rgba(0, 200, 255, 0.6);
    transform: translateY(-2px); /* 微小上移效果 */
  }

  &:active {
    transform: translateY(1px); /* 微小下压效果 */
    box-shadow: 0 2px 10px rgba(0, 100, 255, 0.4);
  }
}

/* 添加漫游控制按钮容器样式 */
.roaming-controls {
  position: absolute;
  bottom: 40px; /* 调整位置 */
  right: 40px; /* 调整位置 */
  display: flex;
  gap: 15px; /* 按钮之间的间距 */
}

/* 添加漫游控制按钮基础样式，复用部分科幻风样式 */
.roaming-button {
  padding: 12px 25px; /* 调整内边距 */
  background: linear-gradient(145deg, rgba(0, 128, 255, 0.9) 0%, rgba(0, 180, 255, 0.7) 100%); /* 渐变背景 */
  color: #e0f7ff; /* 调整文字颜色 */
  border: none; /* 移除边框 */
  border-radius: 25px; /* 更大的圆角 */
  cursor: pointer;
  font-size: 18px; /* 调整字体大小 */
  font-weight: 500; /* 字体粗细 */
  backdrop-filter: blur(10px); /* 更强的毛玻璃效果 */
  box-shadow: 0 4px 20px rgba(0, 180, 255, 0.4); /* 添加阴影 */
  transition: all 0.3s ease; /* 平滑过渡效果 */
  outline: none; /* 移除focus时的轮廓 */

  &:hover {
    background: linear-gradient(145deg, rgba(0, 180, 255, 1) 0%, rgba(0, 200, 255, 0.8) 100%);
    box-shadow: 0 6px 25px rgba(0, 200, 255, 0.6);
    transform: translateY(-2px); /* 微小上移效果 */
  }

  &:active {
    transform: translateY(1px); /* 微小下压效果 */
    box-shadow: 0 2px 10px rgba(0, 100, 255, 0.4);
  }

  /* 结束按钮特殊样式 */
  &.end-button {
    background: linear-gradient(145deg, rgba(255, 0, 0, 0.9) 0%, rgba(255, 50, 50, 0.7) 100%);
    box-shadow: 0 4px 20px rgba(255, 50, 50, 0.4);

    &:hover {
      background: linear-gradient(145deg, rgba(255, 50, 50, 1) 0%, rgba(255, 70, 70, 0.8) 100%);
      box-shadow: 0 6px 25px rgba(255, 70, 70, 0.6);
    }

    &:active {
      box-shadow: 0 2px 10px rgba(200, 0, 0, 0.4);
    }
  }
}
</style>
