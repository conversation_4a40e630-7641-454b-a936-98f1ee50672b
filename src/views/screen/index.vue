<template>
  <div class="screen-container">
    <Head @headCurrent="headCurrent"></Head>
    <div class="content" v-show="current !== 3 && current !== 4">
      <div class="map">
        <map3d></map3d>
      </div>
      <div
        class="right"
        :class="{ hide: show }"
        :style="{ height: height }"
        v-show="showSelf"
      >
        <div class="expand" @click="show = false" v-if="show"></div>
        <div class="recover" @click="show = true" v-if="!show"></div>
        <Right v-show="current == 0"></Right>
        <Scheme v-if="current == 1"></Scheme>
        <Hedging v-if="current == 2"></Hedging>
      </div>
    </div>
    <iframe
      v-if="current == 3"
      id="sonSystem"
      width="100%"
      height="92.5%"
      title="Inline Frame"
      src="http://202.96.98.106:19001/screen"
    ></iframe>
    <iframe
      v-if="current == 4"
      id="sonSystem"
      width="100%"
      height="92.5%"
      title="Inline Frame 2"
      src="http://saas.keepsoft.net:32460/pc/#/external"
    ></iframe>
    <div class="mask" v-show="mainPanel.visible || maskShow1">
      <Panel
        v-show="mainPanel.visible"
        class="center"
      ></Panel>
      <Contrast
        v-show="maskShow1"
        class="center"
        @closeMask1="maskShow1 = false"
        @openMask1="maskShow1 = true"
      >
      </Contrast>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import Map3d from "../../components/Map";
import Head from "./component/head.vue";
import Right from "./component/right.vue";
import Scheme from "./component/scheme.vue";
import Hedging from "./component/hedging.vue";
import Panel from "./component/panel.vue";
import Contrast from "./component/contrast.vue";
import { useMapPanelStore } from "@/store/modules/map";

export default defineComponent({
  components: {
    Map3d,
    Head,
    Right,
    Scheme,
    Hedging,
    Panel,
    Contrast,
  },
  setup() {
    const panelStore = useMapPanelStore();

    const { mainPanel } = storeToRefs(panelStore);
    const state = reactive({
      showSelf: true,
      show: false,
      current: 4,
      height: "95%",
      maskShow1: false,
    });
    const router = useRouter();
    const headCurrent = (id) => {
      state.current = id;
    };
    onMounted(() => {
      window.EventBus.$on("change/right/height/max", (data) => {
        state.height = "95%";
      });
      window.EventBus.$on("change/right/height/min", (data) => {
        state.height = "auto";
      });
      window.EventBus.$on("change/right/show", (data) => {
        state.showSelf = true;
      });
      window.EventBus.$on("change/right/hide", (data) => {
        state.showSelf = false;
      });
      window.EventBus.$on("sliderHide", () => {
        state.show = true;
      });
      window.EventBus.$on("sliderShow", () => {
        state.show = false;
      });
    });
    return {
      ...toRefs(state),
      headCurrent,
      mainPanel,
    };
  },
});
</script>
<style scoped lang="scss">
.screen-container {
  margin: 0;
  padding: 0 !important;
  width: 100vw;
  height: 100vh;
  background-color: #001442;
  overflow: hidden;
  iframe {
    border: none;
  }
  .content {
    width: 100%;
    height: calc(100% - 73px);
    position: relative;
    overflow: hidden;

    .map {
      width: 100%;
      height: 100%;
    }

    .right {
      width: 520px;
      height: 95%;
      position: absolute;
      right: 10px;
      top: 3%;

      &.hide {
        right: -520px;
      }

      .expand {
        position: absolute;
        top: 50%;
        left: -20px;
        width: 22px;
        height: 38px;
        cursor: pointer;
        z-index: 99999;
        background-image: url(./image/right.png);
        transform: rotate(180deg);
      }

      .recover {
        position: absolute;
        top: 50%;
        left: 0;
        width: 22px;
        height: 38px;
        cursor: pointer;
        color: #fff;
        z-index: 999;
        background-image: url(./image/right.png);
      }
    }
  }

  .mask {
    width: 100vw;
    height: 100vh;
    background-color: rgba($color: #000000, $alpha: 0.5);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999;

    .center {
      position: absolute;
      left: 50%;
      top: 100px;
      transform: translateX(-50%);
    }
  }
}
</style>
