<template>
  <div class="app-container">
    <el-tabs v-model="transferType" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="应转移" :name="1"></el-tab-pane>
      <el-tab-pane label="已转移" :name="2"></el-tab-pane>
    </el-tabs>
    <div class="form">
      <el-form :model="queryForm" label-width="70px" :inline="true">
        <el-form-item label="人员姓名">
          <el-input v-model="queryForm.peoName"></el-input>
        </el-form-item>
        <el-form-item label="行政区划">
          <el-tree-select v-model="queryForm.adcd" :data="adcdList"
            :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="id" placeholder="请选择行政区"
            check-strictly />
        </el-form-item>
        <el-form-item label="超时情况" v-if="transferType == 1">
          <el-select v-model="queryForm.timeoutType" clearable>
            <el-option label="未超时" :value="1"></el-option>
            <el-option label="已超时" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="安置方式" v-if="transferType == 2">
          <el-select v-model="queryForm.resettleType" clearable>
            <el-option label="集中安置" :value="1"></el-option>
            <el-option label="自行安置" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div>
        <el-button type="primary" @click="getListDate">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </div>
    </div>
    <div class="table">
      <el-table v-loading="loading" border :data="tableList" height="100%">
        <el-table-column label="序号" width="60" type="index"></el-table-column>
        <el-table-column label="剩余时间" align="center" v-if="transferType == 1">
          <template #default="scope">
            <span :class="bolTime(scope.row.transferEndTime) ? 'red' : ''"> {{ computedTime(scope.row.transferEndTime)
              }} </span>
          </template>
        </el-table-column>
        <el-table-column label="行政区划/危险区" align="center" prop="riskName" />
        <el-table-column label="居民户编码" align="center" prop="peoCode" />
        <el-table-column label="户主名称" align="center" prop="peoHolderName" />
        <el-table-column label="人员姓名" align="center" prop="peoName" />
        <el-table-column label="转移负责人" align="center" prop="transferName" />
        <el-table-column label="职务" align="center" prop="transferJob" />
        <el-table-column label="联系方式" align="center" prop="transferTel" />
        <el-table-column label="安置方式" align="center" prop="placeName" v-if="transferType == 2">
          <template #default="scope">
            <span>{{ scope.row.resettleType == 1 ? '集中安置' : '自行安置' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="安置点" align="center" prop="placementName" />
        <el-table-column label="转移开始时间" align="center" prop="transferStartTime" v-if="transferType == 1" />
        <el-table-column label="转移结束时间" align="center" prop="transferEndTime" v-if="transferType == 1" />
        <el-table-column label="操作" align="center" width="130px" v-if="transferType == 1">
          <template #default="scope">
            <el-button type="primary" link @click="handleResettle(scope.row)">确认转移安置点 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
      @pagination="getListDate" />
    <el-dialog title="转移安置信息" v-model="resettleShow" width="30%" @close="resettleForm = { resettleType: 1, }">
      <el-form :model="resettleForm" ref="resettleRef" :rules="resettleRules" label-width="150px">
        <el-form-item label="安置方式" prop="resettleType">
          <el-select v-model="resettleForm.resettleType" style="width: 80%;" @change="resettleForm.placementName = ''">
            <el-option label="集中安置" :value="1"></el-option>
            <el-option label="自行安置" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="安置点" prop="placementId" v-if="resettleForm.resettleType == 1">
          <el-select v-model="resettleForm.placementId" style="width: 80%;" @change="placementChange">
            <el-option v-for="item in placeIdList" :label="item.placeName" :value="item.id" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="安置点" prop="placementName" v-if="resettleForm.resettleType == 2">
          <el-input v-model="resettleForm.placementName" style="width: 80%;"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitResettle">确 定</el-button>
          <el-button @click="resettleShow = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance } from "vue";
import { getAdcdTree, } from "@/api/watershed/ads";
import { transferResettlePage, placementPage, setPlacementParam } from "@/api/watershed/transfer"
export default defineComponent({
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      headList: [
        { name: '四级风险', },
        { name: '三级风险', },
        { name: '二级风险', },
        { name: '一级风险', },
      ],
      transferType: 1,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        peoName: '',
        timeoutType: '',
        adcd: '',
      },
      adcdList: [],
      total: 0,
      loading: false,
      tableList: [],
      resettleShow: false,
      resettleForm: {
        resettleType: 1,
      },
      resettleRules: {
        resettleType: [{ required: true, message: "不能为空", trigger: "blur" }],
        placementId: [{ required: true, message: "不能为空", trigger: "blur" }],
        placementName: [{ required: true, message: "不能为空", trigger: "blur" }],
      },
      placeIdList: [],
      transferResettleId: ''
    })
    onMounted(() => {
      getAdcdList()
      getListDate()
    })
    const getAdcdList = () => {
      getAdcdTree({
        adcd: ''
      }).then((res) => {
        state.adcdList = res.data[0].children
      })
    }
    const handleClick = (v) => {
      state.transferType = v.paneName
      state.queryForm.resettleType = ''
      state.queryForm.timeoutType = ''
      getListDate()
    }
    const getListDate = () => {
      state.loading = true
      transferResettlePage({
        pageNum: state.queryForm.pageNum,
        pageSize: state.queryForm.pageSize,
        peoName: state.queryForm.peoName,
        timeoutType: state.queryForm.timeoutType,
        adcd: state.queryForm.adcd,
        resettleType: state.queryForm.resettleType,
        transferType: state.transferType,
      }).then(res => {
        state.tableList = res.data
        state.total = res.total
        state.loading = false
      })
    }
    const reset = () => {
      state.queryForm = {
        pageNum: 1,
        pageSize: 10
      }
      getListDate()
    }
    const computedTime = (endTime) => {
      const now = new Date();
      //截止时间
      const until = new Date(endTime);
      // 计算时会发生隐式转换，调用valueOf()方法，转化成时间戳的形式
      const days = (until - now) / 1000 / 3600 / 24;
      // 下面都是简单的数学计算
      let day = Math.floor(days);

      const hours = (days - day) * 24;
      const hour = Math.floor(hours);
      const minutes = (hours - hour) * 60;
      const minute = Math.floor(minutes);

      const seconds = (minutes - minute) * 60;
      const second = Math.floor(seconds);
      if (day <= 0) {
        day = -day
      }
      const back = day + '天' + hour + '小时' + minute + '分钟';
      // var back = day + '天' + hour + ':' + minute;
      return back;

    }
    const bolTime = (endTime) => {
      const now = new Date().getTime();
      //截止时间
      const until = new Date(endTime).getTime();
      return now > until;
    }
    const retTime = (endTime) => {
      const now = new Date();
      //截止时间
      const until = new Date(endTime);
      // 计算时会发生隐式转换，调用valueOf()方法，转化成时间戳的形式
      const days = (until - now) / 1000 / 3600 / 24;
      // 下面都是简单的数学计算
      const day = Math.floor(days);
      const hours = (days - day) * 24;
      const hour = Math.floor(hours);
      const minutes = (hours - hour) * 60;
      const minute = Math.floor(minutes);

      const seconds = (minutes - minute) * 60;
      const second = Math.floor(seconds);
      const back = day + '天' + hour + '小时' + minute + '分钟';
      // var back = day + '天' + hour + ':' + minute;
      return back;
    }
    // 点击操作转移按钮
    const handleResettle = (row) => {
      state.resettleShow = true
      state.transferResettleId = row.id
      placementPage({
        adcd: row.riskId
      }).then(res => {
        state.placeIdList = res.data
      })
    }
    const placementChange = (v) => {
      state.resettleForm.placementName = state.placeIdList.find((el) => { return el.id == v }).placeName
    }
    // 转移点击确定
    const submitResettle = () => {
      // console.log(state.resettleForm);
      proxy.$refs["resettleRef"].validate(valid => {
        if (valid) {
          setPlacementParam({
            transferResettleId: state.transferResettleId,
            resettleType: state.resettleForm.resettleType,
            placementId: state.resettleForm.placementId,
            placementName: state.resettleForm.placementName
          }).then(res => {
            if (res.code == 200) {
              proxy.$modal.msgSuccess('转移成功')
              state.resettleShow = false
              getListDate()
            }
          })
        }
      })

    }

    return {
      ...toRefs(state),
      handleClick,
      getListDate,
      reset,
      handleResettle,
      submitResettle,
      placementChange,
      computedTime,
      bolTime
    };
  },
});
</script>
<style scoped lang="scss">
.form {
  display: flex;
  justify-content: space-between;
  margin: 10px 0;
}

.table {
  height: calc(100vh - 300px);

  .red {
    color: red;
  }
}
</style>