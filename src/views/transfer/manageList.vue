<template>
  <div class="app-container">
    <div class="headList">
      <div class="items" v-for="(item, index) in headList" :key="index">
        <div class="ig"></div>
        <div>{{ item.value }}个</div>
      </div>
    </div>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="风险清单" name="1"></el-tab-pane>
      <el-tab-pane label="准备转移" name="2"></el-tab-pane>
      <el-tab-pane label="立即转移" name="3"></el-tab-pane>
    </el-tabs>
    <div class="form">
      <el-form :model="queryForm" label-width="70px" :inline="true">
        <el-form-item label="行政区划">
          <el-tree-select v-model="queryForm.adcd" :data="adcdList"
            :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="id" placeholder="请选择行政区"
            check-strictly />
        </el-form-item>
        <el-form-item label="风险等级">
          <el-select v-model="queryForm.riskLevel" clearable>
            <el-option label="红色" :value="1"></el-option>
            <el-option label="橙色" :value="2"></el-option>
            <el-option label="黄色" :value="3"></el-option>
            <el-option label="蓝色" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发生时间">
          <el-date-picker v-model="queryForm.date" style="width: 305px;" :clearable="false" type="datetimerange"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryForm.riskStatus" clearable>
            <el-option label="风险中" :value="1"></el-option>
            <el-option label="已关闭" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div>
        <el-button type="primary" icon="Search" @click="getList">查询</el-button>
        <el-button @click="reset" icon="Refresh">重置</el-button>
        <el-button @click="addShow = true" v-if="activeName == '1'" plain icon="Plus">新增</el-button>
      </div>
    </div>
    <div class="btn" v-if="activeName != '3'" style="margin-bottom: 10px;">
      <el-button :disabled="single" type="danger" @click=" transferShow = true">立即转移</el-button>
      <el-button :disabled="single" type="warning" @click="preparation" v-if="activeName == '1'">准备转移</el-button>
    </div>
    <div class="table" :class="activeName == '3' ? 'nobtn' : ''">
      <el-table v-loading="loading" border :data="tableList" height="100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" width="60" type="index"></el-table-column>
        <el-table-column label="风险对象类型" align="left" prop="riskType">
          <template #default="scope">
            {{ scope.row.riskType == 1 ? '行政区' : '危险区' }}
          </template>
        </el-table-column>
        <el-table-column label="风险对象代码" align="center" prop="riskId" />
        <el-table-column label="风险对象名称" align="center" prop="riskName" />
        <el-table-column label="转移负责人" align="center" prop="chkSourTp" />
        <el-table-column label="联系方式" align="center" prop="planWw" />
        <el-table-column label="安置点" align="center" prop="actualWw" />
        <el-table-column label="致险因子" align="center" prop="riskReason" />
        <el-table-column label="发生时间" align="center" prop="occurrenceTime" />
        <el-table-column label="风险等级" align="center">
          <template #default="scope">
            <span v-if="scope.row.riskLevel == 1" style="color: #ff4040;">红色风险</span>
            <span v-if="scope.row.riskLevel == 2" style="color: #ff8f1f;">橙色风险</span>
            <span v-if="scope.row.riskLevel == 3" style="color: #ffc300;">黄色风险</span>
            <span v-if="scope.row.riskLevel == 4" style="color: #1890ff;">蓝色风险</span>
          </template>
        </el-table-column>
        <el-table-column label="风险状态" align="center" prop="riskStatus">
          <template #default="scope">
            <span v-if="scope.row.riskStatus == 1">风险中</span>
            <span v-if="scope.row.riskStatus == 2">已关闭</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">

            <el-button type="primary" v-if="activeName == 3" link @click="handleView(scope.row)">通知书预览</el-button>
            <el-button type="primary" v-if="scope.row.riskStatus == 1" link
              @click="handleClose(scope.row)">关闭</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination :total="total" v-model:page="queryForm.pageNum" v-model:limit="queryForm.pageSize"
      @pagination="getList" />
    <el-dialog title="添加避险转移通知" v-model="transferShow" width="30%" @close="transferForm = {}">
      <el-form :model="transferForm" ref="transRef" :rules="transRules" label-width="150px">
        <el-form-item label="转移开始时间" prop="transferStartTime">
          <el-date-picker v-model="transferForm.transferStartTime" style="width: 90%;" type="datetime"
            placeholder="选择日期时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="转移结束时间" prop="transferEndTime">
          <el-date-picker v-model="transferForm.transferEndTime" style="width: 90%;" type="datetime"
            placeholder="选择日期时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="上传转移通知书文件">
          <el-upload action="/papi/risks/risks/file/uploadFile" accept=".pdf" :on-remove="handleRemove"
            :on-change="handleChange" :headers="headers" :on-success="fileSuccess" :file-list="fileList">
            <el-button> 点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="immediately">确 定</el-button>
          <el-button @click="transferShow = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog title="新增" v-model="addShow" width="30%" @close="addForm = {}">
      <el-form :model="addForm" ref="addRef" class="addForm" :rules="addRules" label-width="106px" :inline="true">
        <el-form-item label="风险对象类型" prop="riskType">
          <el-select v-model="addForm.riskType">
            <el-option label="行政区" value="1"></el-option>
            <el-option label="危险区" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="行政区" prop="riskId">
          <el-tree-select v-model="addForm.riskId" :data="adcdList"
            :props="{ value: 'adcd', label: 'adnm', children: 'children' }" value-key="id" placeholder="请选择行政区"
            check-strictly @change="changeAdcd" />
        </el-form-item>
        <el-form-item label="风险区" prop="riskName" v-if="addForm.riskType == '2'">
          <el-select v-model="addForm.riskName">
            <el-option v-for="item in riskList" :label="item.dangerousName" :value="item.id" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="安置点" prop="placeIdList">
          <el-select v-model="addForm.placeIdList" multiple>
            <el-option v-for="item in placeIdList" :label="item.placeName" :value="item.id" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="风险等级" prop="riskLevel">
          <el-select v-model="addForm.riskLevel">
            <el-option label="红色" :value="1"></el-option>
            <el-option label="橙色" :value="2"></el-option>
            <el-option label="黄色" :value="3"></el-option>
            <el-option label="蓝色" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发生时间" prop="occurrenceTime">
          <el-date-picker v-model="addForm.occurrenceTime" type="datetime" placeholder="选择日期时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="致险原因" style="width: 100%;" prop="riskReason">
          <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="addForm.riskReason"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitAdd">确 定</el-button>
          <el-button @click="addShow = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, onMounted, getCurrentInstance } from "vue";
import moment from "moment";
import { getAdcdTree, } from "@/api/watershed/ads";
import { ElMessageBox } from 'element-plus'
import { riskTransferPage, riskTransClose, updateTransferStatus, insertRisk, placementPage, danListByAdcd } from "@/api/watershed/transfer"
import { getToken } from '@/utils/auth'
export default defineComponent({
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      headers: { Authorization: "Bearer " + getToken() },
      headList: [
        { name: '四级风险', value: 0 },
        { name: '三级风险', value: 0 },
        { name: '二级风险', value: 0 },
        { name: '一级风险', value: 0 },
      ],
      activeName: '1',
      adcdList: [],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        date: [moment().subtract(1, 'day'), moment()],
      },
      transferStatus: '1',
      total: 0,
      loading: false,
      tableList: [],
      single: true,
      ids: [],
      transferShow: false,
      transferForm: {},
      fileList: [],
      transRules: {
        transferStartTime: [{ required: true, message: "不能为空", trigger: "blur" }],
        transferEndTime: [{ required: true, message: "不能为空", trigger: "blur" }],
      },
      addShow: false,
      addForm: {
        placeIdList: []
      },
      addRules: {
        riskType: [{ required: true, message: "不能为空", trigger: "blur" }],
        riskId: [{ required: true, message: "不能为空", trigger: "blur" }],
        riskName: [{ required: true, message: "不能为空", trigger: "blur" }],
        placeIdList: [{ required: true, message: "不能为空", trigger: "blur" }],
        riskLevel: [{ required: true, message: "不能为空", trigger: "blur" }],
        occurrenceTime: [{ required: true, message: "不能为空", trigger: "blur" }],
        riskReason: [{ required: true, message: "不能为空", trigger: "blur" }],
      },
      placeIdList: [],
      riskList: []
    })
    onMounted(() => {
      getAdcdList()
      getList()
    })
    const getAdcdList = () => {
      getAdcdTree({
        adcd: ''
      }).then((res) => {
        state.adcdList = res.data[0].children
      })
    }
    const getList = () => {
      state.loading = true
      riskTransferPage({
        pageNum: state.queryForm.pageNum,
        pageSize: state.queryForm.pageSize,
        adcd: state.queryForm.adcd,
        riskLevel: state.queryForm.riskLevel,
        riskStatus: state.queryForm.riskStatus,
        transferStatus: state.transferStatus,
        // startTime: moment(state.queryForm.date[0]).format("YYYY-MM-DD HH:mm:ss"),
        // endTime: moment(state.queryForm.date[1]).format("YYYY-MM-DD HH:mm:ss"),
      }).then(res => {
        state.tableList = res.data.ipage.records
        state.total = res.data.ipage.total
        state.loading = false
        state.headList[0].value = res.data.red
        state.headList[1].value = res.data.orange
        state.headList[2].value = res.data.yellow
        state.headList[3].value = res.data.blue
      })
    }
    const reset = () => {
      state.queryForm = {
        pageNum: 1,
        pageSize: 10,
        date: [moment().subtract(1, 'day'), moment()],
      }
      getList()
    }
    const handleClick = (v) => {
      state.transferStatus = v.paneName
      state.ids = []
      reset()
    }
    const handleClose = (row) => {
      ElMessageBox.confirm('是否确认关闭当前风险对象', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        riskTransClose(row.id).then(res => {
          if (res.code == 200) {
            proxy.$modal.msgSuccess('关闭成功')
            getList()
          }
        })
      }).catch(() => { });

    }
    const handleSelectionChange = (selection) => {
      state.ids = selection.map((item) => item.id)
      state.single = selection.length != 1
    }
    const preparation = () => {
      ElMessageBox.confirm('是否准备转移当前风险对象', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateTransferStatus({
          transferStatus: 2,
          id: state.ids[0]
        }).then(res => {
          if (res.code == 200) {
            proxy.$modal.msgSuccess('转移成功')
            getList()
          }
        })

      }).catch(() => { });
    }
    const fileSuccess = (res, file, fileList) => {
      state.fileList = fileList
    }
    const handleChange = (file, fileList) => {
      if (fileList.length > 1) {
        fileList[0] = fileList[1]
        fileList.splice(1, 1);
        state.fileList = fileList
      }
    }
    const handleRemove = (file, fileList) => {
      console.log(state.fileList);
    }
    const immediately = () => {
      proxy.$refs["transRef"].validate(valid => {
        if (valid) {
          updateTransferStatus({
            transferStartTime: moment(state.transferForm.transferStartTime).format("YYYY-MM-DD HH:mm:ss"),
            transferEndTime: moment(state.transferForm.transferEndTime).format("YYYY-MM-DD HH:mm:ss"),
            transferStatus: 3,
            id: state.ids[0],
            filePath: state.fileList.length ? state.fileList[0].response.msg : ''
          }).then(res => {
            if (res.code == 200) {
              proxy.$modal.msgSuccess('转移成功')
              getList()
              state.transferShow = false
            }
          })
        }
      })
      // console.log(state.fileList[0].response.msg);

    }
    const changeAdcd = (v) => {
      // console.log(v);
      // console.log(v.replace(/\.?0+$/, ''));
      let a = v.replace(/\.?0+$/, '')
      if (a.length != 9) {
        proxy.$modal.msgWarning('请选择至乡镇级别')
        state.addForm.riskId = ''
        return
      }
      // console.log(123456);
      placementPage({
        adcd: v
      }).then(res => {
        state.placeIdList = res.data
        state.addForm.placeIdList = []
      })
      danListByAdcd(v).then(res => {
        state.riskList = res.data
        state.addForm.riskName = ''
      })
    }
    const submitAdd = () => {
      proxy.$refs["addRef"].validate(valid => {
        console.log(valid);
        if (valid) {

          insertRisk({
            riskType: state.addForm.riskType,
            riskId: state.addForm.riskId,
            riskName: state.addForm.riskName,
            placeIdList: state.addForm.placeIdList,
            riskLevel: state.addForm.riskLevel,
            occurrenceTime: moment(state.addForm.occurrenceTime).format("YYYY-MM-DD HH:mm:ss"),
            riskReason: state.addForm.riskReason,

          }).then(res => {
            if (res.code == 200) {
              proxy.$modal.msgSuccess('新增成功')
              state.addShow = false
              getList()
            }
          })
        }
      })
    }
    const handleView = (row) => {
      // window.open(row.filePath, '_blank')
      proxy.download("/risks/risks/file/downloadFile", {
        fileName: row.filePath
      }, `${row.riskName}通知书_${new Date().getTime()}.pdf`);
    }

    return {
      ...toRefs(state),
      getList,
      reset,
      handleClick,
      handleSelectionChange,
      preparation,
      immediately,
      handleClose,
      submitAdd,
      fileSuccess,
      handleChange,
      handleRemove,
      changeAdcd,
      handleView
    };
  },
});
</script>
<style scoped lang="scss">
.headList {
  display: flex;
  margin-bottom: 10px;

  .items {
    width: 170px;
    // height: 122px;
    border-radius: 8px;
    margin-right: 20px;
    padding: 10px;
    display: flex;
    align-items: center;

    .ig {
      width: 48px;
      height: 48px;
      background-image: url(../../assets/icon/risk1.png);
    }

    &:nth-child(1) {
      .ig {
        background-image: url(../../assets/icon/risk4.png);
      }
    }

    &:nth-child(2) {
      .ig {
        background-image: url(../../assets/icon/risk3.png);
      }
    }

    &:nth-child(3) {
      .ig {
        background-image: url(../../assets/icon/risk2.png);
      }
    }

  }
}

.form {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #E8EAED;
  margin-bottom: 20px;
}

.table {
  height: calc(100vh - 420px);

  &.nobtn {
    height: calc(100vh - 377px);
  }
}

.addForm {
  :deep(.el-form-item) {
    width: calc(50% - 10px);
    margin-right: 10px;
  }
}
</style>