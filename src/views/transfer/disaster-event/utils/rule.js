export const formRules = {
  name: [
    { required: true, message: "请输入灾害事件名称", trigger: "blur" },
    { max: 50, message: "长度不能超过50个字符", trigger: "blur" },
  ],
  townCode: [{ required: true, message: "请选择所在乡镇", trigger: "change" }],
  villageCode: [
    { required: true, message: "请选择所在村社", trigger: "change" },
  ],
  recurrencePeriod: [
    { required: true, message: "请选择重现期", trigger: "change" },
  ],
  longitude: [
    { required: true, message: "请输入经度", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null || value === "") {
          callback(new Error("请输入经度"));
        } else if (value < -180 || value > 180) {
          callback(new Error("经度范围应在-180到180度之间"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  latitude: [
    { required: true, message: "请输入纬度", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null || value === "") {
          callback(new Error("请输入纬度"));
        } else if (value < -90 || value > 90) {
          callback(new Error("纬度范围应在-90到90度之间"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  transferNum: [
    {
      pattern: /^[0-9]{1,6}$/,
      message: "请输入0-999999的整数",
      trigger: "blur",
    },
  ],
  deadNum: [
    {
      pattern: /^[0-9]{1,6}$/,
      message: "请输入0-999999的整数",
      trigger: "blur",
    },
  ],
  missingNum: [
    {
      pattern: /^[0-9]{1,6}$/,
      message: "请输入0-999999的整数",
      trigger: "blur",
    },
  ],
  damagedHouseNum: [
    { pattern: /^[0-9]{1,4}$/, message: "请输入0-9999的整数", trigger: "blur" },
  ],
};
