import { ref, reactive, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getDisasterEventList,
  deleteDisasterEvent,
  saveDisasterEvent,
} from "@/api/transfer";

/**
 * 灾害事件数据管理
 * 处理查询、获取列表、重置查询、表单控制等所有操作
 */
export function useDisasterEventData() {
  // 查询参数
  const queryParams = ref({
    name: "",
    townCode: "",
    villageCode: "",
    recurrencePeriod: "",
    pageNum: 1,
    pageSize: 10,
  });

  // 表格数据
  const tableData = ref([]);
  const total = ref(0);
  const loading = ref(false);

  /**
   * 获取灾害事件列表
   */
  const getList = async () => {
    loading.value = true;
    try {
      const res = await getDisasterEventList(queryParams.value);
      tableData.value = res.rows || [];
      total.value = res.total || 0;
    } catch (error) {
      console.error("获取灾害事件列表失败:", error);
      ElMessage.error("获取列表数据失败，请稍后重试");
      tableData.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 重置查询参数
   */
  const resetQuery = () => {
    queryParams.value = {
      name: "",
      townCode: "",
      villageCode: "",
      recurrencePeriod: "",
      pageNum: 1,
      pageSize: 10,
    };
    getList();
  };

  /**
   * 处理查询
   */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  // ========== 表单控制相关 ==========

  // 表单弹窗相关
  const dialog = reactive({
    visible: false,
    title: "",
    type: "add", // add, edit, view
  });

  // 表单引用
  const formRef = ref(null);

  // 表单数据
  const formData = ref({
    id: undefined,
    name: "",
    occurrenceDate: "",
    townCode: "",
    townName: "",
    villageCode: "",
    villageName: "",
    recurrencePeriod: "",
    totalRainfall: "",
    rainfallStartTime: "",
    rainfallLastHours: "",
    maxRainfallIntensity: "",
    maxRainfallIntensityTime: "",
    maxRainfallIntensityDistance: "",
    peakFlow: "",
    maxWaterLevel: "",
    transferNum: "",
    deadNum: "",
    missingNum: "",
    damagedHouseNum: "",
    directLoss: "",
    longitude: "",
    latitude: "",
  });

  /**
   * 初始化表单
   */
  const initForm = () => {
    formData.value = {
      id: undefined,
      name: "",
      occurrenceDate: "",
      townCode: "",
      townName: "",
      villageCode: "",
      villageName: "",
      recurrencePeriod: "",
      totalRainfall: "",
      rainfallStartTime: "",
      rainfallLastHours: "",
      maxRainfallIntensity: "",
      maxRainfallIntensityTime: "",
      maxRainfallIntensityDistance: "",
      peakFlow: "",
      maxWaterLevel: "",
      transferNum: "",
      deadNum: "",
      missingNum: "",
      damagedHouseNum: "",
      directLoss: "",
      longitude: "",
      latitude: "",
    };
  };

  /**
   * 打开新增弹窗
   */
  const handleAdd = () => {
    initForm();
    if (formRef.value) {
      formRef.value.resetFields();
      formRef.value.clearValidate();
    }
    dialog.type = "add";
    dialog.title = "新增灾害事件";
    dialog.visible = true;
  };

  /**
   * 打开编辑弹窗
   * @param {Object} row 行数据
   */
  const handleEdit = (row) => {
    dialog.type = "edit";
    dialog.title = "编辑灾害事件 - " + row.name;
    dialog.visible = true;
    nextTick(() => {
      formData.value = { ...row };
    });
  };

  /**
   * 打开查看弹窗
   * @param {Object} row 行数据
   */
  const handleView = (row) => {
    dialog.type = "view";
    dialog.title = "查看灾害事件 - " + row.name;
    dialog.visible = true;
    nextTick(() => {
      formData.value = { ...row };
    });
  };

  /**
   * 提交表单
   */
  const submitForm = async () => {
    if (!formRef.value) return;

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          await saveDisasterEvent(formData.value);
          ElMessage.success(dialog.type === "add" ? "新增成功" : "修改成功");
          dialog.visible = false;
          getList();
        } catch (error) {
          console.error("保存失败:", error);
        }
      } else {
        console.warn("表单验证失败");
        return false;
      }
    });
  };

  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    dialog.visible = false;
    nextTick(() => {
      if (formRef.value) {
        formRef.value.resetFields();
        formRef.value.clearValidate();
      }
      initForm();
    });
  };

  /**
   * 处理删除操作
   */
  const handleDelete = (row) => {
    ElMessageBox.confirm("确认要删除该灾害事件吗？", "警告", {
      type: "warning",
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    })
      .then(async () => {
        try {
          await deleteDisasterEvent(row.id);
          ElMessage.success("删除成功");
          getList();
        } catch (error) {
          console.error("删除失败:", error);
        }
      })
      .catch(() => {});
  };

  // 过滤节点方法
  const filterNode = (query, node) => {
    return node.adnm?.toLowerCase().includes(query.toLowerCase());
  };

  return {
    // 数据管理相关
    queryParams,
    tableData,
    loading,
    total,
    getList,
    resetQuery,
    handleQuery,
    handleDelete,

    // 表单控制相关
    dialog,
    formRef,
    formData,
    handleAdd,
    handleEdit,
    handleView,
    closeDialog,
    submitForm,
    filterNode,
  };
}
