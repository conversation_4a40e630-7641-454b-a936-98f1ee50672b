<script setup>
import { onMounted, reactive } from "vue";
import { useDisasterEventData } from "./utils/useDisasterEventData";
import { useLocationData } from "@/composables/useLocationData";
import { formRules } from "./utils/rule";
import { recurrencePeriodOptions } from "./utils/enums";
import SearchForm from "@/components/SearchForm/index.vue";

defineOptions({
  name: "DisasterEvent",
});

const {
  queryParams,
  tableData,
  loading,
  total,
  dialog,
  formRef,
  formData,
  resetQuery,
  handleQuery,
  getList,
  handleDelete,
  handleAdd,
  handleEdit,
  handleView,
  closeDialog,
  submitForm,
  filterNode,
} = useDisasterEventData();

// 地区数据与操作 - 乡镇和村社
const { townshipOptions, villageOptions, getTownships, getVillages } =
  useLocationData();

/**
 * 监听查询乡镇选择变化
 */
const townQuerySelectChange = (value) => {
  queryParams.value.villageCode = "";
  if (value) {
    getVillages(value);
  }
};

/**
 * 乡镇选择变化
 */
const townSelectChange = (value) => {
  formData.value.villageCode = "";
  if (value) {
    getVillages(value);
  }
};

// 搜索表单配置
const searchColumns = reactive([
  {
    type: "input",
    label: "灾害事件名称",
    prop: "name",
    placeholder: "请输入灾害事件名称",
    clearable: true,
  },
  {
    type: "select",
    label: "所在乡镇",
    prop: "townCode",
    placeholder: "请选择乡镇",
    options: townshipOptions,
    labelKey: "adnm",
    valueKey: "adcd",
    clearable: true,
    change: townQuerySelectChange,
  },
  {
    type: "select",
    label: "所在村社",
    prop: "villageCode",
    placeholder: "请选择村社",
    options: villageOptions,
    labelKey: "adnm",
    valueKey: "adcd",
    clearable: true,
    disabled: () => !queryParams.value.townCode,
  },
  {
    type: "select",
    label: "重现期",
    prop: "recurrencePeriod",
    placeholder: "请选择重现期",
    clearable: true,
    options: recurrencePeriodOptions,
  },
]);

onMounted(() => {
  getTownships();
  getList();
});
</script>
<template>
  <div class="app-container">
    <!-- 搜索表单组件 -->
    <search-form
      :columns="searchColumns"
      :searchParam="queryParams"
      :actionCol="4"
      :buttonsCol="12"
      @search="handleQuery"
      @reset="resetQuery"
      class="form-container"
    >
    </search-form>
    <div class="content content-table">
      <div class="table-header">
        <el-button type="success" icon="CirclePlus" @click="handleAdd"
          >新增</el-button
        >
      </div>

      <!-- 表格 -->
      <el-table v-loading="loading" :data="tableData" class="table-container">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="name" label="灾害事件名称" />
        <el-table-column prop="townName" label="乡镇" />
        <el-table-column prop="villageName" label="村社" />
        <el-table-column prop="occurrenceDate" label="发生时间" />
        <el-table-column prop="recurrencePeriod" label="重现期">
          <template #default="scope">
            {{ scope.row.recurrencePeriod }}年一遇
          </template>
        </el-table-column>
        <el-table-column prop="totalRainfall" label="总降雨量(mm)" />
        <el-table-column prop="transferNum" label="转移人数(人)" />
        <el-table-column prop="deadNum" label="死亡人数(人)" />
        <el-table-column prop="directLoss" label="直接经济损失(万元)" />
        <el-table-column label="操作" width="210" fixed="right" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              icon="view"
              link
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              type="primary"
              icon="Edit"
              link
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              icon="Delete"
              link
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 新增/编辑/查看弹窗 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="800px"
      append-to-body
      @close="closeDialog"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="165px"
        :disabled="dialog.type === 'view'"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="灾害事件名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入灾害事件名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生时间" prop="occurrenceDate">
              <el-date-picker
                v-model="formData.occurrenceDate"
                type="datetime"
                placeholder="请选择发生时间"
                style="width: 100%"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number
                v-model="formData.longitude"
                :precision="6"
                :step="0.000001"
                :min="-180"
                :max="180"
                placeholder="请输入经度(-180~180)"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number
                v-model="formData.latitude"
                :precision="6"
                :step="0.000001"
                :min="-90"
                :max="90"
                placeholder="请输入纬度(-90~90)"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在乡镇" prop="townCode">
              <el-select
                v-model="formData.townCode"
                placeholder="请选择乡镇"
                style="width: 100%"
                @change="townSelectChange"
                clearable
              >
                <el-option
                  :label="item.adnm"
                  :value="item.adcd"
                  :filterNodeMethod="filterNode"
                  v-for="item in townshipOptions"
                  :key="item.adcd"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在村社" prop="villageCode">
              <el-select
                v-model="formData.villageCode"
                placeholder="请选择村社"
                style="width: 100%"
                clearable
                :disabled="!formData.townCode"
              >
                <el-option
                  v-for="item in villageOptions"
                  :key="item.adcd"
                  :label="item.adnm"
                  :value="item.adcd"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重现期" prop="recurrencePeriod">
              <el-select
                v-model="formData.recurrencePeriod"
                placeholder="请选择重现期"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in recurrencePeriodOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总降雨量(mm)" prop="totalRainfall">
              <el-input-number
                v-model="formData.totalRainfall"
                :precision="2"
                placeholder="请输入总降雨量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="降雨开始时间" prop="rainfallStartTime">
              <el-date-picker
                v-model="formData.rainfallStartTime"
                type="datetime"
                placeholder="请选择降雨开始时间"
                style="width: 100%"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="降雨历时(时)" prop="rainfallLastHours">
              <el-input-number
                v-model="formData.rainfallLastHours"
                :precision="2"
                placeholder="请输入降雨历时"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="最大降雨强度(mm/h)"
              prop="maxRainfallIntensity"
            >
              <el-input-number
                v-model="formData.maxRainfallIntensity"
                :precision="2"
                placeholder="请输入最大降雨强度"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="最大强度出现时间"
              prop="maxRainfallIntensityTime"
            >
              <el-date-picker
                v-model="formData.maxRainfallIntensityTime"
                type="datetime"
                placeholder="请选择最大强度出现时间"
                style="width: 100%"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="最大强度至灾害时距(时)"
              prop="maxRainfallIntensityDistance"
            >
              <el-input-number
                v-model="formData.maxRainfallIntensityDistance"
                :precision="2"
                placeholder="请输入时距"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调查洪峰流量(m³/s)" prop="peakFlow">
              <el-input-number
                v-model="formData.peakFlow"
                :precision="2"
                placeholder="请输入调查洪峰流量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调查最高水位(m)" prop="maxWaterLevel">
              <el-input-number
                v-model="formData.maxWaterLevel"
                :precision="2"
                placeholder="请输入调查最高水位"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="转移人数(人)" prop="transferNum">
              <el-input-number
                v-model="formData.transferNum"
                :precision="0"
                :min="0"
                :max="999999"
                placeholder="请输入转移人数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="死亡人数(人)" prop="deadNum">
              <el-input-number
                v-model="formData.deadNum"
                :precision="0"
                :min="0"
                :max="999999"
                placeholder="请输入死亡人数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失踪人数(人)" prop="missingNum">
              <el-input-number
                v-model="formData.missingNum"
                :precision="0"
                :min="0"
                :max="999999"
                placeholder="请输入失踪人数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="损坏房屋(座)" prop="damagedHouseNum">
              <el-input-number
                v-model="formData.damagedHouseNum"
                :precision="0"
                :min="0"
                :max="9999"
                placeholder="请输入损坏房屋数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="直接经济损失(万元)" prop="directLoss">
              <el-input-number
                v-model="formData.directLoss"
                :precision="2"
                :min="0"
                placeholder="请输入直接经济损失"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer v-if="dialog.type !== 'view'">
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="submitForm"
            v-if="dialog.type !== 'view'"
            >保 存</el-button
          >
          <el-button @click="closeDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
