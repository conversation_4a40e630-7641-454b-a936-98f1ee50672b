<!--
 * @Description:
 * @Author: ligui<PERSON>
 * @LastEditors: liguiyuan
-->
<template>
  <router-view />
</template>

<script setup>
import useSettingsStore from "@/store/modules/settings";
// import { handleThemeStyle } from '@/utils/theme'
// 在这个页面先加载下不然别的模块地图黑了就
DC.ready({ baseUrl: "/libs/map3d-dc-sdk/resources/" }).then(() => {});
onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    // handleThemeStyle(useSettingsStore().theme)
  });
});
</script>
<style>
.el-dropdown__popper {
  background-color: #013e7e !important;
}
</style>
