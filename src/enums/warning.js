/**
 * 风险隐患类型配置
 */
export const RISK_TYPES = [
  { value: 1, label: "临河滑坡" },
  { value: 2, label: "泥石流" },
  { value: 3, label: "低洼地" },
  { value: 4, label: "急弯" },
  { value: 5, label: "束窄" },
  { value: 6, label: "流域" },
  { value: 7, label: "多支齐汇" },
  { value: 8, label: "桥梁" },
];

/**
 * 威胁类型配置
 */
export const THREAT_TYPES = [
  { value: 1, label: "临河滑坡" },
  { value: 2, label: "泥石流" },
  { value: 3, label: "低洼地漫流" },
  { value: 4, label: "急弯改道" },
  { value: 5, label: "束窄" },
  { value: 6, label: "流域路涵" },
  { value: 7, label: "多支齐汇顶托" },
  { value: 8, label: "桥梁壅水" },
  { value: 9, label: "桥梁漫溢" },
  { value: 10, label: "桥梁溃决" },
];

/**
 * 企事业单位类型配置
 */
export const ENTERPRISE_TYPES = [
  { value: 1, label: "事业单位" },
  { value: 2, label: "国企" },
  { value: 3, label: "民营企业" },
];

/**
 * 防治区类型配置
 */
export const PREVENTION_ZONES_TYPES = [
  { value: 1, label: "重点防治区" },
  { value: 2, label: "一般防治区" },
  { value: 3, label: "非防治区" },
];

/**
 * 枚举类型映射
 */
export const ENUM_MAP = {
  RISK_TYPES,
  THREAT_TYPES,
  ENTERPRISE_TYPES,
  PREVENTION_ZONES_TYPES,
};

/**
 * 获取类型标签
 * @param {number} value - 类型值
 * @param {Array|string} types - 类型配置数组或枚举类型名称
 * @returns {string} 类型标签
 */
export function getTypeLabel(value, types) {
  let typeArray = types;

  // 如果types是字符串，则从ENUM_MAP中获取对应的枚举数组
  if (typeof types === "string") {
    typeArray = ENUM_MAP[types];
    if (!typeArray) {
      console.warn(`未找到枚举类型: ${types}`);
      return "";
    }
  }

  const type = typeArray.find((item) => item.value === value);
  return type ? type.label : "";
}
