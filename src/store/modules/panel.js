const usePanelStore = defineStore('panel', {
  state: () => ({
    currentPanel: 'default', // 当前显示的面板：'default' | 'safe' | 'flood'
  }),
  
  getters: {
    // 获取当前面板
    getCurrentPanel: (state) => state.currentPanel,
    
    // 判断是否显示默认面板
    isDefaultPanel: (state) => state.currentPanel === 'default',
    
    // 判断是否显示安全面板
    isSafePanel: (state) => state.currentPanel === 'safe',
    
    // 判断是否显示防洪调度面板
    isFloodPanel: (state) => state.currentPanel === 'flood',
  },
  
  actions: {
    // 切换到指定面板
    switchPanel(panelType) {
      this.currentPanel = panelType;
    },
    
    // 切换到默认面板
    switchToDefault() {
      this.currentPanel = 'default';
    },
    
    // 切换到安全面板
    switchToSafe() {
      this.currentPanel = 'safe';
    },
    
    // 切换到防洪调度面板
    switchToFlood() {
      this.currentPanel = 'flood';
    },
  },
})

export default usePanelStore 