import { defineStore } from "pinia";
import { ref, computed } from "vue";

/**
 * 地图状态管理
 * 管理地图相关的状态：区域选择、位置定位、图层控制等
 */
const useMapStore = defineStore("map", () => {
  // ==================== 状态定义 ====================

  // 区域选择状态
  const selectedArea = ref({
    code: "", // 区域编码
    type: "", // 类型：'adcd' | 'watershed'
    regions: "", // 区域坐标串
    name: "", // 区域名称
  });

  // 当前地图位置
  const currentPosition = ref({
    lng: null, // 经度
    lat: null, // 纬度
    zoom: null, // 缩放级别
  });

  // 图层控制状态
  const layerStates = ref({
    heatLayer: false, // 热力图图层
    contourLayer: false, // 等值面图层
    cloudLayer: false, // 云图图层
    mouseInteraction: false, // 鼠标交互
  });

  // 流域颜色控制状态
  const watershedColors = ref({
    viewer: null, // 主视图流域颜色
    viewer2: null, // 副视图流域颜色
    defaultColor: "rgba(0, 0, 0, 0)", // 默认透明色
  });

  // 地图标记状态
  const mapMarkers = ref([]);

  // 场景控制状态
  const sceneStates = ref({
    flashModels: [], // 闪烁模型列表
    stareAtModel: null, // 跟踪的模型
    mouseShowLabel: false, // 鼠标显示标签
    historyPaths: [], // 历史路径列表
    activeHistoryPath: null, // 当前激活的历史路径
  });

  // ==================== 计算属性 ====================

  // 是否有选中的区域
  const hasSelectedArea = computed(() => {
    return selectedArea.value.code !== "";
  });

  // 是否选中的是行政区
  const isDistrictSelected = computed(() => {
    return selectedArea.value.type === "adcd";
  });

  // 是否选中的是流域
  const isWatershedSelected = computed(() => {
    return selectedArea.value.type === "watershed";
  });

  // 是否有地图标记
  const hasMapMarkers = computed(() => {
    return mapMarkers.value.length > 0;
  });

  // 活跃的图层数量
  const activeLayerCount = computed(() => {
    return Object.values(layerStates.value).filter(Boolean).length;
  });

  // ==================== Actions ====================

  /**
   * 设置选中的区域
   * 替代原来的 changeAreaSelect 事件
   */
  const setSelectedArea = (areaData) => {
    selectedArea.value = {
      code: areaData.code || "",
      type: areaData.type || "",
      regions: areaData.regions || "",
      name: areaData.name || "",
    };

    console.log("Area selected:", selectedArea.value);
  };

  /**
   * 清除选中的区域
   */
  const clearSelectedArea = () => {
    selectedArea.value = {
      code: "",
      type: "",
      regions: "",
      name: "",
    };
  };

  /**
   * 飞行到指定位置
   * 替代原来的 flyToPostion 事件
   */
  const flyToPosition = (position) => {
    currentPosition.value = {
      lng: position.lng,
      lat: position.lat,
      zoom: position.zoom || currentPosition.value.zoom,
    };

    console.log("Flying to position:", currentPosition.value);
  };

  /**
   * 根据站点编码飞行
   * 替代原来的 flyByStcd 事件
   */
  const flyByStcd = (stcd) => {
    console.log("Flying by STCD:", stcd);
    // 这里可以根据 stcd 查询坐标，然后调用 flyToPosition
  };

  /**
   * 飞行到模型位置
   * 替代原来的 flyToModel 事件
   */
  const flyToModel = (modelData) => {
    if (modelData.lng && modelData.lat) {
      flyToPosition({
        lng: modelData.lng,
        lat: modelData.lat,
        zoom: modelData.zoom,
      });
    }
    console.log("Flying to model:", modelData);
  };

  /**
   * 控制热力图图层显示
   * 替代原来的 showHeatLayer/hideHeatLayer 事件
   */
  const toggleHeatLayer = (visible) => {
    layerStates.value.heatLayer = visible;
    console.log("Heat layer:", visible ? "shown" : "hidden");
  };

  /**
   * 控制等值面图层
   * 替代原来的 rainContour/visible 和 rainContour/clear 事件
   */
  const toggleContourLayer = (visible) => {
    layerStates.value.contourLayer = visible;
    console.log("Contour layer:", visible ? "shown" : "hidden");
  };

  /**
   * 控制云图图层
   * 替代原来的 layerCloud/visible 事件
   */
  const toggleCloudLayer = (visible) => {
    layerStates.value.cloudLayer = visible;
    console.log("Cloud layer:", visible ? "shown" : "hidden");
  };

  /**
   * 设置鼠标交互状态
   * 替代原来的 scene/activityMouseShowLabel 和 scene/deactivityMouseShowLabel 事件
   */
  const setMouseInteraction = (active) => {
    layerStates.value.mouseInteraction = active;
    sceneStates.value.mouseShowLabel = active;
    console.log("Mouse interaction:", active ? "activated" : "deactivated");
  };

  /**
   * 设置地图标记
   * 替代原来的 map/marker 事件
   */
  const setMapMarker = (markerData) => {
    if (Array.isArray(markerData)) {
      mapMarkers.value = markerData;
    } else {
      mapMarkers.value.push(markerData);
    }
    console.log("Map markers updated:", mapMarkers.value.length);
  };

  /**
   * 清除地图标记
   * 替代原来的 map/marker/clear 事件
   */
  const clearMapMarkers = () => {
    mapMarkers.value = [];
    console.log("Map markers cleared");
  };

  /**
   * 显示闪烁模型
   * 替代原来的 scene/showflashModels 事件
   */
  const showFlashModels = (models) => {
    sceneStates.value.flashModels = Array.isArray(models) ? models : [models];
    console.log("Flash models shown:", sceneStates.value.flashModels.length);
  };

  /**
   * 隐藏闪烁模型
   * 替代原来的 scene/hideflashModels 事件
   */
  const hideFlashModels = () => {
    sceneStates.value.flashModels = [];
    console.log("Flash models hidden");
  };

  /**
   * 设置跟踪模型
   * 替代原来的 scene/stareAtModel 事件
   */
  const stareAtModel = (modelData) => {
    sceneStates.value.stareAtModel = modelData;
    console.log("Staring at model:", modelData);
  };

  /**
   * 显示历史路径
   * 替代原来的 scene/showHistoryPath 事件
   */
  const showHistoryPath = (pathData) => {
    if (!sceneStates.value.historyPaths.find((p) => p.id === pathData.id)) {
      sceneStates.value.historyPaths.push(pathData);
    }
    sceneStates.value.activeHistoryPath = pathData;
    console.log("History path shown:", pathData);
  };

  /**
   * 隐藏历史路径
   * 替代原来的 scene/hideHistoryPath 事件
   */
  const hideHistoryPath = (pathId = null) => {
    if (pathId) {
      sceneStates.value.historyPaths = sceneStates.value.historyPaths.filter(
        (p) => p.id !== pathId
      );
      if (sceneStates.value.activeHistoryPath?.id === pathId) {
        sceneStates.value.activeHistoryPath = null;
      }
    } else {
      sceneStates.value.historyPaths = [];
      sceneStates.value.activeHistoryPath = null;
    }
    console.log("History path hidden:", pathId || "all");
  };

  // ==================== 流域颜色控制 ====================

  /**
   * 更新主视图流域颜色
   * 替代原来的 catchmentFillColor_viewer/update 事件
   */
  const updateWatershedColor = (color) => {
    watershedColors.value.viewer = color;
    console.log("Watershed color updated for viewer:", color);

    // 如果有全局的 viewer 对象，直接应用颜色
    if (window.viewer) {
      try {
        // 这里需要根据实际的地图库API来实现颜色更新
        // 示例：假设有流域图层需要更新颜色
        console.log("Applying watershed color to viewer:", color);
      } catch (error) {
        console.error("Failed to apply watershed color:", error);
      }
    }
  };

  /**
   * 更新副视图流域颜色
   * 替代原来的 catchmentFillColor_viewer2/update 事件
   */
  const updateWatershedColor2 = (color) => {
    watershedColors.value.viewer2 = color;
    console.log("Watershed color updated for viewer2:", color);

    // 如果有全局的 viewer2 对象，直接应用颜色
    if (window.viewer2) {
      try {
        // 这里需要根据实际的地图库API来实现颜色更新
        console.log("Applying watershed color to viewer2:", color);
      } catch (error) {
        console.error("Failed to apply watershed color to viewer2:", error);
      }
    }
  };

  /**
   * 重置流域颜色为默认值
   */
  const resetWatershedColors = () => {
    const defaultColor = watershedColors.value.defaultColor;
    updateWatershedColor(defaultColor);
    updateWatershedColor2(defaultColor);
  };

  // ==================== 图层控制 ====================

  /**
   * 重置所有地图状态
   */
  const resetMapState = () => {
    clearSelectedArea();
    clearMapMarkers();
    hideFlashModels();
    resetWatershedColors();
    layerStates.value = {
      heatLayer: false,
      contourLayer: false,
      cloudLayer: false,
      mouseInteraction: false,
    };
    sceneStates.value = {
      flashModels: [],
      stareAtModel: null,
      mouseShowLabel: false,
      historyPaths: [],
      activeHistoryPath: null,
    };
    console.log("Map state reset");
  };

  // ==================== 返回 ====================
  return {
    // 状态
    selectedArea,
    currentPosition,
    layerStates,
    mapMarkers,
    sceneStates,
    watershedColors,

    // 计算属性
    hasSelectedArea,
    isDistrictSelected,
    isWatershedSelected,
    hasMapMarkers,
    activeLayerCount,

    // 地图基础方法
    setSelectedArea,
    clearSelectedArea,
    flyToPosition,
    flyByStcd,
    flyToModel,

    // 图层控制方法
    toggleHeatLayer,
    toggleContourLayer,
    toggleCloudLayer,
    setMouseInteraction,

    // 标记控制方法
    setMapMarker,
    clearMapMarkers,

    // 场景控制方法
    showFlashModels,
    hideFlashModels,
    stareAtModel,
    showHistoryPath,
    hideHistoryPath,

    // 流域颜色控制方法
    updateWatershedColor,
    updateWatershedColor2,
    resetWatershedColors,

    // 重置方法
    resetMapState,
  };
});

export default useMapStore;
