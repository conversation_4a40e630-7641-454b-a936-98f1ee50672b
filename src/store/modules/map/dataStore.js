import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 地图数据状态管理
 * 管理雨量、河流、水库等数据的状态
 */
const useMapDataStore = defineStore("mapData", () => {
  // ==================== 状态定义 ====================

  // 雨量数据
  const rainData = ref([])
  const rainUpdateTime = ref(null)
  const rainLoading = ref(false)

  // 河流数据
  const riverData = ref([])
  const riverUpdateTime = ref(null)
  const riverLoading = ref(false)

  // 水库数据
  const reservoirData = ref([])
  const reservoirUpdateTime = ref(null)
  const reservoirLoading = ref(false)

  // 洪水预警数据
  const floodData = ref([])
  const floodUpdateTime = ref(null)

  // 转移风险点数据
  const transferRiskData = ref([])
  const transferPlaceData = ref([])

  // 河流数据（对比分析用）
  const river3Data = ref([])      // 主屏河流数据
  const river31Data = ref([])     // 副屏河流数据

  // 水库数据（对比分析用）
  const reservoir3Data = ref([])  // 主屏水库数据
  const reservoir31Data = ref([]) // 副屏水库数据

  // 流域颜色渐变数据
  const catchmentColors = ref({
    viewer: null,               // 主屏流域颜色
    viewer2: null               // 副屏流域颜色
  })

  // 雨量地图更新时间
  const rainMapUpdateHour = ref(null)

  // 数据过滤条件
  const dataFilters = ref({
    rainThreshold: 0,           // 雨量阈值
    riverWarnType: '全部',      // 河流预警类型
    timeRange: null,            // 时间范围
    adcd: '',                   // 行政区编码
    lycode: ''                  // 流域编码
  })

  // ==================== 计算属性 ====================

  // 是否有雨量数据
  const hasRainData = computed(() => rainData.value.length > 0)

  // 是否有河流数据
  const hasRiverData = computed(() => riverData.value.length > 0)

  // 是否有水库数据
  const hasReservoirData = computed(() => reservoirData.value.length > 0)

  // 有雨的站点数据
  const rainStationsWithRain = computed(() => {
    return rainData.value.filter(item => item.totalRainValue > 0)
  })

  // 无雨的站点数据
  const rainStationsWithoutRain = computed(() => {
    return rainData.value.filter(item => item.totalRainValue === 0)
  })

  // 过滤后的河流数据
  const filteredRiverData = computed(() => {
    if (dataFilters.value.riverWarnType === '全部') {
      return riverData.value
    }
    return riverData.value.filter(item => item.warnType === dataFilters.value.riverWarnType)
  })

  // 最新数据更新时间
  const latestUpdateTime = computed(() => {
    const times = [
      rainUpdateTime.value,
      riverUpdateTime.value,
      reservoirUpdateTime.value,
      floodUpdateTime.value
    ].filter(Boolean)

    if (times.length === 0) return null

    return Math.max(...times.map(t => new Date(t).getTime()))
  })

  // 是否有任何数据在加载
  const isAnyDataLoading = computed(() => {
    return rainLoading.value || riverLoading.value || reservoirLoading.value
  })

  // 数据统计信息
  const dataStats = computed(() => {
    return {
      rainStations: rainData.value.length,
      rainStationsWithRain: rainStationsWithRain.value.length,
      riverStations: riverData.value.length,
      reservoirs: reservoirData.value.length,
      transferRiskPoints: transferRiskData.value.length,
      transferPlaces: transferPlaceData.value.length
    }
  })

  // ==================== Actions ====================

  /**
   * 更新雨量数据
   * 替代原来的 rain/update 事件
   */
  const updateRainData = (data) => {
    rainData.value = Array.isArray(data) ? data : []
    rainUpdateTime.value = new Date().toISOString()
    rainLoading.value = false

    console.log('Rain data updated:', rainData.value.length, 'records')
  }

  /**
   * 更新河流数据
   * 替代原来的 river/update 事件
   */
  const updateRiverData = (data) => {
    riverData.value = Array.isArray(data) ? data : []
    riverUpdateTime.value = new Date().toISOString()
    riverLoading.value = false

    console.log('River data updated:', riverData.value.length, 'records')
  }

  /**
   * 更新水库数据
   * 替代原来的 reservoir/update 事件
   */
  const updateReservoirData = (data) => {
    reservoirData.value = Array.isArray(data) ? data : []
    reservoirUpdateTime.value = new Date().toISOString()
    reservoirLoading.value = false

    console.log('Reservoir data updated:', reservoirData.value.length, 'records')
  }

  /**
   * 更新洪水预警数据
   * 替代原来的 update/flood/data 事件
   */
  const updateFloodData = (data) => {
    floodData.value = Array.isArray(data) ? data : []
    floodUpdateTime.value = new Date().toISOString()

    console.log('Flood data updated:', floodData.value.length, 'records')
  }

  /**
   * 更新转移风险点数据
   * 替代原来的 transfer/risk 事件
   */
  const updateTransferRiskData = (data) => {
    transferRiskData.value = Array.isArray(data) ? data : []
    console.log('Transfer risk data updated:', transferRiskData.value.length, 'records')
  }

  /**
   * 更新转移安置点数据
   * 替代原来的 transfer/place 事件
   */
  const updateTransferPlaceData = (data) => {
    transferPlaceData.value = Array.isArray(data) ? data : []
    console.log('Transfer place data updated:', transferPlaceData.value.length, 'records')
  }

  /**
   * 设置数据加载状态
   */
  const setDataLoading = (dataType, loading) => {
    switch (dataType) {
      case 'rain':
        rainLoading.value = loading
        break
      case 'river':
        riverLoading.value = loading
        break
      case 'reservoir':
        reservoirLoading.value = loading
        break
    }
  }

  /**
   * 设置数据过滤条件
   */
  const setDataFilter = (filterType, value) => {
    if (filterType in dataFilters.value) {
      dataFilters.value[filterType] = value
      console.log(`Data filter ${filterType} set to:`, value)
    }
  }

  /**
   * 批量设置数据过滤条件
   */
  const setDataFilters = (filters) => {
    Object.keys(filters).forEach(key => {
      if (key in dataFilters.value) {
        dataFilters.value[key] = filters[key]
      }
    })
    console.log('Data filters updated:', dataFilters.value)
  }

  /**
   * 清除所有数据
   */
  const clearAllData = () => {
    rainData.value = []
    riverData.value = []
    reservoirData.value = []
    floodData.value = []
    transferRiskData.value = []
    transferPlaceData.value = []

    rainUpdateTime.value = null
    riverUpdateTime.value = null
    reservoirUpdateTime.value = null
    floodUpdateTime.value = null

    rainLoading.value = false
    riverLoading.value = false
    reservoirLoading.value = false

    console.log('All data cleared')
  }

  /**
   * 重置数据过滤条件
   */
  const resetDataFilters = () => {
    dataFilters.value = {
      rainThreshold: 0,
      riverWarnType: '全部',
      timeRange: null,
      adcd: '',
      lycode: ''
    }
    console.log('Data filters reset')
  }

  /**
   * 更新对比分析河流数据
   * 替代原来的 river3/update 和 river31/update 事件
   */
  const updateRiver3Data = (data) => {
    river3Data.value = Array.isArray(data) ? data : []
    console.log('River3 data updated:', river3Data.value.length, 'items')
  }

  const updateRiver31Data = (data) => {
    river31Data.value = Array.isArray(data) ? data : []
    console.log('River31 data updated:', river31Data.value.length, 'items')
  }

  /**
   * 更新对比分析水库数据
   * 替代原来的 reservoir3/update 和 reservoir31/update 事件
   */
  const updateReservoir3Data = (data) => {
    reservoir3Data.value = Array.isArray(data) ? data : []
    console.log('Reservoir3 data updated:', reservoir3Data.value.length, 'items')
  }

  const updateReservoir31Data = (data) => {
    reservoir31Data.value = Array.isArray(data) ? data : []
    console.log('Reservoir31 data updated:', reservoir31Data.value.length, 'items')
  }

  /**
   * 更新流域颜色渐变
   * 替代原来的 catchmentFillColor_viewer/update 和 catchmentFillColor_viewer2/update 事件
   */
  const updateCatchmentColor = (viewer, color) => {
    if (viewer === 'viewer' || viewer === 'viewer1') {
      catchmentColors.value.viewer = color
    } else if (viewer === 'viewer2') {
      catchmentColors.value.viewer2 = color
    }
    console.log(`Catchment color updated for ${viewer}:`, color)
  }

  /**
   * 更新雨量地图时间
   * 替代原来的 rainMap/update 事件
   */
  const updateRainMapHour = (hour) => {
    rainMapUpdateHour.value = hour
    console.log('Rain map hour updated:', hour)
  }

  /**
   * 根据站点编码查找数据
   */
  const findDataByStcd = (stcd, dataType = 'all') => {
    const results = {}

    if (dataType === 'all' || dataType === 'rain') {
      results.rain = rainData.value.find(item => item.STCD === stcd)
    }

    if (dataType === 'all' || dataType === 'river') {
      results.river = riverData.value.find(item => item.STCD === stcd)
    }

    if (dataType === 'all' || dataType === 'reservoir') {
      results.reservoir = reservoirData.value.find(item => item.STCD === stcd)
    }

    return dataType === 'all' ? results : results[dataType]
  }

  // ==================== 返回 ====================
  return {
    // 状态
    rainData,
    riverData,
    reservoirData,
    floodData,
    transferRiskData,
    transferPlaceData,
    river3Data,
    river31Data,
    reservoir3Data,
    reservoir31Data,
    catchmentColors,
    rainMapUpdateHour,
    rainUpdateTime,
    riverUpdateTime,
    reservoirUpdateTime,
    floodUpdateTime,
    rainLoading,
    riverLoading,
    reservoirLoading,
    dataFilters,

    // 计算属性
    hasRainData,
    hasRiverData,
    hasReservoirData,
    rainStationsWithRain,
    rainStationsWithoutRain,
    filteredRiverData,
    latestUpdateTime,
    isAnyDataLoading,
    dataStats,

    // 方法
    updateRainData,
    updateRiverData,
    updateReservoirData,
    updateFloodData,
    updateTransferRiskData,
    updateTransferPlaceData,
    updateRiver3Data,
    updateRiver31Data,
    updateReservoir3Data,
    updateReservoir31Data,
    updateCatchmentColor,
    updateRainMapHour,
    setDataLoading,
    setDataFilter,
    setDataFilters,
    clearAllData,
    resetDataFilters,
    findDataByStcd
  }
})

export default useMapDataStore
