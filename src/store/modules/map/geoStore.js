import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 地理数据状态管理
 * 管理地理边界、几何数据等相关状态
 */
const useGeoStore = defineStore("geo", () => {
  // ==================== 状态定义 ====================
  
  // 当前编辑的边界数据
  const currentBoundary = ref({
    geom: null,              // 几何数据
    type: '',                // 数据类型：'basin' | 'river' | 'station' | 'township' | 'reservoir'
    id: null,                // 数据ID
    name: '',                // 数据名称
    isEditing: false         // 是否正在编辑
  })
  
  // 边界更新事件监听器
  const boundaryUpdateListeners = ref(new Map())
  
  // 地理数据缓存
  const geoDataCache = ref({
    basins: [],              // 流域数据
    rivers: [],              // 河流数据
    stations: [],            // 测站数据
    townships: [],           // 乡镇数据
    reservoirs: []           // 水库数据
  })
  
  // 地图编辑状态
  const mapEditState = ref({
    isEditing: false,        // 是否正在编辑
    editMode: '',            // 编辑模式：'draw' | 'edit' | 'delete'
    selectedFeatures: [],    // 选中的要素
    drawingType: ''          // 绘制类型：'point' | 'line' | 'polygon'
  })
  
  // ==================== 计算属性 ====================
  
  /**
   * 是否有当前边界数据
   */
  const hasCurrentBoundary = computed(() => {
    return currentBoundary.value.geom !== null
  })
  
  /**
   * 是否正在编辑地图
   */
  const isMapEditing = computed(() => {
    return mapEditState.value.isEditing
  })
  
  /**
   * 当前编辑的数据类型
   */
  const currentEditType = computed(() => {
    return currentBoundary.value.type
  })
  
  // ==================== 方法 ====================
  
  /**
   * 更新边界数据
   * 替代原来的 updateBound/update 事件
   */
  const updateBoundary = (geojson, options = {}) => {
    currentBoundary.value = {
      geom: geojson,
      type: options.type || currentBoundary.value.type,
      id: options.id || currentBoundary.value.id,
      name: options.name || currentBoundary.value.name,
      isEditing: true
    }
    
    // 触发所有注册的监听器
    boundaryUpdateListeners.value.forEach((listener, key) => {
      try {
        listener(geojson, options)
      } catch (error) {
        console.error(`Boundary update listener error for ${key}:`, error)
      }
    })
    
    console.log('Boundary updated:', geojson)
  }
  
  /**
   * 注册边界更新监听器
   */
  const registerBoundaryListener = (key, listener) => {
    boundaryUpdateListeners.value.set(key, listener)
    console.log(`Boundary listener registered: ${key}`)
  }
  
  /**
   * 移除边界更新监听器
   */
  const unregisterBoundaryListener = (key) => {
    boundaryUpdateListeners.value.delete(key)
    console.log(`Boundary listener unregistered: ${key}`)
  }
  
  /**
   * 设置当前编辑的边界
   */
  const setCurrentBoundary = (boundaryData) => {
    currentBoundary.value = {
      ...currentBoundary.value,
      ...boundaryData
    }
  }
  
  /**
   * 清除当前边界数据
   */
  const clearCurrentBoundary = () => {
    currentBoundary.value = {
      geom: null,
      type: '',
      id: null,
      name: '',
      isEditing: false
    }
  }
  
  /**
   * 开始地图编辑
   */
  const startMapEdit = (mode = 'draw', drawingType = 'polygon') => {
    mapEditState.value = {
      isEditing: true,
      editMode: mode,
      selectedFeatures: [],
      drawingType: drawingType
    }
    console.log('Map edit started:', mode, drawingType)
  }
  
  /**
   * 结束地图编辑
   */
  const endMapEdit = () => {
    mapEditState.value = {
      isEditing: false,
      editMode: '',
      selectedFeatures: [],
      drawingType: ''
    }
    console.log('Map edit ended')
  }
  
  /**
   * 设置选中的要素
   */
  const setSelectedFeatures = (features) => {
    mapEditState.value.selectedFeatures = Array.isArray(features) ? features : [features]
  }
  
  /**
   * 缓存地理数据
   */
  const cacheGeoData = (type, data) => {
    if (type in geoDataCache.value) {
      geoDataCache.value[type] = data
      console.log(`Geo data cached for ${type}:`, data.length)
    }
  }
  
  /**
   * 获取缓存的地理数据
   */
  const getCachedGeoData = (type) => {
    return geoDataCache.value[type] || []
  }
  
  /**
   * 清除地理数据缓存
   */
  const clearGeoDataCache = (type = null) => {
    if (type) {
      geoDataCache.value[type] = []
    } else {
      geoDataCache.value = {
        basins: [],
        rivers: [],
        stations: [],
        townships: [],
        reservoirs: []
      }
    }
    console.log('Geo data cache cleared:', type || 'all')
  }
  
  /**
   * 重置所有地理状态
   */
  const resetGeoState = () => {
    clearCurrentBoundary()
    endMapEdit()
    clearGeoDataCache()
    boundaryUpdateListeners.value.clear()
    console.log('Geo state reset')
  }
  
  // ==================== 返回 ====================
  return {
    // 状态
    currentBoundary,
    boundaryUpdateListeners,
    geoDataCache,
    mapEditState,
    
    // 计算属性
    hasCurrentBoundary,
    isMapEditing,
    currentEditType,
    
    // 方法
    updateBoundary,
    registerBoundaryListener,
    unregisterBoundaryListener,
    setCurrentBoundary,
    clearCurrentBoundary,
    startMapEdit,
    endMapEdit,
    setSelectedFeatures,
    cacheGeoData,
    getCachedGeoData,
    clearGeoDataCache,
    resetGeoState
  }
})

export default useGeoStore
