import { defineStore } from "pinia";
import { ref, computed } from "vue";

/**
 * 面板状态管理
 * 管理各种弹窗面板的显示控制和数据状态
 */
const useMapPanelStore = defineStore("mapPanel", () => {
  // ================= S =================
  // 主要信息面板
  const mainPanel = ref({
    visible: false, // 是否显示
    title: "", // 标题
    data: null, // 数据
    type: "", // 面板类型：'rain', 'river', 'reservoir'
    position: { x: 0, y: 0 }, // 面板位置 - 暂时没用
    loading: false, // 是否加载中 - 暂时没用
  });

  /**
   * 显示主面板
   * 替代原来的 panel_show 事件
   */
  const showMainPanel = (data, position = null) => {
    // 记录到历史
    addToHistory("main", data);

    mainPanel.value = {
      visible: true,
      title: data.STNM ? data.STNM : "",
      data: data,
      type: data?.STTP || "",
      position: position || { x: 0, y: 0 },
      loading: false,
    };
  };

  // 对比分析面板（双屏对比时使用）
  const comparisonPanel = ref({
    visible: false, // 是否显示
    data: null, // 数据
  });

  /**
   * 显示对比面板
   * 替代原来的 panel_show_2 事件
   */
  const showComparisonPanel = (data) => {
    // 记录到历史
    addToHistory("comparison", data);

    comparisonPanel.value = {
      visible: true,
      data: data,
    };

    console.log("显示对比面板:", data);
  };

  // ================= E =================

  // 详情面板（详细信息展示）
  const detailPanel = ref({
    visible: false,
    data: null,
    title: "",
    loading: false,
  });

  // 分析面板（淹没分析等）
  const analysisPanel = ref({
    visible: false,
    data: null,
    analysisType: "", // 分析类型
    results: null, // 分析结果
    loading: false,
  });

  // 方案面板（调度方案等）
  const schemePanel = ref({
    visible: false,
    data: null,
    schemeType: "", // 方案类型
    selectedSchemes: [], // 选中的方案
    loading: false,
  });

  // 智能方案面板
  const agentSchemePanel = ref({
    visible: false,
    data: null,
    loading: false,
  });

  // 气泡提示面板
  const bubblePanel = ref({
    visible: false,
    content: "",
    position: { x: 0, y: 0 },
    autoHide: true,
    hideDelay: 3000,
  });

  // 结果面板特殊事件状态
  const resultPanelEvent = ref({
    triggered: false,
    data: null,
    timestamp: 0,
  });

  // 调度方案选择状态
  const dispatchSelection = ref({
    primary: null, // 主要方案选择
    secondary: null, // 次要方案选择（对比分析用）
    timestamp: 0,
  });

  // 右侧面板控制状态
  const rightPanelControl = ref({
    visible: true, // 是否显示右侧面板
    height: "auto", // 面板高度：'auto' | '95%'
    currentTab: 0, // 当前标签页
    showEvent: false, // 显示事件触发状态
    hideEvent: false, // 隐藏事件触发状态
  });

  // 面板历史记录
  const panelHistory = ref([]);
  const maxHistoryLength = 10;

  // ==================== 计算属性 ====================

  // 是否有可见的面板
  const hasVisiblePanels = computed(() => {
    return (
      mainPanel.value.visible ||
      comparisonPanel.value.visible ||
      detailPanel.value.visible ||
      analysisPanel.value.visible ||
      schemePanel.value.visible ||
      agentSchemePanel.value.visible
    );
  });

  // 可见面板数量
  const visiblePanelCount = computed(() => {
    let count = 0;
    if (mainPanel.value.visible) count++;
    if (comparisonPanel.value.visible) count++;
    if (detailPanel.value.visible) count++;
    if (analysisPanel.value.visible) count++;
    if (schemePanel.value.visible) count++;
    if (agentSchemePanel.value.visible) count++;
    return count;
  });

  // 是否有任何面板在加载
  const isAnyPanelLoading = computed(() => {
    return (
      mainPanel.value.loading ||
      comparisonPanel.value.loading ||
      detailPanel.value.loading ||
      analysisPanel.value.loading ||
      schemePanel.value.loading ||
      agentSchemePanel.value.loading
    );
  });

  // 当前活跃的面板类型
  const activePanelTypes = computed(() => {
    const types = [];
    if (mainPanel.value.visible) types.push("main");
    if (comparisonPanel.value.visible) types.push("comparison");
    if (detailPanel.value.visible) types.push("detail");
    if (analysisPanel.value.visible) types.push("analysis");
    if (schemePanel.value.visible) types.push("scheme");
    if (agentSchemePanel.value.visible) types.push("agentScheme");
    return types;
  });

  // ==================== Actions ====================

  /**
   * 显示详情面板
   */
  const showDetailPanel = (data, title = "") => {
    detailPanel.value = {
      visible: true,
      data: data,
      title: title,
      loading: false,
    };

    console.log("Detail panel shown:", { title, data });
  };

  /**
   * 显示分析面板
   */
  const showAnalysisPanel = (analysisType, data = null) => {
    analysisPanel.value = {
      visible: true,
      data: data,
      analysisType: analysisType,
      results: null,
      loading: false,
    };

    console.log("Analysis panel shown:", analysisType);
  };

  /**
   * 显示方案面板
   * 替代原来的 forecastDispatchPlan 事件
   */
  const showSchemePanel = (schemeType, data = null) => {
    schemePanel.value = {
      visible: true,
      data: data,
      schemeType: schemeType,
      selectedSchemes: [],
      loading: false,
    };

    console.log("Scheme panel shown:", schemeType);
  };

  /**
   * 显示智能方案面板
   * 替代原来的 change/agent-scheme/show 事件
   */
  const showAgentSchemePanel = (data = null) => {
    agentSchemePanel.value = {
      visible: true,
      data: data,
      loading: false,
    };

    console.log("Agent scheme panel shown");
  };

  /**
   * 显示结果面板（特殊面板）
   * 替代原来的 panel_show123 事件
   */
  const showResultPanel = (data) => {
    // 触发结果面板事件
    resultPanelEvent.value = {
      triggered: true,
      data: data,
      timestamp: Date.now(),
    };

    console.log("Result panel event triggered:", data);
  };

  /**
   * 选择主要调度方案
   * 替代原来的 floodPreRehearsal/dispatch/select 事件
   */
  const selectPrimaryDispatch = (data) => {
    dispatchSelection.value = {
      ...dispatchSelection.value,
      primary: data,
      timestamp: Date.now(),
    };

    console.log("Primary dispatch selected:", data);
  };

  /**
   * 选择次要调度方案（对比分析用）
   * 替代原来的 floodPreRehearsal/dispatch/select2 事件
   */
  const selectSecondaryDispatch = (data) => {
    dispatchSelection.value = {
      ...dispatchSelection.value,
      secondary: data,
      timestamp: Date.now(),
    };

    console.log("Secondary dispatch selected:", data);
  };

  /**
   * 隐藏主面板
   */
  const hideMainPanel = () => {
    mainPanel.value.visible = false;
    console.log("Main panel hidden");
  };

  /**
   * 隐藏对比面板
   */
  const hideComparisonPanel = () => {
    comparisonPanel.value.visible = false;
    console.log("Comparison panel hidden");
  };

  /**
   * 隐藏详情面板
   */
  const hideDetailPanel = () => {
    detailPanel.value.visible = false;
    console.log("Detail panel hidden");
  };

  /**
   * 隐藏分析面板
   */
  const hideAnalysisPanel = () => {
    analysisPanel.value.visible = false;
    console.log("Analysis panel hidden");
  };

  /**
   * 隐藏方案面板
   * 替代原来的 backList 事件
   */
  const hideSchemePanel = () => {
    schemePanel.value.visible = false;
    console.log("Scheme panel hidden");
  };

  /**
   * 隐藏智能方案面板
   * 替代原来的 change/agent-scheme/hide 事件
   */
  const hideAgentSchemePanel = () => {
    agentSchemePanel.value.visible = false;
    console.log("Agent scheme panel hidden");
  };

  /**
   * 隐藏所有面板
   */
  const hideAllPanels = () => {
    mainPanel.value.visible = false;
    comparisonPanel.value.visible = false;
    detailPanel.value.visible = false;
    analysisPanel.value.visible = false;
    schemePanel.value.visible = false;
    agentSchemePanel.value.visible = false;
    hideBubblePanel();

    console.log("All panels hidden");
  };

  /**
   * 显示气泡提示
   * 替代原来的 bubble_vanish 事件（反向操作）
   */
  const showBubblePanel = (
    content,
    position,
    autoHide = true,
    hideDelay = 3000
  ) => {
    bubblePanel.value = {
      visible: true,
      content: content,
      position: position || { x: 0, y: 0 },
      autoHide: autoHide,
      hideDelay: hideDelay,
    };

    // 自动隐藏
    if (autoHide) {
      setTimeout(() => {
        hideBubblePanel();
      }, hideDelay);
    }

    console.log("Bubble panel shown:", content);
  };

  /**
   * 隐藏气泡提示
   * 替代原来的 bubble_vanish 事件
   */
  const hideBubblePanel = () => {
    bubblePanel.value.visible = false;
    console.log("Bubble panel hidden");
  };

  /**
   * 设置面板加载状态
   */
  const setPanelLoading = (panelType, loading) => {
    switch (panelType) {
      case "main":
        mainPanel.value.loading = loading;
        break;
      case "comparison":
        comparisonPanel.value.loading = loading;
        break;
      case "detail":
        detailPanel.value.loading = loading;
        break;
      case "analysis":
        analysisPanel.value.loading = loading;
        break;
      case "scheme":
        schemePanel.value.loading = loading;
        break;
      case "agentScheme":
        agentSchemePanel.value.loading = loading;
        break;
    }
  };

  /**
   * 更新面板数据
   */
  const updatePanelData = (panelType, data) => {
    switch (panelType) {
      case "main":
        mainPanel.value.data = data;
        break;
      case "comparison":
        comparisonPanel.value.data = data;
        break;
      case "detail":
        detailPanel.value.data = data;
        break;
      case "analysis":
        analysisPanel.value.data = data;
        break;
      case "scheme":
        schemePanel.value.data = data;
        break;
      case "agentScheme":
        agentSchemePanel.value.data = data;
        break;
    }
  };

  /**
   * 设置分析结果
   */
  const setAnalysisResults = (results) => {
    analysisPanel.value.results = results;
    console.log("Analysis results updated:", results);
  };

  /**
   * 设置选中的方案
   */
  const setSelectedSchemes = (schemes) => {
    schemePanel.value.selectedSchemes = Array.isArray(schemes)
      ? schemes
      : [schemes];
    console.log("Selected schemes updated:", schemePanel.value.selectedSchemes);
  };

  /**
   * 添加到历史记录
   */
  const addToHistory = (panelType, data) => {
    const historyItem = {
      panelType,
      data,
      timestamp: new Date().toISOString(),
    };

    panelHistory.value.unshift(historyItem);

    // 限制历史记录长度
    if (panelHistory.value.length > maxHistoryLength) {
      panelHistory.value = panelHistory.value.slice(0, maxHistoryLength);
    }
  };

  /**
   * 清除历史记录
   */
  const clearHistory = () => {
    panelHistory.value = [];
    console.log("Panel history cleared");
  };

  // ==================== 右侧面板控制方法 ====================

  /**
   * 显示右侧面板
   * 替代原来的 change/right/show 事件
   */
  const showRightPanel = () => {
    rightPanelControl.value.visible = true;
    rightPanelControl.value.showEvent = true;
    setTimeout(() => {
      rightPanelControl.value.showEvent = false;
    }, 100);
    console.log("Right panel shown");
  };

  /**
   * 隐藏右侧面板
   * 替代原来的 change/right/hide 事件
   */
  const hideRightPanel = () => {
    rightPanelControl.value.visible = false;
    rightPanelControl.value.hideEvent = true;
    setTimeout(() => {
      rightPanelControl.value.hideEvent = false;
    }, 100);
    console.log("Right panel hidden");
  };

  /**
   * 切换到指定标签页
   * 替代原来的 gotoTab 事件
   */
  const gotoTab = (tabId) => {
    rightPanelControl.value.currentTab = tabId;
    console.log("Switched to tab:", tabId);
  };

  /**
   * 重置所有面板状态
   */
  const resetPanelState = () => {
    hideAllPanels();
    clearHistory();
    rightPanelControl.value = {
      visible: true,
      height: "auto",
      currentTab: 0,
      showEvent: false,
      hideEvent: false,
    };
    console.log("Panel state reset");
  };

  // ==================== 返回 ====================
  return {
    // 状态
    mainPanel,
    comparisonPanel,
    detailPanel,
    analysisPanel,
    schemePanel,
    agentSchemePanel,
    bubblePanel,
    panelHistory,
    resultPanelEvent,
    dispatchSelection,
    rightPanelControl,

    // 计算属性
    hasVisiblePanels,
    visiblePanelCount,
    isAnyPanelLoading,
    activePanelTypes,

    // 方法
    showMainPanel,
    showComparisonPanel,
    showDetailPanel,
    showAnalysisPanel,
    showSchemePanel,
    showAgentSchemePanel,
    showResultPanel,
    selectPrimaryDispatch,
    selectSecondaryDispatch,
    hideMainPanel,
    hideComparisonPanel,
    hideDetailPanel,
    hideAnalysisPanel,
    hideSchemePanel,
    hideAgentSchemePanel,
    hideAllPanels,
    showBubblePanel,
    hideBubblePanel,
    setPanelLoading,
    updatePanelData,
    setAnalysisResults,
    setSelectedSchemes,
    addToHistory,
    clearHistory,
    resetPanelState,

    // 右侧面板控制方法
    showRightPanel,
    hideRightPanel,
    gotoTab,
  };
});

export default useMapPanelStore;
