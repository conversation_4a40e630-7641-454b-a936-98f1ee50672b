import { defineStore } from "pinia";
import { ref, computed } from "vue";

/**
 * 地图UI状态管理
 * 管理面板、侧边栏、模块切换等UI相关状态
 */
const useMapUIStore = defineStore("mapUI", () => {
  // ==================== 状态定义 ====================

  // 侧边栏状态
  const sidebar = ref({
    visible: true, // 是否显示
    collapsed: false, // 是否折叠
    width: 260, // 展开宽度
    collapsedWidth: 180, // 折叠宽度
  });

  // 右侧面板状态
  const rightPanel = ref({
    height: "95%", // 'auto' | '95%'
    visible: true,
    collapsed: false,
  });

  // 当前模块状态
  const currentModule = ref({
    id: null,
    name: "",
    type: "",
    data: null,
  });

  // 各种UI控件状态
  const uiControls = ref({
    colorBandVisible: false, // 色带显示
    navigationVisible: false, // 导航显示
    layerControlVisible: true, // 图层控制显示
    searchToolVisible: true, // 搜索工具显示
    analysisToolVisible: false, // 分析工具显示
    cloudLayerVisible: false, // 气象云图显示
    radarLayerVisible: false, // 雷达回波显示
    rainContourVisible: false, // 雨量等值面显示
  });

  // 气象相关状态
  const meteorological = ref({
    cloudPlayType: null, // 气象播放类型: 1-云图, 2-雷达, 3-雨量格点, 4-预警
    isPlaying: false, // 是否正在播放
    currentFrame: 0, // 当前帧
    totalFrames: 0, // 总帧数
    playSpeed: 1000, // 播放速度(ms)
    contourPoints: "", // 等值面点数据
    cloudLayerVisible: false, // 云图图层可见性
    floodData: [], // 洪水预警数据
  });

  // 防洪预演相关状态
  const floodRehearsal = ref({
    playType: null, // 播放类型
    isPlaying: false, // 是否正在播放
    currentFrame: 0, // 当前帧
    totalFrames: 0, // 总帧数
    playSpeed: 1000, // 播放速度(ms)
    visible: false, // 是否可见
    data: null, // 预演数据
  });

  // 视图播放控制状态
  const viewPlayControl = ref({
    isPlaying: false, // 是否正在播放视图
    isPaused: false, // 是否暂停
    playSpeed: 1000, // 播放速度
    currentTime: 0, // 当前播放时间
  });

  // 加载状态
  const loadingStates = ref({
    map: false,
    data: false,
    panel: false,
    module: false,
  });

  // 面板可见性状态
  const panelVisibility = ref({
    rain: false, // 雨量面板
    river: false, // 河流面板
    reservoir: false, // 水库面板
    meteorological: false, // 气象面板
    scheme: false, // 方案面板
    agentScheme: false, // 智能方案面板
    analysis: false, // 分析面板
  });

  // 面板数据状态
  const panelData = ref({
    rain: null, // 雨量面板数据
    river: null, // 河流面板数据
    reservoir: null, // 水库面板数据
    meteorological: null, // 气象面板数据
    overview: null, // 概览面板数据
  });

  // 右侧标签页状态
  const rightTabs = ref({
    current: 4, // 当前标签页 ID
    tabs: [
      { id: 0, name: "概览", type: "overview" },
      { id: 1, name: "雨情", type: "rain" },
      { id: 2, name: "水情", type: "river" },
      { id: 3, name: "库情", type: "reservoir" },
      { id: 4, name: "气象", type: "meteorological" },
    ],
  });

  // ==================== 计算属性 ====================

  // 是否为对比分析模式
  const isComparisonMode = computed(() => {
    return currentModule.value.name === "对比分析";
  });

  // 是否有活跃模块
  const hasActiveModule = computed(() => {
    return currentModule.value.id !== null;
  });

  // 是否有任何加载状态
  const isAnyLoading = computed(() => {
    return Object.values(loadingStates.value).some(Boolean);
  });

  // 可见面板数量
  const visiblePanelCount = computed(() => {
    return Object.values(panelVisibility.value).filter(Boolean).length;
  });

  // 侧边栏实际宽度
  const sidebarActualWidth = computed(() => {
    if (!sidebar.value.visible) return 0;
    return sidebar.value.collapsed
      ? sidebar.value.collapsedWidth
      : sidebar.value.width;
  });

  // ==================== Actions ====================

  /**
   * 切换侧边栏显示
   * 替代原来的 sliderHide/sliderShow 事件
   */
  const toggleSidebar = () => {
    sidebar.value.visible = !sidebar.value.visible;
    console.log("Sidebar toggled:", sidebar.value.visible ? "shown" : "hidden");
  };

  /**
   * 设置侧边栏显示状态
   */
  const setSidebarVisible = (visible) => {
    sidebar.value.visible = visible;
    console.log("Sidebar visibility set to:", visible);
  };

  /**
   * 切换侧边栏折叠状态
   */
  const toggleSidebarCollapsed = () => {
    sidebar.value.collapsed = !sidebar.value.collapsed;
    console.log("Sidebar collapsed:", sidebar.value.collapsed);
  };

  /**
   * 设置侧边栏折叠状态
   */
  const setSidebarCollapsed = (collapsed) => {
    sidebar.value.collapsed = collapsed;
  };

  /**
   * 设置右侧面板高度
   * 替代原来的 change/right/height/max 和 change/right/height/min 事件
   */
  const setRightPanelHeight = (height) => {
    rightPanel.value.height = height;
    console.log("Right panel height set to:", height);
  };

  /**
   * 最大化右侧面板
   */
  const maximizeRightPanel = () => {
    setRightPanelHeight("95%");
  };

  /**
   * 最小化右侧面板
   */
  const minimizeRightPanel = () => {
    setRightPanelHeight("auto");
  };

  /**
   * 设置当前模块
   * 替代原来的 module/type/change 事件
   */
  const setCurrentModule = (moduleData) => {
    currentModule.value = {
      id: moduleData.id || null,
      name: moduleData.name || "",
      type: moduleData.type || "",
      data: moduleData.data || null,
    };
    console.log("Current module set to:", currentModule.value);
  };

  /**
   * 清除当前模块
   */
  const clearCurrentModule = () => {
    currentModule.value = {
      id: null,
      name: "",
      type: "",
      data: null,
    };
  };

  /**
   * 设置色带显示状态
   * 替代原来的 colorBandShow/update 事件
   */
  const setColorBandVisible = (visible) => {
    uiControls.value.colorBandVisible = visible;
    console.log("Color band visibility set to:", visible);
  };

  /**
   * 设置导航显示状态
   * 替代原来的 Navigation/update 事件
   */
  const setNavigationVisible = (visible) => {
    uiControls.value.navigationVisible = visible;
    console.log("Navigation visibility set to:", visible);
  };

  /**
   * 设置UI控件状态
   */
  const setUIControl = (controlName, value) => {
    if (controlName in uiControls.value) {
      uiControls.value[controlName] = value;
      console.log(`UI control ${controlName} set to:`, value);
    }
  };

  /**
   * 批量设置UI控件状态
   */
  const setUIControls = (controls) => {
    Object.keys(controls).forEach((key) => {
      if (key in uiControls.value) {
        uiControls.value[key] = controls[key];
      }
    });
    console.log("UI controls updated:", controls);
  };

  /**
   * 设置加载状态
   */
  const setLoading = (loadingType, loading) => {
    if (loadingType in loadingStates.value) {
      loadingStates.value[loadingType] = loading;
      console.log(`Loading state ${loadingType} set to:`, loading);
    }
  };

  /**
   * 设置面板可见性
   */
  const setPanelVisible = (panelName, visible) => {
    if (panelName in panelVisibility.value) {
      panelVisibility.value[panelName] = visible;
      console.log(`Panel ${panelName} visibility set to:`, visible);
    }
  };

  /**
   * 显示指定面板
   * 替代原来的 rainPanel/visible, riverPanel/visible 等事件
   */
  const showPanel = (panelName, data = null) => {
    setPanelVisible(panelName, true);
    if (data) {
      // 可以在这里处理面板数据
      console.log(`Panel ${panelName} shown with data:`, data);
    }
  };

  /**
   * 隐藏指定面板
   */
  const hidePanel = (panelName) => {
    setPanelVisible(panelName, false);
  };

  /**
   * 隐藏所有面板
   */
  const hideAllPanels = () => {
    Object.keys(panelVisibility.value).forEach((key) => {
      panelVisibility.value[key] = false;
    });
    console.log("All panels hidden");
  };

  /**
   * 切换面板显示状态
   */
  const togglePanel = (panelName) => {
    if (panelName in panelVisibility.value) {
      panelVisibility.value[panelName] = !panelVisibility.value[panelName];
      console.log(
        `Panel ${panelName} toggled to:`,
        panelVisibility.value[panelName]
      );
    }
  };

  /**
   * 设置气象云图显示状态
   * 替代原来的 layerCloud/visible 事件
   */
  const setCloudLayerVisible = (visible) => {
    uiControls.value.cloudLayerVisible = visible;
    if (visible) {
      meteorological.value.cloudPlayType =
        meteorological.value.cloudPlayType || 1;
    }
    console.log("Cloud layer visibility set to:", visible);
  };

  /**
   * 设置雷达回波显示状态
   */
  const setRadarLayerVisible = (visible) => {
    uiControls.value.radarLayerVisible = visible;
    if (visible) {
      meteorological.value.cloudPlayType = 2;
    }
    console.log("Radar layer visibility set to:", visible);
  };

  /**
   * 设置雨量等值面显示状态
   * 替代原来的 rainContour/visible 和 rainContour/clear 事件
   */
  const setRainContourVisible = (visible, points = "") => {
    uiControls.value.rainContourVisible = visible;
    if (visible && points) {
      meteorological.value.contourPoints = points;
    } else if (!visible) {
      meteorological.value.contourPoints = "";
    }
    console.log(
      "Rain contour visibility set to:",
      visible,
      points ? "with points" : ""
    );
  };

  /**
   * 设置气象播放类型
   * 替代原来的 cloudPlay/change 事件
   */
  const setCloudPlayType = (type) => {
    meteorological.value.cloudPlayType = type;

    // 根据类型设置相应的图层可见性
    if (type === 1) {
      setCloudLayerVisible(true);
      setRadarLayerVisible(false);
    } else if (type === 2) {
      setCloudLayerVisible(false);
      setRadarLayerVisible(true);
    } else if (type === null) {
      setCloudLayerVisible(false);
      setRadarLayerVisible(false);
    }

    console.log("Cloud play type set to:", type);
  };

  /**
   * 设置气象播放状态
   */
  const setMeteorologicalPlaying = (isPlaying) => {
    meteorological.value.isPlaying = isPlaying;
    console.log("Meteorological playing state set to:", isPlaying);
  };

  /**
   * 设置气象播放帧信息
   */
  const setMeteorologicalFrame = (currentFrame, totalFrames = null) => {
    meteorological.value.currentFrame = currentFrame;
    if (totalFrames !== null) {
      meteorological.value.totalFrames = totalFrames;
    }
  };

  /**
   * 设置云图图层可见性
   * 替代原来的 layerCloud/visible 事件
   */
  const setCloudLayerVisibility = (visible) => {
    meteorological.value.cloudLayerVisible = visible;
    console.log("Cloud layer visibility set to:", visible);
  };

  /**
   * 更新洪水预警数据
   * 替代原来的 update/flood/data 事件
   */
  const updateFloodData = (floodData) => {
    meteorological.value.floodData = floodData || [];
    console.log("Flood data updated:", floodData?.length || 0, "items");
  };

  /**
   * 设置防洪预演播放类型
   * 替代原来的 floodPreRehearsal/floodPlay/change 事件
   */
  const setFloodRehearsalType = (type) => {
    floodRehearsal.value.playType = type;
    floodRehearsal.value.visible = type !== null;
    console.log("Flood rehearsal type set to:", type);
  };

  /**
   * 设置防洪预演播放状态
   */
  const setFloodRehearsalPlaying = (isPlaying) => {
    floodRehearsal.value.isPlaying = isPlaying;
    console.log("Flood rehearsal playing state set to:", isPlaying);
  };

  /**
   * 设置防洪预演帧信息
   */
  const setFloodRehearsalFrame = (currentFrame, totalFrames = null) => {
    floodRehearsal.value.currentFrame = currentFrame;
    if (totalFrames !== null) {
      floodRehearsal.value.totalFrames = totalFrames;
    }
  };

  /**
   * 设置防洪预演数据
   */
  const setFloodRehearsalData = (data) => {
    floodRehearsal.value.data = data;
    console.log("Flood rehearsal data set:", data);
  };

  /**
   * 重置防洪预演状态
   */
  const resetFloodRehearsal = () => {
    floodRehearsal.value = {
      playType: null,
      isPlaying: false,
      currentFrame: 0,
      totalFrames: 0,
      playSpeed: 1000,
      visible: false,
      data: null,
    };
    console.log("Flood rehearsal state reset");
  };

  // ==================== 视图播放控制方法 ====================

  /**
   * 开始视图播放
   * 替代原来的 setViewPlay 事件
   */
  const startViewPlay = () => {
    viewPlayControl.value.isPlaying = true;
    viewPlayControl.value.isPaused = false;
    console.log("View play started");
  };

  /**
   * 暂停视图播放
   * 替代原来的 setViewPause 事件
   */
  const pauseViewPlay = () => {
    viewPlayControl.value.isPlaying = false;
    viewPlayControl.value.isPaused = true;
    console.log("View play paused");
  };

  /**
   * 停止视图播放
   */
  const stopViewPlay = () => {
    viewPlayControl.value.isPlaying = false;
    viewPlayControl.value.isPaused = false;
    viewPlayControl.value.currentTime = 0;
    console.log("View play stopped");
  };

  /**
   * 设置视图播放速度
   */
  const setViewPlaySpeed = (speed) => {
    viewPlayControl.value.playSpeed = speed;
    console.log("View play speed set to:", speed);
  };

  /**
   * 切换右侧标签页
   * 替代原来的 gotoTab 事件和 changeTab 方法
   */
  const setRightTab = (tabId) => {
    rightTabs.value.current = tabId;

    // 根据标签页类型设置相应的面板可见性和UI状态
    switch (tabId) {
      case 0: // 概览
        // 触发概览数据更新
        triggerOverviewUpdate();
        maximizeRightPanel();
        break;
      case 1: // 雨情
        showRainPanel();
        maximizeRightPanel();
        break;
      case 2: // 水情
        showRiverPanel();
        maximizeRightPanel();
        break;
      case 3: // 库情
        showReservoirPanel();
        maximizeRightPanel();
        break;
      case 4: // 气象
        setCloudLayerVisible(true);
        minimizeRightPanel();
        break;
      default:
        maximizeRightPanel();
    }

    console.log("Right tab set to:", tabId);
  };

  /**
   * 显示雨量面板
   * 替代原来的 rainPanel/visible 事件
   */
  const showRainPanel = (data = null) => {
    setPanelVisible("rain", true);
    if (data) {
      panelData.value.rain = data;
      console.log("Rain panel shown with data:", data);
    } else {
      console.log("Rain panel shown");
    }
  };

  /**
   * 显示河流面板
   * 替代原来的 riverPanel/visible 事件
   */
  const showRiverPanel = (data = null) => {
    setPanelVisible("river", true);
    if (data) {
      panelData.value.river = data;
      console.log("River panel shown with data:", data);
    } else {
      console.log("River panel shown");
    }
  };

  /**
   * 显示水库面板
   */
  const showReservoirPanel = (data = null) => {
    setPanelVisible("reservoir", true);
    if (data) {
      panelData.value.reservoir = data;
      console.log("Reservoir panel shown with data:", data);
    } else {
      console.log("Reservoir panel shown");
    }
  };

  /**
   * 触发概览数据更新
   * 替代原来的 overview/rain/update 事件
   */
  const triggerOverviewUpdate = () => {
    panelData.value.overview = { updateTime: Date.now() };
    console.log("Overview update triggered");
  };

  /**
   * 处理河流面板过滤
   * 替代原来的 riverPanel/handlerFilter 事件
   */
  const handleRiverPanelFilter = (filterData) => {
    if (panelData.value.river) {
      panelData.value.river = { ...panelData.value.river, filter: filterData };
    } else {
      panelData.value.river = { filter: filterData };
    }
    console.log("River panel filter applied:", filterData);
  };

  /**
   * 显示智能方案面板
   * 替代原来的 change/agent-scheme/show 事件
   */
  const showAgentSchemePanel = () => {
    panelVisibility.value.agentScheme = true;
    console.log("Agent scheme panel shown");
  };

  /**
   * 隐藏智能方案面板
   * 替代原来的 change/agent-scheme/hide 事件
   */
  const hideAgentSchemePanel = () => {
    panelVisibility.value.agentScheme = false;
    console.log("Agent scheme panel hidden");
  };

  /**
   * 显示右侧面板
   * 替代原来的 change/right/show 事件
   */
  const showRightPanel = () => {
    rightPanel.value.visible = true;
    console.log("Right panel shown");
  };

  /**
   * 隐藏右侧面板
   * 替代原来的 change/right/hide 事件
   */
  const hideRightPanel = () => {
    rightPanel.value.visible = false;
    console.log("Right panel hidden");
  };

  /**
   * 播放预警音频
   * 替代原来的 alarmWarn 事件
   */
  const playAlarmSound = () => {
    // 这里可以添加实际的音频播放逻辑
    // 例如：播放预警音频文件
    console.log("Playing alarm sound...");

    // 示例：如果有音频文件，可以这样播放
    // const audio = new Audio('/sounds/alarm.mp3')
    // audio.play().catch(err => {
    //   console.warn('Failed to play alarm sound:', err)
    // })
  };

  /**
   * 重置所有UI状态
   */
  const resetUIState = () => {
    sidebar.value = {
      visible: true,
      collapsed: false,
      width: 260,
      collapsedWidth: 180,
    };

    rightPanel.value = {
      height: "auto",
      visible: true,
      collapsed: false,
    };

    clearCurrentModule();

    uiControls.value = {
      colorBandVisible: false,
      navigationVisible: false,
      layerControlVisible: true,
      searchToolVisible: true,
      analysisToolVisible: false,
      cloudLayerVisible: false,
      radarLayerVisible: false,
      rainContourVisible: false,
    };

    meteorological.value = {
      cloudPlayType: null,
      isPlaying: false,
      currentFrame: 0,
      totalFrames: 0,
      playSpeed: 1000,
      contourPoints: "",
    };

    loadingStates.value = {
      map: false,
      data: false,
      panel: false,
      module: false,
    };

    hideAllPanels();

    console.log("UI state reset");
  };

  // ==================== 返回 ====================
  return {
    // 状态
    sidebar,
    rightPanel,
    currentModule,
    uiControls,
    loadingStates,
    panelVisibility,
    meteorological,
    floodRehearsal,
    viewPlayControl,
    panelData,
    rightTabs,

    // 计算属性
    isComparisonMode,
    hasActiveModule,
    isAnyLoading,
    visiblePanelCount,
    sidebarActualWidth,

    // 方法
    toggleSidebar,
    setSidebarVisible,
    toggleSidebarCollapsed,
    setSidebarCollapsed,
    setRightPanelHeight,
    maximizeRightPanel,
    minimizeRightPanel,
    setCurrentModule,
    clearCurrentModule,
    setColorBandVisible,
    setNavigationVisible,
    setUIControl,
    setUIControls,
    setLoading,
    setPanelVisible,
    showPanel,
    hidePanel,
    hideAllPanels,
    togglePanel,
    resetUIState,

    // 气象相关方法
    setCloudLayerVisible,
    setRadarLayerVisible,
    setRainContourVisible,
    setCloudPlayType,
    setMeteorologicalPlaying,
    setMeteorologicalFrame,
    setCloudLayerVisibility,
    updateFloodData,

    // 防洪预演控制方法
    setFloodRehearsalType,
    setFloodRehearsalPlaying,
    setFloodRehearsalFrame,
    setFloodRehearsalData,
    resetFloodRehearsal,

    // 视图播放控制方法
    startViewPlay,
    pauseViewPlay,
    stopViewPlay,
    setViewPlaySpeed,

    // 面板控制方法
    setRightTab,
    showRainPanel,
    showRiverPanel,
    showReservoirPanel,
    triggerOverviewUpdate,
    handleRiverPanelFilter,

    // 智能方案面板控制
    showAgentSchemePanel,
    hideAgentSchemePanel,

    // 右侧面板控制
    showRightPanel,
    hideRightPanel,

    // 预警音频方法
    playAlarmSound,

    // 重置方法
    resetUIState,
  };
});

export default useMapUIStore;
