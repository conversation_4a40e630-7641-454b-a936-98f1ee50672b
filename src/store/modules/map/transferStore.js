import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 避险转移状态管理
 * 管理避险转移相关的状态：风险点、安置点、转移路线等
 */
const useTransferStore = defineStore("transfer", () => {
  // ==================== 状态定义 ====================
  
  // 风险点状态
  const riskPoints = ref([])
  
  // 安置点状态
  const settlementPoints = ref([])
  
  // 当前选中的风险点
  const selectedRisk = ref({
    id: null,
    riskName: '',
    data: null,
    visible: false
  })
  
  // 转移路线状态
  const transferRoutes = ref([])
  
  // 转移人员统计
  const transferStats = ref({
    shouldTransferCount: 0,
    alreadyTransferCount: 0,
    noTransferCount: 0,
    totalCount: 0
  })
  
  // 转移面板状态
  const transferPanel = ref({
    visible: false,
    type: 'person', // 'person' | 'place' | 'route'
    data: null
  })
  
  // ==================== 计算属性 ====================
  
  /**
   * 是否有选中的风险点
   */
  const hasSelectedRisk = computed(() => {
    return selectedRisk.value.id !== null
  })
  
  /**
   * 转移完成率
   */
  const transferCompletionRate = computed(() => {
    const total = transferStats.value.totalCount
    if (total === 0) return 0
    return Math.round((transferStats.value.alreadyTransferCount / total) * 100)
  })
  
  /**
   * 风险点总数
   */
  const riskPointCount = computed(() => {
    return riskPoints.value.length
  })
  
  /**
   * 安置点总数
   */
  const settlementPointCount = computed(() => {
    return settlementPoints.value.length
  })
  
  // ==================== 方法 ====================
  
  /**
   * 显示风险点详情
   * 替代原来的 transfer/showRisk 事件
   */
  const showRiskDetail = (riskData) => {
    selectedRisk.value = {
      id: riskData.id,
      riskName: riskData.riskName,
      data: riskData,
      visible: true
    }
    
    // 显示转移面板
    transferPanel.value = {
      visible: true,
      type: 'person',
      data: riskData
    }
    
    console.log('Risk detail shown:', riskData)
  }
  
  /**
   * 隐藏风险点详情
   */
  const hideRiskDetail = () => {
    selectedRisk.value = {
      id: null,
      riskName: '',
      data: null,
      visible: false
    }
    
    transferPanel.value.visible = false
    console.log('Risk detail hidden')
  }
  
  /**
   * 设置风险点数据
   */
  const setRiskPoints = (points) => {
    riskPoints.value = points
    console.log('Risk points updated:', points.length)
  }
  
  /**
   * 添加风险点
   */
  const addRiskPoint = (point) => {
    riskPoints.value.push(point)
    console.log('Risk point added:', point)
  }
  
  /**
   * 移除风险点
   */
  const removeRiskPoint = (pointId) => {
    const index = riskPoints.value.findIndex(p => p.id === pointId)
    if (index > -1) {
      riskPoints.value.splice(index, 1)
      console.log('Risk point removed:', pointId)
    }
  }
  
  /**
   * 设置安置点数据
   */
  const setSettlementPoints = (points) => {
    settlementPoints.value = points
    console.log('Settlement points updated:', points.length)
  }
  
  /**
   * 添加安置点
   */
  const addSettlementPoint = (point) => {
    settlementPoints.value.push(point)
    console.log('Settlement point added:', point)
  }
  
  /**
   * 移除安置点
   */
  const removeSettlementPoint = (pointId) => {
    const index = settlementPoints.value.findIndex(p => p.id === pointId)
    if (index > -1) {
      settlementPoints.value.splice(index, 1)
      console.log('Settlement point removed:', pointId)
    }
  }
  
  /**
   * 设置转移路线
   */
  const setTransferRoutes = (routes) => {
    transferRoutes.value = routes
    console.log('Transfer routes updated:', routes.length)
  }
  
  /**
   * 添加转移路线
   */
  const addTransferRoute = (route) => {
    transferRoutes.value.push(route)
    console.log('Transfer route added:', route)
  }
  
  /**
   * 移除转移路线
   */
  const removeTransferRoute = (routeId) => {
    const index = transferRoutes.value.findIndex(r => r.id === routeId)
    if (index > -1) {
      transferRoutes.value.splice(index, 1)
      console.log('Transfer route removed:', routeId)
    }
  }
  
  /**
   * 更新转移统计数据
   */
  const updateTransferStats = (stats) => {
    transferStats.value = {
      ...transferStats.value,
      ...stats,
      totalCount: (stats.shouldTransferCount || 0) + (stats.alreadyTransferCount || 0) + (stats.noTransferCount || 0)
    }
    console.log('Transfer stats updated:', transferStats.value)
  }
  
  /**
   * 设置转移面板状态
   */
  const setTransferPanel = (visible, type = 'person', data = null) => {
    transferPanel.value = {
      visible,
      type,
      data
    }
    console.log('Transfer panel state set:', { visible, type, data })
  }
  
  /**
   * 显示转移面板
   */
  const showTransferPanel = (type = 'person', data = null) => {
    setTransferPanel(true, type, data)
  }
  
  /**
   * 隐藏转移面板
   */
  const hideTransferPanel = () => {
    setTransferPanel(false)
  }
  
  /**
   * 重置所有转移状态
   */
  const resetTransferState = () => {
    riskPoints.value = []
    settlementPoints.value = []
    transferRoutes.value = []
    selectedRisk.value = {
      id: null,
      riskName: '',
      data: null,
      visible: false
    }
    transferStats.value = {
      shouldTransferCount: 0,
      alreadyTransferCount: 0,
      noTransferCount: 0,
      totalCount: 0
    }
    transferPanel.value = {
      visible: false,
      type: 'person',
      data: null
    }
    console.log('Transfer state reset')
  }
  
  // ==================== 返回 ====================
  return {
    // 状态
    riskPoints,
    settlementPoints,
    selectedRisk,
    transferRoutes,
    transferStats,
    transferPanel,
    
    // 计算属性
    hasSelectedRisk,
    transferCompletionRate,
    riskPointCount,
    settlementPointCount,
    
    // 方法
    showRiskDetail,
    hideRiskDetail,
    setRiskPoints,
    addRiskPoint,
    removeRiskPoint,
    setSettlementPoints,
    addSettlementPoint,
    removeSettlementPoint,
    setTransferRoutes,
    addTransferRoute,
    removeTransferRoute,
    updateTransferStats,
    setTransferPanel,
    showTransferPanel,
    hideTransferPanel,
    resetTransferState
  }
})

export default useTransferStore
