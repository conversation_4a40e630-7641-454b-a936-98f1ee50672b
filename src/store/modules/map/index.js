// 地图模块统一导出
export { default as useMapStore } from "./mapStore.js";
export { default as useMapDataStore } from "./dataStore.js";
export { default as useMapUIStore } from "./uiStore.js";
export { default as useMapPanelStore } from "./panelStore.js";
export { default as useTransferStore } from "./transferStore.js";
export { default as useGeoStore } from "./geoStore.js";
export { default as useLayerControlStore } from "./layerControlStore.js";

// 便于批量导入
export const mapStores = {
  useMapStore: () => import("./mapStore.js").then((m) => m.default),
  useMapDataStore: () => import("./dataStore.js").then((m) => m.default),
  useMapUIStore: () => import("./uiStore.js").then((m) => m.default),
  useMapPanelStore: () => import("./panelStore.js").then((m) => m.default),
  useTransferStore: () => import("./transferStore.js").then((m) => m.default),
  useGeoStore: () => import("./geoStore.js").then((m) => m.default),
  useLayerControlStore: () =>
    import("./layerControlStore.js").then((m) => m.default),
};
