import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken,getTenant,setTenant,removeTenant } from '@/utils/auth'
import defManAva from '@/assets/images/man-profile.png'
import defWomanAva from '@/assets/images/woman-profile.png'

const useUserStore = defineStore(
  'user',
  {
    state: () => ({
      token: getToken(),
      name: '',
      avatar: '',
      roles: [],
      permissions: [],
      tenantid: getTenant(),
      adcds:'',
      lyCode:'',
      tenantName:'',
      deptId: '',
    }),
    actions: {
      // 登录
      login(userInfo) {
        const username = userInfo.username.trim()
        const password = userInfo.password
        const code = userInfo.code
        const uuid = userInfo.uuid
        return new Promise((resolve, reject) => {
          login(username, password, code, uuid).then(res => {
            setToken(res.data.access_token)
            this.token = res.data.access_token
            if (res.data.tenant_id) {
              setTenant(res.data.tenant_id)
              this.tenantid = res.data.tenant_id
            }
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 获取用户信息
      getInfo() {
        return new Promise((resolve, reject) => {
          getInfo().then(res => {
            const user = res.user
            // 根据性别选择默认头像
            const defaultAvatar = user.sex === "1" ? defWomanAva : defManAva;
            const avatar = (user.avatar == "" || user.avatar == null) ? defaultAvatar : import.meta.env.VITE_APP_BASE_API + user.avatar;

            if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
              this.roles = res.roles
              this.permissions = res.permissions
            } else {
              this.roles = ['ROLE_DEFAULT']
            }
            this.name = user.userName
            this.avatar = avatar;
            this.deptId = user.deptId;
            if (res.tenantName) {
              this.adcds = res.tenantName.adcds;
              this.lyCode = res.tenantName.lyCode;
              this.tenantName = res.tenantName.tenantName;
            }
            resolve(res)
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 退出系统
      logOut() {
        return new Promise((resolve, reject) => {
          logout(this.token).then(() => {
            this.token = ''
            if (this.tenantid) {
              this.tenantid = ''
              removeTenant()
            }
            this.roles = []
            this.permissions = []
            removeToken()
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      }
    }
  })

export default useUserStore
