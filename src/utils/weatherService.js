import axios from "axios";

const WEATHER_API_KEY = "8c87cf73bbba45d79f486a0088fecf75"; // 替换为你的和风天气 API Key
const WEATHER_API_BASE_URL = "https://devapi.qweather.com/v7";

class WeatherService {
  constructor() {
    this.axiosInstance = axios.create({
      baseURL: WEATHER_API_BASE_URL,
      timeout: 5000,
    });
  }

  // 获取未来 3 天天气预报
  async getWeatherForecast(locationId) {
    try {
      const response = await this.axiosInstance.get("/weather/3d", {
        params: {
          location: locationId,
          key: WEATHER_API_KEY,
        },
      });
      return response.data;
    } catch (error) {
      console.error("获取天气预报失败:", error);
      throw error;
    }
  }

  // 获取天气图标
  getWeatherIcon(weatherCode) {
    const iconMap = {
      100: "sunny", // 晴
      101: "cloudy", // 多云
      102: "cloudy", // 少云
      103: "cloudy", // 晴间多云
      104: "cloudy", // 阴
      200: "windy", // 有风
      201: "windy", // 平静
      202: "windy", // 微风
      203: "windy", // 和风
      204: "windy", // 清风
      205: "windy", // 强风/劲风
      206: "windy", // 疾风
      207: "windy", // 大风
      208: "windy", // 烈风
      209: "windy", // 风暴
      210: "windy", // 狂爆风
      211: "windy", // 飓风
      212: "windy", // 龙卷风
      213: "windy", // 热带风暴
      300: "rain", // 阵雨
      301: "rain", // 强阵雨
      302: "rain", // 雷阵雨
      303: "rain", // 强雷阵雨
      304: "rain", // 雷阵雨伴有冰雹
      305: "rain", // 小雨
      306: "rain", // 中雨
      307: "rain", // 大雨
      308: "rain", // 极端降雨
      309: "rain", // 毛毛雨/细雨
      310: "rain", // 暴雨
      311: "rain", // 大暴雨
      312: "rain", // 特大暴雨
      313: "rain", // 冻雨
      400: "snow", // 小雪
      401: "snow", // 中雪
      402: "snow", // 大雪
      403: "snow", // 暴雪
      404: "snow", // 雨夹雪
      405: "snow", // 雨雪天气
      406: "snow", // 阵雨夹雪
      407: "snow", // 阵雪
      500: "fog", // 薄雾
      501: "fog", // 雾
      502: "fog", // 霾
      503: "fog", // 扬沙
      504: "fog", // 浮尘
      507: "fog", // 沙尘暴
      508: "fog", // 强沙尘暴
      900: "sunny", // 热
      901: "cold", // 冷
      999: "unknown", // 未知
    };

    return iconMap[weatherCode] || "unknown";
  }
}

export const weatherService = new WeatherService();
