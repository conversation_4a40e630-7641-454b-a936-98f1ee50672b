/**
 * 海康威视H5视频播放器工具类
 * 基于海康威视官方H5播放器SDK v2.5.1
 */

export class HikvisionPlayer {
  constructor(options = {}) {
    this.containerId = options.containerId;
    this.videoUrl = options.videoUrl;
    this.onError = options.onError || (() => {});
    this.onSuccess = options.onSuccess || (() => {});
    this.onLoading = options.onLoading || (() => {});

    // 播放器实例
    this.jsPlugin = null;
    this.currentWindowIndex = 0;
    this.isInitialized = false;
    this.isPlaying = false;

    // 配置参数
    this.config = {
      mode: 0, // 解码类型：0=普通模式; 1=高级模式
      PlayBackMode: 1, // 1：绝对时间正放; 3 绝对时间倒放
      keepDecoder: 0, // 0:回收解码资源; 1:不回收解码资源
      mseWorkerEnable: false, // 是否开启多线程解码
      bSupporDoubleClickFull: true, // 是否支持双击全屏
      iMaxSplit: 1, // 分屏播放，默认1
      iCurrentSplit: 1,
      ...options.config,
    };

    // 重试机制
    this.retryCount = 0;
    this.maxRetries = 3;
  }

  /**
   * 初始化播放器
   */
  async init() {
    try {
      console.log("初始化海康威视H5播放器:", this.videoUrl);
      this.onLoading(true);

      // 检查海康威视播放器是否已加载
      if (!this.checkPlayerAvailable()) {
        await this.loadPlayerScript();
      }

      // 创建播放器实例
      await this.createPlayer();
    } catch (error) {
      console.error("海康威视播放器初始化失败:", error);
      // 不使用硬编码错误代码，直接传递错误信息
      this.onError(`播放器初始化失败: ${error.message}`, null);
    }
  }

  /**
   * 检查播放器是否可用
   */
  checkPlayerAvailable() {
    return typeof window.JSPlugin === "function";
  }

  /**
   * 动态加载播放器脚本
   */
  loadPlayerScript() {
    return new Promise((resolve, reject) => {
      if (this.checkPlayerAvailable()) {
        resolve();
        return;
      }

      const script = document.createElement("script");
      script.src = "/hikvision/h5player.min.js";
      script.onload = () => {
        console.log("海康威视播放器脚本加载成功");
        // 等待一下确保全局函数可用
        setTimeout(() => {
          if (this.checkPlayerAvailable()) {
            resolve();
          } else {
            reject(new Error("播放器脚本加载后JSPlugin不可用"));
          }
        }, 100);
      };
      script.onerror = () => {
        reject(new Error("海康威视播放器脚本加载失败"));
      };

      document.head.appendChild(script);
    });
  }

  /**
   * 创建播放器实例
   */
  async createPlayer() {
    try {
      // 创建播放器容器
      const container = document.getElementById(this.containerId);
      if (!container) {
        throw new Error(`找不到容器元素: ${this.containerId}`);
      }

      // 清空容器
      container.innerHTML = "";

      // 创建JSPlugin实例
      this.jsPlugin = new window.JSPlugin({
        szId: this.containerId, // 父窗口id，需要英文字母开头
        szBasePath: "/hikvision/", // 与h5player.min.js的引用目录一致
        mseWorkerEnable: this.config.mseWorkerEnable, // 是否开启多线程解码
        bSupporDoubleClickFull: this.config.bSupporDoubleClickFull, // 是否支持双击全屏
        iMaxSplit: this.config.iMaxSplit, // 分屏播放
        iCurrentSplit: this.config.iCurrentSplit,
      });

      // 设置事件回调
      await this.setupEventCallbacks();

      this.isInitialized = true;
      console.log("海康威视播放器创建成功");

      // 开始播放
      await this.startPlay();
    } catch (error) {
      console.error("创建播放器失败:", error);
      throw error;
    }
  }

  /**
   * 获取错误消息
   */
  getErrorMessage(errorCode) {
    const errorMessages = {
      0x80004005: "一般性失败",
      0x0190004b: "取流失败，请检查网络连接或视频源",
      0x01900001: "用户名或密码错误",
      0x01900002: "权限不足",
      0x01900003: "设备不在线",
      0x01900004: "通道号错误",
      0x01900005: "资源不足",
      0x01900006: "设备忙",
      0x01900007: "时间段错误",
      0x01900008: "录像文件不存在",
      0x01900009: "用户被锁定",
      0x0190000a: "输入参数非法",
      0x0190000b: "设备断开连接",
      0x0190000c: "声卡模式错误",
      0x0190000d: "缓冲区太小",
      0x0190000e: "创建SOCKET出错",
      0x0190000f: "设置SOCKET出错",
      0x01900010: "个数达到最大",
      0x01900011: "用户不存在",
      0x01900012: "写FLASH出错",
      0x01900013: "升级失败",
      0x01900014: "密码输入格式不正确",
      0x01900015: "设备正在格式化硬盘",
      0x01900016: "设备功能不支持",
      0x01900017: "账号未激活",
      0x01900018: "密码不安全",
      0x01900019: "密码不匹配",
      0x0190001a: "账号已锁定",
      0x0190001b: "账号不在有效期",
      0x0190001c: "用户已达上限",
      0x0190001d: "用户已存在",
      0x0190001e: "发送数据失败",
      0x0190001f: "接收数据失败",
      0x01900020: "超时",
      0x01900021: "串口号错误",
      0x01900022: "报警输入号错误",
      0x01900023: "报警输出号错误",
      0x01900024: "硬盘号错误",
      0x01900025: "通道号错误",
      0x01900026: "窗口号错误",
      0x01900027: "连接到设备的客户端个数超过最大",
      0x01900028: "版本不匹配",
      0x01900029: "网络连接失败",
      0x0190002a: "向设备发送失败",
      0x0190002b: "从设备接收数据失败",
      0x0190002c: "预览时分辨率不支持",
      0x0190002d: "命令返回数据长度不正确",
      0x0190002e: "播放器或解码库加载失败",
      0x0190002f: "播放器初始化失败",
      0x01900030: "文件头不正确",
      0x01900031: "版本信息不正确",
      0x01900032: "文件信息不正确",
      0x01900033: "视频编码类型不正确",
      0x01900034: "音频编码类型不正确",
      0x01900035: "文件播放完毕",
      0x01900036: "调用播放库中某个函数失败",
      0x01900037: "登录设备失败",
      0x01900038: "注销设备失败",
      0x01900039: "设备不支持该功能",
      0x0190003a: "查找不到指定文件",
      0x0190003b: "搜索时间段内无录像文件",
      0x0190003c: "设备不支持该命令",
      0x0190003d: "播放出错",
      0x0190003e: "文件格式不正确",
      0x0190003f: "路径错误",
      0x01900040: "硬盘满",
      0x01900041: "硬盘出错",
      0x01900042: "声卡模式错误",
      0x01900043: "缓冲区太小",
      0x01900044: "创建SOCKET出错",
      0x01900045: "设置SOCKET出错",
      0x01900046: "个数达到最大",
      0x01900047: "用户不存在",
      0x01900048: "写FLASH出错",
      0x01900049: "升级失败",
      0x0190004a: "密码输入格式不正确",
      0x0190004b: "取流失败，请检查网络连接或视频源",
      0x0190004c: "设备功能不支持",
      0x0190004d: "账号未激活",
      0x0190004e: "密码不安全",
      0x0190004f: "密码不匹配",
    };

    // 如果是数字，转换为十六进制查找
    if (typeof errorCode === "number") {
      return errorMessages[errorCode];
    }

    // 如果是字符串，尝试解析为十六进制
    if (typeof errorCode === "string") {
      const hexCode = parseInt(errorCode.replace("0x", ""), 16);
      return errorMessages[hexCode];
    }

    return null;
  }

  /**
   * 设置事件回调
   */
  async setupEventCallbacks() {
    try {
      await this.jsPlugin.JS_SetWindowControlCallback({
        windowEventSelect: (index) => {
          this.currentWindowIndex = index;
          console.log("选择窗口:", index);
        },
        pluginErrorHandler: (index, iErrorCode, oError) => {
          // 格式化错误代码为十六进制
          const errorCodeHex = iErrorCode;
          const errorMessage =
            this.getErrorMessage(iErrorCode) || `${errorCodeHex || iErrorCode}`;
          this.onError(errorMessage, errorCodeHex);
        },
        windowEventOver: (index) => {
          // 鼠标移过回调
        },
        windowEventOut: (index) => {
          // 鼠标移出回调
        },
        windowEventUp: (index) => {
          // 鼠标mouseup事件回调
        },
        windowFullCcreenChange: (bFull) => {
          console.log("全屏切换:", bFull);
        },
        firstFrameDisplay: (index, iWidth, iHeight) => {
          console.log("首帧显示:", index, iWidth, iHeight);
          this.isPlaying = true;
          this.onLoading(false);
          this.onSuccess();
        },
        performanceLack: () => {
          console.warn("性能不足");
        },
        StreamEnd: (index) => {
          console.log("回放结束:", index);
        },
        InterruptStream: (iWndIndex, interruptTime) => {
          console.log("断流事件:", iWndIndex, interruptTime);
          // 断流事件不使用硬编码错误代码，提供更详细的信息
          this.onError(
            `视频流中断 (窗口: ${iWndIndex}, 时间: ${interruptTime})`,
            null
          );
        },
      });
    } catch (error) {
      console.error("设置事件回调失败:", error);
      throw error;
    }
  }

  /**
   * 开始播放
   */
  async startPlay() {
    try {
      if (!this.isInitialized || !this.jsPlugin) {
        throw new Error("播放器未初始化");
      }

      console.log("开始播放视频:", this.videoUrl);
      this.onLoading(true);

      // 使用海康威视播放器API播放
      const playConfig = {
        playURL: this.videoUrl, // 流媒体播放时必传
        mode: this.config.mode, // 解码类型：0=普通模式; 1=高级模式
        PlayBackMode: this.config.PlayBackMode, // 1：绝对时间正放; 3 绝对时间倒放
        keepDecoder: this.config.keepDecoder, // 0:回收解码资源; 1:不回收解码资源
      };

      const result = await this.jsPlugin.JS_Play(
        this.videoUrl,
        playConfig,
        this.currentWindowIndex
      );
    } catch (error) {
      this.onLoading(false);
    }
  }

  /**
   * 停止播放
   */
  async stop() {
    try {
      if (this.isPlaying && this.jsPlugin) {
        const result = await this.jsPlugin.JS_Stop(this.currentWindowIndex);
        console.log("停止播放结果:", result);
        this.isPlaying = false;
      }
    } catch (error) {
      console.error("停止播放失败:", error);
    }
  }

  /**
   * 开启声音
   */
  async openSound() {
    try {
      if (this.jsPlugin) {
        const result = await this.jsPlugin.JS_OpenSound(
          this.currentWindowIndex
        );
        console.log("开启声音结果:", result);
        return result;
      }
    } catch (error) {
      console.error("开启声音失败:", error);
    }
  }

  /**
   * 关闭声音
   */
  async closeSound() {
    try {
      if (this.jsPlugin) {
        const result = await this.jsPlugin.JS_CloseSound(
          this.currentWindowIndex
        );
        console.log("关闭声音结果:", result);
        return result;
      }
    } catch (error) {
      console.error("关闭声音失败:", error);
    }
  }

  /**
   * 设置音量
   */
  async setVolume(volume) {
    try {
      console.log("🚀 ~ ", this.currentWindowIndex);
      if (this.jsPlugin) {
        // 海康威视播放器设置音量 (1-100)
        const vol = Math.max(1, Math.min(100, volume));
        const result = await this.jsPlugin.JS_SetVolume(
          this.currentWindowIndex,
          vol
        );
        console.log("设置音量结果:", result, "音量:", vol);
        return result;
      }
    } catch (error) {
      console.error("设置音量失败:", error);
    }
  }

  /**
   * 获取当前音量
   */
  async getVolume() {
    try {
      if (this.jsPlugin) {
        const result = await this.jsPlugin.JS_GetVolume(
          this.currentWindowIndex
        );
        console.log("获取音量结果:", result);
        return result;
      }
    } catch (error) {
      console.error("获取音量失败:", error);
    }
  }

  /**
   * 整体全屏
   */
  async fullscreen(isFull = true) {
    try {
      if (this.jsPlugin) {
        const result = await this.jsPlugin.JS_FullScreenDisplay(isFull);
        console.log("全屏切换结果:", result);
        return result;
      }
    } catch (error) {
      console.error("全屏切换失败:", error);
    }
  }

  /**
   * 单窗口全屏
   */
  async fullscreenSingle() {
    try {
      if (this.jsPlugin) {
        const result = await this.jsPlugin.JS_FullScreenSingle(
          this.currentWindowIndex
        );
        console.log("单窗口全屏结果:", result);
        return result;
      }
    } catch (error) {
      console.error("单窗口全屏失败:", error);
    }
  }

  /**
   * 截图功能
   */
  async capture() {
    try {
      if (this.jsPlugin) {
        const fileName = `screenshot_${Date.now()}`;
        const fileType = 'JPEG';
        const result = await this.jsPlugin.JS_CapturePicture(
          this.currentWindowIndex,
          fileName,
          fileType
        );
        console.log("截图结果:", result);
        return result;
      }
      return false;
    } catch (error) {
      console.error("截图失败:", error);
      return false;
    }
  }

  /**
   * 销毁播放器
   */
  async destroy() {
    try {
      // 先停止播放
      if (this.isPlaying) {
        await this.stop();
      }

      // 停止所有播放
      if (this.jsPlugin) {
        try {
          await this.jsPlugin.JS_StopRealPlayAll();
          console.log("停止所有播放成功");
        } catch (error) {
          console.warn("停止所有播放失败:", error);
        }
      }

      // 清理容器
      const container = document.getElementById(this.containerId);
      if (container) {
        container.innerHTML = "";
      }

      // 重置状态
      this.jsPlugin = null;
      this.currentWindowIndex = 0;
      this.isInitialized = false;
      this.isPlaying = false;
      this.retryCount = 0;

      console.log("海康威视播放器销毁完成");
    } catch (error) {
      console.error("销毁播放器失败:", error);
    }
  }

  /**
   * 获取播放器状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isPlaying: this.isPlaying,
      currentWindowIndex: this.currentWindowIndex,
      retryCount: this.retryCount,
      hasJSPlugin: !!this.jsPlugin,
    };
  }

  /**
   * 获取视频信息
   */
  async getVideoInfo() {
    try {
      if (this.jsPlugin) {
        const result = await this.jsPlugin.JS_GetVideoInfo(
          this.currentWindowIndex
        );
        console.log("获取视频信息结果:", result);
        return result;
      }
    } catch (error) {
      console.error("获取视频信息失败:", error);
    }
  }

  /**
   * 设置窗口大小
   */
  async resize(width, height) {
    try {
      if (this.jsPlugin) {
        const result = await this.jsPlugin.JS_Resize(width, height);
        console.log("设置窗口大小结果:", result);
        return result;
      }
    } catch (error) {
      console.error("设置窗口大小失败:", error);
    }
  }
}
