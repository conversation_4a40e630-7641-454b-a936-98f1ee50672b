/*
 * @Description: 
 * @Author: liguiyuan
 * @LastEditors: liguiyuan
 */
export const sortColor = (waterData, Key, maxWaterValue, minWaterValue, colorBand) => {
    const key = Key || 'waterLevel';
    const max = maxWaterValue || 120;
    const min = minWaterValue || 0;
    const colorTag = averageWaterData(max, min);
    const allData = []
    const colorBandByRed = [
      "rgba(2, 255, 0, 0.5)", 
      "rgba(54, 255, 3, 0.5)", 
      "rgba(112, 255, 2, 0.5)",
      "rgba(170, 255, 1, 0.5)",
      "rgba(224, 255, 3, 0.5)",
      "rgba(255, 225, 0, 0.5)",
      "rgba(254, 170, 1, 0.5)",
      "rgba(254, 111, 0, 0.5)",
      "rgba(255, 55, 1, 0.5)",
      "rgba(255, 1, 0, 0.5)",
    ]
    const colorBandByBlue = [
      "rgba(3,255,213,0.7)",
      "rgba(2,255,234, 0.7)",
      "rgba(4,251,255, 0.7)",
      "rgba(0,229,255, 0.7)",
      "rgba(0,204,255, 0.7)",
      "rgba(1,183,255, 0.7)",
      "rgba(1,157,255, 0.7)",
      "rgba(3,136,255, 0.7)",
      "rgba(1,111,255, 0.7)",
      "rgba(0,88,254, 0.7)",
    ]
    const colorBandPrimary = colorBand || colorBandByRed
    colorTag.length > 0 && waterData.forEach(data => {
        //测试
        if(data[key] <= 0.01) {
          //去掉该数据
          data.color = "rgba(3,255,213,0)" //给一个透明值
          return 
        }
        if(data[key] <= colorTag[0]) {
          data.color = colorBandPrimary[0]
        } 
        else if( data[key] <= colorTag[1] &&  data[key] > colorTag[0]) {
          data.color = colorBandPrimary[1]
        } 
        else if(data[key] <= colorTag[2] &&  data[key] > colorTag[1]) {
           data.color = colorBandPrimary[2]
        } 
        else if(data[key] <= colorTag[3] &&  data[key] > colorTag[2]) {
          data.color = colorBandPrimary[3]
        } 
        else if(data[key] <= colorTag[4] &&  data[key] > colorTag[3]) {
          data.color = colorBandPrimary[4]
        } 
        else if(data[key] <= colorTag[5] &&  data[key] > colorTag[4]) {
          data.color = colorBandPrimary[5]
        } 
        else if(data[key] <= colorTag[6] &&  data[key] > colorTag[5]) {
          data.color = colorBandPrimary[6]
        } 
        else if(data[key] <= colorTag[7] &&  data[key] > colorTag[6]) {
          data.color = colorBandPrimary[7]
        } 
        else if(data[key] <= colorTag[8] &&  data[key] > colorTag[7]) {
          data.color = colorBandPrimary[8]
        } 
        else  {
          data.color = colorBandPrimary[9]
        }
      })
      if(!colorTag.length > 0) {
        return new Error('colorTag is empty!')
      }
      colorTag.forEach((value, index) => {
        
        colorBand.forEach((color, i ) => {
          i === index && allData.push({
            color,
            value
          })
        })
      
      })
      return {waterData, allData};
}
//平分10条颜色
export const averageWaterData = (max, min) => {
    const step = (max - min) / 10;
    const colorData = [];
    for(let i = 0; i <= 10; i++) {
      colorData.push(min + step * i);
    }
    //测试 
    if( colorData[0] === 0) {
      colorData.shift()
    }
    
    return colorData;
}
