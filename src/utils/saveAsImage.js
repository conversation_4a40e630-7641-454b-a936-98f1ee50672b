import html2canvas from "html2canvas";
import { ElMessage } from "element-plus";

/**
 * 保存图片
 * @param {string} dom - 图片的DOM
 */
export const saveDomAsImage = async (dom) => {
  try {
    if (!dom) return;
    // 配置html2canvas选项
    const options = {
      backgroundColor: "#ffffff",
      scale: 2, // 提高图片质量
      logging: false,
      useCORS: true, // 允许加载跨域图片
    };

    // 生成图片
    const canvas = await html2canvas(dom, options);

    // 转换为图片并下载
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const fileName = `chat-message-${timestamp}.png`;

    // 创建下载链接
    const link = document.createElement("a");
    link.download = fileName;
    link.href = canvas.toDataURL("image/png");
    link.click();

    // 显示成功提示
    ElMessage.success("图片已保存");
  } catch (error) {
    console.error("保存图片失败:", error);
    ElMessage.error("保存图片失败");
  }
};
