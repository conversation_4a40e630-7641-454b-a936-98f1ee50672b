import mitt from "mitt";

// 创建mitt实例
const emitter = mitt();

// 兼容旧版API的EventBus类
class EventBus {
  constructor() {
    this.emitter = emitter;
  }

  // 注册事件（兼容旧API）
  $on(eventName, handler) {
    this.emitter.on(eventName, handler);
  }

  // 触发事件（兼容旧API）
  $emit(eventName, handlerParams) {
    this.emitter.emit(eventName, handlerParams);
  }

  // 触发一次事件并移除（兼容旧API）
  $onece(eventName, handlerParams) {
    // 这里使用一次性触发的实现
    this.emitter.emit(eventName, handlerParams);
    this.emitter.off(eventName);
  }

  // 移除事件（兼容旧API）
  $remove(eventName, handler) {
    if (handler) {
      // 移除特定事件的特定处理函数
      this.emitter.off(eventName, handler);
    } else {
      // 移除特定事件的所有处理函数
      this.emitter.off(eventName);
    }
  }

  // 提供新的API方法，方便后续代码迁移

  // 注册事件
  on(eventName, handler) {
    this.emitter.on(eventName, handler);
  }

  // 移除事件
  off(eventName, handler) {
    this.emitter.off(eventName, handler);
  }

  // 触发事件
  emit(eventName, data) {
    this.emitter.emit(eventName, data);
  }

  // 获取原始mitt实例
  getEmitter() {
    return this.emitter;
  }
}

// 创建全局事件总线实例
export const eventBus = new EventBus();

// 导出原始mitt实例，可以自定义使用
export const mittInstance = emitter;

// 默认导出EventBus类
export default EventBus;
