function S4 () {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
}
export function getGuid () {
  return (S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4())
}
export function initguid () {
  return getGuid()
}

export function isValidIP (ip) {
  const reg = /^http:\/\/.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]).+.$/;
  return reg.test(ip)
}

// 格式化日期，如月、日、时、分、秒保证为2位数
function formatNumber (n) {
  n = n.toString()
  return n[1] ? n : '0' + n
}

// 参数number为毫秒时间戳，format为需要转换成的日期格式
export function formatTime (number, format) {
  let time = new Date(number)
  let newArr = []
  let formatArr = ['Y', 'M', 'D', 'h', 'm', 's']
  newArr.push(time.getFullYear())
  newArr.push(formatNumber(time.getMonth() + 1))
  newArr.push(formatNumber(time.getDate()))

  newArr.push(formatNumber(time.getHours()))
  newArr.push(formatNumber(time.getMinutes()))
  newArr.push(formatNumber(time.getSeconds()))

  for (let i in newArr) {
    format = format.replace(formatArr[i], newArr[i])
  }
  return format
}

export function dataSizeFun (limit) {
  let size;
  if( limit < 0.1 * 1024 ){
    size = limit.toFixed(2) + 'KB'
  } else if(limit < 0.1 * 1024 * 1024 ){
    size = (limit / 1024).toFixed(2) + 'MB'
  } else if(limit < 0.1 * 1024 * 1024 * 1024){
    size = (limit / (1024 * 1024)).toFixed(2) + 'GB'
  } else {
    size = (limit / (1024 * 1024 * 1024)).toFixed(2) + 'TB'
  }
  const sizestr = size + '';
  const len = sizestr.indexOf('.');
  const dec = sizestr.substr(len + 1, 2);
  if (dec === '00') {
    return sizestr.substring(0, len) + sizestr.substr(len + 3, 2)
  }
  return sizestr
}

export function defaultDate (flag, beginEndTime, date) {
  let orderDate = null
  if (date !== null) {
    if (flag === 'default' && beginEndTime === 'start' && date === '') {
      orderDate = new Date(new Date(new Date().setTime(new Date(new Date().setHours(0, 0, 0, 0)).getTime() - 3600 * 1000 * 24 * 30)).setHours(0, 0, 0, 0))
    } else if (flag === 'default' && beginEndTime === 'end' && date === '') {
      orderDate = new Date(new Date(new Date().setTime(new Date(new Date().setHours(0, 0, 0, 0)).getTime() + 60000)).setHours(23, 59, 59, 59))
    } else if (flag === 'notDefault' && beginEndTime === 'start' && date !== '') {
      orderDate = new Date(new Date(new Date().setTime(new Date(new Date(date).setHours(0, 0, 0, 0)).getTime())).setHours(0, 0, 0, 0))
    } else if (flag === 'notDefault' && beginEndTime === 'end' && date !== '') {
      orderDate = new Date(new Date(new Date().setTime(new Date(new Date(date).setHours(0, 0, 0, 0)).getTime())).setHours(23, 59, 59, 59))
    }
  }else{
    if (beginEndTime === 'start') {
      orderDate = new Date(1970, 0, 1, 8)
    }else{
      orderDate = new Date(new Date(new Date().setTime(new Date(new Date().setHours(0, 0, 0, 0)).getTime() + 6000000000000)).setHours(23, 59, 59, 59))
    }
  }
  return orderDate
}

export function isJSON(str) {
  if (typeof str == 'string') {
      try {
        const obj = JSON.parse(str);
        return !!(typeof obj == 'object' && obj);

      } catch(e) {
          console.log('error：'+str+'!!!'+e);
          return false;
      }
  }
  console.log('It is not a string!')
}
// 因为数据不是最新的，把这个作为一个方法使用
// 获取当前8点的数据 - 年份会改成2022-10-01
export function getCurrent8Time () {
  // return '2022-10-01 00:00:00'

  let time = getRainTodayTime()
  return time[0]
}
// 获取雨情的今日时间
export function getRainTodayTime () {
  //默认是八点到当前时间，早8点到现在 ,如果没到8点那就是近24小时
  let nowdate = new Date();
  let _stm = new Date();
  if (nowdate.getHours() >= 8) {
    _stm = new Date(nowdate.getFullYear(), nowdate.getMonth(), nowdate.getDate(), 8);
  } else {
    _stm.setTime(nowdate.getTime() - (1000 * 60 * 60 * 24));
  }
  let _etm = new Date(nowdate.getFullYear(), nowdate.getMonth(), nowdate.getDate(), nowdate.getHours(), nowdate.getMinutes());
  return [_stm, _etm]
  //
  // return [new Date('2022-10-01 00:00:00'), _etm]
}
// 获取雨情的昨天时间
export function getRainYesterdayTime () {
  //默认是昨天八点到早8点，如果没到8点那就是到当前时间
  let nowdate = new Date();
  let _etm = new Date();
  let _stm;
  if (nowdate.getHours() >= 8) {
    _etm = new Date(nowdate.getFullYear(), nowdate.getMonth(), nowdate.getDate(), 8);
  }
  // 获取昨天的时间
  _stm = new Date(nowdate.getFullYear(), nowdate.getMonth(), nowdate.getDate(), 8);
  _stm = new Date(_stm.getTime() - (24 * 60 * 60 * 1000));
  return [_stm, _etm]
}
export function getHourTime(hour) {
  hour = Number(hour)
  // 获取当前时间
  const now = new Date();
  // 获取开始时间
  const startTime = new Date(now.getTime() - (hour * 60 * 60 * 1000));
  // 返回开始时间和当前时间
  return [startTime, now];
}
export function getRainTimeByType(type) {
  if(type === 'today') {
    return getRainTodayTime()
  } else if(type === 'yesterday') {
    return getRainYesterdayTime()
  } else {
    return getHourTime(type)
  }
}
// 请求拦截器里面时间处理
export function handlerConfigTimes (data) {
  let bgtm = data['bgtm']
  // 判断如果大于2022 那么就不变,否则改成2023
  if(new Date(bgtm).getFullYear()>2022){
    data['bgtm'] =  bgtm.replace( new Date(bgtm).getFullYear() +'','2022')
  }else {
  }
  let endtm = data['endtm']
  // 判断如果大于2022 那么就不变,否则改成2023
  if(new Date(endtm).getFullYear()>2022){
    data['endtm'] =  endtm.replace( new Date(endtm).getFullYear() +'','2022')
  }else {

  }
}
