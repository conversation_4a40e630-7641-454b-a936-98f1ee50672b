import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'
const TenantKey = 'Admin-Tenant'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
export function getTenant() {
  return Cookies.get(TenantKey)
}
export function setTenant(tenantid) {
  return Cookies.set(Tenant<PERSON><PERSON>, tenantid)
}
export function removeTenant() {
  return Cookies.remove(TenantKey)
}