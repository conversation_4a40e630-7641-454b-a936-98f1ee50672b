<template>
  <div
    :class="{ 'has-logo': showLogo }"
    class="sidebar-container"
    :style="{
      backgroundColor: '#ffffff',
      width: isCollapse ? '64px' : '260px',
    }"
  >
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        class="leftMenu"
        :collapse="isCollapse"
        background-color="#ffffff"
        text-color="#333"
        :unique-opened="true"
        active-text-color="#1478F0"
        collapse-transition
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
    <div class="sidebar-bg"></div>
    <div class="hamburger-container">
      <hamburger
        id="hamburger-container"
        :is-active="appStore.sidebar.opened"
        @toggleClick="toggleSideBar"
      />
    </div>
  </div>
</template>

<script setup>
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.module.scss";
import useAppStore from "@/store/modules/app";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";
import Hamburger from "@/components/Hamburger";

const route = useRoute();
const appStore = useAppStore();
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();
function toggleSideBar() {
  appStore.toggleSideBar();
}

const sidebarRouters = computed(() => permissionStore.sidebarRouters);
const showLogo = computed(() => settingsStore.sidebarLogo);
const sideTheme = computed(() => settingsStore.sideTheme);
const theme = computed(() => settingsStore.theme);
const isCollapse = computed(() => !appStore.sidebar.opened);

const activeMenu = computed(() => {
  const { meta, path } = route;
  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});
</script>

<style lang="scss" scoped>
.hamburger-container {
  line-height: 40px;
  height: 40px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  // background: rgba(20, 120, 240, 0.05);
  position: absolute;
  bottom: 0;
  width: 100%;
  // border-top: 1px solid #edf2f7;

  &:hover {
    color: #1478f0;
    // background: rgba(20, 120, 240, 0.1);
  }
}
</style>

<style lang="scss">
.leftMenu {
  height: 100%;
  width: 100% !important;
  background: transparent;

  &.el-menu--collapse {
    .el-menu-item,
    .el-sub-menu__title {
      margin: 10px 0 !important;
    }
  }

  .el-menu-item,
  .el-sub-menu__title {
    height: 40px;
    line-height: 40px;
    border-radius: 30px;
    margin: 10px 10px;
    padding: 0 15px !important;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    color: #333;
    letter-spacing: 0.2px;

    .svg-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }

    &:hover {
      background-color: #0e6ce3 !important;
      color: #fff !important;

      .svg-icon {
        defs {
          stop {
            stop-color: #fff;
          }
        }
      }
    }
  }

  .el-menu-item.is-active {
    position: relative;
    background-color: #0e6ce3;
    color: #fff;
    border-radius: 20px;
    font-weight: 500;
    box-shadow: none;

    .svg-icon {
      defs {
        stop {
          stop-color: #fff;
        }
      }
    }

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 22px;
      background: #1478f0;
      border-radius: 0 3px 3px 0;
    }
  }

  .el-sub-menu {
    .el-sub-menu__title {
      &:hover {
        background-color: rgba(20, 120, 240, 0.04) !important;
        color: #1478f0 !important;
      }
    }

    .el-menu {
      padding: 0 0 0 10px;
      background: transparent !important;
    }
  }

  .svg-icon {
    margin-right: 12px;
    font-size: 18px;
    vertical-align: middle;
    transition: color 0.25s;
  }

  .menu-title {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
    transition: all 0.25s;
    font-size: 14px;
  }
}

.el-menu--collapse {
  .el-menu-item,
  .el-sub-menu__title {
    margin: 3px 2px !important;
  }

  .el-menu-item {
    text-align: center;
    padding-left: 0 !important;
    padding-right: 0 !important;

    .svg-icon {
      margin: 0;
    }
  }

  .el-sub-menu {
    & > .el-sub-menu__title {
      text-align: center;
      padding-left: 0 !important;
      padding-right: 0 !important;

      .svg-icon {
        margin: 0;
      }

      .el-sub-menu__icon-arrow {
        display: none;
      }
    }
  }
}
</style>
