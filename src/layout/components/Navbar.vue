<template>
  <div class="navbar">
    <!-- <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container"
      @toggleClick="toggleSideBar" /> -->
    <!-- <div class="logo">拉萨市智慧水利系统</div> -->
    <div class="header-right">
      <div class="header-right-logo" />
      <div class="header-right-title" />
    </div>
    <breadcrumb
      id="breadcrumb-container"
      class="breadcrumb-container"
      v-if="!settingsStore.topNav"
    />
    <top-nav
      id="topmenu-container"
      class="topmenu-container"
      v-if="settingsStore.topNav"
    />

    <div class="right-menu">
      <!-- <template v-if="appStore.device !== 'mobile'">
        <header-search id="header-search" class="right-menu-item" />

        <el-tooltip content="源码地址" effect="dark" placement="bottom">
          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
        </el-tooltip>

        <el-tooltip content="文档地址" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip>

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template> -->
      <div class="avatar-container">
        <el-dropdown
          @command="handleCommand"
          class="right-menu-item hover-effect"
          trigger="click"
        >
          <div class="avatar-wrapper">
            <img :src="userStore.avatar" class="user-avatar" />
            <span class="username-text">{{ userStore.name }}</span>
            <el-icon><caret-bottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
              <!-- <el-dropdown-item command="setLayout">
                <span>布局设置</span>
              </el-dropdown-item> -->
              <el-dropdown-item divided command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus";
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from "@/components/TopNav";
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";

const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();

function toggleSideBar() {
  appStore.toggleSideBar();
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        location.href = "/index";
      });
    })
    .catch(() => {});
}

const emits = defineEmits(["setLayout"]);
function setLayout() {
  emits("setLayout");
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .header-right {
    display: flex;
    align-items: center;
    max-width: 490px;
    width: 490px;
    // width: auto;
    min-width: 200px;
    height: 100%;
    flex-shrink: 0;

    .header-right-logo {
      width: 40px;
      height: 40px;
      background: url("@/assets/images/header-logo.png") no-repeat center center;
      margin: 0 14px 0 24px;
      flex-shrink: 0;
    }

    .header-right-title {
      width: 315px;
      height: 100%;
      background: url("@/assets/images/header-title.png") no-repeat center
        center;
      background-size: contain;
    }
  }

  .logo {
    width: 300px;
    height: 50px;
    line-height: 49px;
    font-size: 26px;
    text-align: center;
    position: absolute;
    color: #fff;
    top: 0;
    left: 0;
    background: url("@/assets/images/header-title.png") no-repeat center center;
    // background-size: 100% 100%;
  }

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    flex: 1;
    min-width: 0;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .topmenu-container {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .right-menu {
    max-width: 200px;
    min-width: 120px;
    width: auto;
    height: 100%;
    line-height: 60px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #333;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      padding: 0 10px;
      transition: all 0.3s;
      height: 100%;
      display: flex;
      align-items: center;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      .avatar-wrapper {
        margin-top: 0;
        position: relative;
        color: #333;
        display: flex;
        align-items: center;
        gap: 8px;
        height: 100%;

        .user-avatar {
          cursor: pointer;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: 2px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;
          flex-shrink: 0;

          &:hover {
            border-color: rgba(255, 255, 255, 0.4);
            transform: scale(1.05);
          }
        }

        .username-text {
          font-size: 16px;
          font-weight: 500;
          max-width: 80px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: 2;

          @media screen and (max-width: 768px) {
            max-width: 60px;
          }

          @media screen and (max-width: 576px) {
            display: none;
          }
        }

        i {
          cursor: pointer;
          font-size: 12px;
          transition: transform 0.3s ease;
          margin-left: 4px;
        }
      }
    }
  }
}

@media screen and (max-width: 992px) {
  .navbar {
    .header-right {
      max-width: 300px;
    }
  }
}

@media screen and (max-width: 768px) {
  .navbar {
    .header-right {
      max-width: 180px;

      .header-right-title {
        max-width: 120px;
      }
    }
  }
}

@media screen and (max-width: 576px) {
  .navbar {
    .header-right-title {
      display: none;
    }

    .header-right {
      max-width: 80px;
    }
  }
}
</style>
