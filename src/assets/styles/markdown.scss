@use "markdown-variables" as *;
@use "markdown-mixins" as *;
@use "sass:math";

.markdown-body {
  background-color: transparent;

  img {
    max-width: 35%;
  }

  :deep(pre) {
    font-size: 90%;
    padding: 0;
    // border-radius: 1rem;
  }

  :deep(.hljs) {
    padding: 1rem;
    padding-bottom: 0;
  }

  :deep(.copy-btn) {
    position: absolute;
    right: $spacing-sm;
    top: 3px;
    z-index: $zindex-tooltip;
    color: $text-color;
    padding: calc($spacing-sm / 2) $spacing-sm;
    cursor: pointer;
    background-color: $background-color;
    border: 1px solid $border-color;
    border-radius: $border-radius-base;
    font-size: $font-size-sm;
    @include transition;
    opacity: 0;

    &:hover {
      background-color: #f5f5f5;
      border-color: #d0d0d0;
      color: $text-color;
    }

    &:active {
      background-color: #ebecef;
    }
  }

  :deep(pre:hover .copy-btn) {
    opacity: 1;
  }

  :deep(.code-header) {
    @include flex-between;
    background: #e5e7ed;
    padding: $spacing-sm $spacing-md;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial,
      sans-serif;
    position: relative;
  }

  :deep(.code-lang) {
    font-size: $font-size-sm;
    font-weight: 600;
    color: #57606a;
    text-transform: lowercase;
  }

  :deep(.copy-text) {
    font-size: $font-size-sm;
  }
}
