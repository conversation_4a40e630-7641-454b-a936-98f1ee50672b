@use "markdown-variables" as *;
@use "markdown-mixins" as *;
@use "sass:math";

%loading-base {
  @include flex-center;
  flex-direction: column;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  @include transition;
}

%loading-dots {
  display: flex;
  gap: calc($spacing-sm / 2);
  margin-bottom: $spacing-sm;

  span {
    width: $spacing-sm;
    height: $spacing-sm;
    background: $primary-color;
    border-radius: 50%;
    animation: bounce 1.4s infinite ease-in-out;
    opacity: 0.6;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }
}

.loading-dots {
  @extend %loading-dots;
}

.chart-loading {
  @extend %loading-base;
  height: 400px;
  border-radius: $border-radius-lg;

  .loading-text {
    font-size: $font-size-base;
    color: $text-color;
  }
}

.map-loading {
  @extend %loading-base;
  height: 100%;
  background: rgba($background-color, 0.95);
  z-index: $zindex-fixed;

  .loading-text {
    font-size: $font-size-base;
    color: $text-color;
  }
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
