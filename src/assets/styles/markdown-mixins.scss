// Flexbox mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// Media queries
@mixin respond-to($breakpoint) {
  @if $breakpoint == "sm" {
    @media (min-width: $screen-sm) {
      @content;
    }
  }
  @if $breakpoint == "md" {
    @media (min-width: $screen-md) {
      @content;
    }
  }
  @if $breakpoint == "lg" {
    @media (min-width: $screen-lg) {
      @content;
    }
  }
  @if $breakpoint == "xl" {
    @media (min-width: $screen-xl) {
      @content;
    }
  }
}

// Typography
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin multi-line-ellipsis($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// Transitions
@mixin transition($property: all, $duration: 0.3s) {
  transition: $property $duration ease-in-out;
}

// Box shadow
@mixin box-shadow($level: 1) {
  @if $level == 1 {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  } @else if $level == 2 {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  } @else if $level == 3 {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
}
