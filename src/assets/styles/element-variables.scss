$--colors: (
  "primary": (
    "base": #0e6ce3,
    "light-3": #3e89e9,
    "light-5": #86b5f1,
    "light-9": #fff,
    "dark-2": #0d66d7,
  ),
  "success": (
    "base": #51c79a,
    "light-3": #74d2ae,
    "dark-2": #4dbd92,
  ),
  "warning": (
    "base": #f79f4c,
    "light-3": #f9b270,
    "dark-2": #ea9748,
  ),
  "danger": (
    "base": #ed6060,
    "light-3": #f18080,
    "dark-2": #e15b5b,
  ),
);

$--table: (
  "header-bg-color": #f3f7fd,
  "header-text-color": #333,
);

@forward "element-plus/theme-chalk/src/common/var.scss" with (
    $colors: $--colors,
    $table: $--table,
    // $fill-color: $--fill-color
  );

// $--fill-color: (
//   "light": #e6f0fc,
// );

// 涵盖一些元素ui样式
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-picker-panel__shortcut {
  width: 220px;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-overlay-dialog {
  overflow: hidden;
}

.el-dialog {
  position: relative;
  border-radius: 8px;
  overflow: hidden;

  .el-dialog__header {
    padding: 20px;
    margin: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    max-height: 80vh;
    overflow-y: auto;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__close {
    color: #909399;

    &:hover {
      color: var(--el-color-primary);
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 14px 20px;
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

.el-form .el-form-item__label {
  font-weight: 700;
}
.el-dialog:not(.is-fullscreen) {
  margin-top: 6vh !important;
}

.el-dialog.scrollbar .el-dialog__body {
  overflow: auto;
  overflow-x: hidden;
  max-height: 70vh;
  padding: 10px 20px 0;
}

.el-table {
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      height: 46px;
      font-size: 14px;
    }
  }
  .el-table__body-wrapper {
    .el-button [class*="el-icon-"] + span {
      margin-left: 1px;
    }
  }
}

.el-table .fixed-width .el-button--small {
  padding-left: 0;
  padding-right: 0;
  width: inherit;
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link {
  cursor: pointer;
  color: #409eff;
  margin-left: 10px;
}

.el-table .el-dropdown,
.el-icon-arrow-down {
  font-size: 12px;
}

.el-tree-node__content > .el-checkbox {
  margin-right: 8px;
}

.el-card__header {
  padding: 14px 15px 7px !important;
  min-height: 40px;
}

.el-card__body {
  padding: 15px 20px 20px 20px !important;
}

/* button color */
.el-button--cyan.is-active,
.el-button--cyan:active {
  background: #20b2aa;
  border-color: #20b2aa;
  color: #ffffff;
}

.el-button--cyan:focus,
.el-button--cyan:hover {
  background: #48d1cc;
  border-color: #48d1cc;
  color: #ffffff;
}

.el-button--cyan {
  background-color: #20b2aa;
  border-color: #20b2aa;
  color: #ffffff;
}
