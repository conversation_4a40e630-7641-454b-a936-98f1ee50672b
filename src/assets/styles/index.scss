@use "element-plus/theme-chalk/src/index.scss" as *;
// @use "element-plus/theme-chalk/src/dark/css-vars.scss" as *;

@import "./variables.module.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./sidebar.scss";
@import "./btn.scss";
@import "./ruoyi.scss";
@import "./markdown-common.scss";
@import "./element-variables.scss";

body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0 !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  margin: 16px;
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  height: calc(100vh - 126px);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .content {
    flex: 1;
    display: flex;
    overflow: hidden;
    margin-top: 8px;

    .right-table,
    &.content-table {
      overflow: hidden;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 8px 0;

      .el-table {
        flex: 1;
        width: 100%;
      }
    }
  }
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.form-container {
  width: 100%;
  background: #f7f8fa;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px 0;

  .el-form-item {
    margin-bottom: 0;
    margin-right: 10px;

    .el-form-item__label {
      margin-left: 16px;
    }
  }

  .form-item-button {
    margin-left: 16px;
  }

  .el-input,
  .el-select,
  .el-date-editor {
    width: 160px;
  }

  .el-date-editor--datetimerange {
    width: 320px;
  }
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.el-picker-panel__shortcut {
  width: 220px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.map-panel {
  padding: 5px;
  background: #1c8bda;
  border: 1px solid #1c8bda;
}

.map-panel-content {
  background: rgba(0, 57, 115, 0.9);
  width: 100%;
  height: 100%;
  box-shadow: 0 0 6px 5px rgba(0, 57, 115, 1);
}

/* 表格容器自动撑满剩余空间 */
.table-container {
  height: calc(100% - 24px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .tablesBox {
    flex: auto;
    overflow: hidden;

    .tablesContent {
      height: 100%;
      width: 100%;

      .el-table {
        /* 表格高度设置 */
        height: 100% !important;
      }
    }
  }
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.flex-c {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex {
  display: flex;

}