/**
 * @file useECharts.js
 * @description ECharts图表渲染的Vue组合式API
 * @features
 * - 支持从Markdown代码块中解析图表配置
 * - 支持动态渲染多个图表
 * - 支持响应式布局
 * - 自动清理资源
 */

import { ref, onUnmounted, nextTick } from "vue";
import * as echarts from "echarts";

/**
 * ECharts图表管理Hook
 * @returns {Object} 返回图表相关的状态和方法
 */
export function useECharts() {
  // 存储所有图表实例
  const charts = ref([]);
  // 图表计数器，用于生成唯一标识
  const echartsCounter = ref(0);
  // 存储所有resize事件监听器，用于组件卸载时清理
  const resizeListeners = ref([]);

  /**
   * 解析Markdown中的图表数据
   * @param {string} content - 包含图表配置的Markdown内容
   * @returns {Array|null} 解析后的图表配置数组，解析失败返回null
   */
  const parseChartData = (content) => {
    if (!content) {
      console.log("parseChartData: content is empty");
      return null;
    }

    try {
      // 使用正则表达式匹配所有echarts代码块
      const echartsMatches = Array.from(
        content.matchAll(/```echarts\n([\s\S]*?)\n```/g)
      );

      if (echartsMatches && echartsMatches.length > 0) {
        return echartsMatches
          .map((match) => {
            try {
              // 尝试直接解析JSON
              const jsonStr = match[1].trim();
              return JSON.parse(jsonStr);
            } catch (jsonError) {
              console.error(jsonError);
              // JSON解析失败时，尝试清理和转换JavaScript对象为JSON
              try {
                // 移除JavaScript注释
                const codeWithoutComments = match[1]
                  .replace(/\/\/.*/g, "") // 移除单行注释
                  .replace(/\/\*[\s\S]*?\*\//g, "") // 移除多行注释
                  .trim();

                // 将JavaScript对象字面量转换为标准JSON格式
                // 1. 为属性名添加双引号
                // 2. 将单引号替换为双引号
                // 3. 移除尾随逗号
                const jsObjectStr = codeWithoutComments
                  .replace(/([{,]\s*)(\w+):/g, '$1"$2":')
                  .replace(/'/g, '"')
                  .replace(/,(\s*[}\]])/g, "$1");

                return JSON.parse(jsObjectStr);
              } catch (cleanError) {
                console.error("清理后的JSON解析失败:", cleanError);
                return null;
              }
            }
          })
          .filter(Boolean); // 过滤掉解析失败的null值
      }
    } catch (e) {
      console.error("解析图表数据失败:", e);
    }
    return null;
  };

  /**
   * 渲染图表
   * @param {HTMLElement} markdownContent - Markdown内容的DOM容器
   * @param {Array} chartData - 图表配置数组
   */
  const renderCharts = (markdownContent, chartData) => {
    if (!markdownContent || !chartData) {
      console.warn("renderCharts: 缺少必要参数", {
        markdownContent: !!markdownContent,
        chartData: !!chartData,
      });
      return;
    }

    // 等待DOM更新完成后再渲染图表
    nextTick(() => {
      // 查找所有图表占位符
      const placeholders = markdownContent.querySelectorAll(
        ".echarts-placeholder"
      );

      // 确保有图表数据后开始渲染
      if (Array.isArray(chartData)) {
        placeholders.forEach((placeholder, index) => {
          try {
            // 检查图表是否已经渲染过
            const rendered = placeholder.querySelector(".echarts-rendered");
            if (rendered) {
              console.log(`图表${index}已渲染，跳过...`);
              return;
            }

            // 创建或获取图表容器
            let chartContainer = placeholder.querySelector(".echarts-chart");
            if (!chartContainer) {
              chartContainer = document.createElement("div");
              chartContainer.className = "echarts-chart";
              // 设置图表容器样式
              chartContainer.style.cssText = `
                width: 100%;
                height: 400px;
                opacity: 1;
                display: block;
              `;
            }

            // 获取或检查wrapper容器
            const wrapper = placeholder.querySelector(".chart-wrapper");
            if (!wrapper) {
              console.error(`未找到图表${index}的wrapper容器`);
              return;
            }

            // 确保图表容器已添加到wrapper中
            if (!wrapper.contains(chartContainer)) {
              wrapper.appendChild(chartContainer);
            }

            // 初始化ECharts实例
            const chart = echarts.init(chartContainer);

            // 使用对应索引的图表配置
            if (chartData[index]) {
              // 设置图表配置
              chart.setOption(chartData[index]);

              // 隐藏加载动画
              const loadingContainer =
                placeholder.querySelector(".chart-loading");
              if (loadingContainer) {
                loadingContainer.style.display = "none";
              }

              // 强制更新图表大小以适应容器
              chart.resize();

              // 添加渲染完成标记
              const marker = document.createElement("div");
              marker.className = "echarts-rendered";
              marker.style.display = "none";
              wrapper.appendChild(marker);

              // 保存图表实例以便后续管理
              charts.value[index] = chart;

              // 添加窗口大小变化的响应式处理
              const resizeHandler = () => {
                chart.resize();
              };
              window.addEventListener("resize", resizeHandler);

              // 保存resize监听器以便后续清理
              resizeListeners.value.push({
                handler: resizeHandler,
                chart: chart,
              });
            } else {
              console.warn(`未找到索引${index}的图表数据`);
            }
          } catch (error) {
            console.error(`渲染图表${index}时出错:`, error);
          }
        });
      }
    });
  };

  /**
   * 清理函数：清理所有图表实例和事件监听器
   */
  const cleanup = () => {
    // 清理所有resize监听器和图表实例
    resizeListeners.value.forEach(({ handler, chart }) => {
      window.removeEventListener("resize", handler);
      if (chart) {
        chart.dispose(); // 销毁图表实例，释放资源
      }
    });
    resizeListeners.value = [];
    charts.value = [];
  };

  // 组件卸载时自动清理资源
  onUnmounted(() => {
    cleanup();
  });

  // 返回图表相关的状态和方法
  return {
    charts, // 图表实例数组
    echartsCounter, // 图表计数器
    parseChartData, // 解析图表数据方法
    renderCharts, // 渲染图表方法
    cleanup, // 手动清理方法
  };
}
