import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { villageList } from '@/api/warning/index';
import { selectTsList } from '@/api/watershed/ads';

/**
 * 地区数据管理
 * 处理乡镇和村社数据的获取和管理
 */
export function useLocationData() {
  // 乡镇和村社数据
  const townshipOptions = ref([]);
  const villageOptions = ref([]);

  /**
   * 获取乡镇列表
   */
  const getTownships = async () => {
    try {
      const res = await selectTsList({ pageNum: 1, pageSize: 9999 });
      townshipOptions.value = res.rows || [];
    } catch (error) {
      console.error('获取乡镇列表失败:', error);
      ElMessage.error('获取乡镇数据失败，请稍后重试');
      townshipOptions.value = [];
    }
  };

  /**
   * 获取村社列表
   * @param {string} townshipId 乡镇ID
   */
  const getVillages = async (townshipId) => {
    if (!townshipId) {
      villageOptions.value = [];
      return;
    }

    try {
      const res = await villageList({
        pageNum: 1,
        pageSize: 1000,
        geoType: 'pepole',
        townCode: townshipId,
      });
      villageOptions.value = res.data?.records || [];
    } catch (error) {
      console.error('获取村社列表失败:', error);
      ElMessage.error('获取村社数据失败，请稍后重试');
      villageOptions.value = [];
    }
  };

  return {
    townshipOptions,
    villageOptions,
    getTownships,
    getVillages,
  };
}