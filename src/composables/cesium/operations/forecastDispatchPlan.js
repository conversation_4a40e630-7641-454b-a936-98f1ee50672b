import { taskWorkList } from "@/api/scheduling";

/**
 * 防洪四预预报调度方案操作
 * @param {Object} viewer - Cesium Viewer实例
 * @param {Object} baseOps - 基础操作实例
 * @returns {Object} - 防洪四预预报调度方案操作对象
 */
export function getForecastDispatchPlan(viewer, baseOps) {
  /**
   * 获取预报调度方案列表
   * @param {Object} params - 查询参数
   * @param {string} params.name - 名称
   * @param {Array} params.time - 查询时间范围 [开始时间, 结束时间] - 时间格式为YYYY-MM-DD HH:mm:ss
   * @returns {Promise<Object>} - 返回查询结果，包含success标识
   */
  const getForecastDispatchPlanList = async (params) => {
    try {
      if (!params) {
        return {
          success: false,
          error: "参数不能为空",
          type: "forecastDispatchPlan",
        };
      }

      const { name, time } = params;

      if (!time || !Array.isArray(time) || time.length !== 2) {
        return {
          success: false,
          error: "时间参数格式错误，应为[开始时间, 结束时间]",
          type: "forecastDispatchPlan",
        };
      }

      const res = await taskWorkList({
        state: 5,
        name: name,
        startTime: time[0],
        endTime: time[1],
      });

      const data = res.data || [];

      return {
        success: true,
        data: data,
        type: "forecastDispatchPlan",
        message: "获取预报调度方案列表成功"
      };
    } catch (error) {
      console.error("获取预报调度方案列表失败:", error);
      return {
        success: false,
        error: error.message || "获取预报调度方案列表失败",
        type: "forecastDispatchPlan",
      };
    }
  };

  return {
    getForecastDispatchPlanList,
  };
}
