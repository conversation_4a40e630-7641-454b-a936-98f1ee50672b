import { ElMessage } from "element-plus"

/**
 * cesium 导航操作
 * 包含缩放、定位等功能
 * @param {Object} viewer - Cesium Viewer实例
 * @param {Object} baseOps - 基础操作实例
 * @returns {Object} 导航操作集合
 */
export function getNavigationOperations(viewer, baseOps) {
  const Cesium = DC.getLib("Cesium")

  return {
    /**
     * 放大地图
     */
    async zoomBig() {
      try {
        const center = baseOps._getCenterPosition()
        await viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(center.lon, center.lat, center.height / 1.8),
          duration: 1.0
        })
        ElMessage.success("放大成功")
        return { success: true }
      } catch (error) {
        console.error("Error in zoomBig operation:", error)
        return {
          success: false,
          error: error.message
        }
      }
    },

    /**
     * 缩小地图
     */
    async zoomSmall() {
      try {
        const center = baseOps._getCenterPosition()
        await viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(center.lon, center.lat, center.height * 1.2),
          duration: 1.0
        })
        ElMessage.success("缩小成功")
        return { success: true }
      } catch (error) {
        console.error("Error in zoomSmall operation:", error)
        return {
          success: false,
          error: error.message
        }
      }
    }
  }
}
