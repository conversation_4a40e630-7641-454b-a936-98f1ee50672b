import * as turf from "@turf/turf";
import {
  riverStationList,
  reservoirStationList,
} from "@/api/watershed/query/index";
import {
  getRiverColor,
  getRainTypeByDrp,
} from "@/components/Map/utils/water-common";
import { rainSumlist } from "@/api/watershed/screenRight/overview";
import { getRvInfo, selectRvList, selectStlyList } from "@/api/watershed/ads";
import {
  villageList, // 村社列表
  queryEnterpriseList, // 企事业单位列表
  residentList, // 居民住宅列表
  queryRiskProblem, // 风险隐患列表
  placeList, // 安置点列表
  dangerousAreaList, // 危险区列表
  dangerousAreaDetail, // 危险区详情
  queryVillagePlacementList, // 村社安置点列表
  queryVillagePlacementDetail, // 村社安置点详情
} from "@/api/warning";
import { useMapPanelStore, useLayerControlStore } from "@/store/modules/map";
import moment from "moment";

/**
 * 防洪四预地图图层点位加载操作
 * 包含河流、监测站点（雨量站、河道站、水库站）等点位加载功能
 * @param {Object} viewer - Cesium Viewer实例
 * @param {Object} baseOps - 基础操作实例
 * @returns {Object} 防洪四预操作集合
 */
export function getFloodPreventionOperations(viewer, baseOps) {
  const Cesium = DC.getLib("Cesium");
  const panelStore = useMapPanelStore();

  const layerControlStore = useLayerControlStore();

  const { changeVis } = layerControlStore;

  const clearLayer = (name, viewer) => {
    if (viewer) {
      let layer = viewer.getLayer(name + "-layer");
      let htmllayer = viewer.getLayer(name + "-layer-html");
      layer?.clear();
      htmllayer?.clear();
    }
  };

  const createGLayer = (type) => {
    let hasLayer = viewer.getLayer(type + "-layer");
    if (hasLayer) {
      return;
    }
    let layer = new DC.VectorLayer(type + "-layer");
    let layer2 = new DC.VectorLayer(type + "2-layer");
    let hlayer = new DC.HtmlLayer(type + "-layer-html");
    let hlayer2 = new DC.HtmlLayer(type + "2-layer-html");
    let glayer = new DC.LayerGroup(type);
    let glayer2 = new DC.LayerGroup(type + "2");
    viewer.addLayerGroup(glayer);
    viewer.addLayerGroup(glayer2);
    glayer.addLayer(layer);
    glayer.addLayer(hlayer);
    glayer2.addLayer(layer2);
    glayer2.addLayer(hlayer2);
  };

  const formatTree = (data) => {
    let dataList = data.map((item) => {
      return {
        ...item.data,
        children: item.children && formatTree(item.children),
      };
    });

    return dataList;
  };

  const getRiverImg = (sttp, warnType, size) => {
    sttp = sttp || "ZQ";

    warnType = warnType || "";
    size = size || 32;
    let color = getRiverColor(warnType);
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    canvas.width = size;
    canvas.height = size;
    ctx.beginPath();
    ctx.moveTo(size * 0.25, 0);
    ctx.lineTo(size * 0.75, 0);
    ctx.lineTo(size * 0.5, size);
    ctx.closePath();
    if (sttp === "ZQ") {
      ctx.fillStyle = color;
      ctx.fill();
    } else {
      ctx.lineWidth = 3;
      ctx.strokeStyle = color;
      ctx.stroke();
    }
    let image = new Image();
    image.src = canvas.toDataURL("image/png");
    return image;
  };

  // 危险因素类型枚举函数
  const getDangerTypeText = (type) => {
    const typeMap = {
      1: "临河滑坡",
      2: "泥石流",
      3: "低洼地",
      4: "急弯",
      5: "束窄",
      6: "流域",
      7: "多支齐汇",
      8: "桥梁",
    };
    return typeMap[type] || "--";
  };

  // calculateBoundsFromGeom方法已经移至operations.js中的BaseOperations类

  // 添加雨量站
  const addrainData = (data, rainlayer, rainHtmllayer) => {
    let position = new DC.Position(data.lgtd, data.lttd);
    let rainType = getRainTypeByDrp(data.todayRainfall);
    let Cesium = DC.getLib("Cesium");

    let point = new DC.PointPrimitive(position);
    point.setStyle({
      pixelSize: 12,
      color: Cesium.Color.fromCssColorString(rainType.color),
      outlineColor: Cesium.Color.fromCssColorString(rainType.color),
      outlineWidth: 0,
      scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 0.5),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      distanceDisplayCondition: {
        near: 0, //最近距离
        far: 100000, //最远距离
      },
    });

    point.on(DC.MouseEventType.CLICK, (e) => {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.stcd,
        STNM: e.overlay.attr.stnm,
        STTP: "PP",
      });
    });

    // 存储数据用于事件处理
    point.attr = data;

    // 添加到图层
    rainlayer.addOverlay(point);

    // 创建HTML标签显示雨量信息
    let divIcon = new DC.DivIcon(
      position,
      `<div onmouseover="showMorePopInfo('${
        data.stcd + rainlayer.id
      }')"  onmouseout="hideMorePopInfo('${data.stcd + rainlayer.id}')">
                <div id="${
                  data.stcd + rainlayer.id + "pop1"
                }" style="" class="marker-rain-html">${
        data.todayRainfall
      }mm</div>
                <div id="${
                  data.stcd + rainlayer.id + "pop2"
                }" style="display: none" class="marker-rain-html2">
                  <div style="width: 100%;height: 30px;line-height: 30px"><div style="float: left;font-size: 24px;">${
                    data.todayRainfall
                  }</div><div style="float:left;line-height: 30px">mm</div></div>
                  <div style="width: 100%;height: 25px;font-size: 13px"><div style="float: left;max-width: 150px;text-align: left;" class="hideMoreText">${
                    data.stnm || "--"
                  }</div></div>
                  <hr class="popHr">
                  <div style="width: 100%;height: 25px;font-size: 14px;"><div style="float: left;max-width: 150px" class="hideMoreText">${
                    data.adnm || "--"
                  }</div></div>
                  <div style="width: 100%;height: 25px;font-size: 12px;"><div  style="float: left;">${
                    data?.beforeTime + "至" + data?.nowTime
                  }</div></div>
                  <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
                </div>
              </div>`
    );

    divIcon.attr = data;
    divIcon.setStyle({
      distanceDisplayCondition: {
        near: 0,
        far: 10000,
      },
    });
    divIcon.on(DC.MouseEventType.CLICK, (e) => {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.stcd,
        STNM: e.overlay.attr.stnm,
        STTP: "PP",
      });
    });
    rainHtmllayer.addOverlay(divIcon);
  };

  // 添加河道站
  const addriverData = (data, layer, htmllayer) => {
    let position = new DC.Position(data.lgtd, data.lttd);
    let image = getRiverImg(data.sttp, data.warnTypeVal, 32);

    let point = new DC.BillboardPrimitive(position, image);
    point.setStyle({
      scale: 0.7,
      pixelOffset: new Cesium.Cartesian2(0, 0),
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 0.5),
      distanceDisplayCondition: {
        near: 0, //最近距离
        far: 100000, //最远距离
      },
    });

    // 存储数据用于事件处理
    point.attr = data;

    // 添加点击事件
    point.on(DC.MouseEventType.CLICK, (e) => {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.riverStationCode,
        STNM: e.overlay.attr.riverStationName,
        STTP: e.overlay.attr.sttp,
      });
    });

    // 添加到图层
    layer.addOverlay(point);

    // 根据预警状态区分
    let divIcon = null;

    let subClass = "";
    let subTitle = "";
    let subTitleColor = "";
    if (data?.wptn == "涨") {
      subClass = "up_marker";
      subTitle = "涨";
      subTitleColor = "#d7cd11";
    } else if (data?.wptn == "落") {
      subClass = "down_marker";
      subTitle = "落";
      subTitleColor = "#1eb738";
    } else if (data?.wptn == "平") {
      subClass = "ping_marker";
      subTitle = "平";
      subTitleColor = "#0cb7e3";
    }
    if (data.warnTypeVal) {
      // data.WARNTP 不同级别样式不一样可能
      data.warnTypeVal = data?.warnTypeVal.replace("水位", "");
      if (!data.warnTypeVal) return;

      let mainClass = "";
      let mainClassColor = "";
      if (data.warnTypeVal.indexOf("历史") > -1) {
        mainClass = "up-river";
        mainClassColor = "#FF7E01";
      } else if (data.warnTypeVal.indexOf("保证") > -1) {
        mainClass = "down-river";
        mainClassColor = "#FFA92F";
      } else if (data.warnTypeVal.indexOf("警戒") > -1) {
        mainClass = "ping-river";
        mainClassColor = "#FFEA35";
      }
      divIcon = new DC.DivIcon(
        position,
        `<div onmouseover="showMorePopInfo('${
          data.riverStationCode + layer.id
        }')"  onmouseout="hideMorePopInfo('${
          data.riverStationCode + layer.id
        }')">
             <div id="${
               data.riverStationCode + layer.id + "pop1"
             }" style="" class="marker-river-warn-html ${mainClass}">${
          data.warnTypeVal || "--"
        }m<div class="${subClass}" >${subTitle}</div></div>
             <div id="${
               data.riverStationCode + layer.id + "pop2"
             }" style="display: none" class="marker-river-warn-html2">
               <div style="width: 100%;height: 30px;line-height: 30px"><div style="float: left;font-size: 24px;">${
                 data.waterLevel || "--"
               }</div><div style="float:left;line-height: 30px">m</div><div style="float: right;color: ${subTitleColor}">${subTitle}</div></div>
               <div style="width: 100%;height: 25px;font-size: 13px"><div style="float: left;max-width: 60px;text-align: left;" class="hideMoreText">${
                 data.riverStationName || "--"
               }</div><div style="float:right;color: ${mainClassColor}" >${
          data.warnTypeVal || "--"
        }m</div></div>
               <hr class="popHr">
               <div style="width: 100%;height: 25px;font-size: 14px;"><div style="float: left;max-width: ${
                 data.sttp === "ZZ" ? 150 : 65
               }px" class="hideMoreText">${
          data.adminRegionName || "--"
        }</div><div style="float: right;display: ${
          data.sttp === "ZZ" ? "none" : ""
        }" >流量${data.waterFlow || "--"}m³/s</div></div>
               <div style="width: 100%;height: 25px;font-size: 14px;"><div  style="float: left;">${
                 data?.time?.substring(5, 16).replace("T", " ") || "--"
               }</div></div>
               <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
             </div>
           </div>`
      );
    } else {
      divIcon = new DC.DivIcon(
        position,
        `<div onmouseover="showMorePopInfo('${
          data.riverStationCode + layer.id
        }')"  onmouseout="hideMorePopInfo('${
          data.riverStationCode + layer.id
        }')">
             <div id="${
               data.riverStationCode + layer.id + "pop1"
             }" style="" class="marker-river-html">${
          data.waterLevel || "--"
        }m<div class="${subClass}" >${subTitle}</div></div>
             <div id="${
               data.riverStationCode + layer.id + "pop2"
             }" style="display: none" class="marker-river-warn-html2">
               <div style="width: 100%;height: 30px;line-height: 30px"><div style="float: left;font-size: 24px;">${
                 data.waterLevel || "--"
               }</div><div style="float:left;line-height: 30px">m</div><div style="float: right;color: ${subTitleColor}">${subTitle}</div></div>
               <div style="width: 100%;height: 25px;font-size: 13px"><div style="float: left;max-width: 60px;text-align: left;" class="hideMoreText">${
                 data.riverStationName || "--"
               }</div></div>
               <hr class="popHr">
               <div style="width: 100%;height: 25px;font-size: 14px;"><div style="float: left;max-width: ${
                 data.sttp === "ZZ" ? 150 : 65
               }px" class="hideMoreText">${
          data.adminRegionName || "--"
        }</div><div style="float: right;display: ${
          data.sttp === "ZZ" ? "none" : ""
        }" >流量${data.waterFlow || "--"}m³/s</div></div>
               <div style="width: 100%;height: 25px;font-size: 14px;"><div  style="float: left;">${
                 data?.time?.substring(5, 16).replace("T", " ") || "--"
               }</div></div>
               <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
             </div>
           </div>`
      );
    }

    divIcon.attr = data;
    divIcon.setStyle({
      distanceDisplayCondition: {
        near: 0,
        far: 10000,
      },
    });
    divIcon.on(DC.MouseEventType.CLICK, (e) => {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.riverStationCode,
        STNM: e.overlay.attr.riverStationName,
        STTP: e.overlay.attr.sttp,
      });
    });
    htmllayer.addOverlay(divIcon);
  };

  // 添加水库站
  const addreservoirData = (data, layer, htmllayer) => {
    let position = new DC.Position(data.lgtd, data.lttd);

    let point = new DC.BillboardPrimitive(
      position,
      "/icons/marker/reservoir.png"
    );
    point.setStyle({
      scale: 0.5,
      pixelOffset: new Cesium.Cartesian2(0, 0),
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 0.5),
      distanceDisplayCondition: {
        near: 0, //最近距离
        far: 100000, //最远距离
      },
    });

    // 存储数据用于事件处理
    point.attr = data;

    // 添加点击事件
    point.on(DC.MouseEventType.CLICK, (e) => {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.stcd,
        STNM: e.overlay.attr.stnm,
        STTP: "RR",
      });
    });

    // 添加到图层
    layer.addOverlay(point);

    let subClass = "";
    let subTitle = "";
    let subTitleColor = "";
    if (data?.wptn === "涨") {
      subClass = "up_marker";
      subTitle = "涨";
      subTitleColor = "#d7cd11";
    } else if (data?.wptn === "落") {
      subClass = "down_marker";
      subTitle = "落";
      subTitleColor = "#1eb738";
    } else if (data?.wptn === "平") {
      subClass = "ping_marker";
      subTitle = "平";
      subTitleColor = "#0cb7e3";
    }
    let mainClass = "";
    let mainClassColor = "";
    if (data?.warnTypeVal?.indexOf("历史") > -1) {
      mainClass = "up-river";
      mainClassColor = "#FF7E01";
    } else if (data?.warnTypeVal?.indexOf("保证") > -1) {
      mainClass = "down-river";
      mainClassColor = "#FFA92F";
    } else if (data?.warnTypeVal?.indexOf("警戒") > -1) {
      mainClass = "ping-river";
      mainClassColor = "#FFEA35";
    } else if (data?.warnTypeVal?.indexOf("汛限") > -1) {
      mainClass = "over-limit";
      mainClassColor = "#FFEB98";
    }
    // 根据预警状态区分
    let divIcon = null;

    divIcon = new DC.DivIcon(
      position,
      `<div onmouseover="showMorePopInfo('${
        data.stcd + layer.id
      }')"  onmouseout="hideMorePopInfo('${data.stcd + layer.id}')">
             <div id="${
               data.stcd + layer.id + "pop1"
             }"  class="marker-reservoir-warn-html ${mainClass}"> ${
        data.waterLevel || "--"
      }m<div class="${subClass}" >${subTitle}</div></div>
             <div id="${
               data.stcd + layer.id + "pop2"
             }" style="display: none" class="marker-reservoir-warn-html2">
               <div style="width: 100%;height: 30px;line-height: 30px"><div style="float: left;font-size: 24px;">${
                 data.waterLevel || "--"
               }</div><div style="float:left;line-height: 30px">m</div><div style="float: right;color: ${subTitleColor}">${subTitle}</div></div>
               <div style="width: 100%;height: 25px;font-size: 13px"><div style="float: left;max-width: 130px;text-align: left;" class="hideMoreText">${
                 data.stnm || "--"
               }</div></div>
               <hr class="popHr">
               <div style="width: 100%;height: 25px;font-size: 14px;"><div style="float: left;max-width: 65px" class="hideMoreText">${
                 data.adnm || "--"
               }</div><div style="float: right">流量${
        data.inboundFlow || "--"
      }m³/s</div></div>
               <div style="width: 100%;height: 25px;font-size: 14px;"><div  style="float: left;">${
                 data?.time || "--"
               }</div></div>
               <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
             </div>
           </div>`
    );
    divIcon.attr = data;
    divIcon.setStyle({
      distanceDisplayCondition: {
        near: 0,
        far: 10000,
      },
    });
    divIcon.on(DC.MouseEventType.CLICK, (e) => {
      panelStore.showMainPanel({
        STCD: e.overlay.attr.stcd,
        STNM: e.overlay.attr.stnm,
        STTP: "RR",
      });
    });
    htmllayer.addOverlay(divIcon);
  };

  //添加水闸点位 5.15手动前端添加 后续添加接口读取
  const addWaterGateData = (data, layer, htmllayer) => {
    let position = new DC.Position(data.lgtd, data.lttd);

    let point = new DC.BillboardPrimitive(position, "/icons/marker/zhamen.png");
    point.setStyle({
      heightReference: 0,
      scale: 1,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      distanceDisplayCondition: {
        near: 0, //最近距离
        far: 500000, //最远距离
      },
    });
    point.size = [20, 20];
    point.attr = data;
    layer.addOverlay(point);
    layer.show = true;
    // window.viewer.flyTo(primitiveLayer);
  };

  // 添加村社点位
  const addTownshipData = (data, layer, htmllayer) => {
    let position = new DC.Position(data.lgtd, data.lttd);
    let point = new DC.BillboardPrimitive(
      position,
      "/icons/marker/house-poi.png"
    );
    point.setStyle({
      heightReference: 0,
      scale: 1,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      distanceDisplayCondition: {
        near: 0, //最近距离
        far: 50000, //最远距离
      },
      // pixelOffset: new Cesium.Cartesian2(0, 0),
      // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      // scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 1)
    });
    point.size = [20, 20];
    // 存储数据用于事件处理
    point.attr = data;

    // 添加到图层
    layer.addOverlay(point);

    // 创建HTML标签显示信息
    let divIcon = new DC.DivIcon(
      position,
      `<div onmouseover="showMorePopInfo('${
        data.adcd + layer.id
      }')"  onmouseout="hideMorePopInfo('${data.adcd + layer.id}')">
     <div id="${data.adcd + layer.id + "pop1"}" class="marker-township-html">${
        data.adnm || "--"
      }</div>
     <div id="${
       data.adcd + layer.id + "pop2"
     }" style="display: none" class="marker-township-html2">
       <div style="width: 100%;height: 30px;line-height: 30px"><div style="float: left;font-size: 16px;">${
         data.adnm || "--"
       }</div></div>

       <hr class="popHr">
       <div style="width: 100%;height: 25px;font-size: 14px;"><div style="float: left;max-width: 150px" class="hideMoreText">户籍人口：${
         data.registeredPopulation || "--"
       }</div></div>
       <div style="width: 100%;height: 25px;font-size: 12px;"><div style="float: left;">常住人口：${
         data.contacts || "--"
       }</div></div>
       <div style="width: 100%;height: 25px;font-size: 12px;"><div style="float: left;">防洪能力：${
         data.floodControlCapacity || "--"
       }</div></div>
       <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
     </div>
   </div>`
    );

    divIcon.attr = data;
    divIcon.setStyle({
      distanceDisplayCondition: {
        near: 0,
        far: 10000,
      },
    });
    htmllayer.addOverlay(divIcon);
  };

  // 添加企事业单位点位
  const addEnterpriseData = (data, layer, htmllayer) => {
    let position = new DC.Position(data.longitude, data.latitude);
    let point = new DC.BillboardPrimitive(
      position,
      "/icons/marker/enterprises-poi.png"
    );
    point.setStyle({
      heightReference: 0,
      scale: 1,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      distanceDisplayCondition: {
        near: 0, //最近距离
        far: 50000, //最远距离
      },
      // pixelOffset: new Cesium.Cartesian2(0, 0),
      // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      // scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 1)
    });
    point.size = [20, 20];
    point.attr = data;
    layer.addOverlay(point);

    let divIcon = new DC.DivIcon(
      position,
      `<div onmouseover="showMorePopInfo('${
        data.id + layer.id
      }')"  onmouseout="hideMorePopInfo('${data.id + layer.id}')">
        <div id="${
          data.id + layer.id + "pop1"
        }" class="marker-enterprise-html">${data.name || "--"}</div>
        <div id="${
          data.id + layer.id + "pop2"
        }" style="display: none" class="marker-enterprise-html2">
          <div class="marker-title">${data.name || "--"}</div>
          <div class="marker-content">

            <hr class="popHr">
            <div class="marker-item">
              <span class="marker-label">房屋数量：</span>
              <span class="marker-value">${data.houseCount || "--"}</span>
            </div>
            <div class="marker-item">
              <span class="marker-label">在岗人数：</span>
              <span class="marker-value">${data.staffCount || "--"}人</span>
            </div>
            <div class="marker-item">
              <span class="marker-label">占地面积：</span>
              <span class="marker-value">${data.area || "--"}Km²</span>
            </div>
          </div>
          <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
        </div>
      </div>`
    );
    divIcon.attr = data;
    divIcon.setStyle({
      distanceDisplayCondition: {
        near: 0,
        far: 10000,
      },
    });
    htmllayer.addOverlay(divIcon);
  };

  // 添加居民住宅点位
  const addResidentData = (data, layer, htmllayer) => {
    let position = new DC.Position(data.lgtd, data.lttd);
    let point = new DC.BillboardPrimitive(
      position,
      "/icons/marker/household-poi.png"
    );
    point.setStyle({
      heightReference: 0,
      scale: 1,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      distanceDisplayCondition: {
        near: 0, //最近距离
        far: 50000, //最远距离
      },
      // pixelOffset: new Cesium.Cartesian2(0, 0),
      // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      // scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 1)
    });
    point.size = [20, 20];
    point.attr = data;
    layer.addOverlay(point);
    let divIcon = new DC.DivIcon(
      position,
      `<div onmouseover="showMorePopInfo('${
        data.id + layer.id
      }')"  onmouseout="hideMorePopInfo('${data.id + layer.id}')">
        <div id="${data.id + layer.id + "pop1"}" class="marker-resident-html">${
        data.name || "--"
      }</div>
        <div id="${
          data.id + layer.id + "pop2"
        }" style="display: none" class="marker-resident-html2">
          <div class="marker-title">${data.name || "--"}</div>
          <div class="marker-content">

            <hr class="popHr">
            <div class="marker-item">
              <span class="marker-label">户数：</span>
              <span class="marker-value">${data.households || "--"}户</span>
            </div>
            <div class="marker-item">
              <span class="marker-label">常住人口：</span>
              <span class="marker-value">${data.householdSize || "--"}人</span>
            </div>
            <div class="marker-item">
              <span class="marker-label">防洪能力：</span>
              <span class="marker-value">${"" || "--"}</span>
            </div>
          </div>
          <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
        </div>
      </div>`
    );
    divIcon.attr = data;
    divIcon.setStyle({
      distanceDisplayCondition: {
        near: 0,
        far: 5000,
      },
    });
    htmllayer.addOverlay(divIcon);
  };

  // 添加风险隐患点位
  const addRiskProblemData = (data, layer, htmllayer) => {
    let position = new DC.Position(data.longitude, data.latitude);

    let point = new DC.BillboardPrimitive(
      position,
      "/icons/marker/riskproblem-poi.png"
    );
    point.setStyle({
      heightReference: 0,
      scale: 1,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      distanceDisplayCondition: {
        near: 0, //最近距离
        far: 50000, //最远距离
      },
      // pixelOffset: new Cesium.Cartesian2(0, 0),
      // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      // scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 1)
    });
    point.size = [20, 20];
    point.attr = data;
    layer.addOverlay(point);

    let divIcon = new DC.DivIcon(
      position,
      `<div onmouseover="showMorePopInfo('${
        data.id + layer.id
      }')"  onmouseout="hideMorePopInfo('${data.id + layer.id}')">
        <div id="${data.id + layer.id + "pop1"}" class="marker-risk-html">${
        data.name || "--"
      }</div>
        <div id="${
          data.id + layer.id + "pop2"
        }" style="display: none" class="marker-risk-html2">
          <div class="marker-title">${data.name || "--"}</div>
          <div class="marker-content">

            <hr class="popHr">
            <div class="marker-item">
              <span class="marker-label">危险因素：</span>
              <span class="marker-value">${
                getDangerTypeText(data.type) || "--"
              }</span>
            </div>
            <div class="marker-item">
              <span class="marker-label">影响村社：</span>
              <span class="marker-value">${data.villageNames || "--"}</span>
            </div>
          </div>
          <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
        </div>
      </div>`
    );
    divIcon.attr = data;
    divIcon.setStyle({
      distanceDisplayCondition: {
        near: 0,
        far: 10000,
      },
    });
    htmllayer.addOverlay(divIcon);
  };

  // 添加安置点点位
  const addPlaceData = (data, layer, htmllayer) => {
    let position = new DC.Position(data.lgtd, data.lttd);

    let point = new DC.BillboardPrimitive(
      position,
      "/icons/marker/place-poi.png"
    );
    point.setStyle({
      heightReference: 0,
      scale: 1,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      distanceDisplayCondition: {
        near: 0, //最近距离
        far: 50000, //最远距离
      },
      // pixelOffset: new Cesium.Cartesian2(0, 0),
      // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      // scaleByDistance: new Cesium.NearFarScalar(300000, 1, 500000, 1)
    });
    point.size = [20, 20];
    point.attr = data;
    layer.addOverlay(point);

    let divIcon = new DC.DivIcon(
      position,
      `<div onmouseover="showMorePopInfo('${
        data.id + layer.id
      }')"  onmouseout="hideMorePopInfo('${data.id + layer.id}')">
        <div id="${data.id + layer.id + "pop1"}" class="marker-place-html">${
        data.name || "--"
      }</div>
        <div id="${
          data.id + layer.id + "pop2"
        }" style="display: none" class="marker-place-html2">
          <div class="marker-title">${data.name || "--"}</div>
          <div class="marker-content">

            <hr class="popHr">
            <div class="marker-item">
              <span class="marker-label">容纳人数：</span>
              <span class="marker-value">${
                data.resettlementPeoNum || "--"
              }人</span>
            </div>
            <div class="marker-item">
              <span class="marker-label">负责人：</span>
              <span class="marker-value">${data.notes || "--"}</span>
            </div>
            <div class="marker-item">
              <span class="marker-label">联系方式：</span>
              <span class="marker-value">${data.phone || "--"}</span>
            </div>
            <div class="marker-item">
              <span class="marker-label">地址:</span>
              <span class="marker-value">${data.address || "--"}</span>
            </div>
          </div>
          <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
        </div>
      </div>`
    );
    divIcon.attr = data;
    divIcon.setStyle({
      distanceDisplayCondition: {
        near: 0,
        far: 10000,
      },
    });
    htmllayer.addOverlay(divIcon);
  };

  // 添加危险区面数据
  const addDangerAreaData = (data, layer, htmllayer, hazardouName) => {
    if (
      !data ||
      !data.features ||
      !data.features[0] ||
      !data.features[0].geometry
    ) {
      console.warn("Invalid GeoJSON data for danger area");
      return;
    }
    try {
      // 获取多边形坐标
      const coordinates = data.features[0].geometry.coordinates[0][0];
      const maskpointArray = [];

      // 转换坐标格式
      for (let i = 0, l = coordinates.length; i < l; i++) {
        maskpointArray.push(coordinates[i][0]);
        maskpointArray.push(coordinates[i][1]);
      }

      // 创建面图层
      const polygon = new DC.Polygon(
        Cesium.Cartesian3.fromDegreesArray(maskpointArray)
      );

      // 设置面的样式
      polygon.setStyle({
        material: DC.Color.fromCssColorString("rgba(255, 0, 0, 0.3)"), // 半透明红色填充
        outline: true,
        outlineColor: DC.Color.fromCssColorString("rgba(255, 0, 0, 1)"), // 红色边框
        outlineWidth: 2,
        classificationType: 2, // 同时影响地形和3D切片
        distanceDisplayCondition: {
          near: 0,
          far: 50000,
        },
      });

      // 添加到图层
      layer.addOverlay(polygon);

      // 获取多边形中心点用于标注
      const center = turf.centerOfMass(data);
      if (center && center.geometry) {
        const centerPos = new DC.Position(
          center.geometry.coordinates[0],
          center.geometry.coordinates[1]
        );

        // 添加HTML标注
        const divIcon = new DC.DivIcon(
          centerPos,
          `<div class="danger-area-container">
                <div class="danger-area-label">${hazardouName || "危险区"}</div>
                <div class="danger-area-popup">
                  <div class="marker-title">${hazardouName || "危险区"}</div>
                  <div class="marker-content">
                    <hr class="popHr">
                    <div class="marker-item">
                      <span class="marker-label">危险区等级：</span>
                      <span class="marker-value">${
                        data.features[0].properties?.level === 1
                          ? "极高风险"
                          : data.features[0].properties?.level === 2
                          ? "高风险"
                          : "低风险"
                      }</span>
                    </div>
                    <div class="marker-item">
                      <span class="marker-label">汇流成灾时间：</span>
                      <span class="marker-value">${
                        data.features[0].properties?.disasterConvergenceTime ||
                        "--"
                      }小时</span>
                    </div>
                  </div>
                  <div style="position:absolute;bottom:-51px;left:33px"><img style="width: 96px;height:62px" src="/icons/marker/bottom.png"></div>
                </div>
              </div>`
        );

        divIcon.setStyle({
          distanceDisplayCondition: {
            near: 0,
            far: 10000,
          },
        });

        htmllayer.addOverlay(divIcon);
      }
    } catch (error) {
      console.error("Failed to add danger area:", error);
    }
  };

  //添加村社转移安置点转移路线
  const addRoad = (data, layer, htmllayer, routeId) => {
    if (
      !data ||
      !data.features ||
      !data.features[0] ||
      !data.features[0].geometry
    ) {
      console.warn("Invalid GeoJSON data for road");
      return;
    }
    try {
      let geom = null;
      geom = data.features[0].geometry.coordinates[0];

      // 转换坐标点为 Cesium 坐标数组
      const positions = geom.map((coord) =>
        Cesium.Cartesian3.fromDegrees(coord[0], coord[1])
      );

      // Define the GLSL shader source
      const flowingArrowShaderSource = `
          uniform vec4 color;
          uniform sampler2D image;
          uniform float speed;
          uniform float time;
          uniform float brightness;
          uniform float repeat; // Ensure repeat uniform is declared

          czm_material czm_getMaterial(czm_materialInput materialInput) {
              czm_material material = czm_getDefaultMaterial(materialInput);
              vec2 st = materialInput.st;
              // Scale the s-coordinate by repeat value
              float scaled_s = st.s * repeat;
              // Calculate flow offset using the scaled coordinate
              float flowOffset = fract(scaled_s - time * speed);
              // Use the calculated flow offset for texture lookup
              vec2 flowingCoord = vec2(flowOffset, st.t);
              // 获取箭头纹理 - 使用 texture 替代 texture2D
              vec4 arrowColor = texture(image, flowingCoord);
              float glow = sin(time * 8.0) * 0.3 + 0.7;
              float edge = smoothstep(0.1, 0.6, st.t) * smoothstep(0.9, 0.4, st.t);
              vec3 baseColor = color.rgb * brightness;
              vec3 glowColor = baseColor + vec3(0.2, 0.5, 0.8) * glow;
              vec3 finalColor = mix(baseColor, glowColor, edge) * (0.8 + glow * 0.2);
              // Ensure alpha considers texture and edge effects correctly
              float alpha = arrowColor.a * color.a * (0.6 + edge * 0.4);
              // Make sure only the arrow part is visible, not the transparent background if the texture has one
              // Assuming the arrow texture's non-transparent part has alpha > 0.1
              alpha = alpha * step(0.1, arrowColor.a);

              material.diffuse = finalColor;
              material.alpha = alpha;
              material.emission = finalColor * glow * 0.3;
              return material;
          }
        `;

      // // Define the material using standard Cesium API
      const flowingMaterial = new Cesium.Material({
        fabric: {
          type: "FlowingArrowPrimitive", // Unique type name for primitive
          uniforms: {
            color: Cesium.Color.fromCssColorString("#00a8ff").withAlpha(1),
            image: "/icons/marker/jt.png",
            speed: 0.5,
            time: 0, // Initial time
            brightness: 1.2,
            repeat: 30.0, // Ensure this value results in desired repetition density
          },
          source: flowingArrowShaderSource,
        },
        translucent: () => true,
      });

      // // Create the appearance 外观
      const appearance = new Cesium.PolylineMaterialAppearance({
        material: flowingMaterial,
      });

      // // Create the polyline geometry instance
      const polylineInstance = new Cesium.GeometryInstance({
        geometry: new Cesium.PolylineGeometry({
          positions: positions,
          width: 25, // Line width
          vertexFormat: Cesium.PolylineMaterialAppearance.VERTEX_FORMAT, // Required for material appearance
        }),
        id: "road-primitive-" + routeId,
      });
      // Create the primitive and add it to the scene
      const roadPrimitive = window.viewer.scene.primitives.add(
        new Cesium.Primitive({
          geometryInstances: polylineInstance,
          appearance: appearance,
          asynchronous: false, // Load synchronously for simplicity here
          id: "road-primitive-" + routeId,
        })
      );
      roadPrimitive.show = false;

      // Update the time uniform in the preRender event
      const preRenderListener = function (scene, time) {
        // Check if the primitive and material still exist
        if (
          roadPrimitive &&
          !roadPrimitive.isDestroyed &&
          appearance &&
          appearance.material &&
          appearance.material.uniforms
        ) {
          appearance.material.uniforms.time = performance.now() / 1000.0;
        } else {
          // Clean up listener if primitive is gone
          window.viewer.scene.preRender.removeEventListener(preRenderListener);
        }
      };
      window.viewer.scene.preRender.addEventListener(preRenderListener);
    } catch (error) {
      console.error("Failed to add road:", error);
    }
  };

  let firstRainLoad = true;
  /**
   * 加载雨量站
   * @param {*} params
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   * @param {string} params.stnm - 雨量站名称
   */
  const loadRainStations = async (params = {}) => {
    try {
      const { hour, treeVisible = true, stnm = "" } = params;
      changeVis({
        id: 5,
        label: "雨量站",
        type: "rain",
        treeVisible,
      });

      if (!treeVisible) {
        return {
          success: true,
          message: "雨量站已隐藏",
        };
      }

      let obj2;
      let hourNum = hour || 1;
      const res2 = await rainSumlist(hourNum, { stnm });
      let nowTime = moment().format("MM-DD HH:mm:ss");
      let beforeTime = moment()
        .subtract(hourNum, "hours")
        .format("MM-DD HH:mm:ss");
      clearLayer("rain", viewer);
      obj2 = res2.data || [];
      if (!obj2.length) {
        return {
          success: false,
          error: "暂无雨量站",
        };
      }

      let rainlayer = viewer.getLayer("rain-primitive");
      if (rainlayer) {
        rainlayer.clear();
      } else {
        rainlayer = new DC.PrimitiveLayer("rain-primitive");
        window.viewer.addLayer(rainlayer);
      }
      // console.log(rainlayer)
      // 创建HTML图层（根据降雨量选择合适的图层）
      let rainHtmllayer = null;
      if (obj2.length > 0) {
        const isRaining = obj2[0].todayRainfall > 0;
        const htmlLayerId = isRaining
          ? "rain-yes-layer-html"
          : "rain-no-layer-html";
        rainHtmllayer = viewer.getLayer(htmlLayerId);
        if (!rainHtmllayer) {
          rainHtmllayer = new DC.HtmlLayer(htmlLayerId);
          if (!isRaining) {
            rainHtmllayer.show = false; // 无雨图层默认隐藏
          }
          window.viewer.addLayer(rainHtmllayer);
        }
      }

      if (stnm) {
        const firstItem = obj2[0];
        const { lgtd, lttd } = firstItem;

        addrainData(firstItem, rainlayer, rainHtmllayer);
        baseOps._flyTo({
          longitude: lgtd,
          latitude: lttd,
          height: 3000,
        });

        return {
          success: true,
          message: `已成功定位到雨量站：${firstItem.stnm}`,
        };
      }

      obj2.forEach((item) => {
        item["nowTime"] = nowTime;
        item["beforeTime"] = beforeTime;
        if (firstRainLoad) {
          addrainData(item, rainlayer, rainHtmllayer);
        } else if (!firstRainLoad) {
          addrainData(item, rainlayer, rainHtmllayer);
        }
      });
      firstRainLoad = false;
      return {
        success: true,
        message: "加载雨量站成功",
      };
    } catch (error) {
      console.log(error);
      return {
        success: false,
        error: "加载雨量站失败",
      };
    }
  };

  /**
   * 加载河道站
   * @param {*} params
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   * @param {string} params.stnm - 河道站名称
   */
  const loadRiverStations = async (params = {}) => {
    const { treeVisible = true, stnm = "" } = params;
    changeVis({
      id: 6,
      label: "河道站",
      type: "river",
      treeVisible,
    });

    if (!treeVisible) {
      return {
        success: true,
        message: "河道站已隐藏",
      };
    }

    clearLayer("river", viewer);

    const res = await riverStationList({
      startTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      pageNum: 1,
      pageSize: 99999,
      stnm,
    });

    if (res.code === 200) {
      const obj = res.data || [];
      clearLayer("river", viewer);
      let layer = new DC.PrimitiveLayer("river-primitive");
      window.viewer.addLayer(layer);
      let hlayer = viewer.getLayer("river-layer-html");
      let list = [];
      if (!obj.length) {
        return {
          success: false,
          error: "暂无河道站",
        };
      }
      if (stnm) {
        const firstItem = obj[0];
        const { lgtd, lttd } = firstItem;

        addriverData(firstItem, layer, hlayer);
        baseOps._flyTo({
          longitude: lgtd,
          latitude: lttd,
          height: 3000,
        });

        return {
          success: true,
          message: `已成功定位到河道站：${firstItem.riverStationName}`,
        };
      }
      obj.forEach((el) => {
        el.time = moment(el.time).format("MM-DD HH:mm");
        addriverData(el, layer, hlayer);
        list.push(el);
      });
      return {
        success: true,
        message: "加载河道站成功",
      };
    } else {
      return {
        success: false,
        error: "加载河道站失败",
      };
    }
  };

  /**
   * 加载水库站
   * @param {*} params
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   * @param {string} params.stnm - 水库站名称
   */
  const loadReservoirStations = async (params = {}) => {
    const { treeVisible = true, stnm = "" } = params;
    changeVis({
      id: 7,
      label: "水库站",
      type: "reservoir",
      treeVisible,
    });
    if (!treeVisible) {
      return {
        success: true,
        message: "水库站已隐藏",
      };
    }

    let time = moment().format("YYYY-MM-DD HH:mm:ss");
    const res = await reservoirStationList({
      pageNum: 1,
      pageSize: 99999,
      time: time,
      stnm,
    });
    const obj = res.data.records || [];
    clearLayer("reservoir", viewer);
    if (!obj.length) {
      return {
        success: false,
        error: "暂无水库站",
      };
    }
    // 创建点位
    let layer = new DC.PrimitiveLayer("reservoir-primitive");
    window.viewer.addLayer(layer);
    let hlayer = viewer.getLayer("reservoir-layer-html");
    if (stnm) {
      const firstItem = obj[0];
      const { lgtd, lttd } = firstItem;

      addreservoirData(firstItem, layer, hlayer);
      baseOps._flyTo({
        longitude: lgtd,
        latitude: lttd,
        height: 3000,
      });

      return {
        success: true,
        message: `已成功定位到水库站：${firstItem.stnm}`,
      };
    }

    let list = [];
    obj.forEach((item) => {
      item["time"] = moment(time).format("MM-DD HH:mm");
      addreservoirData(item, layer, hlayer);
      list.push(item);
    });

    return {
      success: true,
      message: "加载水库站成功",
    };
  };
  /**
   * 加载河流图层
   * @param {*} params
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   * @param {string} params.rvName - 河流名称
   */
  const loadRiverLayer = async (params = {}) => {
    try {
      const { treeVisible = true, rvName = "" } = params;
      changeVis({
        id: 1,
        label: "河流层",
        type: "river1",
        treeVisible,
      });
      if (!treeVisible) {
        return {
          success: true,
          message: "河流已隐藏",
        };
      }

      // 获取流域code
      let stlyRes = await selectStlyList({ lycode: "" });
      let watersheds = formatTree(stlyRes.data);
      const lycode = watersheds[0].basinId || null;

      let paramsRiver = {
        lyCode: lycode,
        pageNum: 1,
        pageSize: 10000,
        asNext: 0,
      };

      let resRiverAry = await selectRvList(paramsRiver);
      let resRiver = formatTree(resRiverAry.data);

      if (resRiver && resRiver.length > 0) {
        try {
          let river1GroupLayer = viewer.getLayerGroup("river1");
          if (river1GroupLayer) {
            river1GroupLayer.remove();
          }
          river1GroupLayer = new DC.LayerGroup("river1");
          viewer.addLayerGroup(river1GroupLayer);

          let targetRiver = null;
          let targetLayer = null;

          // 河流默认颜色和材质
          const riverDefaultColor = DC.Color.fromCssColorString(
            "rgba(24,144,255,0.92)"
          );
          const riverDefaultMaterial = new DC.ColorMaterialProperty(
            riverDefaultColor
          );

          // 根据river id 查询 geom
          for (const item of resRiver) {
            let riverGeoJson = null;
            let rg = await getRvInfo(item.id);
            riverGeoJson = rg.data.geom || {};

            // 加载河流图层数据
            let river1layer = new DC.GeoJsonLayer(
              "river1layer" + item.id,
              riverGeoJson
            );
            river1GroupLayer.addLayer(river1layer);

            // 判断是否为搜索的河流
            const isTargetRiver =
              rvName && item.rvName && item.rvName.includes(rvName);

            // 设置河流样式，所有河流使用相同颜色
            river1layer.eachOverlay((overlay) => {
              if (overlay.polyline) {
                DC.Util.merge(overlay.polyline, {
                  material: riverDefaultMaterial,
                  clampToGround: false,
                  width: 4, // 保持所有河流宽度一致
                  classificationType: Cesium.ClassificationType.BOTH,
                });
              }
            });

            // 如果是查询的河流，保存这个河流的信息用于后续定位和突出显示
            if (isTargetRiver) {
              targetRiver = {
                item,
                riverGeoJson,
              };
              targetLayer = river1layer;
            }
          }

          // 如果找到了指定的河流，飞行到该河流位置并添加突出显示效果
          if (rvName && targetRiver && targetLayer) {
            // 使用基础操作封装的飞行到边界方法
            baseOps._flyToBounds({
              geojson: targetRiver.riverGeoJson,
              expandFactor: 0.5,
              duration: 2.5,
            });

            // 创建高级材质突出显示效果
            const totalDuration = 10000; // 突出显示持续时间(毫秒)

            // 创建流动光效
            const glowMaterial = new DC.PolylineGlowMaterialProperty({
              glowPower: 0.15,
              color: DC.Color.fromCssColorString("rgba(64,224,208,0.95)"), // 蓝绿色调
            });

            // 流动材质
            const flowMaterial = new DC.PolylineFlowMaterialProperty({
              color: DC.Color.fromCssColorString("rgba(24,144,255,0.9)"),
              speed: 5,
              percent: 0.3,
              gradient: 0.1,
            });

            // 设置高亮材质
            targetLayer.eachOverlay((overlay) => {
              if (overlay.polyline) {
                DC.Util.merge(overlay.polyline, {
                  material: flowMaterial,
                  width: 6,
                  shadows: Cesium.ShadowMode.ENABLED,
                  classificationType: Cesium.ClassificationType.BOTH,
                  depthFailMaterial: glowMaterial,
                });
              }
            });

            // 恢复到默认样式
            setTimeout(() => {
              if (targetLayer) {
                targetLayer.eachOverlay((overlay) => {
                  if (overlay.polyline) {
                    DC.Util.merge(overlay.polyline, {
                      material: riverDefaultMaterial,
                      width: 4,
                      shadows: Cesium.ShadowMode.DISABLED,
                    });
                  }
                });
              }
            }, totalDuration);

            return {
              success: true,
              message: `已成功定位到河流：${targetRiver.item.rvName}`,
            };
          } else if (rvName) {
            // 如果指定了rvName但未找到匹配的河流
            return {
              success: false,
              error: `未找到名称包含"${rvName}"的河流`,
            };
          }

          return {
            success: true,
            message: "加载河流图层成功",
          };
        } catch (e) {
          console.error(e, "e");
          return {
            success: false,
            error: "加载河流图层失败",
          };
        }
      } else {
        return {
          success: false,
          error: "暂无河流数据",
        };
      }
    } catch (e) {
      console.error(e, "e");
      return {
        success: false,
        error: "加载河流图层失败",
      };
    }
  };

  /**
   * 加载企事业单位点
   * @param {*} params
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   * @param {string} params.name - 企事业单位名称
   */
  const loadEnterpriseList = async (params = {}) => {
    try {
      const { treeVisible = true, name = "" } = params;
      changeVis({
        id: 13,
        label: "企事业单位点位",
        type: "enterprise",
        treeVisible,
      });
      if (!treeVisible) {
        return {
          success: true,
          message: "企事业单位已隐藏",
        };
      }

      createGLayer("enterprise"); // 确保图层已创建
      const res = await queryEnterpriseList({
        pageNum: 1,
        pageSize: 20,
        name,
      });

      clearLayer("enterprise", viewer);
      let layer = new DC.PrimitiveLayer("enterprise-primitive");
      window.viewer.addLayer(layer);
      let hlayer = viewer.getLayer("enterprise-layer-html");

      if (res && res.rows) {
        if (!res.rows.length) {
          return {
            success: false,
            error: "暂无企事业单位",
          };
        }
        if (name) {
          const firstItem = res.rows[0];
          const { latitude, longitude } = firstItem;

          baseOps._flyTo({
            longitude,
            latitude,
            height: 3000,
          });

          return {
            success: true,
            message: `已成功定位到企事业单位：${firstItem.name}`,
          };
        }
        res.rows.forEach((item) => {
          addEnterpriseData(item, layer, hlayer);
        });
      }

      return {
        success: true,
        message: "加载企事业单位成功",
      };
    } catch (error) {
      console.error("Failed to get enterprise list:", error);
    }
  };

  /**
   * 加载居民户点位
   * @param {*} params
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   * @param {string} params.name - 居民户名称
   */
  const loadResidentList = async (params = {}) => {
    try {
      const { treeVisible = true, name = "" } = params;
      changeVis({
        id: 14,
        label: "居民户点位",
        type: "household",
        treeVisible,
      });
      if (!treeVisible) {
        return {
          success: true,
          message: "居民户已隐藏",
        };
      }

      createGLayer("resident"); // 确保图层已创建
      const res = await residentList({
        pageNum: 1,
        pageSize: 20,
        name,
      });

      clearLayer("resident", viewer);
      let layer = new DC.PrimitiveLayer("resident-primitive");
      window.viewer.addLayer(layer);
      let hlayer = viewer.getLayer("resident-layer-html");

      if (res.data && res.data.records) {
        if (!res.data.records.length) {
          return {
            success: false,
            error: "暂无居民户点",
          };
        }

        if (name) {
          const firstItem = res.data.records[0];
          const { lgtd: longitude, lttd: latitude } = firstItem;

          addResidentData(firstItem, layer, hlayer);
          baseOps._flyTo({
            longitude,
            latitude,
            height: 3000,
          });

          return {
            success: true,
            message: `已成功定位到居民户：${firstItem.name}`,
          };
        }

        res.data.records.forEach((item) => {
          addResidentData(item, layer, hlayer);
        });
      }

      return {
        success: true,
        message: "加载居民户成功",
      };
    } catch (error) {
      console.error("Failed to get resident list:", error);
    }
  };

  /**
   * 加载安置点
   * @param {*} params
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   * @param {string} params.name - 安置点名称
   */
  const loadPlaceList = async (params = {}) => {
    try {
      const { treeVisible = true, name = "" } = params;
      changeVis({
        id: 15,
        label: "安置点点位",
        type: "place",
        treeVisible,
      });
      if (!treeVisible) {
        return {
          success: true,
          message: "安置点已隐藏",
        };
      }

      createGLayer("place"); // 确保图层已创建
      const res = await placeList({
        pageNum: 1,
        pageSize: 20,
        name,
      });

      clearLayer("place", viewer);
      let layer = new DC.PrimitiveLayer("place-primitive");
      window.viewer.addLayer(layer);
      let hlayer = viewer.getLayer("place-layer-html");

      if (res.data && res.data.records) {
        if (!res.data.records.length) {
          return {
            success: false,
            error: "暂无安置点",
          };
        }

        if (name) {
          const firstItem = res.data.records[0];
          const { lgtd: longitude, lttd: latitude } = firstItem;

          addPlaceData(firstItem, layer, hlayer);
          baseOps._flyTo({
            longitude,
            latitude,
            height: 3000,
          });

          return {
            success: true,
            message: `已成功定位到安置点：${firstItem.name}`,
          };
        }

        res.data.records.forEach((item) => {
          addPlaceData(item, layer, hlayer);
        });
      }

      return {
        success: true,
        message: "加载安置点成功",
      };
    } catch (error) {
      console.error("Failed to get place list:", error);
    }
  };

  /**
   * 加载风险隐患点
   * @param {*} params
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   * @param {string} params.name - 风险隐患名称
   */
  const loadRiskProblemList = async (params = {}) => {
    try {
      const { treeVisible = true, name = "" } = params;
      changeVis({
        id: 16,
        label: "风险隐患点位",
        type: "risk",
        treeVisible,
      });
      if (!treeVisible) {
        return {
          success: true,
          message: "风险隐患已隐藏",
        };
      }

      createGLayer("risk-problem"); // 确保图层已创建
      const res = await queryRiskProblem({
        pageNum: 1,
        pageSize: 20,
        name,
      });

      clearLayer("risk-problem", viewer);
      let layer = new DC.PrimitiveLayer("risk-problem-primitive");
      window.viewer.addLayer(layer);
      let hlayer = viewer.getLayer("risk-problem-layer-html");

      if (res && res.rows) {
        if (!res.rows.length) {
          return {
            success: false,
            error: "暂无风险隐患",
          };
        }

        if (name) {
          const firstItem = res.rows[0];
          const { longitude, latitude } = firstItem;

          addRiskProblemData(firstItem, layer, hlayer);
          baseOps._flyTo({
            longitude,
            latitude,
            height: 3000,
          });

          return {
            success: true,
            message: `已成功定位到风险隐患点：${firstItem.name}`,
          };
        }

        res.rows.forEach((item) => {
          addRiskProblemData(item, layer, hlayer);
        });

        return {
          success: true,
          message: "加载风险隐患成功",
        };
      }
    } catch (error) {
      console.error("Failed to get risk problem list:", error);
    }
  };

  /**
   * 加载危险区
   * @param {*} params
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   * @param {string} params.name - 危险区名称
   */
  const loadDangerousArea = async (params = {}) => {
    try {
      const { treeVisible = true, name = "" } = params;
      changeVis({
        id: 21,
        label: "危险区",
        type: "hazardous",
        treeVisible,
      });
      if (!treeVisible) {
        return {
          success: true,
          message: "危险区已隐藏",
        };
      }

      createGLayer("dangerArea"); // 确保图层已创建
      const res = await dangerousAreaList({
        pageNum: 1,
        pageSize: 20,
        name,
      });

      clearLayer("dangerArea", viewer);
      let layer = viewer.getLayer("dangerArea-layer");
      let hlayer = viewer.getLayer("dangerArea-layer-html");
      if (res && res.data.records) {
        if (!res.data.records.length) {
          return {
            success: false,
            error: "暂无危险区",
          };
        }

        if (name) {
          const firstItem = res.data.records[0];

          let hazardouId = firstItem.id;
          const detailResult = await dangerousAreaDetail(hazardouId);
          if (detailResult && detailResult.data.geom) {
            let geom = JSON.parse(detailResult.data.geom);
            addDangerAreaData(geom, layer, hlayer, detailResult.data.name);

            // 使用基础操作封装的飞行到边界方法
            baseOps._flyToBounds({
              geojson: geom,
              expandFactor: 1,
              orientation: { heading: 0, pitch: -90, roll: 0 }, // 垂直俯视视角
              duration: 2.5, // 持续时间(秒)
            });
          } else {
            // 如果获取边界数据失败，回退到简单的flyTo方法
            viewer.flyTo(layer, 2.5);
          }

          return {
            success: true,
            message: `已成功定位到危险区：${firstItem.name}`,
          };
        }

        const resLength = res.data.records.length;
        const loopTimes = Math.min(resLength, 5);
        // 最多展示五个危险区
        for (let i = 0; i < loopTimes; i++) {
          let hazardouId = res.data.records[i].id;
          const detailResult = await dangerousAreaDetail(hazardouId);
          if (detailResult && detailResult.data.geom) {
            let geom = JSON.parse(detailResult.data.geom);
            addDangerAreaData(geom, layer, hlayer, detailResult.data.name);
          }
        }

        return {
          success: true,
          message: "加载危险区成功",
        };
      }
    } catch (error) {
      console.error("Failed to get danger area list:", error);
    }
  };

  /**
   * 加载转移路线
   * @param {*} params
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   * @param {string} params.villageName - 转移路线名称
   */
  const loadTransferNum = async (params = {}) => {
    try {
      const { treeVisible = true, villageName = "" } = params;
      changeVis({
        id: 22,
        label: "转移路线",
        type: "road",
        treeVisible,
      });
      if (!treeVisible) {
        return {
          success: true,
          message: "转移路线已隐藏",
        };
      }

      const res = await queryVillagePlacementList({
        pageNum: 1,
        pageSize: 20,
        villageName,
      });
      if (res && res.rows) {
        if (!res.rows.length) {
          return {
            success: false,
            error: "暂无转移路线",
          };
        }

        if (villageName) {
          const firstItem = res.rows[0];
          let id = firstItem.id;
          if (!id) return;
          const detailResult = await queryVillagePlacementDetail(id);
          if (detailResult && detailResult.data.geom) {
            const geom =
              typeof detailResult.data.geom === "object"
                ? detailResult.data.geom
                : JSON.parse(detailResult.data.geom);

            addRoad(geom, null, null, id);

            // 获取路线的中心点用于飞行定位
            if (
              geom &&
              geom.features &&
              geom.features[0] &&
              geom.features[0].geometry
            ) {
              const coordinates = geom.features[0].geometry.coordinates[0];

              if (coordinates && coordinates.length > 0) {
                // 计算路线中心点，可以选择路线的中间点或起点
                // 这里选择路线的中间点作为飞行目标
                const midIndex = Math.floor(coordinates.length / 2);
                const midPoint = coordinates[midIndex];

                if (midPoint && midPoint.length >= 2) {
                  // 使用基础操作封装的飞行到边界方法
                  baseOps._flyToBounds({
                    geojson: geom,
                    expandFactor: 2,
                    orientation: { heading: 0, pitch: -90, roll: 0 }, // 垂直俯视视角
                    duration: 2.5, // 持续时间(秒)
                  });
                } else {
                  // 如果坐标点数据无效，回退到简单的flyTo方法
                  baseOps._flyTo({
                    longitude: midPoint[0],
                    latitude: midPoint[1],
                    height: 3000, // 设置一个适当的高度以便查看整个路线
                  });
                }
              }
            }
          }

          changeVis({
            id: 15,
            label: "安置点点位",
            type: "place",
            treeVisible: true,
          });

          return {
            success: true,
            message: `已成功定位到转移路线：${firstItem.placementName}`,
          };
        }

        for (let i = 0; i < res.rows.length; i++) {
          let id = res.rows[i].id;
          const detailResult = await queryVillagePlacementDetail(id);
          if (detailResult && detailResult.data.geom) {
            typeof detailResult.data.geom === "object"
              ? addRoad(detailResult.data.geom, null, null, id)
              : addRoad(JSON.parse(detailResult.data.geom), null, null, id);
          }

          changeVis({
            id: 15,
            label: "安置点点位",
            type: "place",
            treeVisible: true,
          });

          return {
            success: true,
            message: "加载转移路线成功",
          };
        }
      } else {
        return {
          success: false,
          error: "暂无转移路线",
        };
      }
    } catch (error) {
      console.error("Failed to get danger area list:", error);
    }
  };

  /**
   * 加载村社点
   * @param {Object} params - 操作参数
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   * @param {string} params.adnm - 村名称/村社名称
   */
  const locateToVillage = async (params = {}) => {
    const { treeVisible = true, adnm = "" } = params;

    changeVis({
      id: 12,
      label: "村社点位",
      type: "township",
      treeVisible,
    });
    if (!treeVisible) {
      return {
        success: true,
        message: "村社已隐藏",
      };
    }

    createGLayer("township");

    const res = await villageList({
      pageNum: 1,
      pageSize: 20,
      adnm,
    });

    if (res.code === 200) {
      const records = res.data.records || [];
      if (records.length) {
        let layer = new DC.PrimitiveLayer("township-primitive");
        window.viewer.addLayer(layer);
        let hlayer = viewer.getLayer("township-layer-html");
        if (adnm) {
          const firstVillage = records[0];
          const { lgtd, lttd } = firstVillage;
          baseOps._flyTo({
            longitude: lgtd,
            latitude: lttd,
            height: 3000,
          });

          changeVis({
            id: 12,
            label: "村社点位",
            type: "township",
            treeVisible: true,
          });

          changeVis({
            id: 13,
            label: "企事业单位点位",
            type: "enterprise",
            treeVisible: true,
          });

          changeVis({
            id: 14,
            label: "居民户点位",
            type: "household",
            treeVisible: true,
          });

          changeVis({
            id: 15,
            label: "安置点点位",
            type: "place",
            treeVisible: true,
          });

          changeVis({
            id: 16,
            label: "风险隐患点位",
            type: "risk",
            treeVisible: true,
          });

          changeVis({
            id: 21,
            label: "危险区",
            type: "hazardous",
            treeVisible: true,
          });

          changeVis({
            id: 22,
            visible: false,
            label: "转移路线",
            type: "road",
            treeVisible: true,
          });

          addTownshipData(firstVillage, layer, hlayer);

          return {
            success: true,
            message: `已成功定位到${adnm}`,
          };
        } else {
          res.data.records.forEach((item) => {
            addTownshipData(item, layer, hlayer);
          });
        }
      } else {
        if (adnm) {
          return {
            success: false,
            error: `未找到${adnm}`,
          };
        } else {
          return {
            success: false,
            error: "暂无村社",
          };
        }
      }
    }
  };

  /**
   * 加载水闸
   * @param {*} params
   * @param {Boolean} params.treeVisible 控制显示还是隐藏
   */
  const loadWaterGateList = async (params = {}) => {
    try {
      const { treeVisible = true } = params;
      changeVis({
        id: 23,
        label: "水闸",
        type: "waterGate",
        treeVisible,
      });

      if (!treeVisible) {
        return {
          success: true,
          message: "水闸已隐藏",
        };
      }
      // const res = await queryWaterGateList({
      //   pageNum: 1,
      //   pageSize: 20
      // });
      // if(res && res.rows) {
      //   res.rows.forEach((item) => {
      //     addWaterGateData(item, layer, hlayer);
      //   });
      // }
      let fakeData = [
        {
          lgtd: 91.048,
          lttd: 29.629,
          name: "1#闸",
        },
        {
          lgtd: 91.067,
          lttd: 29.638,
          name: "2#闸",
        },
        {
          lgtd: 91.097,
          lttd: 29.644,
          name: "3#闸",
        },
        {
          lgtd: 91.129,
          lttd: 29.637,
          name: "4#闸",
        },
        {
          lgtd: 91.165,
          lttd: 29.64,
          name: "1#闸",
        },
      ];
      let primitiveLayer = new DC.PrimitiveLayer("gate-primitive");
      window.viewer.addLayer(primitiveLayer);
      for (let i = 0; i < fakeData.length; i++) {
        addWaterGateData(fakeData[i], primitiveLayer);
      }
    } catch (error) {
      console.error("Failed to get water gate list:", error);
    }
  };

  return {
    // 加载雨量站
    loadRainStations,
    // 加载河道站
    loadRiverStations,
    // 加载水库站
    loadReservoirStations,
    // 加载河流图层
    loadRiverLayer,
    // 加载企事业单位点
    loadEnterpriseList,
    // 加载居民住宅点位
    loadResidentList,
    // 加载安置点
    loadPlaceList,
    // 加载风险隐患点
    loadRiskProblemList,
    // 加载危险区
    loadDangerousArea,
    // 加载转移路线
    loadTransferNum,
    // 加载村社点
    locateToVillage,
    // 加载水闸
    loadWaterGateList,
  };
}
