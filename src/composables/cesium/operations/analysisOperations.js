import { ElMessage } from "element-plus"

/**
 * 分析相关操作
 * 包含空间分析、统计分析等功能
 * @param {Object} viewer - Cesium Viewer实例
 * @param {Object} baseOps - 基础操作实例
 * @returns {Object} 分析操作集合
 */
export function getAnalysisOperations(viewer, baseOps) {
  const Cesium = DC.getLib("Cesium")

  /**
   * 计算多边形球面面积
   * @private
   * @param {Array<Array<number>>} positions - 点位数组
   * @returns {number} 面积(平方米)
   */
  function calculateSphericalArea(positions) {
    // 创建Cesium多边形实体以使用其面积计算
    const polygonHierarchy = new Cesium.PolygonHierarchy(
      positions.map(pos => Cesium.Cartesian3.fromDegrees(pos[0], pos[1]))
    )

    // 使用Cesium计算球面多边形面积
    return Cesium.PolygonGeometry.computeArea(polygonHierarchy)
  }

  /**
   * 计算多边形中心点
   * @private
   * @param {Array<Array<number>>} positions - 点位数组
   * @returns {Array<number>} 中心点[lon, lat]
   */
  function calculateCenter(positions) {
    let sumLon = 0
    let sumLat = 0

    for (const pos of positions) {
      sumLon += pos[0]
      sumLat += pos[1]
    }

    return [sumLon / positions.length, sumLat / positions.length]
  }

  return {
    /**
     * 测量距离
     * @param {Object} params - 操作参数
     * @param {Array<Array<number>>} params.positions - 点位数组，格式：[[经度,纬度], [经度,纬度], ...]
     */
    async measureDistance(params) {
      try {
        const { positions } = params
        if (!positions || !Array.isArray(positions) || positions.length < 2) {
          return {
            success: false,
            error: "测量点位不足"
          }
        }

        // 清除之前的测量结果
        let measureLayer = viewer.getLayer("measureLayer")
        if (!measureLayer) {
          measureLayer = new DC.VectorLayer("measureLayer")
          viewer.addLayer(measureLayer)
        } else {
          measureLayer.clear()
        }

        // 创建线段
        const linePositions = positions.map(pos => `${pos[0]},${pos[1]}`).join(";")
        const line = new DC.Polyline(linePositions)

        // 设置线段样式
        line.setStyle({
          width: 3,
          material: new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.YELLOW
          })
        })

        // 计算总距离
        let totalDistance = 0
        const positionCartesians = positions.map(pos => Cesium.Cartesian3.fromDegrees(pos[0], pos[1]))

        for (let i = 0; i < positionCartesians.length - 1; i++) {
          const distance = Cesium.Cartesian3.distance(positionCartesians[i], positionCartesians[i + 1])
          totalDistance += distance
        }

        // 添加线段到图层
        measureLayer.addOverlay(line)

        // 在终点添加标签显示距离
        const lastPos = positions[positions.length - 1]
        const label = new DC.Label(lastPos[0], lastPos[1])
        label.setStyle({
          text: `距离: ${(totalDistance / 1000).toFixed(2)} 公里`,
          font: "14px sans-serif",
          fillColor: Cesium.Color.WHITE,
          backgroundColor: Cesium.Color.fromCssColorString("rgba(38, 38, 38, 0.85)"),
          showBackground: true,
          pixelOffset: new Cesium.Cartesian2(0, -30),
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        })

        measureLayer.addOverlay(label)

        ElMessage.success("测量完成")
        return {
          success: true,
          data: {
            distance: totalDistance,
            distanceKm: totalDistance / 1000
          }
        }
      } catch (error) {
        console.error("距离测量失败:", error)
        return {
          success: false,
          error: error.message
        }
      }
    },

    /**
     * 测量面积
     * @param {Object} params - 操作参数
     * @param {Array<Array<number>>} params.positions - 点位数组，格式：[[经度,纬度], [经度,纬度], ...]
     */
    async measureArea(params) {
      try {
        const { positions } = params
        if (!positions || !Array.isArray(positions) || positions.length < 3) {
          return {
            success: false,
            error: "测量点位不足，至少需要3个点"
          }
        }

        // 创建面图层
        let areaLayer = viewer.getLayer("areaLayer")
        if (!areaLayer) {
          areaLayer = new DC.VectorLayer("areaLayer")
          viewer.addLayer(areaLayer)
        } else {
          areaLayer.clear()
        }

        // 格式化点位
        const polygonPositions = positions.map(pos => `${pos[0]},${pos[1]}`).join(";")
        const polygon = new DC.Polygon(polygonPositions)

        // 计算面积 (使用球面几何算法计算)
        const area = calculateSphericalArea(positions)

        // 设置面样式
        polygon.setStyle({
          material: new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW.withAlpha(0.3)),
          outline: true,
          outlineColor: Cesium.Color.YELLOW,
          outlineWidth: 2
        })

        // 添加到图层
        areaLayer.addOverlay(polygon)

        // 添加面积标签
        const center = calculateCenter(positions)
        const label = new DC.Label(center[0], center[1])

        // 单位转换
        let areaText = ""
        if (area < 1000000) {
          areaText = `面积: ${area.toFixed(2)} 平方米`
        } else {
          areaText = `面积: ${(area / 1000000).toFixed(4)} 平方公里`
        }

        label.setStyle({
          text: areaText,
          font: "14px sans-serif",
          fillColor: Cesium.Color.WHITE,
          backgroundColor: Cesium.Color.fromCssColorString("rgba(38, 38, 38, 0.85)"),
          showBackground: true,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        })

        areaLayer.addOverlay(label)

        ElMessage.success("面积测量完成")
        return {
          success: true,
          data: {
            area: area,
            areaKm2: area / 1000000
          }
        }
      } catch (error) {
        console.error("面积测量失败:", error)
        return {
          success: false,
          error: error.message
        }
      }
    }
  }
}
