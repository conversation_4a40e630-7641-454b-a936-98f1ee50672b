/**
 * Cesium操作模块索引
 *
 * 本文件作为所有Cesium操作模块的集中导出点，方便管理和使用。
 * 默认导出的模块会在Cesium初始化时直接加载，其他模块则通过动态导入按需加载。
 *
 * 模块组织结构：
 * - navigationOperations: 导航相关操作（缩放、定位等）
 * - analysisOperations: 分析相关操作（测量距离、面积等）
 * - terrainOperations: 地形相关操作（高程分析等）
 * - floodPreventionOperations: 防洪四预相关操作（河流、监测站点等点位加载）
 */

/**
 * 常用操作模块导出
 * 这些模块会在初始化时直接加载
 */
export { getNavigationOperations } from "./navigationOperations";
export { getFloodPreventionOperations } from "./floodPreventionOperations";
export { getForecastDispatchPlan } from "./forecastDispatchPlan";

/**
 * 按需加载的操作模块
 * 这些模块不会在初始化时加载，而是通过loadOperationModule方法动态加载
 *
 * 使用示例：
 * ```javascript
 * // 动态加载分析模块
 * await executeOperation({
 *   type: "map",
 *   operation: "loadOperationModule",
 *   params: { moduleName: "analysis" }
 * });
 *
 * // 加载完成后即可使用该模块的操作
 * await executeOperation({
 *   type: "map",
 *   operation: "measureDistance",
 *   params: { positions: [[120.1, 30.2], [120.2, 30.3]] }
 * });
 * ```
 */
// 这些模块通过动态import加载，不需要在这里导出
// export { getTerrainOperations } from './terrainOperations'
// export { getAnalysisOperations } from './analysisOperations'
