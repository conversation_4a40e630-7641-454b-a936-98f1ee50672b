import * as turf from "@turf/turf";
import {
  getNavigationOperations,
  getFloodPreventionOperations,
  getForecastDispatchPlan,
} from "./operations/index";

/**
 * 基础地图操作类
 * 提供通用的地图操作方法
 */
export class BaseOperations {
  constructor(viewer) {
    this.viewer = viewer;
    this.Cesium = DC.getLib("Cesium");
  }

  /**
   * 获取相机高度
   * @protected
   */
  _getHeight() {
    return this.viewer.camera.positionCartographic.height;
  }

  /**
   * 获取相机中心点坐标
   * @protected
   */
  _getCenterPosition() {
    const result = this.viewer.camera.pickEllipsoid(
      new this.Cesium.Cartesian2(
        this.viewer.canvas.clientWidth / 2,
        this.viewer.canvas.clientHeight / 2
      )
    );
    const curPosition =
      this.Cesium.Ellipsoid.WGS84.cartesianToCartographic(result);
    const lon = (curPosition.longitude * 180) / Math.PI;
    const lat = (curPosition.latitude * 180) / Math.PI;
    const height = this._getHeight();

    return { lon, lat, height };
  }

  /**
   * 飞行到指定位置 - 经纬度
   * @param {Object} params - 操作参数
   * @param {number|string} params.longitude - 经度
   * @param {number|string} params.latitude - 纬度
   * @param {number|string} params.height - 高度(可选)
   * @param {number|string} params.duration - 飞行时间(秒)(可选)
   */
  async _flyTo(params) {
    try {
      const { longitude, latitude, height = 3000, duration = 2.5 } = params;

      // 参数验证
      if (!latitude || !longitude) {
        return { success: false };
      }

      // 类型转换和验证
      const lon = Number(longitude);
      const lat = Number(latitude);
      const h = Number(height);
      const dur = Number(duration);

      // 验证转换后的数值是否有效
      if (isNaN(lon) || isNaN(lat) || isNaN(h) || isNaN(dur)) {
        return { success: false, error: "参数类型转换失败" };
      }

      // 验证经纬度范围
      if (lon < -180 || lon > 180 || lat < -90 || lat > 90) {
        return { success: false, error: "经纬度范围无效" };
      }

      await viewer.camera.flyTo({
        destination: this.Cesium.Cartesian3.fromDegrees(lon, lat, h),
        duration: dur,
      });
      return { success: true };
    } catch (error) {
      console.error("Error in flyTo operation:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 从GeoJSON几何数据计算边界范围
   * @param {Object} geojson - GeoJSON格式的地理数据，必须包含features数组
   * @param {number} [expandFactor=0.3] - 边界扩展系数，值越大视野越广
   *   - 0: 不扩展边界
   *   - 0.3: 默认值，向各方向扩展30%
   *   - 0.5: 推荐用于小面积区域如危险区，提供更广阔视野
   *   - 0.4: 推荐用于线性要素如转移路线
   *   - >1.0: 不建议使用过大值，可能导致视野过于宽广
   * @returns {Array|null} 边界范围数组 [minX, minY, maxX, maxY] 或 null
   */
  _calculateBoundsFromGeom(geojson, expandFactor = 0.3) {
    if (!geojson || !geojson.features || geojson.features.length === 0) {
      return null;
    }

    try {
      // 使用turf计算GeoJSON的边界
      const bbox = turf.bbox(geojson);

      // 扩展边界范围，使视角高度更高
      // 计算当前边界的宽度和高度
      const width = bbox[2] - bbox[0];
      const height = bbox[3] - bbox[1];

      // 扩展边界 (根据传入的扩展因子来增加边界范围)
      bbox[0] -= width * expandFactor; // minX
      bbox[1] -= height * expandFactor; // minY
      bbox[2] += width * expandFactor; // maxX
      bbox[3] += height * expandFactor; // maxY

      return bbox; // 返回扩展后的边界 [minX, minY, maxX, maxY]
    } catch (error) {
      console.error("计算GeoJSON边界失败:", error);
      return null;
    }
  }

  /**
   * 飞行到指定边界范围
   * @param {Object} params - 操作参数
   * @param {Object} params.geojson - GeoJSON格式的地理数据
   * @param {number} [params.expandFactor=0.3] - 边界扩展系数
   * @param {Object} [params.orientation] - 相机朝向参数
   * @param {number} [params.orientation.heading=0] - 相机朝向角度
   * @param {number} [params.orientation.pitch=-90] - 相机俯仰角度
   * @param {number} [params.orientation.roll=0] - 相机翻滚角度
   * @param {number} [params.duration=2.5] - 飞行时间(秒)
   * @returns {Object} 操作结果
   */
  async _flyToBounds(params) {
    try {
      const {
        geojson,
        expandFactor = 0.3,
        orientation = { heading: 0, pitch: -90, roll: 0 },
        duration = 2.5,
      } = params;

      if (!geojson) {
        return { success: false, error: "未提供GeoJSON数据" };
      }

      const bounds = this._calculateBoundsFromGeom(geojson, expandFactor);

      if (!bounds) {
        return { success: false, error: "计算边界失败" };
      }

      await this.viewer.flyToBounds(
        bounds,
        orientation,
        null, // 回调函数参数为null
        duration
      );

      return { success: true };
    } catch (error) {
      console.error("飞行到边界范围失败:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

// 保存已加载的操作模块
const loadedOperationModules = new Map();

/**
 * 创建并组合所有操作
 * @param {Object} viewer - Cesium viewer实例
 * @returns {Object} 所有操作的集合
 */
export function createOperations(viewer) {
  if (!viewer) {
    console.warn("Cesium viewer未初始化，操作可能无法正常工作");
    return {};
  }

  // 创建基础操作实例
  const baseOperations = new BaseOperations(viewer);

  // cesium 操作模块
  const navigationOps = getNavigationOperations(viewer, baseOperations);
  // 防洪四预操作模块
  const floodPreventionOps = getFloodPreventionOperations(
    viewer,
    baseOperations
  );
  // 预测调度计划操作模块
  const forecastDispatchPlanOps = getForecastDispatchPlan(
    viewer,
    baseOperations
  );

  // 添加动态加载功能
  const operations = {
    ...navigationOps,
    ...floodPreventionOps,
    ...forecastDispatchPlanOps,
    /**
     * 动态加载操作模块
     * 仅在需要时才加载特定模块，减少初始加载体积
     * @param {string} moduleName - 模块名称
     * @returns {Promise<Object>} - 加载的操作对象
     */
    async loadOperationModule(moduleName) {
      try {
        // 如果模块已加载，直接返回
        if (loadedOperationModules.has(moduleName)) {
          return loadedOperationModules.get(moduleName);
        }

        let moduleOperations = {};

        // 根据模块名称动态加载
        switch (moduleName) {
          // case "terrain":
          //   // 动态导入地形操作模块 - 暂时不需要
          //   const { getTerrainOperations } = await import("./operations/terrainOperations.js")
          //   moduleOperations = getTerrainOperations(viewer, baseOperations)
          //   break

          case "analysis":
            // 动态导入分析操作模块
            const { getAnalysisOperations } = await import(
              "./operations/analysisOperations.js"
            );
            moduleOperations = getAnalysisOperations(viewer, baseOperations);
            break;

          default:
            throw new Error(`未知的操作模块: ${moduleName}`);
        }

        // 将模块操作注册到已加载模块和当前操作实例中
        loadedOperationModules.set(moduleName, moduleOperations);

        // 将操作方法添加到当前operations对象中
        Object.assign(operations, moduleOperations);

        return moduleOperations;
      } catch (error) {
        console.error(`加载操作模块 ${moduleName} 失败:`, error);
        throw error;
      }
    },
  };

  return operations;
}
