# Cesium操作模块使用指南

## 简介

本模块提供了一套模块化、可扩展的Cesium地图操作框架，具有以下特点：

- **模块化设计**：按功能领域分类，便于维护和协作开发
- **按需加载**：只加载需要的功能模块，优化性能
- **统一接口**：所有操作遵循相同的调用方式，便于使用
- **易于扩展**：可以方便地添加新的操作类型

## 基本用法

### 引入和初始化

```javascript
import { useCesium } from "@/composables/useCesium"

// 在组件中使用
const { executeOperation, parseCesiumData } = useCesium()
```

### 执行操作

所有操作都通过`executeOperation`方法统一调用：

```javascript
// 执行放大操作
const result = await executeOperation({
  type: "map",
  operation: "zoomBig"
})

// 执行带参数的操作
const result = await executeOperation({
  type: "map",
  operation: "flyTo",
  params: {
    longitude: 120.2,
    latitude: 30.3,
    height: 5000,
    duration: 2.0
  }
})
```

## 操作模块说明

### 1. 导航操作 (navigationOperations)

默认加载的模块，提供基础的地图导航功能：

- **zoomBig**: 放大地图
  ```javascript
  await executeOperation({
    type: "map",
    operation: "zoomBig"
  })
  ```

- **zoomSmall**: 缩小地图
  ```javascript
  await executeOperation({
    type: "map",
    operation: "zoomSmall"
  })
  ```

- **flyTo**: 飞行到指定位置
  ```javascript
  await executeOperation({
    type: "map",
    operation: "flyTo",
    params: {
      longitude: 120.2,
      latitude: 30.3,
      height: 5000,    // 可选，默认50000
      duration: 2.0    // 可选，默认2.0秒
    }
  })
  ```

### 2. 防洪四预操作 (floodPreventionOperations)

默认加载的模块，提供防洪相关的图层加载功能：

- **loadStations**: 加载监测站点
  ```javascript
  // 加载雨量站
  await executeOperation({
    type: "map",
    operation: "loadStations",
    params: {
      type: "rain",
      data: rainStationData
    }
  })

  // 加载河道站
  await executeOperation({
    type: "map",
    operation: "loadStations",
    params: {
      type: "river",
      data: riverStationData
    }
  })

  // 加载水库站
  await executeOperation({
    type: "map",
    operation: "loadStations",
    params: {
      type: "reservoir",
      data: reservoirStationData
    }
  })
  ```

- **loadRiverLayer**: 加载河流图层
  ```javascript
  await executeOperation({
    type: "map",
    operation: "loadRiverLayer",
    params: {
      data: riverGeoJsonData
    }
  })
  ```

- **clearLayer**: 清除指定图层
  ```javascript
  await executeOperation({
    type: "map",
    operation: "clearLayer",
    params: {
      name: "river1"  // 图层组名称
    }
  })
  ```

### 3. 分析操作 (analysisOperations)

按需加载的模块，提供空间分析功能：

- **measureDistance**: 测量距离
  ```javascript
  // 先加载分析模块
  await executeOperation({
    type: "map",
    operation: "loadOperationModule",
    params: { moduleName: "analysis" }
  })

  // 执行距离测量
  const result = await executeOperation({
    type: "map",
    operation: "measureDistance",
    params: {
      positions: [
        [120.1, 30.2],
        [120.2, 30.3]
      ]
    }
  })
  ```

- **measureArea**: 测量面积
  ```javascript
  const result = await executeOperation({
    type: "map",
    operation: "measureArea",
    params: {
      positions: [
        [120.1, 30.2],
        [120.2, 30.3],
        [120.3, 30.2]
      ]
    }
  })
  ```

## 从Markdown代码块解析操作

聊天消息中的Cesium操作通过代码块定义：

````
```cesium
{
  "type": "map",
  "operation": "zoomBig"
}
```
````

使用`parseCesiumData`方法解析：

```javascript
/**
 * @watch isStreamLoad
 * @description 监听流式加载状态，完成后渲染地图路线和执行Cesium操作
 */
watch(
  () => props.isStreamLoad,
  async isStreamLoad => {
    if (!isStreamLoad) {
      const cesiumData = parseCesiumData(message.content)
      if (cesiumData && cesiumData.length > 0) {
        // 提前检查是否需要加载分析模块
        const needsAnalysisModule = cesiumData.some(
          op => op.type === "map" && (op.operation === "measureDistance" || op.operation === "measureArea")
        )

        // 如果需要，先加载分析模块
        if (needsAnalysisModule) {
          try {
            await executeOperation({
              type: "map",
              operation: "loadOperationModule",
              params: { moduleName: "analysis" }
            })
            console.log("已加载分析模块")
          } catch (error) {
            console.error("加载分析模块失败:", error)
          }
        }

        // 执行所有操作
        for (const operation of cesiumData) {
          try {
            const result = await executeOperation(operation)
            console.log("Cesium operation result:", result)

            if (!result.success) {
              console.error("Operation failed:", result.error)
            }
          } catch (error) {
            console.error("Error executing Cesium operation:", error)
          }
        }
      }
    }
  }
)
```

## 扩展新模块

### 1. 创建新的操作模块文件

```javascript
// src/composables/cesium/operations/myNewOperations.js
export function getMyNewOperations(viewer, baseOps) {
  const Cesium = DC.getLib("Cesium")

  return {
    async myOperation(params) {
      try {
        // 实现你的操作
        return {
          success: true,
          data: {
            /* 结果数据 */
          }
        }
      } catch (error) {
        return { success: false, error: error.message }
      }
    }
  }
}
```

### 2. 在动态加载系统中注册

在`operations.js`中添加新模块的加载支持：

```javascript
async loadOperationModule(moduleName) {
  // ...
  switch (moduleName) {
    // ... 现有case
    case 'myNew':
      const { getMyNewOperations } = await import('./operations/myNewOperations.js')
      moduleOperations = getMyNewOperations(viewer, baseOperations)
      break
    // ...
  }
  // ...
}
```

### 3. 预加载（可选）

如果这是常用模块，可以修改`operations/index.js`直接导出：

```javascript
// 常用模块直接导出
export { getMyNewOperations } from "./myNewOperations"
```

如果是按需加载的模块，可以在组件中预加载：

```javascript
const { preloadOperationModules } = useCesium()

// 组件挂载时预加载常用的Cesium操作模块
onMounted(() => {
  preloadOperationModules(["analysis"])
})
```

## 最佳实践

1. **操作分类原则**：按功能领域和使用频率分类
2. **常用操作预加载**：常用的基础操作应该在初始化时加载
3. **参数校验**：每个操作函数应该先校验参数
4. **错误处理**：捕获并返回统一格式的错误信息
5. **性能考虑**：大型资源应该在操作结束后释放
6. **文档维护**：新增操作时及时更新文档
