import { ref, getCurrentInstance } from "vue";
import { getForecastSchemeList } from "@/api/scheduling/index";
import { MarkerType } from "@vue-flow/core";

/**
 * 预报方案相关的 hooks
 */
export function useForecastScheme() {
  const { proxy } = getCurrentInstance();
  const loading = ref(false);

  /**
   * 获取方案管理的任务详情并构建流程图数据
   * @param {string|number} id - 任务ID
   * @returns {Promise<Object>} 返回包含表单数据、节点、边和节点值的对象
   */
  const getTaskDetail = async (id) => {
    try {
      loading.value = true;
      const res = await getForecastSchemeList({
        id,
      });

      if (res.code === 200) {
        if (res.rows.length === 0) {
          return null;
        }

        const detailData = res.rows[0];
        return getFlowDataBase(detailData);
      }
    } catch (error) {
      console.log(error);
      return {
        formData: {},
        nodes: [],
        edges: [],
        nodeValue: {},
      };
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取构建流程图数据 - 普通展示
   */
  const getFlowDataBase = (detailData) => {
    try {
      // 构建流程图节点数据
      const nodes = [];
      const edges = [];
      const nodeValue = [];
      const subBasinList = detailData.subBasinList || [];
      const riverSectionList = detailData.riverSectionList || [];

      // 处理小流域节点
      if (detailData.subBasinList && detailData.subBasinList.length > 0) {
        detailData.subBasinList.forEach((subBasin) => {
          console.log("🚀 ~ ", subBasin, "subBasin");
          // 添加节点
          nodes.push({
            id: subBasin.nodeOrder,
            type: "subBasin",
            class: "area1",
            position: {
              x: subBasin.locationX || 0,
              y: subBasin.locationY || 0,
            },
            data: {
              label: subBasin.nodeName,
              nodeType: "subBasin",
              nodeId: subBasin.nodeOrder,
            },
          });

          // 添加节点配置数据
          nodeValue.push({
            type: 1, // 小流域类型
            nodeType: "subBasin",
            nodeOrder: subBasin.nodeOrder,
            nodeName: subBasin.nodeName,
            label: subBasin.nodeName,
            outletOrder: subBasin.outletOrder,
            basinCode: subBasin.basinCode,
            basinName: subBasin.basinName,
            basinIdentifier: subBasin.basinIdentifier,
            locationX: subBasin.locationX,
            locationY: subBasin.locationY,
            list:
              subBasin?.list?.map((station) => {
                return {
                  ...station,
                  stnm: station.stationName,
                };
              }) || [],
          });

          // 如果有出口节点，添加连接边
          if (subBasin.outletOrder) {
            edges.push({
              id: `${subBasin.nodeOrder}-${subBasin.outletOrder}`,
              source: subBasin.nodeOrder,
              target: subBasin.outletOrder,
              markerEnd: MarkerType.ArrowClosed,
            });
          }
        });
      }

      // 处理河道断面节点
      if (
        detailData.riverSectionList &&
        detailData.riverSectionList.length > 0
      ) {
        detailData.riverSectionList.forEach((section) => {
          // 添加节点
          nodes.push({
            id: section.nodeOrder,
            type: "section",
            class: "area1",
            position: {
              x: section.locationX || 0,
              y: section.locationY || 0,
            },
            data: {
              label: section.nodeName,
              nodeType: "section",
              nodeId: section.nodeOrder,
            },
          });

          // 添加节点配置数据
          nodeValue.push({
            type: 5, // 河道断面类型
            nodeType: "section",
            nodeOrder: section.nodeOrder,
            nodeName: section.nodeName,
            label: section.nodeName,
            outletOrder: section.outletOrder,
            riverSectionCode: section.riverSectionCode,
            sectionIdentifier: section.sectionIdentifier,
            locationX: section.locationX,
            locationY: section.locationY,
            isInflowSection: section.isInflowSection,
          });

          // 如果有出口节点，添加连接边
          if (section.outletOrder) {
            edges.push({
              id: `${section.nodeOrder}-${section.outletOrder}`,
              source: section.nodeOrder,
              target: section.outletOrder,
              markerEnd: MarkerType.ArrowClosed,
            });
          }
        });
      }

      // 处理水库节点
      if (
        detailData.riverReservoirList &&
        detailData.riverReservoirList.length > 0
      ) {
        detailData.riverReservoirList.forEach((reservoir) => {
          // 添加节点
          nodes.push({
            id: reservoir.nodeOrder,
            type: "reservoir",
            class: "area1",
            position: {
              x: reservoir.locationX || 0,
              y: reservoir.locationY || 0,
            },
            data: {
              label: reservoir.nodeName,
              nodeType: "reservoir",
              nodeId: reservoir.nodeOrder,
            },
          });

          // 添加节点配置数据
          nodeValue.push({
            type: 2, // 水库类型
            nodeType: "reservoir",
            nodeOrder: reservoir.nodeOrder,
            nodeName: reservoir.nodeName,
            label: reservoir.nodeName,
            outletOrder: reservoir.outletOrder,
            reservoirCode: reservoir.reservoirCode,
            reservoirName: reservoir.reservoirName,
            reservoirIdentifier: reservoir.reservoirIdentifier,
            releaseMethod: reservoir.releaseMethod,
            locationX: reservoir.locationX,
            locationY: reservoir.locationY,
            releaseFlow: reservoir.releaseFlow,
          });

          // 如果有出口节点，添加连接边
          if (reservoir.outletOrder) {
            edges.push({
              id: `${reservoir.nodeOrder}-${reservoir.outletOrder}`,
              source: reservoir.nodeOrder,
              target: reservoir.outletOrder,
              markerEnd: MarkerType.ArrowClosed,
            });
          }
        });
      }

      return {
        formData: detailData,
        nodes,
        edges,
        nodeValue,
        subBasinList,
        riverSectionList,
      };
    } catch (error) {
      console.log(error);
      return {
        formData: {},
        nodes: [],
        edges: [],
        nodeValue: [],
        subBasinList: [],
        riverSectionList: [],
      };
    }
  };

  /**
   * 获取预报成果详情的流程图数据
   */
  const getTaskFlowData = async (data) => {
    try {
      const detailData = data;

      const nodes = [];
      const edges = [];
      const nodeValue = [];
      // 处理小流域节点
      if (detailData.subBasinList && detailData.subBasinList.length > 0) {
        detailData.subBasinList.forEach((subBasin) => {
          // 添加节点
          nodes.push({
            id: subBasin.nodeOrder,
            type: "subBasin",
            class: "area1",
            position: {
              x: subBasin.locationX || 0,
              y: subBasin.locationY || 0,
            },
            data: {
              label: subBasin.nodeName,
              nodeType: "subBasin",
              nodeId: subBasin.nodeOrder,
            },
          });

          // 添加节点配置数据
          nodeValue.push({
            type: 1, // 小流域类型
            nodeType: "subBasin",
            nodeOrder: subBasin.nodeOrder,
            nodeName: subBasin.nodeName,
            label: subBasin.nodeName,
            outletOrder: subBasin.outletOrder,
            basinCode: subBasin.basinCode,
            basinName: subBasin.basinName,
            basinIdentifier: subBasin.basinIdentifier,
            locationX: subBasin.locationX,
            locationY: subBasin.locationY,
            stationInfo: subBasin.stationInfo, // 雨量站信息
            totalRainfall: subBasin.totalRainfall, // 累计降雨量
            totalOutflow: subBasin.totalOutflow, // 累计出流量
            list:
              subBasin?.list?.map((station) => {
                return {
                  ...station,
                  stnm: station.stationName,
                };
              }) || [],
          });

          // 如果有出口节点，添加连接边
          if (subBasin.outletOrder) {
            edges.push({
              id: `${subBasin.nodeOrder}-${subBasin.outletOrder}`,
              source: subBasin.nodeOrder,
              target: subBasin.outletOrder,
              markerEnd: MarkerType.ArrowClosed,
            });
          }
        });
      }

      // 处理河道断面节点
      if (
        detailData.riverSectionList &&
        detailData.riverSectionList.length > 0
      ) {
        detailData.riverSectionList.forEach((section) => {
          // 添加节点
          nodes.push({
            id: section.nodeOrder,
            type: "section",
            class: "area1",
            position: {
              x: section.locationX || 0,
              y: section.locationY || 0,
            },
            data: {
              label: section.nodeName,
              nodeType: "section",
              nodeId: section.nodeOrder,
            },
          });

          // 添加节点配置数据
          nodeValue.push({
            type: 5, // 河道断面类型
            nodeType: "section",
            nodeOrder: section.nodeOrder,
            nodeName: section.nodeName,
            label: section.nodeName,
            outletOrder: section.outletOrder,
            riverSectionCode: section.riverSectionCode,
            sectionIdentifier: section.sectionIdentifier,
            locationX: section.locationX,
            locationY: section.locationY,
            isInflowSection: section.isInflowSection,
            peakInflow: section.peakInflow, // 洪峰流量
            peakInflowTime: section.peakInflowTime, // 峰现时间
          });

          // 如果有出口节点，添加连接边
          if (section.outletOrder) {
            edges.push({
              id: `${section.nodeOrder}-${section.outletOrder}`,
              source: section.nodeOrder,
              target: section.outletOrder,
              markerEnd: MarkerType.ArrowClosed,
            });
          }
        });
      }

      // 处理水库节点
      if (
        detailData.riverReservoirList &&
        detailData.riverReservoirList.length > 0
      ) {
        detailData.riverReservoirList.forEach((reservoir) => {
          // 添加节点
          nodes.push({
            id: reservoir.nodeOrder,
            type: "reservoir",
            class: "area1",
            position: {
              x: reservoir.locationX || 0,
              y: reservoir.locationY || 0,
            },
            data: {
              label: reservoir.nodeName,
              nodeType: "reservoir",
              nodeId: reservoir.nodeOrder,
            },
          });

          // 添加节点配置数据
          nodeValue.push({
            type: 2, // 水库类型
            nodeType: "reservoir",
            nodeOrder: reservoir.nodeOrder,
            nodeName: reservoir.nodeName,
            label: reservoir.nodeName,
            outletOrder: reservoir.outletOrder,
            reservoirCode: reservoir.reservoirCode,
            reservoirName: reservoir.reservoirName,
            reservoirIdentifier: reservoir.reservoirIdentifier,
            releaseMethod: reservoir.releaseMethod,
            locationX: reservoir.locationX,
            locationY: reservoir.locationY,
            releaseFlow: reservoir.releaseFlow,
          });

          // 如果有出口节点，添加连接边
          if (reservoir.outletOrder) {
            edges.push({
              id: `${reservoir.nodeOrder}-${reservoir.outletOrder}`,
              source: reservoir.nodeOrder,
              target: reservoir.outletOrder,
              markerEnd: MarkerType.ArrowClosed,
            });
          }
        });
      }

      return {
        nodes,
        edges,
        nodeValue,
      };
    } catch (error) {
      return {
        nodes: [],
        edges: [],
        nodeValue: {},
      };
    }
  };

  return {
    loading,
    getFlowDataBase,
    getTaskDetail,
    getTaskFlowData,
  };
}
