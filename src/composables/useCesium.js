import { createOperations } from "./cesium/operations"

/**
 * Cesium操作钩子函数
 * 提供Cesium地图交互和数据可视化能力
 * @returns {Object} Cesium操作相关方法
 */
export function useCesium() {
  // 创建操作实例
  const operations = createOperations(window.viewer)

  /**
   * 解析消息内容中的Cesium操作数据
   * @param {string} content - 消息内容
   * @returns {Array|null} 解析后的操作数据数组
   */
  const parseCesiumData = content => {
    if (!content) {
      console.log("parseCesiumData: content is empty")
      return null
    }

    try {
      const cesiumMatches = Array.from(content.matchAll(/```cesium\n([\s\S]*?)\n```/g))

      if (cesiumMatches && cesiumMatches.length > 0) {
        return cesiumMatches
          .map(match => {
            try {
              // 尝试直接解析JSON
              const jsonStr = match[1].trim()
              return JSON.parse(jsonStr)
            } catch (jsonError) {
              console.log("🚀 ~ ", jsonError)
              // JSON解析失败时，尝试清理和转换JavaScript对象为JSON
              try {
                // 移除JavaScript注释
                const codeWithoutComments = match[1]
                  .replace(/\/\/.*/g, "") // 移除单行注释
                  .replace(/\/\*[\s\S]*?\*\//g, "") // 移除多行注释
                  .trim()

                // 将JavaScript对象字面量转换为标准JSON格式
                const jsObjectStr = codeWithoutComments
                  .replace(/([{,]\s*)(\w+):/g, '$1"$2":')
                  .replace(/'/g, '"')
                  .replace(/,(\s*[}\]])/g, "$1")

                return JSON.parse(jsObjectStr)
              } catch (cleanError) {
                console.error("清理后的JSON解析失败:", cleanError)
                return null
              }
            }
          })
          .filter(Boolean) // 过滤掉解析失败的null值
      }
    } catch (error) {
      console.error("parseCesiumData: error", error)
      return null
    }

    return null
  }

  /**
   * 执行地图操作
   * @param {Object} operationData - 操作数据
   * @param {string} operationData.type - 操作类型
   * @param {string} operationData.operation - 操作名称
   * @param {Object} [operationData.params] - 操作参数
   * @returns {Promise<Object>} 操作结果
   */
  const executeOperation = async operationData => {
    // console.log("executeOperation", operationData)

    if (!operationData?.type || !operationData?.operation) {
      console.error("操作数据不合法")
      return {
        success: false,
        error: "操作数据不合法"
      }
    }

    const { type, operation, params } = operationData

    // 检查操作类型
    if (type !== "map") {
      console.error("不支持的操作类型:", type)
      return {
        success: false,
        error: `不支持的操作类型: ${type}`
      }
    }

    // 获取对应的操作函数
    const operationFn = operations[operation]
    if (typeof operationFn !== "function") {
      console.error("不支持的操作:", operation)
      return {
        success: false,
        error: `不支持的操作: ${operation}`
      }
    }

    try {
      // 执行操作
      return await operationFn.call(operations, params)
    } catch (error) {
      console.error("执行操作失败:", error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 预加载操作模块
   * 可用于预先加载常用模块，提高用户体验
   * @param {Array<string>} moduleNames - 模块名称数组
   * @returns {Promise<void>}
   */
  const preloadOperationModules = async moduleNames => {
    if (!Array.isArray(moduleNames) || moduleNames.length === 0) return

    try {
      const loadPromises = moduleNames.map(name => operations.loadOperationModule(name))
      await Promise.all(loadPromises)
      console.log("预加载操作模块完成:", moduleNames)
    } catch (error) {
      console.error("预加载操作模块失败:", error)
    }
  }

  return {
    parseCesiumData,
    executeOperation,
    preloadOperationModules
  }
}
