import { createApp } from 'vue'

// 导入Element Plus UI框架及其样式
import ElementPlus from 'element-plus'
// import 'element-plus/dist/index.css'
import locale from 'element-plus/lib/locale/lang/zh-cn' // 配置Element Plus的中文语言包

// 导入全局样式文件
import '@/assets/styles/index.scss'

// 导入应用核心模块
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // 导入自定义指令

// 导入插件系统和下载工具
import installPlugins from './plugins'
import { download } from '@/utils/request'

// 配置SVG图标系统
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

// 导入权限控制系统
import './permission'

// 导入工具函数和字典管理
import { useDict } from '@/utils/dict'
import { parseTime, resetForm, addDateRange, handleTree, selectDictLabel, selectDictLabels } from '@/utils/ruoyi'

// 导入全局通用组件
import Pagination from '@/components/Pagination' // 分页组件
import RightToolbar from '@/components/RightToolbar' // 表格工具栏组件
import FileUpload from "@/components/FileUpload" // 文件上传组件
import ImageUpload from "@/components/ImageUpload" // 图片上传组件
import ImagePreview from "@/components/ImagePreview" // 图片预览组件
import TreeSelect from '@/components/TreeSelect' // 树形选择组件
import DictTag from '@/components/DictTag' // 字典标签组件

// 配置全局事件总线，用于组件间通信
import { eventBus } from './utils/eventBus.js'
window.EventBus = eventBus

// 创建Vue应用实例
const app = createApp(App)

// 注册全局方法
app.config.globalProperties.useDict = useDict // 字典管理
app.config.globalProperties.download = download // 文件下载
app.config.globalProperties.parseTime = parseTime // 时间格式化
app.config.globalProperties.resetForm = resetForm // 表单重置
app.config.globalProperties.handleTree = handleTree // 树形数据处理
app.config.globalProperties.addDateRange = addDateRange // 日期范围处理
app.config.globalProperties.selectDictLabel = selectDictLabel // 字典标签选择
app.config.globalProperties.selectDictLabels = selectDictLabels // 多字典标签选择

// 注册全局组件
app.component('DictTag', DictTag)
app.component('Pagination', Pagination)
app.component('TreeSelect', TreeSelect)
app.component('FileUpload', FileUpload)
app.component('ImageUpload', ImageUpload)
app.component('ImagePreview', ImagePreview)
app.component('RightToolbar', RightToolbar)

// 使用Vue插件
app.use(router) // 路由
app.use(store) // 状态管理
app.use(installPlugins) // 自定义插件
app.use(elementIcons) // Element Plus图标
app.component('svg-icon', SvgIcon) // SVG图标组件

// 注册自定义指令
directive(app)

// 配置Element Plus，设置中文语言包
app.use(ElementPlus, {
  locale: locale
})

app.mount('#app')
