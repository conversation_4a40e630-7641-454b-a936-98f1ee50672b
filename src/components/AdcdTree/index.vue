<script setup>
import { onMounted, ref } from "vue";
import { getTreeOption } from "@/api/watershed/ads";
import { deepClone } from "@/utils";

const adcd = defineModel();

const props = defineProps({
  type: {
    type: Number,
    default: 5,
  },
  defaultExpandAll: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["getList"]);

// 树形控件配置
const propsTree = ref({
  value: "adcd", // 节点值字段
  label: "adnm", // 节点标签字段
  children: "children", // 子节点字段
});

// 数据列表相关
const filterText = ref(""); // 树形筛选文本
const adcdList = ref([]); // 行政区树形数据

const { proxy } = getCurrentInstance();

const onQueryChanged = (query) => {
  proxy.$refs.treeRef.filter(query);
};

/**
 * 树节点过滤方法
 * @param {string} query - 搜索关键字
 * @param {Object} node - 树节点数据
 * @returns {boolean} - 是否匹配
 */
const filterNode = (query, node) => {
  return node.adnm.includes(query);
};

/**
 * 树节点点击事件处理
 * @param {Object} el - 节点数据
 */
const handleNodeClick = (el) => {
  adcd.value = el.adcd;
  emit("getList");
};

const defaultExpandedKeys = ref([]);
/**
 * 获取行政区树形数据
 * 获取5级行政区划数据
 */
const getTreeList = () => {
  getTreeOption(props.type).then((res) => {
    adcdList.value = formatTree(res.data);

    if (props.defaultExpandAll) {
      // 从树形数据中提取所有键值
      defaultExpandedKeys.value = deepClone(adcdList.value).flatMap((node) =>
        getAllKeys(node)
      );

      proxy.$refs.treeRef.setExpandedKeys(defaultExpandedKeys.value);
    }
  });
};

/**
 * 格式化树形数据
 * 处理树形数据的格式，移除空的子节点
 * @param {Array} data - 原始树形数据
 * @returns {Array} - 格式化后的树形数据
 */
const formatTree = (data) => {
  const formattedData = data.map((item) => ({
    ...item.data,
    children: item.children && formatTree(item.children),
  }));

  return formattedData.map((item) => {
    if (item.children?.length === 0) {
      delete item.children;
    }
    return item;
  });
};

/**
 * 递归收集树形结构中所有非叶子节点的 adcd 值
 * @param {Object} node - 树节点对象
 * @returns {Array} - 包含所有非叶子节点 adcd 值的数组
 */
const getAllKeys = (node) => {
  // 如果是叶子节点，直接返回空数组
  if (!node?.children?.length) return [];

  // 返回当前节点和所有子节点的 adcd 值
  return [node.adcd, ...node.children.flatMap((child) => getAllKeys(child))];
};

onMounted(() => {
  // 初始化获取树形数据
  getTreeList();
});
</script>

<template>
  <div class="left-tree">
    <div class="head-container">
      <el-input
        v-model="filterText"
        placeholder="请输入行政区名称"
        clearable
        prefix-icon="Search"
        class="filter-input"
        @input="onQueryChanged"
      />
    </div>
    <div class="tree-container">
      <el-tree-v2
        v-model="adcd"
        ref="treeRef"
        :data="adcdList"
        :props="propsTree"
        :height="600"
        :filter-method="filterNode"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
        highlight-current
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.left-tree {
  width: 100%;
  max-width: 300px;
  min-width: 180px;
  margin: 0 auto;

  .filter-input {
    margin-bottom: 20px;
    width: 100%;
  }

  .tree-container {
    width: 100%;
    overflow: hidden;
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .left-tree {
    max-width: 100%;
  }
}
</style>
