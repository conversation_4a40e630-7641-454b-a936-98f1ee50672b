<template>
  <div class="map-container">
    <draw-tool @update-bound="updateBound"></draw-tool>
  </div>
</template>

<script setup>
import DrawTool from "./plugins/drawTool";

let viewer = null;

const updateBound = (data) => {
  ctx.emit("updateBound", data); //通过ctx.emit分发事件
};

onMounted(() => {
  DC.ready().then(() => {
    viewer = createViwer("minViewer", "mapMin3d");
  });
});
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #000c2f;
}
.map-main {
  width: 100%;
  height: 100%;
  display: inline-block;
  position: relative;
}
.width51 {
  position: fixed;
  left: 50%;
  width: 4px;
  background: #fff;
  top: 64px;
  bottom: 0;
  height: 100%;
  z-index: 10;
}
.duibiBtn {
  right: 50px;
  top: 30px;
  position: absolute;
}

.width50 {
  width: 50%;
  border-bottom: 275px solid #000c2f;
}
.hideTwoMap {
  visibility: hidden;
}
</style>
<style>
.marker-rain-html {
  width: 82px;
  height: 26px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
  padding: 3px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -40px;
}
.marker-rain-html2 {
  width: 168px;
  height: 135px;
  padding: 8px 8px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -78px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}
.marker-river-html {
  width: 82px;
  height: 26px;
  padding: 4px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -39px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}
.marker-river-warn-html {
  width: 82px;
  height: 26px;
  padding: 2px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -64px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}
.marker-river-warn-html2 {
  width: 168px;
  height: 135px;
  padding: 8px 8px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -78px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}

.marker-reservoir-html {
  /*width: 142px;*/
  width: 82px;
  height: 26px;
  padding: 1px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -39px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}
.marker-reservoir-warn-html {
  width: 82px;
  height: 26px;
  padding: 2px 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -39px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}
.marker-reservoir-warn-html2 {
  width: 168px;
  height: 135px;
  padding: 8px 8px;
  border-radius: 4px;
  color: #fff;
  font-size: 15px;
  position: absolute;
  bottom: 15px;
  left: -78px;
  background: rgba(0, 78, 196, 0.6);
  border: 1px solid #2ab3fc;
}
.up-river {
  width: 142px;
  background: rgba(255, 122, 39, 0.74);
  border: 1px solid #ff7a27;
}
.down-river {
  width: 142px;
  background: rgba(255, 149, 19, 0.76);
  border: 1px solid #ff9513;
}
.ping-river {
  width: 142px;
  background: rgba(255, 213, 74, 0.68);
  border: 1px solid #ffd54a;
}
.up_marker {
  width: 25px;
  height: 25px;
  position: absolute;
  right: -14px;
  top: -10px;
  z-index: 10;
  background: #ff7e23;
  color: #fff;
  font-size: 12px;
  border-radius: 50%;
  line-height: 25px;
}
.down_marker {
  width: 25px;
  height: 25px;
  position: absolute;
  right: -14px;
  top: -10px;
  z-index: 10;
  background: rgb(29 190 39 / 99%);
  color: #fff;
  font-size: 12px;
  border-radius: 50%;
  line-height: 25px;
}
.ping_marker {
  width: 25px;
  height: 25px;
  position: absolute;
  right: -14px;
  top: -10px;
  z-index: 10;
  display: none;
  line-height: 25px;
}
.div-icon {
  background: none;
}
.hideMoreText {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.popHr {
  height: 2px;
  border: 0;
  margin: 2px;
  width: 100%;
  background: linear-gradient(to right, #0ec2fe 0%, rgb(255 255 255 / 0%) 100%);
}
</style>
