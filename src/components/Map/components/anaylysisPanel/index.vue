<!--预警相关信息提示-->
<template>
  <div class="anaylysis-panel" v-show="show">
    <div class="leftPanel">
      <div class="topPanel">
        <div class="leftPanel">
          <div class="subPanel">
            <div class="subPanel-header">
              <img :src="img_banner" class="header-img" />
              <div class="header-title">水库</div>
            </div>
            <div class="subPanel-content">
              <div class="exceed">
                <div
                  class="items"
                  :class="reservoirType === item.name ? 'active' : ''"
                  v-for="item in reservoirList"
                  @click="handlerReservoirFilter(item)"
                >
                  <div class="value" :style="{ color: item.color }">
                    {{ item.value }}
                  </div>
                  <div class="name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="rightPanel">
          <div class="subPanel">
            <div class="subPanel-header">
              <img :src="img_banner" class="header-img" />
              <div class="header-title">河道</div>
            </div>
            <div class="subPanel-content">
              <div class="exceed">
                <div
                  class="items"
                  :class="riverType === item.name ? 'active' : ''"
                  v-for="item in riverList"
                  @click="handlerRiverFilter(item)"
                >
                  <div class="value" :style="{ color: item.color }">
                    {{ item.value }}
                  </div>
                  <div class="name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottomPanel">
        <div class="subPanel">
          <div class="subPanel-header">
            <img :src="img_banner" class="header-img" />
            <div class="header-title">淹没分析</div>
          </div>
          <div class="subPanel-content">
            <div class="exceed">
              <div
                class="items"
                :class="floodType === item.name ? 'active' : ''"
                v-for="item in floodList"
                @click="handlerFloodFilter(item)"
              >
                <div class="value" :style="{ color: item.color }">
                  {{ item.value }}
                </div>
                <div class="name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="rightPanel">
      <div class="topPanel">
        <div class="leftPanel">
          <div class="subPanel">
            <div class="subPanel-header">
              <img :src="img_banner" class="header-img" />
              <div class="header-title">水库</div>
            </div>
            <div class="subPanel-content">
              <div class="exceed">
                <div
                  class="items"
                  :class="reservoirType2 === item.name ? 'active' : ''"
                  v-for="item in reservoirList2"
                  @click="handlerReservoirFilter2(item)"
                >
                  <div class="value" :style="{ color: item.color }">
                    {{ item.value }}
                  </div>
                  <div class="name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="rightPanel">
          <div class="subPanel">
            <div class="subPanel-header">
              <img :src="img_banner" class="header-img" />
              <div class="header-title">河道</div>
            </div>
            <div class="subPanel-content">
              <div class="exceed">
                <div
                  class="items"
                  :class="riverType2 === item.name ? 'active' : ''"
                  v-for="item in riverList2"
                  @click="handlerRiverFilter2(item)"
                >
                  <div class="value" :style="{ color: item.color }">
                    {{ item.value }}
                  </div>
                  <div class="name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottomPanel">
        <div class="subPanel">
          <div class="subPanel-header">
            <img :src="img_banner" class="header-img" />
            <div class="header-title">淹没分析</div>
          </div>
          <div class="subPanel-content">
            <div class="exceed">
              <div
                class="items"
                :class="floodType2 === item.name ? 'active' : ''"
                v-for="item in floodList2"
                @click="handlerFloodFilter2(item)"
              >
                <div class="value" :style="{ color: item.color }">
                  {{ item.value }}
                </div>
                <div class="name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import img_banner from "../../images/flood/banner.png";
import {
  getDisStatResTj,
  getDisYmResTj,
} from "@/api/watershed/screenRight/dispatch";

defineOptions({
  name: "anaylysis-panel",
});

const warnText = ref(
  "【新吴区防汛办】【河道超警戒预警】09曰21日06时13分太湖新安站实测水位345.21吗，超警戒0.32吗，请加强监测巡查，做好安全防汛工作。"
);
const show = ref(true);
const tickTime = ref(60);
// 河道统计
const riverList = ref([
  {
    name: "超警戒",
    value: "0",
    color: "#FFEA35",
  },
  {
    name: "超保证",
    value: "0",
    color: "#FFA92F",
  },
  {
    name: "超历史",
    value: "0",
    color: "#FF7E01",
  },
]);
// 水库站点统计
const reservoirList = ref([
  {
    id: 0,
    value: 3,
    name: "超汛限",
    color: "#FFEA35",
  },
  {
    id: 2,
    value: 3,
    name: "超设计",
    color: "#FFA92F",
  },
  {
    id: 3,
    value: 3,
    name: "超校核",
    color: "#FF7E01",
  },
]);
// 淹没分析统计
const floodList = ref([
  {
    id: 0,
    value: "0",
    name: "自然村镇(个)",
  },
  {
    id: 1,
    value: "0",
    name: "受灾人口(万人)",
  },
  {
    id: 2,
    value: "0",
    name: "淹没面积(km2)",
  },
  {
    id: 3,
    value: "0",
    name: "影响耕地",
  },
  {
    id: 4,
    value: "0",
    name: "危险区(个)",
  },
  {
    id: 5,
    value: "0",
    name: "山洪隐患点(个)",
  },
  {
    id: 6,
    value: "0",
    name: "淹没区GDP(万元)",
  },
]);

// 河道统计2
const riverList2 = ref([
  // {
  //   name: '全部',
  //   value: '62',
  //   color: '#87EAFF'
  // },
  {
    name: "超警戒",
    value: "0",
    color: "#FFEA35",
  },
  {
    name: "超保证",
    value: "0",
    color: "#FFA92F",
  },
  {
    name: "超历史",
    value: "0",
    color: "#FF7E01",
  },
]);
// 水库站点统计2
const reservoirList2 = ref([
  {
    id: 0,
    value: 3,
    name: "超汛限",
    color: "#FFEA35",
  },
  {
    id: 2,
    value: 3,
    name: "超设计",
    color: "#FFA92F",
  },
  {
    id: 3,
    value: 3,
    name: "超校核",
    color: "#FF7E01",
  },
]);
// 淹没分析统计2
const floodList2 = ref([
  {
    id: 0,
    value: "0",
    name: "自然村镇(个)",
  },
  {
    id: 1,
    value: "0",
    name: "受灾人口(万人)",
  },
  {
    id: 2,
    value: "0",
    name: "淹没面积(km2)",
  },
  {
    id: 3,
    value: "0",
    name: "影响耕地",
  },
  {
    id: 4,
    value: "0",
    name: "危险区(个)",
  },
  {
    id: 5,
    value: "0",
    name: "山洪隐患点(个)",
  },
  {
    id: 6,
    value: "0",
    name: "淹没区GDP(万元)",
  },
]);

const riverType = ref("");
const reservoirType = ref("");
const floodType = ref("");
const riverType2 = ref("");
const reservoirType2 = ref("");
const floodType2 = ref("");
const currentModuleType = ref("");
const dispId = ref("");
const dispId2 = ref("");

onMounted(() => {
  window.EventBus.$on("module/type/change", changeModuleType);
  // 调度方案的选择，联动地图上的东西刷新，站点的初始时刻，和时间轴
  window.EventBus.$on("floodPreRehearsal/dispatch/select", dispatchSelect);
  window.EventBus.$on("floodPreRehearsal/dispatch/select2", dispatchSelect2);
});

const changeModuleType = (data) => {
  currentModuleType.value = data.name;
};
const dispatchSelect = (data) => {
  dispId.value = data.ID;
};
const dispatchSelect2 = (data) => {
  dispId2.value = data.ID;
  // 一旦2 有了那么说明开始显示对比分析了，对比分析的话那就请求一下统计数据
  getTJData();
};
const handlerRiverFilter = (item) => {
  riverType.value = item.name;
  // window.EventBus.$emit('riverPanel/handlerFilter', { name: item.name })
  // if (item.name == '全部') {
  //   getRiverList()
  // } else {
  //   // console.log(state.backList)
  //   const list = riverDataFilter(item.name, state.backList)
  //   // console.log(list.length + '条数据')
  //   window.EventBus.$emit('river/update', list)
  //   // window.EventBus.$emit('river/update', list)
  //   state.regimenList = list.slice(0, 5)
  // }
};
const handlerReservoirFilter = (item) => {
  reservoirType.value = item.name;
};
const handlerFloodFilter = (item) => {
  floodType.value = item.name;
};
const handlerRiverFilter2 = (item) => {
  riverType2.value = item.name;
};
const handlerReservoirFilter2 = (item) => {
  reservoirType2.value = item.name;
};
const handlerFloodFilter2 = (item) => {
  floodType2.value = item.name;
};

const getTJData = () => {
  // 左下角的统计
  getDisStatResTj({
    dispId: dispId.value,
  }).then((res) => {
    reservoirList.value[0].value = res.data.rsvr.UPFSLTDZ_C || 0;
    reservoirList.value[1].value = res.data.rsvr.UPDSFLZ_C || 0;
    reservoirList.value[2].value = res.data.rsvr.UPCKFLZ_C || 0;
    riverList.value[0].value = res.data.river.UPWRZ_C || 0;
    riverList.value[1].value = res.data.river.UPGRZ_C || 0;
    riverList.value[2].value = res.data.river.UPOBHTZ_C || 0;
  });
  getDisYmResTj({
    dispId: dispId.value,
  }).then((res) => {
    floodList.value[0].value = res.data[0].T_QXNM || 0;
    floodList.value[1].value = res.data[0].T_EFCT_P || 0;
    floodList.value[2].value = res.data[0].T_EFCT_AREA || 0;
    floodList.value[3].value = res.data[0].T_EFCT_GD || 0;
    floodList.value[4].value = res.data[0].T_DENGS || 0;
    floodList.value[5].value = res.data[0].T_SHYHD || 0;
    floodList.value[6].value = res.data[0].T_EFECT_GDP || 0;
  });

  getDisStatResTj({
    dispId: dispId2.value,
  }).then((res) => {
    reservoirList2.value[0].value = res.data.rsvr.UPFSLTDZ_C || 0;
    reservoirList2.value[1].value = res.data.rsvr.UPDSFLZ_C || 0;
    reservoirList2.value[2].value = res.data.rsvr.UPCKFLZ_C || 0;
    riverList2.value[0].value = res.data.river.UPWRZ_C || 0;
    riverList2.value[1].value = res.data.river.UPGRZ_C || 0;
    riverList2.value[2].value = res.data.river.UPOBHTZ_C || 0;
  });
  getDisYmResTj({
    dispId: dispId2.value,
  }).then((res) => {
    floodList2.value[0].value = res.data[0].T_QXNM || 0;
    floodList2.value[1].value = res.data[0].T_EFCT_P || 0;
    floodList2.value[2].value = res.data[0].T_EFCT_AREA || 0;
    floodList2.value[3].value = res.data[0].T_EFCT_GD || 0;
    floodList2.value[4].value = res.data[0].T_DENGS || 0;
    floodList2.value[5].value = res.data[0].T_SHYHD || 0;
    floodList2.value[6].value = res.data[0].T_EFECT_GDP || 0;
  });
};
const closeCurrentPanel = () => {
  // 关闭当前面板可能还有别的预警信息？
  show.value = false;
};
</script>

<style scoped lang="scss">
.anaylysis-panel {
  position: absolute;
  left: 68px;
  right: 68px;
  bottom: 32px;
  padding: 2px;
  height: 250px;
  z-index: 10;
  //border: 1px solid rgba(28, 132, 198, 0.45);
}
.leftPanel {
  float: left;
  width: 49%;
}
.rightPanel {
  float: right;
  width: 49%;
}
.topPanel {
  height: 50%;
  width: 100%;
  display: inline-block;
}
.bottomPanel {
  height: 50%;
  width: 100%;
  display: inline-block;
}
.subPanel {
  margin: 6px 5px;
  padding: 5px 5px;
  //border: 1px solid #007dd7;
  background: rgba(0, 138, 255, 0.2);
}
.subPanel-header {
  height: 21px;
  width: 100%;
}
.subPanel-content {
  height: calc(100% - 21px);
}
.header-img {
  width: 240px;
  position: relative;
  top: -15px;
  left: -5px;
}
.header-title {
  position: relative;
  top: -39px;
  color: #fff;
  font-size: 15px;
  font-weight: bold;
  left: 38px;
}

.exceed {
  width: 95%;
  margin: 2px auto;
  height: 80px;
  display: flex;
  color: #fff;

  .items {
    flex: 1;
    height: 100%;
    text-align: center;

    .value {
      margin: 10px 0;
      font-size: 20px;
    }
    .name {
      margin: 10px 0;
      color: #a5d1ff;
      font-size: 12px;
    }
  }

  .items:hover {
    background: linear-gradient(0deg, #07529f 0%, rgba(8, 61, 132, 0) 100%);
    box-sizing: border-box;
    border: 0 solid #000000;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
    cursor: pointer;
  }

  .active {
    background: linear-gradient(0deg, #07529f 0%, rgba(8, 61, 132, 0) 100%);
    box-sizing: border-box;
    border: 0 solid #000000;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
    cursor: pointer;
  }
}
</style>
