<!--模型双击查看面板-->
<template>
  <div class="detailMain">
    <div class="leftMenu">
      <el-menu
        default-active="info"
        class="el-menu-vertical-demo"
        @select="menuSelect"
        background-color="#060E26"
        text-color="#fff"
        active-text-color="#00C3FF"
      >
        <el-menu-item index="info">
          <i class="el-icon-menu"></i>
          <span slot="title">基础数据</span>
        </el-menu-item>
        <el-menu-item index="realtime">
          <i class="el-icon-document"></i>
          <span slot="title">实时数据</span>
        </el-menu-item>
        <el-menu-item index="repair">
          <i class="el-icon-setting"></i>
          <span slot="title">维修数据</span>
        </el-menu-item>
        <el-menu-item index="warn">
          <i class="el-icon-aim"></i>
          <span slot="title">报警数据</span>
        </el-menu-item>
      </el-menu>
    </div>
    <div class="content">
      <div v-show="curSelect === 'info'" class="contentInfo">
        <div
          class="infoItem"
          :key="item.attrCode"
          v-for="item in modelInstanceAttrValues"
        >
          <div class="infoItemLabel">{{ item.attrName }}：</div>
          <div class="infoItemValue">{{ item.attrValue }}</div>
        </div>
      </div>
      <div v-show="curSelect === 'realtime'" class="contentInfo">
        <div class="infoItem" :key="item.attrName" v-for="item in realDatas">
          <div class="infoItemLabel">{{ item.attrName }}：</div>
          <div class="infoItemValue">{{ item.attrValue }}</div>
        </div>
      </div>
      <div v-show="curSelect === 'repair'" class="contentInfo">
        <el-table
          :data="repairList"
          height="315"
          style="width: calc(100% - 10px)"
        >
          <el-table-column
            v-for="item in repairFields"
            :key="item.prop"
            :prop="item.prop"
            align="center"
            :label="item.label"
            :show-overflow-tooltip="true"
            width="150"
          >
          </el-table-column>
        </el-table>
      </div>
      <div v-show="curSelect === 'warn'" class="contentInfo">
        <el-table
          :data="alarmInfoList"
          height="315"
          style="width: calc(100% - 10px)"
        >
          <el-table-column
            prop="alarmTime"
            align="center"
            label="报警时间"
            width="110"
          >
          </el-table-column>
          <el-table-column
            prop="warnType"
            align="center"
            label="报警类型"
            width="110"
          >
            <template slot-scope="scope">
              <el-popover trigger="hover" placement="top">
                <p>{{ scope.row.warnType }}</p>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            prop="alarmContent"
            show-overflow-tooltip
            align="center"
            label="报警内容"
          >
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: "ModelInfo",
});

const props = defineProps({
  onlyShow: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const routerList = ref([]);
// 模型属性信息
const modelInstanceAttrValues = ref([
  // {
  //   'id':61,
  //   'modelInstanceId':35,
  //   'attrCode':'sex',
  //   'attrValue':'19.0',
  //   'channelNo':'123',
  //   'attrName':'性别'
  // },
  // {
  //   'id':62,
  //   'modelInstanceId':35,
  //   'attrCode':'age',
  //   'attrValue':'17.0',
  //   'channelNo':'456',
  //   'attrName':'年龄'
  // },
  // {
  //   'id':63,
  //   'modelInstanceId':35,
  //   'attrCode':'height',
  //   'attrValue':'180.0',
  //   'channelNo':'789',
  //   'attrName':'身高'
  // }
]);
// 模型预警信息列表
const alarmInfoList = ref([
  // {
  //   'id': 35,
  //   'alarmRuleId': 18,
  //   'warnType': 'EARLY_WARNING',
  //   'alarmTime': '2021-06-22',
  //   'alarmType': 'FRAME_ALARM',
  //   'modelInstanceId': 35,
  //   'alarmContent': '年龄大于等于 18.000000 已经成年18',
  //   'processStatus': 'IN_PROCESS',
  //   'processTime': null,
  //   'oneLevelLayerId': null
  // },
  // {
  //   'id': 37,
  //   'alarmRuleId': 20,
  //   'warnType': 'EARLY_WARNING',
  //   'alarmTime': '2021-06-22',
  //   'alarmType': 'VOICE_ALARM',
  //   'modelInstanceId': 35,
  //   'alarmContent': '身高小于 175.000000 有bug20',
  //   'processStatus': 'IN_PROCESS',
  //   'processTime': null,
  //   'oneLevelLayerId': null
  // },
  // {
  //   'id': 40,
  //   'alarmRuleId': 21,
  //   'warnType': 'EARLY_WARNING',
  //   'alarmTime': '2021-06-22',
  //   'alarmType': 'RIPPLE_ALARM',
  //   'modelInstanceId': 35,
  //   'alarmContent': '身高> 160.000000 后置21 并且 年龄>= 18.000000 已经成年21',
  //   'processStatus': 'IN_PROCESS',
  //   'processTime': null,
  //   'oneLevelLayerId': null
  // }
]);
// 维修数据列表
const repairList = ref([]);
const repairFields = ref([]);
// 实时数据
const realDatas = ref([
  // {
  //   'attrValue':'300W',
  //   'attrName':'电压'
  // },
  // {
  //   'attrValue':'50A',
  //   'attrName':'电流'
  // },
  // {
  //   'attrValue':'200ml',
  //   'attrName':'液位'
  // },
  // {
  //   'attrValue':'500HZ',
  //   'attrName':'频率'
  // }
]);
const curSelect = ref("info");

const menuSelect = (index) => {
  curSelect.value = index;
};
const reload = (modelInstanceId) => {
  curSelect.value = "info";
  repairFields.value = [];
  repairList.value = [];
  modelInstanceAttrValues.value = [];
  realDatas.value = [];
  alarmInfoList.value = [];
  let params = {
    jsonStr: '"deviceName":"' + modelInstanceId + '"',
  };
  let res = {
    data: [
      {
        daocangNo: "A1",
        equipmentLocation: "来来来",
        afterMaintenance:
          "/statics/2021/07/08/f33e2e49-7cc8-48f5-aff7-a4b0c0610eae.jpg",
        damagedCondition: "微小",
        maintenanceBy: "bird",
        beforeMaintenance:
          "/statics/2021/07/08/c8023c31-9e6b-4805-badf-739d0b051d7d.jpg",
        problemDescription: "来来来",
        faultType: "龟裂",
        failureCause: "来来来",
        deviceName: "回路48199",
        equipmentNunber: "16",
      },
    ],
    fields: [
      {
        道仓编号: "daocangNo",
      },
      {
        设备名称: "deviceName",
      },
      {
        设备编号: "equipmentNunber",
      },
      {
        设备位置: "equipmentLocation",
      },
      {
        故障类型: "faultType",
      },
      {
        故障原因: "failureCause",
      },
      {
        损坏程度: "damagedCondition",
      },
      {
        问题描述: "problemDescription",
      },
      {
        维修前: "beforeMaintenance",
      },
      {
        维修后: "afterMaintenance",
      },
      {
        维修人: "maintenanceBy",
      },
    ],
  };
  if (res.fields && res.fields.length > 0) {
    // 解析
    let fields = res.fields;
    fields.forEach((item) => {
      for (let key in item) {
        vm.repairFields.push({
          prop: item[key],
          label: key,
        });
      }
    });
    vm.repairList = res.data;
  }
};
</script>

<style scoped>
.detailMain {
  height: 324px;
  width: 680px;
  background: rgba(6, 14, 38, 0.48);
  border: 2px solid #00c3ff;
}
.modelMain {
  /*opacity: .8;*/
  /*background-color: #fff;*/
  border-radius: 4px 3px 4px 4px;
  /*border:1px solid rgba(223,229,249,1);*/
  text-align: center;
}
.leftMenu {
  width: 150px;
  float: left;
  height: 100%;
  background: #060e26;
}
.el-menu-vertical-demo {
  margin-top: 32px;
}
.content {
  width: calc(100% - 150px);
  float: left;
  height: 100%;
}
.contentInfo {
  height: 100%;
  width: 100%;
  padding: 5px;
}
.infoItem {
  width: 50%;
  float: left;
  color: #fff;
  height: 35px;
  line-height: 35px;
  font-size: 14px;
}
.infoItemLabel {
  width: 50%;
  display: inline-block;
  text-align: right;
  letter-spacing: 1px;
  font-weight: bold;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.infoItemValue {
  width: 50%;
  display: inline-block;
  text-align: left;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
