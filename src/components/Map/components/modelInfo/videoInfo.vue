<!--视频查看面板-->
<template>
  <div class="detailMain">
    <video
      style="width: 100%; height: 100%"
      src="https://www.w3school.com.cn/i/movie.ogg"
      controls="controls"
    >
      your browser does not support the video tag
    </video>
  </div>
</template>

<script setup>
defineOptions({
  name: "videoInfo",
});
</script>

<style scoped>
.detailMain {
  height: calc(100% - 0px);
  width: 100%;
  background: rgba(6, 14, 38, 0.48);
  border: 2px solid #00c3ff;
}
.modelMain {
  /*opacity: .8;*/
  /*background-color: #fff;*/
  border-radius: 4px 3px 4px 4px;
  /*border:1px solid rgba(223,229,249,1);*/
  text-align: center;
}
.leftMenu {
  width: 150px;
  float: left;
  height: 100%;
}
.content {
  width: calc(100% - 150px);
  float: left;
  height: 100%;
}
.contentInfo {
  height: 100%;
  width: 100%;
  padding: 5px;
}
.infoItem {
  width: 50%;
  float: left;
  color: #fff;
  height: 35px;
  line-height: 35px;
  font-size: 14px;
}
.infoItemLabel {
  width: 60%;
  display: inline-block;
  text-align: right;
  letter-spacing: 1px;
  font-weight: bold;
}
.infoItemValue {
  width: 40%;
  display: inline-block;
  text-align: left;
}
</style>
