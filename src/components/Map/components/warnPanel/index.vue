<!--预警相关信息提示-->
<template>
  <div class="warn-panel" v-if="show && warnList.length > 0">
    <div class="warn-content-amin">
      <div
        :class="{ anim: animate == true }"
        @mouseenter="Stop"
        @mouseleave="Up"
      >
        <div
          class="warn-content"
          v-for="(item, index) in warnList"
          :key="index"
        >
          <div class="warn-content-info">
            <!-- 预警铃声开关 -->
            <div class="sound-switch">
              <el-button
                @click="toggleSound"
                :type="soundEnabled ? 'warning' : 'info'"
                size="small"
                circle
                :title="soundEnabled ? '关闭预警铃声' : '开启预警铃声'"
              >
                <img :src="soundEnabled ? voiceIcon : voiceMuteIcon" />
              </el-button>
            </div>
            <img class="warn-img" :src="img_warn" />
            <span class="warn-text">{{ item.remark }}</span>
          </div>
          <div class="bottom-btns">
            <el-button
              @click="closeCurrentPanel(item)"
              type="warning"
              size="small"
              plain
              style="
                border-radius: 0;
                background: rgba(253, 254, 190, 0);
                border: 1px solid #fdfebe;
                font-size: 14px;
                font-weight: 400;
                color: #e0dda7;
                line-height: 22px;
              "
              >关闭</el-button
            >
            <el-button
              type="warning"
              size="small"
              style="
                border-radius: 0;
                background: #fdfebe;
                font-size: 14px;
                font-weight: 400;
                color: #bb3900;
                line-height: 22px;
              "
              >收到</el-button
            >
          </div>
        </div>
      </div>
    </div>
    <!-- 音频元素 -->
    <audio ref="warnAudio" preload="auto" hidden>
      <source src="@/assets/audio/warn.wav" type="audio/wav" />
    </audio>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import img_warn from "../../images/warn_info.png";
import { ElMessage, ElMessageBox } from "element-plus";
import { queryWarningRelease, closeWarningRelease } from "@/api/warning/index";
import moment from "moment";
import voiceIcon from "@/assets/icon/voice.svg";
import voiceMuteIcon from "@/assets/icon/voice-mute.svg";

// 组件名称
defineOptions({
  name: "warn-panel",
});

// 响应式数据
const show = ref(true);
const warnList = ref([]);
const animate = ref(false);
const soundEnabled = ref(false); // 预警铃声开关，默认开启，先关闭
const audioTimer = ref(null); // 音频播放定时器
const audioEndedHandler = ref(null); // 音频结束事件处理函数
const warnAudio = ref(null); // 音频元素引用
const userInteracted = ref(false); // 用户是否已经与页面交互
const pendingAudioPlay = ref(false); // 是否有待播放的音频

// 常量和变量
const tickTime = 60; // 60秒
const scrollTime = 10 * 1000; // 10秒滚动一次
let timer = null;
let componentTimer = null;

// 查询参数
const queryParams = ref({
  adcd: "",
  lycode: "",
  warnType: "",
  status: 1, // 查预警中的
  pageNum: 1,
  pageSize: 10,
});

onMounted(() => {
  getWarnList();

  // 需要一个定时，轮询去查询
  timer = setInterval(() => {
    getWarnList();
  }, 1000 * tickTime);

  // 增加区域切换监听
  window.EventBus.$on("changeAreaSelect", (data) => {
    if (data.type === "adcd") {
      queryParams.value.adcd = data.code;
      queryParams.value.lycode = null;
    } else if (data.type === "watershed") {
      queryParams.value.adcd = null;
      queryParams.value.lycode = data.code;
    }
    // 刷新
    getWarnList();
  });

  // 只有当预警信息多于一条时才启动滚动（原 created 逻辑）
  if (warnList.value.length > 1) {
    componentTimer = setInterval(scroll, scrollTime);
  }

  // 添加用户交互事件监听器，用于解决音频自动播放限制
  const handleUserInteraction = () => {
    if (!userInteracted.value) {
      userInteracted.value = true;

      // 如果有待播放的音频，尝试播放
      if (
        pendingAudioPlay.value &&
        warnList.value.length > 0 &&
        soundEnabled.value
      ) {
        playWarnSound();
      }

      // 移除事件监听器，因为我们只需要一次用户交互
      document.removeEventListener("click", handleUserInteraction);
      document.removeEventListener("touchstart", handleUserInteraction);
      document.removeEventListener("keydown", handleUserInteraction);
    }
  };

  // 添加多种用户交互事件监听
  document.addEventListener("click", handleUserInteraction);
  document.addEventListener("touchstart", handleUserInteraction);
  document.addEventListener("keydown", handleUserInteraction);
});

// 生命周期钩子 - unmounted
onUnmounted(() => {
  // 在需要的时候清除定时器
  clearInterval(timer);
  if (componentTimer) {
    clearInterval(componentTimer);
  }
  // 停止预警声音并清理相关资源
  stopWarnSound();

  // 清理用户交互事件监听器
  const handleUserInteraction = () => {};
  document.removeEventListener("click", handleUserInteraction);
  document.removeEventListener("touchstart", handleUserInteraction);
  document.removeEventListener("keydown", handleUserInteraction);
});

// 方法
const getWarnList = () => {
  queryWarningRelease({
    pageNum: 1,
    pageSize: 999999,
    startTime: moment().subtract(7, "days").format("YYYY-MM-DD HH:mm:ss"),
    endTime: moment().format("YYYY-MM-DD HH:mm:ss"),
    // warnStatus: 1, // 默认选中是
  }).then((res) => {
    const prevWarnCount = warnList.value.length;
    warnList.value = res.data.records;
    // warnList.value = [
    //   {
    //     remark:'次角林村1组在2025-5-15 21:18:31累计危险区雨情预警112毫米，超过准备转移阈值110毫米，请及时通知危险区内群众准备转移避险！'
    //   },
    //   {
    //     remark:'白定村4组在2025-5-15 17:09:54累计危险区雨情预警151毫米，超过立即转移阈值150毫米，请及时通知危险区内群众准备转移避险！'
    //   },
    //   {
    //     remark:'蔡公堂村3组在2025-5-15 23:48:29预报山洪气象预警90豪米，发生气象风险的可能性较大！'
    //   },
    // ];

    // 根据预警信息数量决定是否启动滚动
    updateScrollTimer();

    nextTick(() => {
      // 如果有预警信息且预警数量发生变化，播放预警声音
      if (
        warnList.value.length > 0 &&
        warnList.value.length !== prevWarnCount
      ) {
        playWarnSound();
      } else if (warnList.value.length === 0) {
        // 如果没有预警信息，停止声音
        stopWarnSound();
      }
    });
  });
};

// 播放音频的函数
const playAudio = (audio) => {
  // 如果声音开关关闭或没有预警信息，停止播放
  if (!soundEnabled.value || warnList.value.length === 0) {
    stopWarnSound();
    return;
  }

  // 重置音频并播放
  audio.currentTime = 0;

  // 尝试播放音频
  const playPromise = audio.play();

  // 处理可能的播放错误
  if (playPromise !== undefined) {
    playPromise
      .then(() => {
        console.log("音频播放成功");
      })
      .catch((error) => {
        console.error("音频播放失败:", error);

        // 根据错误类型提供不同的处理
        if (error.name === "NotAllowedError") {
          console.log("浏览器阻止了音频自动播放，等待用户交互...");
          pendingAudioPlay.value = true;
        } else if (error.name === "NotSupportedError") {
          console.error("音频格式不支持");
        } else {
          console.error("其他音频播放错误:", error.message);
        }
      });
  }
};

// 播放预警声音
const playWarnSound = () => {
  // 如果声音开关关闭，不播放声音
  if (!soundEnabled.value) return;

  // 获取音频元素
  const audio = warnAudio.value;
  if (!audio) return;

  // 如果用户尚未与页面交互，标记为待播放并返回
  if (!userInteracted.value) {
    pendingAudioPlay.value = true;
    console.log("音频播放等待用户交互...");
    return;
  }

  // 用户已交互，可以播放音频
  pendingAudioPlay.value = false;

  // 清除之前的定时器
  if (audioTimer.value) {
    clearInterval(audioTimer.value);
    audioTimer.value = null;
  }

  // 立即播放一次
  playAudio(audio);

  // 创建音频结束事件处理函数
  audioEndedHandler.value = () => {
    // 播放结束后，等待10秒再播放下一次
    audioTimer.value = setTimeout(() => {
      playAudio(audio);
    }, 10000); // 10秒间隔
  };

  // 移除之前的事件监听器（如果有的话）
  if (audioEndedHandler.value) {
    audio.removeEventListener("ended", audioEndedHandler.value);
  }
  // 添加新的事件监听器
  audio.addEventListener("ended", audioEndedHandler.value);
};

// 停止预警声音
const stopWarnSound = () => {
  const audio = warnAudio.value;
  if (audio) {
    audio.pause();
    audio.currentTime = 0;
    // 移除事件监听器
    if (audioEndedHandler.value) {
      audio.removeEventListener("ended", audioEndedHandler.value);
      audioEndedHandler.value = null;
    }
  }

  // 清除定时器
  if (audioTimer.value) {
    clearTimeout(audioTimer.value);
    audioTimer.value = null;
  }

  // 清除待播放标志
  pendingAudioPlay.value = false;
};

// 切换声音开关
const toggleSound = () => {
  soundEnabled.value = !soundEnabled.value;

  if (soundEnabled.value && warnList.value.length > 0) {
    // 如果开启声音且有预警，播放声音
    playWarnSound();
  } else {
    // 如果关闭声音，停止播放
    stopWarnSound();
  }
};

const updateScrollTimer = () => {
  // 清除现有定时器
  if (componentTimer) {
    clearInterval(componentTimer);
    componentTimer = null;
  }
  // 只有当预警信息多于一条时才启动滚动
  if (warnList.value.length > 1) {
    componentTimer = setInterval(scroll, scrollTime);
  }
};

const scroll = () => {
  // 如果只有一条预警信息，不执行滚动
  if (warnList.value.length <= 1) return;

  animate.value = true;
  setTimeout(() => {
    warnList.value.unshift(warnList.value.pop());
    animate.value = false;
  }, 500);
};

//鼠标移上去停止
const Stop = () => {
  if (componentTimer) {
    clearInterval(componentTimer);
    componentTimer = null;
  }
};

const Up = () => {
  // 只有当预警信息多于一条时才重新启动滚动
  if (warnList.value.length > 1) {
    componentTimer = setInterval(scroll, scrollTime);
  }
};

const closeCurrentPanel = (item) => {
  // 关闭当前面板可能还有别的预警信息？
  ElMessageBox.confirm('确定关闭预警对象为【"' + item.name + '"】的预警?', {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      closeWarningRelease(item.id).then((res) => {
        if (res.code === 200) {
          ElMessage.info("关闭预警成功！");
          getWarnList();
        } else {
          ElMessage.error(res.msg);
        }
      });
    })
    .then(() => {
      getWarnList();
      // ElMessage.info("关闭预警成功！");
    })
    .catch(() => {});
};
</script>

<style scoped lang="scss">
.warn-panel {
  position: absolute;
  left: 630px;
  top: 110px;
  padding: 2px;
  height: 35px;
  z-index: 10;
  display: flex;
  align-items: center;
}

.sound-switch {
  display: flex;
  align-items: center;

  .el-button {
    width: 16px;
    height: 16px;
    background: rgba(0, 0, 0, 0);
    border: none;

    img {
      width: 16px;
      height: 16px;
      filter: brightness(0) invert(1);
    }
  }
}

.weather-panel {
  float: left;
  margin-left: 5px;
  border-radius: 3px;
  border: 2px solid #555;
  width: 87px;
  height: 75px;
}

.warn-level {
  float: left;
  margin-left: 5px;
  border-radius: 3px;
  border: 2px solid #fff;
  width: 85px;
  height: 75px;
}

.warn-content-amin {
  width: 1080px;
  float: right;
  height: 35px;
  margin-left: 15px;
  overflow: hidden;
}

.warn-content {
  // display: inline-block;
  width: 1060px;
  float: right;
  height: 35px;
  background: linear-gradient(
    to right,
    rgba(154, 151, 151, 0.1) 5%,
    rgb(204, 71, 15) 50%,
    rgba(154, 151, 151, 0.1) 95%
  );
  border: 1px solid rgba(255, 80, 3, 0.98);
  position: relative;
}

.warn-content-info {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 15px;

  .warn-img {
    width: 42px;
    height: 42px;
  }

  .warn-text {
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #fdfec0;
    line-height: 22px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.bottom-btns {
  float: right;
  right: 20px;
  position: absolute;
  bottom: 4px;
}

.anim {
  transition: all 0.5s ease;
  transform: translateX(-100%);
}

/* 移除不需要的样式 */
.weather-panel,
.warn-level,
.bottom-btns {
  display: none;
}
</style>
