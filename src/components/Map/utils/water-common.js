// 根据降雨量获取降雨类型
export function getRainTypeByDrp(dropRain) {
    // 定义返回的文本和颜色
    let text = "";
    let colorStr = "#D7D7D7";
    // 如果降雨量为0，则返回灰色
    if (dropRain == 0) {
        colorStr = "#D7D7D7";
    }
    // 如果降雨量大于0且小于等于10，则返回浅绿色，文本为小雨
    else if (dropRain > 0 && dropRain <= 10) {
        colorStr = "#BAEF9F";
        text = "小雨";
    }
    // 如果降雨量大于10且小于等于25，则返回绿色，文本为中雨
    else if (dropRain > 10 && dropRain <= 25) {
        colorStr = "#55DD33";
        text = "中雨";
    }
    // 如果降雨量大于25且小于等于50，则返回浅蓝色，文本为大雨
    else if (dropRain > 25 && dropRain <= 50) {
        colorStr = "#7FBDFF";
        text = "大雨";
    }
    // 如果降雨量大于50且小于等于100，则返回蓝色，文本为暴雨
    else if (dropRain > 50 && dropRain <= 100) {
        colorStr = "#000DFF";
        text = "暴雨";
    }
    // 如果降雨量大于100且小于等于250，则返回紫色，文本为大暴雨
    else if (dropRain > 100 && dropRain <= 250) {
        colorStr = "#E434EF";
        text = "大暴雨";
    }
    // 如果降雨量大于250，则返回棕色，文本为特大暴雨
    else if (dropRain > 250) {
        colorStr = "#7F0341";
        text = "特大暴雨";
    }
    // 返回文本和颜色
    let levelItem = {
        text: text,
        color: colorStr
    }
    return levelItem
}
export function getFloodTypeByValue(value) {
    // let colorStr = "#5906a2"
    let colorStr = "#fff"
    let text = ''
    if (value == 0) {
        // colorStr="#5906a2";
        colorStr = "#fff";
    }
    else if (value > 0 && value <= 0.2) {
        colorStr = "#7401d9";
        text = "0-0.2";
    }
    else if (value > 0.2 && value <= 0.4) {
        colorStr = "#3d01f6";
        text = "中雨";
    }
    else if (value > 0.4 && value <= 0.8) {
        colorStr = "#0201f8";
        text = "大雨";
    }
    else if (value > 0.8 && value <= 1.6) {
        colorStr = "#024ff5";
        text = "暴雨";
    }
    else if (value > 1.6 && value <= 2.4) {
        colorStr = "#0095f4";
        text = "大暴雨";
    }
    else if (value > 2.4 && value <= 3.2) {
        colorStr = "#02d5d8";
        text = "大暴雨";
    }
    else if (value > 3.2 && value <= 4.0) {
        colorStr = "#02f596";
        text = "大暴雨";
    }
    else if (value > 4.0 && value <= 4.8) {
        colorStr = "#02fd4b";
        text = "大暴雨";
    }
    else if (value > 4.8 && value <= 5.6) {
        colorStr = "#02fb09";
        text = "大暴雨";
    }
    else if (value > 5.6 && value <= 6.4) {
        colorStr = "#79fd00";
        text = "大暴雨";
    }
    else if (value > 6.4 && value <= 7.2) {
        colorStr = "#dafe02";
        text = "大暴雨";
    }
    else if (value > 7.2 && value <= 8.0) {
        colorStr = "#fdfd02";
        text = "大暴雨";
    }
    else if (value > 8.0 && value <= 8.8) {
        colorStr = "#fbdb03";
        text = "大暴雨";
    }
    else if (value > 8.8 && value <= 9.6) {
        colorStr = "#fe7800";
        text = "大暴雨";
    }
    else if (value > 9.6) {
        colorStr = "#fb0201";
        text = "特大暴雨";
    }
    let flv = {
        text: text,
        color: colorStr
    }
    return flv
}

export function getSTTPName(type) {

    let text = '测站'
    switch (type) {
        case "PP":
            text = '雨量站'
            break
        case "ZP":
            text = '同位站'
            break
        case "RP":
            text = '同位站'
            break
        case "RR"://
            text = '水库站'
            break
        case "ZZ"://
            text = '河道水位站'
            break
        case "ZQ":// ZZ河道水位站，ZQ河道水文站
            text = '河道水文站'
            break
        case "VV"://
            text = '视频站'
            break
        case "SS"://
            text = '墒情站'
            break
        case "II"://
            text = '图像站'
            break
        case "DR"://
            text = '排水站'
            break
        case "MM"://
            text = '气象站'
            break
        case "BB"://
            text = '蒸发站'
            break
        case "DD"://
            text = '堰闸站'
            break
    }
    return text
}

// 格式化雨量值
export function formatRainValue(value) {
    return Number(value).toFixed(1)
}

// 格式化河道值
export function formatRiverValue(value) {
    if (value) {
        return Number(value).toFixed(2)
    } else {
        return '--'
    }
}
// 水情列表排序
export function riverDataSort(list) {
    let ll1 = []
    let ll2 = []
    let ll3 = []
    let ll4 = []
    let ll5 = []
    list.forEach(item => {
        if (item.WARNTP) {
            if (item.WARNTP.indexOf('历史') > -1) {
                ll1.push(item)
            } else if (item.WARNTP.indexOf('保证') > -1) {
                ll2.push(item)
            } else if (item.WARNTP.indexOf('警戒') > -1) {
                ll3.push(item)
            }
        } else {
            if (item.Z) {
                ll4.push(item)
            } else {
                ll5.push(item)
            }
        }
    })
    ll1 = ll1.sort((a, b) => {
        return Number(b.WARNTP.replace('超历史水位', '')) - Number(a.WARNTP.replace('超历史水位', ''))
    })
    ll2 = ll2.sort((a, b) => {
        return Number(b.WARNTP.replace('超保证', '')) - Number(a.WARNTP.replace('超保证', ''))
    })
    ll3 = ll3.sort((a, b) => {
        return Number(b.WARNTP.replace('超警戒', '')) - Number(a.WARNTP.replace('超警戒', ''))
    })
    ll4 = ll4.sort((a, b) => {
        let a1 = a.Z || 0
        let b1 = b.Z || 0
        return b1 - a1
    })
    return ll1.concat(ll2, ll3, ll4, ll5)
}
export function riverDataSort2(list) {
    let ll1 = []
    let ll2 = []
    let ll3 = []
    let ll4 = []
    let ll5 = []
    list.forEach(item => {
        if (item.warnType) {
            if (item.warnType == 1) {
                ll1.push(item)
            } else if (item.warnType == 2) {
                ll2.push(item)
            } else if (item.warnType == 3) {
                ll3.push(item)
            }
        } else {
            if (item.Z) {
                ll4.push(item)
            } else {
                ll5.push(item)
            }
        }
    })
    ll1 = ll1.sort((a, b) => {
        return Number(b.warnTypeVal.replace('超警戒', '')) - Number(a.warnTypeVal.replace('超警戒', ''))
    })
    ll2 = ll2.sort((a, b) => {
        return Number(b.warnTypeVal.replace('超保证', '')) - Number(a.warnTypeVal.replace('超保证', ''))
    })
    ll3 = ll3.sort((a, b) => {
        return Number(b.warnTypeVal.replace('超历史', '')) - Number(a.warnTypeVal.replace('超历史', ''))
    })
    ll4 = ll4.sort((a, b) => {
        let a1 = a.waterLevel || 0
        let b1 = b.waterLevel || 0
        return b1 - a1
    })
    return ll1.concat(ll2, ll3, ll4, ll5)
}

export function riverDataFilter(key, list) {
    let ll
	ll = list.filter((item) => {
        if (item.WARNTP) {
            return item.WARNTP.indexOf(key) > -1
        } else {
            return false
        }
    })
    return ll
}

export function getRiverColor(item) {
    let color = "#35BFFF";
    if (item.indexOf('历史') > -1) {
        color = "#FF7E01";
    } else if (item.indexOf('保证') > -1) {
        color = "#FFA92F";
    } else if (item.indexOf('警戒') > -1) {
        color = "#FFEA35";
    }
    return color
}

// 获取行政过滤参数
export function getAdcdOrLycode(data) {
    if (data.lycode) {
        return { lyCode: data.lycode }
    } else {
        return { ad: data.adcd }
    }
}
// 根据降雨量获取降雨类型
export function getRainTypeByDrpOnUE(dropRain) {
    // 定义返回的文本和颜色
    let text = "";
    let colorStr = "#FFFFFF";
    // 如果降雨量为0，则返回白色
    if (dropRain == 0) {
        colorStr = "#FFFFFF";
    }
    // 如果降雨量大于0且小于等于10，则返回浅绿色，文本为小雨
    else if (dropRain > 0 && dropRain <= 10) {
        colorStr = "green";
        text = "小雨";
    }
    // 如果降雨量大于10且小于等于25，则返回绿色，文本为中雨
    else if (dropRain > 10 && dropRain <= 25) {
        colorStr = "blue";
        text = "中雨";
    }
    // 如果降雨量大于25且小于等于50，则返回浅蓝色，文本为大雨
    else if (dropRain > 25 && dropRain <= 50) {
        colorStr = "yellow";
        text = "大雨";
    }
    // 如果降雨量大于50且小于等于100，则返回蓝色，文本为暴雨
    else if (dropRain > 50 && dropRain <= 100) {
        colorStr = "grey";
        text = "暴雨";
    }
    // 如果降雨量大于100且小于等于250，则返回紫色，文本为大暴雨
    else if (dropRain > 100 && dropRain <= 250) {
        colorStr = "red";
        text = "大暴雨";
    }
    // 如果降雨量大于250，则返回红色，文本为特大暴雨
    else if (dropRain > 250) {
        colorStr = "red";
        text = "特大暴雨";
    }
    // 返回文本和颜色
    let levelItem = {
        text: text,
        color: colorStr
    }
    return levelItem
}