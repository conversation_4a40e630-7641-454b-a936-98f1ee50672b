<template>
  <div class="svgPanel">
    <div class="svg-box">
      <div class="svg-dialog__header">
        <!-- 标题部分 -->
        <span class="svg-dialog__title"> {{ SVGTitle }}</span>
        <div class="svg-dialog__headerBtn" @click="hideSVG">
          <el-icon><CloseBold /></el-icon>
        </div>
      </div>
      <div id="svg-dialog__body" v-loading="embedLoading">
        <!-- SVG嵌入部分 -->
        <embed type="image/svg+xml" id="svg-trigger" :src="currentSvg" />
      </div>
    </div>
  </div>
</template>

<script setup>
import svgPanZoom from "svg-pan-zoom";

defineOptions({
  name: "svgPanel",
});

let panZoomTiger = null;
let svgCon = null;
let svgTiger = null;
let svgDoc = null;

const embedLoading = ref(false); // 加载
const SVGTitle = ref("概化图"); // 窗口标题
const currentSvg = ref(null); // SVG地址
const oldTargetNode = ref([]); // 记录点击的节点
const jumpSVGData = ref({}); // 记录跳转的数据

onMounted(() => {
  getData(); // 获取窗口数据，显示SVG
});

const hideSVG = () => {
  window.EventBus.$emit("module/type/change", { name: "防洪预演" });
};
// 获取窗口数据，显示SVG
const getData = () => {
  // 这里根据后端的接口获取了SVG的地址，赋值给了currentSvg
  currentSvg.value = "./datas/svgs/gaihua.svg";
  // 赋值后调用展示SVG方法
  getZoomTiger();
};
// 获取SVG窗口
// 跳转时addEventListener必须移除load
const getZoomTiger = () => {
  panZoomTiger = document.getElementById("svg-trigger");
  panZoomTiger.addEventListener("load", waitLoad);
};
// 定义svgPanZoom
// 跳转时addEventListener必须移除click
const waitLoad = () => {
  svgTiger = svgPanZoom("#svg-trigger", {
    viewportSelector: ".svg-pan-zoom_viewport",
    preventMouseEventsDefault: false,
  });
  svgCon = panZoomTiger.getSVGDocument().querySelector("svg");
  svgCon.style = "cursor: pointer;";
  svgCon.addEventListener("click", svgClick);
};
// 点击SVG节点操作
const svgClick = (evt) => {
  let newObj = [];
  if (evt.target.nodeName === "text") {
    // 判断点击节点是否存在path
    if (evt.target.parentNode.children[0].nodeName !== "text") {
      newObj.push(evt.target);
    } else if (evt.target.parentNode.children[0].nodeName === "text") {
      newObj = evt.target.parentNode.children;
    }
    // 记录点击的节点
    // 用于下次点击时取消上次点击节点的高亮
    if (oldTargetNode.value.length === 0) {
      // 最开始记录
      oldTargetNode.value = newObj;
    } else if (oldTargetNode.value.length > 0) {
      // 高亮过取消,重新记录
      resetHightLight(newObj);
    }
    // 高亮当前点击
    hightLight(newObj, false);
  }
};
// 高亮SVG节点
const hightLight = (ids, flag) => {
  // let targetCon
  for (let i = 0; i < ids.length; i++) {
    ids[i].style.fill = "rgb(255,0,0)";
    ids[i].style.stroke = "rgb(255,0,0)";
    ids[i].style.strokeWidth = 0.3;
    // if (i === 0) targetCon = ids[i].getBoundingClientRect()
  }
  // 可有可无，看自己个人感觉无效果，但也不影响代码和页面就先放着了
  // if (flag) {
  //  setTimeout(() => {
  //    svgTiger.zoomAtPoint(1.5, {x: targetCon.x, y: targetCon.y})
  //  }, 100)
};
// 取消高亮
const resetHightLight = (ids) => {
  for (let i = 0; i < oldTargetNode.value.length; i++) {
    oldTargetNode.value[i].style.fill = "rgb(0, 0, 0)";
    oldTargetNode.value[i].style.stroke = "rgb(0, 0, 0)";
    oldTargetNode.value[i].style.strokeWidth = 0;
  }
  oldTargetNode.value = ids;
};
// 在SVG中创建并加入新的元素
// full: matrix中前四位算法（后端返回）
// X：横坐标（后端返回）
// Y：纵坐标（后端返回）
const addSVGElement = (full, x, y) => {
  svgDoc = panZoomTiger
    .getSVGDocument()
    .getElementsByClassName("svg-pan-zoom_viewport")[0]
    .getElementsByTagName("g")[0];
  const newRect = document.createElementNS(
    "http://www.w3.org/2000/svg",
    "rect"
  ); // 创建一个svg元素
  const tMatrix = `matrix(${full} ${x} ${y})`;
  newRect.setAttribute("transform", tMatrix);
  newRect.setAttribute("width", "30");
  newRect.setAttribute("height", "30");
  newRect.setAttribute("fill", "rgb(0,0,0)");
  newRect.setAttribute("fill-opacity", "0");
  newRect.setAttribute("stroke", "rgb(255,0,0)");
  newRect.setAttribute("stroke-width", 1);
  newRect.setAttribute("x", -15); // 宽度的一半
  newRect.setAttribute("y", -15); // 高度的一半
  svgDoc.appendChild(newRect);
};
// 在SVG中移除某个元素元素
const removeSVGElement = () => {
  const removeRect = svgDoc.getElementsByTagName("rect");
  while (removeRect.length > 0) {
    const SVGRect = removeRect[0];
    removeRect[0].parentNode.removeChild(SVGRect);
  }
};
// 获取搜索节点
const getSearchText = (text) => {
  const con = panZoomTiger
    .getSVGDocument()
    .getElementsByClassName("svg-pan-zoom_viewport")[0]
    .getElementsByTagName("g")[0]
    .getElementsByTagName("g")[0];
  let objText = [];
  const returnVal = recSearch(con.children, text);
  // 如果有就高亮
  if (returnVal) {
    objText.push(returnVal);
    oldTargetNode.value = objText;
    hightLight(objText, false);
  }
};

// 循环查找，找到第一个匹配的则不继续查找
const recSearch = (lists, text) => {
  const hasChildrenAttr = function (obj) {
    return obj.children.length !== 0;
  };
  for (let i = 0; i < lists.length; i++) {
    if (lists[i].innerHTML.trim().toLowerCase() === text.toLowerCase()) {
      return lists[i];
    } else if (hasChildrenAttr(lists[i])) {
      const result = recSearch(lists[i].children, text);
      if (result) {
        return result;
      }
    }
  }
  return null;
};
const enlargeSVGEmbed = (targetCon) => {
  // 获取SVG窗口宽高
  const svgBoxWidth = this.$refs.svgDialog.offsetWidth; // 窗口宽
  const svgBoxHeight = this.$refs.svgDialog.offsetHeight; // 窗口高
  // 节点相对中心位置偏移度数
  const targetCenterHeight =
    svgBoxHeight / 2 - targetCon.top - (targetCon.height / 2) * 3;
  const targetCenterWidth =
    svgBoxWidth / 2 - targetCon.left + targetCon.width * 3;
  svgTiger.pan({ x: targetCenterWidth, y: targetCenterHeight });
  svgTiger.zoom(3);
};
</script>

<style scoped lang="scss">
.svgPanel {
  position: absolute;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  background: rgba(2, 39, 87, 0.95);
  top: 26px;
  left: 30px;
  right: 560px;
  bottom: 18px;
  border: 1px #1c8bda solid;
}
.svg-box {
  width: 100%;
  height: 100%;
}
.svg-dialog__header {
  padding: 20px 20px 10px;

  .svg-dialog__title {
    line-height: 24px;
    font-size: 18px;
    color: #fff;
  }

  .svg-dialog__headerBtn {
    position: absolute;
    width: 18px;
    height: 18px;
    top: 20px;
    right: 20px;
    padding: 0;
    background: transparent;
    border: none;
    outline: none;
    cursor: pointer;
    font-size: 18px;
    color: #fff;
    &:hover {
      color: #409eff !important;
    }
  }
}

#svg-dialog__body {
  height: calc(100% - 54px);

  #svg-trigger {
    width: 100%;
    height: 100%;
  }
}
</style>
