<!--政区，流域选择组件-->
<template>
  <div class="area-select">
    <el-dropdown
      class="w-full"
      trigger="click"
      popper-class="dark-select-dropdown"
    >
      <el-input
        v-model="areaText"
        placeholder="请选择区域"
        class="input-with-select"
      >
        <template #prefix>
          <span>区域</span>
        </template>
        <template #suffix>
          <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </template>
      </el-input>
      <template #dropdown>
        <div class="selectPanel">
          <!-- 返回初始区域按钮和选择类型区域 -->
          <div class="control-area">
            <el-button
              @click="gotoInit"
              plain
              type="primary"
              size="small"
              class="back-btn"
              >初始区域</el-button
            >
            <el-radio-group v-model="radio" class="area-type-group">
              <el-radio :label="1">行政区</el-radio>
              <el-radio :label="2">流域</el-radio>
            </el-radio-group>
          </div>
          <!-- 行政区树形选择 -->
          <el-tree
            v-show="radio == 1"
            class="panel-tree"
            :data="districts"
            :props="districtTreeProps"
            @node-click="handleDistrictNodeClick"
          />
          <!-- 流域树形选择 -->
          <el-tree
            v-show="radio == 2"
            class="panel-tree"
            :data="watersheds"
            :props="watershedTreeProps"
            @node-click="handleWatershedNodeClick"
          />
        </div>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import axios from "axios";
import {
  getAdcBoundLayerByTD,
  getTreeOption,
  selectStlyInfo,
  selectStlyList,
} from "@/api/watershed/ads";
import useScreenStore from "@/store/modules/screen";
import { onMounted } from "vue";

defineOptions({
  name: "areaSelect",
});

// 响应式变量定义
const areaText = ref("全域"); // 当前选中的区域名称
const activeName = ref("district"); // 当前激活的标签页
const districts = ref([]); // 行政区树数据
const watersheds = ref([
  {
    adnm: "滦河流域",
    adcd: "1",
    children: [
      {
        adnm: "滦河潘家口以下",
        adcd: "2",
      },
      {
        adnm: "滦河潘家口以上",
        adcd: "3",
      },
    ],
  },
]); // 流域树数据
const code = ref(""); // 当前选中的区域编码
const radio = ref(1); // 当前选中的类型（1：行政区，2：流域）
const regions = ref(""); // 区域边界坐标串

// 树形控件配置
const districtTreeProps = {
  children: "children",
  label: "adnm",
};

const watershedTreeProps = {
  children: "children",
  label: "name",
};

// GeoServer 相关配置
const geoserverUrl = "/geoserver/hc";
let dataSource = null; // 当前数据源

/**
 * 格式化树形数据
 * @param {Array} data - 原始树形数据
 * @returns {Array} 格式化后的树形数据
 */
const formatTree = (data) => {
  return data.map((item) => {
    return {
      ...item.data,
      children: item.children && formatTree(item.children),
    };
  });
};

/**
 * 获取初始边界数据
 */
const getInitBound = async () => {
  // 获取流域code
  let params = {
    lycode: "",
  };
  let res2 = await selectStlyList(lycode);
  let lycode = res2.data[0].lycode;
  // 获取流域边界
  const res = await selectStlyInfo(lycode);
  if (res.data.features?.length === 0) return;
  let fs = res.data.features[0];
  let coordinates = fs.geometry.coordinates[0][0];
  let regions = "";
  coordinates.forEach((pos) => {
    regions += pos[0] + "," + pos[1] + ",";
  });
  regions = regions.substring(0, regions.length - 2);
  regions.value = regions;
};

/**
 * 返回初始区域
 */
const gotoInit = async () => {
  areaText.value = "全域";
  if (dataSource) {
    window.viewer.dataSources.remove(dataSource);
  }
  window.viewer.entities.removeAll();
  window.EventBus.$emit("changeAreaSelect", {
    // regions: "116.640419,42.730239,116.634651,42.674446,116.658655,42.670507,116.672272,42.647198,116.686169,42.657083,116.723588,42.607354,116.739692,42.617608,116.764134,42.600332,116.775286,42.555756,116.802152,42.555318,116.829436,42.577961,116.830232,42.602587,116.894856,42.578583,116.93482,42.607382,116.992686,42.558151,117.042829,42.543365,117.097509,42.574595,117.178934,42.511088,117.220899,42.498547,117.351849,42.534881,117.374214,42.532061,117.379158,42.515344,117.426698,42.514562,117.462106,42.460661,117.435485,42.453416,117.443918,42.425296,117.427414,42.403712,117.397706,42.400034,117.377534,42.378037,117.3957,42.346441,117.439116,42.324027,117.466636,42.340782,117.515921,42.342519,117.534008,42.274689,117.588333,42.272098,117.603081,42.257152,117.633473,42.261526,117.727085,42.230091,117.770148,42.237233,117.765765,42.094931,117.744178,42.048811,117.773259,42.045039,117.798651,42.007789,117.792392,41.964421,117.93809,42.0063,117.957751,41.96064,118.015411,41.943917,118.056943,41.961185,118.10206,41.931062,118.089672,41.812684,118.135214,41.75061,118.128216,41.716072,118.212162,41.640329,118.228663,41.576822,118.312596,41.558949,118.300416,41.543708,118.316427,41.50825,118.26442,41.464153,118.335239,41.443315,118.359508,41.386027,118.341871,41.369714,118.347421,41.329928,118.394519,41.308437,118.433779,41.266995,118.406865,41.242632,118.443306,41.217067,118.444205,41.193614,118.512195,41.179628,118.532737,41.161936,118.567316,41.171738,118.693512,41.14586,118.762523,41.178307,118.763481,41.147313,118.827576,41.117755,118.814152,41.095983,118.837138,41.08267,118.88943,41.092948,118.929666,41.051,118.955771,41.07457,118.955027,41.103807,119.046996,41.143439,119.115856,41.104154,119.177795,41.016808,119.215036,41.020081,119.233961,40.995951,119.277024,41.00009,119.255816,40.958738,119.271641,40.911442,119.242876,40.906011,119.251757,40.857014,119.211359,40.793879,119.261148,40.778056,119.264024,40.759515,119.313135,40.751869,119.359876,40.709731,119.367737,40.681592,119.401051,40.683997,119.446639,40.647312,119.406739,40.569674,119.421302,40.539987,119.474884,40.531866,119.491543,40.554168,119.543423,40.554117,119.562952,40.532524,119.540889,40.508973,119.593522,40.462783,119.587676,40.391714,119.57144,40.379164,119.596336,40.362118,119.580354,40.342692,119.709671,40.281542,119.673792,40.266526,119.664381,40.241308,119.682955,40.207667,119.709639,40.194477,119.735092,40.206297,119.745428,40.188563,119.754741,40.138844,119.718032,40.087559,119.764596,40.080963,119.742153,40.053432,119.81557,40.028941,119.840819,39.987515,119.802766,39.977641,119.784339,39.94868,119.69161,39.93658,119.677194,39.913756,119.664269,39.937105,119.610511,39.904941,119.555352,39.902607,119.526488,39.872184,119.529567,39.808082,119.464304,39.812082,119.363149,39.744218,119.257476,39.514234,119.258067,39.488679,119.302841,39.465693,119.293731,39.3871,119.253123,39.361699,119.192886,39.356721,119.0937,39.252544,119.01888,39.202888,119.014603,39.196264,119.005541,39.195544,118.966167,39.15786,118.849216,39.095085,118.792316,39.08999,118.786441,39.083624,118.778542,39.079641,118.751305,39.068502,118.73238,39.071631,118.714843,39.07298,118.666079,39.074776,118.646833,39.120758,118.630499,39.112428,118.601573,39.123509,118.605138,39.104096,118.551185,39.175818,118.533283,39.289405,118.560445,39.373361,118.540073,39.41287,118.574026,39.456699,118.59625,39.68017,118.565661,39.755597,118.611815,39.80007,118.591114,39.845385,118.609752,39.855484,118.629898,39.945261,118.602177,39.969286,118.611141,39.989649,118.593239,39.991383,118.592415,40.03248,118.54164,40.107108,118.516309,40.106501,118.476337,40.037562,118.437967,40.02701,118.453542,40.101579,118.431147,40.128072,118.386687,40.143622,118.353614,40.125327,118.260072,40.118278,118.183259,40.147606,118.159243,40.173027,118.195311,40.18797,118.19774,40.217551,118.131942,40.304154,118.093183,40.283811,118.07331,40.307122,118.048285,40.301446,117.997866,40.326162,117.941251,40.321627,117.935439,40.353362,117.885202,40.320438,117.842159,40.33492,117.781104,40.303514,117.745811,40.322358,117.752731,40.356835,117.688999,40.355206,117.673248,40.384116,117.612186,40.376444,117.58434,40.333493,117.592635,40.313638,117.571026,40.307166,117.528252,40.35305,117.470945,40.3572,117.424142,40.379219,117.447454,40.414073,117.400026,40.457878,117.436396,40.483562,117.423731,40.501358,117.454941,40.558986,117.448015,40.577681,117.483153,40.594982,117.498987,40.587508,117.527511,40.622239,117.568867,40.630035,117.573538,40.655038,117.508103,40.652688,117.497926,40.66562,117.5213,40.703688,117.49677,40.759834,117.434073,40.762957,117.384916,40.821494,117.356772,40.821288,117.315137,40.865173,117.300654,40.862556,117.28434,40.956051,117.199936,41.011357,117.21409,41.046195,117.190728,41.118927,117.168103,41.141428,117.124392,41.14292,117.089156,41.218173,116.990824,41.220608,116.878654,41.285525,116.87267,41.310443,116.845163,41.313848,116.806716,41.358285,116.732279,41.387512,116.771136,41.451231,116.716273,41.52257,116.715711,41.552031,116.663541,41.583653,116.664252,41.60828,116.638586,41.609097,116.628527,41.624962,116.544131,41.610553,116.528766,41.578828,116.460747,41.534216,116.409455,41.567773,116.381554,41.554872,116.332995,41.564923,116.274176,41.518922,116.225344,41.517063,116.21551,41.46963,116.158629,41.448825,116.14119,41.359955,116.040025,41.387297,116.02772,41.420106,116.003456,41.429315,116.004313,41.454796,115.975912,41.46914,115.966927,41.518185,115.893639,41.533745,115.837752,41.513471,115.816367,41.536601,115.816979,41.568795,115.782251,41.566632,115.749468,41.588113,115.730103,41.780346,115.772588,41.838878,115.760371,41.879261,115.687096,41.913108,115.619106,41.921988,115.597592,41.954813,115.562396,41.967054,115.535279,42.020766,115.549516,42.038877,115.593169,42.046137,115.61923,42.023532,115.696842,42.032823,115.719734,42.073952,115.778716,42.054339,115.774595,42.080178,115.818064,42.098414,115.791581,42.140269,115.815699,42.160508,115.798199,42.221993,115.859453,42.233872,115.887828,42.290866,115.942744,42.306015,115.937852,42.32645,115.971323,42.338458,115.980622,42.363748,116.031343,42.380842,116.016902,42.391972,116.056266,42.41931,116.043033,42.430606,116.053749,42.4709,116.119375,42.488294,116.138418,42.523968,116.166916,42.516377,116.180071,42.545578,116.243081,42.544476,116.26197,42.56868,116.309058,42.578201,116.290301,42.621071,116.341616,42.638482,116.327484,42.648752,116.369303,42.673745,116.427646,42.686577,116.495468,42.673955,116.485221,42.693076,116.537464,42.700051,116.543598,42.723555,116.584706,42.715272,116.640419,42.730239",
    regions: regions.value,
    code: "",
    type: "adcd",
  });
  window.viewer.getLayerGroup("boundary").show = true;
  window.adcd = "";
  let Cesium = DC.getLib("Cesium");
  // window.viewer.camera.flyTo({
  //   destination: Cesium.Rectangle.fromDegrees(112.62, 38.56, 127.13,45.3)
  // })
  // 查询后台获取初始流域数据
  let params = {
    lycode: "",
  };
  // 获取流域code
  let res2 = await selectStlyList(params);
  let backData = formatTree(res2.data);
  let lycode = backData[0].basinId;
  // 获取流域边界
  const res = await selectStlyInfo(lycode);
  // console.log(typeof res.data.geom)
  let geom =
    typeof res.data.geom == "string"
      ? JSON.parse(res.data.geom)
      : res.data.geom;
  if (geom.features?.length === 0) return;
  let layer = new DC.GeoJsonLayer("layerXXxx", geom.features[0], {
    // stroke: DC.Color.YELLOW,
    stroke: DC.Color.fromCssColorString("rgba(83,179,231,0)"),
    fill: DC.Color.fromCssColorString("rgba(83,179,231,0)"),
    strokeWidth: 5,
  });
  window.viewer.addLayer(layer); // 为了获取边界视角定位，现在隐藏
  window.viewer.flyTo(layer); // 为了获取边界视角定位，现在隐藏
  setTimeout(() => {
    window.viewer.removeLayer(layer);
  }, 1000);

  delete window["watershed"];
};

/**
 * 切换行政区
 * @param {Object} data - 行政区数据
 */
const changeArea = (data) => {
  // for (let i = 0; i < window.viewer.entities.values.length; i++) {
  //   const element = window.viewer.entities.values[i];
  //   if (element.polyline) {
  //     element.polyline.show = false
  //   }

  // }
  window.viewer.entities.removeAll();
  queryByApi(data.adcd, data.adnm, 1);
  // queryByProperty(data.name || data.CNNM, 'CNNM', 'hc:省界')
  // queryByProperty(data.name || data.CNNM, 'CNNM', 'hc:市界')
  areaText.value = data.adnm;
};

/**
 * 切换流域
 * @param {Object} data - 流域数据
 */
const changeRiver = (data) => {
  queryWatershedByApi(data.basinId);
  areaText.value = data.name;
};

/**
 * 构建URL参数字符串
 * @param {Object} obj - 参数对象
 * @param {string} existingUrl - 现有URL
 * @param {boolean} uppercase - 是否大写
 * @returns {string} 参数字符串
 */
const getParamString = (obj, existingUrl, uppercase) => {
  const params = [];
  for (const i in obj) {
    params.push(
      encodeURIComponent(uppercase ? i.toUpperCase() : i) +
        "=" +
        encodeURIComponent(obj[i])
    );
  }
  return (
    (!existingUrl || existingUrl.indexOf("?") === -1 ? "?" : "&") +
    params.join("&")
  );
};

/**
 * 绘制城市边界
 * @param {Array} data - 城市数据数组
 */
const drawCity = async (data) => {
  for (let i = 0; i < data.length; i++) {
    const res = await getAdcBoundLayerByTD(data[i].name, 0);
    let backData = res.data;
    if (backData.data.district?.length === 0) return;
    let Cesium = DC.getLib("Cesium");
    let geom = parseMultiPolygon(backData.data.district[0].boundary);
    // console.log(data[i], geom)

    geom.forEach((arr) => {
      let positionsArr = [];
      arr.map((tdd) => {
        positionsArr = positionsArr.concat(tdd);
      });
      let lineEntity = new Cesium.Entity({
        polygon: {
          hierarchy: {
            positions: Cesium.Cartesian3.fromDegreesArray(positionsArr),
          },
          outlineWidth: 3,
          outline: true,
          outlineColor: DC.Color.fromCssColorString("rgb(32, 240, 255)"),
          material: DC.Color.fromCssColorString("rgba(32, 240, 255, 0.2)"),
        },
      });
      let line = new Cesium.Entity({
        polyline: {
          positions: Cesium.Cartesian3.fromDegreesArray(positionsArr),
          width: 2,
          material: DC.Color.fromCssColorString("rgba(32, 240, 255, 1)"),
        },
      });
      window.viewer.entities.add(lineEntity);
      window.viewer.entities.add(line);
    });
  }
};

/**
 * 通过API查询行政区
 * @param {string} adcd - 行政区编码
 * @param {string} adnm - 行政区名称
 * @param {number} level - 行政区级别
 */
const queryByApi = async (adcd, adnm, level) => {
  const res = await getAdcBoundLayerByTD(adnm, level);
  code.value = adcd;
  let backData = res.data;
  if (backData.data.district?.length === 0) return;

  let Cesium = DC.getLib("Cesium");
  let geom = parseMultiPolygon(backData.data.district[0].boundary);
  let positionsArr = [];

  // console.log(geom, '省')
  geom.forEach((arr) => {
    arr.map((tdd) => {
      positionsArr = positionsArr.concat(tdd);
    });
    let lineEntity = new Cesium.Entity({
      polygon: {
        hierarchy: {
          positions: Cesium.Cartesian3.fromDegreesArray(positionsArr),
        },
        outlineWidth: 3,
        outline: true,
        outlineColor: DC.Color.fromCssColorString("rgb(32, 240, 255,0.2)"),
        material: DC.Color.fromCssColorString("rgba(32, 240, 255, 0.2)"),
      },
    });
    let line = new Cesium.Entity({
      polyline: {
        positions: Cesium.Cartesian3.fromDegreesArray(positionsArr),
        width: 2,
        material: DC.Color.fromCssColorString("rgba(32, 240, 255, 1)"),
      },
    });

    window.viewer.entities.add(lineEntity);
    window.viewer.entities.add(line);

    if (backData.data.district[0].children?.length > 0) {
      drawCity(backData.data.district[0].children);
    }
  });
  let position = new DC.Position(
    backData.data.district[0].center.lng - 0.1,
    backData.data.district[0].center.lat + 0.1,
    110000.0,
    0,
    -90
  );
  window.viewer.flyToPosition(position, null, 3);
  // window.viewer.flyTo(lineEntity);

  // const smcPromise = Cesium.GeoJsonDataSource.load(res.data.features[0], { clampToGround: true });
  // smcPromise.then((ds) => {
  //   if (dataSource) {
  //     window.viewer.dataSources.remove(dataSource);
  //   }
  //   dataSource = ds
  //   window.viewer.getLayerGroup('boundary').show = false
  //   window.viewer.dataSources.add(dataSource);
  //   window.viewer.flyTo(dataSource);
  // const entities = dataSource.entities.values;
  // for (let i = 0; i < entities.length; i++) {
  //   const entity = entities[i];
  //   entity.polygon.outline = true
  //   entity.polygon.outlineColor = DC.Color.fromCssColorString("rgb(32, 240, 255)")
  //   entity.polygon.outlineWidth = 3
  //   entity.polygon.material = DC.Color.fromCssColorString("rgba(32, 240, 255, 0.2)")
  // }
  // let regions = ''
  // if (entities.length === 1) {
  //   // 解析经纬度串
  //   let now = DC.JulianDate.now()
  //   let positions = DC.Transform.transformCartesianArrayToWGS84Array(
  //     entities[0].polygon.hierarchy.getValue(now).positions
  //   )
  //   positions.forEach(pos => {
  //     regions += pos.lng + ',' + pos.lat + ','
  //   })
  //   regions = regions.substring(0, regions.length - 2)
  // }
  let regions = positionsArr.join(",");
  // 政区或者子流域切换后数据联动 定位 和 统计刷新
  window.EventBus.$emit("changeAreaSelect", {
    regions: regions,
    code: code.value, // 对应行政区编码，或者流域编码 ，传 空就是还原
    type: "adcd", // 行政区 adcd， 流域的话叫 watershed
    // })
  });
};

/**
 * 格式化天地图返回的行政区域代码
 * @param {String} multiPolygonStr 天地图返回的行政区域代码 MULTIPOLYGON 开头的字符串
 * @return {Array} 返回一个二维数组，每个元素是一个多边形的坐标数组
 */
const parseMultiPolygon = (multiPolygonStr) => {
  // 去除 MULTIPOLYGON，并将字符串转换为正确的多边形结构
  const polygonStr = multiPolygonStr
    .replace(/MULTIPOLYGON\s*\(/, "")
    .replace(/\)\s*$/, "")
    .trim();

  // 用 "),(" 分割字符串为不同的多边形区域
  const polygons = polygonStr
    .split(")),((")
    .map((p) => p.replace(/\(\(/g, "").replace(/\)\)/g, ""));
  // 存储转换后的坐标数组
  const coordinatesArray = [];

  polygons.forEach((polygon) => {
    // 根据逗号分割每个多边形的坐标对
    const points = polygon.split(",");
    const pointArrays = [];

    points.forEach((point) => {
      // 去除空格，并根据空格分割每个坐标对
      const [longitude, latitude] = point.trim().split(" ").map(Number);
      pointArrays.push([longitude, latitude]);
    });

    // 将当前多边形的坐标数组添加到总数组中
    coordinatesArray.push(pointArrays);
  });
  return coordinatesArray;
};

/**
 * 通过API查询流域
 * @param {string} lycode - 流域编码
 */
const queryWatershedByApi = async (lycode) => {
  // const res = await getLYBoundLayers(lycode)
  const res = await selectStlyInfo(lycode);
  code.value = lycode;
  // console.log(typeof res.data.geom)
  let geom =
    typeof res.data.geom == "string"
      ? JSON.parse(res.data.geom)
      : res.data.geom;
  if (geom.features?.length === 0) return;
  let Cesium = DC.getLib("Cesium");
  const smcPromise = Cesium.GeoJsonDataSource.load(geom.features[0], {
    clampToGround: true,
  });
  smcPromise.then((ds) => {
    if (dataSource) {
      window.viewer.dataSources.remove(dataSource);
    }
    dataSource = ds;
    window.viewer.getLayerGroup("boundary").show = false;
    window.viewer.dataSources.add(dataSource);
    window.viewer.flyTo(dataSource);
    const entities = dataSource.entities.values;
    for (let i = 0; i < entities.length; i++) {
      const entity = entities[i];
      entity.polygon.outline = true;
      entity.polygon.outlineColor =
        DC.Color.fromCssColorString("rgb(32, 240, 255)");
      entity.polygon.outlineWidth = 3;
      entity.polygon.material = DC.Color.fromCssColorString(
        "rgba(32, 240, 255, 0.2)"
      );
    }
    let regions = "";
    if (entities.length === 1) {
      // 解析经纬度串
      let now = DC.JulianDate.now();
      let positions = DC.Transform.transformCartesianArrayToWGS84Array(
        entities[0].polygon.hierarchy.getValue(now).positions
      );
      positions.forEach((pos) => {
        regions += pos.lng + "," + pos.lat + ",";
      });
      regions = regions.substring(0, regions.length - 2);
    }

    // 政区或者子流域切换后数据联动 定位 和 统计刷新
    window.EventBus.$emit("changeAreaSelect", {
      regions: regions,
      code: code.value, // 对应行政区编码，或者流域编码 ，传空就是还原
      type: "watershed", // 行政区 adcd， 流域的话叫 watershed
    });
  });
};

/**
 * 通过属性查询
 * @param {string} propertyValue - 属性值
 * @param {string} propertyName - 属性名
 * @param {string} typeName - 类型名
 * @param {Function} callback - 回调函数
 */
const queryByProperty = (propertyValue, propertyName, typeName, callback) => {
  let filter =
    '<Filter xmlns="http://www.opengis.net/ogc" xmlns:gml="http://www.opengis.net/gml">';
  filter += '<PropertyIsLike wildCard="*" singleChar="#" escapeChar="!">';
  filter += "<PropertyName>" + propertyName + "</PropertyName>";
  filter += "<Literal>*" + propertyValue + "*</Literal>";
  filter += "</PropertyIsLike>";
  filter += "</Filter>";
  const urlString = geoserverUrl + "/ows";
  const param = {
    service: "WFS",
    version: "1.0.0",
    request: "GetFeature",
    typeName: typeName,
    outputFormat: "application/json",
    filter: filter,
  };
  let Cesium = DC.getLib("Cesium");
  const geojsonUrl = urlString + getParamString(param, urlString);
  axios.get(geojsonUrl).then((res) => {
    if (res.data.features?.length === 0) return;
    window.viewer.getLayerGroup("boundary").show = false;
    code.value = res.data.features[0].properties.CNNMCD;
    const smcPromise = Cesium.GeoJsonDataSource.load(res.data, {
      clampToGround: true,
    });
    smcPromise.then((ds) => {
      if (dataSource) {
        window.viewer.dataSources.remove(dataSource);
      }
      dataSource = ds;
      window.viewer.dataSources.add(dataSource);
      window.viewer.flyTo(dataSource);
      const entities = dataSource.entities.values;
      for (let i = 0; i < entities.length; i++) {
        const entity = entities[i];
        entity.polygon.outline = true;
        entity.polygon.outlineColor =
          DC.Color.fromCssColorString("rgb(32, 240, 255)");
        entity.polygon.outlineWidth = 3;
        entity.polygon.material = DC.Color.fromCssColorString(
          "rgba(32, 240, 255, 0.2)"
        );
      }
      let regions = "";
      if (entities.length === 1) {
        // 解析经纬度串
        let now = DC.JulianDate.now();
        let positions = DC.Transform.transformCartesianArrayToWGS84Array(
          entities[0].polygon.hierarchy.getValue(now).positions
        );
        positions.forEach((pos) => {
          regions += pos.lng + "," + pos.lat + ",";
        });
        regions = regions.substring(0, regions.length - 2);
      }

      // 政区或者子流域切换后数据联动 定位 和 统计刷新
      window.EventBus.$emit("changeAreaSelect", {
        regions: regions,
        code: code.value, // 对应行政区编码，或者流域编码 ，传 空就是还原
        type: activeName.value === "district" ? "adcd" : "watershed", // 行政区 adcd， 流域的话叫 watershed
      });
    });
  });
};

/**
 * 行政区树节点点击事件
 * @param {Object} e - 节点数据
 */
const handleDistrictNodeClick = (e) => {
  // console.log(e);
  areaText.value = e.adnm;
  changeArea(e);
};

/**
 * 流域树节点点击事件
 * @param {Object} e - 节点数据
 */
const handleWatershedNodeClick = (e) => {
  console.log(e);
  areaText.value = e.lynm;
  changeRiver(e);
};

// 计算左侧位置
const screenStore = useScreenStore();
const leftPosition = computed(() => {
  return screenStore.isPanelCollapsed ? "20px" : "400px";
});

// 组件挂载时加载数据
onMounted(() => {
  // 加载行政区树
  getTreeOption(3).then((res) => {
    districts.value = formatTree(res.data);
  });
  let params = {
    lycode: "",
  };
  // 加载流域树
  selectStlyList(params).then((res) => {
    watersheds.value = formatTree(res.data);
  });
});
</script>

<style scoped>
.area-select {
  position: absolute;
  left: v-bind(leftPosition);
  top: 110px;
  width: 250px;
  height: 35px;
  padding: 2px;
  z-index: 18;
  transition: left 0.3s ease;
}

:deep(.el-input__wrapper) {
  background: rgba(0, 57, 115, 1);
  border: 1px solid #1c8bda;
  border-radius: 2px;
  box-shadow: 0 0 0 1px #4bb1ff, #4bb1ff inset;
  color: #fff;
}

:deep(.el-input__inner) {
  color: #4eb6f1;
}

:deep(.el-input__prefix) {
  color: #a3deff;
}

:deep(.el-input__inner::placeholder) {
  color: #a3deff;
}

.selectPanel {
  min-width: 250px;
  padding: 0 5px;
  background-color: #013e7e;
  overflow: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
.selectPanel::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.selectPanel.el-popper.is-light {
  background: #243662;
  border: 1px solid #4bb1ff;
}

.area-select-item {
  float: left;
  padding: 3px 5px;
  font-weight: bold;
}

.area-select-item:hover {
  cursor: pointer;
  background: #025ba1;
  color: #fff;
}

.area-select-item-shi {
  float: left;
  padding: 3px 5px;
}

.area-select-item-shi:hover {
  cursor: pointer;
  background: rgba(2, 91, 161, 0.65);
  color: #fff;
}

:deep(.el-input__wrapper) {
  box-shadow: none !important;
}

:deep(.el-popper.is-light) {
  background: rgb(0 66 133 / 90%) !important;
  border: 1px solid #4bb1ff !important;
}
</style>
<style lang="scss" scoped>
.selectPanel {
  min-height: 200px;
  max-height: 500px;
  min-width: 250px;
  padding: 5px;
  background-color: #013e7e;
  border-radius: 3px;
}

.selectPanel .group {
  display: flex;
  justify-content: center;
  width: 90%;
  margin: 0 auto;
  border-bottom: 1px solid #2e84cd;
}

:deep(.el-radio__label) {
  color: #fff;
}

.panel-tree {
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  color: #7ca6d2;
  line-height: 20px;
  padding: 5px;
  background-color: #013e7e !important;
  -webkit-user-select: none;
  /* Chrome/Safari/Opera */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* IE/Edge */
  user-select: none;
  /* Standard */
}

:deep(.el-tree) {
  background: none;
}

:deep(.el-divider--horizontal) {
  border-top: 1px solid #4494d4;
}

:deep(.panel-tree .el-tree-node__label) {
  width: 100%;
}

:deep(.el-tree-node__content:hover) {
  background-color: #004892;
  color: #fff;
}

:deep(.el-tree-node:focus > .el-tree-node__content) {
  background-color: #004892;
  color: #fff;
}

.control-area {
  width: 100%;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid rgba(46, 132, 205, 0.15);

  .back-btn {
    background: transparent;
    border: 1px solid rgba(75, 177, 255, 0.3);
    color: #a3deff;
    padding: 4px 12px;
    border-radius: 3px;
    transition: all 0.2s ease;
    font-size: 12px;
    height: 28px;
    line-height: 1;
    min-width: 70px;
    flex-shrink: 0;

    &:hover {
      background: rgba(75, 177, 255, 0.1);
      border-color: rgba(75, 177, 255, 0.5);
      color: #fff;
    }

    &:active {
      background: rgba(75, 177, 255, 0.15);
    }
  }

  .area-type-group {
    display: inline-flex;
    background: rgba(0, 57, 115, 0.3);
    border-radius: 3px;
    padding: 2px;
    height: 28px;
    border: 1px solid rgba(75, 177, 255, 0.15);
    flex: 1;
    justify-content: space-between;
  }

  :deep(.el-radio) {
    margin: 0;
    height: 100%;
    padding: 0;
    border-radius: 2px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #a3deff;
    transition: all 0.2s ease;
    font-size: 12px;
    flex: 1;
    min-width: 0;

    &.is-checked {
      .el-radio__label {
        color: #a3deff !important;
      }
    }

    .el-radio__input {
      display: none;
    }

    .el-radio__label {
      padding: 0;
      line-height: 1;
      width: 100%;
      text-align: center;
    }

    &:hover {
      background: rgba(75, 177, 255, 0.1);
    }

    &.is-checked {
      background: rgba(75, 177, 255, 0.25);
    }

    & + .el-radio {
      margin-left: 1px;
    }
  }
}
.dark-select-dropdown {
  .el-scrollbar {
    background: #013e7e;
  }
}
// 移除不需要的样式
.back-init-area,
.area-type-selector {
  display: none;
}
</style>
