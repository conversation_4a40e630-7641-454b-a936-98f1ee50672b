<!--基础工具， 测量，标记-->
<template>
  <div class="base-tool map-panel">
    <div class="map-panel-content ">
      <div class="panel-header" @click="changeFlag">
        <img :src="toolImg" style="width: 19px; padding: 2px;float: left" />
        <div style="padding: 0 7px;float: left">基础工具</div>
        <el-icon v-show="!flag" class="control-icon">
          <ArrowRight />
        </el-icon>
        <el-icon v-show="flag" class="control-icon">
          <ArrowDown />
        </el-icon>
      </div>
      <el-divider v-if="flag" style="margin: 5px 0" />
      <div v-if="flag" style="height: 260px;width: 100%;display: inline-block">
        <div class="map-switch-item" :class="curType === 'gotoMapInitExtent' ? 'active' : ''"
          @click="gotoMapInitExtent">
          回到原位</div>
        <div class="map-switch-item" :class="curType === 'distanceSurface' ? 'active' : ''" @click="distanceSurface">测距离
        </div>
        <div class="map-switch-item" :class="curType === 'areaSurface' ? 'active' : ''" @click="areaSurface">测面积</div>
        <div class="map-switch-item" :class="curType === 'calcAngle' ? 'active' : ''" @click="calcAngle">测角度</div>
        <div class="map-switch-item" :class="curType === 'clear' ? 'active' : ''" @click="clear">清除测量</div>
        <el-divider style="margin: 2px 0" />
        <div class="map-switch-item" :class="curType === 'point' ? 'active' : ''" @click="drawPlot('point')">标记点</div>
        <div class="map-switch-item" :class="curType === 'polyline' ? 'active' : ''" @click="drawPlot('polyline')">标记线
        </div>
        <div class="map-switch-item" :class="curType === 'polygon' ? 'active' : ''" @click="drawPlot('polygon')">标记面
        </div>
        <div class="map-switch-item" :class="curType === 'tailed_attack_arrow' ? 'active' : ''"
          @click="drawPlot('tailed_attack_arrow')">标记箭头</div>
        <div class="map-switch-item" :class="curType === 'removePlot' ? 'active' : ''" @click="removePlot">清除标记</div>
        <!--        <div class="map-switch-item" :class="curType === 'showSVG' ? 'active':''"  @click="showSVG">概化图(测试)</div>-->
        <!--        <div class="map-switch-item" :class="curType === 'show2Map' ? 'active':''"  @click="show2Map">对比分析(测试)</div>-->
        <!--        <div class="map-switch-item" :class="curType === 'dispatch2select' ? 'active':''"  @click="dispatch2select">调度方案查看(测试)</div>-->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import toolImg from '../../images/tool.png'
import { selectStlyInfo, selectStlyList } from "@/api/watershed/ads";

let measure = null
let plot = null
let layer = null
const flag = ref(false)
const curType = ref('')

const gotoMapInitExtent = async () => {
  window.viewer.entities.removeAll();
  curType.value = 'gotoMapInitExtent'
  // window.viewer.camera.flyHome(1)
  let Cesium = DC.getLib('Cesium')
  // window.viewer.camera.flyTo({
  //   destination: Cesium.Rectangle.fromDegrees(112.62, 38.56, 127.13,45.3)
  // })
  // window.viewer.camera.setView({
  //   destination: Cesium.Cartesian3.fromDegrees(118.64482064959539,36.1759707289059,575497.5787557325),
  //   orientation: {
  //     heading:  6.283185307179586, // 方向
  //     pitch: -0.8655324845699397, // 视角
  //     roll: 6.283185307179585 // 倾斜角度
  //   }
  // })
  // 查询后台
  let params = {
    basinId: ''
  }
  // 获取流域code
  let res2 = await selectStlyList(params)
  let backData = formatTree(res2.data)
  let lycode = backData[0].basinId
  // 获取流域边界
  const res = await selectStlyInfo(lycode)
  console.log(typeof res.data.geom)
  let geom = typeof res.data.geom == 'string' ? JSON.parse(res.data.geom) : res.data.geom
  if (geom.features?.length === 0) return;
  let layer = new DC.GeoJsonLayer(
    'layerXX',
    geom.features[0],
    {
      // stroke: DC.Color.YELLOW,
      stroke: DC.Color.fromCssColorString("rgba(83,179,231,0)"),
      fill: DC.Color.fromCssColorString("rgba(83,179,231,0)"),
      strokeWidth: 5
    }
  )
  window.viewer.addLayer(layer) // 为了获取边界视角定位，现在隐藏
  window.viewer.flyTo(layer) // 为了获取边界视角定位，现在隐藏
  setTimeout(() => {
    window.viewer.removeLayer(layer)
  }, 1000)
}
// 贴地测距
const distanceSurface = () => {
  curType.value = 'distanceSurface'
  measure.distanceSurface()
}
// 贴地测距
const areaSurface = () => {
  curType.value = 'areaSurface'
  measure.areaSurface()
}
// 贴地测距
const calcAngle = () => {
  curType.value = 'calcAngle'
  measure.angle()
}
const formatTree = (data) => {
  let dataList = data.map(item => {
    return {
      ...item.data,
      children: item.children && formatTree(item.children)
    }
  })

  return dataList
}
const changeFlag = () => {
  flag.value = !flag.value
  if (flag.value) {
    if (!measure) {
      measure = new DC.Measure(window.viewer)
    }
    if (!plot) {
      plot = new DC.Plot(window.viewer)
      layer = new DC.VectorLayer('plot-layer')
      window.viewer.addLayer(layer)
    }
  } else {
    clear()
    removePlot()
  }

}
const drawPlot = (type) => {
  curType.value = type
  plot && plot.draw(type, overlay => {
    if (overlay) {
      console.log(overlay)
      layer.addOverlay(overlay)
      // plot.edit(overlay)
    }
  })
}
const removePlot = () => {
  curType.value = 'removePlot'
  layer.clear()
}
const showSVG = () => {
  curType.value = 'showSVG'
  window.EventBus.$emit('module/type/change', { name: '概化图' })
}
const show2Map = () => {
  curType.value = 'show2Map'
  window.EventBus.$emit('module/type/change', { name: '对比分析' })
}
const dispatch2select = () => {
  curType.value = 'dispatch2select'
  window.EventBus.$emit('floodPreRehearsal/dispatch/select', { ID: 1 })
}
// 清除
const clear = () => {
  curType.value = 'clear'
  measure.deactivate()
}
</script>

<style scoped>
.base-tool {
  width: 180px;
  margin: 5px;
  flex: 1;
}

.panel-header {
  width: 100%;
  height: 30px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #A3DEFF;
  line-height: 20px;
  padding: 5px 2px 5px 5px;
}

.tool-image {
  width: 40px;
  height: 40px;
  padding: 5px;
  cursor: pointer;
}

.control-icon {
  float: right;
  padding-right: 5px;
  font-size: 20px;
  cursor: pointer;
}

.map-switch-item {
  height: 30px;
  padding: 5px 10px;
  font-weight: 400;
  color: #7CA6D2;
  line-height: 20px;
}

.map-switch-item:hover {
  background: rgba(0, 68, 138, 0.7);
  border-radius: 2px;
  color: #fff;
  cursor: pointer;
}

.map-switch-item-mode {
  height: 30px;
  padding: 5px 10px;
}

:deep(.el-divider--horizontal) {
  border-top: 1px solid #4494D4;
}

.active {
  background-color: #004892;
  color: #fff;
}
</style>
