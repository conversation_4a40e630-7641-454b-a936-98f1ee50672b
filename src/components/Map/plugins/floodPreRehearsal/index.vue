<template>
  <div>
    <div
      class="floodPreRehearsal-panel"
      v-show="currentModuleType != '对比分析'"
    >
      <div>
        <div
          class="map-switch-item"
          :class="curType === 1 ? 'active' : ''"
          @click="changeType(1)"
        >
          <img
            class="imgY"
            :src="curType === 1 ? img_flood1_img : img_flood11_img"
          />
          <div class="switchTitle">未来推演</div>
        </div>
        <div
          class="map-switch-item"
          :class="curType === 2 ? 'active' : ''"
          @click="changeType(2)"
        >
          <img
            class="imgY"
            :src="curType === 2 ? img_flood2_img : img_flood21_img"
          />
          <div class="switchTitle">历史复盘</div>
        </div>
        <div
          class="map-switch-item"
          :class="curType === 3 ? 'active' : ''"
          @click="changeType(3)"
        >
          <img
            class="imgY"
            :src="curType === 3 ? img_flood3_img : img_flood31_img"
          />
          <div class="switchTitle">模拟演练</div>
        </div>
        <div style="color: rgb(163 222 255); padding: 5px 10px">
          {{ curGridValue }}
        </div>
      </div>
    </div>
    <div
      :class="
        currentModuleType === '对比分析'
          ? 'floodPreRehearsal2'
          : 'floodPreRehearsal'
      "
      v-show="times.length > 0"
      v-loading="loading"
    >
      <div class="slider-demo-block" id="myeslider">
        <span class="demonstration" @click="floodPlay"
          ><img
            style="width: 40px; height: 40px; cursor: pointer"
            :src="isplay ? play_pause_img : play_start_img"
        /></span>
        <el-slider
          ref="eslider"
          v-model="currentValue"
          @change="changeSlider"
          :step="1"
          :max="max"
          show-stops
          :marks="marks"
          tooltip-class="cusTip"
          :format-tooltip="formatTooltip"
        />
      </div>
    </div>
    <div v-show="currentModuleType === '对比分析'">
      <!--    <div v-show="dispId2" >-->
      <anaylysis-panel></anaylysis-panel>
    </div>
    <div
      style="
        position: absolute;
        top: 20px;
        left: 40%;
        color: #25cafb;
        font-size: 22px;
      "
      v-show="dispId && currentModuleType != '对比分析'"
    >
      {{ dispName }}
    </div>
    <div
      style="
        position: absolute;
        top: 20px;
        left: 20%;
        color: #25cafb;
        font-size: 22px;
      "
      v-show="currentModuleType === '对比分析'"
    >
      {{ dispName }}
    </div>
    <div
      style="
        position: absolute;
        top: 80px;
        left: 20px;
        color: #25cafb;
        font-size: 22px;
      "
      v-show="currentModuleType === '对比分析'"
    >
      <div style="color: rgb(163 222 255); padding: 5px 10px">
        {{ curGridValue }}
      </div>
    </div>
    <div
      style="
        position: absolute;
        top: 20px;
        right: 20%;
        color: #eecb72;
        font-size: 22px;
      "
      v-show="currentModuleType === '对比分析'"
    >
      {{ dispName2 }}
    </div>
    <div
      style="
        position: absolute;
        top: 80px;
        right: 20px;
        color: #eecb72;
        font-size: 22px;
      "
      v-show="currentModuleType === '对比分析'"
    >
      <div style="color: rgb(163 222 255); padding: 5px 10px">
        {{ curGridValue2 }}
      </div>
    </div>
  </div>
</template>

<script setup>
import axios from "axios";
import play_start from "../../images/play_start.png";
import play_pause from "../../images/play_pause.png";
import img_flood1 from "../../images/flood/1.png";
import img_flood11 from "../../images/flood/11.png";
import img_flood2 from "../../images/flood/2.png";
import img_flood21 from "../../images/flood/21.png";
import img_flood3 from "../../images/flood/3.png";
import img_flood31 from "../../images/flood/31.png";
import anaylysisPanel from "../../components/anaylysisPanel/index.vue";
import {
  getAllStations,
  getDisStatResList,
  getXzhqByYbid,
  getYmGridWaterDepthGeojson,
} from "@/api/watershed/ads";
import moment from "moment";
import { getFloodTypeByValue } from "@/components/Map/utils/water-common";
import { getWorkDetail } from "@/api/scheduling";

let gridTimes;
let gridTimes2;

// 定义组件名称
defineOptions({
  name: "floodPreRehearsal",
});
// 响应式数据
const imageListAcc = ref([]);
const _layers = ref([]);
const loading = ref(false);
const isplay = ref(false);
const curGridValue = ref("");
const curGridValue2 = ref("");
const curType = ref(1); //
const regions = ref(
  "116.640419,42.730239,116.634651,42.674446,116.658655,42.670507,116.672272,42.647198,116.686169,42.657083,116.723588,42.607354,116.739692,42.617608,116.764134,42.600332,116.775286,42.555756,116.802152,42.555318,116.829436,42.577961,116.830232,42.602587,116.894856,42.578583,116.93482,42.607382,116.992686,42.558151,117.042829,42.543365,117.097509,42.574595,117.178934,42.511088,117.220899,42.498547,117.351849,42.534881,117.374214,42.532061,117.379158,42.515344,117.426698,42.514562,117.462106,42.460661,117.435485,42.453416,117.443918,42.425296,117.427414,42.403712,117.397706,42.400034,117.377534,42.378037,117.3957,42.346441,117.439116,42.324027,117.466636,42.340782,117.515921,42.342519,117.534008,42.274689,117.588333,42.272098,117.603081,42.257152,117.633473,42.261526,117.727085,42.230091,117.770148,42.237233,117.765765,42.094931,117.744178,42.048811,117.773259,42.045039,117.798651,42.007789,117.792392,41.964421,117.93809,42.0063,117.957751,41.96064,118.015411,41.943917,118.056943,41.961185,118.10206,41.931062,118.089672,41.812684,118.135214,41.75061,118.128216,41.716072,118.212162,41.640329,118.228663,41.576822,118.312596,41.558949,118.300416,41.543708,118.316427,41.50825,118.26442,41.464153,118.335239,41.443315,118.359508,41.386027,118.341871,41.369714,118.347421,41.329928,118.394519,41.308437,118.433779,41.266995,118.406865,41.242632,118.443306,41.217067,118.444205,41.193614,118.512195,41.179628,118.532737,41.161936,118.567316,41.171738,118.693512,41.14586,118.762523,41.178307,118.763481,41.147313,118.827576,41.117755,118.814152,41.095983,118.837138,41.08267,118.88943,41.092948,118.929666,41.051,118.955771,41.07457,118.955027,41.103807,119.046996,41.143439,119.115856,41.104154,119.177795,41.016808,119.215036,41.020081,119.233961,40.995951,119.277024,41.00009,119.255816,40.958738,119.271641,40.911442,119.242876,40.906011,119.251757,40.857014,119.211359,40.793879,119.261148,40.778056,119.264024,40.759515,119.313135,40.751869,119.359876,40.709731,119.367737,40.681592,119.401051,40.683997,119.446639,40.647312,119.406739,40.569674,119.421302,40.539987,119.474884,40.531866,119.491543,40.554168,119.543423,40.554117,119.562952,40.532524,119.540889,40.508973,119.593522,40.462783,119.587676,40.391714,119.57144,40.379164,119.596336,40.362118,119.580354,40.342692,119.709671,40.281542,119.673792,40.266526,119.664381,40.241308,119.682955,40.207667,119.709639,40.194477,119.735092,40.206297,119.745428,40.188563,119.754741,40.138844,119.718032,40.087559,119.764596,40.080963,119.742153,40.053432,119.81557,40.028941,119.840819,39.987515,119.802766,39.977641,119.784339,39.94868,119.69161,39.93658,119.677194,39.913756,119.664269,39.937105,119.610511,39.904941,119.555352,39.902607,119.526488,39.872184,119.529567,39.808082,119.464304,39.812082,119.363149,39.744218,119.257476,39.514234,119.258067,39.488679,119.302841,39.465693,119.293731,39.3871,119.253123,39.361699,119.192886,39.356721,119.0937,39.252544,119.01888,39.202888,119.014603,39.196264,119.005541,39.195544,118.966167,39.15786,118.849216,39.095085,118.792316,39.08999,118.786441,39.083624,118.778542,39.079641,118.751305,39.068502,118.73238,39.071631,118.714843,39.07298,118.666079,39.074776,118.646833,39.120758,118.630499,39.112428,118.601573,39.123509,118.605138,39.104096,118.551185,39.175818,118.533283,39.289405,118.560445,39.373361,118.540073,39.41287,118.574026,39.456699,118.59625,39.68017,118.565661,39.755597,118.611815,39.80007,118.591114,39.845385,118.609752,39.855484,118.629898,39.945261,118.602177,39.969286,118.611141,39.989649,118.593239,39.991383,118.592415,40.03248,118.54164,40.107108,118.516309,40.106501,118.476337,40.037562,118.437967,40.02701,118.453542,40.101579,118.431147,40.128072,118.386687,40.143622,118.353614,40.125327,118.260072,40.118278,118.183259,40.147606,118.159243,40.173027,118.195311,40.18797,118.19774,40.217551,118.131942,40.304154,118.093183,40.283811,118.07331,40.307122,118.048285,40.301446,117.997866,40.326162,117.941251,40.321627,117.935439,40.353362,117.885202,40.320438,117.842159,40.33492,117.781104,40.303514,117.745811,40.322358,117.752731,40.356835,117.688999,40.355206,117.673248,40.384116,117.612186,40.376444,117.58434,40.333493,117.592635,40.313638,117.571026,40.307166,117.528252,40.35305,117.470945,40.3572,117.424142,40.379219,117.447454,40.414073,117.400026,40.457878,117.436396,40.483562,117.423731,40.501358,117.454941,40.558986,117.448015,40.577681,117.483153,40.594982,117.498987,40.587508,117.527511,40.622239,117.568867,40.630035,117.573538,40.655038,117.508103,40.652688,117.497926,40.66562,117.5213,40.703688,117.49677,40.759834,117.434073,40.762957,117.384916,40.821494,117.356772,40.821288,117.315137,40.865173,117.300654,40.862556,117.28434,40.956051,117.199936,41.011357,117.21409,41.046195,117.190728,41.118927,117.168103,41.141428,117.124392,41.14292,117.089156,41.218173,116.990824,41.220608,116.878654,41.285525,116.87267,41.310443,116.845163,41.313848,116.806716,41.358285,116.732279,41.387512,116.771136,41.451231,116.716273,41.52257,116.715711,41.552031,116.663541,41.583653,116.664252,41.60828,116.638586,41.609097,116.628527,41.624962,116.544131,41.610553,116.528766,41.578828,116.460747,41.534216,116.409455,41.567773,116.381554,41.554872,116.332995,41.564923,116.274176,41.518922,116.225344,41.517063,116.21551,41.46963,116.158629,41.448825,116.14119,41.359955,116.040025,41.387297,116.02772,41.420106,116.003456,41.429315,116.004313,41.454796,115.975912,41.46914,115.966927,41.518185,115.893639,41.533745,115.837752,41.513471,115.816367,41.536601,115.816979,41.568795,115.782251,41.566632,115.749468,41.588113,115.730103,41.780346,115.772588,41.838878,115.760371,41.879261,115.687096,41.913108,115.619106,41.921988,115.597592,41.954813,115.562396,41.967054,115.535279,42.020766,115.549516,42.038877,115.593169,42.046137,115.61923,42.023532,115.696842,42.032823,115.719734,42.073952,115.778716,42.054339,115.774595,42.080178,115.818064,42.098414,115.791581,42.140269,115.815699,42.160508,115.798199,42.221993,115.859453,42.233872,115.887828,42.290866,115.942744,42.306015,115.937852,42.32645,115.971323,42.338458,115.980622,42.363748,116.031343,42.380842,116.016902,42.391972,116.056266,42.41931,116.043033,42.430606,116.053749,42.4709,116.119375,42.488294,116.138418,42.523968,116.166916,42.516377,116.180071,42.545578,116.243081,42.544476,116.26197,42.56868,116.309058,42.578201,116.290301,42.621071,116.341616,42.638482,116.327484,42.648752,116.369303,42.673745,116.427646,42.686577,116.495468,42.673955,116.485221,42.693076,116.537464,42.700051,116.543598,42.723555,116.584706,42.715272,116.640419,42.730239"
);
const currentValue = ref(0);
const adcd = ref("");
const st = ref(null);
const marks = ref({
  0: "04 09",
  1: "04 10",
  2: "04 11",
  3: "04 12",
  4: "04 13",
  5: "04 14",
  6: "04 15",
  7: "04 16",
  8: "04 17",
  9: "04 18",
});
const currentType = ref("");
const currentModuleType = ref("防洪预演");
const allStations = ref(null);
const dispId = ref(null); // 当前的方案的id
const dispName = ref(""); // 当前的方案的id
const dispId2 = ref(null); // 当前的第二个方案的id
const dispName2 = ref(""); // 当前的第二个方案的id
const times = ref([]);
const serisTime = ref({});
const serisTime2 = ref({});
const max = ref(10);
const detailData = ref([]);
const handlers = ref([]);

// 图片资源
const play_start_img = play_start;
const play_pause_img = play_pause;
const img_flood1_img = img_flood1;
const img_flood11_img = img_flood11;
const img_flood2_img = img_flood2;
const img_flood21_img = img_flood21;
const img_flood3_img = img_flood3;
const img_flood31_img = img_flood31;
// 生命周期钩子
onMounted(() => {
  imageListAcc.value = [];
  _layers.value = [];
  handlers.value = [];

  // UI改变之后，把左侧隐藏了就，把事件暴露出来就
  window.EventBus.$on("floodPreRehearsal/floodPlay/change", changeType);
  window.EventBus.$on("changeAreaSelect", (data) => {
    if (data.type === "adcd") {
      adcd.value = data.code || "";
      regions.value = data.regions || "";
    } else if (data.type === "watershed") {
    }
  });
  window.viewer.on(DC.MouseEventType.MOUSE_MOVE, (e) => {
    // console.log(e)
    if (e.target?.id) {
      if (e.target?.id?.properties?.GRIDCODE) {
        curGridValue.value =
          "当前网格淹没水深" + e.target?.id?.properties?.VALUE + "m";
      }
    } else {
      curGridValue.value = "";
    }
  });

  window.EventBus.$on("module/type/change", changeModuleType);
  window.EventBus.$on("bubble_vanish", bubbleVanish);
  // 调度方案的选择，联动地图上的东西刷新，站点的初始时刻，和时间轴
  window.EventBus.$on("floodPreRehearsal/dispatch/select", dispatchSelect);
  window.EventBus.$on("floodPreRehearsal/dispatch/select2", dispatchSelect2);

  // 修改下时间条
  let dv =
    '<div class="markTT"><div><img class="markImg" src="./icons/marker/map-marker.png"/><div class="markImgTitle" >潘家口入库水库洪峰</div></div><div class="markTitle" >04 10</div></div>';
  let box = document.getElementById("myeslider");
  let marksNode = box.getElementsByClassName("el-slider__marks-text");
  marksNode[1].innerHTML = dv;
  marksNode[5].innerHTML = dv;
  // getXzhqByYbid()
  // getDisStatResList(584)
  // getDisStatResList(1)
});

onUnmounted(() => {
  // window.viewer.getLayerGroup('rain').show = true
  // window.viewer.getLayerGroup('river').show = true
  // window.viewer.getLayerGroup('reservoir').show = true
  // clearAll()
});
// 方法定义
const clearAll = () => {
  dispId.value = null;
  dispId2.value = null;
  times.value = [];
  window.viewer.getLayerGroup("rain").show = false;
  window.EventBus.$emit("river3/update", []);
  window.EventBus.$emit("reservoir3/update", []);
  window.EventBus.$emit("river31/update", []);
  window.EventBus.$emit("reservoir31/update", []);
  let hasLayer = window.viewer.getLayerGroup("flood-grids-group");
  if (hasLayer) {
    hasLayer.show = false;
  }
  let layer = window.viewer.getLayer("flood-grids-layer");
  if (layer) {
    layer.clear();
  }
  cloudLayers.value.forEach((item) => {
    item.alpha = 0.0;
  });
  cloudLayers.value = [];
  // if (layers) {
  //   layers.removeAll()
  // }
  curGridValue.value = "";
  curGridValue2.value = "";
};

const getXzhqByYbid = () => {
  let params = {
    lycode: "GC000000",
  };
  getXzhqByYbid(params).then((res) => {
    console.log(res);
  });
};
// 调度方案，关联的 河道，水库站点
const getDisStatResListLocal = async (dispIdParam) => {
  let params = {
    dispId: 1,
  };
  let res = await getDisStatResList(params);
  let serisTimeLocal = {}; // 根据发生时间进行的
  let timesLocal = [];
  let allSt = []; // 所有的站存起来
  for (let key in res.data) {
    let ll = res.data[key];
    // 这是同一个站的根据小时的数据 ,只记录一次
    if (timesLocal.length === 0) {
      ll.forEach((item) => {
        timesLocal.push(item.YMDH);
      });
    }
    ll.forEach((item) => {
      allSt.push(item);
    });
  }
  timesLocal.forEach((YMDH) => {
    let curTimeSts = [];
    allSt.forEach((st) => {
      if (st.YMDH === YMDH) {
        curTimeSts.push(st);
      }
    });
    serisTimeLocal[YMDH] = curTimeSts;
  });
  times.value = timesLocal;
  console.log(serisTimeLocal);
  serisTime.value = serisTimeLocal;
  // 更新时间条
  let index = 0;
  marks.value = {};
  max.value = timesLocal.length;
  timesLocal.forEach((tm) => {
    marks.value[index] = tm.substring(5, 13).replace("T", " ");
    index++;
  });
  curentStations(times.value[0]);
};
// 对比分析的数据 ，时间轴公用》》
const getDisStatResList2Local = async (dispIdParam) => {
  let params = {
    dispId: dispIdParam,
  };
  let res = await getDisStatResList(params);
  let serisTimeLocal = {}; // 根据发生时间进行的
  let timesLocal = [];
  let allSt = []; // 所有的站存起来
  for (let key in res.data) {
    let ll = res.data[key];
    // 这是同一个站的根据小时的数据 ,只记录一次
    if (timesLocal.length === 0) {
      ll.forEach((item) => {
        timesLocal.push(item.YMDH);
      });
    }
    ll.forEach((item) => {
      allSt.push(item);
    });
  }
  timesLocal.forEach((YMDH) => {
    let curTimeSts = [];
    allSt.forEach((st) => {
      if (st.YMDH === YMDH) {
        curTimeSts.push(st);
      }
    });
    serisTimeLocal[YMDH] = curTimeSts;
  });
  console.log(serisTimeLocal);
  serisTime2.value = serisTimeLocal;
  // 更新时间条
  // let index = 0
  // marks.value = {}
  // max.value = timesLocal.length
  // timesLocal.forEach(tm=>{
  //   marks.value[index] = tm.substring(5,13).replace('T',' ')
  //   index++
  // })
  curentStations2(timesLocal[0]);
};
// 根据当前时间刷新地图数据
const curentStations = (time) => {
  let list = serisTime.value[time] || [];
  let riverList = [];
  let reservoirList = [];
  list.forEach((item) => {
    Object.assign(item, allStations.value[item.STCD]);
    if (item.TYPE === "river") {
      riverList.push(item);
    } else if (item.TYPE === "rsvr") {
      reservoirList.push(item);
    }
  });
  console.log(reservoirList, "reservoirList");
  // 把正常的水库，河道隐藏了
  window.viewer.getLayerGroup("rain").show = false;
  window.viewer.getLayerGroup("river").show = false;
  window.viewer.getLayerGroup("reservoir").show = false;
  window.EventBus.$emit("river3/update", riverList);
  window.EventBus.$emit("reservoir3/update", reservoirList);

  updateGridLayer(time);
};

// 根据当前时间刷新地图数据
const curentStations2 = (time) => {
  let list = serisTime2.value[time] || [];
  let riverList = [];
  let reservoirList = [];
  list.forEach((item) => {
    Object.assign(item, allStations.value[item.STCD]);
    if (item.TYPE === "river") {
      riverList.push(item);
    } else if (item.TYPE === "rsvr") {
      reservoirList.push(item);
    }
  });
  window.EventBus.$emit("river31/update", riverList);
  window.EventBus.$emit("reservoir31/update", reservoirList);

  updateGridLayer2(time);
};
const changeModuleType = (data) => {
  currentModuleType.value = data.name;
  if (currentModuleType.value === "防洪预演") {
    // 从别的状态过来
    bubbleVanish();
  } else if (currentModuleType.value === "对比分析") {
    window.rightViewer?.on(DC.MouseEventType.MOUSE_MOVE, (e) => {
      // console.log(e)
      if (e.target?.id) {
        if (e.target?.id?.properties?.GRIDCODE) {
          curGridValue2.value =
            "当前网格淹没水深" + e.target?.id?.properties?.VALUE + "m";
        }
      } else {
        curGridValue2.value = "";
      }
    });
  }
};

// 返回重置
const bubbleVanish = () => {
  times.value = [];
  clearAll();
};

const dispatchSelect = async (data) => {
  dispId.value = data.id;
  dispName.value = data.name;
  // 没请求过就来一下，获取所有的测站的信息
  // if (!allStations.value) {
  //   let allStations = await getAllStations()
  //   allStations.value = allStations.data
  // }
  //获取当前方案详情
  getWorkDetail(data.id).then((res) => {
    detailData.value = res.data;
  });
  currentValue.value = 0;
  // 获取淹没网格数据
  await getDisStatResListLocal(dispId.value);
  await getRainGridData(dispId.value);
};
const dispatchSelect2 = async (data) => {
  dispId2.value = data.ID;
  dispName2.value = data.DISPC_NM;
  // 没请求过就来一下，获取所有的测站的信息
  if (!allStations.value) {
    let allStationsRes = await getAllStations();
    allStations.value = allStationsRes.data;
  }
  currentValue.value = 0;
  // 获取淹没网格数据
  await getDisStatResList2Local(dispId2.value);
  await getRainGridData2(dispId2.value);
};

const changeSlider = (val) => {
  isplay.value = false;
  if (st.value) {
    clearInterval(st.value);
    st.value = null;
  }
  curentStations(times.value[currentValue.value]);
  if (dispId2.value) {
    curentStations2(times.value[currentValue.value]);
  }
};

const getParamString = (obj, existingUrl, uppercase) => {
  const params = [];
  for (const i in obj) {
    params.push(
      encodeURIComponent(uppercase ? i.toUpperCase() : i) +
        "=" +
        encodeURIComponent(obj[i])
    );
  }
  return (
    (!existingUrl || existingUrl.indexOf("?") === -1 ? "?" : "&") +
    params.join("&")
  );
};
// 获取网格底面数据
const getFloodGridData = async (typeName, callback) => {
  const geoserverUrl = "/geoserver/hc";
  let filter =
    '<Filter xmlns="http://www.opengis.net/ogc" xmlns:gml="http://www.opengis.net/gml">';
  filter += '<PropertyIsLike wildCard="*" singleChar="#" escapeChar="!">';
  // filter += '<PropertyName>' + propertyName + '</PropertyName>';
  // filter += '<Literal>*' + propertyValue + '*</Literal>';
  filter += "</PropertyIsLike>";
  filter += "</Filter>";
  const urlString = geoserverUrl + "/ows";
  const param = {
    service: "WFS",
    version: "1.0.0",
    request: "GetFeature",
    typeName: typeName,
    outputFormat: "application/json",
    // filter: filter
  };
  let Cesium = DC.getLib("Cesium");
  const geojsonUrl = urlString + getParamString(param, urlString);
  let viewer = window.viewer;
  let res = await axios.get(geojsonUrl);

  if (res.data.features?.length === 0) return;
  let list = res.data.features;
  list.forEach((item) => {
    if (item.geometry.coordinates.length !== 0) {
      item.geometry.coordinates[0][0].forEach((ii) => {
        // 做整体偏移
        // ii[0] = ii[0] + 5.5
        // ii[1] = ii[1] + 11.5
      });
    }
  });
  return res.data;
};
const getRainGridData = async (id) => {
  let hasLayer = window.viewer.getLayerGroup("flood-grids-group");
  if (hasLayer) {
    hasLayer.show = true;
  } else {
    let groupLayer = new DC.LayerGroup("flood-grids-group");
    let geojson = await getFloodGridData("hc:ymss2");
    let layer = new DC.GeoJsonLayer("flood-grids-layer", geojson, {
      // stroke: DC.Color.YELLOW,
      stroke: DC.Color.fromCssColorString("rgba(83,179,231,0)"),
      fill: DC.Color.fromCssColorString("rgba(83,179,231,0.5)"),
      strokeWidth: 0,
    });
    groupLayer.addLayer(layer);
    // let labelLayer = new DC.PrimitiveLayer('flood-grids-label-layer')
    // groupLayer.addLayer(labelLayer)
    window.viewer.addLayerGroup(groupLayer);
  }
  if (id == 1) {
    startLoadGridPointImages(); //二维图片网格
  } else {
    await createGridPointLabel(id); //二维淹没网格
  }

  //二维淹没图片
};
const startLoadGridPointImages = async () => {
  const imgTestArray = [
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC7.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC8.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC9.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC10.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC11.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC12.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC13.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC14.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC15.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC16.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC17.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC18.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC19.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC20.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC21.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC22.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC23.png",
    "/Analysis/D2GisPng/897099bf65bb44198cfff95a8d42c719/897099bf65bb44198cfff95a8d42c719/81550/R2DC24.png",
  ];
  imageListAcc.value = [];
  // http://typhoon.nmc.cn/weatherservice/imgs/radar/202311161954_1_0.png
  // 从当前时间往前推 10分钟一个间隔
  // 获取当前时间
  const now = new Date();
  // 初始化数组

  let list = [];
  // 循环往前推 10 分钟
  let startTime = "2024-04-24 00:00:00";
  let timesLocal = [];
  for (let i = 0; i < 18; i++) {
    // 计算整 10 分钟时间点 - 改成一个小时了
    const time = moment(startTime).add(i, "hour").valueOf();
    timesLocal.push(time);
  }
  max.value = 18;

  let index = 0;
  timesLocal.forEach((time, count) => {
    // 将时间添加到数组
    let tm = moment(time).format("YYYYMMDDHH00");
    marks.value[index] = moment(time).format("HH:00");
    let item = {
      name: tm,
      tm: time,
      baseUrl: imgTestArray[count],
    };
    index++;
    list.push(item);
  });
  getData(list);
};
const getData = (data) => {
  imageListAcc.value = data;
  // 加载第一张
  if (imageListAcc.value.length > 0) {
    loadImages();
  }
};

const loadImages = () => {
  loading.value = true;
  //加载淹没数据
  const layers = window.viewer.imageryLayers;
  cloudLayers.value = [];
  const cloudsPromise = [];
  let Cesium = DC.getLib("Cesium");
  const datas = imageListAcc.value;
  // 四角不一样
  let ss = Cesium.Rectangle.fromDegrees(
    105.690321,
    26.436869,
    105.780286,
    26.505773
  );
  for (let i = 0; i < datas.length; i++) {
    const lyr = Cesium.ImageryLayer.fromProviderAsync(
      Cesium.SingleTileImageryProvider.fromUrl(datas[i].baseUrl, {
        rectangle: ss,
      })
    );

    layers.add(lyr);
    lyr.show = true;
    lyr.alpha = 0.0;
    cloudLayers.value.push(lyr);
    // 最后一个可见因为展示最新的默认
    // if (i === datas.length - 1) {
    //   lyr.alpha = 0.75;
    //   currentValue.value = 0
    // }
    cloudsPromise.push(lyr.readyPromise);
  }
  // image加载完了之后执行
  Promise.all(cloudsPromise).then(() => {
    loading.value = false;
    console.log("演进图片加载完成!!!");
  });
};
const getRainGridData2 = async (id) => {
  let hasLayer = window.rightViewer.getLayerGroup("flood-grids-group");
  if (hasLayer) {
    hasLayer.show = true;
  } else {
    let groupLayer = new DC.LayerGroup("flood-grids-group");
    let geojson = await getFloodGridData("hc:ymss2");
    let layer = new DC.GeoJsonLayer("flood-grids-layer", geojson, {
      // stroke: DC.Color.YELLOW,
      stroke: DC.Color.fromCssColorString("rgba(83,179,231,0)"),
      fill: DC.Color.fromCssColorString("rgba(83,179,231,0.5)"),
      strokeWidth: 0,
    });
    groupLayer.addLayer(layer);
    // let labelLayer = new DC.PrimitiveLayer('flood-grids-label-layer')
    // groupLayer.addLayer(labelLayer)
    window.rightViewer.addLayerGroup(groupLayer);
  }
  await createGridPointLabel(id); //二维淹没网格
};

const updateGridLayer = (time) => {
  let layer = window.viewer.getLayer("flood-grids-layer");
  let Cesium = DC.getLib("Cesium");
  try {
    let values = gridTimes[time.replace("T", " ").substring(0, 19)];
    let color0 = new Cesium.Color.fromCssColorString(
      "rgba(83,179,231,0.5)"
    ).withAlpha(0);
    layer.eachOverlay((entity) => {
      let code = entity.properties.GRIDCODE._value;
      let value = values[code];
      let flv = getFloodTypeByValue(value);
      let color = value
        ? new Cesium.Color.fromCssColorString(flv.color).withAlpha(0.5)
        : color0;
      if (value) {
        entity.polygon.show = true;
        DC.Util.merge(entity.polygon, {
          material: color,
          // material: DC.Color.fromCssColorString("rgba(83,179,231,0.5)")
        });
      } else {
        entity.polygon.show = false;
      }
    });
  } catch (e) {}
};

const updateGridLayer2 = (time) => {
  // let time =  times.value[currentValue.value]
  let layer = window.rightViewer.getLayer("flood-grids-layer");
  let Cesium = DC.getLib("Cesium");
  try {
    let values = gridTimes2[time.replace("T", " ").substring(0, 19)];
    let color0 = new Cesium.Color.fromCssColorString(
      "rgba(83,179,231,0.5)"
    ).withAlpha(0);
    layer.eachOverlay((entity) => {
      let code = entity.properties.GRIDCODE._value;
      let value = values[code];
      let flv = getFloodTypeByValue(value);
      let color = value
        ? new Cesium.Color.fromCssColorString(flv.color).withAlpha(0.5)
        : color0;
      if (value) {
        entity.polygon.show = true;
        DC.Util.merge(entity.polygon, {
          material: color,
          // material: DC.Color.fromCssColorString("rgba(83,179,231,0.5)")
        });
      } else {
        entity.polygon.show = false;
      }
    });
  } catch (e) {}
};

// 创建降雨的网格点标签
const createGridPointLabel = async (dispIdParam) => {
  let viewer = window.viewer;
  let res = await getYmGridWaterDepthGeojson({
    dispId: dispIdParam,
  });
  gridTimes = {};
  // let time = '2023-01-01 01:00:00' // 只能写死了先
  let time = times.value[currentValue.value];
  for (let k in res.data) {
    let values = {};
    let lll = res.data[k];
    lll.forEach((v) => {
      values[Number(v[0])] = Number(v[1]);
    });
    gridTimes[k] = values;
  }
  // let labelLayer = viewer.getLayer('flood-grids-label-layer')
  let layer = viewer.getLayer("flood-grids-layer");
  if (layer) {
    layer.clear();
  }
  let Cesium = DC.getLib("Cesium");
  // debugger
  let values = gridTimes[time.replace("T", " ").substring(0, 19)] || [];
  layer.eachOverlay((entity) => {
    let code = entity.properties.GRIDCODE._value;
    let value = values[code];
    let flv = getFloodTypeByValue(value);
    let color = value
      ? new Cesium.Color.fromCssColorString(flv.color).withAlpha(0.5)
      : new Cesium.Color.fromCssColorString(flv.color).withAlpha(0);
    DC.Util.merge(entity.polygon, {
      material: color,
    });
  });
};

const changeType = (type) => {
  console.log(type);
  clear();
  curType.value = type;
  if (type === 1) {
  } else if (type === 2) {
  } else if (type === 3) {
  } else if (type === 4) {
  }
};

const formatTooltip = (val) => {
  if (imageListAcc.value.length > 0) {
    console.log(imageListAcc.value[Number(val)].tm);
    let str = moment(imageListAcc.value[Number(val)].tm).format(
      "YYYY-MM-DD HH:00"
    );
    return str;
  }
};

// 根据时间播放 状态
const floodPlay = () => {
  isplay.value = !isplay.value;
  if (isplay.value) {
    // 时间轴的数据应该先获取回来，在弹播放的事儿
    _play();
  } else {
    if (st.value) {
      clearInterval(st.value);
      st.value = null;
    }
  }
};
const _play = async () => {
  let maxValue = 0;
  const showAlpha = 0.7;
  let Cesium = DC.getLib("Cesium");
  if (dispId.value == 1) {
    maxValue = 18;
    window.viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        105.760321,
        26.476869,
        30000.0
      ),
    });
    await new Promise((resolve) => setTimeout(resolve, 1000));
  } else {
    maxValue = times.value.length;
    window.viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        112.20891133,
        30.25860634,
        25000.0
      ),
    });
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  // 判断当前是第几个，如果是最后的那么还原
  if (currentValue.value === maxValue) {
    currentValue.value = 0;
    cloudLayers.value[maxValue - 1].alpha = 0;
    cloudLayers.value[currentValue.value].alpha = showAlpha;
  } else {
  }
  st.value = window.setInterval(() => {
    if (dispId.value == 1) {
      if (currentValue.value === 0) {
        cloudLayers.value[currentValue.value].alpha = showAlpha;
      } else {
        cloudLayers.value[currentValue.value - 1].alpha = 0.0;
        cloudLayers.value[currentValue.value].alpha = showAlpha;
      }
    } else {
      curentStations(times.value[currentValue.value]);
      if (dispId2.value) {
        curentStations2(times.value[currentValue.value]);
      }
    }

    currentValue.value++;
    if (currentValue.value === maxValue) {
      clearInterval(st.value);
      stop();
    }
  }, 1000); //演进速度
};

const stop = () => {
  isplay.value = false;
};

const clear = () => {};
</script>

<style scoped>
.floodPreRehearsal-panel {
  position: absolute;
  left: 60px;
  top: 135px;
  padding: 0;
  width: 150px;
  height: auto;
  z-index: 20;
  border-radius: 4px;
}

.floodPreRehearsal {
  position: absolute;
  left: 25%;
  bottom: 34px;
  padding: 10px 12px;
  width: 742px;
  height: 63px;
  z-index: 20;
  background: rgba(0, 78, 196, 0.3);
  border: 1px solid #2ab3fc;
  border-radius: 4px;
}

.floodPreRehearsal2 {
  position: absolute;
  left: 25%;
  bottom: 287px;
  padding: 10px 12px;
  width: 842px;
  height: 63px;
  z-index: 100;
  background: rgba(0, 78, 196, 0.3);
  border: 1px solid #2ab3fc;
  border-radius: 4px;
}

.map-switch-item {
  height: 54px;
  padding: 5px 10px;
  font-weight: 400;
  color: #a3deff;
  line-height: 50px;
}

.map-switch-item:hover {
  border-radius: 2px;
  cursor: pointer;
  background-color: rgba(0, 72, 146, 0.2);
  color: #fed64b;
  border-radius: 20px;
}

.imgY {
  float: left;
  width: 54px;
}

.switchTitle {
  float: right;
}

.active {
  color: #fed64b;
}

.slider-demo-block {
  display: flex;
  align-items: center;
}

.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}

.slider-demo-block {
  display: flex;
  align-items: center;
}

.slider-demo-block .el-slider {
  width: 100%;
  margin-top: -10px;
  margin-left: 12px;
}

.slider-demo-block .demonstration {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0;
}

.slider-demo-block .demonstration + .el-slider {
  flex: 0 0 90%;
}

.el-slider__bar {
  background-color: #00000000 !important;
}

.el-slider__runway {
  background-color: #2162a5 !important;
}

:deep(.el-radio) {
  color: #a3deff;
}

:deep(.el-radio__inner) {
  background: none;
}

:deep(.el-radio__inner::after) {
  width: 7px;
  height: 7px;
  border-radius: var(--el-radio-input-border-radius);
  background-color: #00ccff;
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.15s ease-in;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  border-color: #96cdef;
  background: none;
}

:deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #a3deff;
}

:deep(.el-slider__stop) {
  position: absolute;
  height: 7px;
  width: 1px;
  border-radius: 0;
  background-color: #a3deff;
  transform: translateX(-50%);
  margin-left: 20px;
  margin-top: 6px;
}

:deep(.el-slider__button) {
  width: 10px;
  height: 10px;
}
</style>
<style>
.cusTip {
  background: #1b83f1 !important;
  color: #fff !important;
  border-radius: 4px !important;
  border: 1px solid #1b83f1 !important;
}

.el-popper.is-dark .el-popper__arrow::before {
  border: 1px solid #1b83f1 !important;
  background: #1b83f1 !important;
  right: 0;
}

.el-slider__runway {
  background-color: #a8cef0;
}

.el-slider__bar {
  background: linear-gradient(90deg, #1b83f1, #0cc3f8);
}

.el-slider__marks-text {
  transform: translateX(0%);
  color: #a3deff;
}

.markTT {
  position: relative;
  top: -23px;
  left: 2px;
  color: #a3deff;
}

.markImg {
  position: relative;
  top: -14px;
  left: 8px;
  width: 20px;
}

.markImgTitle {
  position: relative;
  top: -60px;
  left: -34%;
  text-align: center;
}

.markTitle {
  position: relative;
  top: -19px;
  left: -1px;
}
</style>
