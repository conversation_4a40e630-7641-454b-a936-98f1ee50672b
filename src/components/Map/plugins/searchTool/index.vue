<!--模糊搜索定位-->
<template>
  <div class="search-tool">
    <el-input
      v-model="searchKeyText"
      placeholder="请输入你要搜索的内容"
      class="input-with-select"
      @clear="hideResult"
      @keydown.enter="searchHandler"
      clearable
    >
      <template #suffix>
        <el-icon @click="searchHandler" class="el-input__icon"
          ><search
        /></el-icon>
      </template>
    </el-input>
    <div class="resultPanel" v-show="resultShow">
      <div class="resultItem" v-for="item in results" @click="goto(item)">
        <div class="resultItem_stnm">{{ item.STNM }}</div>
        <div class="resultItem_adnm">{{ item.ADNM }}</div>
        <div class="resultItem_sttp">{{ getSTTPNameText(item.STTP) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Search } from "@element-plus/icons-vue";
import { getStByName } from "@/api/watershed/ads";
import { getSTTPName } from "@/components/Map/utils/water-common";
import { ElMessage } from "element-plus";

defineOptions({
  name: "search-tool",
});
const searchKeyText = ref("");
const resultShow = ref(false);
const adcd = ref("");
const results = ref([
  // {
  //   STNM:'XX水库',
  //   ADNM:'湖南省湘潭市XX县',
  //   STTP:'ZZ'
  // },
  // {
  //   STNM:'YY雨量站',
  //   ADNM:'湖南省湘潭市XX县',
  //   STTP:'PP'
  // },
  // {
  //   STNM:'小一寸河道',
  //   ADNM:'湖南省湘潭市XX县',
  //   STTP:'RR'
  // }
]);

onMounted(() => {
  window.EventBus.$on("changeAreaSelect", (data) => {
    if (data.type === "adcd") {
      adcd.value = data.code || "";
    } else if (data.type === "watershed") {
    }
  });
});

const getSTTPNameText = (sttp) => {
  return getSTTPName(sttp);
};
const searchHandler = () => {
  resultShow.value = true;
  // 根据关键字查询
  let params = {
    adcd: adcd.value,
    ad: adcd.value,
    stnm: searchKeyText.value.trim(),
  };
  getStByName(params).then((res) => {
    let map = res.data;
    let list = [];
    for (let key in map) {
      // console.log(key, map[key])
      list.push(map[key]);
    }
    resultShow.value = list.length > 0;
    results.value = list;
    if (list.length === 0) {
      ElMessage({ message: "数据没有找到", type: "warning" });
    }
  });
};
const hideResult = () => {
  resultShow.value = false;
  window.EventBus.$emit("map/marker/clear");
};
const goto = (data) => {
  searchKeyText.value = data.STNM;
  hideResult();
  // 复杂定位
  let item = {
    data: {
      lng: data.LGTD,
      lat: data.LTTD,
    },
    duration: 3, // 飞行时间 秒
    callback: () => {
      console.log("飞行结束的回调");
    },
  };
  // // 简单形式
  // let item2 = {
  //   lng: data.LGTD,
  //   lat: data.LTTD
  // }
  window.EventBus.$emit("map/marker", data);
  window.EventBus.$emit("flyToPostion", item);
};
</script>

<style scoped>
.search-tool {
  position: absolute;
  left: 400px;
  top: 75px;
  width: 250px;
  height: 35px;
  padding: 2px;
  z-index: 19;
}
:deep(.el-input__wrapper) {
  background: rgba(1, 89, 175, 0.3);
  border: 1px solid #4bb1ff;
  border-radius: 2px;
  box-shadow: none !important;
}
:deep(.el-input__inner) {
  color: #a3deff;
}
:deep(.el-input__inner::placeholder) {
  color: #a3deff;
}
.resultPanel {
  width: 100%;
  background: rgb(26 45 93);
  border: 1px solid #4bb1ff;
  color: #fff;
}
.resultItem {
  padding: 5px 1px;
  height: 35px;
  display: flex;
}
.resultItem:hover {
  background: rgba(178, 189, 199, 0.3);
  cursor: pointer;
}
.resultItem_stnm {
  padding: 3px 1px;
  padding-left: 10px;
  font-size: 12px;
  width: 75px;
  float: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.resultItem_adnm {
  padding: 3px 1px;
  font-size: 12px;
  width: 95px;
  float: left;
  text-overflow: ellipsis; /* ellipsis:显示省略符号来代表被修剪的文本  string:使用给定的字符串来代表被修剪的文本*/
  white-space: nowrap; /* nowrap:规定段落中的文本不进行换行   */
  overflow: hidden; /*超出部分隐藏*/
  color: #9f9b9b;
}

.resultItem_sttp {
  padding: 3px 3px;
  font-size: 12px;
  width: auto;
  float: right;
  text-align: center;
  border: 1px #1c84c6 solid;
  border-radius: 8px;
  background: #025ba1;
}
</style>
