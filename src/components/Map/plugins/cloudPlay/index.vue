<template>
  <div v-show="isOpen">
    <div class="cloud-play-panel" style="display: none">
      <el-radio-group v-model="curType" @change="changeType">
        <el-radio :label="1">气象云图</el-radio>
        <el-radio :label="2">雷达回波图</el-radio>
        <el-radio :label="3">格网降雨预报</el-radio>
        <el-radio :label="4">山洪气象预警</el-radio>
      </el-radio-group>
    </div>
    <div
      class="cloud-play"
      v-show="curType != 4 && curType != 3"
      v-loading="loading"
    >
      <div class="slider-demo-block">
        <!--        <el-button type="primary" @click="getTestData">播放</el-button>-->
        <!--        <el-button type="primary" @click="getTestData2"   v-loading.fullscreen.lock="fullscreenLoading">等值面</el-button>-->
        <span class="demonstration" @click="cloudPlay"
          ><img
            style="width: 40px; height: 40px; cursor: pointer"
            :src="isplay ? play_pause : play_start"
        /></span>
        <el-slider
          v-model="currentValue"
          @change="changeSlider"
          :step="1"
          :max="10"
          show-stops
          :marks="marks"
          tooltip-class="cusTip"
          :format-tooltip="formatTooltip"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import axios from "axios";
import play_start from "../../images/play_start.png";
import play_pause from "../../images/play_pause.png";
import { ElLoading } from "element-plus";
import {
  getAdcBoundLayer,
  getContourData,
  getRainfallGridGeojson,
  getRainfallGridList,
  selectStlyInfo,
  selectStlyList,
} from "@/api/watershed/ads";
import moment from "moment";
import { getGuid, getRainTimeByType } from "@/utils/common";
import * as turf from "@turf/turf";

let cloudLayers = [];

// 响应式数据
const imageListAcc = ref([]);
const _layers = ref([]);
const isOpen = ref(false); // 默认是false
const fullscreenLoading = ref(false);
const loading = ref(false);
const loading1 = ref(false);
const isplay = ref(false);
const firstLoad = ref(true);
const curType = ref(1); //
//颜色的，等值面的
// colorArray:[0xFFFFFF, 0xFFFFFF, 0xA9F18D, 0x3BB642, 0x5EB9FF, 0x0000FE, 0xF901F8, 0x820444, 0x820444, 0x820444, 0x820444, 0x820444, 0x820444, 0x820444],
const colorArray = ref([]);
const regions = ref(
  "116.640419,42.730239,116.634651,42.674446,116.658655,42.670507,116.672272,42.647198,116.686169,42.657083,116.723588,42.607354,116.739692,42.617608,116.764134,42.600332,116.775286,42.555756,116.802152,42.555318,116.829436,42.577961,116.830232,42.602587,116.894856,42.578583,116.93482,42.607382,116.992686,42.558151,117.042829,42.543365,117.097509,42.574595,117.178934,42.511088,117.220899,42.498547,117.351849,42.534881,117.374214,42.532061,117.379158,42.515344,117.426698,42.514562,117.462106,42.460661,117.435485,42.453416,117.443918,42.425296,117.427414,42.403712,117.397706,42.400034,117.377534,42.378037,117.3957,42.346441,117.439116,42.324027,117.466636,42.340782,117.515921,42.342519,117.534008,42.274689,117.588333,42.272098,117.603081,42.257152,117.633473,42.261526,117.727085,42.230091,117.770148,42.237233,117.765765,42.094931,117.744178,42.048811,117.773259,42.045039,117.798651,42.007789,117.792392,41.964421,117.93809,42.0063,117.957751,41.96064,118.015411,41.943917,118.056943,41.961185,118.10206,41.931062,118.089672,41.812684,118.135214,41.75061,118.128216,41.716072,118.212162,41.640329,118.228663,41.576822,118.312596,41.558949,118.300416,41.543708,118.316427,41.50825,118.26442,41.464153,118.335239,41.443315,118.359508,41.386027,118.341871,41.369714,118.347421,41.329928,118.394519,41.308437,118.433779,41.266995,118.406865,41.242632,118.443306,41.217067,118.444205,41.193614,118.512195,41.179628,118.532737,41.161936,118.567316,41.171738,118.693512,41.14586,118.762523,41.178307,118.763481,41.147313,118.827576,41.117755,118.814152,41.095983,118.837138,41.08267,118.88943,41.092948,118.929666,41.051,118.955771,41.07457,118.955027,41.103807,119.046996,41.143439,119.115856,41.104154,119.177795,41.016808,119.215036,41.020081,119.233961,40.995951,119.277024,41.00009,119.255816,40.958738,119.271641,40.911442,119.242876,40.906011,119.251757,40.857014,119.211359,40.793879,119.261148,40.778056,119.264024,40.759515,119.313135,40.751869,119.359876,40.709731,119.367737,40.681592,119.401051,40.683997,119.446639,40.647312,119.406739,40.569674,119.421302,40.539987,119.474884,40.531866,119.491543,40.554168,119.543423,40.554117,119.562952,40.532524,119.540889,40.508973,119.593522,40.462783,119.587676,40.391714,119.57144,40.379164,119.596336,40.362118,119.580354,40.342692,119.709671,40.281542,119.673792,40.266526,119.664381,40.241308,119.682955,40.207667,119.709639,40.194477,119.735092,40.206297,119.745428,40.188563,119.754741,40.138844,119.718032,40.087559,119.764596,40.080963,119.742153,40.053432,119.81557,40.028941,119.840819,39.987515,119.802766,39.977641,119.784339,39.94868,119.69161,39.93658,119.677194,39.913756,119.664269,39.937105,119.610511,39.904941,119.555352,39.902607,119.526488,39.872184,119.529567,39.808082,119.464304,39.812082,119.363149,39.744218,119.257476,39.514234,119.258067,39.488679,119.302841,39.465693,119.293731,39.3871,119.253123,39.361699,119.192886,39.356721,119.0937,39.252544,119.01888,39.202888,119.014603,39.196264,119.005541,39.195544,118.966167,39.15786,118.849216,39.095085,118.792316,39.08999,118.786441,39.083624,118.778542,39.079641,118.751305,39.068502,118.73238,39.071631,118.714843,39.07298,118.666079,39.074776,118.646833,39.120758,118.630499,39.112428,118.601573,39.123509,118.605138,39.104096,118.551185,39.175818,118.533283,39.289405,118.560445,39.373361,118.540073,39.41287,118.574026,39.456699,118.59625,39.68017,118.565661,39.755597,118.611815,39.80007,118.591114,39.845385,118.609752,39.855484,118.629898,39.945261,118.602177,39.969286,118.611141,39.989649,118.593239,39.991383,118.592415,40.03248,118.54164,40.107108,118.516309,40.106501,118.476337,40.037562,118.437967,40.02701,118.453542,40.101579,118.431147,40.128072,118.386687,40.143622,118.353614,40.125327,118.260072,40.118278,118.183259,40.147606,118.159243,40.173027,118.195311,40.18797,118.19774,40.217551,118.131942,40.304154,118.093183,40.283811,118.07331,40.307122,118.048285,40.301446,117.997866,40.326162,117.941251,40.321627,117.935439,40.353362,117.885202,40.320438,117.842159,40.33492,117.781104,40.303514,117.745811,40.322358,117.752731,40.356835,117.688999,40.355206,117.673248,40.384116,117.612186,40.376444,117.58434,40.333493,117.592635,40.313638,117.571026,40.307166,117.528252,40.35305,117.470945,40.3572,117.424142,40.379219,117.447454,40.414073,117.400026,40.457878,117.436396,40.483562,117.423731,40.501358,117.454941,40.558986,117.448015,40.577681,117.483153,40.594982,117.498987,40.587508,117.527511,40.622239,117.568867,40.630035,117.573538,40.655038,117.508103,40.652688,117.497926,40.66562,117.5213,40.703688,117.49677,40.759834,117.434073,40.762957,117.384916,40.821494,117.356772,40.821288,117.315137,40.865173,117.300654,40.862556,117.28434,40.956051,117.199936,41.011357,117.21409,41.046195,117.190728,41.118927,117.168103,41.141428,117.124392,41.14292,117.089156,41.218173,116.990824,41.220608,116.878654,41.285525,116.87267,41.310443,116.845163,41.313848,116.806716,41.358285,116.732279,41.387512,116.771136,41.451231,116.716273,41.52257,116.715711,41.552031,116.663541,41.583653,116.664252,41.60828,116.638586,41.609097,116.628527,41.624962,116.544131,41.610553,116.528766,41.578828,116.460747,41.534216,116.409455,41.567773,116.381554,41.554872,116.332995,41.564923,116.274176,41.518922,116.225344,41.517063,116.21551,41.46963,116.158629,41.448825,116.14119,41.359955,116.040025,41.387297,116.02772,41.420106,116.003456,41.429315,116.004313,41.454796,115.975912,41.46914,115.966927,41.518185,115.893639,41.533745,115.837752,41.513471,115.816367,41.536601,115.816979,41.568795,115.782251,41.566632,115.749468,41.588113,115.730103,41.780346,115.772588,41.838878,115.760371,41.879261,115.687096,41.913108,115.619106,41.921988,115.597592,41.954813,115.562396,41.967054,115.535279,42.020766,115.549516,42.038877,115.593169,42.046137,115.61923,42.023532,115.696842,42.032823,115.719734,42.073952,115.778716,42.054339,115.774595,42.080178,115.818064,42.098414,115.791581,42.140269,115.815699,42.160508,115.798199,42.221993,115.859453,42.233872,115.887828,42.290866,115.942744,42.306015,115.937852,42.32645,115.971323,42.338458,115.980622,42.363748,116.031343,42.380842,116.016902,42.391972,116.056266,42.41931,116.043033,42.430606,116.053749,42.4709,116.119375,42.488294,116.138418,42.523968,116.166916,42.516377,116.180071,42.545578,116.243081,42.544476,116.26197,42.56868,116.309058,42.578201,116.290301,42.621071,116.341616,42.638482,116.327484,42.648752,116.369303,42.673745,116.427646,42.686577,116.495468,42.673955,116.485221,42.693076,116.537464,42.700051,116.543598,42.723555,116.584706,42.715272,116.640419,42.730239"
);
const currentValue = ref(0);
const adcd = ref("");
const marks = ref({
  0: "09:00",
  1: "09:10",
  2: "09:20",
  3: "09:30",
  4: "09:40",
  5: "09:50",
  6: "10:00",
  7: "10:10",
  8: "10:20",
  9: "10:30",
});

// 其他变量
let handlers = [];
let st = null;
let lycode = "";

// 方法定义
// 获取默认流域边界
const getInitBound = async () => {
  // 获取流域code
  let params = {
    lycode: "",
  };
  let res2 = await selectStlyList(params);
  let backData = formatTree(res2.data);
  let lycodeValue = backData[0].basinId;
  // 获取流域边界
  const res = await selectStlyInfo(lycodeValue);
  console.log(typeof res.data.geom);
  let geom =
    typeof res.data.geom == "string"
      ? JSON.parse(res.data.geom)
      : res.data.geom;
  if (geom.features?.length === 0) return;
  let fs = geom.features[0];
  let coordinates = fs.geometry.coordinates[0][0];
  let regionsStr = "";
  coordinates.forEach((pos) => {
    regionsStr += pos[0] + "," + pos[1] + ",";
  });
  regionsStr = regionsStr.substring(0, regionsStr.length - 2);
  regions.value = regionsStr;
};

const changeSlider = (val) => {
  console.log(val);
  isplay.value = false;
  if (st) {
    clearInterval(st);
    st = null;
  }
  cloudLayers.forEach((item) => {
    item.alpha = 0;
  });
  cloudLayers[currentValue.value - 1].alpha = 0.75; // 把当前显示
};

// 获取气象预警的数据
const getFloodWarnData = async (list) => {
  // 只有在选择省，市的时候生效 ，查询对应的一级子
  let gLayer = window.viewer.getLayerGroup("flood-warn-group");
  // if (gLayer && curType.value === 4) {
  //   gLayer.remove();
  // }
  let groupLayer = new DC.LayerGroup("flood-warn-group");
  // let geojsons = await getFloodWarnGeoJsonData(list)

  let geojsons = await getZWHGeoJsonData();
  // 用map key就是行政区adcd 对应的值是geojson
  for (let key in geojsons) {
    let geojson = geojsons[key];
    // let warnLevel = getRandomInt(2, 4);
    // 预警等级 可能发生（蓝色）<可能性较大（黄色）<可能性大（橙色）<可能性很大（红色）
    // list.forEach(ll => {
    //   if (key === ll.adcd) {
    //     warnLevel = ll.warnLevel
    //   }
    // })

    let warnLevel = 1;
    list.forEach((ll) => {
      if (geojson.features[0].properties.name === ll.adnm) {
        warnLevel = ll.warnLevel;
      }
    });
    let Cesium = DC.getLib("Cesium");
    // 不同的level不同颜色
    let color = new Cesium.Color.fromCssColorString(
      "rgba(176,178,180,0.5)"
    ).withAlpha(0.5);
    let alpha = 0.5;
    switch (warnLevel) {
      case 1:
        color = DC.Color.BLUE.withAlpha(alpha);
        break;
      case 2:
        color = DC.Color.YELLOW.withAlpha(alpha);
        break;
      case 3:
        color = DC.Color.ORANGE.withAlpha(alpha);
        break;
      case 4:
        color = DC.Color.RED.withAlpha(alpha);
        break;
    }
    let layer = new DC.GeoJsonLayer(getGuid(), geojson, {
      stroke: new Cesium.Color.fromCssColorString(
        "rgba(176,178,180,0.5)"
      ).withAlpha(0.5),
      fill: color,
      strokeWidth: 2,
    });
    groupLayer.addLayer(layer);
  }

  window.viewer.addLayerGroup(groupLayer);
};

const getZWHGeoJsonData = async () => {
  const response = await fetch("/datas/zhwgeojson.json");
  const json = await response.json();
  return json.data;
  // await axios
  //   .get("https://geo.datav.aliyun.com/areas_v3/bound/140403.json")
  //   .then((res) => {
  //     if (res) {
  //       geojsons.push(res.data);
  //     }
  //   });
  // await axios
  //   .get("https://geo.datav.aliyun.com/areas_v3/bound/140406.json")
  //   .then((res) => {
  //     if (res) {
  //       geojsons.push(res.data);
  //     }
  //   });
  // await axios
  //   .get("https://geo.datav.aliyun.com/areas_v3/bound/140425.json")
  //   .then((res) => {
  //     if (res) {
  //       geojsons.push(res.data);
  //     }
  //   });
  // await axios
  //   .get("https://geo.datav.aliyun.com/areas_v3/bound/410581.json")
  //   .then((res) => {
  //     if (res) {
  //       geojsons.push(res.data);
  //     }
  //   });
  // await axios
  //   .get("https://geo.datav.aliyun.com/areas_v3/bound/130426.json")
  //   .then((res) => {
  //     if (res) {
  //       geojsons.push(res.data);
  //     }
  //   });
};

const getFloodWarnGeoJsonData = async (list) => {
  // 获取当前的行政区数据，通过父，还是通过list自己去一个个获取
  let ls = [];
  list.forEach((item) => {
    ls.push(getAdcBoundLayer(item.adcd));
  });
  let res = await Promise.all(ls);
  console.log(res);
  let map = {};
  res.forEach((result) => {
    let key = result.data.features[0].properties.ADCD;
    let geometry = result.data.features[0].geometry;
    map[key] = geometry;
  });
  return map;
};

// 张教授接口 格网降雨预报 保存
// getRainGridData() {

//   let contourLayer = window.viewer.getLayerGroup('contour')
//   if (contourLayer) {
//     contourLayer.show = true
//   }
//   let hasLayer = window.viewer.getLayerGroup('rain-grids-group')
//   if (hasLayer) {
//     hasLayer.show = true
//   } else {
//     let groupLayer = new DC.LayerGroup('rain-grids-group')
//     let geojson = {}
//     loading1.value = ElLoading.service({
//       lock: true,
//       text: '生成中',
//       background: 'rgba(0, 0, 0, 0.7)',
//     })
//     getRainfallGridGeojson({}).then(res => {
//       loading1.value.close()
//       // 添加格网线
//       geojson = JSON.parse(res.data.gridJsonString)
//       let layer = new DC.GeoJsonLayer(
//         'rain-grids-layer',
//         geojson,
//         {
//           stroke: DC.Color.WHITE,
//           fill: DC.Color.fromCssColorString("rgba(32, 240, 255, 0)"),
//           strokeWidth: 2
//         }
//       )
//       groupLayer.addLayer(layer)
//       let labelLayer = new DC.PrimitiveLayer('rain-grids-label-layer')
//       groupLayer.addLayer(labelLayer)
//       window.viewer.addLayerGroup(groupLayer)

//       // 添加降雨量
//       let labelLayer1 = viewer.getLayer('rain-grids-label-layer')
//       if (labelLayer1) {
//         labelLayer1.clear()
//       }
//       res.data.gridDTOList.forEach(item => {
//         item.lng = 0
//         item.lat = 0
//         item.forecastRainAddressDTOS.forEach((items, index) => {
//           if (index < 4) {
//             item.lng += Number(items.longitude)
//             item.lat += Number(items.latitude)
//           }
//         })
//         item.lng = item.lng / 4
//         item.lat = item.lat / 4
//         let label = new DC.LabelPrimitive(new DC.Position(Number(item.lng), Number(item.lat)), Number(item.rainValue)?.toFixed(1))
//         label.setStyle({
//           fillColor: DC.Color.YELLOW,
//           font: '18px sans-serif',
//           "pixelOffset": { "x": -15, "y": 2 }, //偏移像素
//           "outlineColor": DC.Color.WHITE, //边框颜色
//           "outlineWidth": 1, //边框大小，
//           "scaleByDistance": {
//             "near": 400000, //最近距离
//             "nearValue": 1, //最近距离值
//             "far": 700000, //最远距离值
//             "farValue": 0.5 //最远距离值
//           }, //根据距离设置比例
//           "distanceDisplayCondition": {
//             "near": 0, //最近距离
//             "far": 1000000 //最远距离
//           }, //根据距离设置可见
//         })
//         labelLayer1.addOverlay(label)
//       })

//       // 添加底色
//       addContourGraphics(JSON.parse(res.data.mapJsonString))
//     }).catch((error) => {
//       loading1.value.close()
//     })
//   }
//   // createGridPointLabel()
// },
const getRainGridData = () => {
  let contourLayer = window.viewer.getLayerGroup("contour");
  if (contourLayer) {
    contourLayer.show = true;
  }
  let hasLayer = window.viewer.getLayerGroup("rain-grids-group");
  if (hasLayer) {
    hasLayer.show = true;
  } else {
    let groupLayer = new DC.LayerGroup("rain-grids-group");
    let geojson = getRainfallGridGeojsonData();
    let layer = new DC.GeoJsonLayer(
      "rain-grids-layer",
      // './datas/grids/base.json',
      // geojson,
      "./datas/grids/qyx_grids.geojson",
      {
        stroke: DC.Color.WHITE,
        fill: DC.Color.fromCssColorString("rgba(32, 240, 255, 0)"),
        strokeWidth: 2,
      }
    );
    groupLayer.addLayer(layer);
    let labelLayer = new DC.PrimitiveLayer("rain-grids-label-layer");
    groupLayer.addLayer(labelLayer);
    window.viewer.addLayerGroup(groupLayer);
  }
  createGridPointLabel();
};

const getRainfallGridGeojsonData = async () => {
  let res = await getRainfallGridGeojson({
    fymdh: "2023-12-04 08:00:00",
    range: 3,
  });
  return res.data.geojson;
};

const formatTree = (data) => {
  let dataList = data.map((item) => {
    return {
      ...item.data,
      children: item.children && formatTree(item.children),
    };
  });

  return dataList;
};
// 创建降雨的网格点标签
const createGridPointLabel = () => {
  // 预测的时间
  // 时段范围（1、3、5、8、10、12、24、72）
  let time = getRainTimeByType("today");
  let stm = moment(time[0]).format("YYYY-MM-DD HH:mm:ss");
  let labelLayer = viewer.getLayer("rain-grids-label-layer");
  if (labelLayer) {
    labelLayer.clear();
  }
  let points = "";
  fetch("./datas/grids/qyx_grids.geojson")
    .then((response) => response.json())
    .then((data) => {
      data.features.forEach((item) => {
        let center = turf.center(item);
        // console.log(center, 'center')
        // console.log(new DC.Position(center.geometry.coordinates[0], center.geometry.coordinates[1]), 'Position')
        // console.log((Math.random() * (10 - 0) + 0).toFixed(1), 'Math.random() * (10 - 0) + 0')
        if (center) {
          let label = new DC.LabelPrimitive(
            new DC.Position(
              center.geometry.coordinates[0],
              center.geometry.coordinates[1]
            ),
            (Math.random() * (10 - 0) + 0).toFixed(1) //随机生成0-10的浮点数
          );
          label.setStyle({
            fillColor: DC.Color.YELLOW,
            font: "18px sans-serif",
            pixelOffset: { x: -15, y: 2 }, //偏移像素
            outlineColor: DC.Color.WHITE, //边框颜色
            outlineWidth: 1, //边框大小，
            scaleByDistance: {
              near: 400000, //最近距离
              nearValue: 1, //最近距离值
              far: 700000, //最远距离值
              farValue: 0.5, //最远距离值
            }, //根据距离设置比例
            distanceDisplayCondition: {
              near: 0, //最近距离
              far: 1000000, //最远距离
            }, //根据距离设置可见
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
          });
          labelLayer.addOverlay(label);
          points += `${center.geometry.coordinates[0]},${
            center.geometry.coordinates[1]
          },${Math.random() * (10 - 0) + 0},`;
        }
      });
      points = points.substring(0, points.length - 2);
      window.EventBus.$emit("rainContour/visible", points);
    });
};
// createGridPointLabel() {
//   // 预测的时间
//   // 时段范围（1、3、5、8、10、12、24、72）
//   let time = getRainTimeByType("today");
//   let stm = moment(time[0]).format("YYYY-MM-DD HH:mm:ss");
//   getRainfallGridList({
//     // fymdh: '2023-12-04 08:00:00',
//     fymdh: stm,
//     range: 24, // 默认24小时
//   }).then((res) => {
//     for (let key in res.data) {
//       let list = res.data[key];
//       let labelLayer = viewer.getLayer("rain-grids-label-layer");
//       if (labelLayer) {
//         labelLayer.clear();
//       }
//       // 加等值面
//       let points = "";
//       list.forEach((item) => {
//         console.log(new DC.Position(Number(item[3]), Number(item[2])), 'Position')
//         console.log(Number(item[1])?.toFixed(1), 'Number')
//         let label = new DC.LabelPrimitive(
//           new DC.Position(Number(item[3]), Number(item[2])),
//           Number(item[1])?.toFixed(1)
//         );
//         label.setStyle({
//           fillColor: DC.Color.YELLOW,
//           font: "18px sans-serif",
//           pixelOffset: { x: -15, y: 2 }, //偏移像素
//           outlineColor: DC.Color.WHITE, //边框颜色
//           outlineWidth: 1, //边框大小，
//           scaleByDistance: {
//             near: 400000, //最近距离
//             nearValue: 1, //最近距离值
//             far: 700000, //最远距离值
//             farValue: 0.5, //最远距离值
//           }, //根据距离设置比例
//           distanceDisplayCondition: {
//             near: 0, //最近距离
//             far: 1000000, //最远距离
//           }, //根据距离设置可见
//         });
//         labelLayer.addOverlay(label);
//         points +=
//           Number(item[3]) +
//           "," +
//           Number(item[2]) +
//           "," +
//           Number(item[1])?.toFixed(1) * 1 +
//           ",";
//       });
//       // 加等值面
//       points = points.substring(0, points.length - 2);
//       window.EventBus.$emit("rainContour/visible", points);
//     }
//   });
//   //
//   //
//   // axios.get('./datas/grids/monitor.json', {
//   //   params: {
//   //   },
//   // }).then( (res) => {
//   //
//   // })
// },
// 创建降雨网格等值面 - 可能需要把一系列的等值面数据都计算出来
const creatRainGridContour = () => {};
const getCloudData = () => {
  getTestData();
};
const getRaderData = () => {
  imageListAcc.value = [];
  // http://typhoon.nmc.cn/weatherservice/imgs/radar/202311161954_1_0.png
  // 从当前时间往前推 10分钟一个间隔
  // 获取当前时间
  const now = new Date();
  // 初始化数组
  let list = [];
  // 循环往前推 10 分钟
  let times = [];
  for (let i = 1; i < 11; i++) {
    // 计算整 10 分钟时间点 - 改成一个小时了
    const time = new Date(now - i * 60 * 1000 * 60);
    times.push(time);
  }
  times.reverse();
  let index = 0;
  times.forEach((time) => {
    // 将时间添加到数组
    let tm = moment(time).format("YYYYMMDDHH00");
    marks.value[index] = moment(time).format("HH:00");
    let item = {
      name: tm,
      tm: time,
      baseUrl:
        "http://typhoon.nmc.cn/weatherservice/imgs/radar/" + tm + "_1_0.png",
    };
    index++;
    list.push(item);
  });
  getData(list);
};
const changeType = (type) => {
  if (!type) {
    clear();
    return;
  }
  console.log(type);
  clear();
  curType.value = type;
  if (type === 1) {
    // window.viewer.camera.setView({
    //   destination: Cesium.Cartesian3.fromDegrees(105.83618212265402,24.81243041350635,5682658.757761159),
    //   orientation: {
    //     heading:  0.03489478219342246, // 方向
    //     pitch: -1.3870607222766185, // 视角
    //     roll: 0.00014278912630860674 // 倾斜角度
    //   }
    // })
    getCloudData();
  } else if (type === 2) {
    getRaderData();
  } else if (type === 3) {
    getRainGridData();
  } else if (type === 4) {
    // getFloodWarnData()
  }
};
const changeVis = (flag) => {
  isOpen.value = flag;
  if (firstLoad.value && flag) {
    // 第一次可见
    // 默认请求数据 - 气象云图
    firstLoad.value = false;
    getCloudData();
  } else {
    let gLayer = window.viewer.getLayerGroup("rain-grids-group");
    if (gLayer && curType.value === 3) {
      gLayer.show = flag;
    }

    let gLayer2 = window.viewer.getLayerGroup("flood-warn-group");
    if (gLayer2 && curType.value === 4) {
      gLayer2.show = flag;
    }
    if (cloudLayers.length === 0) return;
    // 之后再进来就得判断了
    if (flag) {
      // 可见
      cloudLayers[currentValue.value - 1].alpha = 0.75; // 把当前可见
    } else {
      // 不可见不管是什么状态都关闭
      isplay.value = false;
      if (st) {
        clearInterval(st);
        st = null;
      }
      cloudLayers[currentValue.value - 1].alpha = 0.0; // 把当前隐藏
    }
  }
};
const formatTooltip = (val) => {
  if (imageListAcc.value.length > 0) {
    let str = moment(imageListAcc.value[Number(val)]?.tm).format(
      "YYYY-MM-DD HH:00"
    );
    return str;
  }
};

const clearContour = () => {
  let layer = window.viewer.getLayer("contour-layer");
  if (layer) {
    layer.clear();
  }
};

const showContour = (points) => {
  // 添加 layer
  let layer = window.viewer.getLayer("contour-layer");
  const loading = ElLoading.service({
    lock: true,
    text: "生成中",
    background: "rgba(0, 0, 0, 0.7)",
  });
  getContourData({
    points: points,
    regions: regions.value,
    levels: "",
    isline: false,
  }).then((res) => {
    loading.close();
    let json = res.msg;
    addContourGraphics(JSON.parse(json));
  });
};
// 测试等值面生成的
const getTestData2 = () => {
  // fullscreenLoading.value = true
  axios
    .post("/analysis/contour/result", {
      // getContourData( {
      // points: "116.6151627,40.90041289,15,116.7434707,40.79628933,62,116.6343721,40.73457731,22,116.5640361,40.69637678,20,116.4816423,40.80628722,5,116.3878609,40.87464606,2,116.6504488,40.62399669,30,116.6002088,40.58579611,32,116.5787731,40.47655584,45,116.6209747,40.35391185,54,116.6651862,40.37736842,60,116.6827863,40.44304654,56,116.6784302,40.45645024,55,116.6586597,40.51006495,42,116.6194539,40.32676929,65,116.681446,40.32107276,65,116.674074,40.28320725,68,116.6204592,40.29627588,70,116.5809183,40.29091439,90,116.4887677,40.28186688,71,116.4604469,40.35682758,67,116.3283605,40.4124784,40,116.3075754,40.4097965,35,116.5013467,40.42789971,55,116.3659079,40.36420301,40,116.6817081,40.26899304,60",
      points:
        "117.937189,40.973132,13,118.668243,39.502834,88,117.494222,40.415933,38,118.695862,41.016692,59,119.23831,39.875053,55,117.793737,40.957474,36,117.75346,41.936336,27,118.906712,39.424635,17,117.732359,41.311848,16,119.604778,39.933324,27,119.770221,39.977657,99,118.88707,39.890698,67,117.65319,40.545145,51,118.69471,39.997139,39,118.69752,39.739373,95,119.15662,39.711453,78,119.478444,39.833728,55,118.167351,40.766887,50,118.944135,40.405712,18,118.479077,40.609889,60,117.32619,40.939694,59,118.308337,40.140193,17,116.479311,42.200899,98,115.985886,42.239545,7",
      // regions: "116.66531,41.04423,116.67763,41.03978,116.68335,41.04291,116.68537,41.03581,116.69171,41.02411,116.69102,41.01979,116.69018,41.01460,116.68486,41.01126,116.68553,41.00680,116.67996,41.00067,116.67930,41.00036,116.67909,41.00002,116.67854,40.99841,116.67685,40.99346,116.67856,40.98090,116.67150,40.97457,116.67157,40.97024,116.68115,40.96023,116.68509,40.94876,116.69619,40.93940,116.70007,40.93268,116.70623,40.93356,116.71171,40.93439,116.71836,40.92781,116.70961,40.91706,116.70952,40.91669,116.70912,40.91508,116.70747,40.90847,116.71975,40.90208,116.72442,40.89564,116.73095,40.89598,116.73234,40.89492,116.74381,40.89037,116.74566,40.88963,116.75002,40.88911,116.75318,40.88874,116.75230,40.88204,116.76475,40.87926,116.77643,40.87193,116.78673,40.86270,116.79181,40.85218,116.79592,40.84959,116.79638,40.84128,116.79941,40.83899,116.80642,40.84661,116.80747,40.84662,116.81463,40.84675,116.81715,40.84126,116.83130,40.83995,116.84309,40.83517,116.84992,40.83369,116.85022,40.83330,116.85135,40.83187,116.85475,40.82759,116.85493,40.82401,116.86485,40.82029,116.86891,40.82001,116.86925,40.82001,116.87498,40.81329,116.87577,40.81235,116.87562,40.80155,116.88035,40.79966,116.87498,40.79647,116.87409,40.79595,116.86891,40.79708,116.86731,40.79743,116.86488,40.79315,116.85645,40.79086,116.86130,40.78343,116.85680,40.78223,116.85431,40.78108,116.84487,40.77680,116.84423,40.77351,116.83280,40.77029,116.82861,40.76867,116.83287,40.76184,116.83394,40.75924,116.82641,40.75192,116.82373,40.74997,116.82181,40.74857,116.82087,40.74788,116.81618,40.74856,116.81284,40.74903,116.81107,40.74856,116.79678,40.74461,116.78704,40.74852,116.78336,40.74999,116.78245,40.75036,116.77758,40.75619,116.77535,40.75092,116.77567,40.74999,116.77619,40.74851,116.77977,40.73828,116.78367,40.72567,116.78303,40.71246,116.77894,40.69981,116.77764,40.69904,116.76173,40.70177,116.75181,40.70463,116.75000,40.70316,116.74384,40.69812,116.74085,40.69568,116.72563,40.68991,116.71492,40.68184,116.70831,40.67822,116.70789,40.66668,116.70786,40.66572,116.70749,40.66528,116.70351,40.66045,116.70564,40.65394,116.70578,40.64131,116.69684,40.63013,116.69795,40.62720,116.69977,40.62243,116.69777,40.61765,116.69587,40.61384,116.70213,40.60639,116.69959,40.60159,116.70506,40.59906,116.70446,40.59602,116.70311,40.58873,116.70611,40.58408,116.70629,40.58335,116.70658,40.58200,116.70871,40.57315,116.70057,40.56303,116.69171,40.56418,116.68716,40.56153,116.68649,40.56068,116.68202,40.55601,116.67848,40.55253,116.67198,40.55041,116.68762,40.54691,116.70273,40.54144,116.70248,40.53053,116.71117,40.52379,116.70755,40.52201,116.70543,40.51667,116.69490,40.50697,116.69370,40.50003,116.69347,40.49875,116.69240,40.49280,116.68654,40.48658,116.69497,40.47982,116.70210,40.46474,116.71159,40.45057,116.71309,40.44657,116.71548,40.44033,116.71649,40.43318,116.71817,40.41669,116.71830,40.41541,116.71832,40.41523,116.71766,40.40244,116.71533,40.39451,116.70552,40.39074,116.70311,40.38590,116.70516,40.37887,116.70008,40.37509,116.70196,40.37176,116.70749,40.36732,116.71307,40.36793,116.71219,40.35993,116.72092,40.35958,116.71766,40.34484,116.71712,40.33846,116.72486,40.33780,116.72427,40.33382,116.73881,40.33770,116.73932,40.33335,116.73942,40.33246,116.74010,40.33207,116.74155,40.33125,116.74165,40.33120,116.74390,40.32995,116.74566,40.32893,116.75001,40.32648,116.75058,40.32523,116.76205,40.32405,116.76309,40.32047,116.76123,40.31284,116.75001,40.30405,116.74894,40.30321,116.74390,40.29904,116.73713,40.29344,116.73745,40.28764,116.73381,40.28223,116.73102,40.27966,116.72967,40.27864,116.72873,40.27793,116.72686,40.27685,116.72656,40.27667,116.71576,40.27043,116.70778,40.25879,116.70329,40.25292,116.70084,40.25002,116.69983,40.24881,116.69867,40.24744,116.68663,40.23861,116.67831,40.22798,116.67246,40.22993,116.67060,40.23055,116.66532,40.24328,116.67047,40.24648,116.67056,40.24885,116.67061,40.25001,116.67062,40.25028,116.67077,40.25425,116.65891,40.26120,116.65400,40.25996,116.63736,40.25634,116.63204,40.25775,116.63193,40.25778,116.62500,40.25963,116.62078,40.26075,116.62036,40.25980,116.61920,40.25721,116.61915,40.25708,116.61599,40.24999,116.61578,40.24951,116.61291,40.24999,116.61079,40.25034,116.59739,40.25017,116.59540,40.25513,116.59308,40.26097,116.58799,40.26332,116.58775,40.26343,116.58493,40.26473,116.58196,40.27038,116.57511,40.27258,116.57500,40.27262,116.57387,40.27299,116.57315,40.27324,116.56484,40.27614,116.54747,40.27582,116.53275,40.27062,116.52070,40.26089,116.50475,40.25783,116.50402,40.25769,116.49997,40.26114,116.49387,40.26433,116.47815,40.27267,116.47295,40.27874,116.46396,40.27939,116.45809,40.28397,116.44838,40.28759,116.44241,40.29818,116.43758,40.30103,116.44895,40.31183,116.44047,40.32171,116.43174,40.32311,116.42751,40.32754,116.40972,40.32947,116.40513,40.33187,116.40236,40.33333,116.40103,40.33402,116.38861,40.33477,116.38525,40.33779,116.38174,40.34744,116.37499,40.35129,116.37415,40.35176,116.36874,40.35746,116.36086,40.36566,116.36057,40.36597,116.33907,40.37222,116.32247,40.38366,116.30484,40.38755,116.28466,40.38213,116.28288,40.39918,116.28194,40.40369,116.29150,40.41197,116.29077,40.41537,116.29049,40.41669,116.28870,40.42516,116.28573,40.43731,116.28862,40.44907,116.29656,40.45935,116.30041,40.46468,116.29398,40.46909,116.28714,40.47729,116.28557,40.48381,116.29342,40.48510,116.30730,40.49139,116.32053,40.49838,116.32213,40.49861,116.32362,40.49881,116.33477,40.49858,116.34012,40.49848,116.34222,40.49858,116.35689,40.49922,116.35870,40.49852,116.36873,40.49464,116.37084,40.49379,116.36873,40.49054,116.36776,40.48906,116.36873,40.48775,116.37231,40.48285,116.37189,40.48043,116.37137,40.47741,116.37500,40.47721,116.37790,40.47705,116.39811,40.47920,116.40997,40.48161,116.41765,40.47833,116.42684,40.47664,116.43839,40.47993,116.44960,40.47963,116.45176,40.48705,116.47378,40.48332,116.48276,40.47974,116.49384,40.48135,116.49403,40.48140,116.49996,40.48145,116.50034,40.48145,116.51281,40.48998,116.51313,40.49468,116.51115,40.49700,116.50909,40.49867,116.50748,40.49998,116.49996,40.50603,116.49764,40.50792,116.49384,40.51349,116.49153,40.51692,116.48438,40.51554,116.47070,40.51254,116.46802,40.51558,116.46527,40.51629,116.45804,40.51815,116.45552,40.52387,116.45820,40.52613,116.46181,40.53018,116.47451,40.54366,116.47830,40.55118,116.49000,40.55349,116.49383,40.55807,116.49996,40.56537,116.50150,40.56722,116.51060,40.57619,116.51680,40.58013,116.51825,40.58196,116.51935,40.58333,116.52558,40.59121,116.53014,40.60274,116.52669,40.60712,116.52987,40.61402,116.53600,40.62544,116.55287,40.62522,116.55803,40.62664,116.56318,40.62327,116.56496,40.62624,116.56726,40.62958,116.56801,40.63483,116.55938,40.63554,116.54584,40.64143,116.53626,40.64184,116.53417,40.64751,116.53171,40.65241,116.52220,40.65162,116.51736,40.65368,116.51312,40.65553,116.51274,40.66047,116.51403,40.66331,116.51223,40.66525,116.51093,40.66665,116.50710,40.67074,116.49996,40.67104,116.49381,40.67130,116.49088,40.67144,116.48018,40.67312,116.47767,40.67656,116.48185,40.68864,116.49381,40.69463,116.49491,40.69519,116.49485,40.69741,116.49469,40.70352,116.49854,40.70617,116.49808,40.71868,116.49997,40.72025,116.50390,40.72352,116.50381,40.72988,116.50748,40.73970,116.50410,40.74083,116.49997,40.74307,116.49565,40.74543,116.49590,40.74849,116.49603,40.74999,116.49606,40.75035,116.49474,40.75845,116.49380,40.75867,116.48599,40.76102,116.47471,40.76962,116.45927,40.77097,116.45701,40.77734,116.45348,40.78857,116.45250,40.79526,116.44572,40.79662,116.43621,40.80534,116.43185,40.81661,116.41797,40.82167,116.40549,40.82864,116.40017,40.83159,116.40003,40.83167,116.39980,40.83333,116.39902,40.83906,116.39108,40.85009,116.38518,40.85346,116.38331,40.86038,116.37565,40.86180,116.37501,40.86248,116.37053,40.86726,116.36867,40.86887,116.35985,40.87635,116.34946,40.88543,116.33780,40.89454,116.32814,40.90418,116.32845,40.91499,116.32850,40.91670,116.32851,40.91707,116.32816,40.91805,116.33476,40.92926,116.34893,40.93495,116.35316,40.93436,116.35962,40.94121,116.35998,40.94159,116.36500,40.94140,116.36865,40.93785,116.37209,40.93448,116.37501,40.92928,116.37819,40.92361,116.38280,40.91667,116.38400,40.91487,116.38562,40.91245,116.39568,40.90371,116.40906,40.89880,116.41805,40.90037,116.42417,40.90144,116.43076,40.89731,116.43897,40.89772,116.45490,40.89803,116.45840,40.89447,116.46798,40.89561,116.47054,40.89802,116.46906,40.91043,116.46683,40.91492,116.46599,40.91665,116.46343,40.92191,116.46128,40.92963,116.45565,40.93155,116.45037,40.94283,116.44154,40.95266,116.44709,40.96464,116.44962,40.97669,116.45756,40.98277,116.46105,40.98156,116.46804,40.97638,116.47376,40.97816,116.47932,40.98064,116.48700,40.97666,116.49377,40.97573,116.49997,40.97492,116.50295,40.97452,116.51062,40.97374,116.51341,40.97978,116.52794,40.98503,116.53529,40.98890,116.54157,40.98633,116.55211,40.98697,116.55407,40.99140,116.56804,40.98656,116.57987,40.97794,116.59387,40.97392,116.60481,40.98103,116.60724,40.98118,116.60757,40.98138,116.60816,40.98172,116.61076,40.99108,116.61008,40.99848,116.60993,41.00000,116.60967,41.00289,116.61595,41.01432,116.61603,41.02596,116.60801,41.03537,116.61232,41.04719,116.61020,41.05138,116.61907,41.05376,116.62014,41.05405,116.62501,41.05883,116.62517,41.05899,116.63155,41.05924,116.63752,41.05755,116.64297,41.05601,116.65287,41.04908,116.66531,41.04423",
      regions:
        "116.640419,42.730239,116.634651,42.674446,116.658655,42.670507,116.672272,42.647198,116.686169,42.657083,116.723588,42.607354,116.739692,42.617608,116.764134,42.600332,116.775286,42.555756,116.802152,42.555318,116.829436,42.577961,116.830232,42.602587,116.894856,42.578583,116.93482,42.607382,116.992686,42.558151,117.042829,42.543365,117.097509,42.574595,117.178934,42.511088,117.220899,42.498547,117.351849,42.534881,117.374214,42.532061,117.379158,42.515344,117.426698,42.514562,117.462106,42.460661,117.435485,42.453416,117.443918,42.425296,117.427414,42.403712,117.397706,42.400034,117.377534,42.378037,117.3957,42.346441,117.439116,42.324027,117.466636,42.340782,117.515921,42.342519,117.534008,42.274689,117.588333,42.272098,117.603081,42.257152,117.633473,42.261526,117.727085,42.230091,117.770148,42.237233,117.765765,42.094931,117.744178,42.048811,117.773259,42.045039,117.798651,42.007789,117.792392,41.964421,117.93809,42.0063,117.957751,41.96064,118.015411,41.943917,118.056943,41.961185,118.10206,41.931062,118.089672,41.812684,118.135214,41.75061,118.128216,41.716072,118.212162,41.640329,118.228663,41.576822,118.312596,41.558949,118.300416,41.543708,118.316427,41.50825,118.26442,41.464153,118.335239,41.443315,118.359508,41.386027,118.341871,41.369714,118.347421,41.329928,118.394519,41.308437,118.433779,41.266995,118.406865,41.242632,118.443306,41.217067,118.444205,41.193614,118.512195,41.179628,118.532737,41.161936,118.567316,41.171738,118.693512,41.14586,118.762523,41.178307,118.763481,41.147313,118.827576,41.117755,118.814152,41.095983,118.837138,41.08267,118.88943,41.092948,118.929666,41.051,118.955771,41.07457,118.955027,41.103807,119.046996,41.143439,119.115856,41.104154,119.177795,41.016808,119.215036,41.020081,119.233961,40.995951,119.277024,41.00009,119.255816,40.958738,119.271641,40.911442,119.242876,40.906011,119.251757,40.857014,119.211359,40.793879,119.261148,40.778056,119.264024,40.759515,119.313135,40.751869,119.359876,40.709731,119.367737,40.681592,119.401051,40.683997,119.446639,40.647312,119.406739,40.569674,119.421302,40.539987,119.474884,40.531866,119.491543,40.554168,119.543423,40.554117,119.562952,40.532524,119.540889,40.508973,119.593522,40.462783,119.587676,40.391714,119.57144,40.379164,119.596336,40.362118,119.580354,40.342692,119.709671,40.281542,119.673792,40.266526,119.664381,40.241308,119.682955,40.207667,119.709639,40.194477,119.735092,40.206297,119.745428,40.188563,119.754741,40.138844,119.718032,40.087559,119.764596,40.080963,119.742153,40.053432,119.81557,40.028941,119.840819,39.987515,119.802766,39.977641,119.784339,39.94868,119.69161,39.93658,119.677194,39.913756,119.664269,39.937105,119.610511,39.904941,119.555352,39.902607,119.526488,39.872184,119.529567,39.808082,119.464304,39.812082,119.363149,39.744218,119.257476,39.514234,119.258067,39.488679,119.302841,39.465693,119.293731,39.3871,119.253123,39.361699,119.192886,39.356721,119.0937,39.252544,119.01888,39.202888,119.014603,39.196264,119.005541,39.195544,118.966167,39.15786,118.849216,39.095085,118.792316,39.08999,118.786441,39.083624,118.778542,39.079641,118.751305,39.068502,118.73238,39.071631,118.714843,39.07298,118.666079,39.074776,118.646833,39.120758,118.630499,39.112428,118.601573,39.123509,118.605138,39.104096,118.551185,39.175818,118.533283,39.289405,118.560445,39.373361,118.540073,39.41287,118.574026,39.456699,118.59625,39.68017,118.565661,39.755597,118.611815,39.80007,118.591114,39.845385,118.609752,39.855484,118.629898,39.945261,118.602177,39.969286,118.611141,39.989649,118.593239,39.991383,118.592415,40.03248,118.54164,40.107108,118.516309,40.106501,118.476337,40.037562,118.437967,40.02701,118.453542,40.101579,118.431147,40.128072,118.386687,40.143622,118.353614,40.125327,118.260072,40.118278,118.183259,40.147606,118.159243,40.173027,118.195311,40.18797,118.19774,40.217551,118.131942,40.304154,118.093183,40.283811,118.07331,40.307122,118.048285,40.301446,117.997866,40.326162,117.941251,40.321627,117.935439,40.353362,117.885202,40.320438,117.842159,40.33492,117.781104,40.303514,117.745811,40.322358,117.752731,40.356835,117.688999,40.355206,117.673248,40.384116,117.612186,40.376444,117.58434,40.333493,117.592635,40.313638,117.571026,40.307166,117.528252,40.35305,117.470945,40.3572,117.424142,40.379219,117.447454,40.414073,117.400026,40.457878,117.436396,40.483562,117.423731,40.501358,117.454941,40.558986,117.448015,40.577681,117.483153,40.594982,117.498987,40.587508,117.527511,40.622239,117.568867,40.630035,117.573538,40.655038,117.508103,40.652688,117.497926,40.66562,117.5213,40.703688,117.49677,40.759834,117.434073,40.762957,117.384916,40.821494,117.356772,40.821288,117.315137,40.865173,117.300654,40.862556,117.28434,40.956051,117.199936,41.011357,117.21409,41.046195,117.190728,41.118927,117.168103,41.141428,117.124392,41.14292,117.089156,41.218173,116.990824,41.220608,116.878654,41.285525,116.87267,41.310443,116.845163,41.313848,116.806716,41.358285,116.732279,41.387512,116.771136,41.451231,116.716273,41.52257,116.715711,41.552031,116.663541,41.583653,116.664252,41.60828,116.638586,41.609097,116.628527,41.624962,116.544131,41.610553,116.528766,41.578828,116.460747,41.534216,116.409455,41.567773,116.381554,41.554872,116.332995,41.564923,116.274176,41.518922,116.225344,41.517063,116.21551,41.46963,116.158629,41.448825,116.14119,41.359955,116.040025,41.387297,116.02772,41.420106,116.003456,41.429315,116.004313,41.454796,115.975912,41.46914,115.966927,41.518185,115.893639,41.533745,115.837752,41.513471,115.816367,41.536601,115.816979,41.568795,115.782251,41.566632,115.749468,41.588113,115.730103,41.780346,115.772588,41.838878,115.760371,41.879261,115.687096,41.913108,115.619106,41.921988,115.597592,41.954813,115.562396,41.967054,115.535279,42.020766,115.549516,42.038877,115.593169,42.046137,115.61923,42.023532,115.696842,42.032823,115.719734,42.073952,115.778716,42.054339,115.774595,42.080178,115.818064,42.098414,115.791581,42.140269,115.815699,42.160508,115.798199,42.221993,115.859453,42.233872,115.887828,42.290866,115.942744,42.306015,115.937852,42.32645,115.971323,42.338458,115.980622,42.363748,116.031343,42.380842,116.016902,42.391972,116.056266,42.41931,116.043033,42.430606,116.053749,42.4709,116.119375,42.488294,116.138418,42.523968,116.166916,42.516377,116.180071,42.545578,116.243081,42.544476,116.26197,42.56868,116.309058,42.578201,116.290301,42.621071,116.341616,42.638482,116.327484,42.648752,116.369303,42.673745,116.427646,42.686577,116.495468,42.673955,116.485221,42.693076,116.537464,42.700051,116.543598,42.723555,116.584706,42.715272,116.640419,42.730239",
      levels: "",
      isline: false,
    })
    .then((res) => {
      // fullscreenLoading.value = false
      let json = res.data.data;
      addContourGraphics(JSON.parse(json));
    });
};
const addContourGraphics = (obj) => {
  let Cesium = DC.getLib("Cesium");
  colorArray.value = [
    new Cesium.Color.fromBytes(169, 241, 141, 155),
    new Cesium.Color.fromBytes(59, 182, 66, 155),
    new Cesium.Color.fromBytes(94, 185, 255, 155),
    new Cesium.Color.fromBytes(0, 0, 254, 155),
    new Cesium.Color.fromBytes(249, 1, 248, 155),
    new Cesium.Color.fromBytes(130, 4, 68, 155),
    new Cesium.Color.fromBytes(130, 4, 68, 155),
    new Cesium.Color.fromBytes(130, 4, 68, 155),
    new Cesium.Color.fromBytes(130, 4, 68, 155),
    new Cesium.Color.fromBytes(130, 4, 68, 155),
    new Cesium.Color.fromBytes(130, 4, 68, 155),
    new Cesium.Color.fromBytes(130, 4, 68, 155),
  ];
  clearContour();
  const resultObject = obj["results"][0];
  const value = resultObject.value;
  const features = value.features;

  let layer = window.viewer.getLayer("contour-layer");
  features.forEach((feature) => {
    const attributes = feature.attributes;
    const i = attributes.level;
    const geometry = feature.geometry;
    let ringsArray = "";
    const rings = geometry.rings[0];
    rings.forEach((item) => {
      ringsArray += item[0] + "," + item[1] + ";";
    });
    ringsArray = ringsArray.substring(0, ringsArray.length - 1);
    let polygon = new DC.Polygon(ringsArray);
    polygon.setStyle({
      material: colorArray.value[i], // (this.isLine?Cesium.Color.GREEN.withAlpha(0.1):Cesium.Color.GREEN.withAlpha(0.6)),
      outline: true,
      outlineColor: colorArray.value[i], //Cesium.Color.ORANGE,
      outlineWidth: 2.0,
    });
    console.log(layer);
    layer.addOverlay(polygon);
    window.viewer.flyTo(layer);
  });
  // loading1.value.close()
};

const getTestData = () => {
  axios
    .get("./datas/clouds/clouds.json", {
      params: {},
    })
    .then((res) => {
      let json = res.data;
      if (json.success == true) {
        //成功返回
        let index = 0;
        // 获取当前时间
        const now = new Date();
        // 初始化数组
        let list = [];
        // 循环往前推 10 分钟
        let times = [];
        for (let i = 1; i < 11; i++) {
          // 计算整 10 分钟时间点 - 改成一个小时了
          const time = new Date(now - i * 60 * 1000 * 60);
          times.push(time);
        }
        times.reverse();
        times.forEach((time) => {
          // 将时间添加到数组
          let tm = moment(time).format("YYYYMMDDHH00");
          marks.value[index] = moment(time).format("HH:00");
          let obj = {
            name: tm,
            tm: time,
            baseUrl: json.data[index].baseUrl,
          };
          index++;
          list.push(obj);
        });
        getData(list);
      } else {
        console.log("云图查询报错!!!");
      }
    });
};
const cloudPlay = () => {
  isplay.value = !isplay.value;
  if (isplay.value) {
    // 时间轴的数据应该先获取回来，在弹播放的事儿
    _play();
  } else {
    if (st) {
      clearInterval(st);
      st = null;
    }
  }
};
const getData = (data) => {
  imageListAcc.value = data;
  // 加载第一张
  if (imageListAcc.value.length > 0) {
    loadImages();
  }
};
const _play = () => {
  const max = cloudLayers.length;
  const showAlpha = 0.7;
  // 判断当前是第几个，如果是最后的那么还原
  if (currentValue.value === max) {
    currentValue.value = 0;
    cloudLayers[max - 1].alpha = 0;
    cloudLayers[currentValue.value].alpha = showAlpha;
  } else {
  }
  st = window.setInterval(() => {
    if (currentValue.value === 0) {
      cloudLayers[currentValue.value].alpha = showAlpha;
    } else {
      cloudLayers[currentValue.value - 1].alpha = 0.0;
      cloudLayers[currentValue.value].alpha = showAlpha;
    }
    currentValue.value++;
    // currentValue.value = index
    if (currentValue.value === max) {
      clearInterval(st);
      // clear();
      stop();
    }
  }, 1000); //演进速度
};
const stop = () => {
  isplay.value = false;
};

const loadImages = () => {
  loading.value = true;
  //加载淹没数据
  const layers = window.viewer.imageryLayers;
  cloudLayers = [];
  const cloudsPromise = [];
  let Cesium = DC.getLib("Cesium");
  const datas = imageListAcc.value;
  // 四角不一样
  let ss = null;
  if (curType.value === 1) {
    ss = Cesium.Rectangle.fromDegrees(61.5, 6.0, 145.0, 60.0);
  } else if (curType.value === 2) {
    // Latitude: 55.776573 Longitude: 140.614014
    // 104.062500  36.580247
    ss = Cesium.Rectangle.fromDegrees(
      104.0625,
      36.580247,
      140.614014,
      55.776573
    );
  }
  for (let i = 0; i < datas.length; i++) {
    const lyr = Cesium.ImageryLayer.fromProviderAsync(
      Cesium.SingleTileImageryProvider.fromUrl(datas[i].baseUrl, {
        rectangle: ss,
      })
    );

    layers.add(lyr);
    lyr.show = true;
    lyr.alpha = 0.0;
    cloudLayers.push(lyr);
    // 最后一个可见因为展示最新的默认
    if (i === datas.length - 1) {
      // lyr.alpha = 0.75;
      currentValue.value = i + 1;
    }
    cloudsPromise.push(lyr.readyPromise);
  }
  // image加载完了之后执行
  Promise.all(cloudsPromise).then(() => {
    loading.value = false;
    console.log("演进图片加载完成!!!");
  });
};

const clear = () => {
  isplay.value = false;
  if (st) {
    clearInterval(st);
    st = null;
  }
  let layers = window.viewer.imageryLayers;
  if (cloudLayers && cloudLayers.length > 0) {
    for (let i = 0; i < cloudLayers.length; i++) {
      try {
        let llyr = cloudLayers[i];
        layers.remove(llyr, true);
      } catch (e) {
        console.log("wrong");
      }
    }
  }
  cloudLayers = [];
  imageListAcc.value = [];
  currentValue.value = 0;

  // 雨量网格的隐藏
  let gLayer = window.viewer.getLayerGroup("rain-grids-group");
  if (gLayer) {
    gLayer.show = false;
  }
  let contourLayer = window.viewer.getLayerGroup("contour");
  if (contourLayer) {
    contourLayer.show = false;
  }

  // 气象预警的隐藏
  let gLayer2 = window.viewer.getLayerGroup("flood-warn-group");
  if (gLayer2) {
    gLayer2.show = false;
  }
};

// 生命周期钩子
onMounted(() => {
  imageListAcc.value = [];
  _layers.value = [];
  handlers = [];
  // 触发面板显示
  window.EventBus.$on("layerCloud/visible", changeVis);
  // 触发等值面专题加载
  window.EventBus.$on("rainContour/visible", showContour);
  // 触发等值面专题清除
  window.EventBus.$on("rainContour/clear", clearContour);
  // UI改变之后，把左侧隐藏了就，把事件暴露出来就
  window.EventBus.$on("cloudPlay/change", changeType);
  // 气象预警地图数据联动
  window.EventBus.$on("update/flood/data", getFloodWarnData);
  window.EventBus.$on("changeAreaSelect", (data) => {
    if (data.type === "adcd") {
      adcd.value = data.code || "";
      regions.value = data.regions || "";
    } else if (data.type === "watershed") {
      lycode = data.code || "";
      regions.value = data.regions || "";
    }
  });
  getInitBound();
});

onUnmounted(() => {
  // 清理事件监听器
  window.EventBus.$remove("layerCloud/visible", changeVis);
  window.EventBus.$remove("rainContour/visible", showContour);
  window.EventBus.$remove("rainContour/clear", clearContour);
  window.EventBus.$remove("cloudPlay/change", changeType);
  window.EventBus.$remove("update/flood/data", getFloodWarnData);
  window.EventBus.$remove("changeAreaSelect");
});
</script>

<style scoped>
.cloud-play-panel {
  position: absolute;
  left: 60px;
  top: 135px;
  padding: 20px;
  width: 180px;
  height: 183px;
  z-index: 20;
  background: rgba(0, 57, 115, 0.9);
  border: 1px solid #1c8bda;
  border-radius: 4px;
}

.cloud-play {
  position: absolute;
  left: 40%;
  bottom: 34px;
  padding: 10px 12px;
  width: 632px;
  height: 63px;
  z-index: 20;
  background: rgba(0, 78, 196, 0.3);
  border: 1px solid #2ab3fc;
  border-radius: 4px;
}

.slider-demo-block {
  display: flex;
  align-items: center;
}

.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}

.slider-demo-block {
  display: flex;
  align-items: center;
}

.slider-demo-block .el-slider {
  width: 100%;
  margin-top: -10px;
  margin-left: 12px;
}

.slider-demo-block .demonstration {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0;
}

.slider-demo-block .demonstration + .el-slider {
  flex: 0 0 90%;
}

.el-slider__bar {
  background-color: #00000000 !important;
}

.el-slider__runway {
  background-color: #2162a5 !important;
}

:deep(.el-radio) {
  color: #a3deff;
}

:deep(.el-radio__inner) {
  background: none;
}

:deep(.el-radio__inner::after) {
  width: 7px;
  height: 7px;
  border-radius: var(--el-radio-input-border-radius);
  background-color: #00ccff;
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.15s ease-in;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  border-color: #96cdef;
  background: none;
}

:deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #a3deff;
}

:deep(.el-slider__stop) {
  position: absolute;
  height: 7px;
  width: 1px;
  border-radius: 0;
  background-color: #a3deff;
  transform: translateX(-50%);
  margin-left: 20px;
  margin-top: 6px;
}

:deep(.el-slider__button) {
  width: 10px;
  height: 10px;
}
</style>
<style>
.cusTip {
  background: #1b83f1 !important;
  color: #fff !important;
  border-radius: 4px !important;
  border: 1px solid #1b83f1 !important;
}

.el-popper.is-dark .el-popper__arrow::before {
  border: 1px solid #1b83f1 !important;
  background: #1b83f1 !important;
  right: 0;
}

.el-slider__runway {
  background-color: #a8cef0;
}

.el-slider__bar {
  background: linear-gradient(90deg, #1b83f1, #0cc3f8);
}

.el-slider__marks-text {
  transform: translateX(0%);
  color: #a3deff;
}
</style>
