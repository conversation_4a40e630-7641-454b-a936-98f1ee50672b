<!--地图切换-->
<template>
  <div class="map-switch map-panel">
    <div class="map-panel-content">
      <div class="panel-header" @click="changeFlag">
        <img
          :src="switch_map_img"
          style="width: 19px; padding: 2px; float: left"
        />
        <div style="padding: 0px 7px; float: left">切换地图</div>
        <el-icon v-show="!flag" class="control-icon"><ArrowRight /></el-icon>
        <el-icon v-show="flag" class="control-icon"><ArrowDown /></el-icon>
      </div>
      <el-divider v-if="flag" style="margin: 5px 0" />
      <div v-if="flag" style="height: 90px; width: 100%; display: inline-block">
        <div
          class="map-switch-item"
          :class="curType === 0 ? 'active' : ''"
          @click="changeBaseLayer(0)"
        >
          影像图
        </div>
        <div
          class="map-switch-item"
          :class="curType === 1 ? 'active' : ''"
          @click="changeBaseLayer(1)"
        >
          矢量图
        </div>
        <div
          class="map-switch-item"
          :class="curType === 2 ? 'active' : ''"
          @click="changeBaseLayer(2)"
        >
          地形图
        </div>
        <div class="map-switch-item-mode">
          <el-radio-group v-model="mapMode" class="ml-4" @change="changeMode">
            <el-radio label="3" size="large">三维</el-radio>
            <el-radio label="2" size="large">二维</el-radio>
          </el-radio-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import switch_map_img from "../../images/switch_map.png";
const flag = ref(false);
const mapMode = ref("3");
const curType = ref(0);

const changeMode = (value) => {
  // window.viewer.changeSceneMode(Number(value))
  const viewer = window.viewer;
  let Cesium = DC.getLib("Cesium");
  let centerResult = viewer.camera.pickEllipsoid(
    new Cesium.Cartesian2(
      viewer.canvas.clientWidth / 2,
      viewer.canvas.clientHeight / 2
    )
  );
  if (2 === Number(value)) {
    // 切到二维地图，如果设置到-90度，视角会自动跳转到正北，所以只设置到-88度
    rotateCamera(centerResult, -88);
    setTimeout(() => {
      // window.viewer.changeSceneMode(Number(value))
      viewer.scene.screenSpaceCameraController.enableTilt = false;
    }, 850);
  } else {
    // 切到三维地图
    // window.viewer.changeSceneMode(Number(value))
    setTimeout(() => {
      rotateCamera(centerResult, -30);
      viewer.scene.screenSpaceCameraController.enableTilt = true;
    }, 550);
  }
};
const changeBaseLayer = (value) => {
  curType.value = value;
  window.viewer.changeBaseLayer(value);
};
const changeFlag = () => {
  flag.value = !flag.value;
};
// 根据视点切换垂直视角
function rotateCamera(options, num) {
  const viewer = window.viewer;
  let Cesium = DC.getLib("Cesium");
  let position = options;
  // 给定切换所需时间，比如0.5s
  let flytime = 0.5;
  let initialPitch = viewer.camera.pitch;
  let pitch3d = Cesium.Math.toRadians(num);
  let angle = (pitch3d - initialPitch) / flytime; // 每秒转动的度数
  // 获取相机和视点距离
  let distance = Cesium.Cartesian3.distance(
    options,
    viewer.scene.camera.positionWC
  );
  let startTime = Cesium.JulianDate.fromDate(new Date());

  let stopTime = Cesium.JulianDate.addSeconds(
    startTime,
    flytime,
    new Cesium.JulianDate()
  );
  viewer.clock.startTime = startTime.clone(); // 开始时间
  viewer.clock.stopTime = stopTime.clone(); // 结速时间
  viewer.clock.currentTime = startTime.clone(); // 当前时间
  viewer.clock.clockRange = Cesium.ClockRange.CLAMPED; // 行为方式
  viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK; // 时钟设置为当前系统时间; 忽略所有其他设置。
  // 相机的当前heading
  let initialHeading = viewer.camera.heading;

  let Exection = function TimeExecution() {
    // 当前已经过去的时间，单位s
    let delTime = Cesium.JulianDate.secondsDifference(
      viewer.clock.currentTime,
      viewer.clock.startTime
    );
    // 根据过去的时间，计算偏航角的变化
    let heading = initialHeading;
    // 相机看点的角度，如果大于0那么则是从地底往上看，所以要为负值，这里取-30度
    let pitch = delTime * angle + initialPitch;

    viewer.camera.lookAt(
      position,
      new Cesium.HeadingPitchRange(heading, pitch, distance)
    );
    //解除目标锁定
    viewer.camera.lookAtTransform(Cesium.Matrix4.IDENTITY);
    if (
      Cesium.JulianDate.compare(
        viewer.clock.currentTime,
        viewer.clock.stopTime
      ) >= 0
    ) {
      viewer.clock.onTick.removeEventListener(Exection);
    }
  };
  viewer.clock.onTick.addEventListener(Exection);
}

onMounted(() => {});
</script>

<style scoped>
/*.map-switch {*/
/*  position: absolute;*/
/*  left: 260px;*/
/*  bottom: 30px;*/
/*  z-index: 18;*/
/*  width: 180px;*/
/*  height: auto;*/
/*}*/
.map-switch {
  width: 180px;
  margin: 5px;
  flex: 1;
}

.panel-header {
  width: 100%;
  height: 30px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #a3deff;
  line-height: 20px;
  padding: 5px 2px 5px 5px;
}
.tool-image {
  width: 40px;
  height: 40px;
  padding: 5px;
  cursor: pointer;
}
.control-icon {
  float: right;
  padding-right: 5px;
  font-size: 20px;
  cursor: pointer;
}
.map-switch-item {
  height: 30px;
  padding: 5px 10px;
  font-weight: 400;
  color: #7ca6d2;
  line-height: 20px;
}
.map-switch-item:hover {
  border-radius: 2px;
  cursor: pointer;
  background-color: #004892;
  color: #fff;
}
.map-switch-item-mode {
  height: 30px;
  padding: 5px 10px;
}
:deep(.el-radio) {
  color: #a3deff;
}
:deep(.el-radio__inner) {
  background: none;
}
:deep(.el-radio__inner::after) {
  width: 7px;
  height: 7px;
  border-radius: var(--el-radio-input-border-radius);
  background-color: #00ccff;
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.15s ease-in;
}
:deep(.el-radio__input.is-checked .el-radio__inner) {
  border-color: #96cdef;
  background: none;
}
:deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #a3deff;
}
:deep(.el-divider--horizontal) {
  border-top: 1px solid #4494d4;
}
.active {
  background-color: #004892;
  color: #fff;
}
</style>
