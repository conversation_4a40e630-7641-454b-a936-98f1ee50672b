<!--基础工具， 测量，标记-->
<template>
  <div style="position: relative; height: 100%; width: 100%">
    <div class="map-main" ref="mapMin3d"></div>
    <div class="draw-tool" v-show="showTool">
      <div>
        <!--        <el-button round plain @click="gotoMapInitExtent">回到原位</el-button>-->
        <el-button round type="primary" @click="drawPlot">绘制</el-button>
        <el-button round type="success" @click="savePlot">保存</el-button>
        <el-button round type="info" @click="removePlot">清除</el-button>
      </div>
    </div>
    <!-- 图例控件及其控制按钮，当legendIcons有内容时才显示 -->
    <template v-if="legendIcons.length > 0">
      <!-- 图例控件 -->
      <div class="map-legend" v-show="legendVisible">
        <span class="legend-close" @click="legendVisible = false">×</span>
        <div class="legend-item" v-for="(item, idx) in legendIcons" :key="idx">
          <img :src="`/icons/marker/${item.icon}`" :alt="item.label" />
          <span class="legend-label">{{ item.label }}</span>
        </div>
      </div>
      <!-- 显示图例按钮（图标） -->
      <button
        v-show="!legendVisible"
        class="legend-toggle-btn show"
        @click="legendVisible = true"
      >
        <span>≡</span>
      </button>
    </template>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, watch, shallowRef, computed } from "vue";
import img_yingxiang from "@/assets/icon/img.png";
import img_elec from "@/assets/icon/elec.png";
import img_ter from "@/assets/icon/ter.png";
let minViewer = null;
let Cesium = null;
let id = 1000;
let measure = null;
let plot = null;
let layer = null;
let overlay = null;
const mapMin3d = shallowRef(null);
const flag = ref(false);
const curType = ref("");
const emits = defineEmits(["updateBound", "save2Server"]);
const props = defineProps({
  geom: Object,
  showTool: Boolean,
  geoType: String,
  points: Array, //只适用于村社转移安置点的 两个村社、安置点点位展示 其他模块不作使用
  //额外的geom数据
  extraData: Object,
  icon: {
    type: [String, Array],
    required: true,
    // 支持 string 和 array 两种类型，array 时每项为 { icon: string, label: string }
  },
});
const redrawExtraData = (featureCollection) => {
  let hasLayer = minViewer.getLayer("extra-data-layer");
  if (hasLayer) {
    minViewer.removeLayer(hasLayer);
  }

  let layer = new DC.GeoJsonLayer("extra-data-layer", featureCollection, {
    stroke: DC.Color.fromCssColorString("rgba(83,179,231,1)"),
    strokeWidth: 5,
  });
  minViewer.addLayer(layer);
  minViewer.flyTo(layer);
};

// 图例显隐控制
const legendVisible = ref(true);
const legendIcons = computed(() => {
  if (Array.isArray(props.icon)) return props.icon;
  if (typeof props.icon == "string") return [{ icon: props.icon, label: "" }];
  return [];
});

const createViwer = (name, id) => {
  minViewer = new DC.Viewer(id, {
    // 解决浏览器缩放后场景变形模糊的问题 useBrowserRecommendedResolution如果为 true 模糊， false 清晰， 好使
    useBrowserRecommendedResolution: false,
  });
  minViewer.changeSceneMode(2);

  // // 是否支持图像渲染像素化处理
  Cesium = DC.getLib("Cesium");
  // Cesium.Camera.DEFAULT_VIEW_RECTANGLE = Cesium.Rectangle.fromDegrees(112.0, 38.5, 124.9,43.3);
  // if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
  //   viewer.resolutionScale = window.devicePixelRatio
  // }
  // 开启抗锯齿 不好使
  // viewer.scene.postProcessStages.fxaa.enabled = true
  //
  minViewer.scene.globe.maximumScreenSpaceError = 4 / 3;
  // 不好使
  // viewer.setOptions({
  //   enableFxaa: true
  // })

  // 设置初始视图位置
  minViewer.camera.setView({
    // fromDegrees()方法，将经纬度和高程转换为世界坐标
    destination: Cesium.Cartesian3.fromDegrees(
      118.64482064959539,
      36.1759707289059,
      575497.5787557325
    ),
    orientation: {
      heading: 6.283185307179586, // 方向
      pitch: -0.8655324845699397, // 视角
      roll: 6.283185307179585, // 倾斜角度
    },
  });
  // 开启深度检测，不同的级别需要动态控制
  minViewer.scene.globe.depthTestAgainstTerrain = false;
  // 增加地形
  // let terrain = DC.TerrainFactory.createTerrain(DC.TerrainType.XYZ,{
  //   // url: 'http://data.marsgis.cn/terrain'
  //   url: 'http://www.narutogis.com:8001/stk-terrain'
  // })
  // viewer.setTerrain(terrain)
  // // 地形夸张
  // viewer.setOptions({ globe: { terrainExaggeration: 3 } })

  let key = "197f27bd411701ca7ab7125797844883";
  let vec = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
    key,
  });
  let img = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.ARCGIS, {
    url: "https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer",
  });
  let china_td_yingxiang_meta = DC.ImageryLayerFactory.createImageryLayer(
    DC.ImageryType.TDT,
    {
      //天地影像图标记
      url: `http://t0.tianditu.gov.cn/cia_w/wmts?tk=`,
      key: `86dc8be8c8a491d9abdf50a40b111862`,
      style: "cva",
    }
  );
  let ter = DC.ImageryLayerFactory.createImageryLayer(DC.ImageryType.TDT, {
    key,
    style: "ter",
  });

  minViewer.addBaseLayer([img, china_td_yingxiang_meta], {
    name: "影像",
    iconUrl: img_yingxiang,
  });
  minViewer.addBaseLayer([vec], {
    name: "电子",
    iconUrl: img_elec,
  });
  minViewer.addBaseLayer([ter], {
    name: "地形",
    iconUrl: img_ter,
  });
  // 面渐变
  let regions =
    "116.640419,42.730239,116.634651,42.674446,116.658655,42.670507,116.672272,42.647198,116.686169,42.657083,116.723588,42.607354,116.739692,42.617608,116.764134,42.600332,116.775286,42.555756,116.802152,42.555318,116.829436,42.577961,116.830232,42.602587,116.894856,42.578583,116.93482,42.607382,116.992686,42.558151,117.042829,42.543365,117.097509,42.574595,117.178934,42.511088,117.220899,42.498547,117.351849,42.534881,117.374214,42.532061,117.379158,42.515344,117.426698,42.514562,117.462106,42.460661,117.435485,42.453416,117.443918,42.425296,117.427414,42.403712,117.397706,42.400034,117.377534,42.378037,117.3957,42.346441,117.439116,42.324027,117.466636,42.340782,117.515921,42.342519,117.534008,42.274689,117.588333,42.272098,117.603081,42.257152,117.633473,42.261526,117.727085,42.230091,117.770148,42.237233,117.765765,42.094931,117.744178,42.048811,117.773259,42.045039,117.798651,42.007789,117.792392,41.964421,117.93809,42.0063,117.957751,41.96064,118.015411,41.943917,118.056943,41.961185,118.10206,41.931062,118.089672,41.812684,118.135214,41.75061,118.128216,41.716072,118.212162,41.640329,118.228663,41.576822,118.312596,41.558949,118.300416,41.543708,118.316427,41.50825,118.26442,41.464153,118.335239,41.443315,118.359508,41.386027,118.341871,41.369714,118.347421,41.329928,118.394519,41.308437,118.433779,41.266995,118.406865,41.242632,118.443306,41.217067,118.444205,41.193614,118.512195,41.179628,118.532737,41.161936,118.567316,41.171738,118.693512,41.14586,118.762523,41.178307,118.763481,41.147313,118.827576,41.117755,118.814152,41.095983,118.837138,41.08267,118.88943,41.092948,118.929666,41.051,118.955771,41.07457,118.955027,41.103807,119.046996,41.143439,119.115856,41.104154,119.177795,41.016808,119.215036,41.020081,119.233961,40.995951,119.277024,41.00009,119.255816,40.958738,119.271641,40.911442,119.242876,40.906011,119.251757,40.857014,119.211359,40.793879,119.261148,40.778056,119.264024,40.759515,119.313135,40.751869,119.359876,40.709731,119.367737,40.681592,119.401051,40.683997,119.446639,40.647312,119.406739,40.569674,119.421302,40.539987,119.474884,40.531866,119.491543,40.554168,119.543423,40.554117,119.562952,40.532524,119.540889,40.508973,119.593522,40.462783,119.587676,40.391714,119.57144,40.379164,119.596336,40.362118,119.580354,40.342692,119.709671,40.281542,119.673792,40.266526,119.664381,40.241308,119.682955,40.207667,119.709639,40.194477,119.735092,40.206297,119.745428,40.188563,119.754741,40.138844,119.718032,40.087559,119.764596,40.080963,119.742153,40.053432,119.81557,40.028941,119.840819,39.987515,119.802766,39.977641,119.784339,39.94868,119.69161,39.93658,119.677194,39.913756,119.664269,39.937105,119.610511,39.904941,119.555352,39.902607,119.526488,39.872184,119.529567,39.808082,119.464304,39.812082,119.363149,39.744218,119.257476,39.514234,119.258067,39.488679,119.302841,39.465693,119.293731,39.3871,119.253123,39.361699,119.192886,39.356721,119.0937,39.252544,119.01888,39.202888,119.014603,39.196264,119.005541,39.195544,118.966167,39.15786,118.849216,39.095085,118.792316,39.08999,118.786441,39.083624,118.778542,39.079641,118.751305,39.068502,118.73238,39.071631,118.714843,39.07298,118.666079,39.074776,118.646833,39.120758,118.630499,39.112428,118.601573,39.123509,118.605138,39.104096,118.551185,39.175818,118.533283,39.289405,118.560445,39.373361,118.540073,39.41287,118.574026,39.456699,118.59625,39.68017,118.565661,39.755597,118.611815,39.80007,118.591114,39.845385,118.609752,39.855484,118.629898,39.945261,118.602177,39.969286,118.611141,39.989649,118.593239,39.991383,118.592415,40.03248,118.54164,40.107108,118.516309,40.106501,118.476337,40.037562,118.437967,40.02701,118.453542,40.101579,118.431147,40.128072,118.386687,40.143622,118.353614,40.125327,118.260072,40.118278,118.183259,40.147606,118.159243,40.173027,118.195311,40.18797,118.19774,40.217551,118.131942,40.304154,118.093183,40.283811,118.07331,40.307122,118.048285,40.301446,117.997866,40.326162,117.941251,40.321627,117.935439,40.353362,117.885202,40.320438,117.842159,40.33492,117.781104,40.303514,117.745811,40.322358,117.752731,40.356835,117.688999,40.355206,117.673248,40.384116,117.612186,40.376444,117.58434,40.333493,117.592635,40.313638,117.571026,40.307166,117.528252,40.35305,117.470945,40.3572,117.424142,40.379219,117.447454,40.414073,117.400026,40.457878,117.436396,40.483562,117.423731,40.501358,117.454941,40.558986,117.448015,40.577681,117.483153,40.594982,117.498987,40.587508,117.527511,40.622239,117.568867,40.630035,117.573538,40.655038,117.508103,40.652688,117.497926,40.66562,117.5213,40.703688,117.49677,40.759834,117.434073,40.762957,117.384916,40.821494,117.356772,40.821288,117.315137,40.865173,117.300654,40.862556,117.28434,40.956051,117.199936,41.011357,117.21409,41.046195,117.190728,41.118927,117.168103,41.141428,117.124392,41.14292,117.089156,41.218173,116.990824,41.220608,116.878654,41.285525,116.87267,41.310443,116.845163,41.313848,116.806716,41.358285,116.732279,41.387512,116.771136,41.451231,116.716273,41.52257,116.715711,41.552031,116.663541,41.583653,116.664252,41.60828,116.638586,41.609097,116.628527,41.624962,116.544131,41.610553,116.528766,41.578828,116.460747,41.534216,116.409455,41.567773,116.381554,41.554872,116.332995,41.564923,116.274176,41.518922,116.225344,41.517063,116.21551,41.46963,116.158629,41.448825,116.14119,41.359955,116.040025,41.387297,116.02772,41.420106,116.003456,41.429315,116.004313,41.454796,115.975912,41.46914,115.966927,41.518185,115.893639,41.533745,115.837752,41.513471,115.816367,41.536601,115.816979,41.568795,115.782251,41.566632,115.749468,41.588113,115.730103,41.780346,115.772588,41.838878,115.760371,41.879261,115.687096,41.913108,115.619106,41.921988,115.597592,41.954813,115.562396,41.967054,115.535279,42.020766,115.549516,42.038877,115.593169,42.046137,115.61923,42.023532,115.696842,42.032823,115.719734,42.073952,115.778716,42.054339,115.774595,42.080178,115.818064,42.098414,115.791581,42.140269,115.815699,42.160508,115.798199,42.221993,115.859453,42.233872,115.887828,42.290866,115.942744,42.306015,115.937852,42.32645,115.971323,42.338458,115.980622,42.363748,116.031343,42.380842,116.016902,42.391972,116.056266,42.41931,116.043033,42.430606,116.053749,42.4709,116.119375,42.488294,116.138418,42.523968,116.166916,42.516377,116.180071,42.545578,116.243081,42.544476,116.26197,42.56868,116.309058,42.578201,116.290301,42.621071,116.341616,42.638482,116.327484,42.648752,116.369303,42.673745,116.427646,42.686577,116.495468,42.673955,116.485221,42.693076,116.537464,42.700051,116.543598,42.723555,116.584706,42.715272,116.640419,42.730239";
  const positions2 = [];
  let coordinates = regions.split(",");
  for (let i = 0; i < coordinates.length; i += 2) {
    const longitude = coordinates[i];
    const latitude = coordinates[i + 1];
    positions2.push(Cesium.Cartesian3.fromDegrees(longitude, latitude));
  }
  const polygonHierarchy = new Cesium.PolygonHierarchy(positions2);

  let polygon2 = new Cesium.PolygonGeometry({
    polygonHierarchy: polygonHierarchy,
    vertexFormat: Cesium.VertexFormat.ALL,
  });
  let geometry = Cesium.PolygonGeometry.createGeometry(polygon2);
  let instance = new Cesium.GeometryInstance({
    geometry: geometry,
    attributes: {
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.RED),
    },
  });
  // 创建渐变材质
  const material = DC.Color.fromCssColorString("rgba(32, 240, 255, 110)");
  let primitive = new Cesium.Primitive({
    geometryInstances: instance,
    appearance: new Cesium.EllipsoidSurfaceAppearance({
      material: material,
    }),
  });
  // 将Primitive添加到场景中
  minViewer.scene.primitives.add(primitive);
  redrawPoints(props.points);
  redrawOverlay(props.geom);
};
const redrawPoints = (points) => {
  let hasLayer = minViewer.getLayer("draw-bound-layer");
  if (hasLayer) {
    minViewer.removeLayer(hasLayer);
  }

  // 检查点位数据有效性
  if (!points || points.length < 2 || !points[0] || !points[1]) {
    console.warn("无效的点位数据:", points);
    return;
  }

  let position = new DC.Position(
    parseFloat(points[0]),
    parseFloat(points[1], 500000)
  );
  // 检查解析后的坐标是否有效
  if (isNaN(position.lng) || isNaN(position.lat)) {
    console.warn("点位坐标解析失败:", points);
    return;
  }

  let point = new DC.Point(position);
  let layer = new DC.VectorLayer("draw-bound-layer");
  minViewer.addLayer(layer);
  layer.addOverlay(point);
  minViewer.flyTo(point);
};
const redrawOverlay = (json) => {
  // 如果json 是字符串,则转换为对象
  let geojson = json;
  if (typeof json === "string") {
    geojson = JSON.parse(json);
  }

  if (!geojson || !minViewer) return;
  let hasLayer = minViewer.getLayer("draw-bound-layer");
  if (hasLayer) {
    minViewer.removeLayer(hasLayer);
  }

  if (geojson.geometry?.type == "Point") {
    // 对于点位 使用DC.BillboardPrimitive来渲染 美观
    let hasLayer = minViewer.getLayer("draw-bound-point-layer");
    if (hasLayer) {
      minViewer.removeLayer(hasLayer);
    }
    let layer = new DC.PrimitiveLayer("draw-bound-point-layer");
    minViewer.addLayer(layer);
    const position = new DC.Position(
      geojson.geometry.coordinates[0],
      geojson.geometry.coordinates[1]
    );
    let name = null;
    if (props.icon) {
      name = "/icons/marker/" + props.icon[0].icon;
    } else {
      name = "/icons/marker/endPoi.png";
    }

    let point = new DC.BillboardPrimitive(position, name);
    point.setStyle({
      heightReference: 0,
      scale: 1,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      distanceDisplayCondition: {
        near: 0, //最近距离
        far: 5000000, //最远距离
      },
    });
    point.size = [20, 20];
    layer.addOverlay(point);
    //PrimitiveLayer和 BillboardPrimitive不支持在flyTo中飞行，所以手动设置相机位置，包括第三参 高度 一定要设置，不然会飞到地心
    minViewer.flyToPosition(
      new DC.Position(
        geojson.geometry.coordinates[0],
        geojson.geometry.coordinates[1],
        50000
      )
    );
  } else {
    let features = null;
    if (geojson.feature && geojson.feature.length) {
      features = geojson.feature[0];
    } else if (geojson.geometry && geojson.geometry.coordinates.length) {
      features = geojson.geometry.coordinates[0];
    }

    if (!features) return;

    geojson.features = [features];
    let layer = new DC.GeoJsonLayer("draw-bound-layer", geojson, {
      // stroke: DC.Color.YELLOW,
      stroke: DC.Color.fromCssColorString("rgba(83,179,231,1)"),
      // fill: DC.Color.fromCssColorString("rgba(83,179,231,0)"),
      strokeWidth: 5,
    });
    minViewer.addLayer(layer);
    minViewer.flyTo(layer);
  }

  // if (geojson.features){
  //   let fs = geojson.features[0]
  //   let geometry = fs.geometry
  //   let positions = []
  //   if(geometry.type==='MultiPolygon'){
  //
  //   } else if (geometry.type==='Polygon') {
  //     let rings = geometry.coordinates[0]
  //   }
  // }
};

const gotoMapInitExtent = () => {
  curType.value = "gotoMapInitExtent";
  let Cesium = DC.getLib("Cesium");
  minViewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      118.64482064959539,
      36.1759707289059,
      575497.5787557325
    ),
    orientation: {
      heading: 6.283185307179586, // 方向
      pitch: -0.8655324845699397, // 视角
      roll: 6.283185307179585, // 倾斜角度
    },
  });
};
const drawPlot = () => {
  let type = props.geoType || "polygon";

  console.log(type);

  let hasLayer = minViewer.getLayer("draw-bound-layer");
  if (hasLayer) {
    minViewer.removeLayer(hasLayer);
  }
  // 延迟初始化，因为viewer对象还没创建可能
  if (!plot) {
    plot = new DC.Plot(minViewer);
    layer = new DC.VectorLayer("plot-layer");
    minViewer.addLayer(layer);
  } else {
    let hasLayer2 = minViewer.getLayer("plot-layer");
    if (hasLayer2) {
      minViewer.removeLayer(hasLayer2);
    } else {
      layer = new DC.VectorLayer("plot-layer");
      minViewer.addLayer(layer);
    }
  }
  curType.value = type;

  plot &&
    plot.draw(type, (overlay2) => {
      if (overlay2) {
        // console.log(overlay2, '正在绘制')
        overlay = overlay2;
        layer.addOverlay(overlay);
        // plot.edit(overlay)
      } else {
        console.log("绘制出错");
      }
    });
};
const savePlot = () => {
  curType.value = "savePlot";
  // 把数据返回到父容器
  if (overlay) {
    let type = props.geoType || "polygon";
    let geojson = null;
    if (type === "polygon") {
      let list = [];
      overlay.positions.forEach((ps) => {
        list.push([ps.lng, ps.lat]);
      });
      list.push([overlay.positions[0].lng, overlay.positions[0].lat]);
      geojson = {
        type: "FeatureCollection",
        features: [
          {
            type: "Feature",
            properties: {
              name: "人工绘制",
            },
            geometry: {
              type: "MultiPolygon",
              coordinates: [[list]],
            },
          },
        ],
      };
    } else if (type == "point") {
      geojson = {
        type: "FeatureCollection",
        features: [
          {
            type: "Feature",
            properties: {
              name: "人工绘制",
            },
            geometry: {
              type: "Point",
              coordinates: [overlay.position._lng, overlay.position._lat],
            },
          },
        ],
      };
    } else {
      let list = [];
      overlay.positions.forEach((ps) => {
        list.push([ps.lng, ps.lat]);
      });
      geojson = {
        type: "FeatureCollection",
        features: [
          {
            type: "Feature",
            properties: {
              name: "人工绘制",
            },
            geometry: {
              type: "LineString",
              coordinates: list,
            },
          },
        ],
      };
    }

    let hasLayer = minViewer.getLayer("plot-layer");
    if (hasLayer) {
      minViewer.removeLayer(hasLayer);
    }
    emits("updateBound", geojson);
  }
};
const removePlot = () => {
  curType.value = "removePlot";
  try {
    layer.clear();
    let hasLayer = minViewer.getLayer("draw-bound-layer");
    if (hasLayer) {
      minViewer.removeLayer(hasLayer);
    }
    if (plot) {
      plot.stop();
    }
  } catch (e) {}
  try {
    let hasLayer = minViewer.getLayer("draw-bound-layer");
    if (hasLayer) {
      minViewer.removeLayer(hasLayer);
    }
    let hasLayer2 = minViewer.getLayer("plot-layer");
    if (hasLayer2) {
      minViewer.removeLayer(hasLayer2);
    }
  } catch (e) {}

  emits("updateBound", null);
};
// 清除
const clear = () => {
  curType.value = "clear";
};

onMounted(() => {
  DC.ready().then(() => {
    createViwer("minViewer", mapMin3d.value);
    // 如果有新的点位数据，则重新绘制
    if (props.points && props.points.length > 0) {
      // 延迟一下再绘制点位，确保地图已经完全初始化
      setTimeout(() => {
        if (minViewer) {
          redrawPoints(props.points);
        }
      }, 300);
    }
  });
});

onUnmounted(() => {});

//因为prop中的值非动态响应，所以需要通过watch监听，immediate 初始化时接收父组件中的传值
watch(
  () => props.geom,
  () => {
    // 更新地图按理说，或者延迟更新了就
    if (props.geom) {
      // Debug log removed before production
      if (minViewer) {
        redrawOverlay(props.geom);
      } else {
        // 如果 minViewer 还未初始化，等待初始化完成后再执行
        const checkMinViewerGeom = setInterval(() => {
          if (minViewer) {
            redrawOverlay(props.geom);
            clearInterval(checkMinViewerGeom);
          }
        }, 100);
      }
    } else {
      if (minViewer) {
        try {
          let hasLayer = minViewer.getLayer("draw-bound-layer");
          if (hasLayer) {
            minViewer.removeLayer(hasLayer);
          }
          let hasLayer2 = minViewer.getLayer("plot-layer");
          if (hasLayer2) {
            minViewer.removeLayer(hasLayer2);
          }
        } catch (e) {}
      }
    }
  },
  {
    immediate: true,
  }
);

watch(
  () => props.points,
  () => {
    if (
      props.points &&
      props.points.length > 0 &&
      props.points[0] !== undefined &&
      props.points[1] !== undefined
    ) {
      if (minViewer) {
        redrawPoints(props.points);
      } else {
        // 当地图尚未初始化时，添加一个延时任务
        const checkMinViewerPoints = setInterval(() => {
          if (minViewer) {
            redrawPoints(props.points);
            clearInterval(checkMinViewerPoints);
          }
        }, 100);
      }
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

watch(
  () => props.extraData,
  () => {
    if (props.extraData && minViewer) {
      redrawExtraData(props.extraData);
    } else {
      try {
        let hasLayer = minViewer.getLayer("draw-bound-layer");
        if (hasLayer) {
          minViewer.removeLayer(hasLayer);
        }
        let hasLayer2 = minViewer.getLayer("plot-layer");
        if (hasLayer2) {
          minViewer.removeLayer(hasLayer2);
        }
      } catch (e) {}
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="scss" scoped>
.draw-tool {
  width: auto;
  margin: 5px;
  flex: 1;
  position: absolute;
  top: 10px;
  left: 30%;
}

.map-main {
  width: 100%;
  height: 100%;
  display: inline-block;
  position: relative;
}

.map-legend {
  position: absolute;
  left: 16px;
  bottom: 16px;
  right: auto;
  background: linear-gradient(
    135deg,
    rgba(0, 36, 89, 0.95) 0%,
    rgba(0, 198, 255, 0.15) 100%
  );
  border: 1.5px solid #00c6ff;
  border-radius: 7px;
  box-shadow: 0 0 14px #00c6ff55;
  padding: 8px 10px 6px 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 100;
  transition: all 0.3s;
  min-width: 90px;
  width: max-content;
  max-height: none;
  overflow-y: visible;
}

.legend-close {
  position: absolute;
  right: 6px;
  top: 4px;
  font-size: 14px;
  color: #00c6ff;
  cursor: pointer;
  font-weight: bold;
  z-index: 2;
  background: transparent;
  border: none;
  outline: none;
  transition: color 0.2s;
}

.legend-close:hover {
  color: #fff;
  text-shadow: 0 0 8px #00c6ff;
}

.legend-item {
  display: flex;
  align-items: center;
  min-width: 60px;
  margin-bottom: 2px;
}

.legend-item img {
  width: 17px;
  height: 17px;
  margin-right: 5px;
  filter: drop-shadow(0 0 4px #00c6ff88);
}

.legend-label {
  color: #00c6ff;
  font-size: 10.5px;
  text-shadow: 0 0 5px #00c6ff44;
}

.legend-toggle-btn {
  position: absolute;
  background: #002459;
  color: #00c6ff;
  border: 1px solid #00c6ff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 0 6px #00c6ff44;
  transition: all 0.3s;
  z-index: 100;

  span {
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
  }

  &:hover {
    background: rgba(0, 36, 89, 0.8);
    box-shadow: 0 0 10px #00c6ff88;
  }
}

.legend-toggle-btn.show {
  left: 16px;
  bottom: 16px;
  right: auto;
  top: auto;
}

@media (max-width: 600px) {
  .map-legend {
    left: 4px;
    bottom: 4px;
    padding: 5px 5px 3px 5px;
    max-height: 30vh;
  }

  .legend-item img {
    width: 12px;
    height: 12px;
  }

  .legend-label {
    font-size: 8px;
  }

  .legend-toggle-btn {
    width: 24px;
    height: 24px;

    span {
      font-size: 14px;
      line-height: 1;
    }

    &:hover {
      box-shadow: 0 0 8px #00c6ff88;
    }
  }

  .legend-toggle-btn.show {
    left: 4px;
    bottom: 4px;
  }
}
</style>
