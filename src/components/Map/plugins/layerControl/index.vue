<!--图层控制-->
<template>
  <div class="layer-control map-panel" :class="{ expanded: flag }">
    <div class="map-panel-content">
      <div class="panel-header" @click="changeFlag">
        <img :src="toolImg" style="width: 19px; padding: 2px; float: left" />
        <div style="padding: 0 7px; float: left">图层管理</div>
        <el-icon v-show="!flag" class="control-icon">
          <ArrowRight />
        </el-icon>
        <el-icon v-show="flag" class="control-icon">
          <ArrowDown />
        </el-icon>
      </div>
      <el-divider v-if="flag" style="margin: 5px 0" />
      <div v-if="flag" class="custom-layer-tree">
        <!-- 河流层 -->
        <div class="layer-item" v-for="item in layerControls" :key="item.id">
          <!-- 父级图层 -->
          <div
            class="layer-node"
            v-if="!item.children || item.children.length === 0"
          >
            <div class="layer-content">
              <div class="layer-label">
                <div
                  v-if="item.className"
                  :class="[item.className, 'custom-tree-node']"
                />
                <img
                  v-if="item.icon"
                  :src="`/icons/marker/${item.icon}`"
                  class="node-icon"
                />
                <span>{{ item.label }}</span>
              </div>
              <div class="layer-controls">
                <a
                  v-if="item.visible"
                  @click="changeVis(item)"
                  class="visibility-btn"
                >
                  <el-icon>
                    <View />
                  </el-icon>
                </a>
                <a v-else @click="changeVis(item)" class="visibility-btn">
                  <el-icon>
                    <Hide />
                  </el-icon>
                </a>
              </div>
            </div>
          </div>

          <!-- 有子级的图层组 -->
          <div v-else class="layer-group">
            <div class="layer-group-header" @click="toggleGroup(item)">
              <div class="layer-label">
                <el-icon
                  class="expand-icon"
                  :class="{ expanded: item.expanded }"
                >
                  <CaretRight />
                </el-icon>
                <span>{{ item.label }}</span>
              </div>
            </div>

            <!-- 子级图层 -->
            <div v-show="item.expanded !== false" class="layer-children">
              <div
                v-for="child in item.children"
                :key="child.id"
                class="layer-child"
              >
                <!-- 如果子项没有children，直接显示 -->
                <div
                  v-if="!child.children || child.children.length === 0"
                  class="layer-content"
                >
                  <div class="layer-label">
                    <img
                      v-if="child.icon"
                      :src="`/icons/marker/${child.icon}`"
                      class="node-icon"
                    />
                    <span>{{ child.label }}</span>
                  </div>
                  <div class="layer-controls">
                    <a
                      v-if="child.visible"
                      @click="changeVis(child)"
                      class="visibility-btn"
                    >
                      <el-icon>
                        <View />
                      </el-icon>
                    </a>
                    <a v-else @click="changeVis(child)" class="visibility-btn">
                      <el-icon>
                        <Hide />
                      </el-icon>
                    </a>
                  </div>
                </div>

                <!-- 如果子项有children，显示为可展开的组 -->
                <div v-else class="layer-subgroup">
                  <div
                    class="layer-subgroup-header"
                    @click="toggleGroup(child)"
                  >
                    <div class="layer-label">
                      <el-icon
                        class="expand-icon"
                        :class="{ expanded: child.expanded }"
                      >
                        <CaretRight />
                      </el-icon>
                      <img
                        v-if="child.icon"
                        :src="`/icons/marker/${child.icon}`"
                        class="node-icon"
                      />
                      <span>{{ child.label }}</span>
                    </div>
                  </div>

                  <!-- 子项的子级 -->
                  <div
                    v-show="child.expanded !== false"
                    class="layer-subchildren"
                  >
                    <div
                      v-for="subchild in child.children"
                      :key="subchild.id"
                      class="layer-subchild"
                    >
                      <!-- 如果子项没有children，直接显示 -->
                      <div
                        v-if="
                          !subchild.children || subchild.children.length === 0
                        "
                        class="layer-content"
                      >
                        <div class="layer-label">
                          <div
                            v-if="subchild.type === 'rain-no'"
                            class="rain-status-icon rain-no-icon"
                          ></div>
                          <div
                            v-else-if="subchild.type === 'rain-yes'"
                            class="rain-status-icon rain-yes-icon"
                          ></div>
                          <img
                            v-else-if="subchild.icon"
                            :src="`/icons/marker/${subchild.icon}`"
                            class="node-icon"
                          />
                          <span>{{ subchild.label }}</span>
                        </div>
                        <div class="layer-controls">
                          <a
                            v-if="subchild.visible"
                            @click="changeVis(subchild)"
                            class="visibility-btn"
                          >
                            <el-icon>
                              <View />
                            </el-icon>
                          </a>
                          <a
                            v-else
                            @click="changeVis(subchild)"
                            class="visibility-btn"
                          >
                            <el-icon>
                              <Hide />
                            </el-icon>
                          </a>
                        </div>
                      </div>

                      <!-- 如果子项有children，显示为可展开的组 -->
                      <div v-else class="layer-rain-subgroup">
                        <div
                          class="layer-rain-subgroup-header"
                          @click="toggleGroup(subchild)"
                        >
                          <div class="layer-label">
                            <el-icon
                              class="expand-icon"
                              :class="{ expanded: subchild.expanded }"
                            >
                              <CaretRight />
                            </el-icon>
                            <span>{{ subchild.label }}</span>
                          </div>
                          <div class="layer-controls">
                            <a
                              v-if="subchild.visible"
                              @click.stop="changeVis(subchild)"
                              class="visibility-btn"
                            >
                              <el-icon>
                                <View />
                              </el-icon>
                            </a>
                            <a
                              v-else
                              @click.stop="changeVis(subchild)"
                              class="visibility-btn"
                            >
                              <el-icon>
                                <Hide />
                              </el-icon>
                            </a>
                          </div>
                        </div>

                        <!-- 降雨等级子项 -->
                        <div
                          v-show="subchild.expanded !== false"
                          class="layer-rain-levels"
                        >
                          <div
                            v-for="rainLevel in subchild.children"
                            :key="rainLevel.id"
                            class="layer-rain-level"
                          >
                            <div class="layer-content">
                              <div class="layer-label">
                                <div
                                  class="rain-level-icon"
                                  :style="{ backgroundColor: rainLevel.color }"
                                ></div>
                                <span>{{ rainLevel.label }}</span>
                              </div>
                              <!-- 降雨等级不显示控制按钮 -->
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 降雨等值面设置弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="设置降雨等值面"
      width="400px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
      class="contour-dialog"
    >
      <div class="dialog-content">
        <div class="time-picker-item">
          <span class="label">开始时间：</span>
          <el-date-picker
            v-model="startTime"
            type="datetime"
            placeholder="选择开始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="dark-date-picker"
            popper-class="dark-popper-class"
          />
        </div>
        <div class="time-picker-item">
          <span class="label">结束时间：</span>
          <el-date-picker
            v-model="endTime"
            type="datetime"
            placeholder="选择结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="dark-date-picker"
            popper-class="dark-popper-class"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose" class="cancel-button"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="handleConfirm"
            :loading="isConfirmLoading"
            class="confirm-button"
          >
            {{ isConfirmLoading ? "处理中..." : "确定" }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import toolImg from "../../images/layer.png";
import { measuringStationList } from "@/api/watershed/query/index";

import { ElMessage } from "element-plus";
import { useLayerControlStore } from "@/store/modules/map";

const layerControlStore = useLayerControlStore();

const { changeVis } = layerControlStore;

const {
  layerControls,
  startTime,
  endTime,
  currentContourLayer,
  dialogVisible,
} = storeToRefs(layerControlStore);
// 根据id查找图层
const findLayerById = (layers, id) => {
  if (!layers || !id) return null;
  for (const layer of layers) {
    if (layer.id === id) {
      return layer;
    }
    if (layer.children && layer.children.length > 0) {
      const found = findLayerById(layer.children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

const isConfirmLoading = ref(false);

// 处理弹窗关闭
const handleDialogClose = () => {
  dialogVisible.value = false;
  // 如果是通过关闭按钮关闭的，需要将图层状态改回false
  if (currentContourLayer.value) {
    currentContourLayer.value.visible = false;
    currentContourLayer.value = null;
  }
};

// 处理确认按钮
const handleConfirm = async () => {
  if (!startTime.value || !endTime.value) {
    ElMessage.warning("请选择时间范围");
    return;
  }

  isConfirmLoading.value = true;
  try {
    const res = await measuringStationList({
      startTime: startTime.value,
      endTime: endTime.value,
      rainLevel: "0,1,2,3,4,5,6",
      pageSize: 99999,
    });

    let rainList = res.data.ipage.records || [];
    rainList.sort((a, b) => b.totalRainValue - a.totalRainValue);

    rainList.forEach((item) => {
      item["nowTime"] = startTime.value;
      item["beforeTime"] = endTime.value;
      item["adnm"] = item.county;
    });

    window.EventBus.$emit("rain/update", rainList);
    if (rainList.length > 0) {
      openContour(rainList);
    }
    dialogVisible.value = false;
    currentContourLayer.value = null;
  } catch (error) {
    console.error("获取数据失败", error);
    ElMessage.error("获取数据失败");
  } finally {
    isConfirmLoading.value = false;
  }
};

const openContour = (rainList) => {
  let points = "";
  rainList.forEach((item) => {
    points +=
      item.lgtd + "," + item.lttd + "," + (item.totalRainValue || 0) + ",";
  });
  points = points.substring(0, points.length - 1);
  console.log(points, "points");
  window.EventBus.$emit("rainContour/visible", points);
};

const flag = ref(false);
const changeFlag = () => {
  flag.value = !flag.value;
};

// 切换图层组展开/收起状态
const toggleGroup = (item) => {
  item.expanded = item.expanded !== false ? false : true;
};

onMounted(() => {
  window.EventBus.$on("rain/update", () => {
    layerControls.value[1].children?.forEach((item) => {
      if (item.type === "rain") {
        item.visible = true;
      }
    });
  });
  window.EventBus.$on("river/update", () => {
    layerControls.value[1].children?.forEach((item) => {
      if (item.type === "river") {
        item.visible = true;
      }
    });
  });
});
</script>

<style lang="scss" scoped>
.layer-control {
  width: 180px;
  margin: 5px;
  flex: 1;
}

.layer-control.expanded {
  width: 230px;
}

.panel-header {
  width: 100%;
  height: 30px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #a3deff;
  line-height: 20px;
  padding: 5px 2px 5px 5px;
}

/* 自定义图层树样式 */
.custom-layer-tree {
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  color: #7ca6d2;
  line-height: 20px;
  padding: 5px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.layer-item {
  margin-bottom: 2px;
}

.layer-node {
  margin-left: 18px;

  .layer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px 8px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #004892;
      color: #fff;
    }
  }
}

.layer-group {
  .layer-group-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #004892;
      color: #fff;
    }
  }
}

.layer-children {
  margin-left: 20px;
  padding-left: 8px;
}

.layer-child {
  .layer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px 8px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #004892;
      color: #fff;
    }
  }
}

.layer-subgroup {
  .layer-subgroup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px 8px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #004892;
      color: #fff;
    }
  }
}

.layer-subchildren {
  margin-left: 20px;
  padding-left: 8px;
}

.layer-subchild {
  .layer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px 8px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #004892;
      color: #fff;
    }
  }
}

.layer-rain-subgroup {
  .layer-rain-subgroup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px 8px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #004892;
      color: #fff;
    }
  }
}

.layer-rain-levels {
  margin-left: 28px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}

.layer-rain-level {
  .layer-content {
    display: flex;
    align-items: center;
    padding: 0;
    border-radius: 3px;
    transition: all 0.2s;
    min-height: 18px;
    font-size: 11px;
  }
}

.layer-label {
  display: flex;
  align-items: center;
  flex: 1;

  .expand-icon {
    margin-right: 5px;
    transition: transform 0.2s;
    font-size: 12px;

    &.expanded {
      transform: rotate(90deg);
    }
  }

  .node-icon {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 5px;
  }

  .rain-status-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 5px;
    display: inline-block;
    vertical-align: middle;
  }

  .rain-no-icon {
    background-color: #d7d7d7;
  }

  .rain-yes-icon {
    background-color: #55dd33;
  }

  .rain-level-icon {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
    display: inline-block;
    vertical-align: middle;
    flex-shrink: 0;
  }

  .custom-tree-node {
    width: 16px;
    margin-right: 5px;
  }

  .river-custom {
    display: inline-block;
    height: 2px;
    background-color: #2a84b7;
  }
}

.layer-controls {
  .visibility-btn {
    color: inherit;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .el-icon {
      font-size: 16px;
      vertical-align: middle;
    }
  }
}

.tool-image {
  width: 40px;
  height: 40px;
  padding: 5px;
  cursor: pointer;
}

.control-icon {
  float: right;
  padding-right: 5px;
  font-size: 20px;
  cursor: pointer;
}

:deep(.el-divider--horizontal) {
  border-top: 1px solid #4494d4;
}

.dialog-content {
  padding: 20px;
}

.time-picker-item {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.time-picker-item .label {
  width: 80px;
  color: #606266;
}

.time-picker-item :deep(.el-date-picker) {
  width: 100%;
}

:deep(.el-dialog) {
  background: linear-gradient(
    180deg,
    rgba(0, 24, 82, 0.95) 0%,
    rgba(0, 36, 89, 0.95) 100%
  ) !important;
  border: 1px solid rgba(0, 198, 255, 0.3) !important;
  box-shadow: 0 0 20px rgba(0, 198, 255, 0.2) !important;
  backdrop-filter: blur(10px);
  border-radius: 4px;
  position: relative;
  margin-top: 15vh !important;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      rgba(0, 198, 255, 0) 0%,
      rgba(0, 198, 255, 0.3) 50%,
      rgba(0, 198, 255, 0) 100%
    );
  }

  .el-dialog__header {
    padding: 15px 20px;
    margin: 0;
    border-bottom: 1px solid rgba(0, 198, 255, 0.15);
    background: linear-gradient(
      180deg,
      rgba(0, 24, 82, 0.6) 0%,
      rgba(0, 36, 89, 0.6) 100%
    );

    .el-dialog__title {
      color: #00c6ff;
      font-size: 16px;
      font-weight: 400;
      text-shadow: 0 0 10px rgba(0, 198, 255, 0.3);
    }

    .el-dialog__close {
      color: #00c6ff;

      &:hover {
        color: #fff;
        text-shadow: 0 0 10px rgba(0, 198, 255, 0.5);
      }
    }
  }

  .el-dialog__body {
    padding: 20px;
    color: #fff;
    background: linear-gradient(
      180deg,
      rgba(0, 36, 89, 0.3) 0%,
      rgba(0, 24, 82, 0.3) 100%
    );
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid rgba(0, 198, 255, 0.15);
    background: linear-gradient(
      180deg,
      rgba(0, 24, 82, 0.6) 0%,
      rgba(0, 36, 89, 0.6) 100%
    );
  }
}

.time-picker-item {
  margin-bottom: 20px;
  display: flex;
  align-items: center;

  .label {
    width: 80px;
    color: #00c6ff;
    font-size: 14px;
    text-shadow: 0 0 10px rgba(0, 198, 255, 0.3);
  }

  :deep(.dark-date-picker) {
    flex: 1;

    .el-input__wrapper {
      background: rgba(0, 24, 82, 0.4);
      border: 1px solid rgba(0, 198, 255, 0.3);
      box-shadow: none;
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(0, 198, 255, 0.5);
        box-shadow: 0 0 10px rgba(0, 198, 255, 0.1);
      }

      &.is-focus {
        border-color: #00c6ff;
        box-shadow: 0 0 15px rgba(0, 198, 255, 0.2);
      }

      .el-input__inner {
        color: #fff;

        &::placeholder {
          color: rgba(0, 198, 255, 0.5);
        }
      }
    }
  }
}

.dialog-footer {
  :deep(.cancel-button) {
    background: transparent;
    border: 1px solid rgba(0, 198, 255, 0.3);
    color: #00c6ff;
    transition: all 0.3s ease;

    &:hover {
      border-color: #00c6ff;
      color: #fff;
      box-shadow: 0 0 15px rgba(0, 198, 255, 0.2);
      text-shadow: 0 0 10px rgba(0, 198, 255, 0.5);
    }
  }

  :deep(.confirm-button) {
    background: linear-gradient(180deg, #0072ff 0%, #00c6ff 100%);
    border: none;
    color: #fff;
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(180deg, #0088ff 0%, #00d8ff 100%);
      box-shadow: 0 0 15px rgba(0, 198, 255, 0.3);
    }

    &.is-loading {
      opacity: 0.8;

      &::before {
        border-color: #fff transparent transparent !important;
      }
    }
  }
}
</style>

<style lang="scss">
// 日期选择器下拉面板样式
.dark-popper-class {
  background: linear-gradient(
    180deg,
    rgba(0, 24, 82, 0.95) 0%,
    rgba(0, 36, 89, 0.95) 100%
  ) !important;
  border: 1px solid rgba(0, 198, 255, 0.3) !important;
  box-shadow: 0 0 20px rgba(0, 198, 255, 0.2) !important;
  backdrop-filter: blur(10px);

  .el-date-picker {
    background: transparent !important;
    color: #fff;

    .el-date-picker__header {
      color: #00c6ff;

      .el-date-picker__header-label {
        color: #00c6ff;

        &:hover {
          color: #fff;
        }
      }

      .el-picker-panel__icon-btn {
        color: #00c6ff;

        &:hover {
          color: #fff;
        }
      }
    }

    .el-picker-panel__content {
      background: transparent !important;

      .el-date-table {
        th {
          color: rgba(0, 198, 255, 0.7);
          font-weight: normal;
          border-bottom: 1px solid rgba(0, 198, 255, 0.1);
        }

        td {
          background: transparent !important;
          border: none;

          .el-date-table-cell__text {
            color: #fff;
          }

          &.available:hover {
            .el-date-table-cell__text {
              background: rgba(0, 198, 255, 0.2);
              box-shadow: 0 0 10px rgba(0, 198, 255, 0.1);
            }
          }

          &.current:not(.disabled) .el-date-table-cell__text {
            background: linear-gradient(180deg, #0072ff 0%, #00c6ff 100%);
            box-shadow: 0 0 10px rgba(0, 198, 255, 0.2);
          }

          &.disabled .el-date-table-cell__text {
            background: transparent;
            color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }

    .el-picker-panel__footer {
      background: linear-gradient(
        180deg,
        rgba(0, 24, 82, 0.6) 0%,
        rgba(0, 36, 89, 0.6) 100%
      ) !important;
      border-top: 1px solid rgba(0, 198, 255, 0.15);

      .el-button {
        background: transparent;
        border: 1px solid rgba(0, 198, 255, 0.3);
        color: #00c6ff;

        &:hover {
          border-color: #00c6ff;
          color: #fff;
          background: rgba(0, 198, 255, 0.1);
        }

        &--primary {
          background: linear-gradient(180deg, #0072ff 0%, #00c6ff 100%);
          border: none;
          color: #fff;

          &:hover {
            background: linear-gradient(180deg, #0088ff 0%, #00d8ff 100%);
            box-shadow: 0 0 15px rgba(0, 198, 255, 0.3);
          }
        }
      }
    }
  }

  .el-time-panel {
    background: linear-gradient(
      180deg,
      rgba(0, 24, 82, 0.95) 0%,
      rgba(0, 36, 89, 0.95) 100%
    ) !important;
    border: 1px solid rgba(0, 198, 255, 0.3);

    .el-time-panel__content {
      background: transparent !important;

      &::after,
      &::before {
        border-color: rgba(0, 198, 255, 0.3);
      }
    }

    .el-time-spinner__wrapper {
      &:hover {
        background: rgba(0, 198, 255, 0.05);
      }

      .el-time-spinner__item {
        color: rgba(255, 255, 255, 0.9);

        &.active {
          color: #00c6ff;
          font-weight: bold;
          text-shadow: 0 0 10px rgba(0, 198, 255, 0.3);
        }

        &:hover {
          background: rgba(0, 198, 255, 0.1);
        }
      }

      .el-time-spinner__list {
        &:hover::-webkit-scrollbar-thumb {
          background: rgba(0, 198, 255, 0.3);
        }
      }
    }

    .el-time-panel__footer {
      border-top: 1px solid rgba(0, 198, 255, 0.3);
      background: linear-gradient(
        180deg,
        rgba(0, 24, 82, 0.6) 0%,
        rgba(0, 36, 89, 0.6) 100%
      ) !important;

      .el-time-panel__btn {
        background: transparent !important;
        color: #00c6ff;
        border: 1px solid rgba(0, 198, 255, 0.3);

        &:hover {
          color: #fff;
          background: rgba(0, 198, 255, 0.1) !important;
          border-color: #00c6ff;
        }

        &.confirm {
          background: linear-gradient(
            180deg,
            #0072ff 0%,
            #00c6ff 100%
          ) !important;
          border: none;
          color: #fff;
          font-weight: bold;

          &:hover {
            background: linear-gradient(
              180deg,
              #0088ff 0%,
              #00d8ff 100%
            ) !important;
          }
        }
      }
    }
  }

  .el-input__wrapper {
    background: rgba(0, 24, 82, 0.4) !important;
    border: 1px solid rgba(0, 198, 255, 0.3) !important;
    box-shadow: none !important;

    &:hover {
      border-color: rgba(0, 198, 255, 0.5) !important;
    }

    &.is-focus {
      border-color: #00c6ff !important;
      box-shadow: 0 0 15px rgba(0, 198, 255, 0.2) !important;
    }

    .el-input__inner {
      color: #fff !important;

      &::placeholder {
        color: rgba(0, 198, 255, 0.5) !important;
      }
    }
  }

  .el-popper__arrow {
    display: none;
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 198, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 198, 255, 0.5);
    }
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}
</style>
