<!--图例显示-->
<template>
  <div class="map-legend">
    <div class="panel-header" >地图图例</div>
    <el-divider style="margin: 5px 0" />

  </div>
</template>

<script setup>
import { ref } from 'vue'

const changeVis = (data) => {
  if (!data.children) {
    data.children = []
  }
  // 控制图层可见不可见
  data.visible = !data.visible
}


const dataSource = ref([
  {
    id: 1,
    label: '河流层',
    visible: true,
    children: [
    ],
  },
  {
    id: 2,
    label: '监测站点',
    visible: true,
    children: [
      {
        id: 5,
        visible: true,
        label: '雨量站',
      },
      {
        id: 6,
        visible: true,
        label: '河道站',
      },
      {
        id: 7,
        visible: true,
        label: '水库站',
      },
    ],
  },
  {
    id: 10,
    visible: true,
    label: '气象云图',
    children: [
    ],
  },
])
</script>

<style scoped>
.map-legend {
  position: absolute;
  right: 540px;
  bottom: 15px;
  padding: 2px;
  z-index: 17;
  width: 180px;
  height: 280px;
  background: rgba(0,57,115,0.9);
  border: 1px solid #1C8BDA;

}

.panel-header {
  width: 100%;
  height: 30px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #A3DEFF;
  line-height: 20px;
  padding: 5px 2px 5px 5px ;
}
.panel-tree {
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  color: #7CA6D2;
  line-height: 20px;
  padding: 5px;
  -webkit-user-select:none; /* Chrome/Safari/Opera */
  -moz-user-select:none; /* Firefox */
  -ms-user-select:none; /* IE/Edge */
  user-select:none; /* Standard */
}
:deep(.el-tree){
  background: none;
}
:deep(.el-divider--horizontal) {
  border-top: 1px solid #4494D4;
}
:deep(.panel-tree .el-tree-node__label) {
  width: 100%;
}
</style>
<style>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.widget.map-cover .bg-cover {
  border: 0 solid;
  background: linear-gradient(to bottom, rgb(0, 12, 47) 0%, rgba(0, 12, 47, 0.4) 30%, rgba(0, 12, 47, 0) 50%, rgba(0, 12, 47, 0.4) 70%, rgb(0, 12, 47) 100%)!important;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  pointer-events: none;
}
.widget.map-cover .bg-cover2 {
  border: 0 solid;
  background: linear-gradient(to right, rgb(0, 12, 47) 0%, rgba(0, 12, 47, 0.1) 20%, rgba(0, 12, 47, 0) 50%, rgba(0, 12, 47, 0.1) 80%, rgb(0, 12, 47) 100%);
}
.widget.compass {
  position: absolute;
  top: 37px;
  left: 0;
  cursor: pointer;
  pointer-events: auto;
  user-select: none;
  width: 55px;
  height: 55px;
}
.widget.compass .out-ring {
  position: absolute;
  top: 0;
  left: 0;
  height: 55px;
  width: 55px;
  background-repeat: no-repeat;
  background-size: contain;
  fill: #3c71b7;
  border-radius: 50%;
}
.widget.zoom-controller {
  position: absolute;
  top: 100px;
  left: 15px;
  pointer-events: auto;
  user-select: none;
  background: #3c71b7;
  border-radius: 100px;
  border: solid 1px rgba(255,255,255,.2);
  text-align: center;
  box-sizing: border-box;
  line-height: 1.2rem;
  width: 24px;
  height: 60px;
}
.widget.location-bar {
  position: absolute;
  left: 270px;
  bottom: 2px;
  font-size: 14px;
  color: #fff;
  background: rgba(87, 87, 87, 0.15);
  padding: 2px 5px;
  border-radius: 2px;
  user-select: none;
  display: flex;
}
</style>
