/**
 * @Author: dlw
 * @Date: 2023-10-30 21:11:09
 */
import request from '@/utils/request'
import { isJSON } from '@/utils/common'
// 场景图层管理
class LayersManager {
  constructor() {
    // 所有高亮的元素
    this.curHighlights = []
    // 名称显示单独的图层
    this.labelLayer = null
    // 报警闪烁的效果图层
    this.warnLayer = null
    // 当前鼠标移入的对象
    this.curMouseMove = null
    this.htmlLayer = null
    // 名称挂牌面板||预警挂牌面板||实时信息挂牌
    this.htmlPanelLayer = null

    this.curLabel = null
    // 热力图
    this.heatLayer = null
    // 历史轨迹路径
    this.historyLayer = null
    // 当前的组layer
    this._groupLayer = null
    this.activityMouse = false
    // 给了个默认高度
    this._defaultHeight = 20
    this._billboard_w = 12
    this._billboard_h = 35
    this._offsetHight = 70

    // 保存当前高亮波纹组
    this.flashModels = []

    this._carPositionIntervalTime = 0.5 //单位秒

    this._modelInstanceLength = 0
    this._addModelInstanceNumber = 0

    this.initEvents()
  }
  // 对外暴露事件
  initEvents() {
    // 定位到某个位置flyToPostion
    window.EventBus.$on('flyToPostion', this.flyToPostion)
    // 定位到某个位置flyToPostion
    window.EventBus.$on('flyByStcd', this.flyByStcd)
    // 定位到某个实例
    window.EventBus.$on('flyToModel', this.flyToModel)
    // 异物热力图
    window.EventBus.$on('showHeatLayer', this.showHeatLayer)
    window.EventBus.$on('hideHeatLayer', this.hideHeatLayer)
    // 启动鼠标滑动事件
    window.EventBus.$on('scene/activityMouseShowLabel', this.activityMouseShowLabel)
    window.EventBus.$on('scene/deactivityMouseShowLabel', this.deactivityMouseShowLabel)
    // 暴露模型事件定位 绿波
    window.EventBus.$on('scene/showflashModels', this.showflashModels)
    window.EventBus.$on('scene/hideflashModels', this.hideflashModels)
    // 盯住某个模型视角跟踪
    window.EventBus.$on('scene/stareAtModel', this.stareAtModel)
    window.EventBus.$on('scene/unStareAtModel', this.unStareAtModel)
    // 展示某条路径
    window.EventBus.$on('scene/showHistoryPath', this.showHistoryPath)
    window.EventBus.$on('scene/hideHistoryPath', this.hideHistoryPath)
  }
  // 解析数据并且加载模型到场景 分layer
  parseLayerData(datas) {
    // // 增加新类支持 按距离偏移
    // class myDivIcon extends DC.DivIcon {
    //   constructor(position, content) {
    //     super(position, content)
    //   }
    //   _updateStyle(style, distance) {
    //     // console.log('---')
    //     super._updateStyle(style, distance)
    //     let topChangeByDistance = this._style.topChangeByDistance
    //     if (topChangeByDistance && topChangeByDistance) {
    //       let modelHeight = topChangeByDistance.modelHeight // 模型的高是根据比例来的
    //       let panelHeight = topChangeByDistance.panelHeight
    //       DC.Util.merge(this._delegate.style, {
    //         top: -((distance * 0.0005 + modelHeight) + (panelHeight / 2)) + 'px'
    //       })
    //     }
    //   }
    // }
    if (this._groupLayer) {
    } else {
      this._groupLayer = new DC.LayerGroup('scene')
      window.viewer.addLayerGroup(this._groupLayer)
    }
    if (this.labelLayer) {
      this.labelLayer.clear()
    } else {
      // 名称图层 ，默认是否显示，每次切换类型都刷新一下
      this.labelLayer = new DC.VectorLayer('labelLayer')
      this._groupLayer.addLayer(this.labelLayer)
    }
    if (this.htmlLayer) {
      this.htmlLayer.clear()
    } else {
      // 名称图层 ，默认是否显示，每次切换类型都刷新一下
      this.htmlLayer = new DC.HtmlLayer('htmlLayer')
      this._groupLayer.addLayer(this.htmlLayer)
    }
    if (this.htmlPanelLayer) {
      this.htmlPanelLayer.clear()
    } else {
      // 名称图层 ，默认是否显示，每次切换类型都刷新一下
      this.htmlPanelLayer = new DC.HtmlLayer('htmlPanelLayer')
      this._groupLayer.addLayer(this.htmlPanelLayer)
    }

    datas.forEach(item => {
      // groupLayer 第一级就是
      // 子的话就是根据不同的类型加到组里面
      // let layerGroup = new DC.LayerGroup(item.code)
      // window.viewer.addLayerGroup(layerGroup)
      if (item.children && item.children.length > 0) {
        this.createOverlayOnLayers(item.code, [item])
      }
      // 循环遍历里面的东西，根据不同类型加到不同类型
      // let layer = new DC.VectorLayer('layer')
      // layerGroup.addLayer(layer)
    })

  }
  // 展示图层信息
  showLayerByGroupId() {
    //
    let layers = this._groupLayer.getLayers()
    layers.forEach((layer) => {
      layer.eachOverlay((item) => {
        if (this._modelIsIn(item, polygon)) {
          selects.push(item)
        } else {
          item.show = false
        }
      })
    })
  }
  // 从自动建模增加模型到layer,无法提供当前的layer，根据类型不一样，只是在同一个组里面
  addToCurrentLayer(overlay) {
    let type = overlay.type
    if (type === 'wires_primitive') {
      let layer = this.getLayerByType(window.curCode || 'all', 'polyline', 'wire')
      layer.addOverlay(overlay)
    } else {
      let layer = this.getLayerByType(window.curCode || 'all', overlay.type)
      layer.addOverlay(overlay)
    }
  }
  flyToPostion(item) {
    let position = null
    if(item.lng) {
      position = new DC.Position(item.lng, item.lat,20000.0,0,-90)
      window.viewer.flyToPosition(position, null, 3)
    } else {
      let data = item.data
      let callback = item.callback || null
      let duration = item.duration || 3
      position = new DC.Position(data.lng, data.lat,20000.0,0,-90)
      window.viewer.flyToPosition(position, callback, duration)
    }
    setTimeout(()=>{
      let circleScan = new DC.CircleScan(window.viewer, position, 2000, {
        speed: 30
      })
      circleScan.start()
      setTimeout(() => {
        circleScan.stop()
        circleScan = null
      }, 1000 * 3)
    },3080)
  }
  flyByStcd(item){
    if(item.lng) { // 如果有经纬度那么就直接定位
      let position = new DC.Position(item.lng, item.lat,300.0,0,-90)
      window.viewer.flyToPosition(position, null, 3)
    } else {
      // 没有的话那就得查询或者从全局测站信息里面获取回来
    }
  }
  flyToModel(data) {
    layersManager.getCurrentModel(data.modelId, data.flyToFlag)
  }
  // 根据图层管理里面的某个叶子ID对应的modelId 获取当前overlay
  getCurrentModel(modelId, flyToFlag) {
    let layers = this._groupLayer.getLayers()
    layers.forEach((layer) => {
      layer.eachOverlay((item) => {
        // 这块类型要扩展判断
        if (item.id === modelId && (item.type !== 'div_icon' && item.type !== 'billboard' && item.type !== 'label')) {
          // 高亮飞过去
          this.highligtModel(item)
          if (flyToFlag) {
            window.viewer.flyTo(item, 1)
          }
          return item
        }
      })
    })
  }
  // 根据模型实例ID删除模型以及对应挂牌
  deleteModelById(modelId) {
    let layers = this._groupLayer.getLayers()
    layers.forEach((layer) => {
      layer.eachOverlay((item) => {
        if (item.id === modelId) {

          layer.removeOverlay(item)
        }
      })
    })
  }
  // 根据图层获取模型示例，叠加场景中
  createOverlayOnLayers(code, list) {
    list.forEach(item => {
      // 是组，不是实例层
      if (item.leaf === '0') {
        // 找组里面的 子继续 ，同时他的子没有字了，直接是实例了
        if (item.children && item.children.length > 0 && item.children[0].leaf === '1') {

          // 这个组是个图层组，他的子就是实例了
          request.getlistModelInstanceTree(item.id).then(res => {
            res.data.children.forEach(children => {
              let modelInstance = children.modelInstance
              if(this._maxModelInstanceId < item.id){
                this._maxModelInstanceId = item.id
              }
              let layerId = children.parentId;
              if (modelInstance !== null && modelInstance.model !== null) {
                try {
                  this._addModelInstanceNumber ++
                  if(this._addModelInstanceNumber == this._modelInstanceLength){
                    // 加载道面相关数据
                    setTimeout(() => {
                    }, 3000)
                  }
                  let instanceType = modelInstance.instanceType
                  let type = modelInstance.model.featureType
                  let layer = this.getLayerByType(code, type, instanceType)
                  this.addOverLay(layer, modelInstance, layerId, code)
                } catch (e) {
                  console.log(e, children, '数据不对')
                }
              } else if (children.children) {
                this.createOverlayOnLayers(code, children.children)
              }
            })
          })
        } else if (item.children && item.children.length > 0) {
          this.createOverlayOnLayers(code, item.children)
        }
      } else {
        // 在组的里面直接有节点是实例叶子节点
        // console.log(item, '这是实例叶子节点')
        request.getlistModelInstanceTree(item.id).then(res => {
          try {
            this._addModelInstanceNumber ++
            if(this._addModelInstanceNumber == this._modelInstanceLength){
              // 加载道面相关数据
              setTimeout(() => {
              }, 3000)
            }
            let modelInstance = res.data.modelInstance
            let layerId = res.data.parentId;
            let instanceType = modelInstance.instanceType
            let type = modelInstance.model.featureType
            let layer = this.getLayerByType(code, type, instanceType)
            this.addOverLay(layer, modelInstance, layerId, code)
          } catch (e) {
            console.log(e, res.data, '数据不对')
          }
        })
      }
    })
  }
  // 根据使用年限获取线的颜色 都是0-3年绿色 3-6橙色 >6 红色
  getPolylineColor (item) {
    const { Cesium } = DC.Workspace
    // launchDate
    if (item.modelInstanceAttrValues && item.modelInstanceAttrValues.length > 0) {
      let list = item.modelInstanceAttrValues
      let startDate = null
      for (let i = 0; i < list.length; i++) {
        let attr = list[i]
        if (attr.attrCode === 'launchDate') {
          startDate = attr.attrValue
          break
        }
      }
      let time = Date.parse(startDate)
      if (startDate !== null && isNaN(startDate)&&!isNaN(time)) {
        // 判断值对不对
        let compareTime = new Date().getTime() - time  // 时间差的毫秒数
        // 计算出相差天数
        let days = Math.floor(compareTime / (24 * 3600 * 1000))
        // 计算出相差年数
        let years = Math.floor(days / 365)
        if (years < 3) {
          return Cesium.Color.GREEN
        } else if (years >= 3 && years < 6) {
          return Cesium.Color.ORANGE
        } else {
          return Cesium.Color.RED
        }
      } else {
        return Cesium.Color.GREEN
      }
    } else {
      return Cesium.Color.GREEN
    }
  }
  addOverLay(layer, modelInstance, layerId, code) {
    // if(modelInstance.position === null) {
    //   return
    // }
    const { Cesium } = DC.Workspace
    let model = null
    let type = modelInstance.model.featureType
    let position = null
    // 判斷是否是人車
    let isPersonOrCar = false
    switch (type) {
      case 'point':
        position = DC.Position.fromString(modelInstance.position)
        model = new DC.Point(position)
        model.setStyle({
          'pixelSize': 10, //像素大小
          // 'heightReference': 0, //高度参照，0：位置无参照，位置是绝对的，1：位置固定在地形上 2：位置高度是指地形上方的高度。
          // 'color': DC.Color.WHITE, //颜色
          // 'outlineColor': DC.Color.WHITE, //边框颜色
          // 'outlineWidth': 0, //边框大小，
          'scaleByDistance': {
            'near': 1, //最近距离
            'nearValue': 0, //最近距离值
            'far': 1000, //最远距离值
            'farValue': 0 //最远距离值
          }, //根据距离设置比例
          // 'translucencyByDistance': {
          //   'near': 0, //最近距离
          //   'nearValue': 0, //最近距离值
          //   'far': 1, //最远距离值
          //   'farValue': 0 //最远距离值
          // }, //根据距离设置透明度
          // 'distanceDisplayCondition': {
          //   'near': 0, //最近距离
          //   'far': Number.MAX_VALUE //最远距离
          // }, //根据距离设置可见
          // 'disableDepthTestDistance': 0 // 深度检测距离，用于防止剪切地形，设置为零时，将始终应用深度测试。设置为Number.POSITIVE_INFINITY时，永远不会应用深度测试。
        })
        break;
      case 'polyline':
        return
        if (modelInstance.instanceType === 'wire3' || modelInstance.instanceType === 'wire4') {

          // 看具体的格式得
          let _position2 = JSON.parse(modelInstance.centerPosition)
          position = DC.Position.fromString(_position2.value._lng + ',' + _position2.value._lat)
          let postiontData2 = JSON.parse(modelInstance.position)
          let points2 = postiontData2.value
          // if (!this.wireManager) {
          //   this.wireManager = new DC.WireManager({})
          // }
          let points = []
          points2.forEach(pp => {
            let position = new DC.Position(pp._lng, pp._lat, pp._alt, pp._heading, pp._pitch, pp._roll)
            points.push(position)
          })
          let color = this.getPolylineColor(modelInstance)
          let type = 3
          if (modelInstance.instanceType === 'wire3') {
            type = 3
          } else {
            type = 4
          }
          model = new DC.WiresPrimitive(points, {
            type: type,
            style: {
              width: 2,
              color: color,
              distanceDisplayCondition: {near: 0, far: 1000}
            }
          })

          // let type = 3
          // let polylines = this.wireManager['createWire' + type + 'Lines'](points)
          // polylines.forEach(line => {
          //   // 还得根据不同时长更改颜色，查看属性里面的年限
          //   line.setStyle({
          //     'distanceDisplayCondition': {
          //       'near': 0, //最近距离
          //       'far': 500 //最远距离
          //     }
          //   })
          //   layer.addOverlay(line)
          // })
        } else {
          // 看具体的格式得
          let _position2 = JSON.parse(modelInstance.centerPosition)
          position = DC.Position.fromString(_position2.value._lng + ',' + _position2.value._lat)
          let postiontData2 = JSON.parse(modelInstance.position)
          let points2 = postiontData2.value
          let polylineStr = ''
          points2.forEach(item => {
            polylineStr += item._lng + ',' + item._lat + ';'
          })
          polylineStr = polylineStr.substring(0, polylineStr.length - 1)
          model = new DC.Polyline(polylineStr)
          let color = this.getPolylineColor(modelInstance)
          model.setStyle({
            'width': 5, // 线宽
            'material': color,  // 材质颜色
            'clampToGround': true, //是否贴地
            // 'shadows': 0, //阴影类型，0：禁用、1：启用 、2：投射、3：接受
            'distanceDisplayCondition': {
              'near': 0, //最近距离
              'far': 3000 //最远距离
            }, //根据距离设置可见
            'classificationType': 2, //分类 是否影响地形，3D切片或同时影响这两者。0:地形、1:3D切片、2：两者
            // 'zIndex': 0 //层级
          })
        }
        break;
      case 'polygon':
        // 看具体的格式得
        let _position = JSON.parse(modelInstance.centerPosition)
        position = DC.Position.fromString(_position.value._lng + ',' + _position.value._lat)
        let postiontData = JSON.parse(modelInstance.position)
        let points = (postiontData.value && postiontData.value instanceof Array) ? postiontData.value :
        (JSON.parse(postiontData.value).value && JSON.parse(postiontData.value).value instanceof Array) ? JSON.parse(postiontData.value).value : null
        if(!points) break
        let polygonStr = ''
        points.forEach(item => {
          polygonStr += item._lng + ',' + item._lat + ';'
        })
        polygonStr = polygonStr.substring(0, polygonStr.length - 1)
        model = new DC.Polygon(polygonStr)
        model.setStyle({
          // 'height': 0.05, //高度
          // 'heightReference': 0, //高度参照，0：位置无参照，位置是绝对的，1：位置固定在地形上 2：位置高度是指地形上方的高度。
          // 'extrudedHeight': 0, //拉升高度
          // 'stRotation': 0, //旋转角度
          // 'fill': true, //是否用提供的材料填充多边形。
          'material': modelInstance.model.materialUrl || DC.Color.WHITE, //材质
          // 'material': new Cesium.ImageMaterialProperty({
          //   image: modelInstance.model.materialUrl,
          //   color: Cesium.Color.NONE,
          //   // repeat : new Cesium.Cartesian2(4, 4)
          // }),
          // 'outline': false, //是否显示边框
          // 'outlineColor': DC.Color.BLACK, //边框颜色
          // 'outlineWidth': 0, //边框宽度
          // 'closeTop': true, //顶面是否闭合
          // 'closeBottom': true, //底面是否闭合
          // 'shadows': 0, //阴影类型，0：禁用、1：启用 、2：投射、3：接受
          // 'distanceDisplayCondition': {
          //   'near': 0, //最近距离
          //   'far': Number.MAX_VALUE //最远距离
          // }, //根据距离设置可见
          'classificationType': 2, //分类 是否影响地形，3D切片或同时影响这两者。0:地形、1:3D切片、2：两者
          'zIndex': 0 //层级
        })
        break;
      case 'model':
        // if (modelInstance.position === null) {
        //   position = new DC.Position(120.42893 + Math.random() * 0.05, 31.48081 + Math.random() * 0.05)
        // } else {
        //   let _position = JSON.parse(modelInstance.centerPosition)
        //   position = DC.Position.fromString(_position.value._lng + ',' + _position.value._lat)
        // }

        let _positionModel = JSON.parse(modelInstance.centerPosition)
        position = DC.Position.fromString(_positionModel.value._lng + ',' + _positionModel.value._lat + ',' + _positionModel.value._alt + ',' + _positionModel.value._heading + ',' + _positionModel.value._pitch + ',' + _positionModel.value._roll)
        // position = modelInstance.position || new DC.Position(120.42893 + Math.random() * 0.05, 31.48081 + Math.random() * 0.05)
        // model = new DC.Model(position, 'static/models/bird/birdA.gltf')
        // 人车 就是动态模型
        if ((modelInstance.model.modelTypeId === 8 || modelInstance.model.modelTypeId === 7) && this.hasBindingRealData(modelInstance)) {
            model = new DC.DynamicModel(position, modelInstance.model.modelUrl)
            // 为了后边标识挂牌竖线的图片，人车单独样式
            isPersonOrCar = true

        } else {
          if(modelInstance.model.modelTypeId === 8 || modelInstance.model.modelTypeId === 7){
            isPersonOrCar = true
          }
          model = new DC.Model(position, modelInstance.model.modelUrl)
        }
        //
        // model.maxCacheSize(2)
        model.setStyle({
          'scale': 1, //比例
          // 'minimumPixelSize': 0, //指定模型的最小像素大小，而不考虑缩放
          // 'maximumScale': 0, //指定模型的最大比例
          'heightReference': 1, //高度参照，0：位置无参照，位置是绝对的，1：位置固定在地形上 2：位置高度是指地形上方的高度。
          'shadows': 0, //阴影类型，0：禁用、1：启用 、2：投射、3：接受
          // 'silhouetteColor': DC.Color.WHITE, //轮廓颜色
          // 'silhouetteSize': 0, //轮廓宽度
          // 'lightColor': DC.Color.RED, //模型着色时指定灯光颜色
          'distanceDisplayCondition': {
            'near': 0, //最近距离
            'far': 3000 //最远距离
          }, //根据距离设置可见
          // 'scaleByDistance': {
          //   'near': 1, //最近距离
          //   'nearValue': 0, //最近距离值
          //   'far': 1000, //最远距离值
          //   'farValue': 0 //最远距离值
          // }, //根据距离设置比例
        })
        break;
      case '3dtiles':
      case 'tileset':
        position = modelInstance.position || new DC.Position(120.42893 + Math.random() * 0.5, 31.48081 + Math.random() * 0.5)
        model = new DC.Tileset(modelInstance.model.modelUrl)
        // model.setPosition(position)
        break;
      default:
        break;
    }
    if (model === null) {
      return
    }
    model.id = modelInstance.id
    // 修改attr下的centerposition和position属性
    let centerPosition, attrPosition
    if (modelInstance.centerPosition) {
      centerPosition = modelInstance.centerPosition
      centerPosition = JSON.parse(centerPosition)
      centerPosition = centerPosition.value
      modelInstance.centerPosition = centerPosition
    }

    if (modelInstance.position) {
      attrPosition = modelInstance.position
      attrPosition = JSON.parse(attrPosition)
      attrPosition = attrPosition.value
      modelInstance.position = attrPosition
    }

    model.attr = modelInstance
    if (isNaN(layerId)) {
      model.attr.layerId = layerId
    }
    layer.addOverlay(model)

    let height = this._defaultHeight // 默认给个模型高度 这块根据设置的labelposition
    if (modelInstance.labelPosition !== null && modelInstance.labelPosition !== undefined && Number(modelInstance.labelPosition) > 0) {
      height = height + Number(modelInstance.labelPosition)
    }
    model.labelHeight = height
    model.isPersonOrCar = isPersonOrCar
    model.ownCode = code
    // // 是否显示名称
    // if (modelInstance.showLabel === '1') {
    //   // 添加竖线
    //   this._setModelLabelBillboard(model, centerPosition._lng, centerPosition._lat)
    //   // 添加label
    //   this._setModelLabel(model, centerPosition._lng, centerPosition._lat)
    // }
    // 处理是否有预警信息
    this._handerMapModelInstanceDetail(modelInstance, 'init')


  }

  hasBindingRealData(modelInstance){
    if(!modelInstance || !modelInstance.modelInstanceAttrValues) return false
    let hasBinding = false
    if(modelInstance.modelInstanceAttrValues instanceof Array){
      modelInstance.modelInstanceAttrValues.forEach(attr => {
        if(attr.channelNo != null || attr.channelNo != undefined){
          hasBinding = true
        }
      })
    }
    return hasBinding
  }

  getLayerByType(code, type, instanceType) {
    if (instanceType === 'wire' || instanceType === 'wire3' || instanceType === 'wire4') {
      let layer = window.viewer.getLayer(code + '_' + type + '_wire')
      if (layer === undefined) {
        layer = new DC.PrimitiveLayer(code + '_' + type + '_wire')
        this._groupLayer.addLayer(layer)
      }
      return layer
    } else {
      let layer = window.viewer.getLayer(code + '_' + type)
      if (layer === undefined) {
        if (type === 'model') {
          layer = new DC.VectorLayer(code + '_' + type)
        } else if (type === 'tileset') {
          layer = new DC.TilesetLayer(code + '_' + type)
        } else if (type === '3dtiles') {
          layer = new DC.TilesetLayer(code + '_' + type)
        } else {
          layer = new DC.VectorLayer(code + '_' + type)
        }
        this._groupLayer.addLayer(layer)
        return layer
      }
      return layer || null
    }
  }
  _setModelLabel(model, lng, lat) {
    const {Cesium} = DC.Workspace
    let ps = new DC.Position(lng, lat, (this._billboard_h + model.labelHeight) / 2) // 这里有个高度 模型高加上图片高等于面板高 不精确
    let label = new DC.Label(ps, model.attr.name)
    let color = Cesium.Color.fromCssColorString ('#060E26')
    color.alpha = 0.5
    let color2 = Cesium.Color.fromCssColorString ('#00C3FF')
    label.setStyle({
      'font': '22px sans-serif', // CSS 字体设置
      // 'scale': 1, //比例
      // 'pixelOffset': { 'x': 0, 'y': 30 }, //偏移像素
      // 'heightReference': Cesium.HeightReference.RELATIVE_TO_GROUND, //高度参照，0：位置无参照，位置是绝对的，1：位置固定在地形上 2：位置高度是指地形上方的高度。
      // 'showBackground': true, //是否显示背景
      // 'backgroundColor': color, //背景颜色
      // 'backgroundPadding': { 'x': 15, 'y': 10}, //背景间隙
      'fillColor': DC.Color.CYAN, //文字颜色
      'outlineColor': color2, //边框颜色
      'outlineWidth': 2, //边框大小，
      eyeOffset:new Cesium.Cartesian3(0.0,-5,-1),
      verticalOrigin:Cesium.VerticalOrigin.BOTTOM,
      horizontalOrigin:Cesium.HorizontalOrigin.CENTER,
      'pixelOffsetScaleByDistance':{
        'near': 1.5e2, //最近距离
        'nearValue': 1.7, //最近距离值
        'far': 4.5e3, //最远距离值
        'farValue': 0 //最远距离值
      },
      pixelOffset:new Cesium.Cartesian2(0,-30),
      'scaleByDistance': {
        'near': 1.5e2, //最近距离
        'nearValue': 1.7, //最近距离值
        'far': 8.5e3, //最远距离值
        'farValue': 0.0 //最远距离值
      }, //根据距离设置比例
      // 'translucencyByDistance': {
      //   'near': 0, //最近距离
      //   'nearValue': 0, //最近距离值
      //   'far': 1, //最远距离值
      //   'farValue': 0 //最远距离值
      // }, //根据距离设置透明度
      // 'distanceDisplayCondition': {
      //   'near': 0, //最近距离
      //   'far': 3000 //最远距离
      // }, //根据距离设置可见
      // 'disableDepthTestDistance': 0 // 深度检测距离，用于防止剪切地形，设置为零时，将始终应用深度测试。设置为Number.POSITIVE_INFINITY时，永远不会应用深度测试。
    })

    this.labelLayer.addOverlay(label)
    // 把他绑定到 model上
    model.ownDivIcon = label
  }
  _getShuImage (model) {
    let bImg
    if (model.isPersonOrCar) {
      bImg = 'static/images/guapai/' + model.ownCode + '.png'
    } else {
      bImg = 'static/images/guapai/line.png'
    }
    return bImg
  }
  _setModelLabelBillboard (model, lng, lat) {
    const {Cesium} = DC.Workspace
    let positionBillBoard = new DC.Position(lng, lat, model.labelHeight - 5)
    let bImg = this._getShuImage(model)
    let billboard = new DC.Billboard(positionBillBoard, bImg)
    // 这个大小看看能不能统一
    billboard.size = [this._billboard_w, this._billboard_h]
    // billboard.size = [1, 1]
    billboard.setStyle({
      // 'heightReference': Cesium.HeightReference.RELATIVE_TO_GROUND, //高度参照，0：位置无参照，位置是绝对的，1：位置固定在地形上 2：位置高度是指地形上方的高度。
      'scale': 1, //比例
      // 'pixelOffset': { 'x': 0, 'y': -20 }, //偏移像素
      'width':12,
      'height':50,
      'eyeOffset':new Cesium.Cartesian3(0,5,0),
      // 'rotation': 0, //旋转角度
      // 'translucencyByDistance': {
      //   'near': 0, //最近距离
      //   'nearValue': 0, //最近距离值
      //   'far': 1, //最远距离值
      //   'farValue': 0 //最远距离值
      // }, //根据距离设置透明度
      'scaleByDistance': {
        'near': 1.5e2, //最近距离
        'nearValue': 1.7, //最近距离值
        'far': 8.5e3, //最远距离值
        'farValue': 0 //最远距离值
      }, //根据距离设置比例
      pixelOffset:new Cesium.Cartesian2(0.0,-10),
      pixelOffsetScaleByDistance:new Cesium.NearFarScalar(1.5e2,1.7,4.5e3,0)
      // 'distanceDisplayCondition': {
      //   'near': 0, //最近距离
      //   'far': 3000 //最远距离
      // }, //根据距离设置可见
      // 'disableDepthTestDistance': 0 // 深度检测距离，用于防止剪切地形，设置为零时，将始终应用深度测试。设置为Number.POSITIVE_INFINITY时，永远不会应用深度测试。
    })

    this.labelLayer.addOverlay(billboard)
    // 把他绑定到 model上
    model.ownBillboard = billboard
  }
  _setModelDivIcon (model, lng, lat, divStr, className , panelHeight = 80) {
    // 有预警的话删除名称挂牌或者实时挂牌
    if (model.ownDivIcon) {
      model.ownDivIcon.remove()
      model.ownDivIcon = null
    }
    let position = new DC.Position(lng, lat) // 利用面的绝对位置偏移
    let divIcon = new DC.DivIcon(
      position,
      divStr
    )
    divIcon.setStyle({
      'className': '' + className, //样式名
      'topChangeByDistance': {
        'modelHeight': model.labelHeight,
        'panelHeight': panelHeight,
      },
      // 'scaleByDistance': {
      //   'near': 1000, //最近距离
      //   'nearValue': 1, //最近距离值
      //   'far': 2000, //最远距离值
      //   'farValue': 0.5 //最远距离值
      // }, //根据距离设置比例
      'distanceDisplayCondition': {
        'near': 0, //最近距离
        'far': 4000 //最远距离
      } //根据距离设置可见
    })
    this.htmlPanelLayer.addOverlay(divIcon)
    model.ownDivIcon = divIcon
    return divIcon
  }
  // 根据ID获取某个实例的对象组
  getOverlaysById(modelId) {
    let overlays = []
    let layers = this._groupLayer.getLayers()
    layers.forEach((layer) => {
      layer.eachOverlay((item) => {
        // 这块类型要扩展判断
        if (item.id === modelId && item.show) {
          overlays.push(item)
        }
      })
    })
    return overlays
  }
  // 模型实例ID ,第三视角追踪
  stareAtModel (data) {
    let modelId = data.modelId
    // 之前的先清除掉
    let models = layersManager.getOverlaysById(modelId)
    if (models.length > 0) {
      window.viewer.trackedEntity = models[0]._delegate
    }
  }
  unStareAtModel () {
    window.viewer.trackedEntity = null
  }
  playHistoryPath (data) {

  }
  // 根据模型类型选择模型
  getOverlaysByModelType(modelTypeId, code) {
    let overlays = []
    if (code === 'all') {
      let layers = this._groupLayer.getLayers()
      layers.forEach((layer) => {
        layer.eachOverlay((item) => {
          // 这块类型要扩展判断
          if (item.attr && item.attr.model && item.attr.model.modelTypeId === modelTypeId && item.show) {
            overlays.push(item)
          }
        })
      })
    } else {
      let layers = this._groupLayer.getLayers()
      layers.forEach((layer) => {
        layer.eachOverlay((item) => {
          // 这块类型要扩展判断
          if (item.attr && item.attr.model && item.attr.model.modelTypeId === modelTypeId && item.show && item.ownCode === code) {
            overlays.push(item)
          }
        })
      })
    }

    return overlays
  }
  // 获取所有的覆盖物
  getAllOverlays() {
    let overlays = []
    let layers = this._groupLayer.getLayers()
    layers.forEach((layer) => {
      layer.eachOverlay((item) => {
        overlays.push(item)
      })
    })
    return overlays
  }
  // 只显示某一个组的数据
  showLayerGroup(code) {
    if (code === 'all') {
      let layers = this._groupLayer.getLayers()
      layers.forEach((layer) => {
        layer.eachOverlay((item) => {
          item.show = true
          this._handerMapModelInstanceDetail(item.attr, 'init')
        })
      })
    } else {
      let layers = this._groupLayer.getLayers()
      layers.forEach((layer) => {
        layer.eachOverlay((item) => {
          // 属于当前组图层可见
          if (layer.id.indexOf(code) > -1) {
            item.show = true
            this._handerMapModelInstanceDetail(item.attr, 'init')
          } else {
            // 不属于当前组但是是驻留的
            if (item.attr.stay === '1') {
              item.show = true
              this._handerMapModelInstanceDetail(item.attr, 'init')
            } else {
              item.show = false
              if (item.ownDivIcon) {
                item.ownDivIcon.remove()
                item.ownDivIcon = null
              }
              if (item.ownBillboard) {
                item.ownBillboard.remove()
                item.ownBillboard = null
              }
            }
          }
        })
      })
    }
  }

  highligtModel(curMouseMove) {
    // if (this.curMouseMove) {
    //   this.curMouseMove.delegate.model.silhouetteColor = null
    //   this.curMouseMove.delegate.model.silhouetteSize = null
    //   this.curMouseMove = null
    // }
    // const {Cesium} = DC.Workspace
    // curMouseMove.delegate.model.silhouetteColor = new Cesium.Color(1.0, 0, 0, 1.0)
    // curMouseMove.delegate.model.silhouetteSize = 3.0
    // this.curMouseMove = curMouseMove
    if (this.curMouseMove && this.curMouseMove.id === curMouseMove.id) {
      return
    }
    if (this.curMouseMove) {
      this.dehightlightModel()
    }
    this.curMouseMove = curMouseMove
    this._highlight(curMouseMove)
  }
  _highlight(target) {
    DC.HighLightUtil.highlight(target)
  }
  // 控制不高亮某个模型
  dehightlightModel() {
    // if (this.curMouseMove) {
    //   this.curMouseMove.delegate.model.silhouetteColor = null
    //   this.curMouseMove.delegate.model.silhouetteSize = null
    //   this.curMouseMove = null
    // }
    if (this.curMouseMove) {
      let isDehightLight = this._deHighlight(this.curMouseMove)
      if(isDehightLight){
        this.curMouseMove = null
      }
    }
  }
  _deHighlight(target) {
    return DC.HighLightUtil.deHighlight(target)
  }
  // 清除所有模型高亮
  clearModelsHighlight() {

  }
  _mouseShowLabelHander(e) {
    // console.log(e, 'mousemove')
    let curMouseMove = e.overlay
    if (curMouseMove !== undefined && curMouseMove.attr.showLabel === '0' && curMouseMove._show) { // 只有0 的才鼠标显示名称
      // if (curMouseMove !== undefined && curMouseMove.attr.showLabel === '1') { // 只有0 的才鼠标显示名称
      if (this.curLabel !== null) {
        this.htmlLayer.removeOverlay(this.curLabel)
        this.curLabel = null
      }
      let position = DC.Transform.transformCartesianToWGS84(e.position)
      this.curLabel = new DC.DivIcon(position, '<div>' + curMouseMove.attr.name + '</div>')
      this.curLabel.setStyle({
        'className': 'moveLabelName', //样式名
        // 'scaleByDistance': {
        //   'near': 0, //最近距离
        //   'nearValue': 0, //最近距离值
        //   'far': 1, //最远距离值
        //   'farValue': 0 //最远距离值
        // }, //根据距离设置比例
        'distanceDisplayCondition': {
          'near': 0, //最近距离
          'far': 10000 //最远距离
        } //根据距离设置可见
      })
      this.htmlLayer.addOverlay(this.curLabel)
    }
    if (curMouseMove === undefined) {
      if (this.curLabel !== null && this.htmlLayer) {
        this.htmlLayer.removeOverlay(this.curLabel)
        this.curLabel = null
      }
    }
    // 高亮
    if (curMouseMove !== undefined && curMouseMove._show) {
      console.log(e, 'mousemove')
      this.highligtModel(curMouseMove)
    }

    if (curMouseMove === undefined) {
      this.dehightlightModel()
    }
  }
  // 激活左键点击事件
  activityMouseClick() {
    window.viewer.on(DC.MouseEventType.CLICK, this._mouseClickHandler, this)
  }
  deactivityMouseClick() {
    window.viewer.off(DC.MouseEventType.CLICK, this._mouseClickHandler, this)
  }
  _mouseClickHandler(e) {
    let curModel = e.overlay
    if (curModel !== undefined && curModel.attr) {
      // 判断是不是本图层的
      if (this.getOverlaysById(curModel.attr.id).length > 0) {
        // 只有是89 才是相机
        if (curModel.attr.model.modelTypeId !== 89) {
          return
        }
        let position = DC.Transform.transformCartesianToWGS84(e.position)
        if (this._mouseClickPanel) {
          this._mouseClickPanel.remove()
          this._mouseClickPanel = null
          this.htmlPanelComp.$destroy()
        }
        Promise.all([
          import('../components/modelInfo/videoInfo.vue'),
        ]).then(([{ default: videoInfo }]) => {
          this._mouseClickPanel = new DC.DivIcon(position, '<div id="videoInfoPanel" style="height: 100%;width: 100%"></div>')
          this._mouseClickPanel.setStyle({
            'className': 'videoInfoPanel', //样式名
            // 'scaleByDistance': {
            //   'near': 0, //最近距离
            //   'nearValue': 0, //最近距离值
            //   'far': 1, //最远距离值
            //   'farValue': 0 //最远距离值
            // }, //根据距离设置比例
            'distanceDisplayCondition': {
              'near': 0, //最近距离
              'far': 10000 //最远距离
            } //根据距离设置可见
          })
          this.htmlPanelLayer.addOverlay(this._mouseClickPanel)

          let comp = new global.Vue({
            render: h => h(videoInfo)
          }).$mount('#videoInfoPanel')
          this.htmlPanelComp = comp.$children[0]
          this.htmlPanelComp.reload(curModel.attr.id)
        }
        )
      }
    } else {
      if (this._mouseClickPanel) {
        this._mouseClickPanel.remove()
        this._mouseClickPanel = null
        this.htmlPanelComp.$destroy()
      }
    }
  }
  // 激活鼠标滑动显示label
  activityMouseShowLabel() {
    // 已经默认挂牌的不需要滑动在显示label了
    // mouseMoveShowLable
    if (layersManager.activityMouse) return
    window.viewer.on(DC.MouseEventType.MOUSE_MOVE, this._mouseShowLabelHander, this)
    layersManager.activityMouse = true
  }
  // 禁止鼠标滑动显示label
  deactivityMouseShowLabel() {
    if (!layersManager.activityMouse) return
    window.viewer.off(DC.MouseEventType.MOUSE_MOVE, this._mouseShowLabelHander, this)
    layersManager.activityMouse = false
  }
  _doubleClickShoInfoHander(e) {
    // 是否需要还原之前的模型挂牌
    if (this._curModel) {
      this._handerMapModelInstanceDetail(this._curModel.attr, 'init')
      this._curModel = null
    }
    let curModel = e.overlay
    if (curModel !== undefined && curModel.attr) {
      this._curModel = curModel
      // 判断是不是本图层的
      if (this.getOverlaysById(curModel.id).length > 0) {
        // attr不为空显示
        if (this._doubleClickPanel) {
          this._doubleClickPanel.remove()
          this._doubleClickPanel = null
          this.htmlPanelComp.$destroy()
        }
        Promise.all([
          import('../components/modelInfo/index.vue'),
        ]).then(([{ default: ModelInfo }]) => {
          if (curModel.ownBillboard) {
            curModel.ownBillboard.remove()
            curModel.ownBillboard = null
          }
          let divStr =
            '<div>' +
              '<div class="modelInfoPanel">' +
                '<div style="height: 100%;width: 100%" id="modelInfoPanel" ></div>' +
              '</div>' +
              '<div style="padding-bottom: 2px;width: 100%;height: 50px;text-align: center">' +
                '<img style="height: 95%" src="static/images/guapai/line.png"/>' +
              '</div>' +
            '</div>'
          let center = curModel.attr.centerPosition
          if(typeof center === 'string'){
            if(isJSON(center)){
              center = JSON.parse(center)
              if(center.value){
                center = center.value
              }
            }
          }else if(center && typeof center == 'object'){
            if(center.value){
              center = center.value
            }
          }
          this._doubleClickPanel = this._setModelDivIcon(curModel, center._lng || center.lng, center._lat || center.lat, divStr, '', 374)
          let comp = new global.Vue({
            render: h => h(ModelInfo)
          }).$mount('#modelInfoPanel')
          this.htmlPanelComp = comp.$children[0]
          this.htmlPanelComp.reload(curModel.id)
        }
        )
      }
    } else {
      if (this._doubleClickPanel) {
        this._doubleClickPanel.remove()
        this._doubleClickPanel = null
        this.htmlPanelComp.$destroy()
        // 是否需要还原之前的模型挂牌
        if (this._curModel) {
          this._handerMapModelInstanceDetail(this._curModel.attr, 'init')
          this._curModel = null
        }
      }
    }
  }
  // 展示历史轨迹线
  showHistoryPath (data) {
    let list = data.list
    let positions = []
    list.forEach(item => {
      positions.push(new DC.Position(item.lng, item.lat))
    })
    if (layersManager.historyLayer) {
      layersManager.historyLayer.clear()
      let polyline = new DC.Polyline(positions)
      polyline.setStyle({
        width: 3,
        clampToGround: true
      })
      layersManager.historyLayer.addOverlay(polyline)
      window.viewer.flyTo(layersManager.historyLayer)
    } else {
      layersManager.historyLayer = new DC.VectorLayer('historyLayer')
      // let trailLinePrimitive = new DC.TrailLinePrimitive(positions)
      let polyline = new DC.Polyline(positions)
      polyline.setStyle({
        width: 3,
        clampToGround: true
      })
      layersManager.historyLayer.addOverlay(polyline)
      window.viewer.addLayer(layersManager.historyLayer)
      window.viewer.flyTo(layersManager.historyLayer)
    }
  }
  // 接收数据展示热力图
  showHeatLayer(list) {
    // {
    //   "id": "1",
    //   "createTime": "2020-02-02 02:02:02",
    //   "fbodyAmount": 20,
    //   "fbodyType": "垃圾" ,
    //   "lng": 120.111,
    //   "lat": 87.221,
    //   "warehouseId": "121212",
    //   "fbodyLevel": 2,
    // }
    let positions = []
    list.forEach(item => {
      positions.push(new DC.Position(item.lng, item.lat))
    })
    if (layersManager.heatLayer) {
      layersManager.heatLayer.setPositions(positions)
    } else {
      layersManager.heatLayer = new DC.HeatLayer('HeatLayer', { height: 5 })
      window.viewer.addLayer(layersManager.heatLayer)
      layersManager.heatLayer.setPositions(positions)
      // window.viewer.flyTo(this.heatLayer)
    }
  }
  // 隐藏数据图
  hideHeatLayer() {
    if (layersManager.heatLayer) {
      layersManager.heatLayer.remove()
      layersManager.heatLayer = null
    }
  }
  // 隐藏数据图
  hideHistoryPath() {
    if (layersManager.historyLayer) {
      layersManager.historyLayer.remove()
      layersManager.historyLayer = null
    }
  }
  // 处理socket相关内容
  handlerSocketMessages(data) {
    let type = data.messageType
    console.log(type, 'handlerSocketMessages-type')
    console.log(data.data, 'handlerSocketMessages-data')
    switch (type) {
    }
  }
  // 处理当前模型是否报警以及，报警是否消失
  _handerMapModelInstanceDetail(item, type) {
    if (item.id === 671) {

    }

    if (item.alarmInfoList && item.alarmInfoList.length > 0) {
      let warnItems1 = item.alarmInfoList.filter(item => {
        return item.alarmType === 'FRAME_ALARM'
      })
      let warnItems2 = item.alarmInfoList.filter(item => {
        return item.alarmType === 'VOICE_ALARM'
      })
      let warnItems3 = item.alarmInfoList.filter(item => {
        return item.alarmType === 'RIPPLE_ALARM'
      })
      // 获取最新一个是弹框报警的
      if (warnItems1 && warnItems1.length > 0) {
        let warnItem = warnItems1[0]
        this._showWarnHtml(item, warnItem)
      } else {
        // 没有弹框预警那么看看有没有实时面板
        if (item.modelInstanceAttrValues && item.modelInstanceAttrValues.length > 0) {
          this._showRealDataHtml(item, type)
        }
      }
      if (warnItems2 && warnItems2.length > 0) {
        // 调用外部接口 播放预警声音
        window.EventBus.$emit('alarmWarn', {})
      }
      if (warnItems3 && warnItems3.length > 0) {
        // 水波纹效果
        this._showWarnEffect(item, warnItems3[0])
      }
    } else {
      // 既然推送了那么就是有预警或者有实时数据，上边处理了预警了
      if (item.modelInstanceAttrValues && item.modelInstanceAttrValues.length > 0) {
        this._showRealDataHtml(item, type)
      } else {
        // 是否有名称挂牌
        let overlays = this.getOverlaysById(item.id)
        if (overlays.length > 0) {
          let item2 = overlays[0]
          if (item2.ownDivIcon) {
            item2.ownDivIcon.remove()
            item2.ownDivIcon = null
          }
          if (item2.ownBillboard) {
            item2.ownBillboard.remove()
            item2.ownBillboard = null
          }
          if (item2.attr.showLabel === '1') {
            // 创建竖线了就得
            this._setModelLabelBillboard(item2, item2.attr.centerPosition._lng, item2.attr.centerPosition._lat)
            // 创建名称挂牌
            this._setModelLabel(item2, item2.attr.centerPosition._lng, item2.attr.centerPosition._lat)
          }
        }
      }
    }
    // // 位置更更新
    // if (type === 'socket' && item.longitude && item.latitude) {
    //   this._changePosition(item)
    // }
  }
  _changePosition(modelInstance) {
    const { Cesium } = DC.Workspace
    // let overlays = this.getOverlaysById(347)
    let overlays = this.getOverlaysById(modelInstance.id)
    let that = this
    overlays.forEach(item => {
      if (item.type === 'model' || item.type === 'dynamic-model') { // div_icon
        if (item.isRuning) return
        item.isRuning = true
        // this._highlight(item)
        let newPosition = new DC.Position(modelInstance.longitude || modelInstance.lng, modelInstance.latitude || modelInstance.lat, 0)
        // item.addPosition(item.position.copy(), 0)
        item.addPosition(newPosition, that._carPositionIntervalTime)



        // let _samplePosition = new Cesium.SampledPositionProperty()
        // _samplePosition.forwardExtrapolationType = Cesium.ExtrapolationType.HOLD
        // let p1 = DC.Parse.parsePosition(item.position)
        // let startTime = Cesium.JulianDate.now()
        // _samplePosition.addSample(
        //   startTime,
        //   DC.Transform.transformWGS84ToCartesian(p1)
        // )
        // item.delegate.position = _samplePosition
        // item.delegate.orientation = new Cesium.VelocityOrientationProperty(_samplePosition)
        // let newTime = Cesium.JulianDate.now()
        // Cesium.JulianDate.addSeconds(startTime, 10, newTime)
        // let time = Cesium.JulianDate.addSeconds(
        //   newTime,
        //   10,
        //   new Cesium.JulianDate()
        // )
        // _samplePosition.addSample(
        //   time,
        //   DC.Transform.transformWGS84ToCartesian((newPosition))
        // )
        // window.viewer.flyToPosition( item.position )
        overlays.forEach(item2 => {

          if (item2.type === 'div_icon' || item2.type === 'label') { // div_icon
            // item2.position = new DC.Workspace.Cesium.CallbackProperty((time, result) => {
            //   return item.position.copy()
            // }, false)
            // item2.position = new DC.CallbackProperty((time) => {
            //   return item.position.copy()
            // })

            function changePP() {
              item2.position = item.position
              // item2.position = item.position.copy()
              // item2.position = DC.Transform.transformCartesianToWGS84(
              //   _samplePosition.getValue(Cesium.JulianDate.now())
              // )
            }
            window.viewer.on(DC.SceneEventType.POST_RENDER, changePP)
            setTimeout(() => {
              // item2.position = item.position.copy()
              window.viewer.off(DC.SceneEventType.POST_RENDER, changePP)
            }, (that._carPositionIntervalTime+0.01) * 1000)
          }
        })
        setTimeout(() => {
          item.isRuning = false
          delete item.isRuning
        }, (that._carPositionIntervalTime+0.01) * 1000)
      } else {
      }
    })
  }
  // 跑道占用状态
  _roadOccupyHander(data) {
    let road = this.getOverlaysById(Number(data.roadId))
    const { Cesium } = DC.Workspace
    road.forEach(() => {
      let value = data.status
      let color
      if (value === 1) {
        color = Cesium.Color.fromCssColorString('#c50000')
      } else if (value === 2) {
        color = Cesium.Color.fromCssColorString('#da8400')
      } else {
        color = Cesium.Color.fromCssColorString('#0083bd')
      }
      road.setStyle({
        'material': color
      })
    })


  }
  // 展示实时挂牌
  _showRealDataHtml(modelInstance, type) {
    let overlays = this.getOverlaysById(modelInstance.id)
    if (overlays.length > 0) {
      let item = overlays[0]
      let contentStr = ''
      // 解析试试数据
      let modelInstanceAttrValues = []
      if (type === 'socket') {
        // 全部都实时的
        modelInstanceAttrValues = modelInstance.modelInstanceAttrValues
      } else {
        // try {
        //   modelInstanceAttrValues = modelInstance.modelInstanceAttrValues.filter(attr => attr.formField && attr.formField.dynamicFlag === '1')
        // } catch (e) {}
      }
      if (item.ownBillboard) {
        item.ownBillboard.remove()
        item.ownBillboard = null
      }
      if (item.ownDivIcon) {
        item.ownDivIcon.remove()
        item.ownDivIcon = null
      }
      if (modelInstanceAttrValues.length > 0) {
        modelInstanceAttrValues.forEach(attr => {
          contentStr += '<div style="width: 100%;height:20px;line-height: 20px;">' + attr.attrName + ':' + attr.attrValue + '</div>'
        })
        let bImg = this._getShuImage(item)
        let divStr =
          '<div >' +
            '<div class="realDataInfoPanel">' +
              '<div style="color: #00C3FF;padding-bottom: 2px;width: 100%">' + item.attr.name + '</div>' + contentStr +
            '</div>' +
            '<div style="color: #00C3FF;padding-bottom: 2px;width: 100%;height: 50px;text-align: center">' +
              '<img style="height: 95%" src="' + bImg + '"/>' +
            '</div>' +
          '</div>'
        // 竖线50  ，内容 ，名称加边框padding 23+20
        let panelHeight = 50 + (modelInstanceAttrValues.length * 20) + 43
        this._setModelDivIcon(item, item.attr.centerPosition._lng, item.attr.centerPosition._lat, divStr, '', panelHeight)
      } else {
        if (item.attr.showLabel === '1') {
          // 创建竖线了就得
          this._setModelLabelBillboard(item, item.attr.centerPosition._lng, item.attr.centerPosition._lat)
          // 创建名称挂牌
          this._setModelLabel(item, item.attr.centerPosition._lng, item.attr.centerPosition._lat)
        }
      }
    }
  }

  // 展示预警弹框
  _showWarnHtml(modelInstance, warnItem) {
    let overlays = this.getOverlaysById(modelInstance.id)
    if (overlays.length > 0) {
      let item = overlays[0]
      if (item.ownBillboard) {
        item.ownBillboard.remove()
        item.ownBillboard = null
      }
      let bImg = this._getShuImage(item)
      let divStr =
        '<div >' +
        '<div class="warnPanel">' +
        '<div style="color: #ff003b;padding-bottom: 2px;width: 100%">' + item.attr.name + '</div>' + warnItem.alarmContent +
        '</div>' +
        '<div style="padding-bottom: 2px;width: 100%;height: 50px;text-align: center">' +
        '<img style="height: 95%" src="' + bImg + '"/>' +
        '</div>' +
        '</div>'
      // 竖线50  ，内容 ，名称加边框padding 23+20
      let panelHeight = 50 + 20 + 43
      this._setModelDivIcon(item, item.attr.centerPosition._lng, item.attr.centerPosition._lat, divStr, '', panelHeight)
    }
  }
  _showWarnEffect(model) {
    let overlays = this.getOverlaysById(model.id)
    let position = null
    overlays.forEach(item => {
      if (item.type === 'model' || item.type === 'dynamic-model') {
        // 除了div_icon 还得加竖线billboard
        position = item.position
        let circleScan = new DC.CircleScan(window.viewer, position, 100, {
          speed: 30
        })
        circleScan.start()
        // window.viewer.flyToPosition(position)
        setTimeout(() => {
          circleScan.stop()
          circleScan = null
        }, 1000 * 15)
      }
    })
  }

  // 显示闪烁
  showflashModels (data) {
    let code = data.code // 所属专业
    let modelTypeId = data.modelTypeId // 所属模型类型  7 人  8 车
    let models = layersManager.getOverlaysByModelType(modelTypeId, code)
    layersManager.hideflashModels()
    layersManager.flashModels = []
    models.forEach(model=>{
      let position = model.position
      let circleScan = new DC.CircleScan(window.viewer, position, 50, {
        color: DC.Color.GREEN, // 颜色
        speed: 30
      })
      circleScan.start()
      layersManager.flashModels.push(circleScan)
    })
  }
  // 隐藏闪烁
  hideflashModels () {
    if (layersManager.flashModels) {
      layersManager.flashModels.forEach(flash => {
        flash.stop()
      })
      layersManager.flashModels = []
    }
  }
}

const layersManager = new LayersManager()
export default layersManager
