/**
 * @Author: dlw
 * @Date: 2023-10-30 21:11:09
 */
// 位置管理器
class PositionManager {
  constructor() {
  }
  // computeCirclularFlight:function(lon, lat){
  //   //Set bounds of our simulation time
  //   let start = Cesium.JulianDate.fromDate(new Date(2015, 2, 25, 16));
  //   let stop = Cesium.JulianDate.addSeconds(start, 360, new Cesium.JulianDate());
  //   let property = new Cesium.SampledPositionProperty();
  //   let ss = 0;
  //   for (let i=0 ;i<this.location.length;i++) {
  //     let time = Cesium.JulianDate.addSeconds(start, ss, new Cesium.JulianDate());
  //     let position = Cesium.Cartesian3.fromDegrees(this.location[i].lgtd,this.location[i].lttd,5);
  //     property.addSample(time, position);
  //     ss+=90;
  //   }
  //   return property;
  // },
  // 传入开始点和结束点
  computePosition (sPosition, ePosition) {
    // Set bounds of our simulation time
    const {Cesium} = DC.Workspace
    let start = Cesium.JulianDate.fromDate(new Date(2015, 2, 25, 16))
    let stop = Cesium.JulianDate.addSeconds(start, 30, new Cesium.JulianDate())
    let property = new Cesium.SampledPositionProperty()
    property.addSample(stop, ePosition)
    return property;
  }
  // 跑到某个位置
  runToPosition (overlay, ePosition) {
    const {Cesium} = DC.Workspace
    // let start = Cesium.JulianDate.fromDate(new Date(2015, 2, 25, 16))
    // let stop = Cesium.JulianDate.addSeconds(start, 30, new Cesium.JulianDate())
    // window.viewer.clock.startTime = start.clone()
    // window.viewer.clock.stopTime = stop.clone()
    // window.viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP // Loop at the end
    // window.viewer.clock.multiplier = 10
    //
    // let newPosition = this.computePosition({},position)
    //
    // let entity = this.map.entities.add({
    //
    //   //Set the entity availability to the same interval as the simulation time.
    //   //Set the entity availability to the same interval as the simulation time.
    //   availability : new Cesium.TimeIntervalCollection([new Cesium.TimeInterval({
    //     start : start,
    //     stop : stop
    //   })]),
    //
    //   // Use our computed positions
    //   position : position,
    //   // Automatically compute orientation based on position movement.
    //   orientation : new Cesium.VelocityOrientationProperty(position),
    //   // Load the Cesium plane model to represent the entity
    //   model : {
    //     //uri : './model/CesiumMilkTruck/CesiumMilkTruck.bgltf',
    //     uri : './model/youzhengche/youzhengche.gltf',
    //     minimumPixelSize : 64
    //   },
    //   //Show the path as a pink line sampled in 1 second increments.
    //   path : {
    //     resolution : 1,
    //     material : new Cesium.PolylineGlowMaterialProperty({
    //       glowPower : 0.1,
    //       color : Cesium.Color.YELLOW
    //     }),
    //     width : 10
    //   }
    // })
    //
    // entity.position.setInterpolationOptions({
    //   interpolationDegree : 2,
    //   //interpolationAlgorithm : Cesium.HermitePolynomialApproximation
    //   //interpolationAlgorithm : Cesium.LagrangePolynomialApproximation
    //   interpolationAlgorithm : Cesium.LinearApproximation
    //
    // })

    // 小车旋转角度
    let radian = Cesium.Math.toRadians(3.0)
    // 小车的速度
    let speed = 60
    // 速度矢量
    let speedVector = new Cesium.Cartesian3()
    let scene = window.viewer.scene
    // 起始位置
    let position = Cesium.Cartesian3.fromDegrees(116.080591, 39.919141, 0)
    // 用于设置小车方向
    let hpRoll = new Cesium.HeadingPitchRoll()
    let fixedFrameTransforms = Cesium.Transforms.localFrameToFixedFrameGenerator('north', 'west')
  }
}

const positionManager = new PositionManager()
export default positionManager
