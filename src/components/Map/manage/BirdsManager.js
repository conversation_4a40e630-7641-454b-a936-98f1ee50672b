/**
 * @Author: dlw
 * @Date: 2023-10-30 21:11:09
 */
// 鸟类对象管理，动画播放等控制
class BirdsManager {
  constructor() {
    // 所有鸟模型元素
    this._birds = []
    // 动态鸟类图层
    this._layer = null
    // 飞行方向
    this._birdTrack = null
    // 是否正在飞
    this._flying = false
  }
  //
  addBirds (data) {
    //   'data': {
    //     'amount': 11 ,//数量
    // 'lng': 120.42893,
    //   'lat': 31.48081,
    //     'height': 250.12 ,
    //     'direction': '东北',//八个方向
    //     'interval': 200.00,//200米
    //     'birdTrack': 1,//1.西往东2.东往西3.南往北4.北往南
    //     'isRisk': 1,//是否高危0.否1.是
    //     'modelUrl': 'http://xx.xx.xx/aaaa.glf',
    //     'birdType': '麻雀'
    //   }

    //
    if (this._flying) return
    this._flying = true
    this._birdTrack = data.birdTrack
    // 初始化过后没结束不接收新的
    if (this._layer !== null) return
    this._layer = new DC.DynamicLayer('birds-layer').addTo(window.viewer)
    // let position = new DC.Position(120.42893, 31.48081, 100)
    // let model = new DC.DynamicModel(position, 'static/models/bird/bird.glb')
    // model.setStyle({
    //   scale: 10,
    //   'silhouetteColor': DC.Color.RED, //轮廓颜色
    //   'silhouetteSize': 5, //轮廓宽度
    // })
    // this._layer.addOverlay(model)
    // data.amount = 5
    for (let i = 0; i < data.amount; i++){
      let model = new DC.DynamicModel(this._generatePosition({lng: data.lng + Math.random() * 0.01, lat: data.lat + Math.random() * 0.01},data.height,1)[0],
        data.modelUrl)
        // 'static/models/bird/birdA.gltf')
      model.setStyle({
        // scale:4,
        scale:400,
        // 'minimumPixelSize': 0, //指定模型的最小像素大小，而不考虑缩放
        // 'maximumScale': 0, //指定模型的最大比例
        // 'heightReference': 1, //高度参照，0：位置无参照，位置是绝对的，1：位置固定在地形上 2：位置高度是指地形上方的高度。
        // 'shadows': 0, //阴影类型，0：禁用、1：启用 、2：投射、3：接受
        'silhouetteColor': DC.Color.YELLOW, //轮廓颜色
        'silhouetteSize': 2, //轮廓宽度
        // 'lightColor': DC.Color.RED, //模型着色时指定灯光颜色
        // 'distanceDisplayCondition': {
        //   'near': 0, //最近距离
        //   'far': Number.MAX_VALUE //最远距离
        // } //根据距离设置可见
      })
      // model.delegate.model.color = new Cesium.Color(1.0, 0, 0, 1.0)
      this._layer.addOverlay(model)
    }
    window.viewer.flyTo(this._layer)
    let timer = setInterval(()=>{
      this._layer.getOverlays().forEach(item=>{
        let position = item.position
        item.addPosition(this._generatePosition2(position,data.height,1)[0],2)
      })
    },2000)
    // 15秒后结束
    setTimeout(()=>{
      clearInterval(timer)
      this._layer.clear()
      this._layer.remove()
      this._layer = null
      this._flying = false
    },1000 * 25)
  }
  _generatePosition(center, height, num) {
    let list = []
    for (let i = 0; i < num; i++) {
      // let lng = center.lng + Math.random() * 0.01
      // let lat = center.lat + Math.random() * 0.01
      let lng = center.lng + 0.01
      let lat = center.lat + 0.01
      let position = new DC.Position(lng, lat, height)
      list.push(position)
    }
    return list
  }
  _generatePosition2(center, height, num) {
    //
    let list = []
    for (let i = 0; i < num; i++) {
      // let lng = center.lng + Math.random() * 0.01
      // let lat = center.lat + Math.random() * 0.01
      if (this._birdTrack === 1) {
        let lng = center.lng + 0.01
        let lat = center.lat
        let position = new DC.Position(lng, lat, height)
        list.push(position)
      } else if (this._birdTrack === 2) {
        let lng = center.lng - 0.01
        let lat = center.lat
        let position = new DC.Position(lng, lat, height)
        list.push(position)
      } else if (this._birdTrack === 3) {
        let lng = center.lng
        let lat = center.lat + 0.01
        let position = new DC.Position(lng, lat, height)
        list.push(position)
      } else {
        let lng = center.lng
        let lat = center.lat - 0.01
        let position = new DC.Position(lng, lat, height)
        list.push(position)
      }
    }
    return list
  }
  // 开启动画
  play () {

  }
  // 停止动画
  stop () {

  }
  // 清除所有动画，以及数据
  clear () {

  }
}

const birdsManager = new BirdsManager()
export default birdsManager
