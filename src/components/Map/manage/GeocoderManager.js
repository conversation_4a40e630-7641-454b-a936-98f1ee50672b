/**
 * @Author: dlw
 * @Date: 2023-10-30 21:11:09
 */
class GeocoderManager{
    constructor(url,token){
        if(!url) {
            throw new Error('url is required')
        }
        this._url = url
        this._token = token
    }

    geocode(query){
        let {Cesium} = DC.Workspace
        let  queryParameters ={modelInstanceName:query}
        const resource = new Cesium.Resource({
            url:this._url,
            headers:{
                'Authorization':this._token,
                'content-type':'application/json'
            }
        })
        return resource.post(JSON.stringify(queryParameters)).then((results)=>{
            results = JSON.parse(results)
            let res = []
            if(results.rows&&results.rows.length>0){
                    results.rows.reduce((total,current)=>{
                        if(current.longitude&&current.latitude){
                            const obj={
                                displayName: current.name,
                                destination: Cesium.Cartesian3.fromDegrees(current.longitude,current.latitude,-400)
                            }
                            total.push(obj)
                        }
                        return total
                    },res)
                }
                return res
            }
        ).otherwise((error)=>{
            console.log('search is filed,please check relevant configuration services')
            return []
            // console.log(error)
        })
    }
}

export default GeocoderManager
