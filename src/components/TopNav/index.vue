<template>
  <el-menu
    :default-active="activeMenu"
    mode="horizontal"
    @select="handleSelect"
    :ellipsis="false"
    class="topmenu-container"
  >
    <template v-for="(item, index) in topMenus">
      <el-menu-item
        :index="item.path"
        :key="index"
        v-if="index < visibleNumber"
      >
        <svg-icon :icon-class="item.meta.icon" />
        {{ item.meta.title }}
      </el-menu-item>
    </template>

    <!-- 顶部菜单超出数量折叠 -->
    <el-sub-menu
      index="more"
      v-if="topMenus.length > visibleNumber"
      popper-class="more-menu"
    >
      <template #title>更多菜单</template>
      <template v-for="(item, index) in topMenus">
        <el-menu-item
          :index="item.path"
          :key="index"
          v-if="index >= visibleNumber"
        >
          <svg-icon :icon-class="item.meta.icon" />
          {{ item.meta.title }}
        </el-menu-item>
      </template>
    </el-sub-menu>
  </el-menu>
</template>

<script setup>
import { constantRoutes } from "@/router";
import { isHttp } from "@/utils/validate";
import useAppStore from "@/store/modules/app";
import usePermissionStore from "@/store/modules/permission";

// 顶部栏初始数
const visibleNumber = ref(null);
// 当前激活菜单的 index
const currentIndex = ref(null);
// 隐藏侧边栏路由
const hideList = ["/index", "/user/profile"];

const appStore = useAppStore();
const permissionStore = usePermissionStore();
const route = useRoute();
const router = useRouter();

// 所有的路由信息
const routers = computed(() => permissionStore.topbarRouters);

// 顶部显示菜单
const topMenus = computed(() => {
  let topMenus = [];
  routers.value.map((menu) => {
    if (menu.hidden !== true) {
      // 兼容顶部栏一级菜单内部跳转
      if (menu.path === "/") {
        topMenus.push(menu.children[0]);
      } else {
        topMenus.push(menu);
      }
    }
  });
  return topMenus;
});

// 设置子路由
const childrenMenus = computed(() => {
  let childrenMenus = [];
  routers.value.map((router) => {
    for (let item in router.children) {
      if (router.children[item].parentPath === undefined) {
        if (router.path === "/") {
          router.children[item].path = "/" + router.children[item].path;
        } else {
          if (!isHttp(router.children[item].path)) {
            router.children[item].path =
              router.path + "/" + router.children[item].path;
          }
        }
        router.children[item].parentPath = router.path;
      }
      childrenMenus.push(router.children[item]);
    }
  });
  return constantRoutes.concat(childrenMenus);
});

// 默认激活的菜单
const activeMenu = computed(() => {
  const path = route.path;
  let activePath = path;
  if (
    path !== undefined &&
    path.lastIndexOf("/") > 0 &&
    hideList.indexOf(path) === -1
  ) {
    const tmpPath = path.substring(1, path.length);
    activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
    if (!route.meta.link) {
      appStore.toggleSideBarHide(false);
    }
  } else if (!route.children) {
    activePath = path;
    appStore.toggleSideBarHide(true);
  }
  activeRoutes(activePath);
  return activePath;
});

function setVisibleNumber() {
  const width = document.body.getBoundingClientRect().width / 3;
  visibleNumber.value = parseInt(width / 85) - 1;
}

function handleSelect(key, keyPath) {
  currentIndex.value = key;
  const route = routers.value.find((item) => item.path === key);
  if (isHttp(key)) {
    // http(s):// 路径新窗口打开
    window.open(key, "_blank");
  } else if (!route || !route.children) {
    // 没有子路由路径内部打开
    router.push({ path: key });
    appStore.toggleSideBarHide(true);
  } else {
    // 获取第一个非隐藏的子菜单
    const firstVisibleChild = route.children.find((child) => !child.hidden);
    if (firstVisibleChild) {
      router.push({ path: firstVisibleChild.path });
    }

    // 显示左侧联动菜单
    activeRoutes(key);
    appStore.toggleSideBarHide(false);
  }
}

function activeRoutes(key) {
  let routes = [];
  if (childrenMenus.value && childrenMenus.value.length > 0) {
    childrenMenus.value.map((item) => {
      if (key == item.parentPath || (key == "index" && "" == item.path)) {
        routes.push(item);
      }
    });
  }
  if (routes.length > 0) {
    permissionStore.setSidebarRouters(routes);
  } else {
    appStore.toggleSideBarHide(true);
  }
  return routes;
}

onMounted(() => {
  window.addEventListener("resize", setVisibleNumber);
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", setVisibleNumber);
});

onMounted(() => {
  setVisibleNumber();
});
</script>

<style lang="scss" scoped>
.topmenu-container.el-menu--horizontal {
  background-color: transparent;
  border-bottom: none;

  .el-menu-item {
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    margin: 0 4px;
    transition: all 0.2s ease;
    font-family: Microsoft YaHei UI, Microsoft YaHei UI;
    font-weight: 700;
    font-size: 16px;
    color: #0e6ce3;
    opacity: 0.6;
    border: unset;

    &:hover {
      opacity: 1;
      background-color: transparent;
      color: #0e6ce3;
    }

    &:focus {
      background-color: transparent;
    }

    &.is-active {
      color: #0e6ce3 !important;
      opacity: 1;
    }

    .svg-icon {
      margin-right: 5px;
      vertical-align: -0.15em;
    }
  }

  :deep(.el-sub-menu) {
    .el-sub-menu__title {
      height: 60px;
      line-height: 60px;
      color: #0e6ce3;
      font-weight: 700;
      opacity: 0.6;
      border: unset;
      margin: 0 0 0 10px;
      font-size: 16px;
      transition: all 0.2s ease;
      min-width: 120px;
      padding: 0 15px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: visible;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: transparent;
        color: #0e6ce3;
        opacity: 1;
      }

      &.is-active {
        color: #0e6ce3 !important;
        opacity: 1;
      }
    }
  }
}

.more-menu {
  min-width: 160px;
  padding: 8px 0;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-menu-item {
    height: 45px;
    line-height: 45px;
    color: #606266;
    font-size: 14px;
    padding: 0 15px;
    margin: 0;

    .svg-icon {
      margin-right: 8px;
      color: #909399;
      vertical-align: -0.15em;
    }

    &:hover {
      background-color: #ecf5ff;
      color: #0e6ce3;

      .svg-icon {
        color: #0e6ce3;
      }
    }
  }
}

/* 响应式布局调整 */
@media screen and (max-width: 1200px) {
  .topmenu-container.el-menu--horizontal {
    .el-menu-item {
      padding: 0 15px;
      margin: 0 2px;
      font-size: 15px;
    }

    :deep(.el-sub-menu) {
      .el-sub-menu__title {
        margin: 0 0 0 15px;
        min-width: 110px;
        font-size: 15px;
      }
    }
  }
}

@media screen and (max-width: 992px) {
  .topmenu-container.el-menu--horizontal {
    .el-menu-item {
      padding: 0 10px;
      margin: 0 1px;
      font-size: 14px;
    }

    :deep(.el-sub-menu) {
      .el-sub-menu__title {
        margin: 0 0 0 10px;
        min-width: 100px;
        font-size: 14px;
      }
    }
  }
}
</style>
