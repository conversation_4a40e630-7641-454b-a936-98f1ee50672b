<template>
  <svg :class="svgClass" aria-hidden="true">
    <defs v-if="gradient">
      <linearGradient
        :id="`gradient-${uniqueId}`"
        x1="0%"
        y1="0%"
        x2="100%"
        y2="100%"
      >
        <stop offset="0%" stop-color="#008CFF" />
        <stop offset="73%" stop-color="#0E6CE3" />
      </linearGradient>
    </defs>
    <use :xlink:href="iconName" :fill="fillColor" />
  </svg>
</template>

<script setup>
import { computed } from "vue";
import { v4 as uuidv4 } from "uuid";

const props = defineProps({
  iconClass: {
    type: String,
    required: true,
  },
  className: {
    type: String,
    default: "",
  },
  color: {
    type: String,
    default: "",
  },
  gradient: {
    type: Boolean,
    default: false,
  },
});
// 生成唯一ID
const uniqueId = computed(() => `${props.iconClass}-${uuidv4()}`);

const iconName = computed(() => `#icon-${props.iconClass}`);

const svgClass = computed(() => {
  if (props.className) {
    return `svg-icon ${props.className}`;
  }
  return "svg-icon";
});

const fillColor = computed(() => {
  if (props.gradient) {
    return `url(#gradient-${uniqueId.value})`;
  }
  return props.color || "currentColor";
});
</script>

<style scope lang="scss">
.sub-el-icon,
.nav-icon {
  display: inline-block;
  font-size: 15px;
  margin-right: 12px;
  position: relative;
}

.svg-icon {
  width: 1em;
  height: 1em;
  position: relative;
  fill: currentColor;
  vertical-align: -2px;
}
</style>
