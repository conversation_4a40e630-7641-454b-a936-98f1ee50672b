<template>
  <!-- 表单项组件：负责渲染各种不同类型的表单控件 -->
  <el-form-item
    :label="label"
    :prop="prop"
    :label-width="attrs.labelWidth || ''"
    class="w-full"
  >
    <!-- 输入框：用于文本输入 -->
    <el-input
      v-if="type === 'input'"
      v-model="modelValue[prop]"
      :placeholder="placeholder || '请输入' + label"
      :style="{ width: width || '100%' }"
      v-bind="attrs"
    />

    <!-- 下拉选择框：用于从预设选项中选择 -->
    <el-select
      v-if="type === 'select'"
      v-model="modelValue[prop]"
      :placeholder="placeholder || '请选择' + label"
      :disabled="typeof disabled === 'function' ? disabled() : disabled"
      @change="handleChange"
      v-bind="attrs"
    >
      <!-- 动态渲染选项，支持自定义标签和值的键名 -->
      <el-option
        v-for="(option, index) in attrs.options"
        :key="index"
        :label="option[attrs.labelKey || 'label']"
        :value="option[attrs.valueKey || 'value']"
      />
    </el-select>

    <!-- 日期选择框：用于选择单个日期 -->
    <el-date-picker
      v-if="type === 'date'"
      v-model="modelValue[prop]"
      :type="attrs.dateType || 'date'"
      :placeholder="placeholder || '请选择' + label"
      :style="{ width: width || '100%' }"
      v-bind="attrs"
    />

    <!-- 日期范围选择框：用于选择日期范围 -->
    <el-date-picker
      v-if="type === 'daterange'"
      v-model="modelValue[prop]"
      type="daterange"
      value-format="YYYY-MM-DD"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :style="{ width: width || '100%' }"
      v-bind="attrs"
    />

    <!-- 级联选择器：用于多级数据选择，如地区选择 -->
    <el-cascader
      v-if="type === 'cascader'"
      v-model="modelValue[prop]"
      :placeholder="placeholder || '请选择' + label"
      :style="{ width: width || '100%' }"
      @change="handleChange"
      v-bind="attrs"
    />

    <!-- 自定义插槽：允许通过插槽自定义表单项内容 -->
    <slot
      v-if="type === 'slot'"
      :name="attrs.slotName"
      :row="modelValue"
    ></slot>
  </el-form-item>
</template>

<script setup>
import { useAttrs } from "vue";

// 定义组件名称
defineOptions({
  name: "SearchFormItem",
});

// 获取所有透传属性，用于扩展表单控件的配置
const attrs = useAttrs();

// 定义组件属性
const props = defineProps({
  // 表单数据对象：表单控件绑定的数据源
  modelValue: {
    type: Object,
    required: true,
  },
  // 占位提示文本：控件未输入时显示的提示信息
  placeholder: {
    type: String,
    default: "",
  },
  // 是否禁用：控制表单项是否可交互，支持函数动态计算
  disabled: {
    type: [Boolean, Function],
    default: false,
  },
  // 控件宽度：设置表单控件的显示宽度
  width: {
    type: String,
    default: "",
  },
  // 表单项属性名：用于表单验证和数据绑定
  prop: {
    type: String,
    default: "",
  },
  // 控件类型：决定渲染哪种类型的表单控件
  type: {
    type: String,
    default: "input",
  },
  // 标签文本：表单项的标签显示文本
  label: {
    type: String,
    default: "",
  },
});

// 值变化处理函数：当表单控件值变化时调用外部传入的change回调
const handleChange = (value) => {
  if (attrs.change && typeof attrs.change === "function") {
    attrs.change(value);
  }
};
</script>
