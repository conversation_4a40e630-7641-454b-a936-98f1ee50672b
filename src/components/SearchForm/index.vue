<template>
  <!-- 搜索表单主组件：基于 Element Plus 的表单组件，通过配置生成搜索表单 -->
  <el-form
    ref="formRef"
    :model="searchParam"
    :inline="inline"
    :label-width="labelWidth"
    @keyup.enter="search"
  >
    <!-- 前置插槽：可以在表单前添加自定义内容 -->
    <slot name="prefix"></slot>

    <el-row :gutter="gutter">
      <!-- 遍历搜索列配置，动态生成表单项 -->
      <template v-for="(item, index) in searchColumns" :key="index">
        <!-- 使用子组件渲染表单项，根据配置控制显示/隐藏 -->
        <el-col :span="item.col || searchCol" v-if="item.show !== false">
          <search-form-item v-bind="item" :modelValue="searchParam">
            <!-- 当表单项类型为自定义插槽时，转发插槽内容 -->
            <template v-if="item.type === 'slot'" #[item.slotName]="scope">
              <slot :name="item.slotName" v-bind="scope"></slot>
            </template>
          </search-form-item>
        </el-col>
      </template>

      <!-- 默认按钮区域：包含查询和重置按钮 -->
      <el-col :span="actionCol">
        <el-form-item class="action-container">
          <slot name="action" :search="search" :reset="reset">
            <el-button
              type="primary"
              @click="search"
              icon="Search"
              v-if="showSearch"
              >查询</el-button
            >
            <el-button @click="reset" icon="Refresh" v-if="showReset"
              >重置</el-button
            >
          </slot>
        </el-form-item>
      </el-col>

      <!-- 独立的按钮插槽区域：用于放置额外的操作按钮 -->
      <el-col :span="buttonsCol" v-if="$slots.buttons">
        <el-form-item class="buttons-container">
          <slot name="buttons"></slot>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 后置插槽：可以在表单后添加自定义内容 -->
    <slot name="suffix"></slot>
  </el-form>
</template>

<script setup>
import { ref, reactive, computed, watchEffect } from "vue";
import SearchFormItem from "./components/SearchFormItem.vue";

// 定义组件名称
defineOptions({
  name: "SearchForm",
});

// 定义props：组件接收的属性
const props = defineProps({
  // 是否使用行内表单样式
  inline: {
    type: Boolean,
    default: true,
  },
  // 表单项之间的间距
  gutter: {
    type: Number,
    default: 20,
  },
  // 搜索表单配置项：定义表单的结构和行为
  columns: {
    type: Array,
    required: true,
  },
  // 表单数据对象：存储表单各字段的值
  searchParam: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  // 是否使用内部数据对象：当为true时，组件内部管理表单数据状态
  useInnerParam: {
    type: Boolean,
    default: false,
  },
  // 表单标签宽度：统一设置所有表单项的标签宽度
  labelWidth: {
    type: String,
    default: "",
  },
  // 搜索项每列占用的栅格数：控制每个表单项的宽度
  searchCol: {
    type: Number,
    default: 4,
  },
  // 操作按钮区域占用的栅格数：控制按钮区域的宽度
  actionCol: {
    type: Number,
    default: 4,
  },
  // 额外按钮区域占用的栅格数：控制额外按钮区域的宽度
  buttonsCol: {
    type: Number,
    default: 24,
  },
  // 是否显示重置按钮
  showReset: {
    type: Boolean,
    default: true,
  },
  // 是否显示搜索按钮
  showSearch: {
    type: Boolean,
    default: true,
  },
});

// 定义事件：组件触发的事件
const emit = defineEmits(["search", "reset"]);

// 表单引用：用于访问表单实例和方法
const formRef = ref(null);

// 可见的搜索列：经过计算的表单配置项
const searchColumns = computed(() => props.columns);

// 内部数据对象：当使用内部状态管理时存储表单数据
const innerParam = reactive({});

// 初始化内部状态：根据表单配置设置初始值
watchEffect(() => {
  if (props.useInnerParam) {
    props.columns.forEach((item) => {
      // 为每个表单项设置初始值，优先使用配置中的initialValue
      if (item.prop && innerParam[item.prop] === undefined) {
        innerParam[item.prop] =
          item.initialValue !== undefined ? item.initialValue : "";
      }
    });
  }
});

// 获取搜索表单数据对象：根据配置自动判断使用内部还是外部数据
const getSearchParams = () => {
  return props.useInnerParam ? innerParam : props.searchParam;
};

// 搜索方法：触发search事件并传递表单数据
const search = () => {
  emit("search", getSearchParams());
};

// 重置方法：清空表单并恢复初始值
const reset = () => {
  if (formRef.value) {
    // 使用Element Plus表单的重置方法
    formRef.value.resetFields();

    // 如果使用内部参数，手动重置为初始值
    if (props.useInnerParam) {
      props.columns.forEach((item) => {
        if (item.prop) {
          innerParam[item.prop] =
            item.initialValue !== undefined ? item.initialValue : "";
        }
      });
    }
  }

  // 触发reset事件
  emit("reset", getSearchParams());
  // 重置后自动搜索
  search();
};

// 对外暴露的方法：允许父组件访问这些方法
defineExpose({
  formRef,
  search,
  reset,
});
</script>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
}

/* 按钮容器样式：使用弹性布局排列按钮 */
.action-container,
.buttons-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
