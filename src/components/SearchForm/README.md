# SearchForm 搜索表单组件

## 介绍

`SearchForm` 是一个基于 Element Plus 的表单组件，可以通过配置生成搜索表单，简化表单的开发工作。组件支持灵活的布局和丰富的表单项类型，适用于各种搜索筛选场景。

## 特性

- 支持多种表单项类型：输入框、选择器、日期选择器、级联选择器等
- 支持自定义表单项宽度、占位文本等
- 支持表单项的显示/隐藏控制
- 支持表单项联动
- 支持自定义插槽扩展
- 支持内部和外部数据管理
- 搜索、重置等常用操作
- 支持自定义布局和栅格系统

## 安装

```
// 组件已内置在项目中，无需额外安装
```

## 基本用法

```vue
<template>
  <search-form
    :columns="searchColumns"
    :searchParam="queryParams"
    @search="handleSearch"
    @reset="handleReset"
  />
</template>

<script setup>
import { reactive, ref } from "vue";
import SearchForm from "@/components/SearchForm/index.vue";

// 查询参数
const queryParams = ref({
  name: "",
  status: "",
  date: "",
});

// 搜索表单配置
const searchColumns = reactive([
  {
    type: "input", // 输入框类型
    label: "名称", // 标签文本
    prop: "name", // 表单项属性名
    placeholder: "请输入名称", // 占位文本
    width: "200px", // 宽度
  },
  {
    type: "select", // 下拉选择框类型
    label: "状态",
    prop: "status",
    options: [
      // 选项数据
      { label: "启用", value: 1 },
      { label: "禁用", value: 0 },
    ],
  },
  {
    type: "date", // 日期选择器类型
    label: "日期",
    prop: "date",
  },
]);

// 处理搜索
const handleSearch = (params) => {
  console.log("搜索参数:", params);
  // 执行查询操作
};

// 处理重置
const handleReset = (params) => {
  console.log("重置参数:", params);
  // 执行重置后操作
};
</script>
```

## 接口说明

### Props

| 参数名        | 说明                     | 类型    | 默认值 |
| ------------- | ------------------------ | ------- | ------ |
| columns       | 搜索表单配置项数组       | Array   | []     |
| searchParam   | 表单数据对象             | Object  | {}     |
| useInnerParam | 是否使用内部数据对象     | Boolean | false  |
| labelWidth    | 表单标签宽度             | String  | ''     |
| inline        | 是否使用行内表单样式     | Boolean | true   |
| gutter        | 表单项之间的间距         | Number  | 20     |
| searchCol     | 搜索项每列占用的栅格数   | Number  | 4      |
| actionCol     | 操作按钮区域占用的栅格数 | Number  | 4      |
| buttonsCol    | 额外按钮区域占用的栅格数 | Number  | 24     |
| showSearch    | 是否显示搜索按钮         | Boolean | true   |
| showReset     | 是否显示重置按钮         | Boolean | true   |

### 表单项配置

| 参数名       | 说明                                                                        | 类型                | 默认值                |
| ------------ | --------------------------------------------------------------------------- | ------------------- | --------------------- |
| type         | 表单项类型，支持 'input'、'select'、'date'、'daterange'、'cascader'、'slot' | String              | 'input'               |
| label        | 标签文本                                                                    | String              | ''                    |
| prop         | 表单项属性名                                                                | String              | ''                    |
| placeholder  | 占位文本                                                                    | String              | '请输入/选择' + label |
| width        | 表单项宽度                                                                  | String              | '100%'                |
| col          | 表单项占用的栅格数（覆盖全局设置）                                          | Number              | -                     |
| clearable    | 是否支持清空                                                                | Boolean             | true                  |
| disabled     | 是否禁用                                                                    | Boolean 或 Function | false                 |
| show         | 是否显示该表单项                                                            | Boolean             | true                  |
| options      | 选择框的选项                                                                | Array               | []                    |
| labelKey     | 选项标签的键名                                                              | String              | 'label'               |
| valueKey     | 选项值的键名                                                                | String              | 'value'               |
| change       | 值变化时的回调函数                                                          | Function            | -                     |
| initialValue | 初始值                                                                      | Any                 | ''                    |
| dateType     | 日期选择器类型                                                              | String              | 'date'                |
| props        | cascader 的配置选项                                                         | Object              | -                     |
| slotName     | 自定义插槽名称（type 为'slot'时必须）                                       | String              | -                     |

### 事件

| 事件名 | 说明               | 参数         |
| ------ | ------------------ | ------------ |
| search | 点击搜索按钮时触发 | 表单数据对象 |
| reset  | 点击重置按钮时触发 | 表单数据对象 |

### 插槽

| 插槽名     | 说明                                              |
| ---------- | ------------------------------------------------- |
| prefix     | 表单前置内容                                      |
| suffix     | 表单后置内容                                      |
| action     | 自定义操作按钮区域，参数包含 search 和 reset 方法 |
| buttons    | 额外按钮区域，独立于操作按钮区域                  |
| [slotName] | 自定义表单项内容，需在 columns 中定义 slotName    |

### 方法

| 方法名  | 说明                                             | 参数 |
| ------- | ------------------------------------------------ | ---- |
| search  | 触发搜索                                         | -    |
| reset   | 重置表单并触发搜索                               | -    |
| formRef | 获取表单实例，可访问 Element Plus 表单的所有方法 | -    |

## 高级用法

### 表单项联动示例

```vue
<script setup>
import { reactive, ref } from "vue";

// 查询参数
const queryParams = ref({
  province: "",
  city: "",
});

// 城市选项
const cityOptions = ref([]);

// 加载城市数据
const loadCities = (provinceId) => {
  // 模拟异步获取城市数据
  setTimeout(() => {
    if (provinceId === 1) {
      cityOptions.value = [{ label: "北京市", value: 10 }];
    } else if (provinceId === 2) {
      cityOptions.value = [{ label: "上海市", value: 20 }];
    } else {
      cityOptions.value = [];
    }
  }, 300);
};

// 省份变化处理
const handleProvinceChange = (value) => {
  queryParams.value.city = ""; // 清空城市选择
  if (value) {
    loadCities(value);
  } else {
    cityOptions.value = [];
  }
};

// 搜索表单配置
const searchColumns = reactive([
  {
    type: "select",
    label: "省份",
    prop: "province",
    options: [
      { label: "北京", value: 1 },
      { label: "上海", value: 2 },
    ],
    change: handleProvinceChange,
  },
  {
    type: "select",
    label: "城市",
    prop: "city",
    options: cityOptions,
    disabled: (val) => !queryParams.value.province,
  },
]);
</script>
```

### 使用自定义插槽

```vue
<template>
  <search-form
    :columns="searchColumns"
    :searchParam="queryParams"
    @search="handleSearch"
  >
    <!-- 自定义表单项内容 -->
    <template #customField="{ row }">
      <div class="custom-field">
        <el-switch v-model="row.status" />
        <span class="ml-2">{{ row.status ? "启用" : "禁用" }}</span>
      </div>
    </template>

    <!-- 自定义按钮区域 -->
    <template #buttons>
      <el-button @click="exportData">导出</el-button>
    </template>
  </search-form>
</template>

<script setup>
// 搜索表单配置
const searchColumns = reactive([
  // ... 其他表单项
  {
    type: "slot",
    label: "状态",
    prop: "status",
    slotName: "customField",
  },
]);

// 导出数据
const exportData = () => {
  // 导出逻辑
};
</script>
```

### 自定义布局示例

```vue
<template>
  <search-form
    :columns="searchColumns"
    :searchParam="queryParams"
    :searchCol="6"
    :actionCol="6"
    :inline="false"
    :labelWidth="'100px'"
  >
    <!-- 自定义操作按钮 -->
    <template #action="{ search, reset }">
      <el-button type="primary" @click="search">查询</el-button>
      <el-button @click="reset">重置</el-button>
      <el-button type="success" @click="exportData">导出</el-button>
    </template>
  </search-form>
</template>
```

## 注意事项

1. 使用 `useInnerParam` 为 `true` 时，组件内部会维护表单数据，无需传入 `searchParam`。
2. 对于联动表单，确保在处理函数中正确清空相关联的项。
3. 重置操作会自动触发搜索，如果不需要，可以通过自定义 `action` 插槽自行处理。
4. 表单项的 `col` 属性可以覆盖全局的 `searchCol` 设置，实现不同宽度的表单项。
