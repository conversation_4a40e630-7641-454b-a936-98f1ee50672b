/*
 * @Description: 
 * @Author: lig<PERSON><PERSON>
 * @LastEditors: liguiyuan
 */
import request from "@/utils/request";
// 查询视频监控站列表
export function getVideoMonitorStationList(params) {
    return request({
      url: '/system/video_monitor_station',
      method: 'get',
      params
    });
  }
  
  // 新增/编辑视频监控站
  export function saveOrUpdateVideoMonitorStation(data) {
    return request({
      url: '/system/video_monitor_station',
      method: 'post',
      data
    });
  }
  
  // 删除视频监控站
  export function deleteVideoMonitorStation(id) {
    return request({
      url: `/system/video_monitor_station/${id}`,
      method: 'delete'
    });
  }

  // 获取视频监控站详情
  export function getVideoMonitorStationDetail(id) {
    return request({
      url: `/system/video_monitor_station/${id}`,
      method: 'get'
    });
  }

  // 获取视频监控站监测点列表
  export function getVideoMonitorStationPoints(id) {
    return request({
      url: `/system/video_monitor_station/list/${id}`,
      method: 'get'
    });
  }

  // 添加/编辑视频监测点
  export function saveOrUpdateVideoMonitorPoint(data) {
    return request({
      url: '/system/video_monitor_point',
      method: 'post',
      data
    });
  }

  // 获取视频监测点列表
  export function getVideoMonitorPointList(params) {
    return request({
      url: '/system/video_monitor_point',
      method: 'get',
      params
    });
  }

  // 获取视频监测点详情
  export function getVideoMonitorPointDetail(id) {
    return request({
      url: `/system/video_monitor_point/${id}`,
      method: 'get'
    });
  }

  // 删除视频监测点
  export function deleteVideoMonitorPoint(id) {
    return request({
      url: `/system/video_monitor_point/${id}`,
      method: 'delete'
    });
  } 