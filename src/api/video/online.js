/*
 * @Description:
 * @Author: liguiyuan
 * @LastEditors: liguiyuan
 */
import request from "@/utils/request";

/**
 * 获取在线视频资源列表
 * @param {object} params
 * @param {string} params.resourceType 资源类型 必填 示例：camera
 * @param {number} params.catalogCode 目录编号 非必填 示例：region
 * @param {number} params.pageNo 当前页码 必填 示例：1
 * @param {number} params.pageSize 分页大小 必填 示例：10
 * @returns
 */
export function getOnlineVideoList(params) {
  return request({
    url: "/warning/camera/monitor/camera-info/resources",
    method: "get",
    params,
  });
}

/**
 * 获取在线视频直播推流url
 * @param {object} params
 * @param {string} params.cameraIndexCode 监控点编号（通用唯一识别码UUID），可通过#API@分页获取监控点资源@#获取。
 * @param {string} params.protocol 协议类型（rtsp-rtsp协议,rtmp-rtmp协议,hls-hLS协议,ws-Websocket协议），未填写为rtsp协议
 * @param {string} params.streamType 码流类型(0-主码流,1-子码流),未填默认为主码流
 * @param {string} params.transmode 传输协议（传输层协议），0:UDP 1:TCP 默认是TCP 注：GB28181 2011及以前版本只支持UDP传输
 * @param {string} params.expand 此字段非必要不建议指定，拓展字段（标准协议取流不需要扩展字段信息 ）当protocol为rtsp时： 支持指定streamform=rtp，表示使用标准RTSP协议，典型如使用VLC播放。当使用海康取流播放工具如视频SDK时，请勿指定streamform=rtp。 支持指定transcode=1，表示将H265编码视频转换成H264编码，典型使用场景如HLS播放。指定transcode=0表示不转码，默认为transcode=0。 可以同时指定streamform与transcode，但二者必须使用“&”连接起来，如transcode=1&streamform=rtp
 * @returns
 */
export function getOnlineVideoPushUrl(params) {
  return request({
    url: "/warning/camera/monitor/camera-info/getPreviewURLs",
    method: "get",
    params,
  });
}

//获取监控点标签树
export function getMonitorPointTree(params) {
  return request({
    url: "/warning/camera/monitor/camera-info/tag-tree",
    method: "get",
    params,
  });
}

//获取监控点回放取流URL
export function getMonitorPointHistoryPlayUrl(params) {
  return request({
    url: "/warning/camera/monitor/camera-info/getPlaybackURLs",
    method: "get",
    params,
  });
}

/**
 * 根据监控点编号进行云台操作
 * @param {Object} params
 * @param {string} params.cameraIndexCode 监控点编号（通用唯一识别码UUID），可通过#API@分页获取监控点资源@#获取。
 * @param {number} params.action 开始或停止操作(0 开始 1 停止)
 * @param {string} params.command 控制命令(不区分大小写) 说明： LEFT 左转 RIGHT 右转 UP 上转 DOWN 下转 ZOOM_IN 焦距变大 ZOOM_OUT 焦距变小 LEFT_UP 左上 LEFT_DOWN 左下 RIGHT_UP 右上 RIGHT_DOWN 右下 FOCUS_NEAR 焦点前移 FOCUS_FAR 焦点后移 IRIS_ENLARGE 光圈扩大 IRIS_REDUCE 光圈缩小 以下命令presetIndex不可为空： GOTO_PRESET到预置点
 * @param {number} params.speed 云台速度(取值范围1-100,默认40)
 * @param {number} params.presetIndex 预置点编号(取值范围为1-128)
 * @returns
 */
export function controlMonitorPoint(params) {
  return request({
    url: "/warning/camera/monitor/camera-info/controlling",
    method: "get",
    params,
  });
}

/**
 * 智能事件中心--分页查询事件
 * @param {Object} params
 * @param {string} params.address 事件地点
 * @param {string} params.analysisOrigin 事件分析来源 0：全部 1：算法智能事件 2：感知设备事件
 * @param {string} params.cameraIndexCodes 点位编号集合，多个逗号分隔
 * @param {string} params.componentId aiEventCenter 必填
 * @param {string} params.endTime 上报结束时间
 * @param {string} params.eventTypeCodes 事件类型码,多个逗号分隔 必填
 * @param {number} params.pageNo 当前页码 必填 示例：1
 * @param {number} params.pageSize 分页大小 必填 示例：10
 * @param {string} params.reviewStatus 是否复核 0：否、1：是
 * @param {string} params.sortType 排序类型 1：按上报时间正序，2：按上报时间倒序 必填
 * @param {string} params.startTime 上报开始时间
 * @param {string} params.strategyStatus 是否过策略 0：否、1：是
 * @param {string} params.sys_exclusive_sourceCompanyCode 数源单位编码
 * @param {string} params.taskId 编排任务Id
 * @param {string} params.tenantCode 租户标识
 * @param {string} params.verifyStatus 事件审核状态 必填
 * @returns
 */
export function getSmartEventList(params) {
  return request({
    url: "/warning/camera/monitor/camera-info/queryCommonEvents",
    method: "get",
    params,
  });
}

//智能事件中心--根据摄像头编号分页查询事件
export function getSmartEventByCameraId(params) {
  return request({
    url: "/warning/camera/monitor/camera-info/queryCommonEventsByCameraIndexCode",
    method: "get",
    params,
  });
}

//获取单事件详情
export function getSingleEventDetail(params) {
  return request({
    url: "/warning/camera/monitor/camera-info/getSingleEventDetail",
    method: "get",
    params,
  });
}
