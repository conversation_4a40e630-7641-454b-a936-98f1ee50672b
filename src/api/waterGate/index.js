/*
 * @Description:
 * @Author: lig<PERSON><PERSON>
 * @LastEditors: liguiyuan
 */
import request from '@/utils/request'

/**
 * 新增或编辑水闸
 * @param {Object} data WaterGatePO对象
 * @returns {Promise}
 */
export function addOrUpdateWaterGate(data) {
  return request({
    url: '/system/water-gate',
    method: 'post',
    data
  });
}

/**
 * 查询水闸信息列表
 * @param {Object} params 查询参数，如 { name: '水闸名称' }
 * @returns {Promise}
 */
export function getWaterGateList(params) {
  return request({
    url: '/system/water-gate',
    method: 'get',
    params
  });
}

/**
 * 删除水闸
 * @param {number|string} id 水闸ID
 * @returns {Promise}
 */
export function deleteWaterGate(id) {
  return request({
    url: `/system/water-gate/${id}`,
    method: 'delete'
  });
}

/**
 * 查询水闸的详细信息
 * @param {number|string} id 水闸ID
 * @returns {Promise}
 */
export function getWaterGateDetail(id) {
  return request({
    url: `/system/water-gate/${id}`,
    method: 'get'
  });
}

// 新增、编辑、删除水闸关联的测站
/**
 * 新增、编辑、删除水闸关联的测站
 * @param {Object} data GateStationRelationDTO对象
 * @returns {Promise}
 */
export function addOrUpdateOrDeleteGateStationRelation(data) {
  return request({
    url: '/system/gate_station_relation',
    method: 'post',
    data
  });
}

// 查询水闸信息列表
/**
 * 查询水闸信息列表
 * @param {Object} params 查询参数，如 { gateName: '水闸名称' }
 * @returns {Promise}
 */
export function getGateStationRelationList(params) {
  return request({
    url: '/system/gate_station_relation',
    method: 'get',
    params
  });
}

// 查询指定水闸关联的测站信息列表
/**
 * 查询指定水闸关联的测站信息列表
 * @param {string} gateCode 水闸代码
 * @returns {Promise}
 */
export function getGateStationRelationDetail(gateCode) {
  return request({
    url: `/system/gate_station_relation/${gateCode}`,
    method: 'get'
  });
}

// 查询水闸安全监测数据
/**
 * 查询水闸安全监测数据
 * @param {Object} params 查询参数，如 { stationCode: '测站编码', sectionCode: '断面编号', pointCode: '测点编码', monitorItem: '监测项目', monitorDate: 'yyyy-MM-dd HH:mm:ss' }
 * @returns {Promise}
 */
export function getSafetyMonitorInfo(params) {
  return request({
    url: '/system/safety-monitor-info',
    method: 'get',
    params
  });
}

/**
 * 查询堰闸水情数据列表
 * @param {Object} params
 * @returns {Promise}
 */
export function getSluiceWaterStationList(params) {
  return request({
    url: '/system//sluice_hydro_station',
    method: 'get',
    params
  });
}

/**
 * 查询堰闸水情详细数据
 * @param {Object} params
 * @returns {Promise}
 */
export function getSluiceWaterDetail(code, params) {
  return request({
    url: `/system/sluice_hydro_station/${code}`,
    method: 'get',
    params
  });
}

// 查询设备监测数据
/**
 * 查询设备监测数据
 *
 * @param {Object} params 查询参数，如 { startTime: 'yyyy-MM-dd HH:mm:ss', endTime: 'yyyy-MM-dd HH:mm:ss'，deviceCode } 设备编码
 * @returns {Promise}
 */
export function getDeviceMonitorInfo(params) {
  return request({
    url: `/system/safety-monitor-info/info`,
    method: 'get',
    params
  });
}

//0723
/**
 * 查询水闸安全监测树结构
 *
 * @param {Object} params 
 * @returns {Promise}
 */
export function getSafetyMonitorTree(params) {
  return request({
    url: `/system/safety-monitor-info/tree`,
    method: 'get',
    params
  });
}
/**
 * 查询安全检测点的监测数据
 *
 * @param {Object} params //id:安全监测点id,startTime:开始时间,endTime:结束时间,
 * monitorItem 监测项 渗压->'0'/温度->'1'
 * @returns {Promise}
 */
export function getSafetyMonitorPointInfo(params) {
  return request({
    url: `/system/safety-monitor-info/pointInfo`,
    method: 'get',
    params
  });
}