// Dify API 客户端实现
class DifyClient {
  constructor() {
    this.BASE_URL = "/dify/v1";
    this.PROXY_BASE_URL = import.meta.env.VITE_DIFY_BASE_URL;
    this.API_KEY = ""; // Will be set in init
    this.fetchInstance = null;
  }

  /**
   * 获取完整API URL
   * @private
   * @param {string} endpoint - API端点
   * @returns {string} 完整URL
   */
  _getFullUrl(endpoint) {
    if (import.meta.env.DEV) {
      return endpoint.startsWith("/")
        ? this.BASE_URL + endpoint
        : this.BASE_URL + `/${endpoint}`;
    } else {
      const url = endpoint.startsWith("/") ? endpoint : `/${endpoint}`;
      return `${this.PROXY_BASE_URL}${url}`;
    }
  }

  /**
   * 初始化Dify API
   * @param {string} apiKey - API密钥
   */
  init(apiKey) {
    this.API_KEY = apiKey;

    this.fetchInstance = async (url, options = {}) => {
      if (!options.headers) {
        options.headers = {};
      }

      options.headers["Authorization"] = `Bearer ${this.API_KEY}`;
      console.log("发送请求到:", url);

      const endpoint = url.startsWith("/") ? url : `/${url}`;
      const fullUrl = this._getFullUrl(endpoint);
      console.log("完整URL:", fullUrl);

      const response = await fetch(fullUrl, options);

      if (!response.ok) {
        try {
          const errorData = await response.clone().json();
          console.error("API响应错误:", {
            status: response.status,
            statusText: response.statusText,
            data: errorData,
          });
        } catch (e) {
          console.error("API响应错误:", {
            status: response.status,
            statusText: response.statusText,
          });
        }
      }

      return response;
    };

    console.log("Dify API 已初始化");
  }

  /**
   * 发送聊天消息并获取回复
   * @param {Object} params - 发送消息的参数，包含用户输入、会话ID等
   * @param {Object} options - 附加选项，如是否流式传输、回调函数等
   * @returns {Promise<Object>} 返回AI回复
   */
  async sendChatMessage(params, options = {}) {
    const { stream = false, onMessage = null, controller = null } = options;

    try {
      const url = "/chat-messages";
      const requestOptions = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      };

      if (controller) {
        requestOptions.signal = controller.signal;
      }

      if (!stream) {
        const response = await this.fetchInstance(url, requestOptions);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "聊天消息发送失败");
        }

        return await response.json();
      } else {
        if (!onMessage) {
          throw new Error("流式请求必须提供onMessage回调函数");
        }

        params.stream = true;
        requestOptions.body = JSON.stringify(params);

        const response = await this.fetchInstance(url, requestOptions);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "聊天消息发送失败");
        }

        if (!response.body) {
          throw new Error("当前浏览器不支持可读流");
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = "";

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              console.log("流读取完成");
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            const lines = buffer.split("\n\n");
            buffer = lines.pop() || "";

            for (const line of lines) {
              if (line.trim() === "") continue;

              if (line.startsWith("data:")) {
                try {
                  const jsonData = line.substring(5).trim();
                  if (jsonData === "[DONE]") continue;

                  const parsed = JSON.parse(jsonData);
                  onMessage(parsed);
                } catch (e) {
                  console.error("解析JSON失败:", e, line);
                  onMessage(line.substring(5).trim());
                }
              } else {
                try {
                  const parsed = JSON.parse(line);
                  console.log("解析的JSON数据:", parsed);
                  onMessage(parsed);
                } catch (e) {
                  console.warn("非JSON格式数据行:", line);

                  if (
                    line.trim().startsWith("event:") ||
                    line.trim().startsWith("data:") ||
                    line.includes("[DONE]") ||
                    line.includes("ping") ||
                    line.trim() === ""
                  ) {
                    continue;
                  }

                  if (line.trim().length > 0) {
                    onMessage(line);
                  }
                }
              }
            }
          }
        } catch (e) {
          if (e.name === "AbortError") {
            console.log("请求被用户中断");
          } else {
            console.error("流读取错误:", e);
            throw e;
          }
        }

        return { success: true, message: "流式传输完成" };
      }
    } catch (error) {
      console.error("发送聊天消息失败:", error);
      throw error;
    }
  }

  /**
   * 发送聊天工作流
   * @param {Object} params - 发送消息的参数，包含用户输入、会话ID等
   * @param {Object} options - 附加选项，如是否流式传输、回调函数等
   * @returns {Promise<Object>} 返回AI回复
   */
  async sendChatWorkflow(params, options = {}) {
    const { stream = false, onMessage = null, controller = null } = options;

    try {
      const url = "/workflows/run";
      const requestOptions = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      };

      if (controller) {
        requestOptions.signal = controller.signal;
      }

      if (!stream) {
        const response = await this.fetchInstance(url, requestOptions);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "聊天消息发送失败");
        }

        return await response.json();
      } else {
        if (!onMessage) {
          throw new Error("流式请求必须提供onMessage回调函数");
        }

        params.stream = true;
        requestOptions.body = JSON.stringify(params);

        const response = await this.fetchInstance(url, requestOptions);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "聊天消息发送失败");
        }

        if (!response.body) {
          throw new Error("当前浏览器不支持可读流");
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = "";

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              console.log("流读取完成");
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            const lines = buffer.split("\n\n");
            buffer = lines.pop() || "";

            for (const line of lines) {
              if (line.trim() === "") continue;

              if (line.startsWith("data:")) {
                try {
                  const jsonData = line.substring(5).trim();
                  if (jsonData === "[DONE]") continue;

                  const parsed = JSON.parse(jsonData);
                  onMessage(parsed);
                } catch (e) {
                  console.error("解析JSON失败:", e, line);
                  onMessage(line.substring(5).trim());
                }
              } else {
                try {
                  const parsed = JSON.parse(line);
                  console.log("解析的JSON数据:", parsed);
                  onMessage(parsed);
                } catch (e) {
                  console.warn("非JSON格式数据行:", e, line);

                  if (
                    line.trim().startsWith("event:") ||
                    line.trim().startsWith("data:") ||
                    line.includes("[DONE]") ||
                    line.includes("ping") ||
                    line.trim() === ""
                  ) {
                    continue;
                  }

                  if (line.trim().length > 0) {
                    onMessage(line);
                  }
                }
              }
            }
          }
        } catch (e) {
          if (e.name === "AbortError") {
            console.log("请求被用户中断");
          } else {
            console.error("流读取错误:", e);
            throw e;
          }
        }

        return { success: true, message: "流式传输完成" };
      }
    } catch (error) {
      console.error("发送聊天消息失败:", error);
      throw error;
    }
  }

  /**
   * 上传文件
   * @param {File} file - 要上传的文件
   * @param {string} user - 用户标识
   * @returns {Promise} 上传响应
   */
  async uploadFile(file, user) {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("user", user);

    return await this.fetchInstance("/files/upload", {
      method: "POST",
      body: formData,
    });
  }

  /**
   * 停止响应生成
   * @param {string} taskId - 要停止的任务 ID
   * @param {string} user - 用户标识
   * @returns {Promise} 停止响应
   */
  async stopResponseGeneration(taskId, user) {
    return await this.fetchInstance(`/chat-messages/${taskId}/stop`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ user }),
    });
  }

  /**
   * 发送消息反馈
   * @param {string} messageId - 消息 ID
   * @param {Object} params - 反馈参数
   * @returns {Promise} 反馈响应
   */
  async sendMessageFeedback(messageId, { rating, user, content }) {
    return await this.fetchInstance(`/messages/${messageId}/feedbacks`, {
      method: "POST",
      body: JSON.stringify({ rating, user, content }),
    });
  }

  /**
   * 获取建议问题
   * @param {string} messageId - 消息 ID
   * @param {string} user - 用户标识
   * @returns {Promise} 建议问题列表
   */
  async getSuggestedQuestions(messageId, user) {
    return await this.fetchInstance(
      `/messages/${messageId}/suggested?user=${user}`
    );
  }

  /**
   * 获取对话历史
   * @param {Object} params - 查询参数
   * @returns {Promise} 对话历史
   */
  async getConversationHistory({
    conversation_id,
    user,
    first_id = "",
    limit = 20,
  }) {
    const query = new URLSearchParams({
      conversation_id,
      user,
      first_id,
      limit: limit.toString(),
    }).toString();

    return await this.fetchInstance(`/messages?${query}`);
  }

  /**
   * 获取对话列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 对话列表
   */
  async getConversations({
    user,
    last_id = "",
    limit = 20,
    sort_by = "-updated_at",
  }) {
    const query = new URLSearchParams({
      user,
      last_id,
      limit: limit.toString(),
      sort_by,
    }).toString();

    return await this.fetchInstance(`/conversations?${query}`);
  }

  /**
   * 删除对话
   * @param {string} conversationId - 对话 ID
   * @param {string} user - 用户标识
   * @returns {Promise} 删除响应
   */
  async deleteConversation(conversationId, user) {
    return await this.fetchInstance(`/conversations/${conversationId}`, {
      method: "DELETE",
      body: JSON.stringify({ user }),
    });
  }

  /**
   * 重命名对话
   * @param {string} conversationId - 对话 ID
   * @param {Object} params - 重命名参数
   * @returns {Promise} 重命名响应
   */
  async renameConversation(
    conversationId,
    { name = "", auto_generate = false, user }
  ) {
    return await this.fetchInstance(`/conversations/${conversationId}/name`, {
      method: "POST",
      body: JSON.stringify({ name, auto_generate, user }),
    });
  }

  /**
   * 语音转文字
   * @param {File} file - 音频文件
   * @param {string} user - 用户标识
   * @returns {Promise} 文字转换响应
   */
  async audioToText(file, user) {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("user", user);

    return await this.fetchInstance("/audio-to-text", {
      method: "POST",
      body: formData,
    });
  }

  /**
   * 文字转语音
   * @param {Object} params - 转换参数
   * @returns {Promise} 语音转换响应
   */
  async textToAudio({ message_id, text, user }) {
    const formData = new FormData();
    if (message_id) formData.append("message_id", message_id);
    if (text) formData.append("text", text);
    formData.append("user", user);

    return await this.fetchInstance("/text-to-audio", {
      method: "POST",
      body: formData,
    });
  }

  /**
   * 获取应用信息
   * @returns {Promise} 应用信息
   */
  async getAppInfo() {
    return await this.fetchInstance("/info");
  }

  /**
   * 获取应用参数
   * @returns {Promise} 应用参数
   */
  async getAppParameters() {
    return await this.fetchInstance("/parameters");
  }

  /**
   * 获取应用元信息
   * @returns {Promise} 应用元信息
   */
  async getAppMeta() {
    return await this.fetchInstance("/meta");
  }
}

// 创建单例实例
const difyClient = new DifyClient();

// 导出实例和类
export default difyClient;
export { DifyClient };
