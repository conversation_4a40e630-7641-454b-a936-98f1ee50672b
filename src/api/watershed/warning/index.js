import request from '@/utils/request'
// 后台业务预警相关的接口
// 查询雨量预警指标
export function selectRainWarnPage(data) {
  return request({
    url: '/sl323/sys/basic/warning/selectRainWarnPage',
    method: 'post',
    data: data
  })
}
// 保存雨量预警指标
export function saveRainWarn(data) {
  return request({
    url: '/sl323/sys/basic/warning/saveRainWarn',
    method: 'put',
    data: data
  })
}

// 查询水位流量预警指标
export function selectWaterLevelPage(data) {
  return request({
    url: '/sl323/sys/basic/warning/selectWaterLevelPage',
    method: 'post',
    data: data
  })
}

// 保存水位流量预警指标
export function saveWaterLevelPage(data) {
  return request({
    url: '/sl323/sys/basic/warning/saveWaterLevelPage',
    method: 'put',
    data: data
  })
}

// 查询山洪预警0000
// export function selectFloodWarn1(id) {
//   return request({
//     url: '/sl323/sys/basic/warning/selectFloodWarn/' + id,
//     // url: '/sl323/sys/basic/warning/selectFloodWarn/',
//     method: 'get'
//   })
// }
export function selectFloodWarn1(data) {
  return request({
    url: '/sl323/sys/basic/warning/selectFloodWarnList',
    method: 'post',
    data: data
  })
}

// 查询山洪预警指标
export function selectFloodWarn(data) {
  return request({
    url: '/sl323/sys/basic/warning/selectFloodWarn',
    method: 'post',
    data: data
  })
}

// 保存山洪预警指标
export function saveFloodWarn(data) {
  return request({
    url: '/sl323/sys/basic/warning/saveFloodWarn',
    method: 'put',
    data: data
  })
}

// 查询预警处置分页
export function selectWarnPage(data) {
  return request({
    url: '/sl323/sys/basic/warning/selectWarnPage',
    method: 'post',
    data: data
  })
}
// 关闭预警处置
export function turnOffWarn(id) {
  return request({
    url: '/sl323/sys/basic/warning/turnOffWarn/' + id,
    method: 'put'
  })
}
// 查询预警详情
export function selectWarnInfo(id) {
  return request({
    url: '/sl323/sys/basic/warning/selectWarnInfo/' + id,
    method: 'get'
  })
}

// 查询危险区与测站
export function selectDanSt(data) {
  return request({
    url: '/sl323/sys/basic/warning/selectDanSt',
    method: 'post',
    data: data
  })
}
// 保存危险区与测站
export function saveDanSt(data) {
  return request({
    url: '/sl323/sys/basic/warning/saveDanSt',
    method: 'put',
    data: data
  })
}
