import request from '@/utils/request'
// 根据条件查询调度方案

export function postDisFn(data) {
  return request({
    url: 'sl323/fn/dispatch/postDisFn',
    method: 'post',
    data

  })
}
//设置调度方案可否执行
export function dispFaEnable(data) {
  return request({
    url: 'sl323/fn/dispatch/dispFaEnable/' + data.dispId + '/' + data.enable+'/'+ data.istrue,
    method: 'PUT',
  })
}

//获取已创建的调度方案的设置内容
export function getDisSettingCont(dispId) {
  return request({
    url: 'sl323/fn/dispatch/getDisSettingCont/' +dispId,
    method: 'get',
  })
}

//获取调度方案中 调度模式
export function getDdms() {
  return request({
    url: 'sl323/fn/dispatch/getDdms' ,
    method: 'get',
  })
}

// 获取预报列表

export function getForecast() {
  return request({
    url: 'sl323/fn/forecast/getForecast' ,
    method: 'get',
  })
}


// 保存调度方案接口

export function putDisSettingCont(data) {
  return request({
    url: 'sl323/fn/dispatch/putDisSettingCont',
    method: 'put',
    data
  })
}
// 设置方案参数 根据方案ID获取预报方案结果列表
export function getForecastResById(query) {
  return request({
    url: 'sl323/fn/forecast/getForecastResById' ,
    method: 'get',
    params:query
  })
}
// 根据方案ID获取 和流域lycode 获取蓄滞洪区
export function getXzhqByYbid(query) {
  return request({
    url: 'sl323/fn/forecast/getXzhqByYbid' ,
    method: 'get',
    params:query
  })
}
// 河道/水库预警数量统计
export function getDisStatResTj(query) {
  return request({
    url: 'sl323/fn/dispatch/getDisStatResTj' ,
    method: 'get',
    params:query
  })
}

// 调度方案淹没列表
export function getDisYmResInfo(query) {
  return request({
    url: 'sl323/fn/dispatch/getDisYmResInfo' ,
    method: 'get',
    params:query
  })
}
// 调度方案统计
export function getDisYmResTj(query) {
  return request({
    url: 'sl323/fn/dispatch/getDisYmResTj' ,
    method: 'get',
    params:query
  })
}

// 调度方案结果中水库/河道站信息
export function getDisStatResList(query) {
  return request({
    url: 'sl323/fn/dispatch/getDisStatResList' ,
    method: 'get',
    params:query
  })
}

// 调度方案结果中水库/河道站信息
export function getDisYmResTj_Ddcg(query) {
  return request({
    url: 'sl323/fn/dispatch/getDisYmResTj_Ddcg' ,
    method: 'get',
    params:query
  })
}