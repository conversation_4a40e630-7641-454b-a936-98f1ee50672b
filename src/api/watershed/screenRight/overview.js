import request from '@/utils/request'
// 右侧概览
// 雨情分级统计信息
export function classification(query) {
  return request({
    url: 'sl323/realtime/rain/ad/sum_x',
    method: 'get',
    params: query
  })
}

// 雨情日降水极值
export function rainPeak(data) {
  return request({
    url: 'sl323/realtime/rain/day-lv/h1',
    method: 'post',
    data
  })
}

export function rainSumlist(hour, query) {
  return request({
    url: `/hydro/measuring/station/extreme-rainfall-statistic/${hour}`,
    method: 'get',
    params: query
  })
}
export function warnRiver(data) {
  return request({
    url: 'sl323/realtime/river/warn/tj',
    method: 'post',
    data
  })
}

export function latestRiver(data) {
  return request({
    url: 'sl323/realtime/river/latest/',
    method: 'post',
    data
  })
}
export function warnListRiver(data) {
  return request({
    url: 'sl323/realtime/river/warn/list',
    method: 'post',
    data
  })
}

// 获取最近1小时3小时6小时12小时降水量
export function rainDetail(hour) {
  return request({
    url: `/hydro/measuring/station/extreme-rainfall-statistic/${hour}`,
    method: 'get',
  })
}

// 水库水情列表
export function rsvrList(query) {
  return request({
    url: 'sl323/realtime/rsvr',
    method: 'get',
    params: query
  })
}

// 查询某个水库的详情
export function rsvrShort(data) {
  return request({
    url: 'sl323/realtime/rsvr/list/' + data.stcd + '/' + data.bgtm + '/' + data.endtm,
    method: 'get',
  })
}
// 查询所有的水库水情信息
export function rsvrInfo() {
  return request({
    url: 'sl323/realtime/rsvr',
    method: 'get'
  })
}
// 获取河道防洪指标表数据
export function rvfcchInfo() {
  return request({
    url: 'sl323/basic/st/rvfcch/',
    method: 'get'
  })
}
// 雨情逐时降雨量过程统计
export function rainListChart(data) {
  return request({
    url: 'sl323/realtime/rain/list',
    method: 'post',
    data
  })
}

// 获取大断面数据信息
export function rvsectInfo() {
  return request({
    url: 'sl323/basic/st/rvsect/',
    method: 'get'
  })
}

// 水库工程基本信息
export function wrresbInfo(query) {
  return request({
    url: 'sl323/realtime/rsvr/wrresb',
    method: 'get',
    params: query

  })
}
// 获取水库防洪指标表数据
export function rsvrfcchInfo() {
  return request({
    url: '/sl323/basic/st/rsvrfcch',
    method: 'get',
  })
}
// 获取水库防洪指标表数据
export function rsvrfsrInfo(query) {
  return request({
    url: '/sl323/basic/st/rsvrfsr',
    method: 'get',
    params: query
  })
}