import request from '@/utils/request'
// 避险信息分页查询
export function riskTransferPage(data) {
  return request({
    url: 'risks/riskTransfer/riskTransferPage' ,
    method: 'post',
    data
  })
}
// 手动关闭预警信息
export function riskTransClose(id) {
  return request({
    url: 'risks/riskTransfer/turnOffRisk/' + id,
    method: 'put',
  })
}
// 更改转移状态
export function updateTransferStatus(data) {
  return request({
    url: 'risks/riskTransfer/updateTransferStatus',
    method: 'put',
    data
  })
}
// 新增避险信息
export function insertRisk(data) {
  return request({
    url: 'risks/riskTransfer/insertRisk',
    method: 'put',
    data
  })
}

// 分页查询避险转移居民安置信息
export function transferResettlePage(data) {
  return request({
    url: 'risks/riskTransfer/transferResettlePage' ,
    method: 'post',
    data
  })
}

// 安置点分页查询
export function placementPage(data) {
  return request({
    url: 'risks/riskTransfer/placementPage' ,
    method: 'post',
    data
  })
}
// 根据adcd查询危险区信息
export function danListByAdcd(adcd) {
  return request({
    url: 'risks/riskTransfer/danListByAdcd/' +   adcd ,
    method: 'get',
  })
}

// 设置安置信息
export function setPlacementParam(data) {
  return request({
    url: 'risks/riskTransfer/SetPlacementParam',
    method: 'put',
    data
  })
}

// 避险转移首页右侧分页列表接口
export function indexPage(data) {
  return request({
    url: 'risks/riskTransfer/index/indexPage' ,
    method: 'post',
    data
  })
}

// 首页安置点信息查询
export function placeIndexPage(data) {
  return request({
    url: 'risks/riskTransfer/index/placeIndexPage' ,
    method: 'post',
    data
  })
}
// 首页安置点信息查询
export function riskTransferResettleIPage(data) {
  return request({
    url: 'risks/riskTransfer/index/riskTransferResettleIPage' ,
    method: 'post',
    data
  })
}