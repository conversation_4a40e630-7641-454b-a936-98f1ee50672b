import request from "@/utils/request";

// #region -------- 小流域管理相关接口 S ----------
/**
 * 查询小流域列表
 */
export function getSubBasinList(data) {
  return request({
    url: "/system/sub-basin",
    method: "get",
    params: data,
  });
}

/**
 * 查询小流域详细信息
 */
export function getSubBasinInfo(code) {
  return request({
    url: "/system/sub-basin/" + code,
    method: "get",
  });
}

/**
 * 新增和编辑小流域信息
 */
export function saveSubBasin(data) {
  return request({
    url: "/system/sub-basin",
    method: "post",
    data: data,
  });
}

/**
 * 删除小流域
 */
export function deleteSubBasin(code) {
  return request({
    url: "/system/sub-basin/" + code,
    method: "delete",
  });
}

/**
 * 查询小流域雨量权重
 */
export function getSubBasinWeight(code) {
  return request({
    url: "/system/sub-basin/weight",
    method: "get",
    params: { code },
  });
}

/**
 * 保存和编辑小流域雨量权重
 */
export function saveSubBasinWeight(data) {
  return request({
    url: "/system/sub-basin/weight",
    method: "post",
    data: data,
  });
}

// #endregion -------- 小流域管理相关接口 E ----------

// #region -------- 闸门管理相关接口 S ----------
/**
 * 查询闸门列表
 */
export function getHydraulicGateList(data) {
  return request({
    url: "/system/gate/list",
    method: "get",
    params: data,
  });
}

/**
 * 查询闸门详细信息
 */
export function getHydraulicGateInfo(code) {
  return request({
    url: "/system/gate/" + code,
    method: "get",
  });
}

/**
 * 新增和编辑闸门信息
 */
export function saveHydraulicGate(data) {
  return request({
    url: "/system/gate",
    method: "post",
    data: data,
  });
}

/**
 * 删除闸门
 */
export function deleteHydraulicGate(code) {
  return request({
    url: "/system/gate/" + code,
    method: "delete",
  });
}

// #endregion -------- 闸门管理相关接口 E ----------

// #region -------- 大坝管理相关接口 S ----------

/**
 * 查询大坝列表
 */
export function getDamList(data) {
  return request({
    url: "/system/dam/list",
    method: "get",
    params: data,
  });
}

/**
 * 查询大坝详细信息
 */
export function getDamInfo(basinId) {
  return request({
    url: "/system/dam/" + basinId,
    method: "get",
  });
}

/**
 * 新增和编辑大坝信息
 */
export function saveDam(data) {
  return request({
    url: "/system/dam",
    method: "post",
    data: data,
  });
}

/**
 * 删除大坝
 */
export function deleteDam(basinId) {
  return request({
    url: "/system/dam/" + basinId,
    method: "delete",
  });
}

// #endregion -------- 大坝管理相关接口 E ----------
/**
 * 查询河道断面列表
 */
export function getRiverSectionList(data) {
  return request({
    url: "/system/river_section",
    method: "get",
    params: data,
  });
}

/**
 * 新增和编辑河道断面
 */
export function saveRiverSection(data) {
  return request({
    url: "/system/river_section",
    method: "post",
    data: data,
  });
}

/**
 * 查询河道断面详细信息
 */
export function getRiverSectionInfo(code) {
  return request({
    url: "/system/river_section/" + code,
    method: "get",
  });
}

/**
 * 河道断面删除
 */
export function deleteRiverSection(code) {
  return request({
    url: "/system/river_section/" + code,
    method: "delete",
  });
}
