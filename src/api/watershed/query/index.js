import request from '@/utils/request'
// 历史雨情列表查询
export function measuringStationList(query) {
  return request({
    url: '/hydro/measuring/station/measuringStationList',
    method: 'get',
    params: query
  })
}
//河道大断面分页列表
export function riverSectionList(query) {
  return request({
    url: '/hydro/strvsectb',
    method: 'get',
    params: query
  })
}
//河道大断面编辑报错
export function riverSectionSave(data) {
  return request({
    url: '/hydro/strvsectb/edit',
    method: 'post',
    data: data
  })
}
//获取河道大断面详情
export function riverSectionDetail(stcd) {
  return request({
    url: '/hydro/strvsectb/' + stcd,
    method: 'get',
  })
}
// 降雨量历史降雨数据详情（实时降雨数据）
export function measuringStationInfo(query) {
  return request({
    url: '/hydro/measuring/station/measuringStationInfo',
    method: 'get',
    params: query
  })
}
// 河道水情列表页查询
export function riverStationList(query) {
  return request({
    url: '/hydro/measuring/station/riverStationList',
    method: 'get',
    params: query
  })
}
// 河道水情详情查询
export function riverWaterLevel(query) {
  return request({
    url: '/hydro/measuring/station/riverWaterLevel',
    method: 'get',
    params: query
  })
}
// 水库水情分页查询统计
export function reservoirStationList(query) {
  return request({
    url: '/hydro/measuring/station/reservoirStationList',
    method: 'get',
    params: query
  })
}
// 水库水情详情查询统计
export function reservoirStationInfo(query) {
  return request({
    url: '/hydro/measuring/station/reservoirStationInfo',
    method: 'get',
    params: query
  })
}
//水库库容曲线
export function reservoirStationLine(query) {
  return request({
    url: '/hydro/measuring/station/water-level-capacity-curve',
    method: 'get',
    params: query
  })
}
//降水频率分析列表
export function rainFallList(query) {
  return request({
    url: '/system/rainfall-frequency-analysis',
    method: 'get',
    params: query
  })
}
//请求区县区域降雨量
export function areaRainFallList(query) {
  return request({
    url: '/hydro/measuring/station/area-rainfall',
    method: 'get',
    params: query
  })
}
//请求乡镇区域降雨量
export function townRainFallList(query) {
  return request({
    url: '/hydro/measuring/station/town-rainfall',
    method: 'get',
    params: query
  })
}
//新增降水频率数据
export function addRainFallData(data) {
  return request({
    url: '/system/rainfall-frequency-analysis/add',
    method: 'post',
    data
  })
}
//删除降水频率数据
export function deleteRainFallData(query) {
  return request({
    url: '/system/rainfall-frequency-analysis',
    method: 'delete',
    params: query
  })
}
//更新降水频率数据
export function updateRainFallData(data) {
  return request({
    url: '/system/rainfall-frequency-analysis/edit',
    method: 'post',
    data
  })
}

export function getHnskVideoTree(query) {
  return request({
    url: '/system/measuring/station/reservoirVideoList',
    method: 'get',
    params: query
  })
}


export function previewUrl(query) {
  return request({
    url: '/system/measuring/station/previewUrl',
    method: 'get',
    params: query
  })
}

export function cameraList(query) {
  return request({
    url: '/system/measuring/station/cameraList',
    method: 'get',
    params: query
  })
}