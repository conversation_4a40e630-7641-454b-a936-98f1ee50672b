import request from '@/utils/request'
// 后台业务预报调度相关的接口
// 获取节点和模型数据
export function getJieDianList(data) {
  return request({
    url: '/sl323/fn/forecast/getDatas',
    method: 'get'
  })
}
// 查询作业预报详情
export function getForecastInfo(id) {
  return request({
    url: '/sl323/fn/forecast/getForecastInfo/' + id,
    method: 'get'
  })
}
// 获取预报列表
export function getForecastList(id) {
  return request({
    url: '/sl323/fn/forecast/getForecast',
    method: 'get'
  })
}
// 查询任务车
export function getRenWuCheList(data) {
  return request({
    url: '/sl323/fn/forecast/questList',
    method: 'post',
    data: data
  })
}
// 删除任务车
export function deleteRenWuQuest(ids) {
  return request({
    url: '/sl323/fn/forecast/deleteQuest/' + ids,
    method: 'delete'
  })
}

// 计算 向模型发起计算请求
export function execCountForecast(data) {
  return request({
    url: '/sl323/fn/forecast/countForecast',
    method: 'put',
    data: data
  })
}

// 人工修改模型
export function updateByPeo(data) {
  return request({
    url: '/sl323/fn/forecast/updateByPeo',
    method: 'put',
    data: data
  })
}
// 保存调度方案
export function putDisSettingCont(data) {
  return request({
    url: '/sl323/fn/dispatch/putDisSettingCont',
    method: 'put',
    data: data
  })
}

// 方案结果保存
export function saveForecastInfo(data) {
  return request({
    url: '/sl323/fn/forecast/saveInfo',
    method: 'put',
    data: data
  })
}
// 自动滚动预报配置分页查询
export function getAutoForecastPage(data) {
  return request({
    url: '/sl323/fn/forecast/autoForecastPage',
    method: 'post',
    data: data
  })
}
// 预报成果展示
export function getForecastValueList(data) {
  return request({
    url: '/sl323/fn/forecast/forecastValue',
    method: 'post',
    data: data
  })
}
// 自动滚动预报配置创建
export function saveAutoRollingForecast(data) {
  return request({
    url: '/sl323/fn/forecast/autoRollingForecast',
    method: 'put',
    data: data
  })
}
// 删除自动预报
export function deleteAutoForecast(id) {
  return request({
    url: '/sl323/fn/forecast/deleteAutoForecast/' + id,
    method: 'delete'
  })
}
// 更新自动预报信息
export function updateAutoForecast(data) {
  return request({
    url: '/sl323/fn/forecast/updateAutoForecast',
    method: 'put',
    data: data
  })
}
// 启用停用自动预报
export function updateUsedStatus(data) {
  return request({
    url: '/sl323/fn/forecast/updateUsedStatus',
    method: 'put',
    data: data
  })
}
// 发布或取消发布预报成果
export function releaseForecast(data) {
  return request({
    url: '/sl323/fn/forecast/releaseForecast',
    method: 'put',
    data: data
  })
}
// 删除预报成果
export function deleteForecastById(id) {
  return request({
    url: '/sl323/fn/forecast/deleteForecastById/' + id,
    method: 'delete'
  })
}
// 下发调度指令分页查询
export function distributeIPage(data) {
  return request({
    url: '/sl323/scheduling/instruction/distributeIPage',
    method: 'post',
    data: data
  })
}

// 下发调度指令
export function createScheduling(data) {
  return request({
    url: '/sl323/scheduling/instruction/createScheduling',
    method: 'put',
    data: data
  })
}
// 更新调令-管理单位
export function updateScheduling(data) {
  return request({
    url: '/sl323/scheduling/instruction/updateScheduling',
    method: 'put',
    data: data
  })
}
// 删除调令-管理单位
export function deleteScheduling(id) {
  return request({
    url: '/sl323/scheduling/instruction/deleteScheduling/' + id,
    method: 'delete'
  })
}
// 接收调度指令分页查询-工程单位
export function getReceiveIPage(data) {
  return request({
    url: '/sl323/scheduling/instruction/receiveIPage',
    method: 'post',
    data: data
  })
}

//
// 根据租户id查询部门信息
export function getDeptsByTenantId() {
  return request({
    url: '/sl323/scheduling/instruction/getDeptsByTenantId',
    method: 'get'
  })
}
// 查询其他单位抄送的调令
export function getCopyPage(data) {
  return request({
    url: '/sl323/scheduling/instruction/getCopyPage',
    method: 'post',
    data: data
  })
}

// 调度方案
//预报结果分页,调度方案拟定查询成果用
export function getForecastPlan(data) {
  return request({
    url: '/sl323/fn/forecast/forecastIdName',
    method: 'post',
    data: data
  })
}

// 调度成果查询
export function getDispatchPage(data) {
  return request({
    url: '/sl323/dispatch/dispatchPage',
    method: 'post',
    data: data
  })
}

// 调度成果详情查询
export function getDispatchInfo(id) {
  return request({
    url: '/sl323/dispatch/dispatchInfo/' + id,
    method: 'get'
  })
}

// 更新调度成果状态
export function setExeCutePlan(data) {
  return request({
    url: '/sl323/dispatch/setExeCutePlan',
    method: 'post',
    data: data
  })
}
// 删除调度成果
export function deleteDispatch(id) {
  return request({
    url: '/sl323/dispatch/deleteDispatch/' + id,
    method: 'delete'
  })
}

// 计算调度方案
export function countDispatch(data) {
  return request({
    url: '/sl323/scheduling/dispatch/countDispatch',
    method: 'put',
    data: data
  })
}
// 新增和编辑典型降雨
export function saveTypicalRainfall(data) {
  return request({
    url: '/system/typical_rainfall',
    method: 'post',
    data: data
  })
}
// 获取典型降雨列表
export function getTypicalRainfallList(data) {
  return request({
    url: '/system/typical_rainfall/list',
    method: 'get',
    params: data
  })
}

/**
 * 获取典型降雨详细信息
 * @param {string} code 典型降雨编号
 * @returns
 */
export function getTypicalRainfallInfo(code) {
  return request({
    url: '/system/typical_rainfall/info',
    method: 'get',
    params: code
  })
}
/**
 * 获取指定流域的降雨量信息
 * @param {string} subBasinCode 小流域编号
 * @param {string} startTime 开始时间
 * @param {string} endTime 结束时间
 * @param {string} stationCode 雨量站编号
 * @returns
 */
export function getRainfallInfo(data) {
  return request({
    url: '/system/typical_rainfall',
    method: 'get',
    params: data
  })
}
/**
 * 删除典型降雨
 * @param {string} id 典型降雨id
 * @returns
 */
export function deleteTypicalRainfall(id) {
  return request({
    url: '/system/typical_rainfall/' + id,
    method: 'delete'
  })
}
/**
 * 上传典型降雨Excel
 * @param {string} file 典型降雨Excel文件
 * @returns
 */
export function uploadTypicalRainfall(file) {
  return request({
    url: '/system/typical_rainfall/upload',
    method: 'post',
    data: file
  })
}

/**
 * 新增历史洪水
 * @param {Object} data FloodHistoryPO 历史洪水对象
 * @returns {Promise} AjaxResult 操作消息提醒
 */
export function addFloodHistory(data) {
  return request({
    url: '/system/flood-history',
    method: 'post',
    data: data
  })
}

/**
 * 修改历史洪水
 * @param {Object} data FloodHistoryPO 历史洪水对象
 * @returns {Promise} AjaxResult 操作消息提醒
 */
export function modifyFloodHistory(data) {
  return request({
    url: '/system/flood-history/modify',
    method: 'post',
    data: data
  })
}

/**
 * 查询历史洪水详情
 * @param {Object} code 查询参数（code）
 * @returns {Promise} TableDataInfoFloodHistoryVO 历史洪水详情
 */
export function getFloodHistoryInfo(code) {
  return request({
    url: '/system/flood-history/detail',
    method: 'get',
    params: { code }
  })
}
/**
 * 查询历史洪水列表
 * @param {Object} params 查询参数（code）
 * @returns {Promise} TableDataInfoFloodHistoryVO 历史洪水分页列表
 */
export function getFloodHistoryList(params) {
  return request({
    url: '/system/flood-history',
    method: 'get',
    params: params
  })
}

/**
 * 删除历史洪水
 * @param {string} code 历史洪水编号
 * @returns {Promise} AjaxResult 操作消息提醒
 */
export function deleteFloodHistory(code) {
  return request({
    url: `/system/flood-history/${code}`,
    method: 'delete'
  })
}

/**
 * 重新评价历史洪水方案（预留）
 * @param {string} code 历史洪水编号
 * @returns {Promise}
 */
export function reEvaluateFloodHistory(code) {
  // 预留接口，后续补充真实请求
  return Promise.reject({ msg: '接口未实现' })
}

/**
 * 获取雨量水位整点数据
 * @param {Object} params { startTime: string, endTime: string, stcd: string }
 * @returns {Promise} TableDataInfoFloodHistoryDataVO
 */
export function getRainLevelData(params) {
  return request({
    url: '/hydro/measuring/station/rain-level',
    method: 'get',
    params: params
  })
}

/**
 * 查询文件模板列表
 * @param {Object} params 查询参数（code, name, fileName 可选）
 * @returns {Promise} TableDataInfoFileTemplatePO 文件模板分页列表
 */
export function getFileTemplateList(params) {
  return request({
    url: '/system/file-template',
    method: 'get',
    params: params
  })
}

/**
 * 新增文件模板
 * @param {Object} params 文件模板参数（code, name, fileName, filePath, tenantId）
 * @returns {Promise} AjaxResult 操作消息提醒
 */
export function addFileTemplate(params) {
  return request({
    url: '/system/file-template',
    method: 'post',
    params: params
  })
}

/**
 * 修改文件模板
 * @param {Object} params 文件模板参数（code, name, fileName, filePath, tenantId）
 * @returns {Promise} AjaxResult 操作消息提醒
 */
export function modifyFileTemplate(params) {
  return request({
    url: '/system/file-template/modify',
    method: 'post',
    params: params
  })
}

/**
 * 删除文件模板
 * @param {string} code 文件模板编号
 * @returns {Promise} AjaxResult 操作消息提醒
 */
export function deleteFileTemplate(code) {
  return request({
    url: `/system/file-template/${code}`,
    method: 'delete'
  })
}

export function saveOrUpdateRollingForecastTask(data) {
  return request({
    url: '/system/rolling-forecast-task',
    method: 'post',
    data: data
  });
}

export function deleteRollingForecastTask(id) {
  return request({
    url: '/system/rolling-forecast-task/' + id,
    method: 'delete'
  });
}

export function getRollingForecastTaskList(params) {
  return request({
    url: '/system/rolling-forecast-task/list',
    method: 'get',
    params: params
  });
}

export function getRollingForecastTaskDetail(id) {
  return request({
    url: '/system/rolling-forecast-task/' + id,
    method: 'get'
  });
}
//精度评价 获取列表
export function getAccuracyEvaluationList(params) {
  return request({
    url: '/forecast/precision-evaluation',
    method: 'get',
    params,
  });
}

/**
 * 安全监测分析 - 查询水闸树形结构
 */
export function getSafetyMonitorTree(params) {
  return request({
    url: '/system/safe-monitor-inspect/tree',
    method: 'get',
    params,
  });
}

/**
 * 安全监测分析 - 查询三级节点的数据
 */
export function getSafetyMonitorPointInfo(data) {
  return request({
    url: '/system/safe-monitor-inspect/info',
    method: 'post',
    data,
  });
}