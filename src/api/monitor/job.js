import request from "@/utils/request"

// 查询定时任务调度列表
export function listJob(query) {
  return request({
    url: "/job/info/list",
    method: "get",
    params: query,
  })
}

// 新增任务
export function addJobInfo(data) {
  return request({
    url: "/job/info/add",
    method: "post",
    data: data,
  })
}

// 修改任务
export function updateJobInfo(data) {
  return request({
    url: "/job/info",
    method: "put",
    data: data,
  })
}

// 删除任务
export function delJobInfo(jobId) {
  return request({
    url: "/job/info/" + jobId,
    method: "delete",
  })
}

// 获取任务详情
export function getJobDetail(jobId) {
  return request({
    url: "/job/info/" + jobId,
    method: "get",
  })
}

// 查询定时任务调度详细
export function getJob(jobId) {
  return request({
    url: "/job/group/" + jobId,
    method: "get",
  })
}

// 新增定时任务调度
export function addJob(data) {
  return request({
    url: "/monitor/job",
    method: "post",
    data: data,
  })
}

// 修改定时任务调度
export function updateJob(data) {
  return request({
    url: "/monitor/job",
    method: "put",
    data: data,
  })
}

// 删除定时任务调度
export function delJob(jobId) {
  return request({
    url: "/monitor/job/" + jobId,
    method: "delete",
  })
}

// 定时任务立即执行一次
export function runJob(data) {
  return request({
    url: "/job/info/trigger",
    method: "post",
    data,
  })
}

// 启用任务
export function startJob(id) {
  return request({
    url: `/job/info/start/${id}`,
    method: "get",
  })
}

// 停用任务
export function stopJob(id) {
  return request({
    url: `/job/info/stop/${id}`,
    method: "get",
  })
}

// 获取执行器下次触发时间
export function getNextTriggerTime(data) {
  return request({
    url: "/job/info/nextTriggerTime",
    method: "post",
    data: data,
  })
}

// 任务状态修改
export function changeJobStatus(jobId, status) {
  const data = {
    jobId,
    status,
  }
  return request({
    url: "/monitor/job/changeStatus",
    method: "put",
    data: data,
  })
}

// ----------------  调度日志 S ------------
// 查询调度日志列表
export function listJobLog(query) {
  return request({
    url: "/job/log/list",
    method: "get",
    params: query,
  })
}

// 清理调度日志
export function clearJobLog(data) {
  return request({
    url: "/job/log/clearLog",
    method: "post",
    data: data,
  })
}

// 查看调度日志
export function getJobLog(logId, fromLineNum) {
  return request({
    url: `/job/log/logDetailCat/${logId}/${fromLineNum}`,
    method: "get",
  })
}
// 终止任务
export function killJob(id) {
  return request({
    url: `/job/log/logKill/${id}`,
    method: "get",
  })
}
// ----------------  调度日志 E ------------

// ----------------  执行器管理 S ------------
// 查询定时任务分组列表
export function listGroup(query) {
  return request({
    url: "/job/group/list",
    method: "get",
    params: query,
  })
}

// 新增执行器
export function addGroup(data) {
  return request({
    url: "/job/group/add",
    method: "post",
    data: data,
  })
}

// 修改执行器
export function updateGroup(data) {
  return request({
    url: "/job/group/edit",
    method: "put",
    data: data,
  })
}

// 删除执行器
export function delGroup(id) {
  return request({
    url: "/job/group/" + id,
    method: "delete",
  })
}
// ----------------  执行器管理 E ------------
