import request from "@/utils/request";

// ---------- 灾害事件管理相关接口 S ----------
// 新增/修改灾害事件
export function saveDisasterEvent(data) {
  return request({
    url: "/risks/disaster-event",
    method: "post",
    data: data,
  });
}

// 获取灾害事件列表
export function getDisasterEventList(query) {
  return request({
    url: "/risks/disaster-event",
    method: "get",
    params: query,
  });
}

// 删除灾害事件
export function deleteDisasterEvent(id) {
  return request({
    url: `/risks/disaster-event/${id}`,
    method: "delete",
  });
}
// ---------- 灾害事件管理相关接口 E ----------
