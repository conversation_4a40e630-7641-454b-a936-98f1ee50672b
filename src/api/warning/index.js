import request from "@/utils/request";
import { downloadByNew } from "@/utils/request";
//防汛预警相关接口
//居民管理列表
export function residentList(data) {
  return request({
    url: "/system/resident/residents",
    method: "post",
    data: data,
  });
}
//新增居民
export function addResident(data) {
  return request({
    url: "/system/resident",
    method: "post",
    data: data,
  });
}
//更新居民信息
export function updateResident(data, id) {
  return request({
    url: "/system/resident/" + id,
    method: "post",
    data: data,
  });
}
//删除居民信息
export function deleteResident(id) {
  return request({
    url: `/system/resident/${id}`,
    method: "delete",
  });
}
//村社管理列表
export function villageList(data) {
  return request({
    url: "/system/admin/village/admin-village",
    method: "post",
    data: data,
  });
}
//更新村社信息
export function updateVillage(data) {
  return request({
    url: "/system/admin/village/town",
    method: "post",
    data: data,
  });
}
//新增村社信息
export function addVillage(data) {
  return request({
    url: "/system/admin/village",
    method: "post",
    data: data,
  });
}
//删除村社信息
export function deleteVillage(adcd) {
  return request({
    url: `/system/admin/village/${adcd}`,
    method: "delete",
  });
}
//危险区信息列表
export function dangerousAreaList(data) {
  return request({
    url: "/system/hazardous/area/hazardous",
    method: "post",
    data: data,
  });
}
//根据id查询危险区详情
export function dangerousAreaDetail(id) {
  return request({
    url: `/system/hazardous/area/${id}`,
    method: "get",
  });
}
//新增危险区信息
export function addDangerousArea(data) {
  return request({
    url: "/system/hazardous/area",
    method: "post",
    data: data,
  });
}
//更新危险区信息
export function updateDangerousArea(data, id) {
  return request({
    url: "/system/hazardous/area/" + id,
    method: "post",
    data: data,
  });
}
//删除危险区信息
export function deleteDangerousArea(id) {
  return request({
    url: `/system/hazardous/area/${id}`,
    method: "delete",
  });
}
//安置点信息列表
export function placeList(data) {
  return request({
    url: "/system/placement/points",
    method: "post",
    data: data,
  });
}
//新增安置点
export function addPlace(data) {
  return request({
    url: "/system/placement",
    method: "post",
    data: data,
  });
}
//更新安置点
export function updatePlace(data, id) {
  return request({
    url: "/system/placement/" + id,
    method: "post",
    data: data,
  });
}
//删除安置点
export function deletePlace(id) {
  return request({
    url: `/system/placement/${id}`,
    method: "delete",
  });
}
//更新安置点状态
export function updatePlaceStatus(data) {
  return request({
    url: "/system/placement/status",
    method: "post",
    data: data,
  });
}
//根据stcd查询测站最近一条数据时间

export function testStcdRecord(stcd, type) {
  return request({
    url: `/hydro/station/station-warns/${stcd}/${type}`,
    method: "get",
  });
}
//危险区关联测站 保存接口

export function dangerStationsSave(data) {
  return request({
    url: `/system/hazardous/area/hazardous-stations`,
    method: "post",
    data,
  });
}
//危险区关联测站详情
export function dangerStationsById(id) {
  return request({
    url: `/system/hazardous/area/hazardous-station/${id}`,
    method: "post",
  });
}

// 分页查询危险区关联测站
export function queryFloodPreventionDangerAreaStations(data) {
  return request({
    url: `/system/hazardous/area/hazardous-stations-page`,
    method: "post",
    data,
  });
}

//危险区雨晴报警
export function selectRainWarnPage(query) {
  return request({
    url: "/system/hazardous/warn/rains",
    method: "get",
    params: query,
  });
}
// 危险区雨晴报警编辑
export function saveRainWarn(data) {
  return request({
    url: "/system/hazardous/warn/rain",
    method: "post",
    data: data,
  });
}
// 查询水位流量预警指标
export function selectWaterLevelPage(query) {
  return request({
    url: "/system/hazardous/warn/water-level",
    method: "get",
    params: query,
  });
}

// 保存水位流量预警指标
export function saveWaterLevelPage(data) {
  return request({
    url: "/system/hazardous/warn/water-level",
    method: "post",
    data: data,
  });
}

// 保存村社气象预警
export function saveFloodWarn(data) {
  return request({
    url: "/system/hazardous/warn/meteorological",
    method: "post",
    data: data,
  });
}

// 查询村社气象预警
export function selectFloodWarn(query) {
  return request({
    url: "/system/hazardous/warn/meteorological",
    method: "get",
    params: query,
  });
}

/* 防汛 */

// 分页查询防汛成员列表
export function queryFloodPreventionMember(query) {
  return request({
    url: "/system/flood-prevention/member",
    method: "get",
    params: query,
  });
}
// 新增防汛成员信息
export function addFloodPreventionMember(data) {
  return request({
    url: "/system/flood-prevention/member",
    method: "post",
    data: data,
  });
}
// 更新防汛成员信息
export function updateFloodPreventionMember(data, id) {
  return request({
    url: `/system/flood-prevention/member/${id}`,
    method: "post",
    data: data,
  });
}
// 删除防汛成员信息
export function deleteFloodPreventionMember(id) {
  return request({
    url: `/system/flood-prevention/member/${id}`,
    method: "delete",
    // data: data
  });
}

// 分页查询防汛责任列表
export function queryFloodPreventionResponsibility(query) {
  return request({
    url: "/system/flood-prevention/responsibility",
    method: "get",
    params: query,
  });
}
// 新增防汛责任信息
export function addFloodPreventionResponsibility(data) {
  return request({
    url: "/system/flood-prevention/responsibility",
    method: "post",
    data: data,
  });
}
// 更新防汛责任信息
export function updateFloodPreventionResponsibility(data, id) {
  return request({
    url: `/system/flood-prevention/responsibility/${id}`,
    method: "post",
    data: data,
  });
}
// 删除防汛责任信息
export function deleteFloodPreventionResponsibility(id) {
  return request({
    url: `/system/flood-prevention/responsibility/${id}`,
    method: "delete",
    // data: data
  });
}

// 分页查询防汛岗位列表
export function queryFloodPreventionPositions(query) {
  return request({
    url: "/system/flood-prevention/positions",
    method: "get",
    params: query,
  });
}
// 新增防汛岗位信息
export function addFloodPreventionPositions(data) {
  return request({
    url: "/system/flood-prevention/position",
    method: "post",
    data: data,
  });
}
// 更新防汛岗位信息
export function updateFloodPreventionPositions(data, id) {
  return request({
    url: `/system/flood-prevention/position/${id}`,
    method: "post",
    data: data,
  });
}
// 删除防汛岗位信息
export function deleteFloodPreventionPositions(id) {
  return request({
    url: `/system/flood-prevention/position/${id}`,
    method: "delete",
    // data: data
  });
}

// 分页查询防汛组织列表
export function queryFloodPreventionOrganizations(query) {
  return request({
    url: "/system/flood-prevention/organizations",
    method: "get",
    params: query,
  });
}
// 新增防汛组织信息
export function addFloodPreventionOrganizations(data) {
  return request({
    url: "/system/flood-prevention/organization",
    method: "post",
    data: data,
  });
}
// 更新防汛组织信息
export function updateFloodPreventionOrganizations(data, id) {
  return request({
    url: `/system/flood-prevention/organization/${id}`,
    method: "post",
    data: data,
  });
}
// 删除防汛组织信息
export function deleteFloodPreventionOrganizations(id) {
  return request({
    url: `/system/flood-prevention/organization/${id}`,
    method: "delete",
    // data: data
  });
}

//危险区
// 分页查询危险区转移安置点列表
export function queryFloodPreventionDangerArea(query) {
  return request({
    url: "/system/flood-prevention/transfers",
    method: "get",
    params: query,
  });
}
// 分页查询危险区转移安置点详情
export function queryFloodPreventionDangerAreaDetail(id) {
  return request({
    url: `/system/flood-prevention/transfer/${id}`,
    method: "get",
  });
}
// 新增危险区转移安置点信息
export function addFloodPreventionDangerArea(data) {
  return request({
    url: "/system/flood-prevention/transfer",
    method: "post",
    data: data,
  });
}
// 更新危险区转移安置点
export function updateFloodPreventionDangerArea(data, id) {
  return request({
    url: `/system/flood-prevention/transfer/${id}`,
    method: "post",
    data: data,
  });
}
// 删除危险区转移安置点
export function deleteFloodPreventionDangerArea(id) {
  return request({
    url: `/system/flood-prevention/transfer/${id}`,
    method: "delete",
    // data: data
  });
}

// 分页查询预案信息
export function queryFloodPreventionPlan(query) {
  return request({
    url: "/system/flood-prevention",
    method: "get",
    params: query,
  });
}
// 新增预案信息
export function addFloodPreventionPlan(data) {
  return request({
    url: "/system/flood-prevention",
    method: "post",
    data: data,
  });
}
// 更新预案信息
export function updateFloodPreventionPlan(data, id) {
  return request({
    url: `/system/flood-prevention/${id}`,
    method: "post",
    data: data,
  });
}
// 删除预案信息
export function deleteFloodPreventionPlan(id) {
  return request({
    url: `/system/flood-prevention/${id}`,
    method: "delete",
    // data: data
  });
}

// 查询预警模板
export function getWarningTemplate(query) {
  return request({
    url: "/system/warning-template",
    method: "get",
    params: query,
  });
}
// 新增预警模板
export function addWarningTemplate(data) {
  return request({
    url: "/system/warning-template",
    method: "post",
    data: data,
  });
}
// 更新预警模板
export function updateWarningTemplate(data, id) {
  return request({
    url: `/system/warning-template/${id}`,
    method: "post",
    data: data,
  });
}
// 删除预警模板
export function deleteWarningTemplate(id) {
  return request({
    url: `/system/warning-template/${id}`,
    method: "delete",
  });
}

//预警发布
// 分页查询预警发布列表
export function queryWarningRelease(query) {
  return request({
    url: "/system/warn-info",
    method: "get",
    params: query,
  });
}
// 分页查询预警发布列表---西藏政务云环境
export function queryWarningReleaseLasa(query) {
  return request({
    url: "/warning/head-warning",
    method: "get",
    params: query,
  });
}
// 新增预警发布
export function addWarningRelease(data) {
  return request({
    url: "/system/warn-info",
    method: "post",
    data: data,
  });
}
// 更新预警发布
export function updateWarningRelease(data, id) {
  return request({
    url: `/system/warn-info/${id}`,
    method: "post",
    data: data,
  });
}
// 删除预警发布
export function deleteWarningRelease(id) {
  return request({
    url: `/system/warn-info/${id}`,
    method: "delete",
  });
}
// 关闭预警发布
export function closeWarningRelease(id) {
  return request({
    url: `/system/warn-info/${id}`,
    method: "get",
  });
}

//内部预警查询列表
export function queryWarningInner(query) {
  return request({
    url: "/system/warn-info/internal",
    method: "get",
    params: query,
  });
}
//内部预警新增
export function addWarningInner(data) {
  return request({
    url: "/system/warn-info/internal",
    method: "post",
    data: data,
  });
}
//单条内部预警详情查询
export function queryWarningInnerDetail(id) {
  return request({
    url: `/system/warn-info/internal/${id}`,
    method: "get",
  });
}

//外部预警列表查询
export function queryWarningOuter(query) {
  return request({
    url: "/system/warn-info/external",
    method: "get",
    params: query,
  });
}
//外部预警新增
export function addWarningOuter(data) {
  return request({
    url: "/system/warn-info/external",
    method: "post",
    data: data,
  });
}
//单条外部预警详情查询
export function queryWarningOuterDetail(id) {
  return request({
    url: `/system/warn-info/external/${id}`,
    method: "get",
  });
}
//危险区预警响应分页查询
export function queryWarningResponse(query) {
  return request({
    url: "/system/hazardous/warn-feedback",
    method: "get",
    params: query,
  });
}
//危险区预警响应更改状态
export function updateWarningResponse(data) {
  return request({
    url: "/system/hazardous/warn-feedback/status",
    method: "post",
    data: data,
  });
}
//编辑转移人数
export function updateTransferNum(data) {
  return request({
    url: "/system/hazardous/warn-feedback/peo-num",
    method: "post",
    data: data,
  });
}

//文件下载
export function downloadByMain(data) {
  return downloadByNew(
    `/file/download`,
    // method: 'post',
    data,
    data.fileName
  );
}

//风险隐患管理
// 新增或修改风险隐患
export function addOrUpdateRiskProblem(data) {
  return request({
    url: "/risks/risk-problem",
    method: "post",
    data: data,
  });
}

// 查询风险隐患
export function queryRiskProblem(params) {
  return request({
    url: "/risks/risk-problem",
    method: "get",
    params,
  });
}

// 删除风险隐患
export function deleteRiskProblem(id) {
  return request({
    url: `/risks/risk-problem/${id}`,
    method: "delete",
  });
}

// 根据风险隐患id查询对应危险因素及影响村社
export function queryRiskProblemFactor(id) {
  return request({
    url: `/risks/risk-problem/factor/${id}`,
    method: "get",
  });
}

// 删除危险因素
export function deleteRiskProblemFactor(id) {
  return request({
    url: `/risks/risk-problem/factor/${id}`,
    method: "delete",
  });
}

// 新增或修改危险因素
export function addOrUpdateRiskProblemFactor(data) {
  return request({
    url: "/risks/risk-problem/factor",
    method: "post",
    data,
  });
}

//企事业单位管理
// 获取企业列表信息
export function queryEnterpriseList(params) {
  return request({
    url: "/risks/enterprise",
    method: "get",
    params,
  });
}

// 新增或修改企业信息
export function addOrUpdateEnterprise(data) {
  return request({
    url: "/risks/enterprise",
    method: "post",
    data,
  });
}

// 删除企业信息
export function deleteEnterprise(id) {
  return request({
    url: `/risks/enterprise/${id}`,
    method: "delete",
  });
}

//村社转移安置点管理
// 查询村社转移安置列表
export function queryVillagePlacementList(params) {
  return request({
    url: "/risks/village/placement",
    method: "get",
    params,
  });
}

// 新增村社转移安置
export function addVillagePlacement(data) {
  return request({
    url: "/risks/village/placement",
    method: "post",
    data,
  });
}

// 查询村社转移安置详情
export function queryVillagePlacementDetail(id) {
  return request({
    url: `/risks/village/placement/${id}`,
    method: "get",
  });
}

// 更新村社转移安置
export function updateVillagePlacement(data, id) {
  return request({
    url: `/risks/village/placement/${id}`,
    method: "post",
    data,
  });
}

// 删除村社转移安置
export function deleteVillagePlacement(id) {
  return request({
    url: `/risks/village/placement/${id}`,
    method: "delete",
  });
}

// 村社与测站关联关系
// 查询村社与测站列表
export function queryVillageStationList(params) {
  return request({
    url: "/risks/village/station",
    method: "get",
    params,
  });
}

// 查询村社和测站详情
export function queryVillageStationDetail(villageCode) {
  return request({
    url: `/risks/village/station/${villageCode}`,
    method: "get",
  });
}

// 保存村社和测站关联关系
export function saveVillageStationRelation(villageCode, data) {
  return request({
    url: `/risks/village/station/${villageCode}`,
    method: "post",
    data,
  });
}

// 村社转移人数导入
export function importTransferNum(data) {
  return request({
    url: "/system/hazardous/warn-feedback/excel/peo-num",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 查询视频平台列表
export function getVideoPlatformList(params) {
  return request({
    url: '/system/video-platform',
    method: 'get',
    params
  });
}

// 新增视频平台
export function addVideoPlatform(data) {
  return request({
    url: '/system/video-platform',
    method: 'post',
    data
  });
}

// 修改视频平台
export function updateVideoPlatform(data) {
  return request({
    url: '/system/video-platform/modify',
    method: 'post',
    data
  });
}

// 删除视频平台
export function deleteVideoPlatform(id) {
  return request({
    url: `/system/video-platform/${id}`,
    method: 'delete'
  });
}
