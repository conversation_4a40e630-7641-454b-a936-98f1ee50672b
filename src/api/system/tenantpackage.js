import request from '@/utils/request'

// 查询租户套餐列表
export function listTenantpackage(query) {
  return request({
    url: '/system/tenant-package',
    method: 'get',
    params: query
  })
}

// 查询租户套餐详细
export function getTenantpackage(id) {
  return request({
    url: '/system/tenant-package/' + id,
    method: 'get'
  })
}

// 新增租户套餐
export function addTenantpackage(data) {
  return request({
    url: '/system/tenant-package',
    method: 'post',
    data: data
  })
}

// 修改租户套餐
export function updateTenantpackage(data) {
  return request({
    url: '/system/tenant-package/'+data.id,
    method: 'post',
    data: data
  })
}

// 删除租户套餐
export function delTenantpackage(id) {
  return request({
    url: '/system/tenant-package/' + id,
    method: 'delete'
  })
}

// 获取租户套餐精简信息列表
export function getTenantPackageList() {
  return request({
    url: '/system/tenant-package/get-simple-list',
    method: 'get'
  })
}

// 查询菜单列表 有按钮
export function listSimpleAllMenus() {
  return request({
    url: '/system/menu/tenant/list-all-simple',
    method: 'get'
  })
}
// 查询菜单列表  无按钮
export function listSimpleMenus() {
  return request({
    url: '/system/menu/tenant/list-simple',
    method: 'get'
  })
}