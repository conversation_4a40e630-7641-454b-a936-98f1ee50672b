import request from '@/utils/request'

// 查询模型列表
export function taskModelList(query) {
    return request({
        url: '/forecast/forecast-dispatch-model',
        method: 'get',
        params: query
    })
}
//新增模型列表
export function addTaskModel(data) {
    return request({
        url: '/forecast/forecast-dispatch-model',
        method: 'post',
        data
    })
}
//停用/启用模型
export function changeTaskModel(data) {
    return request({
        url: '/forecast/forecast-dispatch-model/status',
        method: 'post',
        data
    })
}
//删除模型
export function delTaskModel(id) {
    return request({
        url: `/forecast/forecast-dispatch-model/${id}`,
        method: 'delete',
    })
}
//更新模型
export function updateTaskModel(id, data) {
    return request({
        url: `/forecast/forecast-dispatch-model/${id}`,
        method: 'post',
        data: data
    })
}
//模型详情
export function taskModelDetail(id) {
    return request({
        url: `/forecast/forecast-dispatch-model/${id}`,
        method: 'get',
    })
}
//查询方案列表
export function taskPlanList(query) {
    return request({
        url: `/forecast/forecast-dispatch-plan`,
        method: 'get',
        params: query
    })
}

// 查询预报方案列表
export function getForecastSchemeList(query) {
    return request({
        url: `/system/forecast-scheme`,
        method: 'get',
        params: query
    })
}

// 新增和编辑预报方案
export function saveForecastScheme(data) {
    return request({
        url: `/system/forecast-scheme`,
        method: 'post',
        data
    })
}

//获取方案详情
export function taskPlanDetail(id) {
    return request({
        url: `/forecast/forecast-dispatch-plan/${id}`,
        method: 'get',
    })
}
//新增方案列表
export function addTaskPlan(data) {
    return request({
        url: `/forecast/forecast-dispatch-plan`,
        method: 'post',
        data,
    })
}
//更新方案
export function updateTaskPlan(id, data) {
    return request({
        url: `/forecast/forecast-dispatch-plan/${id}`,
        method: 'post',
        data
    })
}
//删除方案根据id
export function delTaskPlan(id) {
    return request({
        url: `/forecast/forecast-dispatch-plan/${id}`,
        method: 'delete',
    })
}

// #region ------------------ 预报成果 S ------------------
// 预报成果列表
export function forecastResultList(query) {
    return request({
        url: `/system/forecast-result/forecast_result_list`,
        method: 'get',
        params: query
    })
}

// 预报成果详情
export function forecastResultDetail(id) {
    return request({
        url: `/system/forecast-result/forecast_result_info/${id}`,
        method: 'get',
    })
}

// 预报成果详情 - 编辑用
export function forecastResultEdit(id) {
    return request({
        url: `/system/forecast-result/forecast_result_edit/${id}`,
        method: 'get',
    })
}

// 保存-预报成果
export function saveForecastResult(data) {
    return request({
        url: `/system/forecast-result/save`,
        method: 'post',
        data
    })
}

// 预报-预报成果
export function forecastResultForecast(data) {
    return request({
        url: `/system/forecast-result/forecast`,
        method: 'post',
        data
    })
}

// 获取小流域实测降雨数据
export function getObservedRainfall(query) {
    return request({
        url: `/system/forecast-result/observed_rainfall`,
        method: 'get',
        params: query
    })
}

// 获取小流域预报降雨数据
export function getPredictedRainfall(query) {
    return request({
        url: `/system/forecast-result/predicted_rainfall`,
        method: 'get',
        params: query
    })
}

// 获取入流断面的入流量
export function getInflowList(query) {
    return request({
        url: `/system/forecast-result/forecast_result_section`,
        method: 'get',
        params: query
    })
}

// 获取人工降雨数据
export function getDesignRainfall(data) {
    return request({
        url: `/system/forecast-result/design_rainfall`,
        method: 'post',
        data
    })
}

// 预报成果 - 发布任务
export function publishForecastResult(id) {
    return request({
        url: `/system/forecast-result/forecast_result_publish/${id}`,
        method: 'get',
    })
}

// 下架预报成果
export function downForecastResult(id) {
    return request({
        url: `/system/forecast-result/forecast_result_down/${id}`,
        method: 'get',
    })
}

// 删除预报成果
export function deleteForecastResult(id) {
    return request({
        url: `/system/forecast-result/${id}`,
        method: 'delete',
    })
}

// #endregion ------------------ 预报成果 E ------------------

//查询任务列表

//查询任务列表
export function taskWorkList(query) {
    return request({
        url: `/forecast/forecast-dispatch-task`,
        method: 'get',
        params: query
    })
}
//新增任务列表
export function addWorkList(data) {
    return request({
        url: `/forecast/forecast-dispatch-task`,
        method: 'post',
        data
    })
}
//获取任务详情
export function getWorkDetail(id) {
    return request({
        url: `/forecast/forecast-dispatch-task/${id}`,
        method: 'get',
    })
}
//更新任务详情
export function updateTaskWork(id, data) {
    return request({
        url: `/forecast/forecast-dispatch-task/${id}`,
        method: 'post',
        data
    })
}
export function delTaskWork(id) {
    return request({
        url: `/forecast/forecast-dispatch-task/${id}`,
        method: 'delete',
    })
}
//任务计算接口
export function computeWork(id) {
    return request({
        url: `/forecast/forecast-dispatch-task/calculate/${id}`,
        method: 'get',
    })
}
//自动预报查询列表
export function autoTaskList(query) {
    return request({
        url: `/forecast/automatic-forecast`,
        method: 'get',
        params: query
    })
}
//新增自动预报
export function addAutoTask(data) {
    return request({
        url: `/forecast/automatic-forecast`,
        method: 'post',
        data
    })
}
//更新自动任务
export function updateAutoTask(data) {
    return request({
        url: `/forecast/automatic-forecast/edit`,
        method: 'post',
        data
    })
}
//删除自动任务
export function deleteAutoTask(query) {
    return request({
        url: `/forecast/automatic-forecast`,
        method: 'delete',
        params: query
    })
}
//发布/下架 任务
export function publishWork(data) {
    return request({
        url: `/forecast/forecast-dispatch-task/publish`,
        method: 'post',
        data
    })
}
//查询任务对应的一维信息 漳渭河流域
export function queryOneDimensionalWaterInfo(query) {
    return request({
        url: `/forecast/forecast-dispatch-task/one-dimensional-data/${query}`,
        method: 'get',

    })
}
//获取预报降雨的详情
export function getForecastRain(data) {
    return request({
        url: `/forecast/forecast-dispatch-task/rainfall`,
        method: 'post',
        data
    })
}
//新增修改预报降雨的数据
export function updateForecastRainData(data) {
    return request({
        url: `/forecast/forecast-dispatch-task/rainfall`,
        method: 'post',
        data
    })
}