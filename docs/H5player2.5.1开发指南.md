# h5player

> 海康威视播放器

## 描述

h5player 是一个基于 HTML5 的流式网络视频播放器，无需安装浏览器插件即可通过 websocket 协议向媒体服务取流播放多种格式的音视频流。

## 版本

当前版本 2.5.1

## 媒体版本限制

媒体网关:mgc_V5.13.100 版本及以上

## 使用注意事项

1、使用 wss 协议时，由于媒体后台使用的是自签名证书，所以需要安装平台登录页面根证书后重启浏览器
2、在集成过程中 new JSPlugin 时候必填 szBasePath 变量, // 必填,与 h5player.min.js 的引用目录一致

## 浏览器限制以及编解码支持情况

以下数据都是在 pc chrome80+测试所得，更详细的数据请看功能性能规格文档

| 解码方式 |                   浏览器限制                   |
| :------: | :--------------------------------------------: |
| 高级模式 | 当前支持 Chrome80+，ios safari,andriod browser |
| 普通模式 |       除 IE 和 IOS Safari 外，基本都支持       |

| 解码方式 | 视频编码格式 | 音频编码格式 |       |       |      |        |      |       |     |     |
| -------- | ------------ | ------------ | ----- | ----- | ---- | ------ | ---- | ----- | --- | --- |
| H264     | H265         | AAC          | AACLD | ADPCM | G711 | G722_1 | G726 | MP2L2 |     |     |
| 高级模式 | ✓            | ✓            | ✓     | ✓     | ✓    | ✓      | ✓    | ✓     | ✓   | ✓   |
| 普通模式 | ✓            | ✓            | ✓     | ✓     | ✓    | ✓      | ✓    | ✓     | ✓   | ✓   |

| 解码方式       | 视频编码格式    | 分辨率         | 视频参数(bps\*fps) | 解码路数（系统：Win10） |     |     |
| -------------- | --------------- | -------------- | ------------------ | ----------------------- | --- | --- |
| CPU： I7_8700K | CPU： I5-9400/F | CPU： I3-8100  |                    |                         |     |     |
| 显卡：RTX2080  | 显卡：GTX1050TI | 显卡：GT1030D5 |                    |                         |     |     |
| 高级模式       | H265&H264       | 1080P          | 4M \* 25           | 2                       | 2   | 2   |
| 720P           | 2M \* 25        | 4              | 4                  | 4                       |     |     |
| 普通模式       | H265&H264       | 1080P          | 4M \* 25           | 24                      | 20  | 9   |
| 720P           | 2M \* 25        | 32             | 32                 | 16                      |     |     |

## 接口调用流程图

其余控制接口调用都在 JS_Play 后调用Ï

```mermaid
graph TD
    st[开始] --> op1[创建实例：new JSPlugin]
    op1 --> op2[事件初始化：JS_SetWindowControlCallback]
    op2 --> op3[调用预览/回放接口：JS_Play]
    op3 --> op4[调用停止接口：JS_Stop]
    op4 --> e[结束]
    style st fill:#f96,stroke:#333,stroke-width:4px
    style e fill:#f96,stroke:#333,stroke-width:4px
    style op1 fill:#d3d3d3,stroke:#333,stroke-width:2px
    style op2 fill:#d3d3d3,stroke:#333,stroke-width:2px
    style op3 fill:#d3d3d3,stroke:#333,stroke-width:2px
    style op4 fill:#d3d3d3,stroke:#333,stroke-width:2px
```

## 引入

直接用<script>标签引入

```html
<!-- h5player -->
<script src="h5player.min.js"></script>
```

## 创建实例

```html
<body>
  <div id="play_window"></div>
  <script>
    var curIndex = 0; // 当前窗口下标
    var myPlugin = new JSPlugin({
      szId: 'play_window', //父窗口id，需要英文字母开头 必填
      szBasePath: './dist', // 必填,与h5player.min.js的引用目录一致
      mseWorkerEnable: false,//是否开启多线程解码，分辨率大于1080P建议开启，否则可能卡顿
			bSupporDoubleClickFull: true,//是否支持双击全屏，true-双击是全屏；false-双击无响应
      // 当容器div#play_window有固定宽高时，可不传iWidth和iHeight，窗口大小将自适应容器宽高
      // iWidth: 600,
      // iHeight: 400,

      // 分屏播放，默认最大分屏4*4
      // iMaxSplit: 4,
      // iCurrentSplit: 1,
    })
  </script>
</body>
```

## 接口说明

### 事件初始化: JS_SetWindowControlCallback(events)

参数：

| 参数名        | 类型   | 说明                   | 必需 |
| ------------- | ------ | ---------------------- | ---- |
| events        | Object | 事件对应的处理函数集合 | 是   |
| 返回：Promise |        |                        |      |

```js
JS_SetWindowControlCallback({
  windowEventSelect: function (index) {  //插件选中窗口回调
      curIndex = index;
      console.log('selectWindow changed:', index);
  },
  pluginErrorHandler: function (index, iErrorCode, oError) {  //插件错误回调，iErrorCode为错误码，oError错误来源（样式：errPlatformIp=************;errPlatformName=本级）
      // do you want...
      // 取流失败，流中断等错误都会触发该回调函数，请自行对照错误码表进行判断。
      // 业务上层可在此做一些个性化操作，如：个性化错误信息展示，重新取流等。
  },
  windowEventOver: function (index) {  //鼠标移过回调
      // do you want...
  },
  windowEventOut: function (index) {  //鼠标移出回调
      // do you want...
  },
  windowEventUp: function (index) {  //鼠标mouseup事件回调
      // do you want...
  },
  windowFullCcreenChange: function (bFull) {  //全屏切换回调
      // do you want...
  },
  firstFrameDisplay: function (index, iWidth, iHeight) {  //首帧显示回调
      // do you want...
  },
  performanceLack: function () {  //性能不足回调
      // do you want...
  },
  StreamEnd: function (index) {  //回放结束回调,返回对应测窗口id
      // do you want...
  },
  InterruptStream: function (iWndIndex, interruptTime) {  //断流事件回调，interruptTime为秒级
      console.log('recv InterruptStream windowsId: '+iWndIndex+'interrupt time:'+interruptTime);
  },
  talkPluginErrorHandler: (iErrorCode, oErrorInfo) => {  //对讲错误回调
      console.log('recv talkPluginErrorHandler: '+iErrorCode);
  },
  ThumbnailsEvent: (iWndIndex, eventType, eventCode) => { //缩略图事件回调
    console.log('recv ThumbnailsEvent: '+iWndIndex+", eventType:"+eventType+", eventCode:"+eventCode);
  },
});
```

### 播放: JS_Play(url, config, windowIndex, startTime, endTime)

说明：倒放功能并不是所有设备都支持，云存储和 cvr，nvr 可支持，19 年之后出厂的设备都可支持。
参数：

| 参数名        | 类型     | 说明                                         | 必需                 |
| ------------- | -------- | -------------------------------------------- | -------------------- |
| url           | String   | 流媒体 URL                                   | 是                   |
| config        | Object   | 播放配置                                     | 必传,详情见示例      |
| windowIndex   | Number   | 当前窗口下标                                 | 是                   |
| startTime     | DateTime | 回放开始时间，格式类型：2021-06-29T00:00:00Z | 预览不能填，回放必填 |
| endTime       | DateTime | 回放结束时间，格式类型：2021-06-29T00:00:00Z | 预览不能填，回放必填 |
| 返回：Promise |          |                                              |                      |

```text
let url = document.getElementById('url').value;
let startTime, endTime;
myPlugin.JS_Play(
  url,
  {
    playURL: url, // 流媒体播放时必传
    mode: 0, // 解码类型：0=普通模式; 1=高级模式 默认为0
    PlayBackMode: 1,//1：绝对时间正放; 3 绝对时间倒放 默认为1
    keepDecoder 0: // 0:回收解码资源; 1:不回收解码资源。为1时相同的编码格式，通过直接调用js_play接口切换点位不黑屏。默认0
    token: token, //开启安全认证使用的token，在后端开启安全认证时使用
    // ...
  },
  curIndex, //当前窗口下标

  // 回放参数
  startTime,
  endTime
).then(
  () => {
    console.info('JS_Play success');
    // do you want...
  },
  (err) => {
    console.info('JS_Play failed:', err);
    // do you want...
  }
);
```

### 停止播放: JS_Stop(windowIndex)

参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| windowIndex   | Number | 窗口下标 | 是   |
| 返回：Promise |        |          |      |

```text
myPlugin.JS_Stop(curIndex).then(
  () => {
    console.info('JS_Stop success');
    // do you want...
  },
  (err) => {
    console.info('JS_Stop failed:', err);
    // do you want...
  }
);
```

### 停止所有播放: JS_StopRealPlayAll()

参数：无
返回：Promise

```text
myPlugin.JS_StopRealPlayAll().then(
  () => {
    console.info('JS_StopRealPlayAll success');
    // do you want...
  },
  (err) => {
    console.info('JS_StopRealPlayAll failed');
    // do you want...
  }
);
```

### 开启声音: JS_OpenSound(windowIndex)

参数：
说明：一个实例下只支持一路声音播放，开启下一路会默认关闭上一路声音。倍数和倒放不支持声音播放

| 参数名        | 类型   | 说明     | 必需                                 |
| ------------- | ------ | -------- | ------------------------------------ |
| windowIndex   | Number | 窗口下标 | 否，不传时默认开启当前选中窗口的声音 |
| 返回：Promise |        |          |                                      |

```text
myPlugin.JS_OpenSound(curIndex).then(
  () => {
    console.info('JS_OpenSound success');
    // do you want...
  },
  (err) => {
    console.info('JS_OpenSound failed');
    // do you want...
  }
);
```

### 关闭声音: JS_CloseSound(windowIndex)

参数：

| 参数名        | 类型   | 说明     | 必需                                 |
| ------------- | ------ | -------- | ------------------------------------ |
| windowIndex   | Number | 窗口下标 | 否，不传时默认关闭当前选中窗口的声音 |
| 返回：Promise |        |          |                                      |

```text
myPlugin.JS_CloseSound(curIndex).then(
  () => {
    console.info('JS_CloseSound success');
    // do you want...
  },
  (err) => {
    console.info('JS_CloseSound failed');
    // do you want...
  }
);
```

### 设置音量: JS_SetVolume(windowIndex, volumn)

参数：

| 参数名        | 类型   | 说明     | 必需                    |
| ------------- | ------ | -------- | ----------------------- |
| windowIndex   | Number | 窗口下标 | 是                      |
| volumn        | Number | 音量大小 | 是，范围 1~100，默认 50 |
| 返回：Promise |        |          |                         |

```text
myPlugin.JS_SetVolume(curIndex, volumn).then(
  () => {
    console.info('JS_SetVolume success');
    // do you want...
  },
  (err) => {
    console.info('JS_SetVolume failed');
    // do you want...
  }
);
```

### 获取当前音量: JS_GetVolume(windowIndex)

参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| windowIndex   | Number | 窗口下标 | 是   |
| 返回：Promise |        |          |      |

```text
myPlugin.JS_GetVolume(curIndex).then(
  (volumn) => { //在不设置音量的情况下默认50
    console.info('JS_GetVolume success', volumn);
    // do you want...
  },
  (err) => {
    console.info('JS_GetVolume failed');
    // do you want...
  }
);
```

### 录像: JS_StartSaveEx(windowIndex, fileName, idstType)

说明：倒放情况下不支持该功能
参数：

| 参数名        | 类型   | 说明                   | 必需                                                       |
| ------------- | ------ | ---------------------- | ---------------------------------------------------------- |
| windowIndex   | Number | 窗口下标               | 是                                                         |
| fileName      | String | 文件名(保证全局唯一性) | 是，可不带后缀，默认为.mp4                                 |
| idstType      | Number | 录像文件类型           | 是，2-ps 5-mp4 ,mp4 录制音频限制，仅支持 AAC、G711A、G711U |
| 返回：Promise |        |                        |                                                            |

```text
let fileName = 'fileName.mp4';
myPlugin.JS_StartSaveEx(curIndex, fileName, 2).then(
  () => {
    console.info('JS_StartSave success');
    // do you want...
  },
  (err) => {
    console.info('JS_StartSave failed');
    // do you want...
  }
);
```

### 停止录像并保存文件: JS_StopSave(windowIndex)

参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| windowIndex   | Number | 窗口下标 | 是   |
| 返回：Promise |        |          |      |

```text
let fileName = 'fileName.mp4';
myPlugin.JS_StopSave(windowIndex).then(
  () => {
    console.info('JS_StopSave success');
    // do you want...
  },
  (err) => {
    console.info('JS_StopSave failed');
    // do you want...
  }
);
```

### 抓图: JS_CapturePicture(windowIndex, fileName, fileType, callback)

参数：

| 参数名        | 类型    | 说明     | 必需                            |
| ------------- | ------- | -------- | ------------------------------- |
| windowIndex   | Number  | 窗口下标 | 是                              |
| fileName      | String  | 文件名   | 是                              |
| fileType      | String  | 文件类型 | 是，'JPEG'                      |
| callback      | Fuction | 回调函数 | 否,回调图片数据，不进行下载操作 |
| 返回：Promise |         |          |                                 |

```text
let fileName = 'img';
let fileType = 'JPEG';
//下载到本地
myPlugin.JS_CapturePicture(curIndex, fileName, fileType).then(
  () => {
    console.info('JS_CapturePicture success');
    // do you want...
  },
  (err) => {
    console.info('JS_CapturePicture failed');
    // do you want...
  }
);
//不进行下载，数据回调
myPlugin.JS_CapturePicture(curIndex, fileName, fileType，imageData => {
    console.info('JS_CapturePicture success', imageData); //2.1.0开始全是base64，之前的版本存在blob或者是base64
    // do you want...
})
```

### 回放：[JS_Play()](#play)

### 停止回放：[JS_Stop()](#stop)

### 暂停回放: JS_Pause(windowIndex)

参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| windowIndex   | Number | 窗口下标 | 是   |
| 返回：Promise |        |          |      |

```text
myPlugin.JS_Pause(curIndex).then(
  () => {
    console.info('JS_Pause success');
    // do you want...
  },
  (err) => {
    console.info('JS_Pause failed');
    // do you want...
  }
);
```

### 恢复回放: JS_Resume(windowIndex, bForward)

参数：

| 参数名        | 类型    | 说明                                         | 必需               |
| ------------- | ------- | -------------------------------------------- | ------------------ |
| windowIndex   | Number  | 窗口下标                                     | 是                 |
| bForward      | boolean | 单帧情况下，正向恢复为 true,倒向恢复为 false | 否，单帧时入参即可 |
| 返回：Promise |         |                                              |                    |

```text
myPlugin.JS_Resume(curIndex).then(
  () => {
    console.info('JS_Resume success');
    // do you want...
  },
  (err) => {
    console.info('JS_Resume failed');
    // do you want...
  }
);
```

### 开始对讲: JS_StartTalk(szTalkUrl, oParam)

特殊声明：由于浏览器的安全限制，该功能只能在 https 域下使用，只支持一路对讲
参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| szTalkUrl     | String | 对讲 URL | 是   |
| oParam        | object | 对讲 URL | 否   |
| 返回：Promise |        |          |      |

```text
myPlugin.JS_StartTalk(szTalkUrl, {
  token: token //安全认证token
  }).then(
  () => {
    console.info('JS_StartTalk success');
    // do you want...
  },
  (err) => {
    console.info('JS_StartTalk failed');
    // do you want...
  }
);
```

### 停止对讲: JS_StopTalk()

返回：Promise

```text
myPlugin.JS_StopTalk().then(
  () => {
    console.info('JS_StopTalk success');
    // do you want...
  },
  (err) => {
    console.info('JS_StopTalk failed');
    // do you want...
  }
);
```

### 设置对讲音量：JS_TalkSetVolume(nVolume)

参数：

| 参数名        | 类型   | 说明              | 必需 |
| ------------- | ------ | ----------------- | ---- |
| nVolume       | number | 音量大小（0-100） | 否   |
| 返回：Promise |        |                   |      |

```text
myPlugin.设置对讲音量：JS_TalkSetVolume(nVolume).then(
  () => {
    console.info('JS_TalkSetVolume success');
    // do you want...
  },
  (err) => {
    console.info('JS_TalkSetVolume failed');
    // do you want...
  }
);
```

### 获取对讲音量: JS_TalkGetVolume()

返回：Promise

```text
myPlugin.JS_TalkGetVolume().then(
  () => {
    console.info('JS_TalkGetVolume success');
    // do you want...
  },
  (err) => {
    console.info('JS_TalkGetVolume failed');
    // do you want...
  }
);
```

### 录制对讲声音: JS_StartSaveTalk(pcmname, type = 2)

| 参数名        | 类型   | 说明                                                | 必需       |
| ------------- | ------ | --------------------------------------------------- | ---------- |
| pcmname       | string | 音频文件名字                                        | 是         |
| type          | number | 录制类型=> 0:设备的声音，1:客户端的声音, 2:双向声音 | 否，默认 2 |
| 返回：Promise |        |                                                     |            |

```text
let fileName = `${moment().format('YYYYMMDDHHmmss')}.mp3`
myPlugin.JS_StartSaveTalk(fileName, 2).then(
  () => {
    console.info('JS_StartSaveTalk success');
    // do you want...
  },
  (err) => {
    console.info('JS_StartSaveTalk failed');
    // do you want...
  }
);
```

### 关闭录制对讲声音: JS_StopSaveTalk()

返回：Promise

```text
myPlugin.JS_StopSaveTalk().then(
  () => {
    console.info('JS_StopSaveTalk success');
    // do you want...
  },
  (err) => {
    console.info('JS_StopSaveTalk failed');
    // do you want...
  }
);
```

### 录像、抓图功能同预览播放

### 回放快放: JS_Fast(windowIndex)

调节播放倍速为当前播放速度的 2 倍，最大为 8 倍。
参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| windowIndex   | Number | 窗口下标 | 是   |
| 返回：Promise |        |          |      |

```text
myPlugin.JS_Fast(curIndex).then(
  rate => {
    console.info('JS_Fast success:', rate);
    // do you want...
  },
  (err) => {
    console.info('JS_Fast failed');
    // do you want...
  }
);
```

### 回放慢放: JS_Slow(windowIndex)

调节播放倍速为当前播放速度的 1/2 倍，最小为 1/8 倍。
参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| windowIndex   | Number | 窗口下标 | 是   |
| 返回：Promise |        |          |      |

```text
myPlugin.JS_Slow(curIndex).then(
  rate => {
    console.info('JS_Slow success:', rate);
    // do you want...
  },
  (err) => {
    console.info('JS_Slow failed');
    // do you want...
  }
);
```

### 回放定位: JS_Seek(windowIndex, stratTime, endTime)

说明：单帧进情况下进行定位就是前进播放，单帧退情况下进行定位就是后退播放
参数：

| 参数名        | 类型     | 说明                                     | 必需 |
| ------------- | -------- | ---------------------------------------- | ---- |
| windowIndex   | Number   | 窗口下标                                 | 是   |
| stratTime     | DateTime | 开始时间，格式类型：2021-06-29T00:00:00Z | 是   |
| endTime       | DateTime | 结束时间，格式类型：2021-06-29T00:00:00Z | 是   |
| 返回：Promise |          |                                          |      |

```text
myPlugin.JS_Seek(curIndex，zStartDate, szEndDate).then(
  () => {
    console.info('JS_Seek success');
    // do you want...
  },
  (err) => {
    console.info('JS_Seek failed');
    // do you want...
  }
);
```

### 回放单帧进: JS_FrameForward(windowIndex)

说明：该功能在 https+chrome94 级以上+H5player2.3.0+普通模式支持。
大于 1 倍数情况下不支持单帧操作
参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| windowIndex   | Number | 窗口下标 | 是   |
| 返回：Promise |        |          |      |

```text
myPlugin.JS_FrameForward(curIndex).then(
  () => {
    console.info('JS_FrameForward success');
    // do you want...
  },
  (err) => {
    console.info('JS_FrameForward failed');
    // do you want...
  }
);
```

### 回放单帧退: JS_FrameBack(windowIndex)

说明：该功能在 https+chrome94 级以上+H5player2.3.0+普通模式支持。该功能存在媒体版本限制，需要更新组件 vnsc1.16.101+、mgc5.16.101+、dac1.15.102+、vod5.15.100+、sac1.8.100+、ncg5.15.101+。
高级模式下该接口调用一次会大概跳 12 帧，倒放时缓存帧有限，效果不佳请知悉
大于 1 倍数情况下不支持单帧操作
参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| windowIndex   | Number | 窗口下标 | 是   |
| 返回：Promise |        |          |      |

```text
myPlugin.JS_FrameBack(curIndex).then(
  () => {
    console.info('JS_FrameBack success');
    // do you want...
  },
  (err) => {
    console.info('JS_FrameBack failed');
    // do you want...
  }
);
```

### 电子放大: JS_EnableZoom(windowIndex)

说明：该功能在 https+chrome94 级以上+H5player2.3.0+普通模式支持
参数：

| 参数名        | 类型   | 说明     | 必需                                         |
| ------------- | ------ | -------- | -------------------------------------------- |
| windowIndex   | Number | 窗口下标 | 否，不传时默认开启当前选中窗口的电子放大功能 |
| 返回：Promise |        |          |                                              |

```text
myPlugin.JS_EnableZoom(curIndex).then(
  () => {
    console.info('JS_EnableZoom success');
    // do you want...
  },
  (err) => {
    console.info('JS_EnableZoom failed');
    // do you want...
  }
);
```

### 关闭电子放大: JS_DisableZoom(windowIndex)

参数：

| 参数名        | 类型   | 说明     | 必需                                         |
| ------------- | ------ | -------- | -------------------------------------------- |
| windowIndex   | Number | 窗口下标 | 否，不传时默认关闭当前选中窗口的电子放大功能 |
| 返回：Promise |        |          |                                              |

```text
myPlugin.JS_DisableZoom(curIndex).then(
  () => {
    console.info('JS_DisableZoom success');
    // do you want...
  },
  (err) => {
    console.info('JS_DisableZoom failed');
    // do you want...
  }
);
```

### 开启/关闭智能信息展示（高级模式功能）: JS_RenderALLPrivateData(iWndNum, bOpenFlag)

参数：

| 参数名        | 类型    | 说明      | 必需 |
| ------------- | ------- | --------- | ---- |
| iWndNum       | Number  | 窗口下标  | 是   |
| bOpenFlag     | Boolean | 开启/关闭 | 是   |
| 返回：Promise |         |           |      |

```text
myPlugin.JS_RenderALLPrivateData(iWndNum, true).then(
  () => {
    console.info('JS_RenderALLPrivateData success');
    // do you want...
  },
  (err) => {
    console.info('JS_RenderALLPrivateData failed');
    // do you want...
  }
);
```

### 分屏: JS_ArrangeWindow(splitNum)

参数：

| 参数名        | 类型     | 说明   | 必需           |
| ------------- | -------- | ------ | -------------- |
| splitNum      | Number   | 分隔数 | 是， 范围：1~4 |
| 返回：Promise |          |        |                |
| 分隔数        | 分屏效果 |        |                |
| ----          | ----     |        |                |
| 1             | 1x1      |        |                |
| 2             | 2x2      |        |                |
| 3             | 3x3      |        |                |
| 4             | 4x4      |        |                |

```text
myPlugin.JS_ArrangeWindow(splitNum).then(
  () => {
    console.info('JS_ArrangeWindow success');
    // do you want...
  },
  (err) => {
    console.info('JS_ArrangeWindow failed');
    // do you want...
  }
);
```

### 切换选中窗口: JS_SelectWnd(windowIndex)

参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| windowIndex   | Number | 窗口下标 | 是   |
| 返回：Promise |        |          |      |

```text
myPlugin.JS_SelectWnd(windowIndex).then(
  () => {
    console.info('JS_SelectWnd success');
    // do you want...
  },
  (err) => {
    console.info('JS_SelectWnd failed');
    // do you want...
  }
);
```

### 整体全屏: JS_FullScreenDisplay(isFull)

参数：

| 参数名        | 类型    | 说明     | 必需 |
| ------------- | ------- | -------- | ---- |
| isFull        | Boolean | 是否全屏 | 是   |
| 返回：Promise |         |          |      |

```text
myPlugin.JS_FullScreenDisplay(true).then(
  () => {
    console.info('JS_FullScreenDisplay success');
    // do you want...
  },
  (err) => {
    console.info('JS_FullScreenDisplay failed');
    // do you want...
  }
);
```

### 单窗口全屏: JS_FullScreenSingle(windowIndex)

参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| windowIndex   | Number | 窗口下标 | 是   |
| 返回：Promise |        |          |      |

```text
myPlugin.JS_FullScreenSingle(curIndex).then(
  () => {
    console.info('JS_FullScreenSingle success');
    // do you want...
  },
  (err) => {
    console.info('JS_FullScreenSingle failed');
    // do you want...
  }
);
```

### 设置窗口大小：JS_Resize (iWidth, iHeight)

参数：

| 参数名        | 类型   | 说明         | 必需                     |
| ------------- | ------ | ------------ | ------------------------ |
| iWidth        | Number | 播放页面宽度 | 否，不传时默认父窗口大小 |
| iHeight       | Number | 播放页面高度 | 否，不传时默认父窗口大小 |
| 返回：Promise |        |              |                          |

```text
myPlugin.JS_Resize().then(
  () => {
    console.info('JS_Resize success');
    // do you want...
  },
  (err) => {
    console.info('JS_Resize failed');
    // do you want...
  }
);
```

### 获取 OSD 时间：JS_GetOSDTime (windowIndex)

参数：

| 参数名                                                                                 | 类型   | 说明     | 必需 |
| -------------------------------------------------------------------------------------- | ------ | -------- | ---- |
| windowIndex                                                                            | Number | 窗口下标 | 是   |
| 返回：Promise                                                                          |        |          |      |
| 成功返回从 1970-1-1 00:00:00 到该日期对象的毫秒数,但是获取的精度只到秒级别，毫秒都是 0 |        |          |      |

```text
myPlugin.JS_GetOSDTime(curIndex).then(
  (time) => {
    console.info("osdTime:", new Date(time));
    // do you want...
  },
  (err) => {
    console.info('JS_GetOSDTime failed');
    // do you want...
  }
);
```

### 获取音视频信息：JS_GetVideoInfo (windowIndex)

参数：

| 参数名        | 类型   | 说明     | 必需 |
| ------------- | ------ | -------- | ---- |
| windowIndex   | Number | 窗口下标 | 是   |
| 返回：Promise |        |          |      |
| videoInfo = { |        |          |      |

```text
  VideType: 'h264',  //视频编码格式
  audioType: 'without',//音频编码格式
  width: 0,//视频分辨率的宽
  height: 0,//视频分辨率的高
  frameRate: 25,//视频帧率
  bitRate: 2048,//视频码率，单位：Kb/s
  systemFormt: "ps"//视频封装格式
```

};

```text
myPlugin.JS_GetVideoInfo(curIndex).then(
  (VideoInfo) => {
    console.info("VideoInfo:", VideoInfo);
    // do you want...
  },
  (err) => {
    console.info('JS_GetVideoInfo failed');
    // do you want...
  }
);
```

### 设置取流连接超时时间：JS_SetConnectTimeOut (windowIndex, nTime)

参数：
说明：该接口不调用时默认 12s,在 JS_Play 之前调用

| 参数名        | 类型   | 说明     | 必需                         |
| ------------- | ------ | -------- | ---------------------------- |
| windowIndex   | Number | 窗口下标 | 是                           |
| nTime         | Number | 超时时间 | 是，不传时默认 12 秒，单位秒 |
| 返回：Promise |        |          |                              |

```text
myPlugin.JS_SetConnectTimeOut(curIndex, nTime).then(
  () => {
    console.info('JS_SetConnectTimeOut success');
    // do you want...
  },
  (err) => {
    console.info('JS_SetConnectTimeOut failed');
    // do you want...
  }
);
```

### 设置私有数据回调：JS_SetAdditionDataCB(windowIndex, szType, cbCallback)

参数：

| 参数名      | 类型     | 说明     | 必需                                                                                                                                                                                                           |
| ----------- | -------- | -------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| windowIndex | Number   | 窗口下标 | 是                                                                                                                                                                                                             |
| szType      | Number   | 数据类型 | 是，0x0101: 定制温度 0x0103: 基线温度裸数据 0x0006: 车载行车信息 0x0009: 车载调试信息 0x0007: POS 信息解析回调 0x0010: 汽车电子私有信息 0x0011: 无人机私有信息 0x0804: 数立体云防私有数据 0x000B: 设备扩展信息 |
| cbCallback  | function | 回调函数 | 是，function(dataType, dataStrVersion, dataTimeStamp, additionDataBuffer)                                                                                                                                      |

返回：Promise

```text
myPlugin.JS_SetAdditionDataCB(curIndex, 0x0804, function(dataType,dataStrVersion, dataTimeStamp, additionDataBuffer){
  console.log(" JSPlayM4_AdditionDataCBFun dataType:"+dataType +",dataStrVersion:"+dataStrVersion+",dataTimeStamp:"+dataTimeStamp);
  }).then(
  () => {
    console.info('JS_SetAdditionDataCB success');
    // do you want...
  },
  (err) => {
    console.info('JS_SetAdditionDataCB failed');
    // do you want...
  }
);
```

### 设置 traceId：JS_SetTraceId(windowIndex, bOpenFlag)

说明：traceId 是查询调用链的唯一标识，H5player 不做调用链埋点，只拼接 traceId，traceId 建议在传入 url 的时候自行拼接
拼接规则：取流 url 中无?则拼接?traceId=uuid(随机生成的一个 id 编号);有?则拼接&traceId=uuid
示例：wss://************:6014/proxy/************:559/EUrl/SiSnW6z?traceId=d9d0766e3b3f7a41
参数：

| 参数名      | 类型   | 说明             | 必需                      |
| ----------- | ------ | ---------------- | ------------------------- |
| windowIndex | Number | 窗口下标         | 是                        |
| bOpenFlag   | Number | 是否设置 traceId | 是,该接口不调用默认 false |

返回：Promise

```text
myPlugin.JS_SetTraceId(curIndex, bOpenFlag).then(
  () => {
    console.info('JS_SetTraceId success');
    // do you want...
  },
  (err) => {
    console.info('JS_SetTraceId failed');
    // do you want...
  }
);
```

### 获取 traceId：JS_GetTraceId(windowIndex)

说明：traceId 是查询调用链的唯一标识，H5player 不做调用链埋点，只拼接 traceId，traceId 建议在传入 url 的时候自行拼接，更方便应用层管理，h5player 内部会检测无 traceId 时会默认拼接上
拼接规则：取流 url 中无?则拼接?traceId=uuid(随机生成的一个 id 编号);有?则拼接&traceId=uuid
示例：wss://************:6014/proxy/************:559/EUrl/SiSnW6z?traceId=d9d0766e3b3f7a41
参数：

| 参数名      | 类型   | 说明     | 必需 |
| ----------- | ------ | -------- | ---- |
| windowIndex | Number | 窗口下标 | 是   |

返回：Promise

```text
myPlugin.JS_GetTraceId(curIndex).then(
  (traceId) => {
    console.info('JS_GetTraceId success', traceId);
    // do you want...
  },
  (err) => {
    console.info('JS_GetTraceId failed');
    // do you want...
  }
);
```

### 设置断流时间回调：JS_SetInterruptTime(iWndNum, InterruptTime)

说明：断流的事件统一从 JS_SetWindowControlCallback 的 InterruptStream 回调，InterruptTime 为检测无码流的时间，每过 InterruptTime 回调一次断流事件，未设置默认每过 5s 无码流回调一次断流事件
参数：

| 参数名        | 类型   | 说明                                | 必需 |
| ------------- | ------ | ----------------------------------- | ---- |
| windowIndex   | Number | 窗口下标                            | 是   |
| InterruptTime | Number | 检测断流时间阈值,单位：秒,默认 5 秒 | 是   |

返回：Promise

```text
let InterruptTime = 5;
myPlugin.JS_SetInterruptTime(iWndNum, InterruptTime).then(
  () => {
    console.info('JS_SetInterruptTime success');
    // do you want...
  },
  (err) => {
    console.info('JS_SetInterruptTime failed');
    // do you want...
  }
);
```

### 判断是否支持回放优化：JS_OptimizeCapability(windowIndex, szUrl)

说明：调用该接口，判断是否支持回放优化功能
参数：

| 参数名      | 类型   | 说明     | 必需                                                                   |
| ----------- | ------ | -------- | ---------------------------------------------------------------------- |
| windowIndex | Number | 窗口下标 | 是                                                                     |
| szUrl       | string | url      | 是,未播放时已该 url 为准--精确度低，播放成功后以实际状态为准--精确度高 |

返回：Promise

```text
/******考虑到存在滞后性，公布判断条件:***/
if (szUrl.search("mark") !== -1) {
    let mark = atob(szUrl.split('mark/').pop().split('?').shift());
    if (mark === "storageOptimize=1&mediaOptimize=1") {
      //仅该情况支持
    }
}
/******end****/
myPlugin.JS_OptimizeCapability(curIndex, szUrl).then(
  () => {
      console.info('JS_OptimizeCapability support');
    // do you want...
  },
  (err) => {
    console.error('JS_OptimizeCapability unsupport',err);
    // do you want...
  });
```

### 正倒放切换：JS_ChangeMode(windowIndex)

说明：调用该接口，会快速进行正倒放切换，当正放时会切成倒放；当倒放时会切成正放。该功能在 https+chrome94 级以上+H5player2.3.0+普通模式也支持。该功能存在媒体版本限制，需要更新组件 vnsc1.16.101+、mgc5.16.101+、dac1.15.102+、vod5.15.100+、sac1.8.100+、ncg5.15.101+。
参数：

| 参数名      | 类型   | 说明     | 必需 |
| ----------- | ------ | -------- | ---- |
| windowIndex | Number | 窗口下标 | 是   |

返回：Promise

```text
myPlugin.JS_ChangeMode(curIndex).then(
  () => {
    console.info('JS_ChangeMode success');
    // do you want...
  },
  (err) => {
    console.info('JS_ChangeMode failed');
    // do you want...
  }
);
```

### 开始缩略图：JS_StartVideoThumbnails(windowIndex, url, szStartTime, szStopTime, oOption)

说明：调用该接口，传入的开始时间和结束时间是回放的开始和结束时间。需要更新组件 vnsc1.16.101+、mgc5.16.101+、dac1.15.102+、vod5.15.100+、sac1.8.100+、ncg5.15.101+。
参数：

| 参数名      | 类型   | 说明                                | 必需                                               |
| ----------- | ------ | ----------------------------------- | -------------------------------------------------- |
| windowIndex | Number | 窗口下标                            | 是                                                 |
| url         | string | 回放 url                            | 是                                                 |
| szStartTime | string | 回放的开始时间                      | 是 ，时间格式示例："2022-10-09T08:47:36.000+08:00" |
| szStopTime  | string | 回放的结束时间                      | 是 ，时间格式示例："2022-10-09T08:47:36.000+08:00" |
| oOption     | Object | 缩略图的宽高，snapwidth, snapheight | 是                                                 |

返回：Promise

```text
let oOption = {
  snapwidth:500,
  snapheight:400,
  token:""//安全认证token
}
myPlugin.JS_StartVideoThumbnails(curIndex, url, szStartTime, szStopTime, oOption).then(
  () => {
    console.info('JS_StartVideoThumbnails success');
    // do you want...
  },
  (err) => {
    console.info('JS_StartVideoThumbnails failed');
    // do you want...
  }
);
```

### 获取缩略图：JS_GetVideoThumbnails(windowIndex, videoTime, cbCallback)

说明：该功能存在媒体版本限制，需要更新组件 vnsc1.16.101+、mgc5.16.101+、dac1.15.102+、vod5.15.100+、sac1.8.100+、ncg5.15.101+。
参数：

| 参数名      | 类型   | 说明             | 必需                                               |
| ----------- | ------ | ---------------- | -------------------------------------------------- |
| windowIndex | Number | 窗口下标         | 是                                                 |
| videoTime   | string | 缩略图时间       | 是 ，时间格式示例："2022-10-09T08:47:36.000+08:00" |
| cbCallback  | string | 缩略图的回调函数 | 是                                                 |

返回：Promise

```text
function Thumbnailcb(timestamp,binaryData) {
console.log("timestamp:"+timestamp+", binaryData:"+binaryData)
},
myPlugin.JS_GetVideoThumbnails(curIndex, videoTime, Thumbnailcb).then(
  () => {
    console.info('JS_GetVideoThumbnails success');
    // do you want...
  },
  (err) => {
    console.info('JS_GetVideoThumbnails failed');
    // do you want...
  }
);
```

### 停止缩略图：JS_StopVideoThumbnails(windowIndex)

说明：该功能存在媒体版本限制，需要更新组件 vnsc1.16.101+、mgc5.16.101+、dac1.15.102+、vod5.15.100+、sac1.8.100+、ncg5.15.101+。
参数：

| 参数名      | 类型   | 说明     | 必需 |
| ----------- | ------ | -------- | ---- |
| windowIndex | Number | 窗口下标 | 是   |

返回：Promise

```text
myPlugin.JS_StopVideoThumbnails(curIndex).then(
  () => {
    console.info('JS_StopVideoThumbnails success');
    // do you want...
  },
  (err) => {
    console.info('JS_StopVideoThumbnails failed');
    // do you want...
  }
);
```

### 直接倍数接口：JS_Speed(windowIndex, rate)

说明：>8 倍数场景，需要更新组件 vnsc1.16.101+、mgc5.16.101+、dac1.15.102+、vod5.15.100+、sac1.8.100+、ncg5.15.101+。
参数：

| 参数名      | 类型   | 说明                                                                                      | 必需 |
| ----------- | ------ | ----------------------------------------------------------------------------------------- | ---- |
| windowIndex | Number | 窗口下标                                                                                  | 是   |
| rate        | Number | 倍数，仅支持-8、-4、-2、1、2、4、8、16、32、64 入参,-8 表示 1/8，-4 表示 1/4，-2 表示 1/2 | 是   |

返回：Promise

```text
myPlugin.JS_Speed(curIndex, rate).then(
  rate => {
    console.info('JS_Speed success:',rate);
    // do you want...
  },
  (err) => {
    console.info('JS_Speed failed');
    // do you want...
  }
);
```

### 开启即时回放功能：JS_InstantSetParam(windowIndex, rate)

说明：该功能在 https+chrome94 级以上+H5player2.5.0+普通模式支持，只需要每次在预览成功后调用一次即可
参数：

| 参数名      | 类型   | 说明                                                                                                                                 | 必需 |
| ----------- | ------ | ------------------------------------------------------------------------------------------------------------------------------------ | ---- |
| windowIndex | Number | 窗口下标                                                                                                                             | 是   |
| option      | Object | bOpenflag true 为开启即时回放，在预览过程中会一直循环缓冲数据，对性能有一定影响，bInstantTime 入参仅支持 10,20,30 秒。即时回放的时长 | 是   |

返回：Promise

```text
let option = {
  bOpenflag: true,//开启即时回放
  bInstantTime: 10//即时回放的总时长
}
myPlugin.JS_InstantSetParam(curIndex, option).then(
  () => {
    console.info('JS_InstantSetParam success');
    // do you want...
  },
  (err) => {
    console.info('JS_InstantSetParam failed');
    // do you want...
  }
);
```

### 开始即时回放：JS_StartInstant(windowIndex)

说明：该接口之前必须调用 JS_InstantSetParam 后等待 2 秒以上，否则无效，开始后会循环播放缓存数据，直到调用 JS_StopInstant 为止。调用 JS_StopInstant 后可再次调用 JS_StartInstant
参数：

| 参数名      | 类型   | 说明     | 必需 |
| ----------- | ------ | -------- | ---- |
| windowIndex | Number | 窗口下标 | 是   |

返回：Promise

```text
myPlugin.JS_StartInstant(curIndex).then(
  () => {
    console.info('JS_StartInstant success');
    // do you want...
  },
  (err) => {
    console.info('JS_StartInstant failed:'， err);
    // do you want...
  }
);
```

### 关闭即时回放：JS_StopInstant(windowIndex)

说明：关闭时会正常跳回预览
参数：

| 参数名      | 类型   | 说明     | 必需 |
| ----------- | ------ | -------- | ---- |
| windowIndex | Number | 窗口下标 | 是   |

返回：Promise

```text
myPlugin.JS_StopInstant(curIndex).then(
  () => {
    console.info('JS_StopInstant success');
    // do you want...
  },
  (err) => {
    console.info('JS_StopInstant failed:'， err);
    // do you want...
  }
);
```

### 获取即时回放总长：JS_InstantTotalDuration(windowIndex)

参数：

| 参数名      | 类型   | 说明     | 必需 |
| ----------- | ------ | -------- | ---- |
| windowIndex | Number | 窗口下标 | 是   |

返回：Promise

```text
myPlugin.JS_InstantTotalDuration(curIndex).then(
  duration => {
    console.info('JS_InstantTotalDuration success:',duration);
    // do you want...
  },
  (err) => {
    console.info('JS_InstantTotalDuration failed:'， err);
    // do you want...
  }
);
```

### 获取即时回放当前播放长：JS_InstantCurrDuration(windowIndex)

说明：从 1 开始
参数：

| 参数名      | 类型   | 说明     | 必需 |
| ----------- | ------ | -------- | ---- |
| windowIndex | Number | 窗口下标 | 是   |

返回：Promise

```text
myPlugin.JS_InstantCurrDuration(curIndex).then(
  duration => {
    console.info('JS_InstantCurrDuration success:',duration);
    // do you want...
  },
  (err) => {
    console.info('JS_InstantCurrDuration failed:'， err);
    // do you want...
  }
);
```

### 旋转：JS_Rotate(windowIndex, degree)

说明：旋转成功后，再调抓图接口，得到的图片为旋转后的图片
参数：

| 参数名      | 类型   | 说明                                               | 必需 |
| ----------- | ------ | -------------------------------------------------- | ---- |
| windowIndex | Number | 窗口下标                                           | 是   |
| degree      | Number | 旋转角度（顺时针, 支持 0、90、180、270） 默认值：0 | 是   |

返回：Promise

```javascript
myPlugin
  .JS_Rotate(curIndex, 0)
  .then(() => {
    console.log("Success");
  })
  .catch((errCode) => {
    console.error("Failed", errCode);
  });
```

### 缩放：JS_Scale(windowIndex, ratio)

参数：

| 参数名      | 类型   | 说明                                                                                                  | 必需 |
| ----------- | ------ | ----------------------------------------------------------------------------------------------------- | ---- |
| windowIndex | Number | 窗口下标                                                                                              | 是   |
| ratio       | String | 缩放比例（支持："original"-视频原始分辨率、"fill"-平铺、"16:9"、"4:3"、"9:16"、"3:4"） 默认值："fill" | 是   |

返回：Promise

```javascript
myPlugin
  .JS_Scale(curIndex, "fill")
  .then(() => {
    console.log("Success");
  })
  .catch((errCode) => {
    console.error("Failed", errCode);
  });
```

### 取消缩放旋转：JS_ScaleCancel(windowIndex)

说明：同时取消缩放和旋转，若想单独取消，可调对应接口设置回默认值
参数：

| 参数名      | 类型   | 说明     | 必需 |
| ----------- | ------ | -------- | ---- |
| windowIndex | Number | 窗口下标 | 是   |

返回：Promise

```javascript
myPlugin
  .JS_ScaleCancel(curIndex)
  .then(() => {
    console.log("Success");
  })
  .catch((errCode) => {
    console.error("Failed", errCode);
  });
```

### 设置抓图水印：JS_SetWatermarkConfig(watermarkConfig)

说明：设置成功后对所有窗口生效.
参数：

| 参数名          | 类型   | 说明                                                                                                                               | 必需 |
| --------------- | ------ | ---------------------------------------------------------------------------------------------------------------------------------- | ---- |
| watermarkConfig | Object | 水印样式配置 默认值：{ text: 'h5player', color: 'rgba(255, 255, 255, 0.5)', font: '24px 微软雅黑', rotateDegree: -15, space: 240 } | 是   |
| -text           | String | 文本                                                                                                                               | 是   |
| -color          | String | 颜色，[css-color](https://developer.mozilla.org/zh-CN/docs/Web/CSS/color)                                                          | 否   |
| -font           | String | 字体，[css-font](https://developer.mozilla.org/zh-CN/docs/Web/CSS/font)                                                            | 否   |
| -rotateDegree   | Number | 角度，-180°~180°,负值向上旋转，正值向下旋转                                                                                        | 否   |
| -space          | Number | 间隔，单位 px                                                                                                                      | 否   |

返回：Promise

```javascript
myPlugin
  .JS_SetWatermarkConfig({
    text: "h5player",
    color: "rgba(255, 255, 255, 0.5)",
    font: "24px 微软雅黑",
    rotateDegree: -15,
    space: 240,
  })
  .then(() => {
    console.log("Success");
  })
  .catch((errCode) => {
    console.error("Failed", errCode);
  });
// 然后调JS_CapturePicture接口抓取带有水印的图片
// 若抓取的图片没有水印，请检查参数是否符合规范
```

### 取消设置抓图水印：JS_CancelWatermarkConfig()

参数：无
返回：Promise

```javascript
myPlugin
  .JS_CancelWatermarkConfig()
  .then(() => {
    console.log("Success");
  })
  .catch((errCode) => {
    console.error("Failed", errCode);
  });
```

## 错误码及其描述

| 错误码      | 描述                                                                                            |
| ----------- | ----------------------------------------------------------------------------------------------- |
| 0x12f900001 | 接口调用参数错误                                                                                |
| 0x12f900002 | 不在播放状态                                                                                    |
| 0x12f900003 | 仅回放支持该功能                                                                                |
| 0x12f900004 | 普通模式不支持该功能                                                                            |
| 0x12f900005 | 高级模式不支持该功能                                                                            |
| 0x12f900006 | 高级模式的解码库加载失败                                                                        |
| 0x12f900008 | url 格式错误,传了 null 或者是非 ws/wss 的报错                                                   |
| 0x12f900009 | 取流超时错误                                                                                    |
| 0x12f900010 | 设置或者是获取音量失败，因为没有开启音频的窗口                                                  |
| 0x12f900011 | 设置的音量不在 1-100 范围                                                                       |
| 0x12f900012 | 打开视频声音失败，需要等首帧显示后再开启声音,倍数情况不允许打开声音                             |
| 0x12f900013 | 当前解码方式不支持回放优化功能，请采用 https+设置跨域隔离+chrome94+进行支持                     |
| 0x12f900014 | 糟糕的水印设置，单个水印内容已经溢出了图片范围                                                  |
| 0x12f910000 | websocket 连接失败，请检查网络是否通畅，URL 是否正确                                            |
| 0x12f910010 | 取流失败                                                                                        |
| 0x12f910011 | 流中断，电脑配置过低，程序卡主线程都可能导致流中断                                              |
| 0x12f910014 | 没有音频数据                                                                                    |
| 0x12f910015 | 未找到对应 websocket，取流套接字被动关闭的报错                                                  |
| 0x12f910016 | websocket 不在连接状态                                                                          |
| 0x12f910017 | 不支持智能信息展示                                                                              |
| 0x12f910018 | websocket 长时间未收到 message                                                                  |
| 0x12f910019 | wss 连接失败，原因：端口尚未开通、证书未安装、证书不安全,安装平台登录页面根证书后重启浏览器解决 |
| 0x12f910020 | 单帧回放时不支持该操作                                                                          |
| 0x12f910021 | 已是最大倍速                                                                                    |
| 0x12f910022 | 已是最小倍速                                                                                    |
| 0x12f910023 | ws/wss 连接超时，默认 6s 超时时间，原因：网络异常，网络不通                                     |
| 0x12f910026 | jsdecoder1.0 解码报错视频编码格式不支持                                                         |
| 0x12f910027 | 后端取流超时，主动关闭连接（设备突然离线或重启，网络传输超时 20s）                              |
| 0x12f910028 | 设置的缓冲区大小无效，大小 0-5*1024*1024，不在该范围的报错                                      |
| 0x12f910029 | 普通模式的报错，码流异常导致黑屏,尝试重新取流                                                   |
| 0x12f910031 | 普通模式下播放卡主会出现                                                                        |
| 0x12f910032 | 码流编码格式普通模式下不支持，可切换高级模式尝试播放                                            |
| 0x12f910033 | 码流编码格式非 h264,h265;不支持该格式                                                           |
| 0x12f910034 | 后台媒体不支持回放优化功能，请升级 dac、sac、mgc、vod、vnsc、ncg 至新的基线版本                 |
| 0x12f910035 | 获取缩略图异常，请先调用开启缩略图再调用获取缩略图                                              |
| 0x12f910036 | 该场景不支持单帧功能                                                                            |
| 0x12f910037 | 倒放不支持该操作，例如:打开声音、紧急录像                                                       |
| 0x12f910038 | 当前预览未设置开启即时回放，无法开始即时回放                                                    |
| 0x12f910039 | 当前暂停状态，不支持该操作，例如:倍速                                                           |
| 0x12f910040 | 三方接入设备不支持该操作。例如：大华和国标不支持倒放，单帧退                                    |
| 0x12f910041 | 即时回放失败，缓冲区无数据导致,建议等待 2 秒再进行即时回放                                      |
| 0x12f920001 | 文件录制失败，请重试（可能是时间太短导致，请>2 秒做紧急录像）                                   |
| 0x12f920015 | 未调用停止录像，再次调用开始录像                                                                |
| 0x12f920016 | 未开启录像调用停止录像接口错误                                                                  |
| 0x12f920017 | 紧急录像目标格式不支持，非 ps/mp4                                                               |
| 0x12f920018 | 紧急录像文件名为 null                                                                           |
| 0x12f930010 | 内存不足                                                                                        |
| 0x12f930011 | 首帧显示之前无法抓图，请稍后重试                                                                |
| 0x12f950000 | 采集音频失败，可能是在非 https 域下使用对讲导致                                                 |
| 0x12f950001 | 对讲不支持这种音频编码格式                                                                      |
| 0x12f950050 | 操作失败，当前不在对讲状态                                                                      |

## FAQ

1.Q: 实测性能和给的性能数据不符，使用了 chrome92 版本，没有加跨域隔离导致
A: http 头增加跨域隔离，Cross-Origin-Embedder-Policy: require-corp Cross-Origin-Opener-Policy: same-origin 并在 https 环境下使用。
