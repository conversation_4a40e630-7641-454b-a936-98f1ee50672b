/*
 * @Description:
 * @Author: ligui<PERSON>
 * @LastEditors: liguiyuan
 */
import { defineConfig, loadEnv } from "vite";
import path from "path";
import createVitePlugins from "./vite/plugins";

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  const { VITE_APP_ENV } = env;
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: VITE_APP_ENV === "production" ? "/" : "/",
    plugins: createVitePlugins(env, command === "build"),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        "~": path.resolve(__dirname, "./"),
        // 设置别名
        "@": path.resolve(__dirname, "src"),
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
    },
    // vite 相关配置
    server: {
      port: 82,
      host: true,
      open: true,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        "/dev-api": {
          // target: 'http://**************:8080',
          target: "http://************:30006", // 线上
          // target: "http://************:8080", // ls本地
          // target: "http://*************:8080", // 王威本地
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, ""),
        },
        // 添加Dify API代理
        "/dify/v1": {
          target: "http://************:3001",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dify\/v1/, "/v1"),
        },
        "/papi": {
          target: "http://************:30006",
          // target: 'http://**************:8080',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/papi/, ""),
        },
        "/Analysis": {
          target: "https://mc-sh1-prod.obs.cn-east-3.myhuaweicloud.com",
          // target: 'http://**************:8080',
          changeOrigin: true,
          //rewrite: (p) => p.replace(/^\/papi/, '')
        },
        "/geoserver": {
          target: "http://************:10008/geoserver/",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/geoserver/, ""),
        },
        "/analysis": {
          target: "http://www.narutogis.com/water/analysis/",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/analysis/, ""),
        },
        "/tenant": {
          target: "http://************:10014",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/tenant/, ""),
        },
        "/zwh_dem30m": {
          target: "http://************:30009/zwh_dem30m",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/zwh_dem30m/, ""),
        },
      },
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/assets/styles/element-variables.scss" as *;`,
          api: "modern-compiler",
        },
      },
      postcss: {
        plugins: [
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === "charset") {
                  atRule.remove();
                }
              },
            },
          },
        ],
      },
    },
    build: {
      rollupOptions: {
        output: {
          chunkFileNames: "js/[name]-[hash].js", // 引入文件名的名称
          entryFileNames: "js/[name]-[hash].js", // 包的入口文件名称
          assetFileNames: "[ext]/[name]-[hash].[ext]", // 资源文件像 字体，图片等
          // 最小化拆分包
          manualChunks(id) {
            if (id.includes("node_modules")) {
              return id
                .toString()
                .split("node_modules/")[1]
                .split("/")[0]
                .toString();
            }
          },
        },
      },
    },
  };
});
