{
  // 忽略模式：定义哪些文件或目录将被排除在代码检查范围之外
  "ignorePatterns": [
    "node_modules/**",   // 排除所有第三方依赖
    "dist/**",           // 排除构建输出目录
    "public/**",         // 排除公共静态资源
    "*.min.js"           // 排除所有压缩过的JS文件
  ],

  // 规则配置：定义不同类型规则的严重级别
  "rules": {
    "correctness": "error",   // 代码正确性问题设为错误级别
    "suspicious": "error",    // 可疑代码设为错误级别
    "style": "warn",          // 代码风格问题设为警告级别
    "complexity": "warn"      // 代码复杂度问题设为警告级别
  },

  // JSX特定规则配置
  "jsxRules": {
    "noUnusedVariables": "error"  // JSX中未使用的变量被视为错误
  },

  // 解析器配置
  "parser": {
    "ignoreHTML": true  // 忽略HTML内容的解析
  },

  // 自动修复配置
  "fixer": {
    "unsafe": false  // 禁用不安全的自动修复操作
  }
}